"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PositionalArgDescriptor = exports.FlagOptions = exports.RpcCommandOptions_FlagOptionsEntry = exports.RpcCommandOptions = exports.ServiceCommandDescriptor_SubCommandsEntry = exports.ServiceCommandDescriptor = exports.ModuleOptions = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cosmos.autocli.v1";
function createBaseModuleOptions() {
    return { tx: undefined, query: undefined };
}
exports.ModuleOptions = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.tx !== undefined) {
            exports.ServiceCommandDescriptor.encode(message.tx, writer.uint32(10).fork()).ldelim();
        }
        if (message.query !== undefined) {
            exports.ServiceCommandDescriptor.encode(message.query, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModuleOptions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tx = exports.ServiceCommandDescriptor.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.query = exports.ServiceCommandDescriptor.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tx: isSet(object.tx) ? exports.ServiceCommandDescriptor.fromJSON(object.tx) : undefined,
            query: isSet(object.query) ? exports.ServiceCommandDescriptor.fromJSON(object.query) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.tx !== undefined && (obj.tx = message.tx ? exports.ServiceCommandDescriptor.toJSON(message.tx) : undefined);
        message.query !== undefined &&
            (obj.query = message.query ? exports.ServiceCommandDescriptor.toJSON(message.query) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ModuleOptions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseModuleOptions();
        message.tx = (object.tx !== undefined && object.tx !== null)
            ? exports.ServiceCommandDescriptor.fromPartial(object.tx)
            : undefined;
        message.query = (object.query !== undefined && object.query !== null)
            ? exports.ServiceCommandDescriptor.fromPartial(object.query)
            : undefined;
        return message;
    },
};
function createBaseServiceCommandDescriptor() {
    return { service: "", rpcCommandOptions: [], subCommands: {} };
}
exports.ServiceCommandDescriptor = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.service !== "") {
            writer.uint32(10).string(message.service);
        }
        try {
            for (var _b = __values(message.rpcCommandOptions), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.RpcCommandOptions.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        Object.entries(message.subCommands).forEach(function (_a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            exports.ServiceCommandDescriptor_SubCommandsEntry.encode({ key: key, value: value }, writer.uint32(26).fork()).ldelim();
        });
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseServiceCommandDescriptor();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.service = reader.string();
                    break;
                case 2:
                    message.rpcCommandOptions.push(exports.RpcCommandOptions.decode(reader, reader.uint32()));
                    break;
                case 3:
                    var entry3 = exports.ServiceCommandDescriptor_SubCommandsEntry.decode(reader, reader.uint32());
                    if (entry3.value !== undefined) {
                        message.subCommands[entry3.key] = entry3.value;
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            service: isSet(object.service) ? String(object.service) : "",
            rpcCommandOptions: Array.isArray(object === null || object === void 0 ? void 0 : object.rpcCommandOptions)
                ? object.rpcCommandOptions.map(function (e) { return exports.RpcCommandOptions.fromJSON(e); })
                : [],
            subCommands: isObject(object.subCommands)
                ? Object.entries(object.subCommands).reduce(function (acc, _a) {
                    var _b = __read(_a, 2), key = _b[0], value = _b[1];
                    acc[key] = exports.ServiceCommandDescriptor.fromJSON(value);
                    return acc;
                }, {})
                : {},
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.service !== undefined && (obj.service = message.service);
        if (message.rpcCommandOptions) {
            obj.rpcCommandOptions = message.rpcCommandOptions.map(function (e) { return e ? exports.RpcCommandOptions.toJSON(e) : undefined; });
        }
        else {
            obj.rpcCommandOptions = [];
        }
        obj.subCommands = {};
        if (message.subCommands) {
            Object.entries(message.subCommands).forEach(function (_a) {
                var _b = __read(_a, 2), k = _b[0], v = _b[1];
                obj.subCommands[k] = exports.ServiceCommandDescriptor.toJSON(v);
            });
        }
        return obj;
    },
    create: function (base) {
        return exports.ServiceCommandDescriptor.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseServiceCommandDescriptor();
        message.service = (_a = object.service) !== null && _a !== void 0 ? _a : "";
        message.rpcCommandOptions = ((_b = object.rpcCommandOptions) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.RpcCommandOptions.fromPartial(e); })) || [];
        message.subCommands = Object.entries((_c = object.subCommands) !== null && _c !== void 0 ? _c : {}).reduce(function (acc, _a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            if (value !== undefined) {
                acc[key] = exports.ServiceCommandDescriptor.fromPartial(value);
            }
            return acc;
        }, {});
        return message;
    },
};
function createBaseServiceCommandDescriptor_SubCommandsEntry() {
    return { key: "", value: undefined };
}
exports.ServiceCommandDescriptor_SubCommandsEntry = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            exports.ServiceCommandDescriptor.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseServiceCommandDescriptor_SubCommandsEntry();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = exports.ServiceCommandDescriptor.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? exports.ServiceCommandDescriptor.fromJSON(object.value) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined &&
            (obj.value = message.value ? exports.ServiceCommandDescriptor.toJSON(message.value) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ServiceCommandDescriptor_SubCommandsEntry.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseServiceCommandDescriptor_SubCommandsEntry();
        message.key = (_a = object.key) !== null && _a !== void 0 ? _a : "";
        message.value = (object.value !== undefined && object.value !== null)
            ? exports.ServiceCommandDescriptor.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseRpcCommandOptions() {
    return {
        rpcMethod: "",
        use: "",
        long: "",
        short: "",
        example: "",
        alias: [],
        suggestFor: [],
        deprecated: "",
        version: "",
        flagOptions: {},
        positionalArgs: [],
        skip: false,
    };
}
exports.RpcCommandOptions = {
    encode: function (message, writer) {
        var e_2, _a, e_3, _b, e_4, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.rpcMethod !== "") {
            writer.uint32(10).string(message.rpcMethod);
        }
        if (message.use !== "") {
            writer.uint32(18).string(message.use);
        }
        if (message.long !== "") {
            writer.uint32(26).string(message.long);
        }
        if (message.short !== "") {
            writer.uint32(34).string(message.short);
        }
        if (message.example !== "") {
            writer.uint32(42).string(message.example);
        }
        try {
            for (var _d = __values(message.alias), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                writer.uint32(50).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _f = __values(message.suggestFor), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                writer.uint32(58).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (message.deprecated !== "") {
            writer.uint32(66).string(message.deprecated);
        }
        if (message.version !== "") {
            writer.uint32(74).string(message.version);
        }
        Object.entries(message.flagOptions).forEach(function (_a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            exports.RpcCommandOptions_FlagOptionsEntry.encode({ key: key, value: value }, writer.uint32(82).fork()).ldelim();
        });
        try {
            for (var _h = __values(message.positionalArgs), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                exports.PositionalArgDescriptor.encode(v, writer.uint32(90).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_4) throw e_4.error; }
        }
        if (message.skip === true) {
            writer.uint32(96).bool(message.skip);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRpcCommandOptions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rpcMethod = reader.string();
                    break;
                case 2:
                    message.use = reader.string();
                    break;
                case 3:
                    message.long = reader.string();
                    break;
                case 4:
                    message.short = reader.string();
                    break;
                case 5:
                    message.example = reader.string();
                    break;
                case 6:
                    message.alias.push(reader.string());
                    break;
                case 7:
                    message.suggestFor.push(reader.string());
                    break;
                case 8:
                    message.deprecated = reader.string();
                    break;
                case 9:
                    message.version = reader.string();
                    break;
                case 10:
                    var entry10 = exports.RpcCommandOptions_FlagOptionsEntry.decode(reader, reader.uint32());
                    if (entry10.value !== undefined) {
                        message.flagOptions[entry10.key] = entry10.value;
                    }
                    break;
                case 11:
                    message.positionalArgs.push(exports.PositionalArgDescriptor.decode(reader, reader.uint32()));
                    break;
                case 12:
                    message.skip = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            rpcMethod: isSet(object.rpcMethod) ? String(object.rpcMethod) : "",
            use: isSet(object.use) ? String(object.use) : "",
            long: isSet(object.long) ? String(object.long) : "",
            short: isSet(object.short) ? String(object.short) : "",
            example: isSet(object.example) ? String(object.example) : "",
            alias: Array.isArray(object === null || object === void 0 ? void 0 : object.alias) ? object.alias.map(function (e) { return String(e); }) : [],
            suggestFor: Array.isArray(object === null || object === void 0 ? void 0 : object.suggestFor) ? object.suggestFor.map(function (e) { return String(e); }) : [],
            deprecated: isSet(object.deprecated) ? String(object.deprecated) : "",
            version: isSet(object.version) ? String(object.version) : "",
            flagOptions: isObject(object.flagOptions)
                ? Object.entries(object.flagOptions).reduce(function (acc, _a) {
                    var _b = __read(_a, 2), key = _b[0], value = _b[1];
                    acc[key] = exports.FlagOptions.fromJSON(value);
                    return acc;
                }, {})
                : {},
            positionalArgs: Array.isArray(object === null || object === void 0 ? void 0 : object.positionalArgs)
                ? object.positionalArgs.map(function (e) { return exports.PositionalArgDescriptor.fromJSON(e); })
                : [],
            skip: isSet(object.skip) ? Boolean(object.skip) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.rpcMethod !== undefined && (obj.rpcMethod = message.rpcMethod);
        message.use !== undefined && (obj.use = message.use);
        message.long !== undefined && (obj.long = message.long);
        message.short !== undefined && (obj.short = message.short);
        message.example !== undefined && (obj.example = message.example);
        if (message.alias) {
            obj.alias = message.alias.map(function (e) { return e; });
        }
        else {
            obj.alias = [];
        }
        if (message.suggestFor) {
            obj.suggestFor = message.suggestFor.map(function (e) { return e; });
        }
        else {
            obj.suggestFor = [];
        }
        message.deprecated !== undefined && (obj.deprecated = message.deprecated);
        message.version !== undefined && (obj.version = message.version);
        obj.flagOptions = {};
        if (message.flagOptions) {
            Object.entries(message.flagOptions).forEach(function (_a) {
                var _b = __read(_a, 2), k = _b[0], v = _b[1];
                obj.flagOptions[k] = exports.FlagOptions.toJSON(v);
            });
        }
        if (message.positionalArgs) {
            obj.positionalArgs = message.positionalArgs.map(function (e) { return e ? exports.PositionalArgDescriptor.toJSON(e) : undefined; });
        }
        else {
            obj.positionalArgs = [];
        }
        message.skip !== undefined && (obj.skip = message.skip);
        return obj;
    },
    create: function (base) {
        return exports.RpcCommandOptions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        var message = createBaseRpcCommandOptions();
        message.rpcMethod = (_a = object.rpcMethod) !== null && _a !== void 0 ? _a : "";
        message.use = (_b = object.use) !== null && _b !== void 0 ? _b : "";
        message.long = (_c = object.long) !== null && _c !== void 0 ? _c : "";
        message.short = (_d = object.short) !== null && _d !== void 0 ? _d : "";
        message.example = (_e = object.example) !== null && _e !== void 0 ? _e : "";
        message.alias = ((_f = object.alias) === null || _f === void 0 ? void 0 : _f.map(function (e) { return e; })) || [];
        message.suggestFor = ((_g = object.suggestFor) === null || _g === void 0 ? void 0 : _g.map(function (e) { return e; })) || [];
        message.deprecated = (_h = object.deprecated) !== null && _h !== void 0 ? _h : "";
        message.version = (_j = object.version) !== null && _j !== void 0 ? _j : "";
        message.flagOptions = Object.entries((_k = object.flagOptions) !== null && _k !== void 0 ? _k : {}).reduce(function (acc, _a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            if (value !== undefined) {
                acc[key] = exports.FlagOptions.fromPartial(value);
            }
            return acc;
        }, {});
        message.positionalArgs = ((_l = object.positionalArgs) === null || _l === void 0 ? void 0 : _l.map(function (e) { return exports.PositionalArgDescriptor.fromPartial(e); })) || [];
        message.skip = (_m = object.skip) !== null && _m !== void 0 ? _m : false;
        return message;
    },
};
function createBaseRpcCommandOptions_FlagOptionsEntry() {
    return { key: "", value: undefined };
}
exports.RpcCommandOptions_FlagOptionsEntry = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            exports.FlagOptions.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRpcCommandOptions_FlagOptionsEntry();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = exports.FlagOptions.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? exports.FlagOptions.fromJSON(object.value) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value ? exports.FlagOptions.toJSON(message.value) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.RpcCommandOptions_FlagOptionsEntry.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseRpcCommandOptions_FlagOptionsEntry();
        message.key = (_a = object.key) !== null && _a !== void 0 ? _a : "";
        message.value = (object.value !== undefined && object.value !== null)
            ? exports.FlagOptions.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseFlagOptions() {
    return {
        name: "",
        shorthand: "",
        usage: "",
        defaultValue: "",
        deprecated: "",
        shorthandDeprecated: "",
        hidden: false,
    };
}
exports.FlagOptions = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.shorthand !== "") {
            writer.uint32(18).string(message.shorthand);
        }
        if (message.usage !== "") {
            writer.uint32(26).string(message.usage);
        }
        if (message.defaultValue !== "") {
            writer.uint32(34).string(message.defaultValue);
        }
        if (message.deprecated !== "") {
            writer.uint32(50).string(message.deprecated);
        }
        if (message.shorthandDeprecated !== "") {
            writer.uint32(58).string(message.shorthandDeprecated);
        }
        if (message.hidden === true) {
            writer.uint32(64).bool(message.hidden);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFlagOptions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.name = reader.string();
                    break;
                case 2:
                    message.shorthand = reader.string();
                    break;
                case 3:
                    message.usage = reader.string();
                    break;
                case 4:
                    message.defaultValue = reader.string();
                    break;
                case 6:
                    message.deprecated = reader.string();
                    break;
                case 7:
                    message.shorthandDeprecated = reader.string();
                    break;
                case 8:
                    message.hidden = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            name: isSet(object.name) ? String(object.name) : "",
            shorthand: isSet(object.shorthand) ? String(object.shorthand) : "",
            usage: isSet(object.usage) ? String(object.usage) : "",
            defaultValue: isSet(object.defaultValue) ? String(object.defaultValue) : "",
            deprecated: isSet(object.deprecated) ? String(object.deprecated) : "",
            shorthandDeprecated: isSet(object.shorthandDeprecated) ? String(object.shorthandDeprecated) : "",
            hidden: isSet(object.hidden) ? Boolean(object.hidden) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.name !== undefined && (obj.name = message.name);
        message.shorthand !== undefined && (obj.shorthand = message.shorthand);
        message.usage !== undefined && (obj.usage = message.usage);
        message.defaultValue !== undefined && (obj.defaultValue = message.defaultValue);
        message.deprecated !== undefined && (obj.deprecated = message.deprecated);
        message.shorthandDeprecated !== undefined && (obj.shorthandDeprecated = message.shorthandDeprecated);
        message.hidden !== undefined && (obj.hidden = message.hidden);
        return obj;
    },
    create: function (base) {
        return exports.FlagOptions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseFlagOptions();
        message.name = (_a = object.name) !== null && _a !== void 0 ? _a : "";
        message.shorthand = (_b = object.shorthand) !== null && _b !== void 0 ? _b : "";
        message.usage = (_c = object.usage) !== null && _c !== void 0 ? _c : "";
        message.defaultValue = (_d = object.defaultValue) !== null && _d !== void 0 ? _d : "";
        message.deprecated = (_e = object.deprecated) !== null && _e !== void 0 ? _e : "";
        message.shorthandDeprecated = (_f = object.shorthandDeprecated) !== null && _f !== void 0 ? _f : "";
        message.hidden = (_g = object.hidden) !== null && _g !== void 0 ? _g : false;
        return message;
    },
};
function createBasePositionalArgDescriptor() {
    return { protoField: "", varargs: false };
}
exports.PositionalArgDescriptor = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.protoField !== "") {
            writer.uint32(10).string(message.protoField);
        }
        if (message.varargs === true) {
            writer.uint32(16).bool(message.varargs);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePositionalArgDescriptor();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.protoField = reader.string();
                    break;
                case 2:
                    message.varargs = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            protoField: isSet(object.protoField) ? String(object.protoField) : "",
            varargs: isSet(object.varargs) ? Boolean(object.varargs) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.protoField !== undefined && (obj.protoField = message.protoField);
        message.varargs !== undefined && (obj.varargs = message.varargs);
        return obj;
    },
    create: function (base) {
        return exports.PositionalArgDescriptor.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePositionalArgDescriptor();
        message.protoField = (_a = object.protoField) !== null && _a !== void 0 ? _a : "";
        message.varargs = (_b = object.varargs) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
