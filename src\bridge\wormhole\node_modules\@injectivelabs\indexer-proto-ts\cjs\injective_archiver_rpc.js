"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.InjectiveArchiverRPCHistoricalTradesDesc = exports.InjectiveArchiverRPCDenomHoldersDesc = exports.InjectiveArchiverRPCVolLeaderboardFixedResolutionDesc = exports.InjectiveArchiverRPCPnlLeaderboardFixedResolutionDesc = exports.InjectiveArchiverRPCVolLeaderboardDesc = exports.InjectiveArchiverRPCPnlLeaderboardDesc = exports.InjectiveArchiverRPCVolumesDesc = exports.InjectiveArchiverRPCRpnlDesc = exports.InjectiveArchiverRPCBalanceDesc = exports.InjectiveArchiverRPCDesc = exports.InjectiveArchiverRPCClientImpl = exports.PriceLevel = exports.HistoricalTrade = exports.HistoricalTradesResponse = exports.HistoricalTradesRequest = exports.Holder = exports.DenomHoldersResponse = exports.DenomHoldersRequest = exports.VolLeaderboardFixedResolutionResponse = exports.VolLeaderboardFixedResolutionRequest = exports.PnlLeaderboardFixedResolutionResponse = exports.PnlLeaderboardFixedResolutionRequest = exports.VolLeaderboardResponse = exports.VolLeaderboardRequest = exports.LeaderboardRow = exports.PnlLeaderboardResponse = exports.PnlLeaderboardRequest = exports.HistoricalVolumes = exports.VolumesResponse = exports.VolumesRequest = exports.HistoricalRPNL = exports.RpnlResponse = exports.RpnlRequest = exports.HistoricalBalance = exports.BalanceResponse = exports.BalanceRequest = exports.protobufPackage = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const long_1 = __importDefault(require("long"));
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "injective_archiver_rpc";
function createBaseBalanceRequest() {
    return { account: "", resolution: "" };
}
exports.BalanceRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.resolution !== "") {
            writer.uint32(18).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBalanceRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return exports.BalanceRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseBalanceRequest();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.resolution = (_b = object.resolution) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseBalanceResponse() {
    return { historicalBalance: undefined };
}
exports.BalanceResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.historicalBalance !== undefined) {
            exports.HistoricalBalance.encode(message.historicalBalance, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBalanceResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.historicalBalance = exports.HistoricalBalance.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            historicalBalance: isSet(object.historicalBalance)
                ? exports.HistoricalBalance.fromJSON(object.historicalBalance)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.historicalBalance !== undefined && (obj.historicalBalance = message.historicalBalance
            ? exports.HistoricalBalance.toJSON(message.historicalBalance)
            : undefined);
        return obj;
    },
    create(base) {
        return exports.BalanceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseBalanceResponse();
        message.historicalBalance = (object.historicalBalance !== undefined && object.historicalBalance !== null)
            ? exports.HistoricalBalance.fromPartial(object.historicalBalance)
            : undefined;
        return message;
    },
};
function createBaseHistoricalBalance() {
    return { t: [], v: [] };
}
exports.HistoricalBalance = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.t) {
            writer.sint32(v);
        }
        writer.ldelim();
        writer.uint32(18).fork();
        for (const v of message.v) {
            writer.double(v);
        }
        writer.ldelim();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalBalance();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.t.push(reader.sint32());
                        }
                    }
                    else {
                        message.t.push(reader.sint32());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.v.push(reader.double());
                        }
                    }
                    else {
                        message.v.push(reader.double());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            t: Array.isArray(object === null || object === void 0 ? void 0 : object.t) ? object.t.map((e) => Number(e)) : [],
            v: Array.isArray(object === null || object === void 0 ? void 0 : object.v) ? object.v.map((e) => Number(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.t) {
            obj.t = message.t.map((e) => Math.round(e));
        }
        else {
            obj.t = [];
        }
        if (message.v) {
            obj.v = message.v.map((e) => e);
        }
        else {
            obj.v = [];
        }
        return obj;
    },
    create(base) {
        return exports.HistoricalBalance.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseHistoricalBalance();
        message.t = ((_a = object.t) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.v = ((_b = object.v) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        return message;
    },
};
function createBaseRpnlRequest() {
    return { account: "", resolution: "" };
}
exports.RpnlRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.resolution !== "") {
            writer.uint32(18).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRpnlRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return exports.RpnlRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseRpnlRequest();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.resolution = (_b = object.resolution) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseRpnlResponse() {
    return { historicalRpnl: undefined };
}
exports.RpnlResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.historicalRpnl !== undefined) {
            exports.HistoricalRPNL.encode(message.historicalRpnl, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRpnlResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.historicalRpnl = exports.HistoricalRPNL.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            historicalRpnl: isSet(object.historicalRpnl) ? exports.HistoricalRPNL.fromJSON(object.historicalRpnl) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.historicalRpnl !== undefined &&
            (obj.historicalRpnl = message.historicalRpnl ? exports.HistoricalRPNL.toJSON(message.historicalRpnl) : undefined);
        return obj;
    },
    create(base) {
        return exports.RpnlResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseRpnlResponse();
        message.historicalRpnl = (object.historicalRpnl !== undefined && object.historicalRpnl !== null)
            ? exports.HistoricalRPNL.fromPartial(object.historicalRpnl)
            : undefined;
        return message;
    },
};
function createBaseHistoricalRPNL() {
    return { t: [], v: [] };
}
exports.HistoricalRPNL = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.t) {
            writer.sint32(v);
        }
        writer.ldelim();
        writer.uint32(18).fork();
        for (const v of message.v) {
            writer.double(v);
        }
        writer.ldelim();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalRPNL();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.t.push(reader.sint32());
                        }
                    }
                    else {
                        message.t.push(reader.sint32());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.v.push(reader.double());
                        }
                    }
                    else {
                        message.v.push(reader.double());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            t: Array.isArray(object === null || object === void 0 ? void 0 : object.t) ? object.t.map((e) => Number(e)) : [],
            v: Array.isArray(object === null || object === void 0 ? void 0 : object.v) ? object.v.map((e) => Number(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.t) {
            obj.t = message.t.map((e) => Math.round(e));
        }
        else {
            obj.t = [];
        }
        if (message.v) {
            obj.v = message.v.map((e) => e);
        }
        else {
            obj.v = [];
        }
        return obj;
    },
    create(base) {
        return exports.HistoricalRPNL.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseHistoricalRPNL();
        message.t = ((_a = object.t) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.v = ((_b = object.v) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        return message;
    },
};
function createBaseVolumesRequest() {
    return { account: "", resolution: "" };
}
exports.VolumesRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.resolution !== "") {
            writer.uint32(18).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVolumesRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return exports.VolumesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseVolumesRequest();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.resolution = (_b = object.resolution) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseVolumesResponse() {
    return { historicalVolumes: undefined };
}
exports.VolumesResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.historicalVolumes !== undefined) {
            exports.HistoricalVolumes.encode(message.historicalVolumes, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVolumesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.historicalVolumes = exports.HistoricalVolumes.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            historicalVolumes: isSet(object.historicalVolumes)
                ? exports.HistoricalVolumes.fromJSON(object.historicalVolumes)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.historicalVolumes !== undefined && (obj.historicalVolumes = message.historicalVolumes
            ? exports.HistoricalVolumes.toJSON(message.historicalVolumes)
            : undefined);
        return obj;
    },
    create(base) {
        return exports.VolumesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseVolumesResponse();
        message.historicalVolumes = (object.historicalVolumes !== undefined && object.historicalVolumes !== null)
            ? exports.HistoricalVolumes.fromPartial(object.historicalVolumes)
            : undefined;
        return message;
    },
};
function createBaseHistoricalVolumes() {
    return { t: [], v: [] };
}
exports.HistoricalVolumes = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.t) {
            writer.sint32(v);
        }
        writer.ldelim();
        writer.uint32(18).fork();
        for (const v of message.v) {
            writer.double(v);
        }
        writer.ldelim();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalVolumes();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.t.push(reader.sint32());
                        }
                    }
                    else {
                        message.t.push(reader.sint32());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.v.push(reader.double());
                        }
                    }
                    else {
                        message.v.push(reader.double());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            t: Array.isArray(object === null || object === void 0 ? void 0 : object.t) ? object.t.map((e) => Number(e)) : [],
            v: Array.isArray(object === null || object === void 0 ? void 0 : object.v) ? object.v.map((e) => Number(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.t) {
            obj.t = message.t.map((e) => Math.round(e));
        }
        else {
            obj.t = [];
        }
        if (message.v) {
            obj.v = message.v.map((e) => e);
        }
        else {
            obj.v = [];
        }
        return obj;
    },
    create(base) {
        return exports.HistoricalVolumes.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseHistoricalVolumes();
        message.t = ((_a = object.t) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.v = ((_b = object.v) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        return message;
    },
};
function createBasePnlLeaderboardRequest() {
    return { startDate: "0", endDate: "0", limit: 0, account: "" };
}
exports.PnlLeaderboardRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.startDate !== "0") {
            writer.uint32(8).sint64(message.startDate);
        }
        if (message.endDate !== "0") {
            writer.uint32(16).sint64(message.endDate);
        }
        if (message.limit !== 0) {
            writer.uint32(24).sint32(message.limit);
        }
        if (message.account !== "") {
            writer.uint32(34).string(message.account);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePnlLeaderboardRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.startDate = longToString(reader.sint64());
                    break;
                case 2:
                    message.endDate = longToString(reader.sint64());
                    break;
                case 3:
                    message.limit = reader.sint32();
                    break;
                case 4:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            startDate: isSet(object.startDate) ? String(object.startDate) : "0",
            endDate: isSet(object.endDate) ? String(object.endDate) : "0",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            account: isSet(object.account) ? String(object.account) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.startDate !== undefined && (obj.startDate = message.startDate);
        message.endDate !== undefined && (obj.endDate = message.endDate);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create(base) {
        return exports.PnlLeaderboardRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBasePnlLeaderboardRequest();
        message.startDate = (_a = object.startDate) !== null && _a !== void 0 ? _a : "0";
        message.endDate = (_b = object.endDate) !== null && _b !== void 0 ? _b : "0";
        message.limit = (_c = object.limit) !== null && _c !== void 0 ? _c : 0;
        message.account = (_d = object.account) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBasePnlLeaderboardResponse() {
    return { firstDate: "", lastDate: "", leaders: [], accountRow: undefined };
}
exports.PnlLeaderboardResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.firstDate !== "") {
            writer.uint32(10).string(message.firstDate);
        }
        if (message.lastDate !== "") {
            writer.uint32(18).string(message.lastDate);
        }
        for (const v of message.leaders) {
            exports.LeaderboardRow.encode(v, writer.uint32(26).fork()).ldelim();
        }
        if (message.accountRow !== undefined) {
            exports.LeaderboardRow.encode(message.accountRow, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePnlLeaderboardResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.firstDate = reader.string();
                    break;
                case 2:
                    message.lastDate = reader.string();
                    break;
                case 3:
                    message.leaders.push(exports.LeaderboardRow.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.accountRow = exports.LeaderboardRow.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            firstDate: isSet(object.firstDate) ? String(object.firstDate) : "",
            lastDate: isSet(object.lastDate) ? String(object.lastDate) : "",
            leaders: Array.isArray(object === null || object === void 0 ? void 0 : object.leaders) ? object.leaders.map((e) => exports.LeaderboardRow.fromJSON(e)) : [],
            accountRow: isSet(object.accountRow) ? exports.LeaderboardRow.fromJSON(object.accountRow) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.firstDate !== undefined && (obj.firstDate = message.firstDate);
        message.lastDate !== undefined && (obj.lastDate = message.lastDate);
        if (message.leaders) {
            obj.leaders = message.leaders.map((e) => e ? exports.LeaderboardRow.toJSON(e) : undefined);
        }
        else {
            obj.leaders = [];
        }
        message.accountRow !== undefined &&
            (obj.accountRow = message.accountRow ? exports.LeaderboardRow.toJSON(message.accountRow) : undefined);
        return obj;
    },
    create(base) {
        return exports.PnlLeaderboardResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBasePnlLeaderboardResponse();
        message.firstDate = (_a = object.firstDate) !== null && _a !== void 0 ? _a : "";
        message.lastDate = (_b = object.lastDate) !== null && _b !== void 0 ? _b : "";
        message.leaders = ((_c = object.leaders) === null || _c === void 0 ? void 0 : _c.map((e) => exports.LeaderboardRow.fromPartial(e))) || [];
        message.accountRow = (object.accountRow !== undefined && object.accountRow !== null)
            ? exports.LeaderboardRow.fromPartial(object.accountRow)
            : undefined;
        return message;
    },
};
function createBaseLeaderboardRow() {
    return { account: "", pnl: 0, volume: 0, rank: 0 };
}
exports.LeaderboardRow = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.pnl !== 0) {
            writer.uint32(17).double(message.pnl);
        }
        if (message.volume !== 0) {
            writer.uint32(25).double(message.volume);
        }
        if (message.rank !== 0) {
            writer.uint32(32).sint32(message.rank);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseLeaderboardRow();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.pnl = reader.double();
                    break;
                case 3:
                    message.volume = reader.double();
                    break;
                case 4:
                    message.rank = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            pnl: isSet(object.pnl) ? Number(object.pnl) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
            rank: isSet(object.rank) ? Number(object.rank) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.pnl !== undefined && (obj.pnl = message.pnl);
        message.volume !== undefined && (obj.volume = message.volume);
        message.rank !== undefined && (obj.rank = Math.round(message.rank));
        return obj;
    },
    create(base) {
        return exports.LeaderboardRow.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseLeaderboardRow();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.pnl = (_b = object.pnl) !== null && _b !== void 0 ? _b : 0;
        message.volume = (_c = object.volume) !== null && _c !== void 0 ? _c : 0;
        message.rank = (_d = object.rank) !== null && _d !== void 0 ? _d : 0;
        return message;
    },
};
function createBaseVolLeaderboardRequest() {
    return { startDate: "0", endDate: "0", limit: 0, account: "" };
}
exports.VolLeaderboardRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.startDate !== "0") {
            writer.uint32(8).sint64(message.startDate);
        }
        if (message.endDate !== "0") {
            writer.uint32(16).sint64(message.endDate);
        }
        if (message.limit !== 0) {
            writer.uint32(24).sint32(message.limit);
        }
        if (message.account !== "") {
            writer.uint32(34).string(message.account);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVolLeaderboardRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.startDate = longToString(reader.sint64());
                    break;
                case 2:
                    message.endDate = longToString(reader.sint64());
                    break;
                case 3:
                    message.limit = reader.sint32();
                    break;
                case 4:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            startDate: isSet(object.startDate) ? String(object.startDate) : "0",
            endDate: isSet(object.endDate) ? String(object.endDate) : "0",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            account: isSet(object.account) ? String(object.account) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.startDate !== undefined && (obj.startDate = message.startDate);
        message.endDate !== undefined && (obj.endDate = message.endDate);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create(base) {
        return exports.VolLeaderboardRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseVolLeaderboardRequest();
        message.startDate = (_a = object.startDate) !== null && _a !== void 0 ? _a : "0";
        message.endDate = (_b = object.endDate) !== null && _b !== void 0 ? _b : "0";
        message.limit = (_c = object.limit) !== null && _c !== void 0 ? _c : 0;
        message.account = (_d = object.account) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseVolLeaderboardResponse() {
    return { firstDate: "", lastDate: "", leaders: [], accountRow: undefined };
}
exports.VolLeaderboardResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.firstDate !== "") {
            writer.uint32(10).string(message.firstDate);
        }
        if (message.lastDate !== "") {
            writer.uint32(18).string(message.lastDate);
        }
        for (const v of message.leaders) {
            exports.LeaderboardRow.encode(v, writer.uint32(26).fork()).ldelim();
        }
        if (message.accountRow !== undefined) {
            exports.LeaderboardRow.encode(message.accountRow, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVolLeaderboardResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.firstDate = reader.string();
                    break;
                case 2:
                    message.lastDate = reader.string();
                    break;
                case 3:
                    message.leaders.push(exports.LeaderboardRow.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.accountRow = exports.LeaderboardRow.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            firstDate: isSet(object.firstDate) ? String(object.firstDate) : "",
            lastDate: isSet(object.lastDate) ? String(object.lastDate) : "",
            leaders: Array.isArray(object === null || object === void 0 ? void 0 : object.leaders) ? object.leaders.map((e) => exports.LeaderboardRow.fromJSON(e)) : [],
            accountRow: isSet(object.accountRow) ? exports.LeaderboardRow.fromJSON(object.accountRow) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.firstDate !== undefined && (obj.firstDate = message.firstDate);
        message.lastDate !== undefined && (obj.lastDate = message.lastDate);
        if (message.leaders) {
            obj.leaders = message.leaders.map((e) => e ? exports.LeaderboardRow.toJSON(e) : undefined);
        }
        else {
            obj.leaders = [];
        }
        message.accountRow !== undefined &&
            (obj.accountRow = message.accountRow ? exports.LeaderboardRow.toJSON(message.accountRow) : undefined);
        return obj;
    },
    create(base) {
        return exports.VolLeaderboardResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseVolLeaderboardResponse();
        message.firstDate = (_a = object.firstDate) !== null && _a !== void 0 ? _a : "";
        message.lastDate = (_b = object.lastDate) !== null && _b !== void 0 ? _b : "";
        message.leaders = ((_c = object.leaders) === null || _c === void 0 ? void 0 : _c.map((e) => exports.LeaderboardRow.fromPartial(e))) || [];
        message.accountRow = (object.accountRow !== undefined && object.accountRow !== null)
            ? exports.LeaderboardRow.fromPartial(object.accountRow)
            : undefined;
        return message;
    },
};
function createBasePnlLeaderboardFixedResolutionRequest() {
    return { resolution: "", limit: 0, account: "" };
}
exports.PnlLeaderboardFixedResolutionRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.resolution !== "") {
            writer.uint32(10).string(message.resolution);
        }
        if (message.limit !== 0) {
            writer.uint32(16).sint32(message.limit);
        }
        if (message.account !== "") {
            writer.uint32(26).string(message.account);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePnlLeaderboardFixedResolutionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.resolution = reader.string();
                    break;
                case 2:
                    message.limit = reader.sint32();
                    break;
                case 3:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            account: isSet(object.account) ? String(object.account) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.resolution !== undefined && (obj.resolution = message.resolution);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create(base) {
        return exports.PnlLeaderboardFixedResolutionRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBasePnlLeaderboardFixedResolutionRequest();
        message.resolution = (_a = object.resolution) !== null && _a !== void 0 ? _a : "";
        message.limit = (_b = object.limit) !== null && _b !== void 0 ? _b : 0;
        message.account = (_c = object.account) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBasePnlLeaderboardFixedResolutionResponse() {
    return { firstDate: "", lastDate: "", leaders: [], accountRow: undefined };
}
exports.PnlLeaderboardFixedResolutionResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.firstDate !== "") {
            writer.uint32(10).string(message.firstDate);
        }
        if (message.lastDate !== "") {
            writer.uint32(18).string(message.lastDate);
        }
        for (const v of message.leaders) {
            exports.LeaderboardRow.encode(v, writer.uint32(26).fork()).ldelim();
        }
        if (message.accountRow !== undefined) {
            exports.LeaderboardRow.encode(message.accountRow, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePnlLeaderboardFixedResolutionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.firstDate = reader.string();
                    break;
                case 2:
                    message.lastDate = reader.string();
                    break;
                case 3:
                    message.leaders.push(exports.LeaderboardRow.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.accountRow = exports.LeaderboardRow.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            firstDate: isSet(object.firstDate) ? String(object.firstDate) : "",
            lastDate: isSet(object.lastDate) ? String(object.lastDate) : "",
            leaders: Array.isArray(object === null || object === void 0 ? void 0 : object.leaders) ? object.leaders.map((e) => exports.LeaderboardRow.fromJSON(e)) : [],
            accountRow: isSet(object.accountRow) ? exports.LeaderboardRow.fromJSON(object.accountRow) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.firstDate !== undefined && (obj.firstDate = message.firstDate);
        message.lastDate !== undefined && (obj.lastDate = message.lastDate);
        if (message.leaders) {
            obj.leaders = message.leaders.map((e) => e ? exports.LeaderboardRow.toJSON(e) : undefined);
        }
        else {
            obj.leaders = [];
        }
        message.accountRow !== undefined &&
            (obj.accountRow = message.accountRow ? exports.LeaderboardRow.toJSON(message.accountRow) : undefined);
        return obj;
    },
    create(base) {
        return exports.PnlLeaderboardFixedResolutionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBasePnlLeaderboardFixedResolutionResponse();
        message.firstDate = (_a = object.firstDate) !== null && _a !== void 0 ? _a : "";
        message.lastDate = (_b = object.lastDate) !== null && _b !== void 0 ? _b : "";
        message.leaders = ((_c = object.leaders) === null || _c === void 0 ? void 0 : _c.map((e) => exports.LeaderboardRow.fromPartial(e))) || [];
        message.accountRow = (object.accountRow !== undefined && object.accountRow !== null)
            ? exports.LeaderboardRow.fromPartial(object.accountRow)
            : undefined;
        return message;
    },
};
function createBaseVolLeaderboardFixedResolutionRequest() {
    return { resolution: "", limit: 0, account: "" };
}
exports.VolLeaderboardFixedResolutionRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.resolution !== "") {
            writer.uint32(10).string(message.resolution);
        }
        if (message.limit !== 0) {
            writer.uint32(16).sint32(message.limit);
        }
        if (message.account !== "") {
            writer.uint32(26).string(message.account);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVolLeaderboardFixedResolutionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.resolution = reader.string();
                    break;
                case 2:
                    message.limit = reader.sint32();
                    break;
                case 3:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            account: isSet(object.account) ? String(object.account) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.resolution !== undefined && (obj.resolution = message.resolution);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create(base) {
        return exports.VolLeaderboardFixedResolutionRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseVolLeaderboardFixedResolutionRequest();
        message.resolution = (_a = object.resolution) !== null && _a !== void 0 ? _a : "";
        message.limit = (_b = object.limit) !== null && _b !== void 0 ? _b : 0;
        message.account = (_c = object.account) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseVolLeaderboardFixedResolutionResponse() {
    return { firstDate: "", lastDate: "", leaders: [], accountRow: undefined };
}
exports.VolLeaderboardFixedResolutionResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.firstDate !== "") {
            writer.uint32(10).string(message.firstDate);
        }
        if (message.lastDate !== "") {
            writer.uint32(18).string(message.lastDate);
        }
        for (const v of message.leaders) {
            exports.LeaderboardRow.encode(v, writer.uint32(26).fork()).ldelim();
        }
        if (message.accountRow !== undefined) {
            exports.LeaderboardRow.encode(message.accountRow, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseVolLeaderboardFixedResolutionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.firstDate = reader.string();
                    break;
                case 2:
                    message.lastDate = reader.string();
                    break;
                case 3:
                    message.leaders.push(exports.LeaderboardRow.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.accountRow = exports.LeaderboardRow.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            firstDate: isSet(object.firstDate) ? String(object.firstDate) : "",
            lastDate: isSet(object.lastDate) ? String(object.lastDate) : "",
            leaders: Array.isArray(object === null || object === void 0 ? void 0 : object.leaders) ? object.leaders.map((e) => exports.LeaderboardRow.fromJSON(e)) : [],
            accountRow: isSet(object.accountRow) ? exports.LeaderboardRow.fromJSON(object.accountRow) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.firstDate !== undefined && (obj.firstDate = message.firstDate);
        message.lastDate !== undefined && (obj.lastDate = message.lastDate);
        if (message.leaders) {
            obj.leaders = message.leaders.map((e) => e ? exports.LeaderboardRow.toJSON(e) : undefined);
        }
        else {
            obj.leaders = [];
        }
        message.accountRow !== undefined &&
            (obj.accountRow = message.accountRow ? exports.LeaderboardRow.toJSON(message.accountRow) : undefined);
        return obj;
    },
    create(base) {
        return exports.VolLeaderboardFixedResolutionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseVolLeaderboardFixedResolutionResponse();
        message.firstDate = (_a = object.firstDate) !== null && _a !== void 0 ? _a : "";
        message.lastDate = (_b = object.lastDate) !== null && _b !== void 0 ? _b : "";
        message.leaders = ((_c = object.leaders) === null || _c === void 0 ? void 0 : _c.map((e) => exports.LeaderboardRow.fromPartial(e))) || [];
        message.accountRow = (object.accountRow !== undefined && object.accountRow !== null)
            ? exports.LeaderboardRow.fromPartial(object.accountRow)
            : undefined;
        return message;
    },
};
function createBaseDenomHoldersRequest() {
    return { denom: "", token: "", limit: 0 };
}
exports.DenomHoldersRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.token !== "") {
            writer.uint32(18).string(message.token);
        }
        if (message.limit !== 0) {
            writer.uint32(24).sint32(message.limit);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDenomHoldersRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.token = reader.string();
                    break;
                case 3:
                    message.limit = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            token: isSet(object.token) ? String(object.token) : "",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.token !== undefined && (obj.token = message.token);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        return obj;
    },
    create(base) {
        return exports.DenomHoldersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseDenomHoldersRequest();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.token = (_b = object.token) !== null && _b !== void 0 ? _b : "";
        message.limit = (_c = object.limit) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseDenomHoldersResponse() {
    return { holders: [], next: [], total: 0 };
}
exports.DenomHoldersResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.holders) {
            exports.Holder.encode(v, writer.uint32(10).fork()).ldelim();
        }
        for (const v of message.next) {
            writer.uint32(18).string(v);
        }
        if (message.total !== 0) {
            writer.uint32(24).sint32(message.total);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDenomHoldersResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.holders.push(exports.Holder.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.next.push(reader.string());
                    break;
                case 3:
                    message.total = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            holders: Array.isArray(object === null || object === void 0 ? void 0 : object.holders) ? object.holders.map((e) => exports.Holder.fromJSON(e)) : [],
            next: Array.isArray(object === null || object === void 0 ? void 0 : object.next) ? object.next.map((e) => String(e)) : [],
            total: isSet(object.total) ? Number(object.total) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.holders) {
            obj.holders = message.holders.map((e) => e ? exports.Holder.toJSON(e) : undefined);
        }
        else {
            obj.holders = [];
        }
        if (message.next) {
            obj.next = message.next.map((e) => e);
        }
        else {
            obj.next = [];
        }
        message.total !== undefined && (obj.total = Math.round(message.total));
        return obj;
    },
    create(base) {
        return exports.DenomHoldersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseDenomHoldersResponse();
        message.holders = ((_a = object.holders) === null || _a === void 0 ? void 0 : _a.map((e) => exports.Holder.fromPartial(e))) || [];
        message.next = ((_b = object.next) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        message.total = (_c = object.total) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseHolder() {
    return { accountAddress: "", balance: "" };
}
exports.Holder = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.balance !== "") {
            writer.uint32(18).string(message.balance);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHolder();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.balance = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            balance: isSet(object.balance) ? String(object.balance) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.balance !== undefined && (obj.balance = message.balance);
        return obj;
    },
    create(base) {
        return exports.Holder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseHolder();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.balance = (_b = object.balance) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseHistoricalTradesRequest() {
    return { fromBlock: "0", endBlock: "0", fromTime: "0", endTime: "0", perPage: 0, token: "", account: "" };
}
exports.HistoricalTradesRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.fromBlock !== "0") {
            writer.uint32(8).uint64(message.fromBlock);
        }
        if (message.endBlock !== "0") {
            writer.uint32(16).uint64(message.endBlock);
        }
        if (message.fromTime !== "0") {
            writer.uint32(24).sint64(message.fromTime);
        }
        if (message.endTime !== "0") {
            writer.uint32(32).sint64(message.endTime);
        }
        if (message.perPage !== 0) {
            writer.uint32(40).sint32(message.perPage);
        }
        if (message.token !== "") {
            writer.uint32(50).string(message.token);
        }
        if (message.account !== "") {
            writer.uint32(58).string(message.account);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalTradesRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.fromBlock = longToString(reader.uint64());
                    break;
                case 2:
                    message.endBlock = longToString(reader.uint64());
                    break;
                case 3:
                    message.fromTime = longToString(reader.sint64());
                    break;
                case 4:
                    message.endTime = longToString(reader.sint64());
                    break;
                case 5:
                    message.perPage = reader.sint32();
                    break;
                case 6:
                    message.token = reader.string();
                    break;
                case 7:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            fromBlock: isSet(object.fromBlock) ? String(object.fromBlock) : "0",
            endBlock: isSet(object.endBlock) ? String(object.endBlock) : "0",
            fromTime: isSet(object.fromTime) ? String(object.fromTime) : "0",
            endTime: isSet(object.endTime) ? String(object.endTime) : "0",
            perPage: isSet(object.perPage) ? Number(object.perPage) : 0,
            token: isSet(object.token) ? String(object.token) : "",
            account: isSet(object.account) ? String(object.account) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.fromBlock !== undefined && (obj.fromBlock = message.fromBlock);
        message.endBlock !== undefined && (obj.endBlock = message.endBlock);
        message.fromTime !== undefined && (obj.fromTime = message.fromTime);
        message.endTime !== undefined && (obj.endTime = message.endTime);
        message.perPage !== undefined && (obj.perPage = Math.round(message.perPage));
        message.token !== undefined && (obj.token = message.token);
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create(base) {
        return exports.HistoricalTradesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBaseHistoricalTradesRequest();
        message.fromBlock = (_a = object.fromBlock) !== null && _a !== void 0 ? _a : "0";
        message.endBlock = (_b = object.endBlock) !== null && _b !== void 0 ? _b : "0";
        message.fromTime = (_c = object.fromTime) !== null && _c !== void 0 ? _c : "0";
        message.endTime = (_d = object.endTime) !== null && _d !== void 0 ? _d : "0";
        message.perPage = (_e = object.perPage) !== null && _e !== void 0 ? _e : 0;
        message.token = (_f = object.token) !== null && _f !== void 0 ? _f : "";
        message.account = (_g = object.account) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseHistoricalTradesResponse() {
    return { trades: [], lastHeight: "0", lastTime: "0", next: [] };
}
exports.HistoricalTradesResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.trades) {
            exports.HistoricalTrade.encode(v, writer.uint32(10).fork()).ldelim();
        }
        if (message.lastHeight !== "0") {
            writer.uint32(16).uint64(message.lastHeight);
        }
        if (message.lastTime !== "0") {
            writer.uint32(24).sint64(message.lastTime);
        }
        for (const v of message.next) {
            writer.uint32(34).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalTradesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.trades.push(exports.HistoricalTrade.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.lastHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.lastTime = longToString(reader.sint64());
                    break;
                case 4:
                    message.next.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            trades: Array.isArray(object === null || object === void 0 ? void 0 : object.trades) ? object.trades.map((e) => exports.HistoricalTrade.fromJSON(e)) : [],
            lastHeight: isSet(object.lastHeight) ? String(object.lastHeight) : "0",
            lastTime: isSet(object.lastTime) ? String(object.lastTime) : "0",
            next: Array.isArray(object === null || object === void 0 ? void 0 : object.next) ? object.next.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.trades) {
            obj.trades = message.trades.map((e) => e ? exports.HistoricalTrade.toJSON(e) : undefined);
        }
        else {
            obj.trades = [];
        }
        message.lastHeight !== undefined && (obj.lastHeight = message.lastHeight);
        message.lastTime !== undefined && (obj.lastTime = message.lastTime);
        if (message.next) {
            obj.next = message.next.map((e) => e);
        }
        else {
            obj.next = [];
        }
        return obj;
    },
    create(base) {
        return exports.HistoricalTradesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseHistoricalTradesResponse();
        message.trades = ((_a = object.trades) === null || _a === void 0 ? void 0 : _a.map((e) => exports.HistoricalTrade.fromPartial(e))) || [];
        message.lastHeight = (_b = object.lastHeight) !== null && _b !== void 0 ? _b : "0";
        message.lastTime = (_c = object.lastTime) !== null && _c !== void 0 ? _c : "0";
        message.next = ((_d = object.next) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        return message;
    },
};
function createBaseHistoricalTrade() {
    return {
        account: "",
        subaccountId: "",
        marketId: "",
        tradeDirection: "",
        price: undefined,
        fee: "",
        executedAt: "0",
        executedHeight: "0",
        feeRecipient: "",
        executionSide: "",
        usdValue: "",
        flags: [],
        marketType: "",
        tradeId: "",
    };
}
exports.HistoricalTrade = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(26).string(message.marketId);
        }
        if (message.tradeDirection !== "") {
            writer.uint32(34).string(message.tradeDirection);
        }
        if (message.price !== undefined) {
            exports.PriceLevel.encode(message.price, writer.uint32(42).fork()).ldelim();
        }
        if (message.fee !== "") {
            writer.uint32(50).string(message.fee);
        }
        if (message.executedAt !== "0") {
            writer.uint32(56).sint64(message.executedAt);
        }
        if (message.executedHeight !== "0") {
            writer.uint32(64).uint64(message.executedHeight);
        }
        if (message.feeRecipient !== "") {
            writer.uint32(74).string(message.feeRecipient);
        }
        if (message.executionSide !== "") {
            writer.uint32(82).string(message.executionSide);
        }
        if (message.usdValue !== "") {
            writer.uint32(90).string(message.usdValue);
        }
        for (const v of message.flags) {
            writer.uint32(98).string(v);
        }
        if (message.marketType !== "") {
            writer.uint32(106).string(message.marketType);
        }
        if (message.tradeId !== "") {
            writer.uint32(114).string(message.tradeId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalTrade();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.tradeDirection = reader.string();
                    break;
                case 5:
                    message.price = exports.PriceLevel.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.fee = reader.string();
                    break;
                case 7:
                    message.executedAt = longToString(reader.sint64());
                    break;
                case 8:
                    message.executedHeight = longToString(reader.uint64());
                    break;
                case 9:
                    message.feeRecipient = reader.string();
                    break;
                case 10:
                    message.executionSide = reader.string();
                    break;
                case 11:
                    message.usdValue = reader.string();
                    break;
                case 12:
                    message.flags.push(reader.string());
                    break;
                case 13:
                    message.marketType = reader.string();
                    break;
                case 14:
                    message.tradeId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            tradeDirection: isSet(object.tradeDirection) ? String(object.tradeDirection) : "",
            price: isSet(object.price) ? exports.PriceLevel.fromJSON(object.price) : undefined,
            fee: isSet(object.fee) ? String(object.fee) : "",
            executedAt: isSet(object.executedAt) ? String(object.executedAt) : "0",
            executedHeight: isSet(object.executedHeight) ? String(object.executedHeight) : "0",
            feeRecipient: isSet(object.feeRecipient) ? String(object.feeRecipient) : "",
            executionSide: isSet(object.executionSide) ? String(object.executionSide) : "",
            usdValue: isSet(object.usdValue) ? String(object.usdValue) : "",
            flags: Array.isArray(object === null || object === void 0 ? void 0 : object.flags) ? object.flags.map((e) => String(e)) : [],
            marketType: isSet(object.marketType) ? String(object.marketType) : "",
            tradeId: isSet(object.tradeId) ? String(object.tradeId) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.tradeDirection !== undefined && (obj.tradeDirection = message.tradeDirection);
        message.price !== undefined && (obj.price = message.price ? exports.PriceLevel.toJSON(message.price) : undefined);
        message.fee !== undefined && (obj.fee = message.fee);
        message.executedAt !== undefined && (obj.executedAt = message.executedAt);
        message.executedHeight !== undefined && (obj.executedHeight = message.executedHeight);
        message.feeRecipient !== undefined && (obj.feeRecipient = message.feeRecipient);
        message.executionSide !== undefined && (obj.executionSide = message.executionSide);
        message.usdValue !== undefined && (obj.usdValue = message.usdValue);
        if (message.flags) {
            obj.flags = message.flags.map((e) => e);
        }
        else {
            obj.flags = [];
        }
        message.marketType !== undefined && (obj.marketType = message.marketType);
        message.tradeId !== undefined && (obj.tradeId = message.tradeId);
        return obj;
    },
    create(base) {
        return exports.HistoricalTrade.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        const message = createBaseHistoricalTrade();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.marketId = (_c = object.marketId) !== null && _c !== void 0 ? _c : "";
        message.tradeDirection = (_d = object.tradeDirection) !== null && _d !== void 0 ? _d : "";
        message.price = (object.price !== undefined && object.price !== null)
            ? exports.PriceLevel.fromPartial(object.price)
            : undefined;
        message.fee = (_e = object.fee) !== null && _e !== void 0 ? _e : "";
        message.executedAt = (_f = object.executedAt) !== null && _f !== void 0 ? _f : "0";
        message.executedHeight = (_g = object.executedHeight) !== null && _g !== void 0 ? _g : "0";
        message.feeRecipient = (_h = object.feeRecipient) !== null && _h !== void 0 ? _h : "";
        message.executionSide = (_j = object.executionSide) !== null && _j !== void 0 ? _j : "";
        message.usdValue = (_k = object.usdValue) !== null && _k !== void 0 ? _k : "";
        message.flags = ((_l = object.flags) === null || _l === void 0 ? void 0 : _l.map((e) => e)) || [];
        message.marketType = (_m = object.marketType) !== null && _m !== void 0 ? _m : "";
        message.tradeId = (_o = object.tradeId) !== null && _o !== void 0 ? _o : "";
        return message;
    },
};
function createBasePriceLevel() {
    return { price: "", quantity: "", timestamp: "0" };
}
exports.PriceLevel = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.timestamp !== "0") {
            writer.uint32(24).sint64(message.timestamp);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePriceLevel();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.timestamp = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create(base) {
        return exports.PriceLevel.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBasePriceLevel();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.timestamp = (_c = object.timestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
class InjectiveArchiverRPCClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.Balance = this.Balance.bind(this);
        this.Rpnl = this.Rpnl.bind(this);
        this.Volumes = this.Volumes.bind(this);
        this.PnlLeaderboard = this.PnlLeaderboard.bind(this);
        this.VolLeaderboard = this.VolLeaderboard.bind(this);
        this.PnlLeaderboardFixedResolution = this.PnlLeaderboardFixedResolution.bind(this);
        this.VolLeaderboardFixedResolution = this.VolLeaderboardFixedResolution.bind(this);
        this.DenomHolders = this.DenomHolders.bind(this);
        this.HistoricalTrades = this.HistoricalTrades.bind(this);
    }
    Balance(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCBalanceDesc, exports.BalanceRequest.fromPartial(request), metadata);
    }
    Rpnl(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCRpnlDesc, exports.RpnlRequest.fromPartial(request), metadata);
    }
    Volumes(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCVolumesDesc, exports.VolumesRequest.fromPartial(request), metadata);
    }
    PnlLeaderboard(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCPnlLeaderboardDesc, exports.PnlLeaderboardRequest.fromPartial(request), metadata);
    }
    VolLeaderboard(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCVolLeaderboardDesc, exports.VolLeaderboardRequest.fromPartial(request), metadata);
    }
    PnlLeaderboardFixedResolution(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCPnlLeaderboardFixedResolutionDesc, exports.PnlLeaderboardFixedResolutionRequest.fromPartial(request), metadata);
    }
    VolLeaderboardFixedResolution(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCVolLeaderboardFixedResolutionDesc, exports.VolLeaderboardFixedResolutionRequest.fromPartial(request), metadata);
    }
    DenomHolders(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCDenomHoldersDesc, exports.DenomHoldersRequest.fromPartial(request), metadata);
    }
    HistoricalTrades(request, metadata) {
        return this.rpc.unary(exports.InjectiveArchiverRPCHistoricalTradesDesc, exports.HistoricalTradesRequest.fromPartial(request), metadata);
    }
}
exports.InjectiveArchiverRPCClientImpl = InjectiveArchiverRPCClientImpl;
exports.InjectiveArchiverRPCDesc = { serviceName: "injective_archiver_rpc.InjectiveArchiverRPC" };
exports.InjectiveArchiverRPCBalanceDesc = {
    methodName: "Balance",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.BalanceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.BalanceResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCRpnlDesc = {
    methodName: "Rpnl",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.RpnlRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.RpnlResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCVolumesDesc = {
    methodName: "Volumes",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.VolumesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.VolumesResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCPnlLeaderboardDesc = {
    methodName: "PnlLeaderboard",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PnlLeaderboardRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PnlLeaderboardResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCVolLeaderboardDesc = {
    methodName: "VolLeaderboard",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.VolLeaderboardRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.VolLeaderboardResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCPnlLeaderboardFixedResolutionDesc = {
    methodName: "PnlLeaderboardFixedResolution",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PnlLeaderboardFixedResolutionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PnlLeaderboardFixedResolutionResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCVolLeaderboardFixedResolutionDesc = {
    methodName: "VolLeaderboardFixedResolution",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.VolLeaderboardFixedResolutionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.VolLeaderboardFixedResolutionResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCDenomHoldersDesc = {
    methodName: "DenomHolders",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.DenomHoldersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.DenomHoldersResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveArchiverRPCHistoricalTradesDesc = {
    methodName: "HistoricalTrades",
    service: exports.InjectiveArchiverRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.HistoricalTradesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.HistoricalTradesResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
