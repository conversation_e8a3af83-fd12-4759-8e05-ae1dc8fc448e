"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const numbers_js_1 = require("../../../../utils/numbers.js");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const createMessage = (params) => {
    const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgInstantBinaryOptionsMarketLaunch.create();
    message.sender = params.proposer;
    message.ticker = params.market.ticker;
    message.oracleSymbol = params.market.oracleSymbol;
    message.oracleProvider = params.market.oracleProvider;
    message.oracleType = params.market.oracleType;
    message.oracleScaleFactor = params.market.oracleScaleFactor;
    message.makerFeeRate = params.market.makerFeeRate;
    message.takerFeeRate = params.market.takerFeeRate;
    message.expirationTimestamp = params.market.expirationTimestamp.toString();
    message.settlementTimestamp = params.market.settlementTimestamp.toString();
    message.admin = params.market.admin;
    message.quoteDenom = params.market.quoteDenom;
    message.minPriceTickSize = params.market.minPriceTickSize;
    message.minQuantityTickSize = params.market.minQuantityTickSize;
    message.minNotional = params.market.minNotional;
    return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgInstantBinaryOptionsMarketLaunch.fromPartial(message);
};
/**
 * @category Messages
 */
class MsgInstantBinaryOptionsMarketLaunch extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgInstantBinaryOptionsMarketLaunch(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            market: {
                ...initialParams.market,
                minPriceTickSize: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minPriceTickSize).toFixed(),
                makerFeeRate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.makerFeeRate).toFixed(),
                takerFeeRate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.takerFeeRate).toFixed(),
                minQuantityTickSize: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minQuantityTickSize).toFixed(),
                minNotional: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minNotional).toFixed(),
            },
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgInstantBinaryOptionsMarketLaunch',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const msg = createMessage(params);
        const message = {
            ...(0, snakecase_keys_1.default)(msg),
        };
        return {
            type: 'exchange/MsgInstantBinaryOptionsMarketLaunch',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgInstantBinaryOptionsMarketLaunch',
            ...value,
        };
    }
    toEip712() {
        const amino = this.toAmino();
        const { type, value } = amino;
        const messageAdjusted = {
            ...value,
            min_price_tick_size: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.min_price_tick_size).toFixed(),
            min_quantity_tick_size: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.min_quantity_tick_size).toFixed(),
            min_notional: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.min_notional).toFixed(),
            taker_fee_rate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.taker_fee_rate).toFixed(),
            maker_fee_rate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.maker_fee_rate).toFixed(),
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const messageAdjusted = {
            ...web3gw,
            min_price_tick_size: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minPriceTickSize),
            min_quantity_tick_size: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minQuantityTickSize),
            min_notional: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minNotional),
            taker_fee_rate: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.takerFeeRate),
            maker_fee_rate: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.makerFeeRate),
            oracle_type: core_proto_ts_1.InjectiveOracleV1Beta1Oracle.oracleTypeToJSON(params.market.oracleType),
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgInstantBinaryOptionsMarketLaunch',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgInstantBinaryOptionsMarketLaunch.encode(this.toProto()).finish();
    }
}
exports.default = MsgInstantBinaryOptionsMarketLaunch;
