"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisteredInterchainAccount = exports.ActiveChannel = exports.HostGenesisState = exports.ControllerGenesisState = exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var controller_1 = require("../../controller/v1/controller.js");
var host_1 = require("../../host/v1/host.js");
exports.protobufPackage = "ibc.applications.interchain_accounts.genesis.v1";
function createBaseGenesisState() {
    return { controllerGenesisState: undefined, hostGenesisState: undefined };
}
exports.GenesisState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.controllerGenesisState !== undefined) {
            exports.ControllerGenesisState.encode(message.controllerGenesisState, writer.uint32(10).fork()).ldelim();
        }
        if (message.hostGenesisState !== undefined) {
            exports.HostGenesisState.encode(message.hostGenesisState, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.controllerGenesisState = exports.ControllerGenesisState.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.hostGenesisState = exports.HostGenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            controllerGenesisState: isSet(object.controllerGenesisState)
                ? exports.ControllerGenesisState.fromJSON(object.controllerGenesisState)
                : undefined,
            hostGenesisState: isSet(object.hostGenesisState) ? exports.HostGenesisState.fromJSON(object.hostGenesisState) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.controllerGenesisState !== undefined && (obj.controllerGenesisState = message.controllerGenesisState
            ? exports.ControllerGenesisState.toJSON(message.controllerGenesisState)
            : undefined);
        message.hostGenesisState !== undefined &&
            (obj.hostGenesisState = message.hostGenesisState ? exports.HostGenesisState.toJSON(message.hostGenesisState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseGenesisState();
        message.controllerGenesisState =
            (object.controllerGenesisState !== undefined && object.controllerGenesisState !== null)
                ? exports.ControllerGenesisState.fromPartial(object.controllerGenesisState)
                : undefined;
        message.hostGenesisState = (object.hostGenesisState !== undefined && object.hostGenesisState !== null)
            ? exports.HostGenesisState.fromPartial(object.hostGenesisState)
            : undefined;
        return message;
    },
};
function createBaseControllerGenesisState() {
    return { activeChannels: [], interchainAccounts: [], ports: [], params: undefined };
}
exports.ControllerGenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _d = __values(message.activeChannels), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                exports.ActiveChannel.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _f = __values(message.interchainAccounts), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                exports.RegisteredInterchainAccount.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _h = __values(message.ports), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (message.params !== undefined) {
            controller_1.Params.encode(message.params, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseControllerGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.activeChannels.push(exports.ActiveChannel.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.interchainAccounts.push(exports.RegisteredInterchainAccount.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.ports.push(reader.string());
                    break;
                case 4:
                    message.params = controller_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            activeChannels: Array.isArray(object === null || object === void 0 ? void 0 : object.activeChannels)
                ? object.activeChannels.map(function (e) { return exports.ActiveChannel.fromJSON(e); })
                : [],
            interchainAccounts: Array.isArray(object === null || object === void 0 ? void 0 : object.interchainAccounts)
                ? object.interchainAccounts.map(function (e) { return exports.RegisteredInterchainAccount.fromJSON(e); })
                : [],
            ports: Array.isArray(object === null || object === void 0 ? void 0 : object.ports) ? object.ports.map(function (e) { return String(e); }) : [],
            params: isSet(object.params) ? controller_1.Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.activeChannels) {
            obj.activeChannels = message.activeChannels.map(function (e) { return e ? exports.ActiveChannel.toJSON(e) : undefined; });
        }
        else {
            obj.activeChannels = [];
        }
        if (message.interchainAccounts) {
            obj.interchainAccounts = message.interchainAccounts.map(function (e) {
                return e ? exports.RegisteredInterchainAccount.toJSON(e) : undefined;
            });
        }
        else {
            obj.interchainAccounts = [];
        }
        if (message.ports) {
            obj.ports = message.ports.map(function (e) { return e; });
        }
        else {
            obj.ports = [];
        }
        message.params !== undefined && (obj.params = message.params ? controller_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ControllerGenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseControllerGenesisState();
        message.activeChannels = ((_a = object.activeChannels) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.ActiveChannel.fromPartial(e); })) || [];
        message.interchainAccounts = ((_b = object.interchainAccounts) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.RegisteredInterchainAccount.fromPartial(e); })) ||
            [];
        message.ports = ((_c = object.ports) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.params = (object.params !== undefined && object.params !== null)
            ? controller_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseHostGenesisState() {
    return { activeChannels: [], interchainAccounts: [], port: "", params: undefined };
}
exports.HostGenesisState = {
    encode: function (message, writer) {
        var e_4, _a, e_5, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.activeChannels), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exports.ActiveChannel.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _e = __values(message.interchainAccounts), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.RegisteredInterchainAccount.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_5) throw e_5.error; }
        }
        if (message.port !== "") {
            writer.uint32(26).string(message.port);
        }
        if (message.params !== undefined) {
            host_1.Params.encode(message.params, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseHostGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.activeChannels.push(exports.ActiveChannel.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.interchainAccounts.push(exports.RegisteredInterchainAccount.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.port = reader.string();
                    break;
                case 4:
                    message.params = host_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            activeChannels: Array.isArray(object === null || object === void 0 ? void 0 : object.activeChannels)
                ? object.activeChannels.map(function (e) { return exports.ActiveChannel.fromJSON(e); })
                : [],
            interchainAccounts: Array.isArray(object === null || object === void 0 ? void 0 : object.interchainAccounts)
                ? object.interchainAccounts.map(function (e) { return exports.RegisteredInterchainAccount.fromJSON(e); })
                : [],
            port: isSet(object.port) ? String(object.port) : "",
            params: isSet(object.params) ? host_1.Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.activeChannels) {
            obj.activeChannels = message.activeChannels.map(function (e) { return e ? exports.ActiveChannel.toJSON(e) : undefined; });
        }
        else {
            obj.activeChannels = [];
        }
        if (message.interchainAccounts) {
            obj.interchainAccounts = message.interchainAccounts.map(function (e) {
                return e ? exports.RegisteredInterchainAccount.toJSON(e) : undefined;
            });
        }
        else {
            obj.interchainAccounts = [];
        }
        message.port !== undefined && (obj.port = message.port);
        message.params !== undefined && (obj.params = message.params ? host_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.HostGenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseHostGenesisState();
        message.activeChannels = ((_a = object.activeChannels) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.ActiveChannel.fromPartial(e); })) || [];
        message.interchainAccounts = ((_b = object.interchainAccounts) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.RegisteredInterchainAccount.fromPartial(e); })) ||
            [];
        message.port = (_c = object.port) !== null && _c !== void 0 ? _c : "";
        message.params = (object.params !== undefined && object.params !== null)
            ? host_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseActiveChannel() {
    return { connectionId: "", portId: "", channelId: "", isMiddlewareEnabled: false };
}
exports.ActiveChannel = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.connectionId !== "") {
            writer.uint32(10).string(message.connectionId);
        }
        if (message.portId !== "") {
            writer.uint32(18).string(message.portId);
        }
        if (message.channelId !== "") {
            writer.uint32(26).string(message.channelId);
        }
        if (message.isMiddlewareEnabled === true) {
            writer.uint32(32).bool(message.isMiddlewareEnabled);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseActiveChannel();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.connectionId = reader.string();
                    break;
                case 2:
                    message.portId = reader.string();
                    break;
                case 3:
                    message.channelId = reader.string();
                    break;
                case 4:
                    message.isMiddlewareEnabled = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            connectionId: isSet(object.connectionId) ? String(object.connectionId) : "",
            portId: isSet(object.portId) ? String(object.portId) : "",
            channelId: isSet(object.channelId) ? String(object.channelId) : "",
            isMiddlewareEnabled: isSet(object.isMiddlewareEnabled) ? Boolean(object.isMiddlewareEnabled) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.connectionId !== undefined && (obj.connectionId = message.connectionId);
        message.portId !== undefined && (obj.portId = message.portId);
        message.channelId !== undefined && (obj.channelId = message.channelId);
        message.isMiddlewareEnabled !== undefined && (obj.isMiddlewareEnabled = message.isMiddlewareEnabled);
        return obj;
    },
    create: function (base) {
        return exports.ActiveChannel.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseActiveChannel();
        message.connectionId = (_a = object.connectionId) !== null && _a !== void 0 ? _a : "";
        message.portId = (_b = object.portId) !== null && _b !== void 0 ? _b : "";
        message.channelId = (_c = object.channelId) !== null && _c !== void 0 ? _c : "";
        message.isMiddlewareEnabled = (_d = object.isMiddlewareEnabled) !== null && _d !== void 0 ? _d : false;
        return message;
    },
};
function createBaseRegisteredInterchainAccount() {
    return { connectionId: "", portId: "", accountAddress: "" };
}
exports.RegisteredInterchainAccount = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.connectionId !== "") {
            writer.uint32(10).string(message.connectionId);
        }
        if (message.portId !== "") {
            writer.uint32(18).string(message.portId);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRegisteredInterchainAccount();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.connectionId = reader.string();
                    break;
                case 2:
                    message.portId = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            connectionId: isSet(object.connectionId) ? String(object.connectionId) : "",
            portId: isSet(object.portId) ? String(object.portId) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.connectionId !== undefined && (obj.connectionId = message.connectionId);
        message.portId !== undefined && (obj.portId = message.portId);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create: function (base) {
        return exports.RegisteredInterchainAccount.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseRegisteredInterchainAccount();
        message.connectionId = (_a = object.connectionId) !== null && _a !== void 0 ? _a : "";
        message.portId = (_b = object.portId) !== null && _b !== void 0 ? _b : "";
        message.accountAddress = (_c = object.accountAddress) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
