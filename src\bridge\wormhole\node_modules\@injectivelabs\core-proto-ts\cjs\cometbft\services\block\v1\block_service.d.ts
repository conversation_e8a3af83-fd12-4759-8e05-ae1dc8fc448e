import { grpc } from "@injectivelabs/grpc-web";
import { Observable } from "rxjs";
import { GetByHeightRequest, GetByHeightResponse, GetLatestHeightRequest, GetLatestHeightResponse } from "./block";
export declare const protobufPackage = "cometbft.services.block.v1";
/** BlockService provides information about blocks */
export interface BlockService {
    /** GetBlock retrieves the block information at a particular height. */
    GetByHeight(request: DeepPartial<GetByHeightRequest>, metadata?: grpc.Metadata): Promise<GetByHeightResponse>;
    /**
     * GetLatestHeight returns a stream of the latest block heights committed by
     * the network. This is a long-lived stream that is only terminated by the
     * server if an error occurs. The caller is expected to handle such
     * disconnections and automatically reconnect.
     */
    GetLatestHeight(request: DeepPartial<GetLatestHeightRequest>, metadata?: grpc.Metadata): Observable<GetLatestHeightResponse>;
}
export declare class BlockServiceClientImpl implements BlockService {
    private readonly rpc;
    constructor(rpc: Rpc);
    GetByHeight(request: DeepPartial<GetByHeightRequest>, metadata?: grpc.Metadata): Promise<GetByHeightResponse>;
    GetLatestHeight(request: DeepPartial<GetLatestHeightRequest>, metadata?: grpc.Metadata): Observable<GetLatestHeightResponse>;
}
export declare const BlockServiceDesc: {
    serviceName: string;
};
export declare const BlockServiceGetByHeightDesc: UnaryMethodDefinitionish;
export declare const BlockServiceGetLatestHeightDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        streamingTransport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
