{"version": 3, "file": "number.js", "sourceRoot": "", "sources": ["../../../../src/coders/base/number.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;AA+BF,oCAmCC;AAED,oCAgCC;AAlGD,6CAAuC;AAEvC,2CAA+C;AAC/C,mDAAuC;AAEvC,0CAAwC;AACxC,yDAAkD;AAElD,sCAAsC;AACtC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AAEtC,SAAS,kBAAkB,CAAC,KAAa,EAAE,UAAU,GAAG,oBAAS;IAChE,IAAI,QAAQ,CAAC;IACb,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACf,QAAQ,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;SAAM,CAAC;QACP,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IACD,QAAQ,GAAG,IAAA,oBAAO,EAAC,QAAQ,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;IAC7C,OAAO,sBAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,kBAAkB,CAAC,KAAiB,EAAE,GAAW;IACzD,MAAM,QAAQ,GAAG,sBAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IAChC,IAAI,MAAM,IAAI,GAAG;QAAE,OAAO,MAAM,CAAC;IACjC,OAAO,MAAM,GAAG,IAAI,CAAC;AACtB,CAAC;AAED,SAAgB,YAAY,CAAC,KAAmB,EAAE,KAAc;IAC/D,IAAI,KAAK,CAAC;IACV,IAAI,CAAC;QACJ,KAAK,GAAG,IAAA,qBAAQ,EAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACZ,MAAM,IAAI,sBAAQ,CAAC,oCAAoC,EAAE;YACxD,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;SAChB,CAAC,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,GAAG,+BAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,MAAM,IAAI,sBAAQ,CAAC,+CAA+C,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3F,CAAC;IACD,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,IAAI,sBAAQ,CAAC,oDAAoD,EAAE;YACxE,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,IAAI,sBAAQ,CAAC,uDAAuD,EAAE;YAC3E,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,OAAO;QACN,OAAO,EAAE,KAAK;QACd,OAAO,EAAE,kBAAkB,CAAC,KAAK,CAAC;KAClC,CAAC;AACH,CAAC;AAED,SAAgB,YAAY,CAAC,KAAmB,EAAE,KAAiB;IAClE,IAAI,KAAK,CAAC,MAAM,GAAG,oBAAS,EAAE,CAAC;QAC9B,MAAM,IAAI,sBAAQ,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3F,CAAC;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,oBAAS,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAG,+BAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,MAAM,IAAI,sBAAQ,CAAC,+CAA+C,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3F,CAAC;IACD,MAAM,YAAY,GAAG,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;IAE9D,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QAC9B,MAAM,IAAI,sBAAQ,CAAC,mDAAmD,EAAE;YACvE,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;QAC9B,MAAM,IAAI,sBAAQ,CAAC,sDAAsD,EAAE;YAC1E,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,YAAY;YACnB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;SAC7B,CAAC,CAAC;IACJ,CAAC;IACD,OAAO;QACN,MAAM,EAAE,YAAY;QACpB,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,oBAAS,CAAC;QAClC,QAAQ,EAAE,oBAAS;KACnB,CAAC;AACH,CAAC"}