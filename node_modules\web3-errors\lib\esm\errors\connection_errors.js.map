{"version": 3, "file": "connection_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/connection_errors.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAKF,OAAO,EACN,QAAQ,EACR,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,cAAc,EACd,qBAAqB,EACrB,yBAAyB,EACzB,oBAAoB,GACpB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEtD,MAAM,OAAO,eAAgB,SAAQ,aAAa;IAKjD,YAAmB,OAAe,EAAE,KAAuB;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QALT,SAAI,GAAG,QAAQ,CAAC;QAOtB,IAAI,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;QACjC,CAAC;IACF,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,IAAG;IACxF,CAAC;CACD;AAED,MAAM,OAAO,sBAAuB,SAAQ,eAAe;IAC1D,YAA0B,IAAY,EAAE,KAAuB;QAC9D,KAAK,CAAC,8CAA8C,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAD3C,SAAI,GAAJ,IAAI,CAAQ;QAErC,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC9B,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,IAAI,EAAE,IAAI,CAAC,IAAI,IAAG;IAC/C,CAAC;CACD;AAED,MAAM,OAAO,sBAAuB,SAAQ,eAAe;IAC1D,YAA0B,QAAgB;QACzC,KAAK,CAAC,kCAAkC,QAAQ,aAAa,CAAC,CAAC;QADtC,aAAQ,GAAR,QAAQ,CAAQ;QAEzC,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC9B,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAG;IACvD,CAAC;CACD;AAED,MAAM,OAAO,sBAAuB,SAAQ,eAAe;IAC1D,YAAmB,KAAuB;QACzC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAC/B,CAAC;CACD;AAED,MAAM,OAAO,oBAAqB,SAAQ,eAAe;IACxD,YAAmB,KAAuB;;QACzC,KAAK,CACJ,mEACC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,mCAAI,EAChB,oCAAoC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,mCAAI,EAAE,EAAE,EACzD,KAAK,CACL,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;IAC5B,CAAC;CACD;AAED,MAAM,OAAO,qCAAsC,SAAQ,eAAe;IACzE,YAAmB,gBAAwB;QAC1C,KAAK,CAAC,kDAAkD,gBAAgB,GAAG,CAAC,CAAC;QAC7E,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACnC,CAAC;CACD;AAED,MAAM,OAAO,kCAAmC,SAAQ,eAAe;IACtE;QACC,KAAK,CAAC,mFAAmF,CAAC,CAAC;QAC3F,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IACvC,CAAC;CACD;AAED,MAAM,OAAO,uBAAwB,SAAQ,eAAe;IAC3D,YAAmB,EAAmB;QACrC,KAAK,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IAClC,CAAC;CACD"}