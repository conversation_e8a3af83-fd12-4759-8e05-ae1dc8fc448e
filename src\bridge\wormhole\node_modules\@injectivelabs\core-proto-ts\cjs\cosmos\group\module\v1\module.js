"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Module = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var duration_1 = require("../../../../google/protobuf/duration.js");
exports.protobufPackage = "cosmos.group.module.v1";
function createBaseModule() {
    return { maxExecutionPeriod: undefined, maxMetadataLen: "0" };
}
exports.Module = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.maxExecutionPeriod !== undefined) {
            duration_1.Duration.encode(message.maxExecutionPeriod, writer.uint32(10).fork()).ldelim();
        }
        if (message.maxMetadataLen !== "0") {
            writer.uint32(16).uint64(message.maxMetadataLen);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.maxExecutionPeriod = duration_1.Duration.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.maxMetadataLen = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            maxExecutionPeriod: isSet(object.maxExecutionPeriod) ? duration_1.Duration.fromJSON(object.maxExecutionPeriod) : undefined,
            maxMetadataLen: isSet(object.maxMetadataLen) ? String(object.maxMetadataLen) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.maxExecutionPeriod !== undefined &&
            (obj.maxExecutionPeriod = message.maxExecutionPeriod ? duration_1.Duration.toJSON(message.maxExecutionPeriod) : undefined);
        message.maxMetadataLen !== undefined && (obj.maxMetadataLen = message.maxMetadataLen);
        return obj;
    },
    create: function (base) {
        return exports.Module.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseModule();
        message.maxExecutionPeriod = (object.maxExecutionPeriod !== undefined && object.maxExecutionPeriod !== null)
            ? duration_1.Duration.fromPartial(object.maxExecutionPeriod)
            : undefined;
        message.maxMetadataLen = (_a = object.maxMetadataLen) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
