import { IndexerGrpcDerivativeTransformer } from './IndexerGrpcDerivativeTransformer.js';
/**
 * @category Indexer Stream Transformer
 */
export class IndexerDerivativeStreamTransformer {
    static tradesStreamCallback = (response) => {
        const trade = response.trade;
        return {
            trade: trade
                ? IndexerGrpcDerivativeTransformer.grpcTradeToTrade(trade)
                : undefined,
            operation: response.operationType,
            timestamp: response.timestamp,
        };
    };
    static positionStreamCallback = (response) => {
        const position = response.position;
        return {
            position: position
                ? IndexerGrpcDerivativeTransformer.grpcPositionToPosition(position)
                : undefined,
            timestamp: response.timestamp,
        };
    };
    static ordersStreamCallback = (response) => {
        const order = response.order;
        return {
            order: order
                ? IndexerGrpcDerivativeTransformer.grpcOrderToOrder(order)
                : undefined,
            operation: response.operationType,
            timestamp: response.timestamp,
        };
    };
    static orderHistoryStreamCallback = (response) => {
        const order = response.order;
        return {
            order: order
                ? IndexerGrpcDerivativeTransformer.grpcOrderHistoryToOrderHistory(order)
                : undefined,
            operation: response.operationType,
            timestamp: response.timestamp,
        };
    };
    static orderbookV2StreamCallback = (response) => {
        const orderbook = response.orderbook;
        return {
            orderbook: orderbook
                ? IndexerGrpcDerivativeTransformer.grpcOrderbookV2ToOrderbookV2({
                    sequence: parseInt(orderbook.sequence, 10),
                    buys: orderbook.buys,
                    sells: orderbook.sells,
                })
                : undefined,
            operation: response.operationType,
            marketId: response.marketId,
            timestamp: response.timestamp,
        };
    };
    static orderbookUpdateStreamCallback = (response) => {
        const orderbook = response.orderbookLevelUpdates;
        return {
            orderbook: orderbook
                ? IndexerGrpcDerivativeTransformer.grpcOrderbookV2ToOrderbookV2({
                    sequence: parseInt(orderbook.sequence, 10),
                    buys: orderbook.buys,
                    sells: orderbook.sells,
                })
                : undefined,
            operation: response.operationType,
            marketId: response.marketId,
            timestamp: response.timestamp,
        };
    };
    static positionV2StreamCallback = (response) => {
        const position = response.position;
        return {
            position: position
                ? IndexerGrpcDerivativeTransformer.grpcPositionV2ToPositionV2(position)
                : undefined,
            timestamp: response.timestamp,
        };
    };
}
