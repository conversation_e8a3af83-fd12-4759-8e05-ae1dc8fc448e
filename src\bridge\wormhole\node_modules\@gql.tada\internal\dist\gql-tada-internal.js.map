{"version": 3, "file": "gql-tada-internal.js", "sources": ["../../../node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core-chunk.mjs", "../src/loaders/introspection.ts", "../src/loaders/sdl.ts", "../../../node_modules/.pnpm/@urql+exchange-retry@1.3.0_@urql+core@5.0.6_graphql@16.9.0_/node_modules/@urql/exchange-retry/dist/urql-exchange-retry.mjs", "../src/loaders/url.ts", "../src/loaders/index.ts", "../src/helpers.ts", "../src/errors.ts", "../src/config.ts", "../src/resolve.ts", "../src/introspection/minify.ts", "../src/introspection/preprocess.ts", "../src/introspection/constants.ts", "../src/introspection/output.ts"], "sourcesContent": ["import { GraphQLError as e, print as r, parse as t, Kind as a } from \"@0no-co/graphql.web\";\n\nimport { onEnd as o, filter as n, fromAsyncIterable as s } from \"wonka\";\n\nvar rehydrateGraphQlError = r => {\n  if (r && \"string\" == typeof r.message && (r.extensions || \"GraphQLError\" === r.name)) {\n    return r;\n  } else if (\"object\" == typeof r && \"string\" == typeof r.message) {\n    return new e(r.message, r.nodes, r.source, r.positions, r.path, r, r.extensions || {});\n  } else {\n    return new e(r);\n  }\n};\n\nclass CombinedError extends Error {\n  constructor(e) {\n    var r = (e.graphQLErrors || []).map(rehydrateGraphQlError);\n    var t = ((e, r) => {\n      var t = \"\";\n      if (e) {\n        return `[Network] ${e.message}`;\n      }\n      if (r) {\n        for (var a of r) {\n          if (t) {\n            t += \"\\n\";\n          }\n          t += `[GraphQL] ${a.message}`;\n        }\n      }\n      return t;\n    })(e.networkError, r);\n    super(t);\n    this.name = \"CombinedError\";\n    this.message = t;\n    this.graphQLErrors = r;\n    this.networkError = e.networkError;\n    this.response = e.response;\n  }\n  toString() {\n    return this.message;\n  }\n}\n\nvar phash = (e, r) => {\n  var t = 0 | (r || 5381);\n  for (var a = 0, o = 0 | e.length; a < o; a++) {\n    t = (t << 5) + t + e.charCodeAt(a);\n  }\n  return t;\n};\n\nvar i = new Set;\n\nvar f = new WeakMap;\n\nvar stringify = (e, r) => {\n  if (null === e || i.has(e)) {\n    return \"null\";\n  } else if (\"object\" != typeof e) {\n    return JSON.stringify(e) || \"\";\n  } else if (e.toJSON) {\n    return stringify(e.toJSON(), r);\n  } else if (Array.isArray(e)) {\n    var t = \"[\";\n    for (var a of e) {\n      if (t.length > 1) {\n        t += \",\";\n      }\n      t += stringify(a, r) || \"null\";\n    }\n    return t += \"]\";\n  } else if (!r && (l !== NoopConstructor && e instanceof l || c !== NoopConstructor && e instanceof c)) {\n    return \"null\";\n  }\n  var o = Object.keys(e).sort();\n  if (!o.length && e.constructor && Object.getPrototypeOf(e).constructor !== Object.prototype.constructor) {\n    var n = f.get(e) || Math.random().toString(36).slice(2);\n    f.set(e, n);\n    return stringify({\n      __key: n\n    }, r);\n  }\n  i.add(e);\n  var s = \"{\";\n  for (var d of o) {\n    var v = stringify(e[d], r);\n    if (v) {\n      if (s.length > 1) {\n        s += \",\";\n      }\n      s += stringify(d, r) + \":\" + v;\n    }\n  }\n  i.delete(e);\n  return s += \"}\";\n};\n\nvar extract = (e, r, t) => {\n  if (null == t || \"object\" != typeof t || t.toJSON || i.has(t)) {} else if (Array.isArray(t)) {\n    for (var a = 0, o = t.length; a < o; a++) {\n      extract(e, `${r}.${a}`, t[a]);\n    }\n  } else if (t instanceof l || t instanceof c) {\n    e.set(r, t);\n  } else {\n    i.add(t);\n    for (var n of Object.keys(t)) {\n      extract(e, `${r}.${n}`, t[n]);\n    }\n  }\n};\n\nvar stringifyVariables = (e, r) => {\n  i.clear();\n  return stringify(e, r || !1);\n};\n\nclass NoopConstructor {}\n\nvar l = \"undefined\" != typeof File ? File : NoopConstructor;\n\nvar c = \"undefined\" != typeof Blob ? Blob : NoopConstructor;\n\nvar d = /(\"{3}[\\s\\S]*\"{3}|\"(?:\\\\.|[^\"])*\")/g;\n\nvar v = /(?:#[^\\n\\r]+)?(?:[\\r\\n]+|$)/g;\n\nvar replaceOutsideStrings = (e, r) => r % 2 == 0 ? e.replace(v, \"\\n\") : e;\n\nvar sanitizeDocument = e => e.split(d).map(replaceOutsideStrings).join(\"\").trim();\n\nvar p = new Map;\n\nvar u = new Map;\n\nvar stringifyDocument = e => {\n  var t;\n  if (\"string\" == typeof e) {\n    t = sanitizeDocument(e);\n  } else if (e.loc && u.get(e.__key) === e) {\n    t = e.loc.source.body;\n  } else {\n    t = p.get(e) || sanitizeDocument(r(e));\n    p.set(e, t);\n  }\n  if (\"string\" != typeof e && !e.loc) {\n    e.loc = {\n      start: 0,\n      end: t.length,\n      source: {\n        body: t,\n        name: \"gql\",\n        locationOffset: {\n          line: 1,\n          column: 1\n        }\n      }\n    };\n  }\n  return t;\n};\n\nvar hashDocument = e => {\n  var r;\n  if (e.documentId) {\n    r = phash(e.documentId);\n  } else {\n    r = phash(stringifyDocument(e));\n    if (e.definitions) {\n      var t = getOperationName(e);\n      if (t) {\n        r = phash(`\\n# ${t}`, r);\n      }\n    }\n  }\n  return r;\n};\n\nvar keyDocument = e => {\n  var r;\n  var a;\n  if (\"string\" == typeof e) {\n    r = hashDocument(e);\n    a = u.get(r) || t(e, {\n      noLocation: !0\n    });\n  } else {\n    r = e.__key || hashDocument(e);\n    a = u.get(r) || e;\n  }\n  if (!a.loc) {\n    stringifyDocument(a);\n  }\n  a.__key = r;\n  u.set(r, a);\n  return a;\n};\n\nvar createRequest = (e, r, t) => {\n  var a = r || {};\n  var o = keyDocument(e);\n  var n = stringifyVariables(a, !0);\n  var s = o.__key;\n  if (\"{}\" !== n) {\n    s = phash(n, s);\n  }\n  return {\n    key: s,\n    query: o,\n    variables: a,\n    extensions: t\n  };\n};\n\nvar getOperationName = e => {\n  for (var r of e.definitions) {\n    if (r.kind === a.OPERATION_DEFINITION) {\n      return r.name ? r.name.value : void 0;\n    }\n  }\n};\n\nvar getOperationType = e => {\n  for (var r of e.definitions) {\n    if (r.kind === a.OPERATION_DEFINITION) {\n      return r.operation;\n    }\n  }\n};\n\nvar makeResult = (e, r, t) => {\n  if (!(\"data\" in r || \"errors\" in r && Array.isArray(r.errors))) {\n    throw new Error(\"No Content\");\n  }\n  var a = \"subscription\" === e.kind;\n  return {\n    operation: e,\n    data: r.data,\n    error: Array.isArray(r.errors) ? new CombinedError({\n      graphQLErrors: r.errors,\n      response: t\n    }) : void 0,\n    extensions: r.extensions ? {\n      ...r.extensions\n    } : void 0,\n    hasNext: null == r.hasNext ? a : r.hasNext,\n    stale: !1\n  };\n};\n\nvar deepMerge = (e, r) => {\n  if (\"object\" == typeof e && null != e) {\n    if (!e.constructor || e.constructor === Object || Array.isArray(e)) {\n      e = Array.isArray(e) ? [ ...e ] : {\n        ...e\n      };\n      for (var t of Object.keys(r)) {\n        e[t] = deepMerge(e[t], r[t]);\n      }\n      return e;\n    }\n  }\n  return r;\n};\n\nvar mergeResultPatch = (e, r, t, a) => {\n  var o = e.error ? e.error.graphQLErrors : [];\n  var n = !!e.extensions || !!(r.payload || r).extensions;\n  var s = {\n    ...e.extensions,\n    ...(r.payload || r).extensions\n  };\n  var i = r.incremental;\n  if (\"path\" in r) {\n    i = [ r ];\n  }\n  var f = {\n    data: e.data\n  };\n  if (i) {\n    var _loop = function(e) {\n      if (Array.isArray(e.errors)) {\n        o.push(...e.errors);\n      }\n      if (e.extensions) {\n        Object.assign(s, e.extensions);\n        n = !0;\n      }\n      var r = \"data\";\n      var t = f;\n      var i = [];\n      if (e.path) {\n        i = e.path;\n      } else if (a) {\n        var l = a.find((r => r.id === e.id));\n        if (e.subPath) {\n          i = [ ...l.path, ...e.subPath ];\n        } else {\n          i = l.path;\n        }\n      }\n      for (var c = 0, d = i.length; c < d; r = i[c++]) {\n        t = t[r] = Array.isArray(t[r]) ? [ ...t[r] ] : {\n          ...t[r]\n        };\n      }\n      if (e.items) {\n        var v = +r >= 0 ? r : 0;\n        for (var p = 0, u = e.items.length; p < u; p++) {\n          t[v + p] = deepMerge(t[v + p], e.items[p]);\n        }\n      } else if (void 0 !== e.data) {\n        t[r] = deepMerge(t[r], e.data);\n      }\n    };\n    for (var l of i) {\n      _loop(l);\n    }\n  } else {\n    f.data = (r.payload || r).data || e.data;\n    o = r.errors || r.payload && r.payload.errors || o;\n  }\n  return {\n    operation: e.operation,\n    data: f.data,\n    error: o.length ? new CombinedError({\n      graphQLErrors: o,\n      response: t\n    }) : void 0,\n    extensions: n ? s : void 0,\n    hasNext: null != r.hasNext ? r.hasNext : e.hasNext,\n    stale: !1\n  };\n};\n\nvar makeErrorResult = (e, r, t) => ({\n  operation: e,\n  data: void 0,\n  error: new CombinedError({\n    networkError: r,\n    response: t\n  }),\n  extensions: void 0,\n  hasNext: !1,\n  stale: !1\n});\n\nfunction makeFetchBody(e) {\n  var r = {\n    query: void 0,\n    documentId: void 0,\n    operationName: getOperationName(e.query),\n    variables: e.variables || void 0,\n    extensions: e.extensions\n  };\n  if (\"documentId\" in e.query && e.query.documentId && (!e.query.definitions || !e.query.definitions.length)) {\n    r.documentId = e.query.documentId;\n  } else if (!e.extensions || !e.extensions.persistedQuery || e.extensions.persistedQuery.miss) {\n    r.query = stringifyDocument(e.query);\n  }\n  return r;\n}\n\nvar makeFetchURL = (e, r) => {\n  var t = \"query\" === e.kind && e.context.preferGetMethod;\n  if (!t || !r) {\n    return e.context.url;\n  }\n  var a = splitOutSearchParams(e.context.url);\n  for (var o in r) {\n    var n = r[o];\n    if (n) {\n      a[1].set(o, \"object\" == typeof n ? stringifyVariables(n) : n);\n    }\n  }\n  var s = a.join(\"?\");\n  if (s.length > 2047 && \"force\" !== t) {\n    e.context.preferGetMethod = !1;\n    return e.context.url;\n  }\n  return s;\n};\n\nvar splitOutSearchParams = e => {\n  var r = e.indexOf(\"?\");\n  return r > -1 ? [ e.slice(0, r), new URLSearchParams(e.slice(r + 1)) ] : [ e, new URLSearchParams ];\n};\n\nvar serializeBody = (e, r) => {\n  if (r && !(\"query\" === e.kind && !!e.context.preferGetMethod)) {\n    var t = stringifyVariables(r);\n    var a = (e => {\n      var r = new Map;\n      if (l !== NoopConstructor || c !== NoopConstructor) {\n        i.clear();\n        extract(r, \"variables\", e);\n      }\n      return r;\n    })(r.variables);\n    if (a.size) {\n      var o = new FormData;\n      o.append(\"operations\", t);\n      o.append(\"map\", stringifyVariables({\n        ...[ ...a.keys() ].map((e => [ e ]))\n      }));\n      var n = 0;\n      for (var s of a.values()) {\n        o.append(\"\" + n++, s);\n      }\n      return o;\n    }\n    return t;\n  }\n};\n\nvar makeFetchOptions = (e, r) => {\n  var t = {\n    accept: \"subscription\" === e.kind ? \"text/event-stream, multipart/mixed\" : \"application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed\"\n  };\n  var a = (\"function\" == typeof e.context.fetchOptions ? e.context.fetchOptions() : e.context.fetchOptions) || {};\n  if (a.headers) {\n    if ((e => \"has\" in e && !Object.keys(e).length)(a.headers)) {\n      a.headers.forEach(((e, r) => {\n        t[r] = e;\n      }));\n    } else if (Array.isArray(a.headers)) {\n      a.headers.forEach(((e, r) => {\n        if (Array.isArray(e)) {\n          if (t[e[0]]) {\n            t[e[0]] = `${t[e[0]]},${e[1]}`;\n          } else {\n            t[e[0]] = e[1];\n          }\n        } else {\n          t[r] = e;\n        }\n      }));\n    } else {\n      for (var o in a.headers) {\n        t[o.toLowerCase()] = a.headers[o];\n      }\n    }\n  }\n  var n = serializeBody(e, r);\n  if (\"string\" == typeof n && !t[\"content-type\"]) {\n    t[\"content-type\"] = \"application/json\";\n  }\n  return {\n    ...a,\n    method: n ? \"POST\" : \"GET\",\n    body: n,\n    headers: t\n  };\n};\n\nvar y = \"undefined\" != typeof TextDecoder ? new TextDecoder : null;\n\nvar h = /boundary=\"?([^=\";]+)\"?/i;\n\nvar m = /data: ?([^\\n]+)/;\n\nvar toString = e => \"Buffer\" === e.constructor.name ? e.toString() : y.decode(e);\n\nasync function* streamBody(e) {\n  if (e.body[Symbol.asyncIterator]) {\n    for await (var r of e.body) {\n      yield toString(r);\n    }\n  } else {\n    var t = e.body.getReader();\n    var a;\n    try {\n      while (!(a = await t.read()).done) {\n        yield toString(a.value);\n      }\n    } finally {\n      t.cancel();\n    }\n  }\n}\n\nasync function* split(e, r) {\n  var t = \"\";\n  var a;\n  for await (var o of e) {\n    t += o;\n    while ((a = t.indexOf(r)) > -1) {\n      yield t.slice(0, a);\n      t = t.slice(a + r.length);\n    }\n  }\n}\n\nasync function* fetchOperation(e, r, t) {\n  var a = !0;\n  var o = null;\n  var n;\n  try {\n    yield await Promise.resolve();\n    var s = (n = await (e.context.fetch || fetch)(r, t)).headers.get(\"Content-Type\") || \"\";\n    var i;\n    if (/multipart\\/mixed/i.test(s)) {\n      i = async function* parseMultipartMixed(e, r) {\n        var t = e.match(h);\n        var a = \"--\" + (t ? t[1] : \"-\");\n        var o = !0;\n        var n;\n        for await (var s of split(streamBody(r), \"\\r\\n\" + a)) {\n          if (o) {\n            o = !1;\n            var i = s.indexOf(a);\n            if (i > -1) {\n              s = s.slice(i + a.length);\n            } else {\n              continue;\n            }\n          }\n          try {\n            yield n = JSON.parse(s.slice(s.indexOf(\"\\r\\n\\r\\n\") + 4));\n          } catch (e) {\n            if (!n) {\n              throw e;\n            }\n          }\n          if (n && !1 === n.hasNext) {\n            break;\n          }\n        }\n        if (n && !1 !== n.hasNext) {\n          yield {\n            hasNext: !1\n          };\n        }\n      }(s, n);\n    } else if (/text\\/event-stream/i.test(s)) {\n      i = async function* parseEventStream(e) {\n        var r;\n        for await (var t of split(streamBody(e), \"\\n\\n\")) {\n          var a = t.match(m);\n          if (a) {\n            var o = a[1];\n            try {\n              yield r = JSON.parse(o);\n            } catch (e) {\n              if (!r) {\n                throw e;\n              }\n            }\n            if (r && !1 === r.hasNext) {\n              break;\n            }\n          }\n        }\n        if (r && !1 !== r.hasNext) {\n          yield {\n            hasNext: !1\n          };\n        }\n      }(n);\n    } else if (!/text\\//i.test(s)) {\n      i = async function* parseJSON(e) {\n        yield JSON.parse(await e.text());\n      }(n);\n    } else {\n      i = async function* parseMaybeJSON(e) {\n        var r = await e.text();\n        try {\n          var t = JSON.parse(r);\n          if (\"production\" !== process.env.NODE_ENV) {\n            console.warn('Found response with content-type \"text/plain\" but it had a valid \"application/json\" response.');\n          }\n          yield t;\n        } catch (e) {\n          throw new Error(r);\n        }\n      }(n);\n    }\n    var f;\n    for await (var l of i) {\n      if (l.pending && !o) {\n        f = l.pending;\n      } else if (l.pending) {\n        f = [ ...f, ...l.pending ];\n      }\n      o = o ? mergeResultPatch(o, l, n, f) : makeResult(e, l, n);\n      a = !1;\n      yield o;\n      a = !0;\n    }\n    if (!o) {\n      yield o = makeResult(e, {}, n);\n    }\n  } catch (r) {\n    if (!a) {\n      throw r;\n    }\n    yield makeErrorResult(e, n && (n.status < 200 || n.status >= 300) && n.statusText ? new Error(n.statusText) : r, n);\n  }\n}\n\nfunction makeFetchSource(e, r, t) {\n  var a;\n  if (\"undefined\" != typeof AbortController) {\n    t.signal = (a = new AbortController).signal;\n  }\n  return o((() => {\n    if (a) {\n      a.abort();\n    }\n  }))(n((e => !!e))(s(fetchOperation(e, r, t))));\n}\n\nexport { CombinedError as C, makeFetchBody as a, makeErrorResult as b, mergeResultPatch as c, makeFetchURL as d, makeFetchOptions as e, makeFetchSource as f, getOperationType as g, createRequest as h, stringifyVariables as i, keyDocument as k, makeResult as m, stringifyDocument as s };\n//# sourceMappingURL=urql-core-chunk.mjs.map\n", "import type { IntrospectionQuery } from 'graphql';\nimport { GraphQLID, GraphQLObjectType, GraphQLSchema, executeSync } from 'graphql';\nimport { Kind, OperationTypeNode } from '@0no-co/graphql.web';\n\nimport type {\n  SelectionSetNode,\n  FragmentDefinitionNode,\n  OperationDefinitionNode,\n  FieldNode,\n  DocumentNode,\n} from '@0no-co/graphql.web';\n\n/** Support matrix to be used in the {@link makeIntrospectionQuery} builder */\nexport interface SupportedFeatures {\n  directiveIsRepeatable: boolean;\n  specifiedByURL: boolean;\n  inputValueDeprecation: boolean;\n  directiveArgumentsIsDeprecated: boolean;\n  fieldArgumentsIsDeprecated: boolean;\n  inputOneOf: boolean;\n}\n\n/** Data from a {@link makeIntrospectSupportQuery} result */\nexport interface IntrospectSupportQueryData {\n  directive: { fields: { name: string; args: { name: string }[] | null }[] | null } | null;\n  type: { fields: { name: string }[] | null } | null;\n  field: { fields: { name: string; args: { name: string }[] | null }[] | null } | null;\n  inputValue: { fields: { name: string }[] | null } | null;\n}\n\nconst _hasField = (\n  data: IntrospectSupportQueryData[keyof IntrospectSupportQueryData],\n  fieldName: string\n): boolean => !!data && !!data.fields && data.fields.some((field) => field.name === fieldName);\n\nconst _supportsDeprecatedArgumentsArg = (\n  data: IntrospectSupportQueryData['field' | 'directive']\n): boolean => {\n  const argsField = data && data.fields && data.fields.find((field) => field.name === 'args');\n  return !!(\n    argsField &&\n    argsField.args &&\n    argsField.args.find((arg) => arg.name === 'includeDeprecated')\n  );\n};\n\nexport const ALL_SUPPORTED_FEATURES: SupportedFeatures = {\n  directiveIsRepeatable: true,\n  specifiedByURL: true,\n  inputValueDeprecation: true,\n  directiveArgumentsIsDeprecated: true,\n  fieldArgumentsIsDeprecated: true,\n  inputOneOf: true,\n};\n\nexport const NO_SUPPORTED_FEATURES: SupportedFeatures = {\n  directiveIsRepeatable: false,\n  specifiedByURL: false,\n  inputValueDeprecation: false,\n  directiveArgumentsIsDeprecated: false,\n  fieldArgumentsIsDeprecated: false,\n  inputOneOf: false,\n};\n\n/** Evaluates data from a {@link makeIntrospectSupportQuery} result to {@link SupportedFeatures} */\nexport const toSupportedFeatures = (data: IntrospectSupportQueryData): SupportedFeatures => ({\n  directiveIsRepeatable: _hasField(data.directive, 'isRepeatable'),\n  specifiedByURL: _hasField(data.type, 'specifiedByURL'),\n  inputOneOf: _hasField(data.type, 'isOneOf'),\n  inputValueDeprecation: _hasField(data.inputValue, 'isDeprecated'),\n  directiveArgumentsIsDeprecated: _supportsDeprecatedArgumentsArg(data.directive),\n  fieldArgumentsIsDeprecated: _supportsDeprecatedArgumentsArg(data.field),\n});\n\nexport const introspectionToSupportedFeatures = (data: IntrospectionQuery): SupportedFeatures => {\n  const directive = data.__schema.types.find((type) => type.name === '__Directive') as any;\n  const type = data.__schema.types.find((type) => type.name === '__Type') as any;\n  const inputValue = data.__schema.types.find((type) => type.name === '__InputValue') as any;\n  const field = data.__schema.types.find((type) => type.name === '__Field') as any;\n  if (directive && type && inputValue && field) {\n    return {\n      directiveIsRepeatable: _hasField(directive, 'isRepeatable'),\n      specifiedByURL: _hasField(type, 'specifiedByURL'),\n      inputOneOf: _hasField(type, 'isOneOf'),\n      inputValueDeprecation: _hasField(inputValue, 'isDeprecated'),\n      directiveArgumentsIsDeprecated: _supportsDeprecatedArgumentsArg(directive),\n      fieldArgumentsIsDeprecated: _supportsDeprecatedArgumentsArg(field),\n    };\n  } else {\n    return NO_SUPPORTED_FEATURES;\n  }\n};\n\nlet _localSupport: SupportedFeatures | undefined;\n\n/** Evaluates supported features for local graphql peer-dependency */\nexport const getPeerSupportedFeatures = () => {\n  if (!_localSupport) {\n    const schema = new GraphQLSchema({\n      query: new GraphQLObjectType({\n        name: 'Query',\n        fields: { _noop: { type: GraphQLID } },\n      }),\n    });\n    const result = executeSync({ schema, document: makeIntrospectSupportQuery() });\n    return (_localSupport = result.data\n      ? toSupportedFeatures(result.data as any)\n      : NO_SUPPORTED_FEATURES);\n  }\n  return _localSupport;\n};\n\nlet _introspectionQuery: DocumentNode | undefined;\nlet _previousSupport: SupportedFeatures | undefined;\n/** Builds an introspection query as AST */\nexport const makeIntrospectionQuery = (support: SupportedFeatures): DocumentNode => {\n  if (_introspectionQuery && _previousSupport === support) {\n    return _introspectionQuery;\n  } else {\n    return (_introspectionQuery = _makeIntrospectionQuery((_previousSupport = support)));\n  }\n};\n\nconst _makeIntrospectionQuery = (support: SupportedFeatures): DocumentNode => ({\n  kind: Kind.DOCUMENT,\n  definitions: [\n    {\n      kind: Kind.OPERATION_DEFINITION,\n      name: { kind: Kind.NAME, value: 'IntrospectionQuery' },\n      operation: OperationTypeNode.QUERY,\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: '__schema' },\n            selectionSet: _makeSchemaSelection(support),\n          },\n        ],\n      },\n    } satisfies OperationDefinitionNode,\n\n    _makeSchemaFullTypeFragment(support),\n    _makeSchemaInputValueFragment(support),\n    _makeTypeRefFragment(),\n  ],\n});\n\n/** Builds a support matrix query resulting in {@link IntrospectSupportQueryData} results */\nexport const makeIntrospectSupportQuery = (): DocumentNode => ({\n  kind: Kind.DOCUMENT,\n  definitions: [\n    {\n      kind: Kind.OPERATION_DEFINITION,\n      name: { kind: Kind.NAME, value: 'IntrospectSupportQuery' },\n      operation: OperationTypeNode.QUERY,\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FIELD,\n            alias: { kind: Kind.NAME, value: 'directive' },\n            name: { kind: Kind.NAME, value: '__type' },\n            arguments: [\n              {\n                kind: Kind.ARGUMENT,\n                name: { kind: Kind.NAME, value: 'name' },\n                value: { kind: Kind.STRING, value: '__Directive' },\n              },\n            ],\n            selectionSet: _makeFieldNamesSelection({ includeArgs: true }),\n          },\n          {\n            kind: Kind.FIELD,\n            alias: { kind: Kind.NAME, value: 'field' },\n            name: { kind: Kind.NAME, value: '__type' },\n            arguments: [\n              {\n                kind: Kind.ARGUMENT,\n                name: { kind: Kind.NAME, value: 'name' },\n                value: { kind: Kind.STRING, value: '__Field' },\n              },\n            ],\n            selectionSet: _makeFieldNamesSelection({ includeArgs: true }),\n          },\n          {\n            kind: Kind.FIELD,\n            alias: { kind: Kind.NAME, value: 'type' },\n            name: { kind: Kind.NAME, value: '__type' },\n            arguments: [\n              {\n                kind: Kind.ARGUMENT,\n                name: { kind: Kind.NAME, value: 'name' },\n                value: { kind: Kind.STRING, value: '__Type' },\n              },\n            ],\n            selectionSet: _makeFieldNamesSelection({ includeArgs: false }),\n          },\n          {\n            kind: Kind.FIELD,\n            alias: { kind: Kind.NAME, value: 'inputValue' },\n            name: { kind: Kind.NAME, value: '__type' },\n            arguments: [\n              {\n                kind: Kind.ARGUMENT,\n                name: { kind: Kind.NAME, value: 'name' },\n                value: { kind: Kind.STRING, value: '__InputValue' },\n              },\n            ],\n            selectionSet: _makeFieldNamesSelection({ includeArgs: false }),\n          },\n        ],\n      },\n    } satisfies OperationDefinitionNode,\n  ],\n});\n\nconst _makeFieldNamesSelection = (options: { includeArgs: boolean }): SelectionSetNode => ({\n  kind: Kind.SELECTION_SET,\n  selections: [\n    {\n      kind: Kind.FIELD,\n      name: { kind: Kind.NAME, value: 'fields' },\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'name' },\n          },\n          ...(options.includeArgs\n            ? ([\n                {\n                  kind: Kind.FIELD,\n                  name: { kind: Kind.NAME, value: 'args' },\n                  selectionSet: {\n                    kind: Kind.SELECTION_SET,\n                    selections: [\n                      {\n                        kind: Kind.FIELD,\n                        name: { kind: Kind.NAME, value: 'name' },\n                      },\n                    ],\n                  },\n                },\n              ] as const)\n            : []),\n        ],\n      },\n    },\n  ],\n});\n\nconst _makeSchemaSelection = (support: SupportedFeatures): SelectionSetNode => ({\n  kind: Kind.SELECTION_SET,\n  selections: [\n    // queryType { name }\n    {\n      kind: Kind.FIELD,\n      name: { kind: Kind.NAME, value: 'queryType' },\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'name' },\n          },\n        ],\n      },\n    },\n    // mutationType { name }\n    {\n      kind: Kind.FIELD,\n      name: { kind: Kind.NAME, value: 'mutationType' },\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'name' },\n          },\n        ],\n      },\n    },\n    // subscriptionType { name }\n    {\n      kind: Kind.FIELD,\n      name: { kind: Kind.NAME, value: 'subscriptionType' },\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'name' },\n          },\n        ],\n      },\n    },\n    // types { ...FullType }\n    {\n      kind: Kind.FIELD,\n      name: { kind: Kind.NAME, value: 'types' },\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FRAGMENT_SPREAD,\n            name: { kind: Kind.NAME, value: 'FullType' },\n          },\n        ],\n      },\n    },\n    // directives { name description locations args }\n    {\n      kind: Kind.FIELD,\n      name: { kind: Kind.NAME, value: 'directives' },\n      selectionSet: {\n        kind: Kind.SELECTION_SET,\n        selections: [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'name' },\n          },\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'description' },\n          },\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'locations' },\n          },\n          _makeSchemaArgsField(support.directiveArgumentsIsDeprecated),\n          ...(support.directiveIsRepeatable\n            ? ([\n                {\n                  kind: Kind.FIELD,\n                  name: { kind: Kind.NAME, value: 'isRepeatable' },\n                },\n              ] as const)\n            : []),\n        ],\n      },\n    },\n  ],\n});\n\nconst _makeSchemaFullTypeFragment = (support: SupportedFeatures): FragmentDefinitionNode => ({\n  kind: Kind.FRAGMENT_DEFINITION,\n  name: { kind: Kind.NAME, value: 'FullType' },\n  typeCondition: { kind: Kind.NAMED_TYPE, name: { kind: Kind.NAME, value: '__Type' } },\n  selectionSet: {\n    kind: Kind.SELECTION_SET,\n    selections: [\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'kind' },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'name' },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'description' },\n      },\n      ...(support.inputOneOf\n        ? ([{ kind: Kind.FIELD, name: { kind: Kind.NAME, value: 'isOneOf' } }] as const)\n        : []),\n      ...(support.specifiedByURL\n        ? ([\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'specifiedByURL' },\n            },\n          ] as const)\n        : []),\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'fields' },\n        arguments: [\n          {\n            kind: Kind.ARGUMENT,\n            name: { kind: Kind.NAME, value: 'includeDeprecated' },\n            value: { kind: Kind.BOOLEAN, value: true },\n          },\n        ],\n        selectionSet: {\n          kind: Kind.SELECTION_SET,\n          selections: [\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'name' },\n            },\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'description' },\n            },\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'isDeprecated' },\n            },\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'deprecationReason' },\n            },\n            _makeSchemaArgsField(support.fieldArgumentsIsDeprecated),\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'type' },\n              selectionSet: {\n                kind: Kind.SELECTION_SET,\n                selections: [\n                  {\n                    kind: Kind.FRAGMENT_SPREAD,\n                    name: { kind: Kind.NAME, value: 'TypeRef' },\n                  },\n                ],\n              },\n            },\n          ],\n        },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'interfaces' },\n        selectionSet: {\n          kind: Kind.SELECTION_SET,\n          selections: [\n            {\n              kind: Kind.FRAGMENT_SPREAD,\n              name: { kind: Kind.NAME, value: 'TypeRef' },\n            },\n          ],\n        },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'possibleTypes' },\n        selectionSet: {\n          kind: Kind.SELECTION_SET,\n          selections: [\n            {\n              kind: Kind.FRAGMENT_SPREAD,\n              name: { kind: Kind.NAME, value: 'TypeRef' },\n            },\n          ],\n        },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'inputFields' },\n        arguments: support.inputValueDeprecation\n          ? [\n              {\n                kind: Kind.ARGUMENT,\n                name: { kind: Kind.NAME, value: 'includeDeprecated' },\n                value: { kind: Kind.BOOLEAN, value: true },\n              },\n            ]\n          : [],\n        selectionSet: {\n          kind: Kind.SELECTION_SET,\n          selections: [\n            {\n              kind: Kind.FRAGMENT_SPREAD,\n              name: { kind: Kind.NAME, value: 'InputValue' },\n            },\n          ],\n        },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'enumValues' },\n        arguments: [\n          {\n            kind: Kind.ARGUMENT,\n            name: { kind: Kind.NAME, value: 'includeDeprecated' },\n            value: { kind: Kind.BOOLEAN, value: true },\n          },\n        ],\n        selectionSet: {\n          kind: Kind.SELECTION_SET,\n\n          selections: [\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'name' },\n            },\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'description' },\n            },\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'isDeprecated' },\n            },\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'deprecationReason' },\n            },\n          ],\n        },\n      },\n    ],\n  },\n});\n\nconst _makeSchemaArgsField = (supportsValueDeprecation: boolean): FieldNode => ({\n  kind: Kind.FIELD,\n  name: { kind: Kind.NAME, value: 'args' },\n  arguments: supportsValueDeprecation\n    ? [\n        {\n          kind: Kind.ARGUMENT,\n          name: { kind: Kind.NAME, value: 'includeDeprecated' },\n          value: { kind: Kind.BOOLEAN, value: true },\n        },\n      ]\n    : [],\n  selectionSet: {\n    kind: Kind.SELECTION_SET,\n    selections: [\n      {\n        kind: Kind.FRAGMENT_SPREAD,\n        name: { kind: Kind.NAME, value: 'InputValue' },\n      },\n    ],\n  },\n});\n\nconst _makeSchemaInputValueFragment = (support: SupportedFeatures): FragmentDefinitionNode => ({\n  kind: Kind.FRAGMENT_DEFINITION,\n  name: { kind: Kind.NAME, value: 'InputValue' },\n  typeCondition: { kind: Kind.NAMED_TYPE, name: { kind: Kind.NAME, value: '__InputValue' } },\n  selectionSet: {\n    kind: Kind.SELECTION_SET,\n    selections: [\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'name' },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'description' },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'defaultValue' },\n      },\n      {\n        kind: Kind.FIELD,\n        name: { kind: Kind.NAME, value: 'type' },\n        selectionSet: {\n          kind: Kind.SELECTION_SET,\n          selections: [\n            {\n              kind: Kind.FRAGMENT_SPREAD,\n              name: { kind: Kind.NAME, value: 'TypeRef' },\n            },\n          ],\n        },\n      },\n      ...(support.inputValueDeprecation\n        ? ([\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'isDeprecated' },\n            },\n            {\n              kind: Kind.FIELD,\n              name: { kind: Kind.NAME, value: 'deprecationReason' },\n            },\n          ] as const)\n        : []),\n    ],\n  },\n});\n\nconst _makeTypeRefFragment = (): FragmentDefinitionNode => ({\n  kind: Kind.FRAGMENT_DEFINITION,\n  name: { kind: Kind.NAME, value: 'TypeRef' },\n  typeCondition: { kind: Kind.NAMED_TYPE, name: { kind: Kind.NAME, value: '__Type' } },\n  selectionSet: _makeTypeRefSelection(0),\n});\n\nconst _makeTypeRefSelection = (depth: number): SelectionSetNode => ({\n  kind: Kind.SELECTION_SET,\n  selections:\n    depth < 9\n      ? [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'kind' },\n          },\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'name' },\n          },\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'ofType' },\n            selectionSet: _makeTypeRefSelection(depth + 1),\n          },\n        ]\n      : [\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'kind' },\n          },\n          {\n            kind: Kind.FIELD,\n            name: { kind: Kind.NAME, value: 'name' },\n          },\n        ],\n});\n", "import ts from 'typescript';\nimport type { IntrospectionQuery } from 'graphql';\nimport { buildSchema, buildClientSchema, executeSync } from 'graphql';\nimport { CombinedError } from '@urql/core';\nimport fs from 'node:fs/promises';\nimport path from 'node:path';\n\nimport { makeIntrospectionQuery, getPeerSupportedFeatures } from './introspection';\n\nimport type { SchemaLoader, SchemaLoaderResult, OnSchemaUpdate } from './types';\n\ninterface LoadFromSDLConfig {\n  name?: string;\n  assumeValid?: boolean;\n  file: string;\n}\n\nexport function loadFromSDL(config: LoadFromSDLConfig): SchemaLoader {\n  const subscriptions = new Set<OnSchemaUpdate>();\n\n  let abort: (() => void) | null = null;\n  let result: SchemaLoaderResult | null = null;\n\n  const load = async (): Promise<SchemaLoaderResult> => {\n    const ext = path.extname(config.file);\n    const data = await fs.readFile(config.file, { encoding: 'utf8' });\n    if (ext === '.json') {\n      const introspection = JSON.parse(data) as IntrospectionQuery | null;\n      if (!introspection || !introspection.__schema) {\n        throw new Error(\n          'Parsing JSON introspection data failed.\\n' +\n            'The JSON payload did not evaluate to an introspection schema.'\n        );\n      }\n      return {\n        introspection: {\n          ...introspection,\n          name: config.name,\n        },\n        schema: buildClientSchema(introspection, { assumeValid: !!config.assumeValid }),\n      };\n    } else {\n      const schema = buildSchema(data, { assumeValidSDL: !!config.assumeValid });\n      const query = makeIntrospectionQuery(getPeerSupportedFeatures());\n      const queryResult = executeSync({ schema, document: query });\n      if (queryResult.errors) {\n        throw new CombinedError({ graphQLErrors: queryResult.errors as any[] });\n      } else if (queryResult.data) {\n        const introspection = {\n          ...(queryResult.data as unknown as IntrospectionQuery),\n          name: config.name,\n        };\n        return { introspection, schema };\n      } else {\n        throw new Error(\n          'Executing introspection against SDL schema failed.\\n' +\n            '`graphql` failed to return any schema data or error.'\n        );\n      }\n    }\n  };\n\n  const watch = async () => {\n    if (ts.sys.watchFile) {\n      const watcher = ts.sys.watchFile(\n        config.file,\n        async () => {\n          try {\n            if ((result = await load())) {\n              for (const subscriber of subscriptions) subscriber(result);\n            }\n          } catch (_error) {}\n        },\n        250,\n        {\n          // NOTE: Using `ts.WatchFileKind.UseFsEvents` causes missed events just like fs.watch\n          // as below on macOS, as of TypeScript 5.5 and is hence avoided here\n          watchFile: ts.WatchFileKind.UseFsEventsOnParentDirectory,\n          fallbackPolling: ts.PollingWatchKind.PriorityInterval,\n        }\n      );\n      abort = () => watcher.close();\n    } else {\n      const controller = new AbortController();\n      abort = () => controller.abort();\n      const watcher = fs.watch(config.file, {\n        signal: controller.signal,\n        persistent: false,\n      });\n      try {\n        for await (const _event of watcher) {\n          if ((result = await load())) {\n            for (const subscriber of subscriptions) subscriber(result);\n          }\n        }\n      } catch (error: any) {\n        if (error.name !== 'AbortError') throw error;\n      } finally {\n        abort = null;\n      }\n    }\n  };\n\n  return {\n    get name() {\n      return config.name;\n    },\n    async load(reload?: boolean) {\n      return reload || !result ? (result = await load()) : result;\n    },\n    notifyOnUpdate(onUpdate) {\n      if (!subscriptions.size) watch();\n      subscriptions.add(onUpdate);\n      return () => {\n        subscriptions.delete(onUpdate);\n        if (!subscriptions.size && abort) abort();\n      };\n    },\n    async loadIntrospection() {\n      const result = await this.load();\n      return result && result.introspection;\n    },\n    async loadSchema() {\n      const result = await this.load();\n      return result && result.schema;\n    },\n  };\n}\n", "import { makeSubject as r, mergeMap as e, filter as t, takeUntil as a, debounce as o, fromValue as n, merge as i } from \"wonka\";\n\nimport { makeOperation as u } from \"@urql/core\";\n\nvar retryExchange = y => {\n  var {retryIf: d, retryWith: s} = y;\n  var l = y.initialDelayMs || 1e3;\n  var p = y.maxDelayMs || 15e3;\n  var m = y.maxNumberAttempts || 2;\n  var c = null != y.randomDelay ? !!y.randomDelay : !0;\n  return ({forward: y, dispatchDebug: v}) => f => {\n    var {source: h, next: x} = r();\n    var E = e((r => {\n      var e = r.context.retry || {\n        count: 0,\n        delay: null\n      };\n      var i = ++e.count;\n      var y = e.delay || l;\n      var d = Math.random() + 1.5;\n      if (c) {\n        if (y * d < p) {\n          y *= d;\n        } else {\n          y = p;\n        }\n      } else {\n        y = Math.min(i * l, p);\n      }\n      e.delay = y;\n      var s = t((e => (\"query\" === e.kind || \"teardown\" === e.kind) && e.key === r.key))(f);\n      \"production\" !== process.env.NODE_ENV && v({\n        type: \"retryAttempt\",\n        message: `The operation has failed and a retry has been triggered (${i} / ${m})`,\n        operation: r,\n        data: {\n          retryCount: i,\n          delayAmount: y\n        },\n        source: \"retryExchange\"\n      });\n      return a(s)(o((() => y))(n(u(r.kind, r, {\n        ...r.context,\n        retry: e\n      }))));\n    }))(h);\n    return t((r => {\n      var e = r.operation.context.retry;\n      if (!(r.error && (d ? d(r.error, r.operation) : s || r.error.networkError))) {\n        if (e) {\n          e.count = 0;\n          e.delay = null;\n        }\n        return !0;\n      }\n      if (!((e && e.count || 0) >= m - 1)) {\n        var t = s ? s(r.error, r.operation) : r.operation;\n        if (!t) {\n          return !0;\n        }\n        x(t);\n        return !1;\n      }\n      \"production\" !== process.env.NODE_ENV && v({\n        type: \"retryExhausted\",\n        message: \"Maximum number of retries has been reached. No further retries will be performed.\",\n        operation: r.operation,\n        source: \"retryExchange\"\n      });\n      return !0;\n    }))(y(i([ f, E ])));\n  };\n};\n\nexport { retryExchange };\n//# sourceMappingURL=urql-exchange-retry.mjs.map\n", "import type { IntrospectionQuery } from 'graphql';\nimport { buildClientSchema } from 'graphql';\nimport { Client, fetchExchange } from '@urql/core';\nimport { retryExchange } from '@urql/exchange-retry';\n\nimport {\n  makeIntrospectionQuery,\n  makeIntrospectSupportQuery,\n  toSupportedFeatures,\n  introspectionToSupportedFeatures,\n  NO_SUPPORTED_FEATURES,\n} from './introspection';\n\nimport type { SupportedFeatures, IntrospectSupportQueryData } from './introspection';\nimport type { SchemaLoader, SchemaLoaderResult, OnSchemaUpdate } from './types';\n\ninterface LoadFromURLConfig {\n  name?: string;\n  url: URL | string;\n  headers?: HeadersInit;\n  interval?: number;\n}\n\nexport function loadFromURL(config: LoadFromURLConfig): SchemaLoader {\n  const interval = config.interval || 60_000;\n  const subscriptions = new Set<OnSchemaUpdate>();\n\n  let timeoutID: NodeJS.Timeout | null = null;\n  let supportedFeatures: SupportedFeatures | null = null;\n  let result: SchemaLoaderResult | null = null;\n\n  const client = new Client({\n    url: `${config.url}`,\n    fetchOptions: { headers: config.headers },\n    exchanges: [\n      retryExchange({\n        initialDelayMs: 200,\n        maxDelayMs: 1_500,\n        maxNumberAttempts: 3,\n        retryWith(error, operation) {\n          if (error.networkError) process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';\n          return operation;\n        },\n      }),\n      fetchExchange,\n    ],\n  });\n\n  const scheduleUpdate = () => {\n    if (subscriptions.size && !timeoutID) {\n      timeoutID = setTimeout(async () => {\n        timeoutID = null;\n        try {\n          result = await load();\n        } catch (_error) {\n          result = null;\n        }\n        if (result) for (const subscriber of subscriptions) subscriber(result);\n      }, interval);\n    }\n  };\n\n  const introspect = async (support: SupportedFeatures): Promise<SchemaLoaderResult> => {\n    const query = makeIntrospectionQuery(support);\n    const introspectionResult = await client.query<IntrospectionQuery>(query, {});\n    try {\n      if (introspectionResult.error) {\n        throw introspectionResult.error;\n      } else if (introspectionResult.data) {\n        const introspection = introspectionResult.data;\n        return {\n          introspection: {\n            ...introspection,\n            name: config.name,\n          },\n          schema: buildClientSchema(introspection, { assumeValid: true }),\n        };\n      } else {\n        throw new Error(\n          'Executing introspection against API failed.\\n' +\n            'The API failed to return any schema data or error.'\n        );\n      }\n    } finally {\n      scheduleUpdate();\n    }\n  };\n\n  const load = async (): Promise<SchemaLoaderResult> => {\n    if (!supportedFeatures) {\n      const query = makeIntrospectSupportQuery();\n      const supportResult = await client.query<IntrospectSupportQueryData>(query, {});\n      if (supportResult.error && supportResult.error.graphQLErrors.length > 0) {\n        try {\n          // If we failed to determine support, we do a regular introspection\n          const { introspection } = await introspect(NO_SUPPORTED_FEATURES);\n          supportedFeatures = introspectionToSupportedFeatures(introspection);\n        } catch (_error) {\n          // If this also failed, we assume no supported features\n          supportedFeatures = NO_SUPPORTED_FEATURES;\n        }\n      } else if (supportResult.data && !supportResult.error) {\n        // Succeeding the support query, we get the supported features\n        supportedFeatures = toSupportedFeatures(supportResult.data);\n      } else if (supportResult.error) {\n        // On misc. error, we rethrow and reset supported features\n        supportedFeatures = null;\n        throw supportResult.error;\n      } else {\n        // Otherwise we assume no features are supported\n        supportedFeatures = NO_SUPPORTED_FEATURES;\n      }\n    }\n    return introspect(supportedFeatures);\n  };\n\n  return {\n    get name() {\n      return config.name;\n    },\n    async load(reload?: boolean) {\n      return reload || !result ? (result = await load()) : result;\n    },\n    notifyOnUpdate(onUpdate) {\n      subscriptions.add(onUpdate);\n      return () => {\n        subscriptions.delete(onUpdate);\n        if (!subscriptions.size && timeoutID) {\n          clearTimeout(timeoutID);\n          timeoutID = null;\n        }\n      };\n    },\n    async loadIntrospection() {\n      const result = await this.load();\n      return result && result.introspection;\n    },\n    async loadSchema() {\n      const result = await this.load();\n      return result && result.schema;\n    },\n  };\n}\n", "export type * from './types';\n\nimport path from 'node:path';\nimport { loadFromSDL } from './sdl';\nimport { loadFromURL } from './url';\n\nimport type {\n  SchemaLoaderResult,\n  SchemaLoader,\n  SchemaOrigin,\n  SchemaRef,\n  SingleSchemaInput,\n  MultiSchemaInput,\n  BaseLoadConfig,\n} from './types';\n\nexport { loadFromSDL, loadFromURL };\n\nexport const getURLConfig = (origin: SchemaOrigin | null) => {\n  try {\n    return origin\n      ? {\n          url: new URL(typeof origin === 'object' ? origin.url : origin),\n          headers: typeof origin === 'object' ? origin.headers : undefined,\n        }\n      : null;\n  } catch (_error) {\n    return null;\n  }\n};\n\nexport interface LoadConfig extends BaseLoadConfig {\n  name?: string;\n  origin: SchemaOrigin;\n}\n\nexport function load(config: LoadConfig): SchemaLoader {\n  const urlOrigin = getURLConfig(config.origin);\n  if (urlOrigin) {\n    return loadFromURL({\n      ...urlOrigin,\n      interval: config.fetchInterval,\n      name: config.name,\n    });\n  } else if (typeof config.origin === 'string') {\n    const file = config.rootPath ? path.resolve(config.rootPath, config.origin) : config.origin;\n    const assumeValid = config.assumeValid != null ? config.assumeValid : true;\n    return loadFromSDL({\n      file,\n      assumeValid,\n      name: config.name,\n    });\n  } else {\n    throw new Error(`Configuration contains an invalid \"schema\" option`);\n  }\n}\n\nexport function loadRef(\n  input: SingleSchemaInput | MultiSchemaInput | (SingleSchemaInput & MultiSchemaInput)\n): SchemaRef {\n  const teardowns: (() => void)[] = [];\n\n  let _loaders: { input: SingleSchemaInput; loader: SchemaLoader }[] | undefined;\n  const getLoaders = (config?: BaseLoadConfig) => {\n    if (!_loaders) {\n      _loaders = (('schemas' in input && input.schemas) || []).map((input) => ({\n        input,\n        loader: load({ ...config, origin: input.schema, name: input.name }),\n      }));\n      if ('schema' in input && input.schema) {\n        _loaders.push({\n          input: { ...input, name: undefined },\n          loader: load({ ...config, origin: input.schema }),\n        });\n      }\n    }\n    return _loaders;\n  };\n\n  const ref: SchemaRef = {\n    version: 0,\n    current: null,\n\n    multi: (('schemas' in input && input.schemas) || []).reduce((acc, { name }) => {\n      if (name) acc[name] = null;\n      return acc;\n    }, {}),\n\n    autoupdate(config: BaseLoadConfig, onUpdate) {\n      const loaders = getLoaders(config);\n      teardowns.push(\n        ...loaders.map(({ input, loader }) => {\n          loader\n            .load()\n            .then((result) => {\n              ref.version++;\n              if (input.name) {\n                ref.multi[input.name] = { ...input, ...result };\n              } else {\n                ref.current = { ...input, ...result };\n              }\n            })\n            .catch((_error) => {\n              /*noop*/\n            });\n          return loader.notifyOnUpdate((result) => {\n            ref.version++;\n            if (input.name) {\n              ref.multi[input.name] = { ...input, ...result };\n            } else {\n              ref.current = { ...input, ...result };\n            }\n            onUpdate(ref, input);\n          });\n        })\n      );\n      return () => {\n        let teardown: (() => void) | undefined;\n        while ((teardown = teardowns.pop()) != null) teardown();\n      };\n    },\n    async load(config: BaseLoadConfig) {\n      const loaders = getLoaders(config);\n      await Promise.all(\n        loaders.map(async ({ input, loader }) => {\n          const result = await loader.load();\n          ref.version++;\n          if (input.name) {\n            ref.multi[input.name] = { ...input, ...result };\n          } else {\n            ref.current = { ...input, ...result };\n          }\n        })\n      );\n      return ref as SchemaRef<SchemaLoaderResult>;\n    },\n  };\n\n  return ref;\n}\n", "import * as path from 'node:path';\n\nexport const cwd = process.cwd();\n\nexport const maybeRelative = (filePath: string): string => {\n  const relative = path.relative(cwd, filePath);\n  return !relative.startsWith('..') ? relative : filePath;\n};\n", "import type ts from 'typescript';\nimport { maybeRelative } from './helpers';\n\nexport class TSError extends Error {\n  readonly name: 'TSError';\n  readonly diagnostic: ts.Diagnostic;\n  constructor(diagnostic: ts.Diagnostic) {\n    let message =\n      typeof diagnostic.messageText !== 'string'\n        ? diagnostic.messageText.messageText\n        : diagnostic.messageText;\n    if (diagnostic.file) message += ` (${maybeRelative(diagnostic.file.fileName)})`;\n    super(message);\n    this.name = 'TSError';\n    this.diagnostic = diagnostic;\n  }\n}\n\nexport class TadaError extends Error {\n  readonly name: '<PERSON>aError';\n  constructor(message: string) {\n    super(message);\n    this.name = 'TadaError';\n  }\n}\n", "import * as path from 'node:path';\nimport { TadaError } from './errors';\nimport { getURLConfig } from './loaders';\nimport type { SchemaOrigin } from './loaders';\n\nexport interface BaseConfig {\n  template?: string;\n  trackFieldUsage?: boolean;\n  shouldCheckForColocatedFragments?: boolean;\n}\n\nexport interface SchemaConfig {\n  name?: string;\n  schema: SchemaOrigin;\n  tadaOutputLocation?: string;\n  tadaTurboLocation?: string;\n  tadaPersistedLocation?: string;\n}\n\nconst SCHEMA_PROPS = [\n  'name',\n  'tadaOutputLocation',\n  'tadaTurboLocation',\n  'tadaPersistedLocation',\n] as const;\n\ninterface MultiSchemaConfig extends SchemaConfig {\n  name: string;\n}\n\nexport type GraphQLSPConfig = BaseConfig & (SchemaConfig | { schemas: MultiSchemaConfig[] });\n\nconst parseSchemaConfig = (input: unknown, rootPath: string): SchemaConfig => {\n  const resolveConfigDir = (input: string | undefined) => {\n    if (!input) return input;\n    return path.normalize(\n      input.replace(/\\${([^}]+)}/, (_match, name) => {\n        if (name === 'configDir') {\n          return rootPath;\n        } else {\n          throw new TadaError(\n            `Substitution \"\\${${name}}\" is not recognized (did you mean 'configDir'?)`\n          );\n        }\n      })\n    );\n  };\n\n  if (input == null || typeof input !== 'object') {\n    throw new TadaError(`Schema is not configured properly (Received: ${input})`);\n  }\n\n  if ('schema' in input && input.schema && typeof input.schema === 'object') {\n    const { schema } = input;\n    if (!('url' in schema)) {\n      throw new TadaError('Configuration contains a `schema` object, but no `url` property');\n    }\n\n    if ('headers' in schema && schema.headers && typeof schema.headers === 'object') {\n      for (const key in schema.headers) {\n        if (schema.headers[key] && typeof schema.headers[key] !== 'string') {\n          throw new TadaError(\n            'Headers at `schema.headers` contain a non-string value at key: ' + key\n          );\n        }\n      }\n    } else if ('headers' in schema) {\n      throw new TadaError(\n        \"Configuration contains a `schema.headers` property, but it's not an object\"\n      );\n    }\n  } else if (!('schema' in input) || typeof input.schema !== 'string') {\n    throw new TadaError('Configuration is missing a `schema` property');\n  }\n\n  if (\n    'tadaOutputLocation' in input &&\n    input.tadaOutputLocation &&\n    typeof input.tadaOutputLocation !== 'string'\n  ) {\n    throw new TadaError(\n      \"Configuration contains a `tadaOutputLocation` property, but it's not a file path\"\n    );\n  }\n\n  if (\n    'tadaTurboLocation' in input &&\n    input.tadaTurboLocation &&\n    typeof input.tadaTurboLocation !== 'string'\n  ) {\n    throw new TadaError(\n      \"Configuration contains a `tadaTurboLocation` property, but it's not a file path\"\n    );\n  }\n\n  if (\n    'tadaPersistedLocation' in input &&\n    input.tadaPersistedLocation &&\n    typeof input.tadaPersistedLocation !== 'string'\n  ) {\n    throw new TadaError(\n      \"Configuration contains a `tadaPersistedLocation` property, but it's not a file path\"\n    );\n  }\n\n  const output = input as any as SchemaConfig;\n\n  let schema: SchemaOrigin = output.schema;\n  if (typeof schema === 'string') {\n    const url = getURLConfig(schema);\n    if (!url) schema = resolveConfigDir(schema) || schema;\n  }\n\n  return {\n    ...output,\n    schema,\n    tadaOutputLocation: resolveConfigDir(output.tadaOutputLocation),\n    tadaTurboLocation: resolveConfigDir(output.tadaTurboLocation),\n    tadaPersistedLocation: resolveConfigDir(output.tadaPersistedLocation),\n  };\n};\n\nexport const parseConfig = (\n  input: unknown,\n  /** Defines the path of the \"main\" `tsconfig.json` file.\n   * @remarks\n   * This should be the `rootPath` output from `loadConfig`,\n   * which is the path of the user's `tsconfig.json` before\n   * resolving `extends` options.\n   */\n  rootPath: string = process.cwd()\n): GraphQLSPConfig => {\n  if (input == null || typeof input !== 'object') {\n    throw new TadaError(`Configuration is of an invalid type (Received: ${input})`);\n  } else if ('template' in input && input.template && typeof input.template !== 'string') {\n    throw new TadaError(\"Configuration contains a `template` property, but it's not a string\");\n  } else if ('name' in input && input.name && typeof input.name !== 'string') {\n    throw new TadaError(\"Configuration contains a `name` property, but it's not a string\");\n  }\n\n  if ('schemas' in input) {\n    if (!Array.isArray(input.schemas)) {\n      throw new TadaError(\"Configuration contains a `schema` property, but it's not an array\");\n    }\n\n    if ('schema' in input) {\n      throw new TadaError(\n        'If configuration contains a `schemas` property, it cannot contain a `schema` configuration.'\n      );\n    } else if ('tadaOutputLocation' in input) {\n      throw new TadaError(\n        \"If configuration contains a `schemas` property, it cannot contain a 'tadaOutputLocation` configuration.\"\n      );\n    } else if ('tadaTurboLocation' in input) {\n      throw new TadaError(\n        \"If configuration contains a `schemas` property, it cannot contain a 'tadaTurboLocation` configuration.\"\n      );\n    } else if ('tadaPersistedLocation' in input) {\n      throw new TadaError(\n        \"If configuration contains a `schemas` property, it cannot contain a 'tadaPersistedLocation` configuration.\"\n      );\n    }\n\n    const schemas = input.schemas.map((schema): MultiSchemaConfig => {\n      if (!('name' in schema) || !schema.name || typeof schema.name !== 'string')\n        throw new TadaError('All `schemas` configurations must contain a `name` label.');\n      if (\n        !('tadaOutputLocation' in schema) ||\n        !schema.tadaOutputLocation ||\n        typeof schema.tadaOutputLocation !== 'string'\n      )\n        throw new TadaError(\n          'All `schemas` configurations must contain a `tadaOutputLocation` path.'\n        );\n      return {\n        ...parseSchemaConfig(schema, rootPath),\n        name: schema.name,\n      };\n    });\n\n    for (const prop of SCHEMA_PROPS) {\n      const values = schemas.map((schema) => schema[prop]).filter(Boolean);\n      const uniqueValues = new Set(values);\n      if (values.length !== uniqueValues.size)\n        throw new TadaError(`All '${prop}' values in \\`schemas[]\\` must be unique.`);\n    }\n\n    return { ...input, schemas };\n  } else {\n    return { ...input, ...parseSchemaConfig(input, rootPath) };\n  }\n};\n\nexport const getSchemaNamesFromConfig = (config: GraphQLSPConfig): Set<null | string> => {\n  return new Set<null | string>([\n    ...('schema' in config ? [null] : []),\n    ...('schemas' in config ? config.schemas.map((input) => input.name) : []),\n  ]);\n};\n\nexport const getSchemaConfigForName = (\n  config: GraphQLSPConfig,\n  name: string | undefined\n): SchemaConfig | null => {\n  if (name && 'name' in config && config.name === name) {\n    return config;\n  } else if (!name && !('schemas' in config)) {\n    return config;\n  } else if (name && 'schemas' in config) {\n    for (let index = 0; index < config.schemas.length; index++)\n      if (config.schemas[index].name === name) return config.schemas[index];\n    return null;\n  } else {\n    return null;\n  }\n};\n", "import ts from 'typescript';\nimport * as path from 'node:path';\nimport * as fs from 'node:fs/promises';\nimport { createRequire } from 'node:module';\nimport type { Stats } from 'node:fs';\nimport type { TsConfigJson } from 'type-fest';\n\nimport { cwd, maybeRelative } from './helpers';\nimport { TSError, TadaError } from './errors';\n\nconst TSCONFIG = 'tsconfig.json';\n\nconst isFile = (stat: Stats): boolean => stat.isFile();\nconst isDir = (stat: Stats): boolean => stat.isDirectory();\nconst stat = (file: string, predicate = isFile): Promise<boolean> =>\n  fs\n    .stat(file)\n    .then(predicate)\n    .catch(() => false);\n\nconst _resolve =\n  typeof require !== 'undefined'\n    ? require.resolve.bind(require)\n    : createRequire(import.meta.url).resolve;\nconst resolveExtend = async (extend: string, from: string) => {\n  try {\n    return toTSConfigPath(_resolve(extend, { paths: [from] }));\n  } catch (_error) {\n    return null;\n  }\n};\n\nconst toTSConfigPath = (tsconfigPath: string): string =>\n  path.extname(tsconfigPath) !== '.json'\n    ? path.resolve(cwd, tsconfigPath, TSCONFIG)\n    : path.resolve(cwd, tsconfigPath);\n\nexport const readTSConfigFile = async (filePath: string): Promise<TsConfigJson> => {\n  const tsconfigPath = toTSConfigPath(filePath);\n  const contents = await fs.readFile(tsconfigPath, 'utf8');\n  const result = ts.parseConfigFileTextToJson(tsconfigPath, contents);\n  if (result.error) throw new TSError(result.error);\n  return result.config || {};\n};\n\nexport const findTSConfigFile = async (targetPath?: string): Promise<string | null> => {\n  let tsconfigPath = toTSConfigPath(targetPath || cwd);\n  const rootPath = toTSConfigPath(path.resolve(tsconfigPath, '/'));\n  while (tsconfigPath !== rootPath) {\n    if (await stat(tsconfigPath)) return tsconfigPath;\n    const gitPath = path.resolve(tsconfigPath, '..', '.git');\n    if (await stat(gitPath, isDir)) return null;\n    const parentPath = toTSConfigPath(path.resolve(tsconfigPath, '..', '..'));\n    if (parentPath === tsconfigPath) break;\n    tsconfigPath = parentPath;\n  }\n  return null;\n};\n\nconst getPluginConfig = (tsconfig: TsConfigJson | null): Record<string, unknown> | null =>\n  (tsconfig &&\n    tsconfig.compilerOptions &&\n    tsconfig.compilerOptions.plugins &&\n    tsconfig.compilerOptions.plugins.find(\n      (x) =>\n        x.name === '@0no-co/graphqlsp' ||\n        x.name === 'gql.tada/lsp' ||\n        x.name === 'gql.tada/ts-plugin'\n    )) ||\n  null;\n\nexport interface LoadConfigResult {\n  pluginConfig: Record<string, unknown>;\n  configPath: string;\n  rootPath: string;\n}\n\nexport const loadConfig = async (targetPath?: string): Promise<LoadConfigResult> => {\n  const rootTsconfigPath = await findTSConfigFile(targetPath);\n  if (!rootTsconfigPath) {\n    throw new TadaError(\n      targetPath\n        ? `No tsconfig.json found at or above: ${maybeRelative(targetPath)}`\n        : 'No tsconfig.json found at or above current working directory'\n    );\n  }\n\n  const load = async (targetPath: string): Promise<LoadConfigResult> => {\n    const tsconfig = await readTSConfigFile(targetPath);\n    const pluginConfig = getPluginConfig(tsconfig);\n\n    if (pluginConfig) {\n      return {\n        pluginConfig,\n        configPath: targetPath,\n        rootPath: path.dirname(rootTsconfigPath),\n      };\n    }\n\n    if (Array.isArray(tsconfig.extends)) {\n      for (let extend of tsconfig.extends) {\n        if (path.extname(extend) !== '.json') extend += '.json';\n        try {\n          const tsconfigPath = await resolveExtend(extend, path.dirname(rootTsconfigPath));\n          if (tsconfigPath) return await load(tsconfigPath);\n        } catch (_error) {}\n      }\n    } else if (tsconfig.extends) {\n      try {\n        const tsconfigPath = await resolveExtend(tsconfig.extends, path.dirname(rootTsconfigPath));\n        if (tsconfigPath) return await load(tsconfigPath);\n      } catch (_error) {}\n    }\n\n    throw new TadaError(\n      `Could not find a valid GraphQLSP plugin entry in: ${maybeRelative(rootTsconfigPath)}`\n    );\n  };\n\n  return await load(rootTsconfigPath);\n};\n\n/** @deprecated Use {@link loadConfig} instead */\nexport const resolveTypeScriptRootDir = async (\n  tsconfigPath: string\n): Promise<string | undefined> => {\n  try {\n    const result = await loadConfig(tsconfigPath);\n    return path.dirname(result.configPath);\n  } catch (_error) {\n    return undefined;\n  }\n};\n", "import type {\n  IntrospectionQuery,\n  IntrospectionType,\n  IntrospectionTypeRef,\n  IntrospectionNamedTypeRef,\n  IntrospectionOutputTypeRef,\n  IntrospectionInputTypeRef,\n  IntrospectionInputValue,\n  IntrospectionEnumValue,\n  IntrospectionField,\n} from 'graphql';\n\nimport type { IntrospectionResult } from '../loaders';\n\nfunction nameCompare(objA: { name: string }, objB: { name: string }) {\n  return objA.name < objB.name ? -1 : objA.name > objB.name ? 1 : 0;\n}\n\nfunction mapTypeRef<const T extends IntrospectionTypeRef>(fromType: T): T;\nfunction mapTypeRef(fromType: IntrospectionTypeRef): IntrospectionTypeRef;\nfunction mapTypeRef(fromType: IntrospectionOutputTypeRef): IntrospectionOutputTypeRef;\nfunction mapTypeRef(fromType: IntrospectionInputTypeRef): IntrospectionInputTypeRef;\n\nfunction mapTypeRef(fromType: IntrospectionTypeRef): IntrospectionTypeRef & { isOneOf?: boolean } {\n  switch (fromType.kind) {\n    case 'NON_NULL':\n      return {\n        kind: fromType.kind,\n        ofType: mapTypeRef(fromType.ofType),\n      };\n    case 'LIST':\n      return {\n        kind: fromType.kind,\n        ofType: mapTypeRef(fromType.ofType),\n      };\n    case 'INPUT_OBJECT':\n    case 'ENUM':\n    case 'SCALAR':\n    case 'OBJECT':\n    case 'INTERFACE':\n    case 'UNION':\n      return {\n        kind: fromType.kind,\n        name: fromType.name,\n      };\n  }\n}\n\nfunction mapEnumValue(value: IntrospectionEnumValue): IntrospectionEnumValue {\n  return {\n    name: value.name,\n    isDeprecated: !!value.isDeprecated,\n    deprecationReason: undefined,\n  };\n}\n\nfunction mapInputField(value: IntrospectionInputValue): IntrospectionInputValue {\n  return {\n    name: value.name,\n    type: mapTypeRef(value.type),\n    defaultValue: value.defaultValue || undefined,\n  };\n}\n\nfunction mapField(field: IntrospectionField): IntrospectionField {\n  return {\n    name: field.name,\n    type: mapTypeRef(field.type),\n    args: field.args ? field.args.map(mapInputField).sort(nameCompare) : [],\n    isDeprecated: !!field.isDeprecated,\n    deprecationReason: undefined,\n  };\n}\n\nfunction mapPossibleType<T extends IntrospectionNamedTypeRef>(ref: T): T {\n  return {\n    kind: ref.kind,\n    name: ref.name,\n  } as T;\n}\n\nfunction minifyIntrospectionType(type: IntrospectionType): IntrospectionType {\n  switch (type.kind) {\n    case 'SCALAR':\n      return {\n        kind: 'SCALAR',\n        name: type.name,\n      };\n\n    case 'ENUM':\n      return {\n        kind: 'ENUM',\n        name: type.name,\n        enumValues: type.enumValues.map(mapEnumValue),\n      };\n\n    case 'INPUT_OBJECT': {\n      return {\n        kind: 'INPUT_OBJECT',\n        name: type.name,\n        inputFields: type.inputFields.map(mapInputField),\n        isOneOf: (type as any).isOneOf || false,\n      } as IntrospectionType;\n    }\n\n    case 'OBJECT':\n      return {\n        kind: 'OBJECT',\n        name: type.name,\n        fields: type.fields ? type.fields.map(mapField).sort(nameCompare) : [],\n        interfaces: type.interfaces ? type.interfaces.map(mapPossibleType).sort(nameCompare) : [],\n      };\n\n    case 'INTERFACE':\n      return {\n        kind: 'INTERFACE',\n        name: type.name,\n        fields: type.fields ? type.fields.map(mapField).sort(nameCompare) : [],\n        interfaces: type.interfaces ? type.interfaces.map(mapPossibleType).sort(nameCompare) : [],\n        possibleTypes: type.possibleTypes\n          ? type.possibleTypes.map(mapPossibleType).sort(nameCompare)\n          : [],\n      };\n\n    case 'UNION':\n      return {\n        kind: 'UNION',\n        name: type.name,\n        possibleTypes: type.possibleTypes\n          ? type.possibleTypes.map(mapPossibleType).sort(nameCompare)\n          : [],\n      };\n  }\n}\n\n/** Minifies an {@link IntrospectionQuery} for use with Graphcache or the `populateExchange`.\n *\n * @param schema - An {@link IntrospectionQuery} object to be minified.\n * @param opts - An optional {@link MinifySchemaOptions} configuration object.\n * @returns the minified {@link IntrospectionQuery} object.\n *\n * @remarks\n * `minifyIntrospectionQuery` reduces the size of an {@link IntrospectionQuery} by\n * removing data and information that a client-side consumer, like Graphcache or the\n * `populateExchange`, may not require.\n *\n * At the very least, it will remove system types, descriptions, depreactions,\n * and source locations. Unless disabled via the options passed, it will also\n * by default remove all scalars, enums, inputs, and directives.\n *\n * @throws\n * If `schema` receives an object that isn’t an {@link IntrospectionQuery}, a\n * {@link TypeError} will be thrown.\n */\nexport const minifyIntrospectionQuery = (\n  schema: IntrospectionQuery | IntrospectionResult\n): IntrospectionResult => {\n  if (!schema || !('__schema' in schema)) {\n    throw new TypeError('Expected to receive an IntrospectionQuery.');\n  }\n\n  const {\n    __schema: { queryType, mutationType, subscriptionType, types },\n  } = schema;\n\n  const minifiedTypes = types\n    .filter((type) => {\n      switch (type.name) {\n        case '__Directive':\n        case '__DirectiveLocation':\n        case '__EnumValue':\n        case '__InputValue':\n        case '__Field':\n        case '__Type':\n        case '__TypeKind':\n        case '__Schema':\n          return false;\n        default:\n          return (\n            type.kind === 'SCALAR' ||\n            type.kind === 'ENUM' ||\n            type.kind === 'INPUT_OBJECT' ||\n            type.kind === 'OBJECT' ||\n            type.kind === 'INTERFACE' ||\n            type.kind === 'UNION'\n          );\n      }\n    })\n    .map(minifyIntrospectionType)\n    .sort(nameCompare);\n\n  return {\n    name: 'name' in schema ? schema.name : undefined,\n    __schema: {\n      queryType: {\n        kind: queryType.kind,\n        name: queryType.name,\n      },\n      mutationType: mutationType\n        ? {\n            kind: mutationType.kind,\n            name: mutationType.name,\n          }\n        : null,\n      subscriptionType: subscriptionType\n        ? {\n            kind: subscriptionType.kind,\n            name: subscriptionType.name,\n          }\n        : null,\n      types: minifiedTypes,\n      directives: [],\n    },\n  };\n};\n", "import type {\n  IntrospectionQuery,\n  IntrospectionType,\n  IntrospectionEnumValue,\n  IntrospectionInputValue,\n  IntrospectionTypeRef,\n  IntrospectionNamedTypeRef,\n  IntrospectionField,\n} from 'graphql';\n\nimport type { IntrospectionResult } from '../loaders';\n\nconst printName = (input: string | undefined | null): string => (input ? `'${input}'` : 'never');\n\nconst printTypeRef = (typeRef: IntrospectionTypeRef) => {\n  if (typeRef.kind === 'NON_NULL') {\n    return `{ kind: 'NON_NULL'; name: never; ofType: ${printTypeRef(typeRef.ofType)}; }`;\n  } else if (typeRef.kind === 'LIST') {\n    return `{ kind: 'LIST'; name: never; ofType: ${printTypeRef(typeRef.ofType)}; }`;\n  } else {\n    return `{ kind: ${printName(typeRef.kind)}; name: ${printName(typeRef.name)}; ofType: null; }`;\n  }\n};\n\nconst printInputFields = (inputFields: readonly IntrospectionInputValue[]) => {\n  let output = '';\n  for (const inputField of inputFields) {\n    if (output) output += ', ';\n    const name = printName(inputField.name);\n    const type = printTypeRef(inputField.type);\n    const defaultValue = inputField.defaultValue ? JSON.stringify(inputField.defaultValue) : 'null';\n    output += `{ name: ${name}; type: ${type}; defaultValue: ${defaultValue} }`;\n  }\n  return `[${output}]`;\n};\n\nconst printNamedTypes = (\n  values: readonly (IntrospectionEnumValue | IntrospectionNamedTypeRef)[]\n) => {\n  if (!values.length) return 'never';\n  let output = '';\n  for (const value of values) {\n    if (output) output += ' | ';\n    output += printName(value.name);\n  }\n  return output;\n};\n\nconst printFields = (fields: readonly IntrospectionField[]) => {\n  let output = '';\n  for (const field of fields) {\n    const name = printName(field.name);\n    const type = printTypeRef(field.type);\n    output += `${printName(field.name)}: { name: ${name}; type: ${type} }; `;\n  }\n  return `{ ${output}}`;\n};\n\nexport const printIntrospectionType = (type: IntrospectionType) => {\n  if (type.kind === 'ENUM') {\n    const values = printNamedTypes(type.enumValues);\n    return `{ name: ${printName(type.name)}; enumValues: ${values}; }`;\n  } else if (type.kind === 'INPUT_OBJECT') {\n    const fields = printInputFields(type.inputFields);\n    return `{ kind: 'INPUT_OBJECT'; name: ${printName(type.name)}; isOneOf: ${\n      (type as any).isOneOf || false\n    }; inputFields: ${fields}; }`;\n  } else if (type.kind === 'OBJECT') {\n    const fields = printFields(type.fields);\n    return `{ kind: 'OBJECT'; name: ${printName(type.name)}; fields: ${fields}; }`;\n  } else if (type.kind === 'INTERFACE') {\n    const name = printName(type.name);\n    const fields = printFields(type.fields);\n    const possibleTypes = printNamedTypes(type.possibleTypes);\n    return `{ kind: 'INTERFACE'; name: ${name}; fields: ${fields}; possibleTypes: ${possibleTypes}; }`;\n  } else if (type.kind === 'UNION') {\n    const name = printName(type.name);\n    const possibleTypes = printNamedTypes(type.possibleTypes);\n    return `{ kind: 'UNION'; name: ${name}; fields: {}; possibleTypes: ${possibleTypes}; }`;\n  } else {\n    return 'unknown';\n  }\n};\n\nexport function preprocessIntrospectionTypes(\n  introspection: IntrospectionResult | IntrospectionQuery\n): string {\n  let evaluatedTypes = '';\n  for (const type of introspection.__schema.types) {\n    const typeStr = printIntrospectionType(type);\n    if (evaluatedTypes) evaluatedTypes += '\\n';\n    evaluatedTypes += `    ${printName(type.name)}: ${typeStr};`;\n  }\n  return `{\\n${evaluatedTypes}\\n}`;\n}\n\nexport function preprocessIntrospection(\n  introspection: IntrospectionResult | IntrospectionQuery,\n  typesStr = preprocessIntrospectionTypes(introspection)\n): string {\n  const { __schema: schema } = introspection;\n  const name = 'name' in introspection ? introspection.name : undefined;\n  const queryName = printName(schema.queryType.name);\n  const mutationName = printName(schema.mutationType && schema.mutationType.name);\n  const subscriptionName = printName(schema.subscriptionType && schema.subscriptionType.name);\n  return (\n    '{\\n' +\n    `  name: ${printName(name)};\\n` +\n    `  query: ${queryName};\\n` +\n    `  mutation: ${mutationName};\\n` +\n    `  subscription: ${subscriptionName};\\n` +\n    `  types: ${typesStr};\\n}`\n  );\n}\n", "const PREAMBLE_IGNORE = ['/* eslint-disable */', '/* prettier-ignore */'].join('\\n') + '\\n';\n\nconst ANNOTATION_DTS = [\n  '/** An IntrospectionQuery representation of your schema.',\n  ' *',\n  ' * @remarks',\n  ' * This is an introspection of your schema saved as a file by GraphQLSP.',\n  ' * It will automatically be used by `gql.tada` to infer the types of your GraphQL documents.',\n  ' * If you need to reuse this data or update your `scalars`, update `tadaOutputLocation` to',\n  ' * instead save to a .ts instead of a .d.ts file.',\n  ' */',\n].join('\\n');\n\nconst ANNOTATION_TS = [\n  '/** An IntrospectionQuery representation of your schema.',\n  ' *',\n  ' * @remarks',\n  ' * This is an introspection of your schema saved as a file by GraphQLSP.',\n  ' * You may import it to create a `graphql()` tag function with `gql.tada`',\n  ' * by importing it and passing it to `initGraphQLTada<>()`.',\n  ' *',\n  ' * @example',\n  ' * ```',\n  \" * import { initGraphQLTada } from 'gql.tada';\",\n  \" * import type { introspection } from './introspection';\",\n  ' *',\n  ' * export const graphql = initGraphQLTada<{',\n  ' *   introspection: typeof introspection;',\n  ' *   scalars: {',\n  ' *     DateTime: string;',\n  ' *     Json: any;',\n  ' *   };',\n  ' * }>();',\n  ' * ```',\n  ' */',\n].join('\\n');\n\nexport { PREAMBLE_IGNORE, ANNOTATION_DTS, ANNOTATION_TS };\n", "import type { IntrospectionQuery } from 'graphql';\nimport type { IntrospectionResult } from '../loaders';\n\nimport { <PERSON>a<PERSON><PERSON>r } from '../errors';\nimport { PREAMBLE_IGNORE, ANNOTATION_DTS, ANNOTATION_TS } from './constants';\nimport { preprocessIntrospection, preprocessIntrospectionTypes } from './preprocess';\n\nconst TYPES_VAR = 'introspection_types';\n\nconst stringifyJson = (input: unknown | string): string =>\n  typeof input === 'string' ? input : JSON.stringify(input, null, 2);\n\ninterface OutputIntrospectionFileOptions {\n  fileType: '.ts' | '.d.ts' | string;\n  shouldPreprocess?: boolean;\n}\n\nexport function outputIntrospectionFile(\n  introspection: IntrospectionQuery | IntrospectionResult,\n  opts: OutputIntrospectionFileOptions\n): string {\n  if (/\\.d\\.ts$/.test(opts.fileType)) {\n    const out = [PREAMBLE_IGNORE];\n    if (typeof introspection !== 'string' && opts.shouldPreprocess) {\n      // NOTE: When types aren't exported separately, composite tsconfigs\n      // will output a serialization error in diagnostics\n      out.push(\n        `export type ${TYPES_VAR} = ${preprocessIntrospectionTypes(introspection)};\\n`,\n        ANNOTATION_DTS,\n        `export type introspection = ${preprocessIntrospection(introspection, TYPES_VAR)};\\n`,\n        `import * as gqlTada from 'gql.tada';\\n`\n      );\n    } else {\n      out.push(\n        ANNOTATION_DTS,\n        `export type introspection = ${stringifyJson(introspection)};\\n`,\n        \"import * as gqlTada from 'gql.tada';\\n\"\n      );\n    }\n    // NOTE: When the `name` option is used and multiple schemas are present,\n    // we omit the automatic schema declaration and rely on the user calling\n    // `initGraphQLTada()` themselves\n    if (!('name' in introspection) || !introspection.name) {\n      out.push(\n        \"declare module 'gql.tada' {\",\n        '  interface setupSchema {',\n        '    introspection: introspection',\n        '  }',\n        '}'\n      );\n    }\n    return out.join('\\n');\n  } else if (/\\.ts$/.test(opts.fileType)) {\n    const json = stringifyJson(introspection);\n    return [\n      PREAMBLE_IGNORE,\n      ANNOTATION_TS,\n      `const introspection = ${json} as const;\\n`,\n      'export { introspection };',\n    ].join('\\n');\n  }\n\n  throw new TadaError(\n    `No available introspection format for \"${opts.fileType}\" (expected \".ts\" or \".d.ts\")`\n  );\n}\n"], "names": ["rehydrateGraphQlError", "r", "message", "extensions", "name", "e", "GraphQLError", "nodes", "source", "positions", "path", "CombinedError", "Error", "constructor", "graphQLErrors", "map", "t", "a", "networkError", "super", "this", "response", "toString", "_hasField", "data", "fieldName", "fields", "some", "field", "_supportsDeprecatedArgumentsArg", "argsField", "find", "args", "arg", "NO_SUPPORTED_FEATURES", "directiveIsRepeatable", "specifiedByURL", "inputValueDeprecation", "directiveArgumentsIsDeprecated", "fieldArgumentsIsDeprecated", "inputOneOf", "toSupportedFeatures", "directive", "type", "inputValue", "introspectionToSupportedFeatures", "__schema", "types", "_localSupport", "getPeerSupportedFeatures", "schema", "GraphQLSchema", "query", "GraphQLObjectType", "_noop", "GraphQLID", "result", "executeSync", "document", "makeIntrospectSupportQuery", "_introspection<PERSON><PERSON>y", "_previousSupport", "makeIntrospectionQuery", "support", "_makeIntrospectionQuery", "kind", "Kind", "DOCUMENT", "definitions", "OPERATION_DEFINITION", "NAME", "value", "operation", "OperationTypeNode", "QUERY", "selectionSet", "SELECTION_SET", "selections", "FIELD", "_makeSchemaSelection", "_makeSchemaFullTypeFragment", "_makeSchemaInputValueFragment", "_makeTypeRefFragment", "alias", "arguments", "ARGUMENT", "STRING", "_makeFieldNamesSelection", "includeArgs", "options", "FRAGMENT_SPREAD", "_makeSchemaArgsField", "FRAGMENT_DEFINITION", "typeCondition", "NAMED_TYPE", "BOOLEAN", "supportsValueDeprecation", "_makeTypeRefSelection", "depth", "loadFromSDL", "config", "subscriptions", "Set", "abort", "load", "async", "ext", "extname", "file", "fs", "readFile", "encoding", "introspection", "JSON", "parse", "buildClientSchema", "<PERSON><PERSON><PERSON><PERSON>", "buildSchema", "assumeValidSDL", "query<PERSON><PERSON>ult", "errors", "reload", "notifyOnUpdate", "onUpdate", "size", "ts", "sys", "watchFile", "watcher", "subscriber", "_error", "WatchFileKind", "UseFsEventsOnParentDirectory", "fallback<PERSON><PERSON>ing", "PollingWatchKind", "PriorityInterval", "close", "controller", "AbortController", "watch", "signal", "persistent", "_event", "error", "add", "delete", "loadIntrospection", "loadSchema", "retryExchange", "y", "retryIf", "d", "retryWith", "s", "l", "initialDelayMs", "p", "max<PERSON>elay<PERSON>", "m", "maxNumberAttempts", "c", "randomDelay", "forward", "dispatchDebug", "v", "f", "h", "next", "x", "E", "context", "retry", "count", "delay", "i", "Math", "random", "min", "key", "process", "env", "NODE_ENV", "retryCount", "delayAmount", "o", "n", "u", "loadFromURL", "interval", "timeoutID", "supportedFeatures", "client", "Client", "url", "fetchOptions", "headers", "exchanges", "NODE_TLS_REJECT_UNAUTHORIZED", "fetchExchange", "introspect", "introspectionResult", "scheduleUpdate", "setTimeout", "supportResult", "length", "clearTimeout", "getURLConfig", "origin", "URL", "undefined", "url<PERSON><PERSON><PERSON>", "fetchInterval", "rootPath", "resolve", "cwd", "maybeRelative", "filePath", "relative", "startsWith", "TSError", "diagnostic", "messageText", "fileName", "<PERSON><PERSON><PERSON><PERSON>", "SCHEMA_PROPS", "parseSchemaConfig", "input", "resolveConfigDir", "normalize", "replace", "_match", "tadaOutputLocation", "tadaTurboLocation", "tadaPersistedLocation", "output", "isFile", "stat", "isDir", "isDirectory", "predicate", "then", "catch", "_resolve", "require", "bind", "createRequire", "pathToFileURL", "__filename", "href", "_documentCurrentScript", "src", "baseURI", "resolveExtend", "extend", "from", "toTSConfigPath", "paths", "tsconfigPath", "readTSConfigFile", "contents", "parseConfigFileTextToJson", "findTSConfigFile", "targetPath", "git<PERSON>ath", "parentPath", "loadConfig", "rootTsconfigPath", "tsconfig", "pluginConfig", "compilerOptions", "plugins", "getPluginConfig", "config<PERSON><PERSON>", "dirname", "Array", "isArray", "extends", "nameCompare", "objA", "objB", "mapTypeRef", "fromType", "ofType", "mapEnumValue", "isDeprecated", "deprecationReason", "mapInputField", "defaultValue", "mapField", "sort", "mapPossibleType", "ref", "minifyIntrospectionType", "enum<PERSON><PERSON><PERSON>", "inputFields", "isOneOf", "interfaces", "possibleTypes", "printName", "printTypeRef", "typeRef", "printNamedTypes", "values", "printFields", "printIntrospectionType", "inputField", "stringify", "printInputFields", "preprocessIntrospectionTypes", "evaluatedTypes", "typeStr", "preprocessIntrospection", "typesStr", "queryName", "queryType", "mutationName", "mutationType", "subscriptionName", "subscriptionType", "PREAMBLE_IGNORE", "join", "ANNOTATION_DTS", "ANNOTATION_TS", "TYPES_VAR", "stringify<PERSON>son", "getSchemaConfigForName", "index", "schemas", "loadRef", "teardowns", "_loaders", "get<PERSON>oaders", "loader", "push", "version", "current", "multi", "reduce", "acc", "autoupdate", "loaders", "teardown", "pop", "Promise", "all", "TypeError", "minifiedTypes", "filter", "directives", "outputIntrospectionFile", "opts", "test", "fileType", "out", "shouldPreprocess", "json", "parseConfig", "template", "_loop", "prop", "Boolean", "uniqueValues"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,IAAMA,wBAAyBC;EAC7B,IACEA,KACyB,mBAAlBA,EAAMC,YACZD,EAAME,cAA6B,mBAAfF,EAAMG;IAE3B,OAAOH;SACF,IAAqB,mBAAVA,KAA+C,mBAAlBA,EAAMC;IACnD,OAAO,IAAIG,EAAAC,aACTL,EAAMC,SACND,EAAMM,OACNN,EAAMO,QACNP,EAAMQ,WACNR,EAAMS,MACNT,GACAA,EAAME,cAAc,CAAA;;IAGtB,OAAO,IAAIE,EAAAA,aAAaJ;;;;AAkBrB,MAAMU,sBAAsBC;EAwCjCC,WAAAA,CAAYR;IAKV,IAAMJ,KAA2BI,EAAMS,iBAAiB,IAAIC,IAC1Df;IAEF,IAAMgB,IAnGmB,EAC3BX,GACAJ;MAEA,IAAIe,IAAQ;MACZ,IAAIX;QAAY,OAAQ,aAAYA,EAAWH;;MAC/C,IAAID;QACF,KAAK,IAAMgB,KAAOhB,GAAa;UAC7B,IAAIe;YAAOA,KAAS;;UACpBA,KAAU,aAAYC,EAAIf;AAC5B;;MAEF,OAAOc;AAAK,MAZe,CAoGvBX,EAAMa,cACNjB;IAGFkB,MAAMH;IAENI,KAAKhB,OAAO;IACZgB,KAAKlB,UAAUc;IACfI,KAAKN,gBAAgBb;IACrBmB,KAAKF,eAAeb,EAAMa;IAC1BE,KAAKC,WAAWhB,EAAMgB;AACxB;EAEAC,QAAAA;IACE,OAAOF,KAAKlB;AACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxFF,IAAMqB,YAAYA,CAChBC,GACAC,QACcD,OAAUA,EAAKE,UAAUF,EAAKE,OAAOC,MAAMC,KAAUA,EAAMxB,SAASqB;;AAEpF,IAAMI,kCACJL;EAEA,IAAMM,IAAYN,KAAQA,EAAKE,UAAUF,EAAKE,OAAOK,MAAMH,KAAyB,WAAfA,EAAMxB;EAC3E,UACE0B,KACAA,EAAUE,QACVF,EAAUE,KAAKD,MAAME,KAAqB,wBAAbA,EAAI7B;AAClC;;AAYI,IAAM8B,IAA2C;EACtDC,wBAAuB;EACvBC,iBAAgB;EAChBC,wBAAuB;EACvBC,iCAAgC;EAChCC,6BAA4B;EAC5BC,aAAY;;;AAIP,IAAMC,sBAAuBjB,MAAyD;EAC3FW,uBAAuBZ,UAAUC,EAAKkB,WAAW;EACjDN,gBAAgBb,UAAUC,EAAKmB,MAAM;EACrCH,YAAYjB,UAAUC,EAAKmB,MAAM;EACjCN,uBAAuBd,UAAUC,EAAKoB,YAAY;EAClDN,gCAAgCT,gCAAgCL,EAAKkB;EACrEH,4BAA4BV,gCAAgCL,EAAKI;;;AAG5D,IAAMiB,mCAAoCrB;EAC/C,IAAMkB,IAAYlB,EAAKsB,SAASC,MAAMhB,MAAMY,KAAuB,kBAAdA,EAAKvC;EAC1D,IAAMuC,IAAOnB,EAAKsB,SAASC,MAAMhB,MAAMY,KAAuB,aAAdA,EAAKvC;EACrD,IAAMwC,IAAapB,EAAKsB,SAASC,MAAMhB,MAAMY,KAAuB,mBAAdA,EAAKvC;EAC3D,IAAMwB,IAAQJ,EAAKsB,SAASC,MAAMhB,MAAMY,KAAuB,cAAdA,EAAKvC;EACtD,IAAIsC,KAAaC,KAAQC,KAAchB;IACrC,OAAO;MACLO,uBAAuBZ,UAAUmB,GAAW;MAC5CN,gBAAgBb,UAAUoB,GAAM;MAChCH,YAAYjB,UAAUoB,GAAM;MAC5BN,uBAAuBd,UAAUqB,GAAY;MAC7CN,gCAAgCT,gCAAgCa;MAChEH,4BAA4BV,gCAAgCD;;;IAG9D,OAAOM;;AACT;;AAGF,IAAIc;;AAGG,IAAMC,2BAA2BA;EACtC,KAAKD,GAAe;IAClB,IAAME,IAAS,IAAIC,gBAAc;MAC/BC,OAAO,IAAIC,EAAAA,kBAAkB;QAC3BjD,MAAM;QACNsB,QAAQ;UAAE4B,OAAO;YAAEX,MAAMY,EAAAA;;;;;IAG7B,IAAMC,IAASC,EAAAA,YAAY;MAAEP;MAAQQ,UAAUC;;IAC/C,OAAQX,IAAgBQ,EAAOhC,OAC3BiB,oBAAoBe,EAAOhC,QAC3BU;AACN;EACA,OAAOc;AAAa;;AAGtB,IAAIY;;AACJ,IAAIC;;AAEG,IAAMC,yBAA0BC;EACrC,IAAIH,KAAuBC,MAAqBE;IAC9C,OAAOH;;IAEP,OAAQA,IAAsBI,wBAAyBH,IAAmBE;;AAC5E;;AAGF,IAAMC,0BAA2BD,MAA8C;EAC7EE,MAAMC,EAAIA,KAACC;EACXC,aAAa,EACX;IACEH,MAAMC,EAAIA,KAACG;IACXjE,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCC,WAAWC,EAAiBA,kBAACC;IAC7BC,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCI,cAAcI,qBAAqBhB;;;KAM3CiB,4BAA4BjB,IAC5BkB,8BAA8BlB,IAC9BmB;;;AAKG,IAAMvB,6BAA6BA,OAAqB;EAC7DM,MAAMC,EAAIA,KAACC;EACXC,aAAa,EACX;IACEH,MAAMC,EAAIA,KAACG;IACXjE,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCC,WAAWC,EAAiBA,kBAACC;IAC7BC,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACY;QACXK,OAAO;UAAElB,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QACjCnE,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCa,WAAW,EACT;UACEnB,MAAMC,EAAIA,KAACmB;UACXjF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;UAChCA,OAAO;YAAEN,MAAMC,EAAIA,KAACoB;YAAQf,OAAO;;;QAGvCI,cAAcY,yBAAyB;UAAEC,cAAa;;SAExD;QACEvB,MAAMC,EAAIA,KAACY;QACXK,OAAO;UAAElB,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QACjCnE,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCa,WAAW,EACT;UACEnB,MAAMC,EAAIA,KAACmB;UACXjF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;UAChCA,OAAO;YAAEN,MAAMC,EAAIA,KAACoB;YAAQf,OAAO;;;QAGvCI,cAAcY,yBAAyB;UAAEC,cAAa;;SAExD;QACEvB,MAAMC,EAAIA,KAACY;QACXK,OAAO;UAAElB,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QACjCnE,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCa,WAAW,EACT;UACEnB,MAAMC,EAAIA,KAACmB;UACXjF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;UAChCA,OAAO;YAAEN,MAAMC,EAAIA,KAACoB;YAAQf,OAAO;;;QAGvCI,cAAcY,yBAAyB;UAAEC,cAAa;;SAExD;QACEvB,MAAMC,EAAIA,KAACY;QACXK,OAAO;UAAElB,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QACjCnE,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCa,WAAW,EACT;UACEnB,MAAMC,EAAIA,KAACmB;UACXjF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;UAChCA,OAAO;YAAEN,MAAMC,EAAIA,KAACoB;YAAQf,OAAO;;;QAGvCI,cAAcY,yBAAyB;UAAEC,cAAa;;;;;;;AAQlE,IAAMD,2BAA4BE,MAAyD;EACzFxB,MAAMC,EAAIA,KAACU;EACXC,YAAY,EACV;IACEZ,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCI,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;YAE9BkB,EAAQD,cACP,EACC;QACEvB,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCI,cAAc;UACZV,MAAMC,EAAIA,KAACU;UACXC,YAAY,EACV;YACEZ,MAAMC,EAAIA,KAACY;YACX1E,MAAM;cAAE6D,MAAMC,EAAIA,KAACI;cAAMC,OAAO;;;;YAM1C;;;;;AAOd,IAAMQ,uBAAwBhB,MAAkD;EAC9EE,MAAMC,EAAIA,KAACU;EACXC,YAAY,EAEV;IACEZ,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCI,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;;;KAMxC;IACEN,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCI,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;;;KAMxC;IACEN,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCI,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;;;KAMxC;IACEN,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCI,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACwB;QACXtF,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;;;KAMxC;IACEN,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCI,cAAc;MACZV,MAAMC,EAAIA,KAACU;MACXC,YAAY,EACV;QACEZ,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;SAElC;QACEN,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;SAElC;QACEN,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;SAElCoB,qBAAqB5B,EAAQzB,oCACzByB,EAAQ5B,wBACP,EACC;QACE8B,MAAMC,EAAIA,KAACY;QACX1E,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;YAGpC;;;;;AAOd,IAAMS,8BAA+BjB,MAAwD;EAC3FE,MAAMC,EAAIA,KAAC0B;EACXxF,MAAM;IAAE6D,MAAMC,EAAIA,KAACI;IAAMC,OAAO;;EAChCsB,eAAe;IAAE5B,MAAMC,EAAIA,KAAC4B;IAAY1F,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;;EACxEI,cAAc;IACZV,MAAMC,EAAIA,KAACU;IACXC,YAAY,EACV;MACEZ,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;OAElC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;OAElC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;UAE9BR,EAAQvB,aACP,EAAC;MAAEyB,MAAMC,EAAIA,KAACY;MAAO1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;UACtD,OACAR,EAAQ3B,iBACP,EACC;MACE6B,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;UAGpC,IACJ;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;MAChCa,WAAW,EACT;QACEnB,MAAMC,EAAIA,KAACmB;QACXjF,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCA,OAAO;UAAEN,MAAMC,EAAIA,KAAC6B;UAASxB,QAAO;;;MAGxCI,cAAc;QACZV,MAAMC,EAAIA,KAACU;QACXC,YAAY,EACV;UACEZ,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;WAElC;UACEN,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;WAElC;UACEN,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;WAElC;UACEN,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;WAElCoB,qBAAqB5B,EAAQxB,6BAC7B;UACE0B,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;UAChCI,cAAc;YACZV,MAAMC,EAAIA,KAACU;YACXC,YAAY,EACV;cACEZ,MAAMC,EAAIA,KAACwB;cACXtF,MAAM;gBAAE6D,MAAMC,EAAIA,KAACI;gBAAMC,OAAO;;;;;;OAQ9C;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;MAChCI,cAAc;QACZV,MAAMC,EAAIA,KAACU;QACXC,YAAY,EACV;UACEZ,MAAMC,EAAIA,KAACwB;UACXtF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;;;OAKxC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;MAChCI,cAAc;QACZV,MAAMC,EAAIA,KAACU;QACXC,YAAY,EACV;UACEZ,MAAMC,EAAIA,KAACwB;UACXtF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;;;OAKxC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;MAChCa,WAAWrB,EAAQ1B,wBACf,EACE;QACE4B,MAAMC,EAAIA,KAACmB;QACXjF,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCA,OAAO;UAAEN,MAAMC,EAAIA,KAAC6B;UAASxB,QAAO;;YAGxC;MACJI,cAAc;QACZV,MAAMC,EAAIA,KAACU;QACXC,YAAY,EACV;UACEZ,MAAMC,EAAIA,KAACwB;UACXtF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;;;OAKxC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;MAChCa,WAAW,EACT;QACEnB,MAAMC,EAAIA,KAACmB;QACXjF,MAAM;UAAE6D,MAAMC,EAAIA,KAACI;UAAMC,OAAO;;QAChCA,OAAO;UAAEN,MAAMC,EAAIA,KAAC6B;UAASxB,QAAO;;;MAGxCI,cAAc;QACZV,MAAMC,EAAIA,KAACU;QAEXC,YAAY,EACV;UACEZ,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;WAElC;UACEN,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;WAElC;UACEN,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;WAElC;UACEN,MAAMC,EAAIA,KAACY;UACX1E,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;;;;;;;AAS9C,IAAMoB,uBAAwBK,MAAkD;EAC9E/B,MAAMC,EAAIA,KAACY;EACX1E,MAAM;IAAE6D,MAAMC,EAAIA,KAACI;IAAMC,OAAO;;EAChCa,WAAWY,IACP,EACE;IACE/B,MAAMC,EAAIA,KAACmB;IACXjF,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCA,OAAO;MAAEN,MAAMC,EAAIA,KAAC6B;MAASxB,QAAO;;QAGxC;EACJI,cAAc;IACZV,MAAMC,EAAIA,KAACU;IACXC,YAAY,EACV;MACEZ,MAAMC,EAAIA,KAACwB;MACXtF,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;;;;;AAMxC,IAAMU,gCAAiClB,MAAwD;EAC7FE,MAAMC,EAAIA,KAAC0B;EACXxF,MAAM;IAAE6D,MAAMC,EAAIA,KAACI;IAAMC,OAAO;;EAChCsB,eAAe;IAAE5B,MAAMC,EAAIA,KAAC4B;IAAY1F,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;;EACxEI,cAAc;IACZV,MAAMC,EAAIA,KAACU;IACXC,YAAY,EACV;MACEZ,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;OAElC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;OAElC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;OAElC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;MAChCI,cAAc;QACZV,MAAMC,EAAIA,KAACU;QACXC,YAAY,EACV;UACEZ,MAAMC,EAAIA,KAACwB;UACXtF,MAAM;YAAE6D,MAAMC,EAAIA,KAACI;YAAMC,OAAO;;;;UAKpCR,EAAQ1B,wBACP,EACC;MACE4B,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;OAElC;MACEN,MAAMC,EAAIA,KAACY;MACX1E,MAAM;QAAE6D,MAAMC,EAAIA,KAACI;QAAMC,OAAO;;UAGpC;;;;AAKV,IAAMW,uBAAuBA,OAA+B;EAC1DjB,MAAMC,EAAIA,KAAC0B;EACXxF,MAAM;IAAE6D,MAAMC,EAAIA,KAACI;IAAMC,OAAO;;EAChCsB,eAAe;IAAE5B,MAAMC,EAAIA,KAAC4B;IAAY1F,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;;EACxEI,cAAcsB,sBAAsB;;;AAGtC,IAAMA,wBAAyBC,MAAqC;EAClEjC,MAAMC,EAAIA,KAACU;EACXC,YACEqB,IAAQ,IACJ,EACE;IACEjC,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;KAElC;IACEN,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;KAElC;IACEN,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;IAChCI,cAAcsB,sBAAsBC,IAAQ;QAGhD,EACE;IACEjC,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;KAElC;IACEN,MAAMC,EAAIA,KAACY;IACX1E,MAAM;MAAE6D,MAAMC,EAAIA,KAACI;MAAMC,OAAO;;;;;ACllBrC,SAAS4B,YAAYC;EAC1B,IAAMC,IAAgB,IAAIC;EAE1B,IAAIC,IAA6B;EACjC,IAAI/C,IAAoC;EAExC,IAAMgD,OAAOC;IACX,IAAMC,IAAMhG,EAAKiG,QAAQP,EAAOQ;IAChC,IAAMpF,UAAaqF,EAAGC,SAASV,EAAOQ,MAAM;MAAEG,UAAU;;IACxD,IAAY,YAARL,GAAiB;MACnB,IAAMM,IAAgBC,KAAKC,MAAM1F;MACjC,KAAKwF,MAAkBA,EAAclE;QACnC,MAAM,IAAIlC,MACR;;MAIJ,OAAO;QACLoG,eAAe;aACVA;UACH5G,MAAMgG,EAAOhG;;QAEf8C,QAAQiE,EAAiBA,kBAACH,GAAe;UAAEI,eAAehB,EAAOgB;;;AAErE,WAAO;MACL,IAAMlE,IAASmE,EAAWA,YAAC7F,GAAM;QAAE8F,kBAAkBlB,EAAOgB;;MAC5D,IAAMhE,IAAQU,uBAAuBb;MACrC,IAAMsE,IAAc9D,EAAAA,YAAY;QAAEP;QAAQQ,UAAUN;;MACpD,IAAImE,EAAYC;QACd,MAAM,IAAI7G,cAAc;UAAEG,eAAeyG,EAAYC;;aAChD,IAAID,EAAY/F,MAAM;QAK3B,OAAO;UAAEwF,eAJa;eAChBO,EAAY/F;YAChBpB,MAAMgG,EAAOhG;;UAES8C;;AAC1B;QACE,MAAM,IAAItC,MACR;;AAIN;AAAA;EA4CF,OAAO;IACL,QAAIR;MACF,OAAOgG,EAAOhG;AACf;IACDqG,MAAUD,MAACiB,KACFA,MAAWjE,IAAUA,UAAegD,SAAUhD;IAEvDkE,cAAAA,CAAeC;MACb,KAAKtB,EAAcuB;QAjDTnB;UACZ,IAAIoB,EAAGC,IAAIC,WAAW;YACpB,IAAMC,IAAUH,EAAGC,IAAIC,UACrB3B,EAAOQ,OACPH;cACE;gBACE,IAAKjD,UAAegD;kBAClB,KAAK,IAAMyB,KAAc5B;oBAAe4B,EAAWzE;;;AAEvD,gBAAE,OAAO0E,IAAS;AAAA,gBAEpB,KACA;cAGEH,WAAWF,EAAGM,cAAcC;cAC5BC,iBAAiBR,EAAGS,iBAAiBC;;YAGzChC,IAAQA,MAAMyB,EAAQQ;AACxB,iBAAO;YACL,IAAMC,IAAa,IAAIC;YACvBnC,IAAQA,MAAMkC,EAAWlC;YACzB,IAAMyB,IAAUnB,EAAG8B,MAAMvC,EAAOQ,MAAM;cACpCgC,QAAQH,EAAWG;cACnBC,aAAY;;YAEd;cACE,WAAW,IAAMC,KAAUd;gBACzB,IAAKxE,UAAegD;kBAClB,KAAK,IAAMyB,KAAc5B;oBAAe4B,EAAWzE;;;;AAGxD,cAAC,OAAOuF;cACP,IAAmB,iBAAfA,EAAM3I;gBAAuB,MAAM2I;;AACzC,cAAU;cACRxC,IAAQ;AACV;AACF;AAAA,UAW2BoC;;MACzBtC,EAAc2C,IAAIrB;MAClB,OAAO;QACLtB,EAAc4C,OAAOtB;QACrB,KAAKtB,EAAcuB,QAAQrB;UAAOA;;AAAO;AAE5C;IACD,uBAAM2C;MACJ,IAAM1F,UAAepC,KAAKoF;MAC1B,OAAOhD,KAAUA,EAAOwD;AACzB;IACD,gBAAMmC;MACJ,IAAM3F,UAAepC,KAAKoF;MAC1B,OAAOhD,KAAUA,EAAON;AAC1B;;AAEJ;;ACjBa,IAAAkG,gBAAiBC;EAC5B,KAAMC,SAAEC,GAAOC,WAAEC,KAAcJ;EAC/B,IAAMK,IAAYL,EAAQM,kBAAkB;EAC5C,IAAMC,IAAYP,EAAQQ,cAAc;EACxC,IAAMC,IAAeT,EAAQU,qBAAqB;EAClD,IAAMC,IACmB,QAAvBX,EAAQY,gBAAwBZ,EAAQY,eAAc;EAExD,OAAO,EAAGC,SAAAb,GAASc,eAAAC,OACjBC;IACE,KAAQ7J,QAAQ8J,GAAQC,MAAMC,KAC5BvK;IAEF,IAAMwK,IAEJpK,UAAUJ;MACR,IAAMI,IAAoBJ,EAAUyK,QAAQC,SAAS;QACnDC,OAAO;QACPC,OAAO;;MAGT,IAAMC,MAAezK,EAAMuK;MAC3B,IAAIvB,IAAchJ,EAAMwK,SAASnB;MAEjC,IAAMH,IAAgBwB,KAAKC,WAAW;MACtC,IAAIhB;QAGF,IAAIX,IAAcE,IAAgBK;UAChCP,KAAeE;;UAEfF,IAAcO;;;QAIhBP,IAAc0B,KAAKE,IAAIH,IAAapB,GAAWE;;MAIjDvJ,EAAMwK,QAAQxB;MAKd,IAAMI,IAEJzI,QAAOX,MAEU,YAAZA,EAAG4D,QAAgC,eAAZ5D,EAAG4D,SAC3B5D,EAAG6K,QAAQjL,EAAUiL,KAHzBlK,CADAqJ;MASF,iBAAAc,QAAAC,IAAAC,YAAAjB,EAAc;QACZzH,MAAM;QACNzC,SAAU,4DAA2D4K,OAAgBhB;QACrFtF,WAAAvE;QACAuB,MAAM;UACJ8J,YAAAR;UACAS,aAAAlC;;QACD7I,QAAA;;MAIH,OASES,UAAUwI,EAAVxI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAFAuK,EAAS,MAAMnC,GAAfmC,CANAC,UACEC,cAAczL,EAAUgE,MAAMhE,GAAW;WACpCA,EAAUyK;QACbC,OAAAtK;;OAtDRA,CADAiK;IAiEF,OAGEtJ,QAAOf;MACL,IAAMI,IAAQJ,EAAIuE,UAAUkG,QAAQC;MAGpC,KACG1K,EAAI8I,WACJQ,IACIA,EAAQtJ,EAAI8I,OAAO9I,EAAIuE,aACvBiF,KAAcxJ,EAAI8I,MAAM7H,eAC7B;QAEA,IAAIb,GAAO;UACTA,EAAMuK,QAAQ;UACdvK,EAAMwK,QAAQ;AAChB;QACA,QAAO;AACT;MAIA,OADIxK,KAASA,EAAMuK,SAAU,MAAMd,IAAe,IAClB;QAC9B,IAAM9I,IAAYyI,IACdA,EAAUxJ,EAAI8I,OAAO9I,EAAIuE,aACzBvE,EAAIuE;QACR,KAAKxD;UAAW,QAAO;;QAIvBwJ,EAAmBxJ;QACnB,QAAO;AACT;MAEA,iBAAAmK,QAAAC,IAAAC,YAAAjB,EAAc;QACZzH,MAAM;QACNzC,SACE;QACFsE,WAAWvE,EAAIuE;QAAShE,QAAA;;MAG1B,QAAO;AAAA,OAvCTQ,CADAqI,EADAyB,MAAM,EAACT,GAAaI;AAAA;AAAmB;;ACvKxC,SAASkB,YAAYvF;EAC1B,IAAMwF,IAAWxF,EAAOwF,YAAY;EACpC,IAAMvF,IAAgB,IAAIC;EAE1B,IAAIuF,IAAmC;EACvC,IAAIC,IAA8C;EAClD,IAAItI,IAAoC;EAExC,IAAMuI,IAAS,IAAIC,EAAO;IACxBC,KAAK,GAAG7F,EAAO6F;IACfC,cAAc;MAAEC,SAAS/F,EAAO+F;;IAChCC,WAAW,EACThD,cAAc;MACZO,gBAAgB;MAChBE,YAAY;MACZE,mBAAmB;MACnBP,SAAAA,CAAUT,GAAOvE;QACf,IAAIuE,EAAM7H;UAAciK,QAAQC,IAAIiB,+BAA+B;;QACnE,OAAO7H;AACT;QAEF8H;;EAkBJ,IAAMC,aAAa9F;IACjB,IAAMrD,IAAQU,uBAAuBC;IACrC,IAAMyI,UAA4BT,EAAO3I,MAA0BA,GAAO,CAAE;IAC5E;MACE,IAAIoJ,EAAoBzD;QACtB,MAAMyD,EAAoBzD;aACrB,IAAIyD,EAAoBhL,MAAM;QACnC,IAAMwF,IAAgBwF,EAAoBhL;QAC1C,OAAO;UACLwF,eAAe;eACVA;YACH5G,MAAMgG,EAAOhG;;UAEf8C,QAAQiE,EAAiBA,kBAACH,GAAe;YAAEI,cAAa;;;AAE5D;QACE,MAAM,IAAIxG,MACR;;AAIN,MAAU;MAnCW6L;QACrB,IAAIpG,EAAcuB,SAASiE;UACzBA,IAAYa,YAAWjG;YACrBoF,IAAY;YACZ;cACErI,UAAegD;AAChB,cAAC,OAAO0B;cACP1E,IAAS;AACX;YACA,IAAIA;cAAQ,KAAK,IAAMyE,KAAc5B;gBAAe4B,EAAWzE;;;AAAO,cACrEoI;;AACL,QAyBEa;AACF;AAAA;EAGF,IAAMjG,OAAOC;IACX,KAAKqF,GAAmB;MACtB,IAAM1I,IAAQO;MACd,IAAMgJ,UAAsBZ,EAAO3I,MAAkCA,GAAO,CAAE;MAC9E,IAAIuJ,EAAc5D,SAAS4D,EAAc5D,MAAMjI,cAAc8L,SAAS;QACpE;UAEE,KAAM5F,eAAEA,WAAwBuF,WAAWrK;UAC3C4J,IAAoBjJ,iCAAiCmE;AACtD,UAAC,OAAOkB;UAEP4D,IAAoB5J;AACtB;aACK,IAAIyK,EAAcnL,SAASmL,EAAc5D;QAE9C+C,IAAoBrJ,oBAAoBkK,EAAcnL;aACjD,IAAImL,EAAc5D,OAAO;QAE9B+C,IAAoB;QACpB,MAAMa,EAAc5D;AACtB;QAEE+C,IAAoB5J;;AAExB;IACA,OAAOqK,WAAWT;AAAkB;EAGtC,OAAO;IACL,QAAI1L;MACF,OAAOgG,EAAOhG;AACf;IACDqG,MAAUD,MAACiB,KACFA,MAAWjE,IAAUA,UAAegD,SAAUhD;IAEvDkE,cAAAA,CAAeC;MACbtB,EAAc2C,IAAIrB;MAClB,OAAO;QACLtB,EAAc4C,OAAOtB;QACrB,KAAKtB,EAAcuB,QAAQiE,GAAW;UACpCgB,aAAahB;UACbA,IAAY;AACd;AAAA;AAEH;IACD,uBAAM3C;MACJ,IAAM1F,UAAepC,KAAKoF;MAC1B,OAAOhD,KAAUA,EAAOwD;AACzB;IACD,gBAAMmC;MACJ,IAAM3F,UAAepC,KAAKoF;MAC1B,OAAOhD,KAAUA,EAAON;AAC1B;;AAEJ;;AC5Ha4J,IAAAA,eAAgBC;EAC3B;IACE,OAAOA,IACH;MACEd,KAAK,IAAIe,IAAsB,mBAAXD,IAAsBA,EAAOd,MAAMc;MACvDZ,SAA2B,mBAAXY,IAAsBA,EAAOZ,eAAUc;QAEzD;AACL,IAAC,OAAO/E;IACP,OAAO;AACT;AAAA;;AAQK,SAAS1B,KAAKJ;EACnB,IAAM8G,IAAYJ,aAAa1G,EAAO2G;EACtC,IAAIG;IACF,OAAOvB,YAAY;SACduB;MACHtB,UAAUxF,EAAO+G;MACjB/M,MAAMgG,EAAOhG;;SAEV,IAA6B,mBAAlBgG,EAAO2G,QAAqB;IAG5C,OAAO5G,YAAY;MACjBS,MAHWR,EAAOgH,WAAW1M,EAAK2M,QAAQjH,EAAOgH,UAAUhH,EAAO2G,UAAU3G,EAAO2G;MAInF3F,aAHwC,QAAtBhB,EAAOgB,cAAsBhB,EAAOgB,eAAc;MAIpEhH,MAAMgG,EAAOhG;;AAEjB;IACE,MAAM,IAAIQ,MAAM;;AAEpB;;ACrDO,IAAM0M,IAAMnC,QAAQmC;;AAEpB,IAAMC,gBAAiBC;EAC5B,IAAMC,IAAW/M,EAAK+M,SAASH,GAAKE;EACpC,QAAQC,EAASC,WAAW,QAAQD,IAAWD;AAAQ;;ACHlD,MAAMG,gBAAgB/M;EAG3BC,WAAAA,CAAY+M;IACV,IAAI1N,IACgC,mBAA3B0N,EAAWC,cACdD,EAAWC,YAAYA,cACvBD,EAAWC;IACjB,IAAID,EAAWhH;MAAM1G,KAAW,KAAKqN,cAAcK,EAAWhH,KAAKkH;;IACnE3M,MAAMjB;IACNkB,KAAKhB,OAAO;IACZgB,KAAKwM,aAAaA;AACpB;;;AAGK,MAAMG,kBAAkBnN;EAE7BC,WAAAA,CAAYX;IACViB,MAAMjB;IACNkB,KAAKhB,OAAO;AACd;;;ACJF,IAAM4N,IAAe,EACnB,QACA,sBACA,qBACA;;AASF,IAAMC,oBAAoBA,CAACC,GAAgBd;EACzC,IAAMe,mBAAoBD;IACxB,KAAKA;MAAO,OAAOA;;IACnB,OAAOxN,EAAK0N,UACVF,EAAMG,QAAQ,gBAAe,CAACC,GAAQlO;MACpC,IAAa,gBAATA;QACF,OAAOgN;;QAEP,MAAM,IAAIW,UACR,oBAAoB3N;;AAExB;AAEH;EAGH,IAAa,QAAT8N,KAAkC,mBAAVA;IAC1B,MAAM,IAAIH,UAAU,gDAAgDG;;EAGtE,IAAI,YAAYA,KAASA,EAAMhL,UAAkC,mBAAjBgL,EAAMhL,QAAqB;IACzE,KAAQA,QAAAA,KAAWgL;IACnB,MAAM,SAAShL;MACb,MAAM,IAAI6K,UAAU;;IAGtB,IAAI,aAAa7K,KAAUA,EAAOiJ,WAAqC,mBAAnBjJ,EAAOiJ;MACzD,KAAK,IAAMjB,KAAOhI,EAAOiJ;QACvB,IAAIjJ,EAAOiJ,QAAQjB,MAAuC,mBAAxBhI,EAAOiJ,QAAQjB;UAC/C,MAAM,IAAI6C,UACR,oEAAoE7C;;;WAIrE,IAAI,aAAahI;MACtB,MAAM,IAAI6K,UACR;;AAGN,SAAO,MAAM,YAAYG,MAAkC,mBAAjBA,EAAMhL;IAC9C,MAAM,IAAI6K,UAAU;;EAGtB,IACE,wBAAwBG,KACxBA,EAAMK,sBAC8B,mBAA7BL,EAAMK;IAEb,MAAM,IAAIR,UACR;;EAIJ,IACE,uBAAuBG,KACvBA,EAAMM,qBAC6B,mBAA5BN,EAAMM;IAEb,MAAM,IAAIT,UACR;;EAIJ,IACE,2BAA2BG,KAC3BA,EAAMO,yBACiC,mBAAhCP,EAAMO;IAEb,MAAM,IAAIV,UACR;;EAIJ,IAAMW,IAASR;EAEf,IAAIhL,IAAuBwL,EAAOxL;EAClC,IAAsB,mBAAXA,GAAqB;IAE9B,KADY4J,aAAa5J;MACfA,IAASiL,iBAAiBjL,MAAWA;;AACjD;EAEA,OAAO;OACFwL;IACHxL;IACAqL,oBAAoBJ,iBAAiBO,EAAOH;IAC5CC,mBAAmBL,iBAAiBO,EAAOF;IAC3CC,uBAAuBN,iBAAiBO,EAAOD;;AAChD;;AC3GH,IAAME,SAAUC,KAAyBA,EAAKD;;AAC9C,IAAME,QAASD,KAAyBA,EAAKE;;AAC7C,IAAMF,OAAOA,CAAChI,GAAcmI,IAAYJ,WACtC9H,EACG+H,KAAKhI,GACLoI,KAAKD,GACLE,OAAM,OAAM;;AAEjB,IAAMC,IACe,sBAAZC,UACHA,QAAQ9B,QAAQ+B,KAAKD,WACrBE,EAAAA,cAA6B,sBAAA3L,WAAAyL,QAAA,OAAAG,cAAAC,YAAAC,OAAAC,KAAAA,EAAAC,OAAA,IAAA1C,IAAA,6BAAAtJ,SAAAiM,SAAAH,MAAEnC;;AACrC,IAAMuC,gBAAgBnJ,OAAOoJ,GAAgBC;EAC3C;IACE,OAAOC,eAAeb,EAASW,GAAQ;MAAEG,OAAO,EAACF;;AAClD,IAAC,OAAO5H;IACP,OAAO;AACT;AAAA;;AAGF,IAAM6H,iBAAkBE,KACS,YAA/BvP,EAAKiG,QAAQsJ,KACTvP,EAAK2M,QAAQC,GAAK2C,GAxBP,mBAyBXvP,EAAK2M,QAAQC,GAAK2C;;AAEXC,IAAAA,mBAAmBzJ;EAC9B,IAAMwJ,IAAeF,eAAevC;EACpC,IAAM2C,UAAiBtJ,EAAGC,SAASmJ,GAAc;EACjD,IAAMzM,IAASqE,EAAGuI,0BAA0BH,GAAcE;EAC1D,IAAI3M,EAAOuF;IAAO,MAAM,IAAI4E,QAAQnK,EAAOuF;;EAC3C,OAAOvF,EAAO4C,UAAU;AAAE;;AAGfiK,IAAAA,mBAAmB5J;EAC9B,IAAIwJ,IAAeF,eAAeO,KAAchD;EAChD,IAAMF,IAAW2C,eAAerP,EAAK2M,QAAQ4C,GAAc;EAC3D,OAAOA,MAAiB7C,GAAU;IAChC,UAAUwB,KAAKqB;MAAe,OAAOA;;IACrC,IAAMM,IAAU7P,EAAK2M,QAAQ4C,GAAc,MAAM;IACjD,UAAUrB,KAAK2B,GAAS1B;MAAQ,OAAO;;IACvC,IAAM2B,IAAaT,eAAerP,EAAK2M,QAAQ4C,GAAc,MAAM;IACnE,IAAIO,MAAeP;MAAc;;IACjCA,IAAeO;AACjB;EACA,OAAO;AAAI;;AAqBAC,IAAAA,aAAahK;EACxB,IAAMiK,UAAyBL,iBAAiBC;EAChD,KAAKI;IACH,MAAM,IAAI3C,UACRuC,IACI,uCAAuC/C,cAAc+C,OACrD;;EAIR,IAAM9J,OAAOC;IACX,IAAMkK,UAAiBT,iBAAiBI;IACxC,IAAMM,IA9BeD,MACtBA,KACCA,EAASE,mBACTF,EAASE,gBAAgBC,WACzBH,EAASE,gBAAgBC,QAAQ/O,MAC9ByI,KACY,wBAAXA,EAAEpK,QACS,mBAAXoK,EAAEpK,QACS,yBAAXoK,EAAEpK,UAER,KAoBuB2Q,CAAgBJ;IAErC,IAAIC;MACF,OAAO;QACLA;QACAI,YAAYV;QACZlD,UAAU1M,EAAKuQ,QAAQP;;;IAI3B,IAAIQ,MAAMC,QAAQR,EAASS;MACzB,KAAK,IAAIvB,KAAUc,EAASS,SAAS;QACnC,IAA6B,YAAzB1Q,EAAKiG,QAAQkJ;UAAqBA,KAAU;;QAChD;UACE,IAAMI,UAAqBL,cAAcC,GAAQnP,EAAKuQ,QAAQP;UAC9D,IAAIT;YAAc,aAAazJ,KAAKyJ;;AACtC,UAAE,OAAO/H,IAAS;AACpB;WACK,IAAIyI,EAASS;MAClB;QACE,IAAMnB,UAAqBL,cAAce,EAASS,SAAS1Q,EAAKuQ,QAAQP;QACxE,IAAIT;UAAc,aAAazJ,KAAKyJ;;AACtC,QAAE,OAAO/H,IAAS;;IAGpB,MAAM,IAAI6F,UACR,qDAAqDR,cAAcmD;AACpE;EAGH,aAAalK,KAAKkK;AAAiB;;ACzGrC,SAASW,YAAYC,GAAwBC;EAC3C,OAAOD,EAAKlR,OAAOmR,EAAKnR,QAAQ,IAAIkR,EAAKlR,OAAOmR,EAAKnR,OAAO,IAAI;AAClE;;AAOA,SAASoR,WAAWC;EAClB,QAAQA,EAASxN;GACf,KAAK;GAKL,KAAK;IACH,OAAO;MACLA,MAAMwN,EAASxN;MACfyN,QAAQF,WAAWC,EAASC;;;GAEhC,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;GACL,KAAK;IACH,OAAO;MACLzN,MAAMwN,EAASxN;MACf7D,MAAMqR,EAASrR;;;AAGvB;;AAEA,SAASuR,aAAapN;EACpB,OAAO;IACLnE,MAAMmE,EAAMnE;IACZwR,gBAAgBrN,EAAMqN;IACtBC,wBAAmB5E;;AAEvB;;AAEA,SAAS6E,cAAcvN;EACrB,OAAO;IACLnE,MAAMmE,EAAMnE;IACZuC,MAAM6O,WAAWjN,EAAM5B;IACvBoP,cAAcxN,EAAMwN,qBAAgB9E;;AAExC;;AAEA,SAAS+E,SAASpQ;EAChB,OAAO;IACLxB,MAAMwB,EAAMxB;IACZuC,MAAM6O,WAAW5P,EAAMe;IACvBX,MAAMJ,EAAMI,OAAOJ,EAAMI,KAAKjB,IAAI+Q,eAAeG,KAAKZ,eAAe;IACrEO,gBAAgBhQ,EAAMgQ;IACtBC,wBAAmB5E;;AAEvB;;AAEA,SAASiF,gBAAqDC;EAC5D,OAAO;IACLlO,MAAMkO,EAAIlO;IACV7D,MAAM+R,EAAI/R;;AAEd;;AAEA,SAASgS,wBAAwBzP;EAC/B,QAAQA,EAAKsB;GACX,KAAK;IACH,OAAO;MACLA,MAAM;MACN7D,MAAMuC,EAAKvC;;;GAGf,KAAK;IACH,OAAO;MACL6D,MAAM;MACN7D,MAAMuC,EAAKvC;MACXiS,YAAY1P,EAAK0P,WAAWtR,IAAI4Q;;;GAGpC,KAAK;IACH,OAAO;MACL1N,MAAM;MACN7D,MAAMuC,EAAKvC;MACXkS,aAAa3P,EAAK2P,YAAYvR,IAAI+Q;MAClCS,SAAU5P,EAAa4P,YAAW;;;GAItC,KAAK;IACH,OAAO;MACLtO,MAAM;MACN7D,MAAMuC,EAAKvC;MACXsB,QAAQiB,EAAKjB,SAASiB,EAAKjB,OAAOX,IAAIiR,UAAUC,KAAKZ,eAAe;MACpEmB,YAAY7P,EAAK6P,aAAa7P,EAAK6P,WAAWzR,IAAImR,iBAAiBD,KAAKZ,eAAe;;;GAG3F,KAAK;IACH,OAAO;MACLpN,MAAM;MACN7D,MAAMuC,EAAKvC;MACXsB,QAAQiB,EAAKjB,SAASiB,EAAKjB,OAAOX,IAAIiR,UAAUC,KAAKZ,eAAe;MACpEmB,YAAY7P,EAAK6P,aAAa7P,EAAK6P,WAAWzR,IAAImR,iBAAiBD,KAAKZ,eAAe;MACvFoB,eAAe9P,EAAK8P,gBAChB9P,EAAK8P,cAAc1R,IAAImR,iBAAiBD,KAAKZ,eAC7C;;;GAGR,KAAK;IACH,OAAO;MACLpN,MAAM;MACN7D,MAAMuC,EAAKvC;MACXqS,eAAe9P,EAAK8P,gBAChB9P,EAAK8P,cAAc1R,IAAImR,iBAAiBD,KAAKZ,eAC7C;;;AAGZ;;ACzHA,IAAMqB,YAAaxE,KAA8CA,IAAQ,IAAIA,OAAW;;AAExF,IAAMyE,eAAgBC;EACpB,IAAqB,eAAjBA,EAAQ3O;IACV,OAAO,4CAA4C0O,aAAaC,EAAQlB;SACnE,IAAqB,WAAjBkB,EAAQ3O;IACjB,OAAO,wCAAwC0O,aAAaC,EAAQlB;;IAEpE,OAAO,WAAWgB,UAAUE,EAAQ3O,gBAAgByO,UAAUE,EAAQxS;;AACxE;;AAeF,IAAMyS,kBACJC;EAEA,KAAKA,EAAOlG;IAAQ,OAAO;;EAC3B,IAAI8B,IAAS;EACb,KAAK,IAAMnK,KAASuO,GAAQ;IAC1B,IAAIpE;MAAQA,KAAU;;IACtBA,KAAUgE,UAAUnO,EAAMnE;AAC5B;EACA,OAAOsO;AAAM;;AAGf,IAAMqE,cAAerR;EACnB,IAAIgN,IAAS;EACb,KAAK,IAAM9M,KAASF,GAAQ;IAC1B,IAAMtB,IAAOsS,UAAU9Q,EAAMxB;IAC7B,IAAMuC,IAAOgQ,aAAa/Q,EAAMe;IAChC+L,KAAU,GAAGgE,UAAU9Q,EAAMxB,kBAAkBA,YAAeuC;AAChE;EACA,OAAO,KAAK+L;AAAS;;AAGhB,IAAMsE,yBAA0BrQ;EACrC,IAAkB,WAAdA,EAAKsB,MAAiB;IACxB,IAAM6O,IAASD,gBAAgBlQ,EAAK0P;IACpC,OAAO,WAAWK,UAAU/P,EAAKvC,sBAAsB0S;AACzD,SAAO,IAAkB,mBAAdnQ,EAAKsB,MAAyB;IACvC,IAAMvC,IAvCgB4Q;MACxB,IAAI5D,IAAS;MACb,KAAK,IAAMuE,KAAcX,GAAa;QACpC,IAAI5D;UAAQA,KAAU;;QAItBA,KAAU,WAHGgE,UAAUO,EAAW7S,gBACrBuS,aAAaM,EAAWtQ,wBAChBsQ,EAAWlB,eAAe9K,KAAKiM,UAAUD,EAAWlB,gBAAgB;AAE3F;MACA,OAAO,IAAIrD;AAAS,MA8BHyE,CAAiBxQ,EAAK2P;IACrC,OAAO,iCAAiCI,UAAU/P,EAAKvC,mBACpDuC,EAAa4P,YAAW,mBACT7Q;AACpB,SAAO,IAAkB,aAAdiB,EAAKsB,MAAmB;IACjC,IAAMvC,IAASqR,YAAYpQ,EAAKjB;IAChC,OAAO,2BAA2BgR,UAAU/P,EAAKvC,kBAAkBsB;AACrE,SAAO,IAAkB,gBAAdiB,EAAKsB,MAAsB;IAIpC,OAAO,8BAHMyO,UAAU/P,EAAKvC,kBACb2S,YAAYpQ,EAAKjB,2BACVmR,gBAAgBlQ,EAAK8P;AAE7C,SAAO,IAAkB,YAAd9P,EAAKsB,MAAkB;IAGhC,OAAO,0BAFMyO,UAAU/P,EAAKvC,qCACNyS,gBAAgBlQ,EAAK8P;AAE7C;IACE,OAAO;;AACT;;AAGK,SAASW,6BACdpM;EAEA,IAAIqM,IAAiB;EACrB,KAAK,IAAM1Q,KAAQqE,EAAclE,SAASC,OAAO;IAC/C,IAAMuQ,IAAUN,uBAAuBrQ;IACvC,IAAI0Q;MAAgBA,KAAkB;;IACtCA,KAAkB,OAAOX,UAAU/P,EAAKvC,UAAUkT;AACpD;EACA,OAAO,MAAMD;AACf;;AAEO,SAASE,wBACdvM,GACAwM,IAAWJ,6BAA6BpM;EAExC,KAAQlE,UAAUI,KAAW8D;EAC7B,IAAM5G,IAAO,UAAU4G,IAAgBA,EAAc5G,YAAO6M;EAC5D,IAAMwG,IAAYf,UAAUxP,EAAOwQ,UAAUtT;EAC7C,IAAMuT,IAAejB,UAAUxP,EAAO0Q,gBAAgB1Q,EAAO0Q,aAAaxT;EAC1E,IAAMyT,IAAmBnB,UAAUxP,EAAO4Q,oBAAoB5Q,EAAO4Q,iBAAiB1T;EACtF,OAEE,cAAWsS,UAAUtS,iBACTqT,mBACGE,uBACIE,gBACPL;AAEhB;;ACjHA,IAAMO,IAAkB,EAAC,wBAAwB,0BAAyBC,KAAK,QAAQ;;AAEvF,IAAMC,IAAiB,EACrB,4DACA,MACA,eACA,4EACA,gGACA,8FACA,qDACA,QACAD,KAAK;;AAEP,IAAME,IAAgB,EACpB,4DACA,MACA,eACA,4EACA,6EACA,+DACA,MACA,eACA,UACA,kDACA,4DACA,MACA,+CACA,6CACA,mBACA,4BACA,qBACA,WACA,YACA,UACA,QACAF,KAAK;;AC5BP,IAAMG,IAAY;;AAElB,IAAMC,gBAAiBlG,KACJ,mBAAVA,IAAqBA,IAAQjH,KAAKiM,UAAUhF,GAAO,MAAM;;;;;;;;iCL8L5BmG,CACpCjO,GACAhG;EAEA,IAAIA,KAAQ,UAAUgG,KAAUA,EAAOhG,SAASA;IAC9C,OAAOgG;SACF,KAAKhG,OAAU,aAAagG;IACjC,OAAOA;SACF,IAAIhG,KAAQ,aAAagG,GAAQ;IACtC,KAAK,IAAIkO,IAAQ,GAAGA,IAAQlO,EAAOmO,QAAQ3H,QAAQ0H;MACjD,IAAIlO,EAAOmO,QAAQD,GAAOlU,SAASA;QAAM,OAAOgG,EAAOmO,QAAQD;;;IACjE,OAAO;AACT;IACE,OAAO;;AACT;;mCArBuClO,KAChC,IAAIE,IAAmB,KACxB,YAAYF,IAAS,EAAC,SAAQ,OAC9B,aAAaA,IAASA,EAAOmO,QAAQxT,KAAKmN,KAAUA,EAAM9N,SAAQ;;;;;;;;;;;;kBH3InE,SAASoU,QACdtG;EAEA,IAAMuG,IAA4B;EAElC,IAAIC;EACJ,IAAMC,aAAcvO;IAClB,KAAKsO,GAAU;MACbA,KAAa,aAAaxG,KAASA,EAAMqG,WAAY,IAAIxT,KAAKmN,MAAW;QACvEA;QACA0G,QAAQpO,KAAK;aAAKJ;UAAQ2G,QAAQmB,EAAMhL;UAAQ9C,MAAM8N,EAAM9N;;;MAE9D,IAAI,YAAY8N,KAASA,EAAMhL;QAC7BwR,EAASG,KAAK;UACZ3G,OAAO;eAAKA;YAAO9N,WAAM6M;;UACzB2H,QAAQpO,KAAK;eAAKJ;YAAQ2G,QAAQmB,EAAMhL;;;;AAG9C;IACA,OAAOwR;AAAQ;EAGjB,IAAMvC,IAAiB;IACrB2C,SAAS;IACTC,SAAS;IAETC,QAAS,aAAa9G,KAASA,EAAMqG,WAAY,IAAIU,QAAO,CAACC,IAAO9U;MAClE,IAAIA;QAAM8U,EAAI9U,KAAQ;;MACtB,OAAO8U;AAAG,QACT;IAEHC,UAAAA,CAAW/O,GAAwBuB;MACjC,IAAMyN,IAAUT,WAAWvO;MAC3BqO,EAAUI,QACLO,EAAQrU,KAAI,EAAGmN,UAAO0G;QACvBA,EACGpO,OACAwI,MAAMxL;UACL2O,EAAI2C;UACJ,IAAI5G,EAAM9N;YACR+R,EAAI6C,MAAM9G,EAAM9N,QAAQ;iBAAK8N;iBAAU1K;;;YAEvC2O,EAAI4C,UAAU;iBAAK7G;iBAAU1K;;;AAC/B,YAEDyL,OAAO/G;QAGV,OAAO0M,EAAOlN,gBAAgBlE;UAC5B2O,EAAI2C;UACJ,IAAI5G,EAAM9N;YACR+R,EAAI6C,MAAM9G,EAAM9N,QAAQ;iBAAK8N;iBAAU1K;;;YAEvC2O,EAAI4C,UAAU;iBAAK7G;iBAAU1K;;;UAE/BmE,EAASwK,GAAKjE;AAAM;AACpB;MAGN,OAAO;QACL,IAAImH;QACJ,OAAuC,SAA/BA,IAAWZ,EAAUa;UAAgBD;;AAAU;AAE1D;IACD,UAAM7O,CAAKJ;MACT,IAAMgP,IAAUT,WAAWvO;YACrBmP,QAAQC,IACZJ,EAAQrU,KAAI0F,QAASyH,UAAO0G;QAC1B,IAAMpR,UAAeoR,EAAOpO;QAC5B2L,EAAI2C;QACJ,IAAI5G,EAAM9N;UACR+R,EAAI6C,MAAM9G,EAAM9N,QAAQ;eAAK8N;eAAU1K;;;UAEvC2O,EAAI4C,UAAU;eAAK7G;eAAU1K;;;AAC/B;MAGJ,OAAO2O;AACT;;EAGF,OAAOA;AACT;;8BKgBEjP;EAEA,KAAKA,OAAY,cAAcA;IAC7B,MAAM,IAAIuS,UAAU;;EAGtB,KACE3S,WAAU4Q,WAAEA,GAASE,cAAEA,GAAYE,kBAAEA,GAAgB/Q,OAAEA,MACrDG;EAEJ,IAAMwS,IAAgB3S,EACnB4S,QAAQhT;IACP,QAAQA,EAAKvC;KACX,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;MACH,QAAO;;KACT;MACE,OACgB,aAAduC,EAAKsB,QACS,WAAdtB,EAAKsB,QACS,mBAAdtB,EAAKsB,QACS,aAAdtB,EAAKsB,QACS,gBAAdtB,EAAKsB,QACS,YAAdtB,EAAKsB;;AAEX,MAEDlD,IAAIqR,yBACJH,KAAKZ;EAER,OAAO;IACLjR,MAAM,UAAU8C,IAASA,EAAO9C,YAAO6M;IACvCnK,UAAU;MACR4Q,WAAW;QACTzP,MAAMyP,EAAUzP;QAChB7D,MAAMsT,EAAUtT;;MAElBwT,cAAcA,IACV;QACE3P,MAAM2P,EAAa3P;QACnB7D,MAAMwT,EAAaxT;UAErB;MACJ0T,kBAAkBA,IACd;QACE7P,MAAM6P,EAAiB7P;QACvB7D,MAAM0T,EAAiB1T;UAEzB;MACJ2C,OAAO2S;MACPE,YAAY;;;AAEf;;kCGpMI,SAASC,wBACd7O,GACA8O;EAEA,IAAI,WAAWC,KAAKD,EAAKE,WAAW;IAClC,IAAMC,IAAM,EAAClC;IACb,IAA6B,mBAAlB/M,KAA8B8O,EAAKI;MAG5CD,EAAIpB,KACF,eAAeV,OAAef,6BAA6BpM,SAC3DiN,GACA,+BAA+BV,wBAAwBvM,GAAemN,SACtE;;MAGF8B,EAAIpB,KACFZ,GACA,+BAA+BG,cAAcpN,SAC7C;;IAMJ,MAAM,UAAUA,OAAmBA,EAAc5G;MAC/C6V,EAAIpB,KACF,+BACA,6BACA,oCACA,OACA;;IAGJ,OAAOoB,EAAIjC,KAAK;AACjB,SAAM,IAAI,QAAQ+B,KAAKD,EAAKE,WAAW;IACtC,IAAMG,IAAO/B,cAAcpN;IAC3B,OAAO,EACL+M,GACAG,GACA,yBAAyBiC,iBACzB,8BACAnC,KAAK;AACT;EAEA,MAAM,IAAIjG,UACR,0CAA0C+H,EAAKE;AAEnD;;sBLyD2BI,CACzBlI,GAOAd,IAAmBjC,QAAQmC;EAE3B,IAAa,QAATY,KAAkC,mBAAVA;IAC1B,MAAM,IAAIH,UAAU,kDAAkDG;SACjE,IAAI,cAAcA,KAASA,EAAMmI,YAAsC,mBAAnBnI,EAAMmI;IAC/D,MAAM,IAAItI,UAAU;SACf,IAAI,UAAUG,KAASA,EAAM9N,QAA8B,mBAAf8N,EAAM9N;IACvD,MAAM,IAAI2N,UAAU;;EAGtB,IAAI,aAAaG,GAAO;IACtB,KAAKgD,MAAMC,QAAQjD,EAAMqG;MACvB,MAAM,IAAIxG,UAAU;;IAGtB,IAAI,YAAYG;MACd,MAAM,IAAIH,UACR;WAEG,IAAI,wBAAwBG;MACjC,MAAM,IAAIH,UACR;WAEG,IAAI,uBAAuBG;MAChC,MAAM,IAAIH,UACR;WAEG,IAAI,2BAA2BG;MACpC,MAAM,IAAIH,UACR;;IAIJ,IAAMwG,IAAUrG,EAAMqG,QAAQxT,KAAKmC;MACjC,MAAM,UAAUA,OAAYA,EAAO9C,QAA+B,mBAAhB8C,EAAO9C;QACvD,MAAM,IAAI2N,UAAU;;MACtB,MACI,wBAAwB7K,OACzBA,EAAOqL,sBAC6B,mBAA9BrL,EAAOqL;QAEd,MAAM,IAAIR,UACR;;MAEJ,OAAO;WACFE,kBAAkB/K,GAAQkK;QAC7BhN,MAAM8C,EAAO9C;;AACd;IACA,IAAAkW,QAAA,SAAAC;MAGD,IAAMzD,IAASyB,EAAQxT,KAAKmC,KAAWA,EAAOqT,KAAOZ,OAAOa;MAC5D,IAAMC,IAAe,IAAInQ,IAAIwM;MAC7B,IAAIA,EAAOlG,WAAW6J,EAAa7O;QACjC,MAAM,IAAImG,UAAU,QAAQwI;;;IAJhC,KAAK,IAAMA,KAAQvI;MAAYsI,MAAAC;;IAO/B,OAAO;SAAKrI;MAAOqG;;AACrB;IACE,OAAO;SAAKrG;SAAUD,kBAAkBC,GAAOd;;;AACjD;;;;;;mCCnEsC3G;EAGtC;IACE,IAAMjD,UAAeiN,WAAWR;IAChC,OAAOvP,EAAKuQ,QAAQzN,EAAOwN;AAC5B,IAAC,OAAO9I;IACP;AACF;AAAA", "x_google_ignoreList": [0, 3]}