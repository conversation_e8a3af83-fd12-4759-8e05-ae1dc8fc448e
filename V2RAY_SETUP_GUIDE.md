# V2Ray IP轮换器配置指南

## 🚀 快速开始

### 1. 安装V2Ray

**方法一：使用安装脚本（推荐）**
```bash
.\install_v2ray.bat
```

**方法二：手动安装**
1. 访问 https://github.com/v2fly/v2ray-core/releases/latest
2. 下载 `v2ray-windows-64.zip`
3. 解压到 `tools/v2ray/` 目录
4. 确保目录结构如下：
   ```
   tools/v2ray/
   ├── v2ray.exe
   ├── v2ctl.exe
   └── config.json
   ```

### 2. 启动IP轮换

**快速启动（推荐）**
```bash
.\start_v2ray_rotation.bat
```

**手动启动**
```bash
# 每60秒切换一次
python v2ray_ip_rotator.py start 60

# 每30秒切换一次
python v2ray_ip_rotator.py start 30
```

## 🔧 详细配置

### V2Ray配置说明

V2Ray轮换器会自动生成配置文件，包含以下设置：

**本地端口：**
- HTTP代理：`127.0.0.1:7890`
- SOCKS代理：`127.0.0.1:7891`

**支持的代理类型：**
- ✅ Shadowsocks (ss)
- 🔄 VMess (计划支持)
- 🔄 Trojan (计划支持)

### 系统代理设置

轮换器会自动设置Windows系统代理：
- 启用代理：指向 `127.0.0.1:7890`
- 停止时自动禁用系统代理

## 📋 使用命令

### 基本命令

```bash
# 启动自动轮换（每60秒）
python v2ray_ip_rotator.py start 60

# 手动切换到香港节点
python v2ray_ip_rotator.py switch 香港

# 列出所有代理
python v2ray_ip_rotator.py list

# 列出香港地区代理
python v2ray_ip_rotator.py list 香港

# 查看状态
python v2ray_ip_rotator.py status

# 测试当前代理
python v2ray_ip_rotator.py test

# 停止代理
python v2ray_ip_rotator.py stop
```

### 高级用法

```bash
# 每30秒快速轮换
python v2ray_ip_rotator.py start 30

# 每5分钟轮换（300秒）
python v2ray_ip_rotator.py start 300

# 切换到特定节点
python v2ray_ip_rotator.py switch "香港7(推荐)"
python v2ray_ip_rotator.py switch "日本5"
python v2ray_ip_rotator.py switch "新加坡1"
```

## 🔍 状态监控

### 查看运行状态
```bash
python v2ray_ip_rotator.py status
```

**输出示例：**
```
📊 V2Ray IP轮换器状态
------------------------------
运行状态: 🟢 运行中
V2Ray状态: 🟢 运行中
当前代理: 香港7(推荐) 华东入口 [家宽] x1.2
代理服务器: *************:39597
可用节点: 65个
切换次数: 15
运行时长: 0:15:23
本地端口: HTTP 7890, SOCKS 7891
```

### 测试代理连接
```bash
python v2ray_ip_rotator.py test
```

## 🌍 支持的地区

| 地区 | 节点数量 | 特点 |
|------|----------|------|
| 🇭🇰 香港 | 12个 | 低延迟，推荐 |
| 🇹🇼 台湾 | 11个 | 稳定连接 |
| 🇯🇵 日本 | 17个 | 原生IP |
| 🇸🇬 新加坡 | 12个 | 东南亚优选 |
| 🇺🇸 美国 | 7个 | 全球服务 |
| 🇰🇷 韩国 | 6个 | 高速连接 |

## 🔧 故障排除

### 常见问题

**1. V2Ray启动失败**
```bash
# 检查V2Ray是否正确安装
tools\v2ray\v2ray.exe version

# 重新安装V2Ray
.\install_v2ray.bat
```

**2. 代理连接失败**
```bash
# 测试当前代理
python v2ray_ip_rotator.py test

# 切换到其他节点
python v2ray_ip_rotator.py switch 香港
```

**3. 系统代理设置失败**
- 确保以管理员权限运行
- 检查Windows防火墙设置

**4. 端口被占用**
```bash
# 检查端口占用
netstat -ano | findstr :7890
netstat -ano | findstr :7891

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 日志文件

- 日志位置：`logs/v2ray_ip_rotator.log`
- 包含详细的运行日志和错误信息

## 🔒 安全说明

### 注意事项

1. **合法使用** - 请遵守当地法律法规
2. **网络安全** - 不要在公共网络环境下使用
3. **密码保护** - 配置文件中的密码已加密存储
4. **定期更新** - 建议定期更新代理节点配置

### 隐私保护

- V2Ray配置文件存储在临时目录
- 程序退出时自动清理临时文件
- 不记录用户访问的网站信息

## 📈 性能优化

### 推荐设置

**日常使用：**
```bash
python v2ray_ip_rotator.py start 300  # 5分钟轮换
```

**高频使用：**
```bash
python v2ray_ip_rotator.py start 60   # 1分钟轮换
```

**测试环境：**
```bash
python v2ray_ip_rotator.py start 30   # 30秒轮换
```

### 地区选择建议

- **低延迟**：香港、台湾
- **稳定性**：日本、新加坡
- **全球服务**：美国
- **特殊需求**：韩国

## 🆘 技术支持

如遇到问题，请检查：

1. **Python环境** - 确保Python 3.7+已安装
2. **V2Ray安装** - 确保V2Ray正确安装在tools/v2ray/目录
3. **网络连接** - 确保网络连接正常
4. **系统权限** - 确保有管理员权限修改系统代理
5. **防火墙设置** - 确保V2Ray端口未被阻止

---

**🎉 现在您可以享受真正的IP轮换代理服务了！**
