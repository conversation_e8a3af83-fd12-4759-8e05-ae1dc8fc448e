"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.InjectiveReferralRPCGetReferrerByCodeDesc = exports.InjectiveReferralRPCGetInviteeDetailsDesc = exports.InjectiveReferralRPCGetReferrerDetailsDesc = exports.InjectiveReferralRPCDesc = exports.InjectiveReferralRPCClientImpl = exports.GetReferrerByCodeResponse = exports.GetReferrerByCodeRequest = exports.GetInviteeDetailsResponse = exports.GetInviteeDetailsRequest = exports.ReferralInvitee = exports.GetReferrerDetailsResponse = exports.GetReferrerDetailsRequest = exports.protobufPackage = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "injective_referral_rpc";
function createBaseGetReferrerDetailsRequest() {
    return { referrerAddress: "" };
}
exports.GetReferrerDetailsRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.referrerAddress !== "") {
            writer.uint32(10).string(message.referrerAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerDetailsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referrerAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { referrerAddress: isSet(object.referrerAddress) ? String(object.referrerAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.referrerAddress !== undefined && (obj.referrerAddress = message.referrerAddress);
        return obj;
    },
    create(base) {
        return exports.GetReferrerDetailsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseGetReferrerDetailsRequest();
        message.referrerAddress = (_a = object.referrerAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseGetReferrerDetailsResponse() {
    return { invitees: [], totalCommission: "", totalTradingVolume: "", referrerCode: "" };
}
exports.GetReferrerDetailsResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.invitees) {
            exports.ReferralInvitee.encode(v, writer.uint32(10).fork()).ldelim();
        }
        if (message.totalCommission !== "") {
            writer.uint32(18).string(message.totalCommission);
        }
        if (message.totalTradingVolume !== "") {
            writer.uint32(26).string(message.totalTradingVolume);
        }
        if (message.referrerCode !== "") {
            writer.uint32(34).string(message.referrerCode);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerDetailsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.invitees.push(exports.ReferralInvitee.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.totalCommission = reader.string();
                    break;
                case 3:
                    message.totalTradingVolume = reader.string();
                    break;
                case 4:
                    message.referrerCode = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            invitees: Array.isArray(object === null || object === void 0 ? void 0 : object.invitees) ? object.invitees.map((e) => exports.ReferralInvitee.fromJSON(e)) : [],
            totalCommission: isSet(object.totalCommission) ? String(object.totalCommission) : "",
            totalTradingVolume: isSet(object.totalTradingVolume) ? String(object.totalTradingVolume) : "",
            referrerCode: isSet(object.referrerCode) ? String(object.referrerCode) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.invitees) {
            obj.invitees = message.invitees.map((e) => e ? exports.ReferralInvitee.toJSON(e) : undefined);
        }
        else {
            obj.invitees = [];
        }
        message.totalCommission !== undefined && (obj.totalCommission = message.totalCommission);
        message.totalTradingVolume !== undefined && (obj.totalTradingVolume = message.totalTradingVolume);
        message.referrerCode !== undefined && (obj.referrerCode = message.referrerCode);
        return obj;
    },
    create(base) {
        return exports.GetReferrerDetailsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseGetReferrerDetailsResponse();
        message.invitees = ((_a = object.invitees) === null || _a === void 0 ? void 0 : _a.map((e) => exports.ReferralInvitee.fromPartial(e))) || [];
        message.totalCommission = (_b = object.totalCommission) !== null && _b !== void 0 ? _b : "";
        message.totalTradingVolume = (_c = object.totalTradingVolume) !== null && _c !== void 0 ? _c : "";
        message.referrerCode = (_d = object.referrerCode) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseReferralInvitee() {
    return { address: "", commission: "", tradingVolume: "", joinDate: "" };
}
exports.ReferralInvitee = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.commission !== "") {
            writer.uint32(18).string(message.commission);
        }
        if (message.tradingVolume !== "") {
            writer.uint32(26).string(message.tradingVolume);
        }
        if (message.joinDate !== "") {
            writer.uint32(34).string(message.joinDate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseReferralInvitee();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.commission = reader.string();
                    break;
                case 3:
                    message.tradingVolume = reader.string();
                    break;
                case 4:
                    message.joinDate = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            commission: isSet(object.commission) ? String(object.commission) : "",
            tradingVolume: isSet(object.tradingVolume) ? String(object.tradingVolume) : "",
            joinDate: isSet(object.joinDate) ? String(object.joinDate) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.commission !== undefined && (obj.commission = message.commission);
        message.tradingVolume !== undefined && (obj.tradingVolume = message.tradingVolume);
        message.joinDate !== undefined && (obj.joinDate = message.joinDate);
        return obj;
    },
    create(base) {
        return exports.ReferralInvitee.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseReferralInvitee();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        message.commission = (_b = object.commission) !== null && _b !== void 0 ? _b : "";
        message.tradingVolume = (_c = object.tradingVolume) !== null && _c !== void 0 ? _c : "";
        message.joinDate = (_d = object.joinDate) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseGetInviteeDetailsRequest() {
    return { inviteeAddress: "" };
}
exports.GetInviteeDetailsRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.inviteeAddress !== "") {
            writer.uint32(10).string(message.inviteeAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetInviteeDetailsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.inviteeAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { inviteeAddress: isSet(object.inviteeAddress) ? String(object.inviteeAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.inviteeAddress !== undefined && (obj.inviteeAddress = message.inviteeAddress);
        return obj;
    },
    create(base) {
        return exports.GetInviteeDetailsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseGetInviteeDetailsRequest();
        message.inviteeAddress = (_a = object.inviteeAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseGetInviteeDetailsResponse() {
    return { referrer: "", usedCode: "", tradingVolume: "", joinedAt: "", active: false };
}
exports.GetInviteeDetailsResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.referrer !== "") {
            writer.uint32(10).string(message.referrer);
        }
        if (message.usedCode !== "") {
            writer.uint32(18).string(message.usedCode);
        }
        if (message.tradingVolume !== "") {
            writer.uint32(26).string(message.tradingVolume);
        }
        if (message.joinedAt !== "") {
            writer.uint32(34).string(message.joinedAt);
        }
        if (message.active === true) {
            writer.uint32(40).bool(message.active);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetInviteeDetailsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referrer = reader.string();
                    break;
                case 2:
                    message.usedCode = reader.string();
                    break;
                case 3:
                    message.tradingVolume = reader.string();
                    break;
                case 4:
                    message.joinedAt = reader.string();
                    break;
                case 5:
                    message.active = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            referrer: isSet(object.referrer) ? String(object.referrer) : "",
            usedCode: isSet(object.usedCode) ? String(object.usedCode) : "",
            tradingVolume: isSet(object.tradingVolume) ? String(object.tradingVolume) : "",
            joinedAt: isSet(object.joinedAt) ? String(object.joinedAt) : "",
            active: isSet(object.active) ? Boolean(object.active) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.referrer !== undefined && (obj.referrer = message.referrer);
        message.usedCode !== undefined && (obj.usedCode = message.usedCode);
        message.tradingVolume !== undefined && (obj.tradingVolume = message.tradingVolume);
        message.joinedAt !== undefined && (obj.joinedAt = message.joinedAt);
        message.active !== undefined && (obj.active = message.active);
        return obj;
    },
    create(base) {
        return exports.GetInviteeDetailsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseGetInviteeDetailsResponse();
        message.referrer = (_a = object.referrer) !== null && _a !== void 0 ? _a : "";
        message.usedCode = (_b = object.usedCode) !== null && _b !== void 0 ? _b : "";
        message.tradingVolume = (_c = object.tradingVolume) !== null && _c !== void 0 ? _c : "";
        message.joinedAt = (_d = object.joinedAt) !== null && _d !== void 0 ? _d : "";
        message.active = (_e = object.active) !== null && _e !== void 0 ? _e : false;
        return message;
    },
};
function createBaseGetReferrerByCodeRequest() {
    return { referralCode: "" };
}
exports.GetReferrerByCodeRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.referralCode !== "") {
            writer.uint32(10).string(message.referralCode);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerByCodeRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referralCode = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { referralCode: isSet(object.referralCode) ? String(object.referralCode) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.referralCode !== undefined && (obj.referralCode = message.referralCode);
        return obj;
    },
    create(base) {
        return exports.GetReferrerByCodeRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseGetReferrerByCodeRequest();
        message.referralCode = (_a = object.referralCode) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseGetReferrerByCodeResponse() {
    return { referrerAddress: "" };
}
exports.GetReferrerByCodeResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.referrerAddress !== "") {
            writer.uint32(10).string(message.referrerAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerByCodeResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referrerAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { referrerAddress: isSet(object.referrerAddress) ? String(object.referrerAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.referrerAddress !== undefined && (obj.referrerAddress = message.referrerAddress);
        return obj;
    },
    create(base) {
        return exports.GetReferrerByCodeResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseGetReferrerByCodeResponse();
        message.referrerAddress = (_a = object.referrerAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
class InjectiveReferralRPCClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.GetReferrerDetails = this.GetReferrerDetails.bind(this);
        this.GetInviteeDetails = this.GetInviteeDetails.bind(this);
        this.GetReferrerByCode = this.GetReferrerByCode.bind(this);
    }
    GetReferrerDetails(request, metadata) {
        return this.rpc.unary(exports.InjectiveReferralRPCGetReferrerDetailsDesc, exports.GetReferrerDetailsRequest.fromPartial(request), metadata);
    }
    GetInviteeDetails(request, metadata) {
        return this.rpc.unary(exports.InjectiveReferralRPCGetInviteeDetailsDesc, exports.GetInviteeDetailsRequest.fromPartial(request), metadata);
    }
    GetReferrerByCode(request, metadata) {
        return this.rpc.unary(exports.InjectiveReferralRPCGetReferrerByCodeDesc, exports.GetReferrerByCodeRequest.fromPartial(request), metadata);
    }
}
exports.InjectiveReferralRPCClientImpl = InjectiveReferralRPCClientImpl;
exports.InjectiveReferralRPCDesc = { serviceName: "injective_referral_rpc.InjectiveReferralRPC" };
exports.InjectiveReferralRPCGetReferrerDetailsDesc = {
    methodName: "GetReferrerDetails",
    service: exports.InjectiveReferralRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.GetReferrerDetailsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.GetReferrerDetailsResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveReferralRPCGetInviteeDetailsDesc = {
    methodName: "GetInviteeDetails",
    service: exports.InjectiveReferralRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.GetInviteeDetailsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.GetInviteeDetailsResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveReferralRPCGetReferrerByCodeDesc = {
    methodName: "GetReferrerByCode",
    service: exports.InjectiveReferralRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.GetReferrerByCodeRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.GetReferrerByCodeResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
