(function (shake_256, shake_128) {
  describe('#shake_128', function () {
    context('with 256 output', function () {
      it('should be equal', function () {
        expect(shake_128('', 256)).to.be('7f9c2ba4e88f827d616045507605853ed73b8093f6efbc88eb1a6eacfa66ef26');
        expect(shake_128('The quick brown fox jumps over the lazy dog', 256)).to.be('f4202e3c5852f9182a0430fd8144f0a74b95e7417ecae17db0f8cfeed0e3e66e');
        expect(shake_128('The quick brown fox jumps over the lazy dof', 256)).to.be('853f4538be0db9621a6cea659a06c1107b1f83f02b13d18297bd39d7411cf10c');
      });
    });

    context('with 8 output', function () {
      it('should be equal', function () {
        expect(shake_128('', 8)).to.be('7f');
        expect(shake_128('The quick brown fox jumps over the lazy dog', 8)).to.be('f4');
        expect(shake_128('The quick brown fox jumps over the lazy dof', 8)).to.be('85');
      });
    });

    context('with 1368 output', function () {
      it('should be equal', function () {
        expect(shake_128('AAA', 1368)).to.be('15e0fe495a05b74f9fd3eaa8a898a623488220dcbf9ba2f12d23d278b7cecfa4a5e4b8d0fccb0fdbc9e51cd0b4344a32a83f0ba40a514a7b86a77c854c61b836192849da9214c43c4f8bc09ec7a76af92b2fc56e4952024be65b1a47835e0bc014733b24d0e31197ca648f831caebbfd8a5b237ae6bdc9d6cc803a2c5e57dd9346eecf972bd85450f18a413dc6239982e1eb6e0c6df856385c9597d0320edb40b6fe60a74f07524015ad36');
        expect(shake_128('AAA', 1376)).to.be('15e0fe495a05b74f9fd3eaa8a898a623488220dcbf9ba2f12d23d278b7cecfa4a5e4b8d0fccb0fdbc9e51cd0b4344a32a83f0ba40a514a7b86a77c854c61b836192849da9214c43c4f8bc09ec7a76af92b2fc56e4952024be65b1a47835e0bc014733b24d0e31197ca648f831caebbfd8a5b237ae6bdc9d6cc803a2c5e57dd9346eecf972bd85450f18a413dc6239982e1eb6e0c6df856385c9597d0320edb40b6fe60a74f07524015ad36b6');
      });
    });

    context('with more output', function () {
      it('should be equal', function () {
        expect(shake_128('', 16)).to.be('7f9c');
        expect(shake_128('', 24)).to.be('7f9c2b');
        expect(shake_128.array('', 16)).to.eql([0x7f, 0x9c]);
        expect(shake_128.array('', 24)).to.eql([0x7f, 0x9c, 0x2b]);
      });
    });

    context('with 8 output ArrayBuffer', function () {
      it('should be equal', function () {
        expect(shake_128.buffer('', 8).toHexString()).to.be('7f');
        expect(shake_128.buffer('The quick brown fox jumps over the lazy dog', 8).toHexString()).to.be('f4');
        expect(shake_128.buffer('The quick brown fox jumps over the lazy dof', 8).toHexString()).to.be('85');
      });
    });

    context('#update', function () {
      it('should be equal', function () {
        expect(shake_128.update('', 256).hex()).to.be('7f9c2ba4e88f827d616045507605853ed73b8093f6efbc88eb1a6eacfa66ef26');
        expect(shake_128.update('The quick brown fox ', 256).update('jumps over the lazy dog').hex()).to.be('f4202e3c5852f9182a0430fd8144f0a74b95e7417ecae17db0f8cfeed0e3e66e');
      });
    });
  });

  describe('#shake_256', function () {
    context('with 512 output', function () {
      it('should be equal', function () {
        expect(shake_256('', 512)).to.be('46b9dd2b0ba88d13233b3feb743eeb243fcd52ea62b81b82b50c27646ed5762fd75dc4ddd8c0f200cb05019d67b592f6fc821c49479ab48640292eacb3b7c4be');
      });
    });
    context('with 8 output', function () {
      it('should be equal', function () {
        expect(shake_256('', 8)).to.be('46');
      });
    });

    context('with 1112 output', function () {
      it('should be equal', function () {
        expect(shake_256('AAA', 1112)).to.be('419614c8b247ee5e9f4a540f7aaa5ca5b44b119f47ab7f494c05095ae5a61ab6b62c84b8b27888813ce8a4d4dab3ed7617c6bab643aa01bb1b113e6d48c3e1eeb73e96f96ffaf12e0c36b190404982b856087acfcb467535e17152e5c15a4d62a18a15d8fe434b3a7274362b0d46b627df1e011a1d037e161d5b540df7ebadab351fb730904daa9a4f40fd');
        expect(shake_256('AAA', 1120)).to.be('419614c8b247ee5e9f4a540f7aaa5ca5b44b119f47ab7f494c05095ae5a61ab6b62c84b8b27888813ce8a4d4dab3ed7617c6bab643aa01bb1b113e6d48c3e1eeb73e96f96ffaf12e0c36b190404982b856087acfcb467535e17152e5c15a4d62a18a15d8fe434b3a7274362b0d46b627df1e011a1d037e161d5b540df7ebadab351fb730904daa9a4f40fdb5');
      });
    });

    context('with 4100 output', function () {
      it('should be equal', function () {
        // https://raw.githubusercontent.com/gvanas/KeccakCodePackage/master/TestVectors/ShortMsgKAT_SHAKE256.txt
        // Len = 0, Msg = 00
        expect(shake_256('', 4100)).to.be('46b9dd2b0ba88d13233b3feb743eeb243fcd52ea62b81b82b50c27646ed5762fd75dc4ddd8c0f200cb05019d67b592f6fc821c49479ab48640292eacb3b7c4be141e96616fb13957692cc7edd0b45ae3dc07223c8e92937bef84bc0eab862853349ec75546f58fb7c2775c38462c5010d846c185c15111e595522a6bcd16cf86f3d122109e3b1fdd943b6aec468a2d621a7c06c6a957c62b54dafc3be87567d677231395f6147293b68ceab7a9e0c58d864e8efde4e1b9a46cbe854713672f5caaae314ed9083dab4b099f8e300f01b8650f1f4b1d8fcf3f3cb53fb8e9eb2ea203bdc970f50ae55428a91f7f53ac266b28419c3778a15fd248d339ede785fb7f5a1aaa96d313eacc890936c173cdcd0fab882c45755feb3aed96d477ff96390bf9a66d1368b208e21f7c10d04a3dbd4e360633e5db4b602601c14cea737db3dcf722632cc77851cbdde2aaf0a33a07b373445df490cc8fc1e4160ff118378f11f0477de055a81a9eda57a4a2cfb0c83929d310912f729ec6cfa36c6ac6a75837143045d791cc85eff5b21932f23861bcf23a52b5da67eaf7baae0f5fb1369db78f3ac45f8c4ac5671d85735cdddb09d2b1e34a1fc066ff4a162cb263d6541274ae2fcc865f618abe27c124cd8b074ccd516301b91875824d09958f341ef274bdab0bae316339894304e35877b0c28a9b1fd166c796b9cc258a064a8f57e27f2a');

        // Len = 2040
        // Msg = 3A3A819C48EFDE2AD914FBF00E18AB6BC4F14513AB27D0C178A188B61431E7F5623CB66B23346775D386B50E982C493ADBBFC54B9A3CD383382336A1A0B2150A15358F336D03AE18F666C7573D55C4FD181C29E6CCFDE63EA35F0ADF5885CFC0A3D84A2B2E4DD24496DB789E663170CEF74798AA1BBCD4574EA0BBA40489D764B2F83AADC66B148B4A0CD95246C127D5871C4F11418690A5DDF01246A0C80A43C70088B6183639DCFDA4125BD113A8F49EE23ED306FAAC576C3FB0C1E256671D817FC2534A52F5B439F72E424DE376F4C565CCA82307DD9EF76DA5B7C4EB7E085172E328807C02D011FFBF33785378D79DC266F6A5BE6BB0E4A92ECEEBAEB1
        expect(shake_256([0x3A,0x3A,0x81,0x9C,0x48,0xEF,0xDE,0x2A,0xD9,0x14,0xFB,0xF0,0x0E,0x18,0xAB,0x6B,0xC4,0xF1,0x45,0x13,0xAB,0x27,0xD0,0xC1,0x78,0xA1,0x88,0xB6,0x14,0x31,0xE7,0xF5,0x62,0x3C,0xB6,0x6B,0x23,0x34,0x67,0x75,0xD3,0x86,0xB5,0x0E,0x98,0x2C,0x49,0x3A,0xDB,0xBF,0xC5,0x4B,0x9A,0x3C,0xD3,0x83,0x38,0x23,0x36,0xA1,0xA0,0xB2,0x15,0x0A,0x15,0x35,0x8F,0x33,0x6D,0x03,0xAE,0x18,0xF6,0x66,0xC7,0x57,0x3D,0x55,0xC4,0xFD,0x18,0x1C,0x29,0xE6,0xCC,0xFD,0xE6,0x3E,0xA3,0x5F,0x0A,0xDF,0x58,0x85,0xCF,0xC0,0xA3,0xD8,0x4A,0x2B,0x2E,0x4D,0xD2,0x44,0x96,0xDB,0x78,0x9E,0x66,0x31,0x70,0xCE,0xF7,0x47,0x98,0xAA,0x1B,0xBC,0xD4,0x57,0x4E,0xA0,0xBB,0xA4,0x04,0x89,0xD7,0x64,0xB2,0xF8,0x3A,0xAD,0xC6,0x6B,0x14,0x8B,0x4A,0x0C,0xD9,0x52,0x46,0xC1,0x27,0xD5,0x87,0x1C,0x4F,0x11,0x41,0x86,0x90,0xA5,0xDD,0xF0,0x12,0x46,0xA0,0xC8,0x0A,0x43,0xC7,0x00,0x88,0xB6,0x18,0x36,0x39,0xDC,0xFD,0xA4,0x12,0x5B,0xD1,0x13,0xA8,0xF4,0x9E,0xE2,0x3E,0xD3,0x06,0xFA,0xAC,0x57,0x6C,0x3F,0xB0,0xC1,0xE2,0x56,0x67,0x1D,0x81,0x7F,0xC2,0x53,0x4A,0x52,0xF5,0xB4,0x39,0xF7,0x2E,0x42,0x4D,0xE3,0x76,0xF4,0xC5,0x65,0xCC,0xA8,0x23,0x07,0xDD,0x9E,0xF7,0x6D,0xA5,0xB7,0xC4,0xEB,0x7E,0x08,0x51,0x72,0xE3,0x28,0x80,0x7C,0x02,0xD0,0x11,0xFF,0xBF,0x33,0x78,0x53,0x78,0xD7,0x9D,0xC2,0x66,0xF6,0xA5,0xBE,0x6B,0xB0,0xE4,0xA9,0x2E,0xCE,0xEB,0xAE,0xB1], 4100)).to.be('8a5199b4a7e133e264a86202720655894d48cff344a928cf8347f48379cef347dfc5bcffab99b27b1f89aa2735e23d30088ffa03b9edb02b9635470ab9f1038985d55f9ca774572dd006470ea65145469609f9fa0831bf1ffd842dc24acade27bd9816e3b5bf2876cb112232a0eb4475f1dff9f5c713d9ffd4ccb89ae5607fe35731df06317949eef646e9591cf3be53add6b7dd2b6096e2b3fb06e662ec8b2d77422daad9463cd155204acdbd38e319613f39f99b6dfb35ca9365160066db19835888c2241ff9a731a4acbb5663727aac34a401247fbaa7499e7d5ee5b69d31025e63d04c35c798bca1262d5673a9cf0930b5ad89bd485599dc184528da4790f088ebd170b635d9581632d2ff90db79665ced430089af13c9f21f6d443a818064f17aec9e9c5457001fa8dc6afbadbe3138f388d89d0e6f22f66671255b210754ed63d81dce75ce8f189b534e6d6b3539aa51e837c42df9df59c71e6171cd4902fe1bdc73fb1775b5c754a1ed4ea7f3105fc543ee0418dad256f3f6118ea77114a16c15355b42877a1db2a7df0e155ae1d8670abcec3450f4e2eec9838f895423ef63d261138baaf5d9f104cb5a957aea06c0b9b8c78b0d441796dc0350ddeabb78a33b6f1f9e68ede3d1805c7b7e2cfd54e0fad62f0d8ca67a775dc4546af9096f2edb221db42843d65327861282dc946a0ba01a11863ab2d1dfd16e3973d4');
      });
    });

    context('with 4100 output ArrayBuffer', function () {
      it('should be equal', function () {
        // https://raw.githubusercontent.com/gvanas/KeccakCodePackage/master/TestVectors/ShortMsgKAT_SHAKE256.txt
        // Len = 0, Msg = 00
        expect(shake_256.buffer('', 4100).toHexString()).to.be('46b9dd2b0ba88d13233b3feb743eeb243fcd52ea62b81b82b50c27646ed5762fd75dc4ddd8c0f200cb05019d67b592f6fc821c49479ab48640292eacb3b7c4be141e96616fb13957692cc7edd0b45ae3dc07223c8e92937bef84bc0eab862853349ec75546f58fb7c2775c38462c5010d846c185c15111e595522a6bcd16cf86f3d122109e3b1fdd943b6aec468a2d621a7c06c6a957c62b54dafc3be87567d677231395f6147293b68ceab7a9e0c58d864e8efde4e1b9a46cbe854713672f5caaae314ed9083dab4b099f8e300f01b8650f1f4b1d8fcf3f3cb53fb8e9eb2ea203bdc970f50ae55428a91f7f53ac266b28419c3778a15fd248d339ede785fb7f5a1aaa96d313eacc890936c173cdcd0fab882c45755feb3aed96d477ff96390bf9a66d1368b208e21f7c10d04a3dbd4e360633e5db4b602601c14cea737db3dcf722632cc77851cbdde2aaf0a33a07b373445df490cc8fc1e4160ff118378f11f0477de055a81a9eda57a4a2cfb0c83929d310912f729ec6cfa36c6ac6a75837143045d791cc85eff5b21932f23861bcf23a52b5da67eaf7baae0f5fb1369db78f3ac45f8c4ac5671d85735cdddb09d2b1e34a1fc066ff4a162cb263d6541274ae2fcc865f618abe27c124cd8b074ccd516301b91875824d09958f341ef274bdab0bae316339894304e35877b0c28a9b1fd166c796b9cc258a064a8f57e27f2a');

        // Len = 2040
        // Msg = 3A3A819C48EFDE2AD914FBF00E18AB6BC4F14513AB27D0C178A188B61431E7F5623CB66B23346775D386B50E982C493ADBBFC54B9A3CD383382336A1A0B2150A15358F336D03AE18F666C7573D55C4FD181C29E6CCFDE63EA35F0ADF5885CFC0A3D84A2B2E4DD24496DB789E663170CEF74798AA1BBCD4574EA0BBA40489D764B2F83AADC66B148B4A0CD95246C127D5871C4F11418690A5DDF01246A0C80A43C70088B6183639DCFDA4125BD113A8F49EE23ED306FAAC576C3FB0C1E256671D817FC2534A52F5B439F72E424DE376F4C565CCA82307DD9EF76DA5B7C4EB7E085172E328807C02D011FFBF33785378D79DC266F6A5BE6BB0E4A92ECEEBAEB1
        expect(shake_256.buffer([0x3A,0x3A,0x81,0x9C,0x48,0xEF,0xDE,0x2A,0xD9,0x14,0xFB,0xF0,0x0E,0x18,0xAB,0x6B,0xC4,0xF1,0x45,0x13,0xAB,0x27,0xD0,0xC1,0x78,0xA1,0x88,0xB6,0x14,0x31,0xE7,0xF5,0x62,0x3C,0xB6,0x6B,0x23,0x34,0x67,0x75,0xD3,0x86,0xB5,0x0E,0x98,0x2C,0x49,0x3A,0xDB,0xBF,0xC5,0x4B,0x9A,0x3C,0xD3,0x83,0x38,0x23,0x36,0xA1,0xA0,0xB2,0x15,0x0A,0x15,0x35,0x8F,0x33,0x6D,0x03,0xAE,0x18,0xF6,0x66,0xC7,0x57,0x3D,0x55,0xC4,0xFD,0x18,0x1C,0x29,0xE6,0xCC,0xFD,0xE6,0x3E,0xA3,0x5F,0x0A,0xDF,0x58,0x85,0xCF,0xC0,0xA3,0xD8,0x4A,0x2B,0x2E,0x4D,0xD2,0x44,0x96,0xDB,0x78,0x9E,0x66,0x31,0x70,0xCE,0xF7,0x47,0x98,0xAA,0x1B,0xBC,0xD4,0x57,0x4E,0xA0,0xBB,0xA4,0x04,0x89,0xD7,0x64,0xB2,0xF8,0x3A,0xAD,0xC6,0x6B,0x14,0x8B,0x4A,0x0C,0xD9,0x52,0x46,0xC1,0x27,0xD5,0x87,0x1C,0x4F,0x11,0x41,0x86,0x90,0xA5,0xDD,0xF0,0x12,0x46,0xA0,0xC8,0x0A,0x43,0xC7,0x00,0x88,0xB6,0x18,0x36,0x39,0xDC,0xFD,0xA4,0x12,0x5B,0xD1,0x13,0xA8,0xF4,0x9E,0xE2,0x3E,0xD3,0x06,0xFA,0xAC,0x57,0x6C,0x3F,0xB0,0xC1,0xE2,0x56,0x67,0x1D,0x81,0x7F,0xC2,0x53,0x4A,0x52,0xF5,0xB4,0x39,0xF7,0x2E,0x42,0x4D,0xE3,0x76,0xF4,0xC5,0x65,0xCC,0xA8,0x23,0x07,0xDD,0x9E,0xF7,0x6D,0xA5,0xB7,0xC4,0xEB,0x7E,0x08,0x51,0x72,0xE3,0x28,0x80,0x7C,0x02,0xD0,0x11,0xFF,0xBF,0x33,0x78,0x53,0x78,0xD7,0x9D,0xC2,0x66,0xF6,0xA5,0xBE,0x6B,0xB0,0xE4,0xA9,0x2E,0xCE,0xEB,0xAE,0xB1], 4100).toHexString()).to.be('8a5199b4a7e133e264a86202720655894d48cff344a928cf8347f48379cef347dfc5bcffab99b27b1f89aa2735e23d30088ffa03b9edb02b9635470ab9f1038985d55f9ca774572dd006470ea65145469609f9fa0831bf1ffd842dc24acade27bd9816e3b5bf2876cb112232a0eb4475f1dff9f5c713d9ffd4ccb89ae5607fe35731df06317949eef646e9591cf3be53add6b7dd2b6096e2b3fb06e662ec8b2d77422daad9463cd155204acdbd38e319613f39f99b6dfb35ca9365160066db19835888c2241ff9a731a4acbb5663727aac34a401247fbaa7499e7d5ee5b69d31025e63d04c35c798bca1262d5673a9cf0930b5ad89bd485599dc184528da4790f088ebd170b635d9581632d2ff90db79665ced430089af13c9f21f6d443a818064f17aec9e9c5457001fa8dc6afbadbe3138f388d89d0e6f22f66671255b210754ed63d81dce75ce8f189b534e6d6b3539aa51e837c42df9df59c71e6171cd4902fe1bdc73fb1775b5c754a1ed4ea7f3105fc543ee0418dad256f3f6118ea77114a16c15355b42877a1db2a7df0e155ae1d8670abcec3450f4e2eec9838f895423ef63d261138baaf5d9f104cb5a957aea06c0b9b8c78b0d441796dc0350ddeabb78a33b6f1f9e68ede3d1805c7b7e2cfd54e0fad62f0d8ca67a775dc4546af9096f2edb221db42843d65327861282dc946a0ba01a11863ab2d1dfd16e3973d4');
      });
    });

    context('with 4100 output Array', function () {
      it('should be equal', function () {
        // https://raw.githubusercontent.com/gvanas/KeccakCodePackage/master/TestVectors/ShortMsgKAT_SHAKE256.txt
        // Len = 0, Msg = 00
        expect(shake_256.array('', 4100).toHexString()).to.be('46b9dd2b0ba88d13233b3feb743eeb243fcd52ea62b81b82b50c27646ed5762fd75dc4ddd8c0f200cb05019d67b592f6fc821c49479ab48640292eacb3b7c4be141e96616fb13957692cc7edd0b45ae3dc07223c8e92937bef84bc0eab862853349ec75546f58fb7c2775c38462c5010d846c185c15111e595522a6bcd16cf86f3d122109e3b1fdd943b6aec468a2d621a7c06c6a957c62b54dafc3be87567d677231395f6147293b68ceab7a9e0c58d864e8efde4e1b9a46cbe854713672f5caaae314ed9083dab4b099f8e300f01b8650f1f4b1d8fcf3f3cb53fb8e9eb2ea203bdc970f50ae55428a91f7f53ac266b28419c3778a15fd248d339ede785fb7f5a1aaa96d313eacc890936c173cdcd0fab882c45755feb3aed96d477ff96390bf9a66d1368b208e21f7c10d04a3dbd4e360633e5db4b602601c14cea737db3dcf722632cc77851cbdde2aaf0a33a07b373445df490cc8fc1e4160ff118378f11f0477de055a81a9eda57a4a2cfb0c83929d310912f729ec6cfa36c6ac6a75837143045d791cc85eff5b21932f23861bcf23a52b5da67eaf7baae0f5fb1369db78f3ac45f8c4ac5671d85735cdddb09d2b1e34a1fc066ff4a162cb263d6541274ae2fcc865f618abe27c124cd8b074ccd516301b91875824d09958f341ef274bdab0bae316339894304e35877b0c28a9b1fd166c796b9cc258a064a8f57e27f2a');

        // Len = 2040
        // Msg = 3A3A819C48EFDE2AD914FBF00E18AB6BC4F14513AB27D0C178A188B61431E7F5623CB66B23346775D386B50E982C493ADBBFC54B9A3CD383382336A1A0B2150A15358F336D03AE18F666C7573D55C4FD181C29E6CCFDE63EA35F0ADF5885CFC0A3D84A2B2E4DD24496DB789E663170CEF74798AA1BBCD4574EA0BBA40489D764B2F83AADC66B148B4A0CD95246C127D5871C4F11418690A5DDF01246A0C80A43C70088B6183639DCFDA4125BD113A8F49EE23ED306FAAC576C3FB0C1E256671D817FC2534A52F5B439F72E424DE376F4C565CCA82307DD9EF76DA5B7C4EB7E085172E328807C02D011FFBF33785378D79DC266F6A5BE6BB0E4A92ECEEBAEB1
        expect(shake_256.array([0x3A,0x3A,0x81,0x9C,0x48,0xEF,0xDE,0x2A,0xD9,0x14,0xFB,0xF0,0x0E,0x18,0xAB,0x6B,0xC4,0xF1,0x45,0x13,0xAB,0x27,0xD0,0xC1,0x78,0xA1,0x88,0xB6,0x14,0x31,0xE7,0xF5,0x62,0x3C,0xB6,0x6B,0x23,0x34,0x67,0x75,0xD3,0x86,0xB5,0x0E,0x98,0x2C,0x49,0x3A,0xDB,0xBF,0xC5,0x4B,0x9A,0x3C,0xD3,0x83,0x38,0x23,0x36,0xA1,0xA0,0xB2,0x15,0x0A,0x15,0x35,0x8F,0x33,0x6D,0x03,0xAE,0x18,0xF6,0x66,0xC7,0x57,0x3D,0x55,0xC4,0xFD,0x18,0x1C,0x29,0xE6,0xCC,0xFD,0xE6,0x3E,0xA3,0x5F,0x0A,0xDF,0x58,0x85,0xCF,0xC0,0xA3,0xD8,0x4A,0x2B,0x2E,0x4D,0xD2,0x44,0x96,0xDB,0x78,0x9E,0x66,0x31,0x70,0xCE,0xF7,0x47,0x98,0xAA,0x1B,0xBC,0xD4,0x57,0x4E,0xA0,0xBB,0xA4,0x04,0x89,0xD7,0x64,0xB2,0xF8,0x3A,0xAD,0xC6,0x6B,0x14,0x8B,0x4A,0x0C,0xD9,0x52,0x46,0xC1,0x27,0xD5,0x87,0x1C,0x4F,0x11,0x41,0x86,0x90,0xA5,0xDD,0xF0,0x12,0x46,0xA0,0xC8,0x0A,0x43,0xC7,0x00,0x88,0xB6,0x18,0x36,0x39,0xDC,0xFD,0xA4,0x12,0x5B,0xD1,0x13,0xA8,0xF4,0x9E,0xE2,0x3E,0xD3,0x06,0xFA,0xAC,0x57,0x6C,0x3F,0xB0,0xC1,0xE2,0x56,0x67,0x1D,0x81,0x7F,0xC2,0x53,0x4A,0x52,0xF5,0xB4,0x39,0xF7,0x2E,0x42,0x4D,0xE3,0x76,0xF4,0xC5,0x65,0xCC,0xA8,0x23,0x07,0xDD,0x9E,0xF7,0x6D,0xA5,0xB7,0xC4,0xEB,0x7E,0x08,0x51,0x72,0xE3,0x28,0x80,0x7C,0x02,0xD0,0x11,0xFF,0xBF,0x33,0x78,0x53,0x78,0xD7,0x9D,0xC2,0x66,0xF6,0xA5,0xBE,0x6B,0xB0,0xE4,0xA9,0x2E,0xCE,0xEB,0xAE,0xB1], 4100).toHexString()).to.be('8a5199b4a7e133e264a86202720655894d48cff344a928cf8347f48379cef347dfc5bcffab99b27b1f89aa2735e23d30088ffa03b9edb02b9635470ab9f1038985d55f9ca774572dd006470ea65145469609f9fa0831bf1ffd842dc24acade27bd9816e3b5bf2876cb112232a0eb4475f1dff9f5c713d9ffd4ccb89ae5607fe35731df06317949eef646e9591cf3be53add6b7dd2b6096e2b3fb06e662ec8b2d77422daad9463cd155204acdbd38e319613f39f99b6dfb35ca9365160066db19835888c2241ff9a731a4acbb5663727aac34a401247fbaa7499e7d5ee5b69d31025e63d04c35c798bca1262d5673a9cf0930b5ad89bd485599dc184528da4790f088ebd170b635d9581632d2ff90db79665ced430089af13c9f21f6d443a818064f17aec9e9c5457001fa8dc6afbadbe3138f388d89d0e6f22f66671255b210754ed63d81dce75ce8f189b534e6d6b3539aa51e837c42df9df59c71e6171cd4902fe1bdc73fb1775b5c754a1ed4ea7f3105fc543ee0418dad256f3f6118ea77114a16c15355b42877a1db2a7df0e155ae1d8670abcec3450f4e2eec9838f895423ef63d261138baaf5d9f104cb5a957aea06c0b9b8c78b0d441796dc0350ddeabb78a33b6f1f9e68ede3d1805c7b7e2cfd54e0fad62f0d8ca67a775dc4546af9096f2edb221db42843d65327861282dc946a0ba01a11863ab2d1dfd16e3973d4');
      });
    });
  });
})(shake_256, shake_128);
