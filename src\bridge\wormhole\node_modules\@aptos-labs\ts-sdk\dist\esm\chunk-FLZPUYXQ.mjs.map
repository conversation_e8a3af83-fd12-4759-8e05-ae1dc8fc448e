{"version": 3, "sources": ["../../src/bcs/deserializer.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable no-bitwise */\nimport { MAX_U32_NUMBER } from \"./consts\";\nimport { Uint8, Uint16, Uint32, Uint64, Uint128, Uint256, HexInput } from \"../types\";\nimport { Hex } from \"../core/hex\";\n\n/**\n * This interface exists to define Deserializable<T> inputs for functions that\n * deserialize a byte buffer into a type T.\n * It is not intended to be implemented or extended, because Typescript has no support\n * for static methods in interfaces.\n *\n * @template T - The type that this will deserialize into.\n * @group Implementation\n * @category BCS\n */\nexport interface Deserializable<T> {\n  /**\n   * Deserializes the buffered bytes into an instance of the specified class type.\n   * This function provides an alternative syntax for deserialization, allowing users to call\n   * `deserializer.deserialize(MyClass)` instead of `MyClass.deserialize(deserializer)`.\n   *\n   * @param deserializer - The deserializer instance with the buffered bytes.\n   * @returns The deserialized value of class type T.\n   * @example\n   * ```typescript\n   * const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));\n   * const value = deserializer.deserialize(MyClass); // where My<PERSON><PERSON> has a `deserialize` function\n   * // value is now an instance of MyClass\n   * // equivalent to `const value = MyClass.deserialize(deserializer)`\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  deserialize(deserializer: Deserializer): T;\n}\n\n/**\n * A class that provides methods for deserializing various data types from a byte buffer.\n * It supports deserialization of primitive types, strings, and complex objects using a BCS (Binary Common Serialization) layout.\n * @group Implementation\n * @category BCS\n */\nexport class Deserializer {\n  private buffer: ArrayBuffer;\n\n  private offset: number;\n\n  /**\n   * Creates a new instance of the class with a copy of the provided data buffer.\n   * This prevents outside mutation of the buffer.\n   *\n   * @param data - The data to be copied into the internal buffer as a Uint8Array.\n   * @group Implementation\n   * @category BCS\n   */\n  constructor(data: Uint8Array) {\n    // copies data to prevent outside mutation of buffer.\n    this.buffer = new ArrayBuffer(data.length);\n    new Uint8Array(this.buffer).set(data, 0);\n    this.offset = 0;\n  }\n\n  static fromHex(hex: HexInput): Deserializer {\n    const data = Hex.hexInputToUint8Array(hex);\n    return new Deserializer(data);\n  }\n\n  /**\n   * Reads a specified number of bytes from the buffer and advances the offset.\n   *\n   * @param length - The number of bytes to read from the buffer.\n   * @throws Throws an error if the read operation exceeds the buffer's length.\n   * @group Implementation\n   * @category BCS\n   */\n  private read(length: number): ArrayBuffer {\n    if (this.offset + length > this.buffer.byteLength) {\n      throw new Error(\"Reached to the end of buffer\");\n    }\n\n    const bytes = this.buffer.slice(this.offset, this.offset + length);\n    this.offset += length;\n    return bytes;\n  }\n\n  /**\n   * Returns the number of bytes remaining in the buffer.\n   *\n   * This information is useful to determine if there's more data to be read.\n   *\n   * @returns The number of bytes remaining in the buffer.\n   * @group Implementation\n   * @category BCS\n   */\n  remaining(): number {\n    return this.buffer.byteLength - this.offset;\n  }\n\n  /**\n   * Asserts that the buffer has no remaining bytes.\n   *\n   * @throws {Error} Throws an error if there are remaining bytes in the buffer.\n   * @group Implementation\n   * @category BCS\n   */\n  assertFinished(): void {\n    if (this.remaining() !== 0) {\n      throw new Error(\"Buffer has remaining bytes\");\n    }\n  }\n\n  /**\n   * Deserializes a UTF-8 encoded string from a byte array. It first reads the length of the string in bytes,\n   * followed by the actual byte content, and decodes it into a string.\n   *\n   * BCS layout for \"string\": string_length | string_content\n   * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.\n   *\n   * @example\n   * ```typescript\n   * const deserializer = new Deserializer(new Uint8Array([8, 49, 50, 51, 52, 97, 98, 99, 100]));\n   * assert(deserializer.deserializeStr() === \"1234abcd\");\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeStr(): string {\n    const value = this.deserializeBytes();\n    const textDecoder = new TextDecoder();\n    return textDecoder.decode(value);\n  }\n\n  /**\n   * @deprecated use `deserializeOption(\"string\")` instead.\n   *\n   * The BCS layout for Optional<String> is 0 if none, else 1 followed by the string length and string content.\n   * @returns The deserialized string if it exists, otherwise undefined.\n   * @example\n   * ```typescript\n   * const deserializer = new Deserializer(new Uint8Array([0x00]));\n   * assert(deserializer.deserializeOptionStr() === undefined);\n   * const deserializer = new Deserializer(new Uint8Array([1, 8, 49, 50, 51, 52, 97, 98, 99, 100]));\n   * assert(deserializer.deserializeOptionStr() === \"1234abcd\");\n   * ```\n   */\n  deserializeOptionStr(): string | undefined {\n    return this.deserializeOption(\"string\");\n  }\n\n  /**\n   * Deserializes an optional value from the buffer.\n   *\n   * The BCS layout for Optional<T> starts with a boolean byte (0 if none, 1 if some),\n   * followed by the value if present.\n   *\n   * @template T - The type of the value to deserialize\n   * @param type - Either a Deserializable class or one of the string literals: \"string\", \"bytes\", or \"fixedBytes\"\n   * @param len - Required length when type is \"fixedBytes\", ignored otherwise\n   * @returns The deserialized value if present, undefined otherwise\n   *\n   * @throws {Error} When \"fixedBytes\" is specified without a length\n   *\n   * @example\n   * ```typescript\n   * // Deserialize an optional string\n   * const deserializer = new Deserializer(new Uint8Array([1, 3, 97, 98, 99]));\n   * const optStr = deserializer.deserializeOption(\"string\");\n   * // optStr === \"abc\"\n   *\n   * // Deserialize an optional custom type\n   * const deserializer = new Deserializer(new Uint8Array([0]));\n   * const optValue = deserializer.deserializeOption(MyClass);\n   * // optValue === undefined\n   *\n   * // Deserialize optional bytes\n   * const deserializer = new Deserializer(new Uint8Array([1, 3, 1, 2, 3]));\n   * const optBytes = deserializer.deserializeOption(\"bytes\");\n   * // optBytes === Uint8Array[1, 2, 3]\n   *\n   * // Deserialize optional fixed bytes\n   * const deserializer = new Deserializer(new Uint8Array([1, 1, 2, 3, 4]));\n   * const optBytes = deserializer.deserializeOption(\"fixedBytes\", 4);\n   * // optBytes === Uint8Array[1, 2, 3, 4]\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeOption(type: \"string\"): string | undefined;\n  deserializeOption(type: \"bytes\"): Uint8Array | undefined;\n  deserializeOption(type: \"fixedBytes\", len: number): Uint8Array | undefined;\n  deserializeOption<T>(type: Deserializable<T>): T | undefined;\n  deserializeOption<T>(\n    type: Deserializable<T> | \"string\" | \"bytes\" | \"fixedBytes\",\n    len?: number,\n  ): T | string | Uint8Array | undefined {\n    const exists = this.deserializeBool();\n    if (!exists) return undefined;\n\n    if (type === \"string\") {\n      return this.deserializeStr();\n    }\n    if (type === \"bytes\") {\n      return this.deserializeBytes();\n    }\n    if (type === \"fixedBytes\") {\n      if (len === undefined) {\n        throw new Error(\"Fixed bytes length not provided\");\n      }\n      return this.deserializeFixedBytes(len);\n    }\n\n    return this.deserialize(type);\n  }\n\n  /**\n   * Deserializes an array of bytes.\n   *\n   * The BCS layout for \"bytes\" consists of a bytes_length followed by the bytes themselves, where bytes_length is a u32 integer\n   * encoded as a uleb128 integer, indicating the length of the bytes array.\n   *\n   * @returns {Uint8Array} The deserialized array of bytes.\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeBytes(): Uint8Array {\n    const len = this.deserializeUleb128AsU32();\n    return new Uint8Array(this.read(len));\n  }\n\n  /**\n   * Deserializes an array of bytes of a specified length.\n   *\n   * @param len - The number of bytes to read from the source.\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeFixedBytes(len: number): Uint8Array {\n    return new Uint8Array(this.read(len));\n  }\n\n  /**\n   * Deserializes a boolean value from a byte stream.\n   *\n   * The BCS layout for a boolean uses one byte, where \"0x01\" represents true and \"0x00\" represents false.\n   * An error is thrown if the byte value is not valid.\n   *\n   * @returns The deserialized boolean value.\n   * @throws Throws an error if the boolean value is invalid.\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeBool(): boolean {\n    const bool = new Uint8Array(this.read(1))[0];\n    if (bool !== 1 && bool !== 0) {\n      throw new Error(\"Invalid boolean value\");\n    }\n    return bool === 1;\n  }\n\n  /**\n   * Deserializes a uint8 number from the binary data.\n   *\n   * BCS layout for \"uint8\": One byte. Binary format in little-endian representation.\n   *\n   * @returns {number} The deserialized uint8 number.\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeU8(): Uint8 {\n    return new DataView(this.read(1)).getUint8(0);\n  }\n\n  /**\n   * Deserializes a uint16 number from a binary format in little-endian representation.\n   *\n   * BCS layout for \"uint16\": Two bytes.\n   * @example\n   * ```typescript\n   * const deserializer = new Deserializer(new Uint8Array([0x34, 0x12]));\n   * assert(deserializer.deserializeU16() === 4660);\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeU16(): Uint16 {\n    return new DataView(this.read(2)).getUint16(0, true);\n  }\n\n  /**\n   * Deserializes a uint32 number from a binary format in little-endian representation.\n   *\n   * BCS layout for \"uint32\": Four bytes.\n   * @example\n   * ```typescript\n   * const deserializer = new Deserializer(new Uint8Array([0x78, 0x56, 0x34, 0x12]));\n   * assert(deserializer.deserializeU32() === 305419896);\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeU32(): Uint32 {\n    return new DataView(this.read(4)).getUint32(0, true);\n  }\n\n  /**\n   * Deserializes a uint64 number.\n   *\n   * This function combines two 32-bit values to return a 64-bit unsigned integer in little-endian representation.\n   * @example\n   * ```typescript\n   * const deserializer = new Deserializer(new Uint8Array([0x00, 0xEF, 0xCD, 0xAB, 0x78, 0x56, 0x34, 0x12]));\n   * assert(deserializer.deserializeU64() === 1311768467750121216);\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeU64(): Uint64 {\n    const low = this.deserializeU32();\n    const high = this.deserializeU32();\n\n    // combine the two 32-bit values and return (little endian)\n    return BigInt((BigInt(high) << BigInt(32)) | BigInt(low));\n  }\n\n  /**\n   * Deserializes a uint128 number from its binary representation.\n   * This function combines two 64-bit values to return a single uint128 value in little-endian format.\n   *\n   * @returns {BigInt} The deserialized uint128 number.\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeU128(): Uint128 {\n    const low = this.deserializeU64();\n    const high = this.deserializeU64();\n\n    // combine the two 64-bit values and return (little endian)\n    return BigInt((high << BigInt(64)) | low);\n  }\n\n  /**\n   * Deserializes a uint256 number from its binary representation.\n   *\n   * The BCS layout for \"uint256\" consists of thirty-two bytes in little-endian format.\n   *\n   * @returns {BigInt} The deserialized uint256 number.\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeU256(): Uint256 {\n    const low = this.deserializeU128();\n    const high = this.deserializeU128();\n\n    // combine the two 128-bit values and return (little endian)\n    return BigInt((high << BigInt(128)) | low);\n  }\n\n  /**\n   * Deserializes a uleb128 encoded uint32 number.\n   *\n   * This function is used for interpreting lengths of variable-length sequences and tags of enum values in BCS encoding.\n   *\n   * @throws {Error} Throws an error if the parsed value exceeds the maximum uint32 number.\n   * @returns {number} The deserialized uint32 value.\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeUleb128AsU32(): Uint32 {\n    let value: bigint = BigInt(0);\n    let shift = 0;\n\n    while (value < MAX_U32_NUMBER) {\n      const byte = this.deserializeU8();\n      value |= BigInt(byte & 0x7f) << BigInt(shift);\n\n      if ((byte & 0x80) === 0) {\n        break;\n      }\n      shift += 7;\n    }\n\n    if (value > MAX_U32_NUMBER) {\n      throw new Error(\"Overflow while parsing uleb128-encoded uint32 value\");\n    }\n\n    return Number(value);\n  }\n\n  /**\n   * Helper function that primarily exists to support alternative syntax for deserialization.\n   * That is, if we have a `const deserializer: new Deserializer(...)`, instead of having to use\n   * `MyClass.deserialize(deserializer)`, we can call `deserializer.deserialize(MyClass)`.\n   *\n   * @example const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));\n   * const value = deserializer.deserialize(MyClass); // where MyClass has a `deserialize` function\n   * // value is now an instance of MyClass\n   * // equivalent to `const value = MyClass.deserialize(deserializer)`\n   * @param cls The BCS-deserializable class to deserialize the buffered bytes into.\n   *\n   * @returns the deserialized value of class type T\n   * @group Implementation\n   * @category BCS\n   */\n  deserialize<T>(cls: Deserializable<T>): T {\n    // NOTE: `deserialize` in `cls.deserialize(this)` here is a static method defined in `cls`,\n    // It is separate from the `deserialize` instance method defined here in Deserializer.\n    return cls.deserialize(this);\n  }\n\n  /**\n   * Deserializes an array of BCS Deserializable values given an existing Deserializer instance with a loaded byte buffer.\n   *\n   * @param cls The BCS-deserializable class to deserialize the buffered bytes into.\n   * @returns An array of deserialized values of type T.\n   * @example\n   * // serialize a vector of addresses\n   * const addresses = new Array<AccountAddress>(\n   *   AccountAddress.from(\"0x1\"),\n   *   AccountAddress.from(\"0x2\"),\n   *   AccountAddress.from(\"0xa\"),\n   *   AccountAddress.from(\"0xb\"),\n   * );\n   * const serializer = new Serializer();\n   * serializer.serializeVector(addresses);\n   * const serializedBytes = serializer.toUint8Array();\n   *\n   * // deserialize the bytes into an array of addresses\n   * const deserializer = new Deserializer(serializedBytes);\n   * const deserializedAddresses = deserializer.deserializeVector(AccountAddress);\n   * // deserializedAddresses is now an array of AccountAddress instances\n   * @group Implementation\n   * @category BCS\n   */\n  deserializeVector<T>(cls: Deserializable<T>): Array<T> {\n    const length = this.deserializeUleb128AsU32();\n    const vector = new Array<T>();\n    for (let i = 0; i < length; i += 1) {\n      vector.push(this.deserialize(cls));\n    }\n    return vector;\n  }\n}\n"], "mappings": "kFA6CO,IAAMA,EAAN,MAAMC,CAAa,CAaxB,YAAYC,EAAkB,CAE5B,KAAK,OAAS,IAAI,YAAYA,EAAK,MAAM,EACzC,IAAI,WAAW,KAAK,MAAM,EAAE,IAAIA,EAAM,CAAC,EACvC,KAAK,OAAS,CAChB,CAEA,OAAO,QAAQC,EAA6B,CAC1C,IAAMD,EAAOE,EAAI,qBAAqBD,CAAG,EACzC,OAAO,IAAIF,EAAaC,CAAI,CAC9B,CAUQ,KAAKG,EAA6B,CACxC,GAAI,KAAK,OAASA,EAAS,KAAK,OAAO,WACrC,MAAM,IAAI,MAAM,8BAA8B,EAGhD,IAAMC,EAAQ,KAAK,OAAO,MAAM,KAAK,OAAQ,KAAK,OAASD,CAAM,EACjE,YAAK,QAAUA,EACRC,CACT,CAWA,WAAoB,CAClB,OAAO,KAAK,OAAO,WAAa,KAAK,MACvC,CASA,gBAAuB,CACrB,GAAI,KAAK,UAAU,IAAM,EACvB,MAAM,IAAI,MAAM,4BAA4B,CAEhD,CAiBA,gBAAyB,CACvB,IAAMC,EAAQ,KAAK,iBAAiB,EAEpC,OADoB,IAAI,YAAY,EACjB,OAAOA,CAAK,CACjC,CAeA,sBAA2C,CACzC,OAAO,KAAK,kBAAkB,QAAQ,CACxC,CA4CA,kBACEC,EACAC,EACqC,CAErC,GADe,KAAK,gBAAgB,EAGpC,IAAID,IAAS,SACX,OAAO,KAAK,eAAe,EAE7B,GAAIA,IAAS,QACX,OAAO,KAAK,iBAAiB,EAE/B,GAAIA,IAAS,aAAc,CACzB,GAAIC,IAAQ,OACV,MAAM,IAAI,MAAM,iCAAiC,EAEnD,OAAO,KAAK,sBAAsBA,CAAG,CACvC,CAEA,OAAO,KAAK,YAAYD,CAAI,EAC9B,CAYA,kBAA+B,CAC7B,IAAMC,EAAM,KAAK,wBAAwB,EACzC,OAAO,IAAI,WAAW,KAAK,KAAKA,CAAG,CAAC,CACtC,CASA,sBAAsBA,EAAyB,CAC7C,OAAO,IAAI,WAAW,KAAK,KAAKA,CAAG,CAAC,CACtC,CAaA,iBAA2B,CACzB,IAAMC,EAAO,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,EAC3C,GAAIA,IAAS,GAAKA,IAAS,EACzB,MAAM,IAAI,MAAM,uBAAuB,EAEzC,OAAOA,IAAS,CAClB,CAWA,eAAuB,CACrB,OAAO,IAAI,SAAS,KAAK,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAC9C,CAcA,gBAAyB,CACvB,OAAO,IAAI,SAAS,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,EAAG,EAAI,CACrD,CAcA,gBAAyB,CACvB,OAAO,IAAI,SAAS,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,EAAG,EAAI,CACrD,CAcA,gBAAyB,CACvB,IAAMC,EAAM,KAAK,eAAe,EAC1BC,EAAO,KAAK,eAAe,EAGjC,OAAO,OAAQ,OAAOA,CAAI,GAAK,OAAO,EAAE,EAAK,OAAOD,CAAG,CAAC,CAC1D,CAUA,iBAA2B,CACzB,IAAMA,EAAM,KAAK,eAAe,EAC1BC,EAAO,KAAK,eAAe,EAGjC,OAAO,OAAQA,GAAQ,OAAO,EAAE,EAAKD,CAAG,CAC1C,CAWA,iBAA2B,CACzB,IAAMA,EAAM,KAAK,gBAAgB,EAC3BC,EAAO,KAAK,gBAAgB,EAGlC,OAAO,OAAQA,GAAQ,OAAO,GAAG,EAAKD,CAAG,CAC3C,CAYA,yBAAkC,CAChC,IAAIJ,EAAgB,OAAO,CAAC,EACxBM,EAAQ,EAEZ,KAAON,EAAQO,GAAgB,CAC7B,IAAMC,EAAO,KAAK,cAAc,EAGhC,GAFAR,GAAS,OAAOQ,EAAO,GAAI,GAAK,OAAOF,CAAK,GAEvCE,EAAO,OAAU,EACpB,MAEFF,GAAS,CACX,CAEA,GAAIN,EAAQO,EACV,MAAM,IAAI,MAAM,qDAAqD,EAGvE,OAAO,OAAOP,CAAK,CACrB,CAiBA,YAAeS,EAA2B,CAGxC,OAAOA,EAAI,YAAY,IAAI,CAC7B,CA0BA,kBAAqBA,EAAkC,CACrD,IAAMX,EAAS,KAAK,wBAAwB,EACtCY,EAAS,IAAI,MACnB,QAASC,EAAI,EAAGA,EAAIb,EAAQa,GAAK,EAC/BD,EAAO,KAAK,KAAK,YAAYD,CAAG,CAAC,EAEnC,OAAOC,CACT,CACF", "names": ["Deserializer", "_Deserializer", "data", "hex", "Hex", "length", "bytes", "value", "type", "len", "bool", "low", "high", "shift", "MAX_U32_NUMBER", "byte", "cls", "vector", "i"]}