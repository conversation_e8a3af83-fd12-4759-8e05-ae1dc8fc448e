"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetVersionResponse = exports.GetVersionRequest = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cometbft.services.version.v1";
function createBaseGetVersionRequest() {
    return {};
}
exports.GetVersionRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGetVersionRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.GetVersionRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseGetVersionRequest();
        return message;
    },
};
function createBaseGetVersionResponse() {
    return { node: "", abci: "", p2p: "0", block: "0" };
}
exports.GetVersionResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.node !== "") {
            writer.uint32(10).string(message.node);
        }
        if (message.abci !== "") {
            writer.uint32(18).string(message.abci);
        }
        if (message.p2p !== "0") {
            writer.uint32(24).uint64(message.p2p);
        }
        if (message.block !== "0") {
            writer.uint32(32).uint64(message.block);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGetVersionResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.node = reader.string();
                    break;
                case 2:
                    message.abci = reader.string();
                    break;
                case 3:
                    message.p2p = longToString(reader.uint64());
                    break;
                case 4:
                    message.block = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            node: isSet(object.node) ? String(object.node) : "",
            abci: isSet(object.abci) ? String(object.abci) : "",
            p2p: isSet(object.p2p) ? String(object.p2p) : "0",
            block: isSet(object.block) ? String(object.block) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.node !== undefined && (obj.node = message.node);
        message.abci !== undefined && (obj.abci = message.abci);
        message.p2p !== undefined && (obj.p2p = message.p2p);
        message.block !== undefined && (obj.block = message.block);
        return obj;
    },
    create: function (base) {
        return exports.GetVersionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseGetVersionResponse();
        message.node = (_a = object.node) !== null && _a !== void 0 ? _a : "";
        message.abci = (_b = object.abci) !== null && _b !== void 0 ? _b : "";
        message.p2p = (_c = object.p2p) !== null && _c !== void 0 ? _c : "0";
        message.block = (_d = object.block) !== null && _d !== void 0 ? _d : "0";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
