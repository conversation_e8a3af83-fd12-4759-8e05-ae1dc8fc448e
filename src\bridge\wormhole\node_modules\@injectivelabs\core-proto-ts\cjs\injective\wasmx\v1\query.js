"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.QueryWasmxModuleStateDesc = exports.QueryContractRegistrationInfoDesc = exports.QueryWasmxParamsDesc = exports.QueryDesc = exports.QueryClientImpl = exports.QueryContractRegistrationInfoResponse = exports.QueryContractRegistrationInfoRequest = exports.QueryModuleStateResponse = exports.QueryModuleStateRequest = exports.QueryWasmxParamsResponse = exports.QueryWasmxParamsRequest = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var genesis_1 = require("./genesis.js");
var wasmx_1 = require("./wasmx.js");
exports.protobufPackage = "injective.wasmx.v1";
function createBaseQueryWasmxParamsRequest() {
    return {};
}
exports.QueryWasmxParamsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryWasmxParamsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryWasmxParamsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryWasmxParamsRequest();
        return message;
    },
};
function createBaseQueryWasmxParamsResponse() {
    return { params: undefined };
}
exports.QueryWasmxParamsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            wasmx_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryWasmxParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = wasmx_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { params: isSet(object.params) ? wasmx_1.Params.fromJSON(object.params) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? wasmx_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryWasmxParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryWasmxParamsResponse();
        message.params = (object.params !== undefined && object.params !== null)
            ? wasmx_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseQueryModuleStateRequest() {
    return {};
}
exports.QueryModuleStateRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryModuleStateRequest();
        return message;
    },
};
function createBaseQueryModuleStateResponse() {
    return { state: undefined };
}
exports.QueryModuleStateResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            genesis_1.GenesisState.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = genesis_1.GenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? genesis_1.GenesisState.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined && (obj.state = message.state ? genesis_1.GenesisState.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryModuleStateResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? genesis_1.GenesisState.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBaseQueryContractRegistrationInfoRequest() {
    return { contractAddress: "" };
}
exports.QueryContractRegistrationInfoRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryContractRegistrationInfoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryContractRegistrationInfoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryContractRegistrationInfoRequest();
        message.contractAddress = (_a = object.contractAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryContractRegistrationInfoResponse() {
    return { contract: undefined };
}
exports.QueryContractRegistrationInfoResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.contract !== undefined) {
            wasmx_1.RegisteredContract.encode(message.contract, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryContractRegistrationInfoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contract = wasmx_1.RegisteredContract.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { contract: isSet(object.contract) ? wasmx_1.RegisteredContract.fromJSON(object.contract) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.contract !== undefined &&
            (obj.contract = message.contract ? wasmx_1.RegisteredContract.toJSON(message.contract) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryContractRegistrationInfoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryContractRegistrationInfoResponse();
        message.contract = (object.contract !== undefined && object.contract !== null)
            ? wasmx_1.RegisteredContract.fromPartial(object.contract)
            : undefined;
        return message;
    },
};
var QueryClientImpl = /** @class */ (function () {
    function QueryClientImpl(rpc) {
        this.rpc = rpc;
        this.WasmxParams = this.WasmxParams.bind(this);
        this.ContractRegistrationInfo = this.ContractRegistrationInfo.bind(this);
        this.WasmxModuleState = this.WasmxModuleState.bind(this);
    }
    QueryClientImpl.prototype.WasmxParams = function (request, metadata) {
        return this.rpc.unary(exports.QueryWasmxParamsDesc, exports.QueryWasmxParamsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ContractRegistrationInfo = function (request, metadata) {
        return this.rpc.unary(exports.QueryContractRegistrationInfoDesc, exports.QueryContractRegistrationInfoRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.WasmxModuleState = function (request, metadata) {
        return this.rpc.unary(exports.QueryWasmxModuleStateDesc, exports.QueryModuleStateRequest.fromPartial(request), metadata);
    };
    return QueryClientImpl;
}());
exports.QueryClientImpl = QueryClientImpl;
exports.QueryDesc = { serviceName: "injective.wasmx.v1.Query" };
exports.QueryWasmxParamsDesc = {
    methodName: "WasmxParams",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryWasmxParamsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryWasmxParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryContractRegistrationInfoDesc = {
    methodName: "ContractRegistrationInfo",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryContractRegistrationInfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryContractRegistrationInfoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryWasmxModuleStateDesc = {
    methodName: "WasmxModuleState",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryModuleStateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryModuleStateResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
