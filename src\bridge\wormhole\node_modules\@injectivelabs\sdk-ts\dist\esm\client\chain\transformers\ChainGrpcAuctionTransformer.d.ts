import { AuctionBid, GrpcAuctionBid, AuctionModuleState, AuctionCurrentBasket, AuctionModuleStateParams, AuctionLastAuctionResult, GrpcAuctionLastAuctionResult } from '../types/auction.js';
import { InjectiveAuctionV1Beta1Query } from '@injectivelabs/core-proto-ts';
/**
 * @category Chain Grpc Transformer
 */
export declare class ChainGrpcAuctionTransformer {
    static grpcBidToBid(grpcBid: GrpcAuctionBid): AuctionBid;
    static grpcLastAuctionResultToLastAuctionResult(grpcLastAuctionResult: GrpcAuctionLastAuctionResult): AuctionLastAuctionResult;
    static moduleParamsResponseToModuleParams(response: InjectiveAuctionV1Beta1Query.QueryAuctionParamsResponse): AuctionModuleStateParams;
    static currentBasketResponseToCurrentBasket(response: InjectiveAuctionV1Beta1Query.QueryCurrentAuctionBasketResponse): AuctionCurrentBasket;
    static auctionModuleStateResponseToAuctionModuleState(response: InjectiveAuctionV1Beta1Query.QueryModuleStateResponse): AuctionModuleState;
    static LastAuctionResultResponseToLastAuctionResult(response: InjectiveAuctionV1Beta1Query.QueryLastAuctionResultResponse): AuctionLastAuctionResult | undefined;
}
