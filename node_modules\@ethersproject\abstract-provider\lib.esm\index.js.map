{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;AAEb,OAAO,EAAE,SAAS,EAAgB,MAAM,0BAA0B,CAAC;AACnE,OAAO,EAAa,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAE9D,OAAO,EAAc,WAAW,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAIvG,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AA8ClC,CAAC;AAkED,CAAC;AAuBF,qCAAqC;AACrC,0EAA0E;AAC1E,IAAI;AAEJ,MAAM,OAAgB,SAAU,SAAQ,WAAW;IAK/C,MAAM,CAAC,WAAW,CAAC,KAAU;QACzB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;IAC3C,CAAC;CACJ;AAED,MAAM,OAAO,cAAe,SAAQ,SAAS;IAKzC,YAAY,SAAiB,EAAE,MAAe;QAC1C,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YAC7B,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;SAC1E;QAED,KAAK,CAAC;YACF,YAAY,EAAE,IAAI;YAClB,iBAAiB,EAAE,IAAI;YACvB,MAAM,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC;YACrB,SAAS,EAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;CACJ;AAED,MAAM,OAAO,oBAAqB,SAAQ,SAAS;IAK/C,YAAY,IAAY,EAAE,MAAe;QACrC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;YACxB,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SACvE;QAED,KAAK,CAAC;YACF,YAAY,EAAE,IAAI;YAClB,uBAAuB,EAAE,IAAI;YAC7B,MAAM,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC;YACrB,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;IACP,CAAC;CACJ;AAED,MAAM,OAAO,yBAA0B,SAAQ,SAAS;IAIpD,YAAY,UAAkB,EAAE,SAAiB,EAAE,MAAe;QAC9D,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE;YAC9B,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;SACnF;QACD,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YAC7B,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;SACjF;QAED,KAAK,CAAC;YACF,YAAY,EAAE,IAAI;YAClB,4BAA4B,EAAE,IAAI;YAClC,MAAM,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC;YACrB,UAAU,EAAE,UAAU;YACtB,SAAS,EAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;CACJ;AAMD,+BAA+B;AAC/B,qBAAqB;AACrB,MAAM,OAAgB,QAAQ;IAgF1B;QACI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC3C,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IA3EK,UAAU;;YACZ,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,iBAAiB,CAAC;gBAChD,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC9B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACzC,+CAA+C;oBAC/C,qBAAqB;oBACrB,OAAO,IAAI,CAAC;gBAChB,CAAC,CAAC;aACL,CAAC,CAAC;YAEH,IAAI,iBAAiB,GAAG,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE,oBAAoB,GAAG,IAAI,CAAC;YAE/E,IAAI,KAAK,IAAI,KAAK,CAAC,aAAa,EAAE;gBAC9B,6DAA6D;gBAC7D,wDAAwD;gBACxD,+CAA+C;gBAC/C,iBAAiB,GAAG,KAAK,CAAC,aAAa,CAAC;gBACxC,oBAAoB,GAAG,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACpD,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;aACvE;YAED,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC;QAC/E,CAAC;KAAA;IAmCD,iBAAiB;IACjB,WAAW,CAAC,SAAoB,EAAE,QAAkB;QAChD,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,kBAAkB;IAClB,cAAc,CAAC,SAAoB,EAAE,QAAkB;QACnD,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAYD,MAAM,CAAC,UAAU,CAAC,KAAU;QACxB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;CAyCJ"}