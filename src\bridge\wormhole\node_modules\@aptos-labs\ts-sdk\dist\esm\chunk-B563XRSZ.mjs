import{a as r}from"./chunk-7DQDJ2SA.mjs";import{spawn as c}from"child_process";import l from"tree-kill";import{platform as h}from"os";var e=class{constructor(t){this.MAXIMUM_WAIT_TIME_SEC=75;this.READINESS_ENDPOINT="http://127.0.0.1:8070/";this.showStdout=!0;this.process=null;this.showStdout=t?.showStdout??!0}async stop(){await new Promise((t,o)=>{this.process?.pid&&l(this.process.pid,s=>{s?o(s):t(!0)})})}async run(){await this.checkIfProcessIsUp()||(this.start(),await this.waitUntilProcessIsUp())}start(){let t="npx",o=["aptos","node","run-localnet","--force-restart","--assume-yes","--with-indexer-api"],s=h(),a={env:{...process.env,ENABLE_KEYLESS_DEFAULT:"1"},...s==="win32"&&{shell:!0}};this.process=c(t,o,a),this.process.stdout?.on("data",i=>{let n=i.toString();this.showStdout&&console.log(n)})}async waitUntilProcessIsUp(){let t=await this.checkIfProcessIsUp(),o=Date.now()/1e3,s=o;for(;!t&&o+this.MAXIMUM_WAIT_TIME_SEC>s;)await r(1e3),t=await this.checkIfProcessIsUp(),s=Date.now()/1e3;if(!t)throw new Error("Process failed to start");return!0}async checkIfProcessIsUp(){try{return(await fetch(this.READINESS_ENDPOINT)).status===200}catch{return!1}}};export{e as a};
//# sourceMappingURL=chunk-B563XRSZ.mjs.map