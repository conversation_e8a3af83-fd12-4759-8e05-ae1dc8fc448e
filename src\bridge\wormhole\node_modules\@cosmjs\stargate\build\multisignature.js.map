{"version": 3, "file": "multisignature.js", "sourceRoot": "", "sources": ["../src/multisignature.ts"], "names": [], "mappings": ";;;AAAA,yCAAiF;AACjF,+CAA8C;AAC9C,yDAAqD;AACrD,mFAAuG;AACvG,4EAA0E;AAC1E,0DAAyE;AACzE,0DAA0D;AAE1D,SAAgB,mBAAmB,CAAC,IAAwB;IAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAChE,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc;IAEvD,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;QACzB,sCAAsC;QACtC,IAAI,KAAK;YAAE,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,OAAO,0BAAe,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,CAAC,CAAC;AACnF,CAAC;AAbD,kDAaC;AAED;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAC/B,cAAuC,EACvC,QAAgB,EAChB,GAAW,EACX,SAAqB,EACrB,UAAmC;IAEnC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IAChD,MAAM,MAAM,GAAG,IAAA,qBAAU,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAE/C,MAAM,OAAO,GAAc,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClF,MAAM,cAAc,GAAG,IAAI,KAAK,EAAc,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC5D,MAAM,aAAa,GAAG,IAAA,uBAAe,EAAC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC/E,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,SAAS,EAAE;YACb,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAClB,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAChC;KACF;IAED,MAAM,UAAU,GAAe;QAC7B,SAAS,EAAE,IAAA,4BAAY,EAAC,cAAc,CAAC;QACvC,QAAQ,EAAE;YACR,KAAK,EAAE;gBACL,QAAQ,EAAE,mBAAmB,CAAC,OAAO,CAAC;gBACtC,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,kBAAQ,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;aACnG;SACF;QACD,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;KAC3B,CAAC;IAEF,MAAM,QAAQ,GAAG,aAAQ,CAAC,WAAW,CAAC;QACpC,WAAW,EAAE,CAAC,UAAU,CAAC;QACzB,GAAG,EAAE;YACH,MAAM,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;YACvB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;SAC1B;KACF,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,aAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;IACzD,MAAM,QAAQ,GAAG,UAAK,CAAC,WAAW,CAAC;QACjC,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,aAAa;QAC5B,UAAU,EAAE,CAAC,yBAAc,CAAC,MAAM,CAAC,yBAAc,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;KACzG,CAAC,CAAC;IACH,OAAO,QAAQ,CAAC;AAClB,CAAC;AA/CD,8CA+CC;AAED;;;;;GAKG;AACH,SAAgB,sBAAsB,CACpC,cAAuC,EACvC,QAAgB,EAChB,GAAW,EACX,SAAqB,EACrB,UAAmC;IAEnC,MAAM,QAAQ,GAAG,iBAAiB,CAAC,cAAc,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IACzF,OAAO,UAAU,CAAC,IAAI,CAAC,UAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,CAAC;AATD,wDASC"}