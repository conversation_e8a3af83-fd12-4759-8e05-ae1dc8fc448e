import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective.crypto.v1beta1.ethsecp256k1";
/**
 * Pub<PERSON>ey defines a type alias for an ecdsa.PublicKey that implements
 * Tendermint's PubKey interface. It represents the 33-byte compressed public
 * key format.
 */
export interface PubKey {
    key: Uint8Array;
}
/**
 * Priv<PERSON>ey defines a type alias for an ecdsa.PrivateKey that implements
 * Tendermint's PrivateKey interface.
 */
export interface PrivKey {
    key: Uint8Array;
}
export declare const PubKey: {
    encode(message: <PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PubKey;
    fromJSON(object: any): PubKey;
    toJSON(message: PubKey): unknown;
    create(base?: DeepPartial<PubKey>): PubKey;
    fromPartial(object: DeepPartial<PubKey>): PubKey;
};
export declare const PrivKey: {
    encode(message: <PERSON>ri<PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrivKey;
    fromJSON(object: any): PrivKey;
    toJSON(message: PrivKey): unknown;
    create(base?: DeepPartial<PrivKey>): PrivKey;
    fromPartial(object: DeepPartial<PrivKey>): PrivKey;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
