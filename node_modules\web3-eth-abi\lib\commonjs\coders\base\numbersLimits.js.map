{"version": 3, "file": "numbersLimits.js", "sourceRoot": "", "sources": ["../../../../src/coders/base/numbersLimits.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAEF;;GAEG;AACU,QAAA,YAAY,GAAG,IAAI,GAAG,EAAwC,CAAC;AAE5E,IAAI,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;AACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;IAClC,oBAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE;QAC5B,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QACd,GAAG,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;KACrB,CAAC,CAAC;IACH,oBAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;QAC3B,GAAG,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;QACtB,GAAG,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;KACjC,CAAC,CAAC;IACH,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC;AAED,oEAAoE;AACpE,oBAAY,CAAC,GAAG,CAAC,KAAK,EAAE,oBAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,CAAC;AACrD,oEAAoE;AACpE,oBAAY,CAAC,GAAG,CAAC,MAAM,EAAE,oBAAY,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,CAAC"}