"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const numbers_js_1 = require("../../../../utils/numbers.js");
const createMessage = (params) => {
    const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgLiquidatePosition.create();
    message.sender = params.injectiveAddress;
    message.subaccountId = params.subaccountId;
    message.marketId = params.marketId;
    if (params.order) {
        const orderInfo = core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
        orderInfo.subaccountId = params.order.subaccountId;
        orderInfo.feeRecipient = params.order.feeRecipient;
        orderInfo.price = params.order.price;
        orderInfo.quantity = params.order.quantity;
        if (params.order.cid) {
            orderInfo.cid = params.order.cid;
        }
        const order = core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.DerivativeOrder.create();
        order.marketId = params.order.marketId;
        order.margin = params.order.margin;
        order.orderInfo = orderInfo;
        order.orderType = params.order.orderType;
        order.triggerPrice = params.order.triggerPrice || '0';
        message.order = order;
    }
    return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgLiquidatePosition.fromPartial(message);
};
/**
 * @category Messages
 */
class MsgLiquidatePosition extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgLiquidatePosition(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            order: initialParams.order
                ? {
                    ...initialParams.order,
                    price: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.order.price).toFixed(),
                    margin: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.order.margin).toFixed(),
                    triggerPrice: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.order.triggerPrice || 0).toFixed(),
                    quantity: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.order.quantity).toFixed(),
                }
                : undefined,
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgLiquidatePosition',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const order = createMessage(params);
        const message = {
            ...(0, snakecase_keys_1.default)(order),
        };
        return {
            type: 'exchange/MsgLiquidatePosition',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgLiquidatePosition',
            ...value,
        };
    }
    toEip712V2() {
        const web3gw = this.toWeb3Gw();
        const order = web3gw.order;
        const messageAdjusted = {
            ...web3gw,
            order: order
                ? {
                    ...order,
                    order_info: {
                        ...order?.order_info,
                        price: (0, numbers_js_1.numberToCosmosSdkDecString)(order.order_info.price),
                        quantity: (0, numbers_js_1.numberToCosmosSdkDecString)(order.order_info.quantity),
                    },
                    margin: (0, numbers_js_1.numberToCosmosSdkDecString)(order.margin),
                    trigger_price: (0, numbers_js_1.numberToCosmosSdkDecString)(order.trigger_price || '0'),
                    order_type: core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.orderTypeToJSON(order.order_type),
                }
                : undefined,
        };
        return messageAdjusted;
    }
    toEip712() {
        const { params } = this;
        const amino = this.toAmino();
        const { value, type } = amino;
        const order = value.order;
        const messageAdjusted = {
            ...value,
            order: params.order
                ? {
                    ...order,
                    order_info: {
                        ...order?.order_info,
                        price: (0, numbers_js_1.amountToCosmosSdkDecAmount)(order.order_info.price).toFixed(),
                        quantity: (0, numbers_js_1.amountToCosmosSdkDecAmount)(order.order_info.quantity).toFixed(),
                    },
                    margin: (0, numbers_js_1.amountToCosmosSdkDecAmount)(order.margin).toFixed(),
                    trigger_price: (0, numbers_js_1.amountToCosmosSdkDecAmount)(order.trigger_price || '0').toFixed(),
                }
                : undefined,
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgLiquidatePosition',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgLiquidatePosition.encode(this.toProto()).finish();
    }
}
exports.default = MsgLiquidatePosition;
