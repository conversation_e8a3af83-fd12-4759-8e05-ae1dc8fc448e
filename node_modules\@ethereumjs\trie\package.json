{"name": "@ethereumjs/trie", "version": "6.2.1", "description": "Implementation of the modified merkle patricia tree as specified in Ethereum's yellow paper.", "keywords": ["merkle", "radix", "trie", "ethereum"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/trie#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+trie%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MPL-2.0", "author": "EthereumJS Team", "contributors": ["<PERSON> <http://aaron.kumavis.me/> (https://github.com/kumavis)"], "main": "dist/cjs/index.js", "type": "commonjs", "module": "dist/esm/index.js", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "files": ["dist", "src"], "scripts": {"benchmarks": "node -r tsx/register --max-old-space-size=8024 benchmarks", "build": "../../config/cli/ts-build.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "DEBUG=ethjs npx vitest run --coverage.enabled --coverage.reporter=lcov", "docs:build": "typedoc --options typedoc.cjs", "examples": "tsx ../../scripts/examples-runner.ts -- trie", "examples:build": "npx embedme README.md", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "prepublishOnly": "../../config/cli/prepublish.sh", "profiling": "tsc --target ES5 benchmarks/random.ts && 0x benchmarks/random.js", "test": "npm run test:node", "test:browser": "npx vitest run --config=./vitest.config.browser.mts", "test:node": "npx vitest run", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@ethereumjs/rlp": "^5.0.2", "@ethereumjs/util": "^9.1.0", "@types/readable-stream": "^2.3.13", "debug": "^4.3.4", "lru-cache": "10.1.0", "ethereum-cryptography": "^2.2.1", "readable-stream": "^3.6.0"}, "devDependencies": {"@ethereumjs/genesis": "^0.2.3", "@types/benchmark": "^1.0.33", "abstract-level": "^1.0.3", "level": "^8.0.0", "level-legacy": "npm:level@^7.0.0", "level-mem": "^6.0.1", "levelup": "^5.1.1", "lmdb": "^2.5.3", "memory-level": "^1.0.0", "micro-bmark": "0.2.0"}, "engines": {"node": ">=18"}}