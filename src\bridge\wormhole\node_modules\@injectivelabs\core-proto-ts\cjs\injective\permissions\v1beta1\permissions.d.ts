import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
export declare const protobufPackage = "injective.permissions.v1beta1";
/** each Action enum value should be a power of two */
export declare enum Action {
    /** UNSPECIFIED - 0 is reserved for ACTION_UNSPECIFIED */
    UNSPECIFIED = 0,
    /** MINT - 1 is reserved for MINT */
    MINT = 1,
    /** RECEIVE - 2 is reserved for RECEIVE */
    RECEIVE = 2,
    /** BURN - 4 is reserved for BURN */
    BURN = 4,
    /** SEND - 8 is reserved for SEND */
    SEND = 8,
    /** SUPER_BURN - 16 is reserved for SUPER_BURN */
    SUPER_BURN = 16,
    /** MODIFY_POLICY_MANAGERS - 2^27 is reserved for MODIFY_POLICY_MANAGERS */
    MODIFY_POLICY_MANAGERS = 134217728,
    /** MODIFY_CONTRACT_HOOK - 2^28 is reserved for MODIFY_CONTRACT_HOOK */
    MODIFY_CONTRACT_HOOK = 268435456,
    /** MODIFY_ROLE_PERMISSIONS - 2^29 is reserved for MODIFY_ROLE_PERMISSIONS */
    MODIFY_ROLE_PERMISSIONS = 536870912,
    /** MODIFY_ROLE_MANAGERS - 2^30 is reserved for MODIFY_ROLE_MANAGERS */
    MODIFY_ROLE_MANAGERS = 1073741824,
    UNRECOGNIZED = -1
}
export declare function actionFromJSON(object: any): Action;
export declare function actionToJSON(object: Action): string;
/** Namespace defines a permissions namespace */
export interface Namespace {
    /** tokenfactory denom to which this namespace applies to */
    denom: string;
    /** address of smart contract to apply code-based restrictions */
    contractHook: string;
    /** permissions for each role */
    rolePermissions: Role[];
    /** roles for each actor */
    actorRoles: ActorRoles[];
    /** managers for each role */
    roleManagers: RoleManager[];
    /** status for each policy */
    policyStatuses: PolicyStatus[];
    /** capabilities for each manager for each policy */
    policyManagerCapabilities: PolicyManagerCapability[];
}
/** AddressRoles defines roles for an actor */
export interface ActorRoles {
    actor: string;
    roles: string[];
}
/** RoleActors defines actors for a role */
export interface RoleActors {
    role: string;
    actors: string[];
}
/** RoleManager defines roles for a manager address */
export interface RoleManager {
    manager: string;
    roles: string[];
}
/** PolicyStatus defines the status of a policy */
export interface PolicyStatus {
    action: Action;
    isDisabled: boolean;
    isSealed: boolean;
}
/** Role is only used for storage */
export interface Role {
    name: string;
    roleId: number;
    permissions: number;
}
/** PolicyManagerCapability defines the capabilities of a manager for a policy */
export interface PolicyManagerCapability {
    manager: string;
    action: Action;
    canDisable: boolean;
    canSeal: boolean;
}
/** used in storage */
export interface RoleIDs {
    roleIds: number[];
}
/** AddressVoucher is used to represent a voucher for a specific address */
export interface AddressVoucher {
    address: string;
    voucher: Coin | undefined;
}
export declare const Namespace: {
    encode(message: Namespace, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Namespace;
    fromJSON(object: any): Namespace;
    toJSON(message: Namespace): unknown;
    create(base?: DeepPartial<Namespace>): Namespace;
    fromPartial(object: DeepPartial<Namespace>): Namespace;
};
export declare const ActorRoles: {
    encode(message: ActorRoles, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ActorRoles;
    fromJSON(object: any): ActorRoles;
    toJSON(message: ActorRoles): unknown;
    create(base?: DeepPartial<ActorRoles>): ActorRoles;
    fromPartial(object: DeepPartial<ActorRoles>): ActorRoles;
};
export declare const RoleActors: {
    encode(message: RoleActors, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RoleActors;
    fromJSON(object: any): RoleActors;
    toJSON(message: RoleActors): unknown;
    create(base?: DeepPartial<RoleActors>): RoleActors;
    fromPartial(object: DeepPartial<RoleActors>): RoleActors;
};
export declare const RoleManager: {
    encode(message: RoleManager, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RoleManager;
    fromJSON(object: any): RoleManager;
    toJSON(message: RoleManager): unknown;
    create(base?: DeepPartial<RoleManager>): RoleManager;
    fromPartial(object: DeepPartial<RoleManager>): RoleManager;
};
export declare const PolicyStatus: {
    encode(message: PolicyStatus, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PolicyStatus;
    fromJSON(object: any): PolicyStatus;
    toJSON(message: PolicyStatus): unknown;
    create(base?: DeepPartial<PolicyStatus>): PolicyStatus;
    fromPartial(object: DeepPartial<PolicyStatus>): PolicyStatus;
};
export declare const Role: {
    encode(message: Role, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Role;
    fromJSON(object: any): Role;
    toJSON(message: Role): unknown;
    create(base?: DeepPartial<Role>): Role;
    fromPartial(object: DeepPartial<Role>): Role;
};
export declare const PolicyManagerCapability: {
    encode(message: PolicyManagerCapability, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PolicyManagerCapability;
    fromJSON(object: any): PolicyManagerCapability;
    toJSON(message: PolicyManagerCapability): unknown;
    create(base?: DeepPartial<PolicyManagerCapability>): PolicyManagerCapability;
    fromPartial(object: DeepPartial<PolicyManagerCapability>): PolicyManagerCapability;
};
export declare const RoleIDs: {
    encode(message: RoleIDs, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RoleIDs;
    fromJSON(object: any): RoleIDs;
    toJSON(message: RoleIDs): unknown;
    create(base?: DeepPartial<RoleIDs>): RoleIDs;
    fromPartial(object: DeepPartial<RoleIDs>): RoleIDs;
};
export declare const AddressVoucher: {
    encode(message: AddressVoucher, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AddressVoucher;
    fromJSON(object: any): AddressVoucher;
    toJSON(message: AddressVoucher): unknown;
    create(base?: DeepPartial<AddressVoucher>): AddressVoucher;
    fromPartial(object: DeepPartial<AddressVoucher>): AddressVoucher;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
