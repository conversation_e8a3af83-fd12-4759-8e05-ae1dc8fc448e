import{a as o,b as r,c as s}from"./chunk-AMAPBD4D.mjs";import{a as t}from"./chunk-V2QSMVJ5.mjs";var a=class{constructor(e){this.config=e}async getTableItem(e){return o({aptosConfig:this.config,...e})}async getTableItemsData(e){return await t({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"default_processor"}),r({aptosConfig:this.config,...e})}async getTableItemsMetadata(e){return await t({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"default_processor"}),s({aptosConfig:this.config,...e})}};export{a};
//# sourceMappingURL=chunk-U7HD6PQV.mjs.map