{"version": 3, "file": "integers.js", "sourceRoot": "", "sources": ["../src/integers.ts"], "names": [], "mappings": ";;;;;;AAAA,+BAA+B;AAC/B,kDAAuB;AAEvB,MAAM,cAAc,GAAG,IAAI,eAAE,CAAC,sBAAsB,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAsBhE,MAAa,MAAM;IACjB,uCAAuC;IAChC,MAAM,CAAC,kBAAkB,CAAC,KAAwB;QACvD,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,SAAS,CAAC,KAAwB,EAAE,YAAyB,IAAI;QAC7E,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACjE,MAAM,IAAI,KAAK,CAAC,gCAAgC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9D;SACF;QAED,MAAM,OAAO,GAAG,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;QAEzE,8EAA8E;QAC9E,oEAAoE;QACpE,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACpG,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,GAAW;QAClC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAID,YAAmB,KAAa;QAC9B,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QAED,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,UAAU,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;SACnE;QAED,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACpB,CAAC;IAEM,gBAAgB;QACrB,uEAAuE;QACvE,oEAAoE;QACpE,OAAO,IAAI,UAAU,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI;YACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI;SACtC,CAAC,CAAC;IACL,CAAC;IAEM,mBAAmB;QACxB,uEAAuE;QACvE,oEAAoE;QACpE,OAAO,IAAI,UAAU,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI;YACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI;YACrC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI;SACvC,CAAC,CAAC;IACL,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEM,QAAQ;QACb,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;CACF;AAxFD,wBAwFC;AAED,MAAa,KAAK;IACT,MAAM,CAAC,UAAU,CAAC,GAAW;QAClC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QAED,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC;IAID,YAAmB,KAAa;QAC9B,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QAED,IAAI,KAAK,GAAG,MAAM,CAAC,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,gBAAgB,EAAE;YACtE,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;SAClE;QAED,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IACpB,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEM,QAAQ;QACb,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;CACF;AAtCD,sBAsCC;AAED,MAAa,MAAM;IACV,MAAM,CAAC,UAAU,CAAC,GAAW;QAClC,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrC,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvC,CAAC;IAID,YAAmB,KAAa;QAC9B,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACtC;QACD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;IACrB,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAEM,QAAQ;QACb,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;CACF;AA3BD,wBA2BC;AAED,MAAa,MAAM;IACjB,uCAAuC;IAChC,MAAM,CAAC,kBAAkB,CAAC,KAAwB;QACvD,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,SAAS,CAAC,KAAwB,EAAE,YAAyB,IAAI;QAC7E,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBACjE,MAAM,IAAI,KAAK,CAAC,gCAAgC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9D;SACF;QAED,MAAM,OAAO,GAAG,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;QACrF,OAAO,IAAI,MAAM,CAAC,IAAI,eAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IACrC,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,GAAW;QAClC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,MAAM,CAAC,IAAI,eAAE,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,KAAa;QACpC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QAED,IAAI,MAAU,CAAC;QACf,IAAI;YACF,MAAM,GAAG,IAAI,eAAE,CAAC,KAAK,CAAC,CAAC;SACxB;QAAC,MAAM;YACN,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QACD,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAID,YAAoB,IAAQ;QAC1B,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACtC;QACD,IAAI,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC/C;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAEM,gBAAgB;QACrB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,mBAAmB;QACxB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IAEM,QAAQ;QACb,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjC,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;CACF;AAnFD,wBAmFC;AAED,sGAAsG;AACtG,sEAAsE;AACtE,MAAM,WAAW,GAAyB,KAAK,CAAC;AAChD,MAAM,YAAY,GAA0B,MAAM,CAAC;AACnD,MAAM,YAAY,GAA6D,MAAM,CAAC;AACtF,MAAM,YAAY,GAA6D,MAAM,CAAC"}