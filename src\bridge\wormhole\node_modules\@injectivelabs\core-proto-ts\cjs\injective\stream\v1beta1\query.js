"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.StreamStreamDesc = exports.StreamDesc = exports.StreamClientImpl = exports.OraclePriceFilter = exports.SubaccountDepositsFilter = exports.BankBalancesFilter = exports.OrderbookFilter = exports.OrdersFilter = exports.PositionsFilter = exports.TradesFilter = exports.DerivativeTrade = exports.SpotTrade = exports.OraclePrice = exports.Position = exports.DerivativeOrder = exports.DerivativeOrderUpdate = exports.SpotOrder = exports.SpotOrderUpdate = exports.SubaccountDeposit = exports.SubaccountDeposits = exports.BankBalance = exports.Orderbook = exports.OrderbookUpdate = exports.StreamResponse = exports.StreamRequest = exports.OrderUpdateStatus = exports.protobufPackage = void 0;
exports.orderUpdateStatusFromJSON = orderUpdateStatusFromJSON;
exports.orderUpdateStatusToJSON = orderUpdateStatusToJSON;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var rxjs_1 = require("rxjs");
var operators_1 = require("rxjs/operators");
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var exchange_1 = require("../../exchange/v1beta1/exchange.js");
exports.protobufPackage = "injective.stream.v1beta1";
var OrderUpdateStatus;
(function (OrderUpdateStatus) {
    OrderUpdateStatus[OrderUpdateStatus["Unspecified"] = 0] = "Unspecified";
    OrderUpdateStatus[OrderUpdateStatus["Booked"] = 1] = "Booked";
    OrderUpdateStatus[OrderUpdateStatus["Matched"] = 2] = "Matched";
    OrderUpdateStatus[OrderUpdateStatus["Cancelled"] = 3] = "Cancelled";
    OrderUpdateStatus[OrderUpdateStatus["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(OrderUpdateStatus || (exports.OrderUpdateStatus = OrderUpdateStatus = {}));
function orderUpdateStatusFromJSON(object) {
    switch (object) {
        case 0:
        case "Unspecified":
            return OrderUpdateStatus.Unspecified;
        case 1:
        case "Booked":
            return OrderUpdateStatus.Booked;
        case 2:
        case "Matched":
            return OrderUpdateStatus.Matched;
        case 3:
        case "Cancelled":
            return OrderUpdateStatus.Cancelled;
        case -1:
        case "UNRECOGNIZED":
        default:
            return OrderUpdateStatus.UNRECOGNIZED;
    }
}
function orderUpdateStatusToJSON(object) {
    switch (object) {
        case OrderUpdateStatus.Unspecified:
            return "Unspecified";
        case OrderUpdateStatus.Booked:
            return "Booked";
        case OrderUpdateStatus.Matched:
            return "Matched";
        case OrderUpdateStatus.Cancelled:
            return "Cancelled";
        case OrderUpdateStatus.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseStreamRequest() {
    return {
        bankBalancesFilter: undefined,
        subaccountDepositsFilter: undefined,
        spotTradesFilter: undefined,
        derivativeTradesFilter: undefined,
        spotOrdersFilter: undefined,
        derivativeOrdersFilter: undefined,
        spotOrderbooksFilter: undefined,
        derivativeOrderbooksFilter: undefined,
        positionsFilter: undefined,
        oraclePriceFilter: undefined,
    };
}
exports.StreamRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bankBalancesFilter !== undefined) {
            exports.BankBalancesFilter.encode(message.bankBalancesFilter, writer.uint32(10).fork()).ldelim();
        }
        if (message.subaccountDepositsFilter !== undefined) {
            exports.SubaccountDepositsFilter.encode(message.subaccountDepositsFilter, writer.uint32(18).fork()).ldelim();
        }
        if (message.spotTradesFilter !== undefined) {
            exports.TradesFilter.encode(message.spotTradesFilter, writer.uint32(26).fork()).ldelim();
        }
        if (message.derivativeTradesFilter !== undefined) {
            exports.TradesFilter.encode(message.derivativeTradesFilter, writer.uint32(34).fork()).ldelim();
        }
        if (message.spotOrdersFilter !== undefined) {
            exports.OrdersFilter.encode(message.spotOrdersFilter, writer.uint32(42).fork()).ldelim();
        }
        if (message.derivativeOrdersFilter !== undefined) {
            exports.OrdersFilter.encode(message.derivativeOrdersFilter, writer.uint32(50).fork()).ldelim();
        }
        if (message.spotOrderbooksFilter !== undefined) {
            exports.OrderbookFilter.encode(message.spotOrderbooksFilter, writer.uint32(58).fork()).ldelim();
        }
        if (message.derivativeOrderbooksFilter !== undefined) {
            exports.OrderbookFilter.encode(message.derivativeOrderbooksFilter, writer.uint32(66).fork()).ldelim();
        }
        if (message.positionsFilter !== undefined) {
            exports.PositionsFilter.encode(message.positionsFilter, writer.uint32(74).fork()).ldelim();
        }
        if (message.oraclePriceFilter !== undefined) {
            exports.OraclePriceFilter.encode(message.oraclePriceFilter, writer.uint32(82).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseStreamRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bankBalancesFilter = exports.BankBalancesFilter.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.subaccountDepositsFilter = exports.SubaccountDepositsFilter.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.spotTradesFilter = exports.TradesFilter.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.derivativeTradesFilter = exports.TradesFilter.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.spotOrdersFilter = exports.OrdersFilter.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.derivativeOrdersFilter = exports.OrdersFilter.decode(reader, reader.uint32());
                    break;
                case 7:
                    message.spotOrderbooksFilter = exports.OrderbookFilter.decode(reader, reader.uint32());
                    break;
                case 8:
                    message.derivativeOrderbooksFilter = exports.OrderbookFilter.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.positionsFilter = exports.PositionsFilter.decode(reader, reader.uint32());
                    break;
                case 10:
                    message.oraclePriceFilter = exports.OraclePriceFilter.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bankBalancesFilter: isSet(object.bankBalancesFilter)
                ? exports.BankBalancesFilter.fromJSON(object.bankBalancesFilter)
                : undefined,
            subaccountDepositsFilter: isSet(object.subaccountDepositsFilter)
                ? exports.SubaccountDepositsFilter.fromJSON(object.subaccountDepositsFilter)
                : undefined,
            spotTradesFilter: isSet(object.spotTradesFilter) ? exports.TradesFilter.fromJSON(object.spotTradesFilter) : undefined,
            derivativeTradesFilter: isSet(object.derivativeTradesFilter)
                ? exports.TradesFilter.fromJSON(object.derivativeTradesFilter)
                : undefined,
            spotOrdersFilter: isSet(object.spotOrdersFilter) ? exports.OrdersFilter.fromJSON(object.spotOrdersFilter) : undefined,
            derivativeOrdersFilter: isSet(object.derivativeOrdersFilter)
                ? exports.OrdersFilter.fromJSON(object.derivativeOrdersFilter)
                : undefined,
            spotOrderbooksFilter: isSet(object.spotOrderbooksFilter)
                ? exports.OrderbookFilter.fromJSON(object.spotOrderbooksFilter)
                : undefined,
            derivativeOrderbooksFilter: isSet(object.derivativeOrderbooksFilter)
                ? exports.OrderbookFilter.fromJSON(object.derivativeOrderbooksFilter)
                : undefined,
            positionsFilter: isSet(object.positionsFilter) ? exports.PositionsFilter.fromJSON(object.positionsFilter) : undefined,
            oraclePriceFilter: isSet(object.oraclePriceFilter)
                ? exports.OraclePriceFilter.fromJSON(object.oraclePriceFilter)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bankBalancesFilter !== undefined && (obj.bankBalancesFilter = message.bankBalancesFilter
            ? exports.BankBalancesFilter.toJSON(message.bankBalancesFilter)
            : undefined);
        message.subaccountDepositsFilter !== undefined && (obj.subaccountDepositsFilter = message.subaccountDepositsFilter
            ? exports.SubaccountDepositsFilter.toJSON(message.subaccountDepositsFilter)
            : undefined);
        message.spotTradesFilter !== undefined &&
            (obj.spotTradesFilter = message.spotTradesFilter ? exports.TradesFilter.toJSON(message.spotTradesFilter) : undefined);
        message.derivativeTradesFilter !== undefined && (obj.derivativeTradesFilter = message.derivativeTradesFilter
            ? exports.TradesFilter.toJSON(message.derivativeTradesFilter)
            : undefined);
        message.spotOrdersFilter !== undefined &&
            (obj.spotOrdersFilter = message.spotOrdersFilter ? exports.OrdersFilter.toJSON(message.spotOrdersFilter) : undefined);
        message.derivativeOrdersFilter !== undefined && (obj.derivativeOrdersFilter = message.derivativeOrdersFilter
            ? exports.OrdersFilter.toJSON(message.derivativeOrdersFilter)
            : undefined);
        message.spotOrderbooksFilter !== undefined && (obj.spotOrderbooksFilter = message.spotOrderbooksFilter
            ? exports.OrderbookFilter.toJSON(message.spotOrderbooksFilter)
            : undefined);
        message.derivativeOrderbooksFilter !== undefined &&
            (obj.derivativeOrderbooksFilter = message.derivativeOrderbooksFilter
                ? exports.OrderbookFilter.toJSON(message.derivativeOrderbooksFilter)
                : undefined);
        message.positionsFilter !== undefined &&
            (obj.positionsFilter = message.positionsFilter ? exports.PositionsFilter.toJSON(message.positionsFilter) : undefined);
        message.oraclePriceFilter !== undefined && (obj.oraclePriceFilter = message.oraclePriceFilter
            ? exports.OraclePriceFilter.toJSON(message.oraclePriceFilter)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.StreamRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseStreamRequest();
        message.bankBalancesFilter = (object.bankBalancesFilter !== undefined && object.bankBalancesFilter !== null)
            ? exports.BankBalancesFilter.fromPartial(object.bankBalancesFilter)
            : undefined;
        message.subaccountDepositsFilter =
            (object.subaccountDepositsFilter !== undefined && object.subaccountDepositsFilter !== null)
                ? exports.SubaccountDepositsFilter.fromPartial(object.subaccountDepositsFilter)
                : undefined;
        message.spotTradesFilter = (object.spotTradesFilter !== undefined && object.spotTradesFilter !== null)
            ? exports.TradesFilter.fromPartial(object.spotTradesFilter)
            : undefined;
        message.derivativeTradesFilter =
            (object.derivativeTradesFilter !== undefined && object.derivativeTradesFilter !== null)
                ? exports.TradesFilter.fromPartial(object.derivativeTradesFilter)
                : undefined;
        message.spotOrdersFilter = (object.spotOrdersFilter !== undefined && object.spotOrdersFilter !== null)
            ? exports.OrdersFilter.fromPartial(object.spotOrdersFilter)
            : undefined;
        message.derivativeOrdersFilter =
            (object.derivativeOrdersFilter !== undefined && object.derivativeOrdersFilter !== null)
                ? exports.OrdersFilter.fromPartial(object.derivativeOrdersFilter)
                : undefined;
        message.spotOrderbooksFilter = (object.spotOrderbooksFilter !== undefined && object.spotOrderbooksFilter !== null)
            ? exports.OrderbookFilter.fromPartial(object.spotOrderbooksFilter)
            : undefined;
        message.derivativeOrderbooksFilter =
            (object.derivativeOrderbooksFilter !== undefined && object.derivativeOrderbooksFilter !== null)
                ? exports.OrderbookFilter.fromPartial(object.derivativeOrderbooksFilter)
                : undefined;
        message.positionsFilter = (object.positionsFilter !== undefined && object.positionsFilter !== null)
            ? exports.PositionsFilter.fromPartial(object.positionsFilter)
            : undefined;
        message.oraclePriceFilter = (object.oraclePriceFilter !== undefined && object.oraclePriceFilter !== null)
            ? exports.OraclePriceFilter.fromPartial(object.oraclePriceFilter)
            : undefined;
        return message;
    },
};
function createBaseStreamResponse() {
    return {
        blockHeight: "0",
        blockTime: "0",
        bankBalances: [],
        subaccountDeposits: [],
        spotTrades: [],
        derivativeTrades: [],
        spotOrders: [],
        derivativeOrders: [],
        spotOrderbookUpdates: [],
        derivativeOrderbookUpdates: [],
        positions: [],
        oraclePrices: [],
    };
}
exports.StreamResponse = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h, e_9, _j, e_10, _k;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.blockHeight !== "0") {
            writer.uint32(8).uint64(message.blockHeight);
        }
        if (message.blockTime !== "0") {
            writer.uint32(16).int64(message.blockTime);
        }
        try {
            for (var _l = __values(message.bankBalances), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                exports.BankBalance.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_a = _l.return)) _a.call(_l);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _o = __values(message.subaccountDeposits), _p = _o.next(); !_p.done; _p = _o.next()) {
                var v = _p.value;
                exports.SubaccountDeposits.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_p && !_p.done && (_b = _o.return)) _b.call(_o);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _q = __values(message.spotTrades), _r = _q.next(); !_r.done; _r = _q.next()) {
                var v = _r.value;
                exports.SpotTrade.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_r && !_r.done && (_c = _q.return)) _c.call(_q);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _s = __values(message.derivativeTrades), _t = _s.next(); !_t.done; _t = _s.next()) {
                var v = _t.value;
                exports.DerivativeTrade.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_t && !_t.done && (_d = _s.return)) _d.call(_s);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _u = __values(message.spotOrders), _v = _u.next(); !_v.done; _v = _u.next()) {
                var v = _v.value;
                exports.SpotOrderUpdate.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_v && !_v.done && (_e = _u.return)) _e.call(_u);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _w = __values(message.derivativeOrders), _x = _w.next(); !_x.done; _x = _w.next()) {
                var v = _x.value;
                exports.DerivativeOrderUpdate.encode(v, writer.uint32(66).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_x && !_x.done && (_f = _w.return)) _f.call(_w);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _y = __values(message.spotOrderbookUpdates), _z = _y.next(); !_z.done; _z = _y.next()) {
                var v = _z.value;
                exports.OrderbookUpdate.encode(v, writer.uint32(74).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_z && !_z.done && (_g = _y.return)) _g.call(_y);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _0 = __values(message.derivativeOrderbookUpdates), _1 = _0.next(); !_1.done; _1 = _0.next()) {
                var v = _1.value;
                exports.OrderbookUpdate.encode(v, writer.uint32(82).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_1 && !_1.done && (_h = _0.return)) _h.call(_0);
            }
            finally { if (e_8) throw e_8.error; }
        }
        try {
            for (var _2 = __values(message.positions), _3 = _2.next(); !_3.done; _3 = _2.next()) {
                var v = _3.value;
                exports.Position.encode(v, writer.uint32(90).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_3 && !_3.done && (_j = _2.return)) _j.call(_2);
            }
            finally { if (e_9) throw e_9.error; }
        }
        try {
            for (var _4 = __values(message.oraclePrices), _5 = _4.next(); !_5.done; _5 = _4.next()) {
                var v = _5.value;
                exports.OraclePrice.encode(v, writer.uint32(98).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_5 && !_5.done && (_k = _4.return)) _k.call(_4);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseStreamResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.blockHeight = longToString(reader.uint64());
                    break;
                case 2:
                    message.blockTime = longToString(reader.int64());
                    break;
                case 3:
                    message.bankBalances.push(exports.BankBalance.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.subaccountDeposits.push(exports.SubaccountDeposits.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.spotTrades.push(exports.SpotTrade.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.derivativeTrades.push(exports.DerivativeTrade.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.spotOrders.push(exports.SpotOrderUpdate.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.derivativeOrders.push(exports.DerivativeOrderUpdate.decode(reader, reader.uint32()));
                    break;
                case 9:
                    message.spotOrderbookUpdates.push(exports.OrderbookUpdate.decode(reader, reader.uint32()));
                    break;
                case 10:
                    message.derivativeOrderbookUpdates.push(exports.OrderbookUpdate.decode(reader, reader.uint32()));
                    break;
                case 11:
                    message.positions.push(exports.Position.decode(reader, reader.uint32()));
                    break;
                case 12:
                    message.oraclePrices.push(exports.OraclePrice.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            blockTime: isSet(object.blockTime) ? String(object.blockTime) : "0",
            bankBalances: Array.isArray(object === null || object === void 0 ? void 0 : object.bankBalances)
                ? object.bankBalances.map(function (e) { return exports.BankBalance.fromJSON(e); })
                : [],
            subaccountDeposits: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountDeposits)
                ? object.subaccountDeposits.map(function (e) { return exports.SubaccountDeposits.fromJSON(e); })
                : [],
            spotTrades: Array.isArray(object === null || object === void 0 ? void 0 : object.spotTrades) ? object.spotTrades.map(function (e) { return exports.SpotTrade.fromJSON(e); }) : [],
            derivativeTrades: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeTrades)
                ? object.derivativeTrades.map(function (e) { return exports.DerivativeTrade.fromJSON(e); })
                : [],
            spotOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.spotOrders)
                ? object.spotOrders.map(function (e) { return exports.SpotOrderUpdate.fromJSON(e); })
                : [],
            derivativeOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeOrders)
                ? object.derivativeOrders.map(function (e) { return exports.DerivativeOrderUpdate.fromJSON(e); })
                : [],
            spotOrderbookUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.spotOrderbookUpdates)
                ? object.spotOrderbookUpdates.map(function (e) { return exports.OrderbookUpdate.fromJSON(e); })
                : [],
            derivativeOrderbookUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeOrderbookUpdates)
                ? object.derivativeOrderbookUpdates.map(function (e) { return exports.OrderbookUpdate.fromJSON(e); })
                : [],
            positions: Array.isArray(object === null || object === void 0 ? void 0 : object.positions) ? object.positions.map(function (e) { return exports.Position.fromJSON(e); }) : [],
            oraclePrices: Array.isArray(object === null || object === void 0 ? void 0 : object.oraclePrices)
                ? object.oraclePrices.map(function (e) { return exports.OraclePrice.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.blockTime !== undefined && (obj.blockTime = message.blockTime);
        if (message.bankBalances) {
            obj.bankBalances = message.bankBalances.map(function (e) { return e ? exports.BankBalance.toJSON(e) : undefined; });
        }
        else {
            obj.bankBalances = [];
        }
        if (message.subaccountDeposits) {
            obj.subaccountDeposits = message.subaccountDeposits.map(function (e) { return e ? exports.SubaccountDeposits.toJSON(e) : undefined; });
        }
        else {
            obj.subaccountDeposits = [];
        }
        if (message.spotTrades) {
            obj.spotTrades = message.spotTrades.map(function (e) { return e ? exports.SpotTrade.toJSON(e) : undefined; });
        }
        else {
            obj.spotTrades = [];
        }
        if (message.derivativeTrades) {
            obj.derivativeTrades = message.derivativeTrades.map(function (e) { return e ? exports.DerivativeTrade.toJSON(e) : undefined; });
        }
        else {
            obj.derivativeTrades = [];
        }
        if (message.spotOrders) {
            obj.spotOrders = message.spotOrders.map(function (e) { return e ? exports.SpotOrderUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.spotOrders = [];
        }
        if (message.derivativeOrders) {
            obj.derivativeOrders = message.derivativeOrders.map(function (e) { return e ? exports.DerivativeOrderUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.derivativeOrders = [];
        }
        if (message.spotOrderbookUpdates) {
            obj.spotOrderbookUpdates = message.spotOrderbookUpdates.map(function (e) { return e ? exports.OrderbookUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.spotOrderbookUpdates = [];
        }
        if (message.derivativeOrderbookUpdates) {
            obj.derivativeOrderbookUpdates = message.derivativeOrderbookUpdates.map(function (e) {
                return e ? exports.OrderbookUpdate.toJSON(e) : undefined;
            });
        }
        else {
            obj.derivativeOrderbookUpdates = [];
        }
        if (message.positions) {
            obj.positions = message.positions.map(function (e) { return e ? exports.Position.toJSON(e) : undefined; });
        }
        else {
            obj.positions = [];
        }
        if (message.oraclePrices) {
            obj.oraclePrices = message.oraclePrices.map(function (e) { return e ? exports.OraclePrice.toJSON(e) : undefined; });
        }
        else {
            obj.oraclePrices = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.StreamResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        var message = createBaseStreamResponse();
        message.blockHeight = (_a = object.blockHeight) !== null && _a !== void 0 ? _a : "0";
        message.blockTime = (_b = object.blockTime) !== null && _b !== void 0 ? _b : "0";
        message.bankBalances = ((_c = object.bankBalances) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.BankBalance.fromPartial(e); })) || [];
        message.subaccountDeposits = ((_d = object.subaccountDeposits) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.SubaccountDeposits.fromPartial(e); })) || [];
        message.spotTrades = ((_e = object.spotTrades) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.SpotTrade.fromPartial(e); })) || [];
        message.derivativeTrades = ((_f = object.derivativeTrades) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.DerivativeTrade.fromPartial(e); })) || [];
        message.spotOrders = ((_g = object.spotOrders) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.SpotOrderUpdate.fromPartial(e); })) || [];
        message.derivativeOrders = ((_h = object.derivativeOrders) === null || _h === void 0 ? void 0 : _h.map(function (e) { return exports.DerivativeOrderUpdate.fromPartial(e); })) || [];
        message.spotOrderbookUpdates = ((_j = object.spotOrderbookUpdates) === null || _j === void 0 ? void 0 : _j.map(function (e) { return exports.OrderbookUpdate.fromPartial(e); })) || [];
        message.derivativeOrderbookUpdates =
            ((_k = object.derivativeOrderbookUpdates) === null || _k === void 0 ? void 0 : _k.map(function (e) { return exports.OrderbookUpdate.fromPartial(e); })) || [];
        message.positions = ((_l = object.positions) === null || _l === void 0 ? void 0 : _l.map(function (e) { return exports.Position.fromPartial(e); })) || [];
        message.oraclePrices = ((_m = object.oraclePrices) === null || _m === void 0 ? void 0 : _m.map(function (e) { return exports.OraclePrice.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseOrderbookUpdate() {
    return { seq: "0", orderbook: undefined };
}
exports.OrderbookUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.seq !== "0") {
            writer.uint32(8).uint64(message.seq);
        }
        if (message.orderbook !== undefined) {
            exports.Orderbook.encode(message.orderbook, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderbookUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.seq = longToString(reader.uint64());
                    break;
                case 2:
                    message.orderbook = exports.Orderbook.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            seq: isSet(object.seq) ? String(object.seq) : "0",
            orderbook: isSet(object.orderbook) ? exports.Orderbook.fromJSON(object.orderbook) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.seq !== undefined && (obj.seq = message.seq);
        message.orderbook !== undefined &&
            (obj.orderbook = message.orderbook ? exports.Orderbook.toJSON(message.orderbook) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.OrderbookUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseOrderbookUpdate();
        message.seq = (_a = object.seq) !== null && _a !== void 0 ? _a : "0";
        message.orderbook = (object.orderbook !== undefined && object.orderbook !== null)
            ? exports.Orderbook.fromPartial(object.orderbook)
            : undefined;
        return message;
    },
};
function createBaseOrderbook() {
    return { marketId: "", buyLevels: [], sellLevels: [] };
}
exports.Orderbook = {
    encode: function (message, writer) {
        var e_11, _a, e_12, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        try {
            for (var _c = __values(message.buyLevels), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.Level.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_11) throw e_11.error; }
        }
        try {
            for (var _e = __values(message.sellLevels), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.Level.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderbook();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.buyLevels.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.sellLevels.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            buyLevels: Array.isArray(object === null || object === void 0 ? void 0 : object.buyLevels) ? object.buyLevels.map(function (e) { return exchange_1.Level.fromJSON(e); }) : [],
            sellLevels: Array.isArray(object === null || object === void 0 ? void 0 : object.sellLevels) ? object.sellLevels.map(function (e) { return exchange_1.Level.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        if (message.buyLevels) {
            obj.buyLevels = message.buyLevels.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.buyLevels = [];
        }
        if (message.sellLevels) {
            obj.sellLevels = message.sellLevels.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.sellLevels = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Orderbook.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseOrderbook();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.buyLevels = ((_b = object.buyLevels) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        message.sellLevels = ((_c = object.sellLevels) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseBankBalance() {
    return { account: "", balances: [] };
}
exports.BankBalance = {
    encode: function (message, writer) {
        var e_13, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        try {
            for (var _b = __values(message.balances), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                coin_1.Coin.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_13) throw e_13.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBankBalance();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.balances.push(coin_1.Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            balances: Array.isArray(object === null || object === void 0 ? void 0 : object.balances) ? object.balances.map(function (e) { return coin_1.Coin.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        if (message.balances) {
            obj.balances = message.balances.map(function (e) { return e ? coin_1.Coin.toJSON(e) : undefined; });
        }
        else {
            obj.balances = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BankBalance.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseBankBalance();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.balances = ((_b = object.balances) === null || _b === void 0 ? void 0 : _b.map(function (e) { return coin_1.Coin.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSubaccountDeposits() {
    return { subaccountId: "", deposits: [] };
}
exports.SubaccountDeposits = {
    encode: function (message, writer) {
        var e_14, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.deposits), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.SubaccountDeposit.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_14) throw e_14.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountDeposits();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.deposits.push(exports.SubaccountDeposit.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            deposits: Array.isArray(object === null || object === void 0 ? void 0 : object.deposits) ? object.deposits.map(function (e) { return exports.SubaccountDeposit.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.deposits) {
            obj.deposits = message.deposits.map(function (e) { return e ? exports.SubaccountDeposit.toJSON(e) : undefined; });
        }
        else {
            obj.deposits = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.SubaccountDeposits.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseSubaccountDeposits();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.deposits = ((_b = object.deposits) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.SubaccountDeposit.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSubaccountDeposit() {
    return { denom: "", deposit: undefined };
}
exports.SubaccountDeposit = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.deposit !== undefined) {
            exchange_1.Deposit.encode(message.deposit, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountDeposit();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.deposit = exchange_1.Deposit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            deposit: isSet(object.deposit) ? exchange_1.Deposit.fromJSON(object.deposit) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.deposit !== undefined && (obj.deposit = message.deposit ? exchange_1.Deposit.toJSON(message.deposit) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.SubaccountDeposit.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountDeposit();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.deposit = (object.deposit !== undefined && object.deposit !== null)
            ? exchange_1.Deposit.fromPartial(object.deposit)
            : undefined;
        return message;
    },
};
function createBaseSpotOrderUpdate() {
    return { status: 0, orderHash: "", cid: "", order: undefined };
}
exports.SpotOrderUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== 0) {
            writer.uint32(8).int32(message.status);
        }
        if (message.orderHash !== "") {
            writer.uint32(18).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(26).string(message.cid);
        }
        if (message.order !== undefined) {
            exports.SpotOrder.encode(message.order, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotOrderUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.int32();
                    break;
                case 2:
                    message.orderHash = reader.string();
                    break;
                case 3:
                    message.cid = reader.string();
                    break;
                case 4:
                    message.order = exports.SpotOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            status: isSet(object.status) ? orderUpdateStatusFromJSON(object.status) : 0,
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            order: isSet(object.order) ? exports.SpotOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = orderUpdateStatusToJSON(message.status));
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        message.order !== undefined && (obj.order = message.order ? exports.SpotOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.SpotOrderUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSpotOrderUpdate();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : 0;
        message.orderHash = (_b = object.orderHash) !== null && _b !== void 0 ? _b : "";
        message.cid = (_c = object.cid) !== null && _c !== void 0 ? _c : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exports.SpotOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseSpotOrder() {
    return { marketId: "", order: undefined };
}
exports.SpotOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.order !== undefined) {
            exchange_1.SpotLimitOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.SpotLimitOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            order: isSet(object.order) ? exchange_1.SpotLimitOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.order !== undefined && (obj.order = message.order ? exchange_1.SpotLimitOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.SpotOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSpotOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.SpotLimitOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseDerivativeOrderUpdate() {
    return { status: 0, orderHash: "", cid: "", order: undefined };
}
exports.DerivativeOrderUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== 0) {
            writer.uint32(8).int32(message.status);
        }
        if (message.orderHash !== "") {
            writer.uint32(18).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(26).string(message.cid);
        }
        if (message.order !== undefined) {
            exports.DerivativeOrder.encode(message.order, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeOrderUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.int32();
                    break;
                case 2:
                    message.orderHash = reader.string();
                    break;
                case 3:
                    message.cid = reader.string();
                    break;
                case 4:
                    message.order = exports.DerivativeOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            status: isSet(object.status) ? orderUpdateStatusFromJSON(object.status) : 0,
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            order: isSet(object.order) ? exports.DerivativeOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = orderUpdateStatusToJSON(message.status));
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        message.order !== undefined && (obj.order = message.order ? exports.DerivativeOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeOrderUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseDerivativeOrderUpdate();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : 0;
        message.orderHash = (_b = object.orderHash) !== null && _b !== void 0 ? _b : "";
        message.cid = (_c = object.cid) !== null && _c !== void 0 ? _c : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exports.DerivativeOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseDerivativeOrder() {
    return { marketId: "", order: undefined, isMarket: false };
}
exports.DerivativeOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.order !== undefined) {
            exchange_1.DerivativeLimitOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        if (message.isMarket === true) {
            writer.uint32(24).bool(message.isMarket);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.isMarket = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            order: isSet(object.order) ? exchange_1.DerivativeLimitOrder.fromJSON(object.order) : undefined,
            isMarket: isSet(object.isMarket) ? Boolean(object.isMarket) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.order !== undefined && (obj.order = message.order ? exchange_1.DerivativeLimitOrder.toJSON(message.order) : undefined);
        message.isMarket !== undefined && (obj.isMarket = message.isMarket);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDerivativeOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.DerivativeLimitOrder.fromPartial(object.order)
            : undefined;
        message.isMarket = (_b = object.isMarket) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBasePosition() {
    return {
        marketId: "",
        subaccountId: "",
        isLong: false,
        quantity: "",
        entryPrice: "",
        margin: "",
        cumulativeFundingEntry: "",
    };
}
exports.Position = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.isLong === true) {
            writer.uint32(24).bool(message.isLong);
        }
        if (message.quantity !== "") {
            writer.uint32(34).string(message.quantity);
        }
        if (message.entryPrice !== "") {
            writer.uint32(42).string(message.entryPrice);
        }
        if (message.margin !== "") {
            writer.uint32(50).string(message.margin);
        }
        if (message.cumulativeFundingEntry !== "") {
            writer.uint32(58).string(message.cumulativeFundingEntry);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePosition();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.isLong = reader.bool();
                    break;
                case 4:
                    message.quantity = reader.string();
                    break;
                case 5:
                    message.entryPrice = reader.string();
                    break;
                case 6:
                    message.margin = reader.string();
                    break;
                case 7:
                    message.cumulativeFundingEntry = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            isLong: isSet(object.isLong) ? Boolean(object.isLong) : false,
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            entryPrice: isSet(object.entryPrice) ? String(object.entryPrice) : "",
            margin: isSet(object.margin) ? String(object.margin) : "",
            cumulativeFundingEntry: isSet(object.cumulativeFundingEntry) ? String(object.cumulativeFundingEntry) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.isLong !== undefined && (obj.isLong = message.isLong);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.entryPrice !== undefined && (obj.entryPrice = message.entryPrice);
        message.margin !== undefined && (obj.margin = message.margin);
        message.cumulativeFundingEntry !== undefined && (obj.cumulativeFundingEntry = message.cumulativeFundingEntry);
        return obj;
    },
    create: function (base) {
        return exports.Position.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBasePosition();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.isLong = (_c = object.isLong) !== null && _c !== void 0 ? _c : false;
        message.quantity = (_d = object.quantity) !== null && _d !== void 0 ? _d : "";
        message.entryPrice = (_e = object.entryPrice) !== null && _e !== void 0 ? _e : "";
        message.margin = (_f = object.margin) !== null && _f !== void 0 ? _f : "";
        message.cumulativeFundingEntry = (_g = object.cumulativeFundingEntry) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseOraclePrice() {
    return { symbol: "", price: "", type: "" };
}
exports.OraclePrice = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        if (message.type !== "") {
            writer.uint32(26).string(message.type);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOraclePrice();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                case 3:
                    message.type = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            price: isSet(object.price) ? String(object.price) : "",
            type: isSet(object.type) ? String(object.type) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.price !== undefined && (obj.price = message.price);
        message.type !== undefined && (obj.type = message.type);
        return obj;
    },
    create: function (base) {
        return exports.OraclePrice.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseOraclePrice();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "";
        message.type = (_c = object.type) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseSpotTrade() {
    return {
        marketId: "",
        isBuy: false,
        executionType: "",
        quantity: "",
        price: "",
        subaccountId: "",
        fee: "",
        orderHash: "",
        feeRecipientAddress: "",
        cid: "",
        tradeId: "",
    };
}
exports.SpotTrade = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(16).bool(message.isBuy);
        }
        if (message.executionType !== "") {
            writer.uint32(26).string(message.executionType);
        }
        if (message.quantity !== "") {
            writer.uint32(34).string(message.quantity);
        }
        if (message.price !== "") {
            writer.uint32(42).string(message.price);
        }
        if (message.subaccountId !== "") {
            writer.uint32(50).string(message.subaccountId);
        }
        if (message.fee !== "") {
            writer.uint32(58).string(message.fee);
        }
        if (message.orderHash !== "") {
            writer.uint32(66).string(message.orderHash);
        }
        if (message.feeRecipientAddress !== "") {
            writer.uint32(74).string(message.feeRecipientAddress);
        }
        if (message.cid !== "") {
            writer.uint32(82).string(message.cid);
        }
        if (message.tradeId !== "") {
            writer.uint32(90).string(message.tradeId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotTrade();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuy = reader.bool();
                    break;
                case 3:
                    message.executionType = reader.string();
                    break;
                case 4:
                    message.quantity = reader.string();
                    break;
                case 5:
                    message.price = reader.string();
                    break;
                case 6:
                    message.subaccountId = reader.string();
                    break;
                case 7:
                    message.fee = reader.string();
                    break;
                case 8:
                    message.orderHash = reader.string();
                    break;
                case 9:
                    message.feeRecipientAddress = reader.string();
                    break;
                case 10:
                    message.cid = reader.string();
                    break;
                case 11:
                    message.tradeId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            executionType: isSet(object.executionType) ? String(object.executionType) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            price: isSet(object.price) ? String(object.price) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            fee: isSet(object.fee) ? String(object.fee) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            feeRecipientAddress: isSet(object.feeRecipientAddress) ? String(object.feeRecipientAddress) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            tradeId: isSet(object.tradeId) ? String(object.tradeId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.executionType !== undefined && (obj.executionType = message.executionType);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.price !== undefined && (obj.price = message.price);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.fee !== undefined && (obj.fee = message.fee);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.feeRecipientAddress !== undefined && (obj.feeRecipientAddress = message.feeRecipientAddress);
        message.cid !== undefined && (obj.cid = message.cid);
        message.tradeId !== undefined && (obj.tradeId = message.tradeId);
        return obj;
    },
    create: function (base) {
        return exports.SpotTrade.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        var message = createBaseSpotTrade();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuy = (_b = object.isBuy) !== null && _b !== void 0 ? _b : false;
        message.executionType = (_c = object.executionType) !== null && _c !== void 0 ? _c : "";
        message.quantity = (_d = object.quantity) !== null && _d !== void 0 ? _d : "";
        message.price = (_e = object.price) !== null && _e !== void 0 ? _e : "";
        message.subaccountId = (_f = object.subaccountId) !== null && _f !== void 0 ? _f : "";
        message.fee = (_g = object.fee) !== null && _g !== void 0 ? _g : "";
        message.orderHash = (_h = object.orderHash) !== null && _h !== void 0 ? _h : "";
        message.feeRecipientAddress = (_j = object.feeRecipientAddress) !== null && _j !== void 0 ? _j : "";
        message.cid = (_k = object.cid) !== null && _k !== void 0 ? _k : "";
        message.tradeId = (_l = object.tradeId) !== null && _l !== void 0 ? _l : "";
        return message;
    },
};
function createBaseDerivativeTrade() {
    return {
        marketId: "",
        isBuy: false,
        executionType: "",
        subaccountId: "",
        positionDelta: undefined,
        payout: "",
        fee: "",
        orderHash: "",
        feeRecipientAddress: "",
        cid: "",
        tradeId: "",
    };
}
exports.DerivativeTrade = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(16).bool(message.isBuy);
        }
        if (message.executionType !== "") {
            writer.uint32(26).string(message.executionType);
        }
        if (message.subaccountId !== "") {
            writer.uint32(34).string(message.subaccountId);
        }
        if (message.positionDelta !== undefined) {
            exchange_1.PositionDelta.encode(message.positionDelta, writer.uint32(42).fork()).ldelim();
        }
        if (message.payout !== "") {
            writer.uint32(50).string(message.payout);
        }
        if (message.fee !== "") {
            writer.uint32(58).string(message.fee);
        }
        if (message.orderHash !== "") {
            writer.uint32(66).string(message.orderHash);
        }
        if (message.feeRecipientAddress !== "") {
            writer.uint32(74).string(message.feeRecipientAddress);
        }
        if (message.cid !== "") {
            writer.uint32(82).string(message.cid);
        }
        if (message.tradeId !== "") {
            writer.uint32(90).string(message.tradeId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeTrade();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuy = reader.bool();
                    break;
                case 3:
                    message.executionType = reader.string();
                    break;
                case 4:
                    message.subaccountId = reader.string();
                    break;
                case 5:
                    message.positionDelta = exchange_1.PositionDelta.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.payout = reader.string();
                    break;
                case 7:
                    message.fee = reader.string();
                    break;
                case 8:
                    message.orderHash = reader.string();
                    break;
                case 9:
                    message.feeRecipientAddress = reader.string();
                    break;
                case 10:
                    message.cid = reader.string();
                    break;
                case 11:
                    message.tradeId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            executionType: isSet(object.executionType) ? String(object.executionType) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            positionDelta: isSet(object.positionDelta) ? exchange_1.PositionDelta.fromJSON(object.positionDelta) : undefined,
            payout: isSet(object.payout) ? String(object.payout) : "",
            fee: isSet(object.fee) ? String(object.fee) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            feeRecipientAddress: isSet(object.feeRecipientAddress) ? String(object.feeRecipientAddress) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            tradeId: isSet(object.tradeId) ? String(object.tradeId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.executionType !== undefined && (obj.executionType = message.executionType);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.positionDelta !== undefined &&
            (obj.positionDelta = message.positionDelta ? exchange_1.PositionDelta.toJSON(message.positionDelta) : undefined);
        message.payout !== undefined && (obj.payout = message.payout);
        message.fee !== undefined && (obj.fee = message.fee);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.feeRecipientAddress !== undefined && (obj.feeRecipientAddress = message.feeRecipientAddress);
        message.cid !== undefined && (obj.cid = message.cid);
        message.tradeId !== undefined && (obj.tradeId = message.tradeId);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeTrade.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        var message = createBaseDerivativeTrade();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuy = (_b = object.isBuy) !== null && _b !== void 0 ? _b : false;
        message.executionType = (_c = object.executionType) !== null && _c !== void 0 ? _c : "";
        message.subaccountId = (_d = object.subaccountId) !== null && _d !== void 0 ? _d : "";
        message.positionDelta = (object.positionDelta !== undefined && object.positionDelta !== null)
            ? exchange_1.PositionDelta.fromPartial(object.positionDelta)
            : undefined;
        message.payout = (_e = object.payout) !== null && _e !== void 0 ? _e : "";
        message.fee = (_f = object.fee) !== null && _f !== void 0 ? _f : "";
        message.orderHash = (_g = object.orderHash) !== null && _g !== void 0 ? _g : "";
        message.feeRecipientAddress = (_h = object.feeRecipientAddress) !== null && _h !== void 0 ? _h : "";
        message.cid = (_j = object.cid) !== null && _j !== void 0 ? _j : "";
        message.tradeId = (_k = object.tradeId) !== null && _k !== void 0 ? _k : "";
        return message;
    },
};
function createBaseTradesFilter() {
    return { subaccountIds: [], marketIds: [] };
}
exports.TradesFilter = {
    encode: function (message, writer) {
        var e_15, _a, e_16, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.subaccountIds), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_15) throw e_15.error; }
        }
        try {
            for (var _e = __values(message.marketIds), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_16) throw e_16.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradesFilter();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountIds: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountIds) ? object.subaccountIds.map(function (e) { return String(e); }) : [],
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map(function (e) { return e; });
        }
        else {
            obj.subaccountIds = [];
        }
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradesFilter.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseTradesFilter();
        message.subaccountIds = ((_a = object.subaccountIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBasePositionsFilter() {
    return { subaccountIds: [], marketIds: [] };
}
exports.PositionsFilter = {
    encode: function (message, writer) {
        var e_17, _a, e_18, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.subaccountIds), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_17) throw e_17.error; }
        }
        try {
            for (var _e = __values(message.marketIds), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_18_1) { e_18 = { error: e_18_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_18) throw e_18.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePositionsFilter();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountIds: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountIds) ? object.subaccountIds.map(function (e) { return String(e); }) : [],
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map(function (e) { return e; });
        }
        else {
            obj.subaccountIds = [];
        }
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.PositionsFilter.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePositionsFilter();
        message.subaccountIds = ((_a = object.subaccountIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseOrdersFilter() {
    return { subaccountIds: [], marketIds: [] };
}
exports.OrdersFilter = {
    encode: function (message, writer) {
        var e_19, _a, e_20, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.subaccountIds), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_19_1) { e_19 = { error: e_19_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_19) throw e_19.error; }
        }
        try {
            for (var _e = __values(message.marketIds), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_20_1) { e_20 = { error: e_20_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_20) throw e_20.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrdersFilter();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountIds: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountIds) ? object.subaccountIds.map(function (e) { return String(e); }) : [],
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map(function (e) { return e; });
        }
        else {
            obj.subaccountIds = [];
        }
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.OrdersFilter.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseOrdersFilter();
        message.subaccountIds = ((_a = object.subaccountIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseOrderbookFilter() {
    return { marketIds: [] };
}
exports.OrderbookFilter = {
    encode: function (message, writer) {
        var e_21, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_21_1) { e_21 = { error: e_21_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_21) throw e_21.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderbookFilter();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.OrderbookFilter.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseOrderbookFilter();
        message.marketIds = ((_a = object.marketIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseBankBalancesFilter() {
    return { accounts: [] };
}
exports.BankBalancesFilter = {
    encode: function (message, writer) {
        var e_22, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.accounts), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_22_1) { e_22 = { error: e_22_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_22) throw e_22.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBankBalancesFilter();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accounts.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { accounts: Array.isArray(object === null || object === void 0 ? void 0 : object.accounts) ? object.accounts.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accounts) {
            obj.accounts = message.accounts.map(function (e) { return e; });
        }
        else {
            obj.accounts = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BankBalancesFilter.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseBankBalancesFilter();
        message.accounts = ((_a = object.accounts) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseSubaccountDepositsFilter() {
    return { subaccountIds: [] };
}
exports.SubaccountDepositsFilter = {
    encode: function (message, writer) {
        var e_23, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.subaccountIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_23_1) { e_23 = { error: e_23_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_23) throw e_23.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountDepositsFilter();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountIds: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountIds) ? object.subaccountIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map(function (e) { return e; });
        }
        else {
            obj.subaccountIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.SubaccountDepositsFilter.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountDepositsFilter();
        message.subaccountIds = ((_a = object.subaccountIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseOraclePriceFilter() {
    return { symbol: [] };
}
exports.OraclePriceFilter = {
    encode: function (message, writer) {
        var e_24, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.symbol), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_24_1) { e_24 = { error: e_24_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_24) throw e_24.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOraclePriceFilter();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { symbol: Array.isArray(object === null || object === void 0 ? void 0 : object.symbol) ? object.symbol.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.symbol) {
            obj.symbol = message.symbol.map(function (e) { return e; });
        }
        else {
            obj.symbol = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.OraclePriceFilter.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseOraclePriceFilter();
        message.symbol = ((_a = object.symbol) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
var StreamClientImpl = /** @class */ (function () {
    function StreamClientImpl(rpc) {
        this.rpc = rpc;
        this.Stream = this.Stream.bind(this);
    }
    StreamClientImpl.prototype.Stream = function (request, metadata) {
        return this.rpc.invoke(exports.StreamStreamDesc, exports.StreamRequest.fromPartial(request), metadata);
    };
    return StreamClientImpl;
}());
exports.StreamClientImpl = StreamClientImpl;
exports.StreamDesc = { serviceName: "injective.stream.v1beta1.Stream" };
exports.StreamStreamDesc = {
    methodName: "Stream",
    service: exports.StreamDesc,
    requestStream: false,
    responseStream: true,
    requestType: {
        serializeBinary: function () {
            return exports.StreamRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.StreamResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    GrpcWebImpl.prototype.invoke = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var upStreamCodes = this.options.upStreamRetryCodes || [];
        var DEFAULT_TIMEOUT_TIME = 3000;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new rxjs_1.Observable(function (observer) {
            var upStream = (function () {
                var client = grpc_web_1.grpc.invoke(methodDesc, {
                    host: _this.host,
                    request: request,
                    transport: _this.options.streamingTransport || _this.options.transport,
                    metadata: maybeCombinedMetadata,
                    debug: _this.options.debug,
                    onMessage: function (next) { return observer.next(next); },
                    onEnd: function (code, message, trailers) {
                        if (code === 0) {
                            observer.complete();
                        }
                        else if (upStreamCodes.includes(code)) {
                            setTimeout(upStream, DEFAULT_TIMEOUT_TIME);
                        }
                        else {
                            var err = new Error(message);
                            err.code = code;
                            err.metadata = trailers;
                            observer.error(err);
                        }
                    },
                });
                observer.add(function () {
                    if (!observer.closed) {
                        return client.close();
                    }
                });
            });
            upStream();
        }).pipe((0, operators_1.share)());
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
