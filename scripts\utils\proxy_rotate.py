#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理轮换测试工具

该脚本用于测试代理自动轮换功能，可以模拟高频请求场景。
"""

import os
import sys
import time
import argparse
import logging
import requests
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.utils.proxy import ProxyManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('proxy_rotate')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='代理轮换测试工具')
    
    parser.add_argument('-f', '--file', required=True,
                        help='代理列表文件路径，每行一个代理URL')
    
    parser.add_argument('-u', '--url', default='https://api.binance.com/api/v3/time',
                        help='用于测试的API URL，默认为Binance API')
    
    parser.add_argument('-c', '--count', type=int, default=10,
                        help='请求次数，默认为10次')
    
    parser.add_argument('-i', '--interval', type=float, default=1.0,
                        help='请求间隔(秒)，默认为1秒')
    
    parser.add_argument('-r', '--rotate', type=int, default=5,
                        help='代理轮换间隔(次数)，默认为5次请求后轮换')
    
    parser.add_argument('-t', '--timeout', type=int, default=5,
                        help='请求超时时间(秒)，默认为5秒')
    
    return parser.parse_args()

def make_request(url, proxy=None, timeout=5):
    """发送HTTP请求并返回结果"""
    start_time = time.time()
    proxy_dict = {"http": proxy, "https": proxy} if proxy else None
    
    try:
        response = requests.get(url, proxies=proxy_dict, timeout=timeout)
        elapsed = time.time() - start_time
        return {
            "success": True,
            "status_code": response.status_code,
            "elapsed": elapsed,
            "proxy": proxy
        }
    except Exception as e:
        elapsed = time.time() - start_time
        return {
            "success": False,
            "error": str(e),
            "elapsed": elapsed,
            "proxy": proxy
        }

def test_proxy_rotation(proxy_manager, args):
    """测试代理轮换功能"""
    request_count = 0
    success_count = 0
    failure_count = 0
    
    print(f"\n开始测试代理轮换，共 {args.count} 次请求，间隔 {args.interval} 秒")
    print(f"每 {args.rotate} 次请求后强制轮换代理\n")
    
    print(f"{'#':<4} {'时间':<19} {'代理':<30} {'状态':<6} {'耗时(秒)':<10}")
    print("-" * 70)
    
    while request_count < args.count:
        # 每指定次数强制轮换代理
        if request_count > 0 and request_count % args.rotate == 0:
            logger.info(f"强制轮换代理，已完成 {request_count} 次请求")
            proxy_manager.get_proxy(force_rotate=True)
        
        # 获取当前代理
        proxy = proxy_manager.get_proxy()
        
        # 发送请求
        result = make_request(args.url, proxy, args.timeout)
        request_count += 1
        
        # 记录结果
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        proxy_str = proxy[:30] if proxy else "无代理"
        
        if result["success"]:
            status = f"{result['status_code']}"
            success_count += 1
        else:
            status = "失败"
            failure_count += 1
        
        print(f"{request_count:<4} {current_time} {proxy_str:<30} {status:<6} {result['elapsed']:.4f}")
        
        # 等待指定间隔
        if request_count < args.count:
            time.sleep(args.interval)
    
    # 打印统计信息
    success_rate = (success_count / args.count) * 100
    print("\n" + "=" * 70)
    print(f"测试完成: 共 {args.count} 次请求，成功 {success_count} 次，失败 {failure_count} 次")
    print(f"成功率: {success_rate:.2f}%")
    print("=" * 70)

def main():
    """主函数"""
    args = parse_args()
    
    if not os.path.exists(args.file):
        logger.error(f"代理文件不存在: {args.file}")
        return 1
    
    # 创建代理管理器，设置轮换间隔为1秒方便测试
    proxy_manager = ProxyManager(args.file, rotation_interval=1)
    
    if not proxy_manager.proxies:
        logger.error("没有找到可用的代理")
        return 1
    
    logger.info(f"加载了 {len(proxy_manager.proxies)} 个代理")
    
    # 测试代理轮换
    test_proxy_rotation(proxy_manager, args)
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 