{"version": 3, "file": "genesisState.js", "sourceRoot": "", "sources": ["../../../src/util/genesisState.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AACrC,2CAMyB;AACzB,+DAA2D;AAE3D,wCAAiC;AAIjC;;GAEG;AACI,KAAK,UAAU,gBAAgB,CAAC,YAA0B;IAC/D,MAAM,IAAI,GAAG,IAAI,cAAI,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;IAC9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;QACvD,MAAM,OAAO,GAAG,IAAA,kBAAW,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAU,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,2BAAoB,EAAC,GAAG,CAAC,CAAA;QAC9E,MAAM,OAAO,GAAG,IAAI,cAAO,EAAE,CAAA;QAC7B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;SAChC;aAAM;YACL,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,KAA8B,CAAA;YACtE,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;aAClC;YACD,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,MAAM,SAAS,GAAG,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAU,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,2BAAoB,EAAC,IAAI,CAAC,CAAA;gBACnF,OAAO,CAAC,QAAQ,GAAG,IAAA,qBAAS,EAAC,SAAS,CAAC,CAAA;aACxC;YACD,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,MAAM,WAAW,GAAG,IAAI,cAAI,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;gBACrD,KAAK,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE;oBAC9B,MAAM,UAAU,GAAG,IAAA,kBAAW,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAU,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,2BAAoB,EAAC,CAAC,CAAC,CAAA;oBAC3E,MAAM,UAAU,GAAG,SAAG,CAAC,MAAM,CAC3B,IAAA,iBAAU,EAAC,IAAA,kBAAW,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAU,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,2BAAoB,EAAC,GAAG,CAAC,CAAC,CAC3E,CAAA;oBACD,MAAM,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;iBAC9C;gBACD,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAA;aACzC;YACD,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;aAC9B;SACF;QACD,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;KAC7C;IACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAA;AACpB,CAAC;AAlCD,4CAkCC"}