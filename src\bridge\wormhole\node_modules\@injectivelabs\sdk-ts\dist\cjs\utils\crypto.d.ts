import { TypedMessage, MessageTypes, SignTypedDataVersion } from '@metamask/eth-sig-util';
export declare const hashToHex: (data: string) => string;
export declare const sha256: (data: Uint8Array) => Uint8Array;
export declare const ripemd160: (data: Uint8Array) => Uint8Array;
export declare const privateKeyToPublicKey: (privateKey: Uint8Array) => Uint8Array;
export declare const privateKeyHashToPublicKey: (privateKeyHash: string) => Uint8Array;
export declare const privateKeyToPublicKeyBase64: (privateKey: Uint8Array) => string;
export declare const privateKeyHashToPublicKeyBase64: (privateKeyHash: string) => string;
export declare const domainHash: (message: any) => Buffer<ArrayBufferLike>;
export declare const messageHash: (message: any) => Buffer<ArrayBufferLike>;
export declare function uint8ArrayToHex(arr: Uint8Array): string;
export declare function hexToUnit8Array(str: string): Uint8Array<ArrayBuffer>;
export declare function decompressPubKey(startsWith02Or03: string): string;
export declare const publicKeyToAddress: (pubKey: Uint8Array, sanitize?: boolean) => Uint8Array;
export declare const sanitizeTypedData: <T extends object | any[] | bigint | string | number | boolean | null | undefined>(data: T) => T;
export declare const TypedDataUtilsSanitizeData: <T extends MessageTypes>(data: TypedMessage<T>) => TypedMessage<T>;
export declare const TypedDataUtilsHashStruct: (primaryType: string, data: Record<string, unknown>, types: Record<string, import("@metamask/eth-sig-util").MessageTypeProperty[]>, version: SignTypedDataVersion.V3 | SignTypedDataVersion.V4) => Buffer;
export declare const SignTypedDataVersionV4 = SignTypedDataVersion.V4;
export type TypedMessageV4 = TypedMessage<MessageTypes>;
