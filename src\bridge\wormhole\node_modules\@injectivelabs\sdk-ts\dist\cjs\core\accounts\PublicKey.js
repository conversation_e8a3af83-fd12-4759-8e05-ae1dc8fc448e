"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicKey = void 0;
const index_js_1 = require("../../utils/index.js");
const bech32_1 = require("bech32");
const ethereumjs_util_1 = require("ethereumjs-util");
const secp256k1_1 = __importDefault(require("secp256k1"));
const Address_js_1 = require("./Address.js");
const keccak256_1 = __importDefault(require("keccak256"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Crypto Utility Classes
 */
class PublicKey {
    type;
    key;
    constructor(key, type) {
        this.key = key;
        this.type = type || '/injective.crypto.v1beta1.ethsecp256k1.PubKey';
    }
    static fromBase64(publicKey) {
        return new PublicKey(Buffer.from(publicKey, 'base64'));
    }
    static fromBytes(publicKey) {
        return new PublicKey(publicKey);
    }
    static fromHex(pubKey) {
        const pubKeyHex = pubKey.startsWith('0x') ? pubKey.slice(2) : pubKey;
        return new PublicKey(Buffer.from(pubKeyHex.toString(), 'hex'));
    }
    static fromPrivateKeyHex(privateKey) {
        const isString = typeof privateKey === 'string';
        const privateKeyHex = isString && privateKey.startsWith('0x') ? privateKey.slice(2) : privateKey;
        const privateKeyBuff = Buffer.from(privateKeyHex.toString(), 'hex');
        const publicKeyByte = secp256k1_1.default.publicKeyCreate(privateKeyBuff, true);
        const type = '/injective.crypto.v1beta1.ethsecp256k1.PubKey';
        return new PublicKey(publicKeyByte, type);
    }
    toPubKeyBytes() {
        return this.key;
    }
    toBase64() {
        return Buffer.from(this.toPubKeyBytes()).toString('base64');
    }
    toHex() {
        return Buffer.from(this.toPubKeyBytes()).toString('hex');
    }
    /**
     * Convert the public key to a pubkey in bech32 format.
     * Note: this does not convert the public key to an address.
     */
    toBech32() {
        return bech32_1.bech32.encode(index_js_1.BECH32_PUBKEY_ACC_PREFIX, bech32_1.bech32.toWords(Buffer.from(this.toHex(), 'hex')));
    }
    toAddress() {
        const publicKeyHex = this.toHex();
        const decompressedPublicKey = (0, index_js_1.decompressPubKey)(publicKeyHex);
        const addressBuffer = (0, keccak256_1.default)((0, ethereumjs_util_1.toBuffer)(decompressedPublicKey.startsWith('0x')
            ? decompressedPublicKey
            : '0x' + decompressedPublicKey)).subarray(-20);
        return Address_js_1.Address.fromHex(Buffer.from(addressBuffer).toString('hex').toLowerCase());
    }
    toProto() {
        const proto = core_proto_ts_1.InjectiveCryptoV1Beta1Ethsecp256k1Keys.PubKey.create();
        proto.key = this.key;
        return proto;
    }
    toAny() {
        const proto = this.toProto();
        const message = core_proto_ts_1.GoogleProtobufAny.Any.create();
        message.typeUrl = this.type;
        message.value = Buffer.from(core_proto_ts_1.InjectiveCryptoV1Beta1Ethsecp256k1Keys.PubKey.encode(proto).finish());
        return message;
    }
}
exports.PublicKey = PublicKey;
