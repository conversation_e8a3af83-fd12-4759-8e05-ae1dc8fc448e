"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const numbers_js_1 = require("../../../../utils/numbers.js");
const createMessage = (params) => {
    const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgAdminUpdateBinaryOptionsMarket.create();
    message.sender = params.sender;
    message.marketId = params.marketId;
    message.settlementPrice = params.settlementPrice;
    message.expirationTimestamp = params.expirationTimestamp;
    message.settlementTimestamp = params.settlementTimestamp;
    message.status = params.status;
    return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgAdminUpdateBinaryOptionsMarket.fromPartial(message);
};
/**
 * @category Messages
 */
class MsgAdminUpdateBinaryOptionsMarket extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgAdminUpdateBinaryOptionsMarket(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            settlementPrice: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.settlementPrice).toFixed(),
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgAdminUpdateBinaryOptionsMarket',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const message = {
            ...(0, snakecase_keys_1.default)(createMessage(params)),
        };
        return {
            type: 'exchange/MsgAdminUpdateBinaryOptionsMarket',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgAdminUpdateBinaryOptionsMarket',
            ...value,
        };
    }
    toEip712() {
        const amino = this.toAmino();
        const { type, value } = amino;
        const messageAdjusted = {
            ...value,
            settlement_price: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.settlement_price).toFixed(),
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const messageAdjusted = {
            ...web3gw,
            settlement_price: (0, numbers_js_1.numberToCosmosSdkDecString)(params.settlementPrice),
            status: core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.marketStatusToJSON(params.status),
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgAdminUpdateBinaryOptionsMarket',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgAdminUpdateBinaryOptionsMarket.encode(this.toProto()).finish();
    }
}
exports.default = MsgAdminUpdateBinaryOptionsMarket;
