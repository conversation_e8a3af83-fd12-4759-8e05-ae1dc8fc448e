import _m0 from "protobufjs/minimal.js";
import { Block } from "../../../types/v1/block";
import { BlockID } from "../../../types/v1/types";
export declare const protobufPackage = "cometbft.services.block.v1";
/** GetByHeightRequest is a request for a block at the specified height. */
export interface GetByHeightRequest {
    /** The height of the block requested. */
    height: string;
}
/** GetByHeightResponse contains the block ID and the block at the specified height. */
export interface GetByHeightResponse {
    blockId: BlockID | undefined;
    block: Block | undefined;
}
/** GetLatestHeightRequest - empty message since no parameter is required */
export interface GetLatestHeightRequest {
}
/** GetLatestHeightResponse provides the height of the latest committed block. */
export interface GetLatestHeightResponse {
    /**
     * The height of the latest committed block. Will be 0 if no data has been
     * committed yet.
     */
    height: string;
}
export declare const GetByHeightRequest: {
    encode(message: GetByHeightRe<PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetByHeightRequest;
    fromJSON(object: any): GetByHeightRequest;
    toJSON(message: GetByHeightRequest): unknown;
    create(base?: DeepPartial<GetByHeightRequest>): GetByHeightRequest;
    fromPartial(object: DeepPartial<GetByHeightRequest>): GetByHeightRequest;
};
export declare const GetByHeightResponse: {
    encode(message: GetByHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetByHeightResponse;
    fromJSON(object: any): GetByHeightResponse;
    toJSON(message: GetByHeightResponse): unknown;
    create(base?: DeepPartial<GetByHeightResponse>): GetByHeightResponse;
    fromPartial(object: DeepPartial<GetByHeightResponse>): GetByHeightResponse;
};
export declare const GetLatestHeightRequest: {
    encode(_: GetLatestHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetLatestHeightRequest;
    fromJSON(_: any): GetLatestHeightRequest;
    toJSON(_: GetLatestHeightRequest): unknown;
    create(base?: DeepPartial<GetLatestHeightRequest>): GetLatestHeightRequest;
    fromPartial(_: DeepPartial<GetLatestHeightRequest>): GetLatestHeightRequest;
};
export declare const GetLatestHeightResponse: {
    encode(message: GetLatestHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetLatestHeightResponse;
    fromJSON(object: any): GetLatestHeightResponse;
    toJSON(message: GetLatestHeightResponse): unknown;
    create(base?: DeepPartial<GetLatestHeightResponse>): GetLatestHeightResponse;
    fromPartial(object: DeepPartial<GetLatestHeightResponse>): GetLatestHeightResponse;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
