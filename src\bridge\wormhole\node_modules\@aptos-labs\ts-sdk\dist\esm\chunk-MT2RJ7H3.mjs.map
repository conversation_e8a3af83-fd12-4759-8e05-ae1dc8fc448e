{"version": 3, "sources": ["../../src/bcs/serializable/fixedBytes.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../serializer\";\nimport { Deserializer } from \"../deserializer\";\nimport { HexInput } from \"../../types\";\nimport { Hex } from \"../../core/hex\";\nimport { TransactionArgument } from \"../../transactions/instances/transactionArgument\";\n\n/**\n * Represents a contiguous sequence of already serialized BCS bytes.\n * \n * This class differs from most other Serializable classes in that its internal byte buffer is serialized to BCS\n * bytes exactly as-is, without prepending the length of the bytes. It is ideal for scenarios where custom serialization\n * is required, such as passing serialized bytes as transaction arguments. Additionally, it serves as a representation \n * of type-agnostic BCS bytes, akin to a vector<u8>.\n * \n * An example use case includes handling bytes resulting from entry function arguments that have been serialized \n * for an entry function.\n * \n * @example\n * const yourCustomSerializedBytes = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);\n * const fixedBytes = new FixedBytes(yourCustomSerializedBytes);\n * const payload = await generateTransactionPayload({\n *   function: \"0xbeefcafe::your_module::your_function_that_requires_custom_serialization\",\n *   functionArguments: [yourCustomBytes],\n * });\n * \n * This class is particularly useful when you want to handle a fixed-size byte array without the overhead of \n * length prepending, such as when dealing with 32-byte addresses stored as U8 in a MoveVector<U8>.\n\n *  For example, if you store each of the 32 bytes for an address as a U8 in a MoveVector<U8>, when you\n *  serialize that MoveVector<U8>, it will be serialized to 33 bytes. If you solely want to pass around\n *  the 32 bytes as a Serializable class that *does not* prepend the length to the BCS-serialized representation,\n *  use this class.* \n * @param value - HexInput representing a sequence of Uint8 bytes.\n * @returns A Serializable FixedBytes instance, which when serialized, does not prepend the length of the bytes.\n * @see EntryFunctionBytes\n * @group Implementation\n * @category BCS\n */\nexport class FixedBytes extends Serializable implements TransactionArgument {\n  public value: Uint8Array;\n\n  /**\n   * Creates an instance of the class with a specified hexadecimal input.\n   * The value is converted from hexadecimal format to a Uint8Array.\n   *\n   * @param value - The hexadecimal input to be converted.\n   * @group Implementation\n   * @category BCS\n   */\n  constructor(value: HexInput) {\n    super();\n    this.value = Hex.fromHexInput(value).toUint8Array();\n  }\n\n  /**\n   * Serializes the fixed bytes value using the provided serializer.\n   * This function is essential for converting the fixed bytes into a format suitable for storage or transmission.\n   *\n   * @param serializer - The serializer instance used for serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.value);\n  }\n\n  /**\n   * Serializes the current instance for an entry function using the provided serializer.\n   * This allows the instance to be converted into a format suitable for transmission or storage.\n   *\n   * @param serializer - The serializer used to perform the serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeForEntryFunction(serializer: Serializer): void {\n    serializer.serialize(this);\n  }\n\n  /**\n   * Serializes the current instance using the provided serializer.\n   * This function is essential for preparing data to be passed as arguments in script functions.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serialize(this);\n  }\n\n  /**\n   * Deserializes a fixed-length byte array from the provided deserializer.\n   * This function helps in reconstructing a FixedBytes object from the serialized data.\n   *\n   * @param deserializer - The deserializer instance used to read the byte data.\n   * @param length - The length of the byte array to be deserialized.\n   * @group Implementation\n   * @category BCS\n   */\n  static deserialize(deserializer: Deserializer, length: number): FixedBytes {\n    const bytes = deserializer.deserializeFixedBytes(length);\n    return new FixedBytes(bytes);\n  }\n}\n"], "mappings": "kFAyCO,IAAMA,EAAN,MAAMC,UAAmBC,CAA4C,CAW1E,YAAYC,EAAiB,CAC3B,MAAM,EACN,KAAK,MAAQC,EAAI,aAAaD,CAAK,EAAE,aAAa,CACpD,CAUA,UAAUE,EAA8B,CACtCA,EAAW,oBAAoB,KAAK,KAAK,CAC3C,CAUA,0BAA0BA,EAA8B,CACtDA,EAAW,UAAU,IAAI,CAC3B,CAUA,2BAA2BA,EAA8B,CACvDA,EAAW,UAAU,IAAI,CAC3B,CAWA,OAAO,YAAYC,EAA4BC,EAA4B,CACzE,IAAMC,EAAQF,EAAa,sBAAsBC,CAAM,EACvD,OAAO,IAAIN,EAAWO,CAAK,CAC7B,CACF", "names": ["FixedBytes", "_FixedBytes", "Serializable", "value", "Hex", "serializer", "deserializer", "length", "bytes"]}