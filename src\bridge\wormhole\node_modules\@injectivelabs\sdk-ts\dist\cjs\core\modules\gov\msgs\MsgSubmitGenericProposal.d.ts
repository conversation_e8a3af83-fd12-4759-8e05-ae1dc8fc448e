import { CosmosGovV1Tx, GoogleProtobufAny, CosmosBaseV1Beta1Coin } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import { Msgs } from '../../../../core/modules/msgs.js';
export declare namespace MsgSubmitGenericProposal {
    interface Params {
        title: string;
        summary: string;
        expedited?: boolean;
        proposer: string;
        metadata?: string;
        messages: Msgs[];
        deposit: {
            amount: string;
            denom: string;
        };
    }
    type Proto = CosmosGovV1Tx.MsgSubmitProposal;
}
/**
 * @category Messages
 */
export default class MsgSubmitGenericProposal extends MsgBase<MsgSubmitGenericProposal.Params, MsgSubmitGenericProposal.Proto> {
    static fromJSON(params: MsgSubmitGenericProposal.Params): MsgSubmitGenericProposal;
    toProto(): CosmosGovV1Tx.MsgSubmitProposal;
    toData(): {
        messages: GoogleProtobufAny.Any[];
        initialDeposit: CosmosBaseV1Beta1Coin.Coin[];
        proposer: string;
        metadata: string;
        title: string;
        summary: string;
        expedited: boolean;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: any;
    };
    toWeb3Gw(): any;
    toEip712(): {
        type: string;
        value: any;
    };
    toDirectSign(): {
        type: string;
        message: CosmosGovV1Tx.MsgSubmitProposal;
    };
    toBinary(): Uint8Array;
}
