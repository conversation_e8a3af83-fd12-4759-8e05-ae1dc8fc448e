import { Web3RequestManager } from 'web3-core';
import { Address, BlockNumberOrTag, Filter, HexString32Bytes, HexString8Bytes, HexStringBytes, TransactionCallAPI, TransactionWithSenderAPI, Uint, Uint256, Web3<PERSON>thE<PERSON>cutionAPI, Eip712TypedData } from 'web3-types';
export declare function getProtocolVersion(requestManager: Web3RequestManager): Promise<string>;
export declare function getSyncing(requestManager: Web3RequestManager): Promise<import("web3-types").SyncingStatusAPI>;
export declare function getCoinbase(requestManager: Web3RequestManager): Promise<string>;
export declare function getMining(requestManager: Web3RequestManager): Promise<boolean>;
export declare function getHashRate(requestManager: Web3RequestManager): Promise<string>;
export declare function getGasPrice(requestManager: Web3RequestManager): Promise<string>;
export declare function getMaxPriorityFeePerGas(requestManager: Web3RequestManager): Promise<string>;
export declare function getAccounts(requestManager: Web3RequestManager): Promise<string[]>;
export declare function getBlockNumber(requestManager: Web3RequestManager): Promise<string>;
export declare function getBalance(requestManager: Web3RequestManager, address: Address, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function getStorageAt(requestManager: Web3RequestManager, address: Address, storageSlot: Uint256, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function getTransactionCount(requestManager: Web3RequestManager, address: Address, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function getBlockTransactionCountByHash(requestManager: Web3RequestManager, blockHash: HexString32Bytes): Promise<string>;
export declare function getBlockTransactionCountByNumber(requestManager: Web3RequestManager, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function getUncleCountByBlockHash(requestManager: Web3RequestManager, blockHash: HexString32Bytes): Promise<string>;
export declare function getUncleCountByBlockNumber(requestManager: Web3RequestManager, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function getCode(requestManager: Web3RequestManager, address: Address, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function sign(requestManager: Web3RequestManager, address: Address, message: HexStringBytes): Promise<string>;
export declare function signTransaction(requestManager: Web3RequestManager, transaction: TransactionWithSenderAPI | Partial<TransactionWithSenderAPI>): Promise<string | import("web3-types").SignedTransactionInfoAPI>;
export declare function sendTransaction(requestManager: Web3RequestManager, transaction: TransactionWithSenderAPI | Partial<TransactionWithSenderAPI>): Promise<string>;
export declare function sendRawTransaction(requestManager: Web3RequestManager, transaction: HexStringBytes): Promise<string>;
export declare function call(requestManager: Web3RequestManager, transaction: TransactionCallAPI, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function estimateGas<TransactionType = TransactionWithSenderAPI>(requestManager: Web3RequestManager, transaction: Partial<TransactionType>, blockNumber: BlockNumberOrTag): Promise<string>;
export declare function getBlockByHash(requestManager: Web3RequestManager, blockHash: HexString32Bytes, hydrated: boolean): Promise<import("web3-types").BlockAPI>;
export declare function getBlockByNumber(requestManager: Web3RequestManager, blockNumber: BlockNumberOrTag, hydrated: boolean): Promise<import("web3-types").BlockAPI>;
export declare function getTransactionByHash(requestManager: Web3RequestManager, transactionHash: HexString32Bytes): Promise<import("web3-types").TransactionInfoAPI | undefined>;
export declare function getTransactionByBlockHashAndIndex(requestManager: Web3RequestManager, blockHash: HexString32Bytes, transactionIndex: Uint): Promise<import("web3-types").TransactionInfoAPI | undefined>;
export declare function getTransactionByBlockNumberAndIndex(requestManager: Web3RequestManager, blockNumber: BlockNumberOrTag, transactionIndex: Uint): Promise<import("web3-types").TransactionInfoAPI | undefined>;
export declare function getTransactionReceipt(requestManager: Web3RequestManager, transactionHash: HexString32Bytes): Promise<import("web3-types").TransactionReceiptAPI | undefined>;
export declare function getUncleByBlockHashAndIndex(requestManager: Web3RequestManager, blockHash: HexString32Bytes, uncleIndex: Uint): Promise<import("web3-types").BlockAPI>;
export declare function getUncleByBlockNumberAndIndex(requestManager: Web3RequestManager, blockNumber: BlockNumberOrTag, uncleIndex: Uint): Promise<import("web3-types").BlockAPI>;
export declare function getCompilers(requestManager: Web3RequestManager): Promise<string[]>;
export declare function compileSolidity(requestManager: Web3RequestManager, code: string): Promise<import("web3-types").CompileResultAPI>;
export declare function compileLLL(requestManager: Web3RequestManager, code: string): Promise<string>;
export declare function compileSerpent(requestManager: Web3RequestManager, code: string): Promise<string>;
export declare function newFilter(requestManager: Web3RequestManager, filter: Filter): Promise<string>;
export declare function newBlockFilter(requestManager: Web3RequestManager): Promise<string>;
export declare function newPendingTransactionFilter(requestManager: Web3RequestManager): Promise<string>;
export declare function uninstallFilter(requestManager: Web3RequestManager, filterIdentifier: Uint): Promise<boolean>;
export declare function getFilterChanges(requestManager: Web3RequestManager, filterIdentifier: Uint): Promise<import("web3-types").FilterResultsAPI>;
export declare function getFilterLogs(requestManager: Web3RequestManager, filterIdentifier: Uint): Promise<import("web3-types").FilterResultsAPI>;
export declare function getLogs(requestManager: Web3RequestManager, filter: Filter): Promise<import("web3-types").FilterResultsAPI>;
export declare function getWork(requestManager: Web3RequestManager): Promise<[string, string, string]>;
export declare function submitWork(requestManager: Web3RequestManager, nonce: HexString8Bytes, hash: HexString32Bytes, digest: HexString32Bytes): Promise<boolean>;
export declare function submitHashrate(requestManager: Web3RequestManager, hashRate: HexString32Bytes, id: HexString32Bytes): Promise<boolean>;
export declare function getFeeHistory(requestManager: Web3RequestManager, blockCount: Uint, newestBlock: BlockNumberOrTag, rewardPercentiles: number[]): Promise<import("web3-types").FeeHistoryResultAPI>;
export declare function getPendingTransactions(requestManager: Web3RequestManager<Web3EthExecutionAPI>): Promise<import("web3-types").TransactionInfo[]>;
export declare function requestAccounts(requestManager: Web3RequestManager<Web3EthExecutionAPI>): Promise<string[]>;
export declare function getChainId(requestManager: Web3RequestManager<Web3EthExecutionAPI>): Promise<string>;
export declare function getProof(requestManager: Web3RequestManager<Web3EthExecutionAPI>, address: Address, storageKeys: HexString32Bytes[], blockNumber: BlockNumberOrTag): Promise<import("web3-types").AccountObject>;
export declare function getNodeInfo(requestManager: Web3RequestManager<Web3EthExecutionAPI>): Promise<string>;
export declare function createAccessList(requestManager: Web3RequestManager, transaction: TransactionWithSenderAPI | Partial<TransactionWithSenderAPI>, blockNumber: BlockNumberOrTag): Promise<any>;
export declare function signTypedData(requestManager: Web3RequestManager, address: Address, typedData: Eip712TypedData, useLegacy?: boolean): Promise<string>;
//# sourceMappingURL=eth_rpc_methods.d.ts.map