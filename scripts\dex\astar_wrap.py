#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Astar网络原生代币ASTR与包装代币WASTR互换脚本
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

import argparse
import time
from src.dex.astar.wallet import AstarWallet
from config.config import config  # 直接导入config对象


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Astar网络原生代币ASTR与包装代币WASTR互换')
    parser.add_argument('operation', choices=['wrap', 'unwrap'], help='操作类型: wrap(ASTR->WASTR) 或 unwrap(WASTR->ASTR)')
    parser.add_argument('amount', type=float, help='操作金额')
    parser.add_argument('-w', '--no-wait', action='store_true', help='不等待交易确认')
    parser.add_argument('-g', '--gas-multiplier', type=float, default=1.0, help='gas价格倍数，默认1.0')
    
    args = parser.parse_args()
    
    # 检查金额
    if args.amount <= 0:
        print("错误: 金额必须大于0")
        return 1
    
    # 检查gas倍数
    if args.gas_multiplier <= 0:
        print("错误: gas倍数必须大于0")
        return 1
    
    # 不需要初始化配置，直接使用已加载的config对象
    
    try:
        # 创建钱包实例
        wallet = AstarWallet()
        
        # 设置gas价格倍数
        if args.gas_multiplier != 1.0:
            wallet.set_gas_price_multiplier(args.gas_multiplier)
            print(f"已将gas价格设置为默认值的 {args.gas_multiplier} 倍")
        
        # 显示当前余额
        astr_balance = wallet.get_native_balance()
        wastr_balance = wallet.get_token_balance('WASTR')
        
        print(f"\n钱包地址: {wallet.address}")
        print("=" * 60)
        print("操作前余额:")
        print(f"ASTR:  {astr_balance:.6f}")
        print(f"WASTR: {wastr_balance:.6f}")
        print("-" * 60)
        
        # 执行操作
        tx_hash = None
        if args.operation == 'wrap':
            print(f"将 {args.amount} ASTR 转换为 WASTR...")
            
            # 检查余额是否足够
            if astr_balance < args.amount:
                print(f"错误: ASTR余额不足。当前余额: {astr_balance}, 需要: {args.amount}")
                return 1
                
            tx_hash = wallet.wrap_astr(args.amount)
        else:  # unwrap
            print(f"将 {args.amount} WASTR 转换为 ASTR...")
            
            # 检查余额是否足够
            if wastr_balance < args.amount:
                print(f"错误: WASTR余额不足。当前余额: {wastr_balance}, 需要: {args.amount}")
                return 1
                
            tx_hash = wallet.unwrap_wastr(args.amount)
        
        print(f"交易已提交，交易哈希: {tx_hash}")
        
        # 等待交易确认
        if not args.no_wait:
            print("等待交易确认...")
            receipt = wallet.wait_for_transaction_receipt(tx_hash)
            
            if receipt['status'] == 1:
                print("交易成功!")
            else:
                print("交易失败!")
                return 1
                
            # 显示更新后的余额
            time.sleep(2)  # 等待余额更新
            new_astr_balance = wallet.get_native_balance()
            new_wastr_balance = wallet.get_token_balance('WASTR')
            
            print("-" * 60)
            print("操作后余额:")
            print(f"ASTR:  {new_astr_balance:.6f} ({new_astr_balance - astr_balance:+.6f})")
            print(f"WASTR: {new_wastr_balance:.6f} ({new_wastr_balance - wastr_balance:+.6f})")
        else:
            print("已跳过等待确认。请稍后检查余额变化。")
            
        print("=" * 60)
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return 1
        
    return 0


if __name__ == "__main__":
    sys.exit(main()) 