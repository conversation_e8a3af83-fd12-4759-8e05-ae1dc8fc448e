{"version": 3, "sources": ["../../src/internal/table.ts"], "sourcesContent": ["import { AptosConfig } from \"../api/aptosConfig\";\nimport { postAptosFullNode } from \"../client\";\nimport {\n  TableItemRequest,\n  LedgerVersionArg,\n  PaginationArgs,\n  WhereArg,\n  OrderByArg,\n  GetTableItemsDataResponse,\n  GetTableItemsMetadataResponse,\n} from \"../types\";\nimport { GetTableItemsDataQuery, GetTableItemsMetadataQuery } from \"../types/generated/operations\";\nimport { GetTableItemsData, GetTableItemsMetadata } from \"../types/generated/queries\";\nimport { TableItemsBoolExp, TableMetadatasBoolExp } from \"../types/generated/types\";\nimport { queryIndexer } from \"./general\";\n\n/**\n * Retrieves a specific item from a table in the Aptos blockchain.\n *\n * @param args - The arguments for retrieving the table item.\n * @param args.aptosConfig - The configuration for connecting to the Aptos blockchain.\n * @param args.handle - The identifier for the table from which to retrieve the item.\n * @param args.data - The request data for the table item.\n * @param args.options - Optional parameters for the request, including ledger version.\n * @group Implementation\n */\nexport async function getTableItem<T>(args: {\n  aptosConfig: AptosConfig;\n  handle: string;\n  data: TableItemRequest;\n  options?: LedgerVersionArg;\n}): Promise<T> {\n  const { aptosConfig, handle, data, options } = args;\n  const response = await postAptosFullNode<TableItemRequest, any>({\n    aptosConfig,\n    originMethod: \"getTableItem\",\n    path: `tables/${handle}/item`,\n    params: { ledger_version: options?.ledgerVersion },\n    body: data,\n  });\n  return response.data as T;\n}\n\n/**\n * Retrieves table items data based on specified conditions and pagination options.\n *\n * @param args - The arguments for retrieving table items data.\n * @param args.aptosConfig - The configuration object for Aptos.\n * @param args.options - Optional parameters for pagination and filtering.\n * @param args.options.offset - The number of items to skip before starting to collect the result set.\n * @param args.options.limit - The maximum number of items to return.\n * @param args.options.where - Conditions to filter the table items.\n * @param args.options.orderBy - The criteria to sort the results.\n * @group Implementation\n */\nexport async function getTableItemsData(args: {\n  aptosConfig: AptosConfig;\n  options?: PaginationArgs & WhereArg<TableItemsBoolExp> & OrderByArg<GetTableItemsDataResponse[0]>;\n}) {\n  const { aptosConfig, options } = args;\n\n  const graphqlQuery = {\n    query: GetTableItemsData,\n    variables: {\n      where_condition: options?.where,\n      offset: options?.offset,\n      limit: options?.limit,\n      order_by: options?.orderBy,\n    },\n  };\n\n  const data = await queryIndexer<GetTableItemsDataQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getTableItemsData\",\n  });\n\n  return data.table_items;\n}\n\n/**\n * Retrieves metadata for table items based on specified options.\n *\n * @param args - The arguments for retrieving table items metadata.\n * @param args.aptosConfig - The configuration object for Aptos.\n * @param args.options - Optional parameters for pagination and filtering.\n * @param args.options.offset - The number of items to skip before starting to collect the result set.\n * @param args.options.limit - The maximum number of items to return.\n * @param args.options.where - Conditions to filter the results.\n * @param args.options.orderBy - The order in which to return the results.\n * @returns A promise that resolves to an array of table metadata.\n * @group Implementation\n */\nexport async function getTableItemsMetadata(args: {\n  aptosConfig: AptosConfig;\n  options?: PaginationArgs & WhereArg<TableMetadatasBoolExp> & OrderByArg<GetTableItemsMetadataResponse[0]>;\n}): Promise<GetTableItemsMetadataResponse> {\n  const { aptosConfig, options } = args;\n\n  const graphqlQuery = {\n    query: GetTableItemsMetadata,\n    variables: {\n      where_condition: options?.where,\n      offset: options?.offset,\n      limit: options?.limit,\n      order_by: options?.orderBy,\n    },\n  };\n\n  const data = await queryIndexer<GetTableItemsMetadataQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getTableItemsMetadata\",\n  });\n\n  return data.table_metadatas;\n}\n"], "mappings": "kIA0BA,eAAsBA,EAAgBC,EAKvB,CACb,GAAM,CAAE,YAAAC,EAAa,OAAAC,EAAQ,KAAAC,EAAM,QAAAC,CAAQ,EAAIJ,EAQ/C,OAPiB,MAAMK,EAAyC,CAC9D,YAAAJ,EACA,aAAc,eACd,KAAM,UAAUC,CAAM,QACtB,OAAQ,CAAE,eAAgBE,GAAS,aAAc,EACjD,KAAMD,CACR,CAAC,GACe,IAClB,CAcA,eAAsBG,EAAkBN,EAGrC,CACD,GAAM,CAAE,YAAAC,EAAa,QAAAG,CAAQ,EAAIJ,EAE3BO,EAAe,CACnB,MAAOC,EACP,UAAW,CACT,gBAAiBJ,GAAS,MAC1B,OAAQA,GAAS,OACjB,MAAOA,GAAS,MAChB,SAAUA,GAAS,OACrB,CACF,EAQA,OANa,MAAMK,EAAqC,CACtD,YAAAR,EACA,MAAOM,EACP,aAAc,mBAChB,CAAC,GAEW,WACd,CAeA,eAAsBG,EAAsBV,EAGD,CACzC,GAAM,CAAE,YAAAC,EAAa,QAAAG,CAAQ,EAAIJ,EAE3BO,EAAe,CACnB,MAAOI,EACP,UAAW,CACT,gBAAiBP,GAAS,MAC1B,OAAQA,GAAS,OACjB,MAAOA,GAAS,MAChB,SAAUA,GAAS,OACrB,CACF,EAQA,OANa,MAAMK,EAAyC,CAC1D,YAAAR,EACA,MAAOM,EACP,aAAc,uBAChB,CAAC,GAEW,eACd", "names": ["getTableItem", "args", "aptosConfig", "handle", "data", "options", "postAptosFullNode", "getTableItemsData", "graphqlQuery", "GetTableItemsData", "queryIndexer", "getTableItemsMetadata", "GetTableItemsMetadata"]}