import { BaseToken, Web3SideChainClient } from "../utils";
import { IZkEvmClientConfig, ITransactionOption } from "../interfaces";
export declare class ZkEVMWrapper extends BaseToken<IZkEvmClientConfig> {
    constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string);
    method(methodName: string, ...args: any[]): Promise<import("..").BaseContractMethod>;
    depositWithGas(tokenAddress: string, depositAmount: string, userAddress: string, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    depositPermitWithGas(tokenAddress: string, depositAmount: string, userAddress: string, deadline: string, v: number, r: string, s: string, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
}
