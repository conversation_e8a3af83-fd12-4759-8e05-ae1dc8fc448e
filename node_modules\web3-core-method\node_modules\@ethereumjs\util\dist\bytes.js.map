{"version": 3, "file": "bytes.js", "sourceRoot": "", "sources": ["../src/bytes.ts"], "names": [], "mappings": ";;;AAAA,uCAA4E;AAC5E,yCAAkF;AAUlF;;;;GAIG;AACI,MAAM,QAAQ,GAAG,UAAU,CAAS;IACzC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,EAAE,CAAC,CAAA;KAC1D;IACD,OAAO,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAA;AAC9B,CAAC,CAAA;AALY,QAAA,QAAQ,YAKpB;AAED;;;;GAIG;AACI,MAAM,WAAW,GAAG,UAAU,CAAS;IAC5C,MAAM,GAAG,GAAG,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAA;IACvB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAS,EAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACpD,CAAC,CAAA;AAHY,QAAA,WAAW,eAGvB;AAED;;;GAGG;AACI,MAAM,KAAK,GAAG,UAAU,KAAa;IAC1C,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAC1C,CAAC,CAAA;AAFY,QAAA,KAAK,SAEjB;AAED;;;;;;;GAOG;AACH,MAAM,SAAS,GAAG,UAAU,GAAW,EAAE,MAAc,EAAE,KAAc;IACrE,MAAM,GAAG,GAAG,IAAA,aAAK,EAAC,MAAM,CAAC,CAAA;IACzB,IAAI,KAAK,EAAE;QACT,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE;YACvB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,OAAO,GAAG,CAAA;SACX;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;KAC5B;SAAM;QACL,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,EAAE;YACvB,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAA;YAClC,OAAO,GAAG,CAAA;SACX;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAA;KAC1B;AACH,CAAC,CAAA;AAED;;;;;;GAMG;AACI,MAAM,aAAa,GAAG,UAAU,GAAW,EAAE,MAAc;IAChE,IAAA,wBAAc,EAAC,GAAG,CAAC,CAAA;IACnB,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAA;AACtC,CAAC,CAAA;AAHY,QAAA,aAAa,iBAGzB;AAED;;;;;;GAMG;AACI,MAAM,cAAc,GAAG,UAAU,GAAW,EAAE,MAAc;IACjE,IAAA,wBAAc,EAAC,GAAG,CAAC,CAAA;IACnB,OAAO,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;AACrC,CAAC,CAAA;AAHY,QAAA,cAAc,kBAG1B;AAED;;;;GAIG;AACH,MAAM,UAAU,GAAG,UAAU,CAAM;IACjC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAChB,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE;QAC/C,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACd,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;KACb;IACD,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AAED;;;;GAIG;AACI,MAAM,WAAW,GAAG,UAAU,CAAS;IAC5C,IAAA,wBAAc,EAAC,CAAC,CAAC,CAAA;IACjB,OAAO,UAAU,CAAC,CAAC,CAAW,CAAA;AAChC,CAAC,CAAA;AAHY,QAAA,WAAW,eAGvB;AAED;;;;GAIG;AACI,MAAM,UAAU,GAAG,UAAU,CAAW;IAC7C,IAAA,uBAAa,EAAC,CAAC,CAAC,CAAA;IAChB,OAAO,UAAU,CAAC,CAAC,CAAa,CAAA;AAClC,CAAC,CAAA;AAHY,QAAA,UAAU,cAGtB;AAED;;;;GAIG;AACI,MAAM,cAAc,GAAG,UAAU,CAAS;IAC/C,IAAA,2BAAiB,EAAC,CAAC,CAAC,CAAA;IACpB,CAAC,GAAG,IAAA,yBAAc,EAAC,CAAC,CAAC,CAAA;IACrB,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAW,CAAA;AACzC,CAAC,CAAA;AAJY,QAAA,cAAc,kBAI1B;AAcD;;;;;GAKG;AACI,MAAM,QAAQ,GAAG,UAAU,CAAqB;IACrD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE;QACjC,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;KAC7B;IAED,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KACtB;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,UAAU,EAAE;QAC/C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAe,CAAC,CAAA;KACpC;IAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,IAAI,CAAC,IAAA,sBAAW,EAAC,CAAC,CAAC,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,8GAA8G,CAAC,EAAE,CAClH,CAAA;SACF;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAS,EAAC,IAAA,yBAAc,EAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;KACxD;IAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAO,IAAA,mBAAW,EAAC,CAAC,CAAC,CAAA;KACtB;IAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,EAAE,CAAC,CAAA;SACzE;QACD,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACtB,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;QAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;KAC7B;IAED,IAAI,CAAC,CAAC,OAAO,EAAE;QACb,4BAA4B;QAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;KAChC;IAED,IAAI,CAAC,CAAC,QAAQ,EAAE;QACd,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;KACjC;IAED,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;AACjC,CAAC,CAAA;AA7CY,QAAA,QAAQ,YA6CpB;AAED;;;GAGG;AACI,MAAM,WAAW,GAAG,UAAU,GAAW;IAC9C,GAAG,GAAG,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAA;IACnB,OAAO,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;AACnC,CAAC,CAAA;AAHY,QAAA,WAAW,eAGvB;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,GAAW;IACxC,MAAM,GAAG,GAAG,IAAA,mBAAW,EAAC,GAAG,CAAC,CAAA;IAC5B,IAAI,GAAG,KAAK,IAAI,EAAE;QAChB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAA;KACjB;IACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;AACpB,CAAC;AAND,wCAMC;AAED;;GAEG;AACH,SAAgB,cAAc,CAAC,GAAW;IACxC,OAAO,IAAA,gBAAQ,EAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;AAC1C,CAAC;AAFD,wCAEC;AAED;;;;GAIG;AACI,MAAM,WAAW,GAAG,UAAU,GAAW;IAC9C,MAAM,GAAG,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAA;IACvC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;IACzE,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAJY,QAAA,WAAW,eAIvB;AAED;;;GAGG;AACI,MAAM,UAAU,GAAG,UAAU,GAAW;IAC7C,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAA;AAChD,CAAC,CAAA;AAFY,QAAA,UAAU,cAEtB;AAED;;;GAGG;AACI,MAAM,UAAU,GAAG,UAAU,GAAW;IAC7C,OAAO,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AACjD,CAAC,CAAA;AAFY,QAAA,UAAU,cAEtB;AAED;;GAEG;AACI,MAAM,YAAY,GAAG,UAAU,GAAW;IAC/C,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,OAAO,GAAG,CAAA;KACX;IAED,OAAO,IAAA,wBAAa,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAA;AAC9C,CAAC,CAAA;AANY,QAAA,YAAY,gBAMxB;AAED;;;;;;;GAOG;AACH,SAAgB,KAAK,CAAC,MAAuB,EAAE,YAAoB,EAAE;IACnE,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAC3E,IAAI,SAAS,CAAC,MAAM,IAAI,SAAS,EAAE;QACjC,OAAO,SAAS,CAAA;KACjB;IACD,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAA;AAC5C,CAAC;AAND,sBAMC;AAED;;;;;;;;;;;;;;;;GAgBG;AACI,MAAM,MAAM,GAAG,UAAU,GAAW;IACzC,MAAM,WAAW,GAAG,gBAAgB,CAAA;IACpC,GAAG,GAAG,IAAA,yBAAc,EAAC,GAAG,CAAC,CAAA;IACzB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAA;KAC3E;IACD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAA;IAElE,OAAO,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;AACnC,CAAC,CAAA;AATY,QAAA,MAAM,UASlB;AAED;;;;GAIG;AACI,MAAM,QAAQ,GAAG,UAAU,EAAO;IACvC,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;QACvB,OAAO,KAAK,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAA;KACjC;SAAM,IAAI,EAAE,YAAY,KAAK,EAAE;QAC9B,MAAM,KAAK,GAAG,EAAE,CAAA;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,KAAK,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SAC5B;QACD,OAAO,KAAK,CAAA;KACb;AACH,CAAC,CAAA;AAVY,QAAA,QAAQ,YAUpB;AAED;;;;;;;;;;;;GAYG;AACI,MAAM,uBAAuB,GAAG,UAAU,MAA6C;IAC5F,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC3C,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACjD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;SACnF;KACF;AACH,CAAC,CAAA;AANY,QAAA,uBAAuB,2BAMnC;AAQD,SAAgB,WAAW,CAAC,GAAkC;IAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;KACxB;IACD,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;AACvC,CAAC;AALD,kCAKC;AAQD,SAAgB,WAAW,CAAC,GAA+B;IACzD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAA;KAClC;IACD,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;AACvC,CAAC;AALD,kCAKC;AAED;;GAEG;AACI,MAAM,WAAW,GAAG,CAAC,GAAW,EAAE,EAAE;IACzC,OAAO,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;AAChC,CAAC,CAAA;AAFY,QAAA,WAAW,eAEvB;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CAAC,KAAa;IAClD,OAAO,IAAA,mBAAW,EAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAA;AAC3C,CAAC;AAFD,wDAEC;AAED,SAAgB,mBAAmB,CAAC,KAAa;IAC/C,OAAO,IAAA,mBAAW,EAAC,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC,CAAA;AACxC,CAAC;AAFD,kDAEC"}