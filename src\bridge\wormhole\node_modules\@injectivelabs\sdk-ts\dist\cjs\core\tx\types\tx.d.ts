import { Msgs } from '../../modules/msgs.js';
import { StdFee } from '@cosmjs/amino';
import { CosmosTxV1Beta1Tx, CosmosTxV1Beta1Service, CosmosTxSigningV1Beta1Signing, GoogleProtobufAny } from '@injectivelabs/core-proto-ts';
export interface TxClientBroadcastOptions {
    mode?: CosmosTxV1Beta1Service.BroadcastMode;
    timeout?: number;
    txTimeout?: number;
}
export interface TxClientBroadcastResponse {
    height: number;
    txHash: string;
    codespace: string;
    code: number;
    data?: string;
    rawLog: string;
    logs?: any[];
    info?: string;
    gasWanted: number;
    gasUsed: number;
    timestamp: string;
    events?: any[];
}
export interface TxClientSimulateResponse {
    result: {
        data: Uint8Array | string;
        log: string;
        eventsList: any[];
    };
    gasInfo: {
        gasWanted: number;
        gasUsed: number;
    };
}
export interface TxConcreteApi {
    broadcast(txRaw: CosmosTxV1Beta1Tx.TxRaw, options?: TxClientBroadcastOptions): Promise<TxClientBroadcastResponse>;
    broadcastBlock(txRaw: CosmosTxV1Beta1Tx.TxRaw): Promise<TxClientBroadcastResponse>;
    fetchTx(txHash: string): Promise<TxClientBroadcastResponse | undefined>;
    fetchTxPoll(txHash: string): Promise<TxClientBroadcastResponse | undefined>;
    simulate(txRaw: CosmosTxV1Beta1Tx.TxRaw): Promise<TxClientSimulateResponse>;
}
export declare enum TxClientMode {
    gRpc = "grpc",
    rest = "rest"
}
export type MsgArg = {
    type: string;
    message: any;
};
export interface SignerDetails {
    pubKey: string | GoogleProtobufAny.Any;
    sequence: number;
    accountNumber: number;
}
/** @type {CreateTransactionWithSignersArgs} */
export interface CreateTransactionWithSignersArgs {
    fee?: StdFee | string;
    memo?: string;
    chainId: string;
    message: Msgs | Msgs[];
    signers: SignerDetails | SignerDetails[];
    signMode?: CosmosTxSigningV1Beta1Signing.SignMode;
    timeoutHeight?: number;
}
/** @type {CreateTransactionArgs} */
export interface CreateTransactionArgs {
    fee?: StdFee;
    memo?: string;
    chainId: string;
    message: Msgs | Msgs[];
    pubKey: string;
    sequence: number;
    accountNumber: number;
    signMode?: CosmosTxSigningV1Beta1Signing.SignMode;
    timeoutHeight?: number;
}
/** @type {CreateTransactionResult} */
export interface CreateTransactionResult {
    txRaw: CosmosTxV1Beta1Tx.TxRaw;
    signDoc: CosmosTxV1Beta1Tx.SignDoc;
    bodyBytes: Uint8Array;
    signers: SignerDetails | SignerDetails[];
    signer: SignerDetails;
    authInfoBytes: Uint8Array;
    signBytes: Uint8Array;
    signHashedBytes: Uint8Array;
}
export interface TxResponse {
    height: number;
    txHash: string;
    codespace: string;
    code: number;
    data?: string;
    rawLog: string;
    logs?: any[];
    info?: string;
    gasWanted: number;
    gasUsed: number;
    timestamp: string;
    events?: any[];
}
