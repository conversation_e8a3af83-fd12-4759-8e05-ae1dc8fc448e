"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.InjectiveChartRPCAllDerivativeMarketSummaryDesc = exports.InjectiveChartRPCDerivativeMarketSummaryDesc = exports.InjectiveChartRPCAllSpotMarketSummaryDesc = exports.InjectiveChartRPCSpotMarketSummaryDesc = exports.InjectiveChartRPCDerivativeMarketHistoryDesc = exports.InjectiveChartRPCSpotMarketHistoryDesc = exports.InjectiveChartRPCDesc = exports.InjectiveChartRPCClientImpl = exports.AllDerivativeMarketSummaryResponse = exports.AllDerivativeMarketSummaryRequest = exports.DerivativeMarketSummaryResponse = exports.DerivativeMarketSummaryRequest = exports.MarketSummaryResp = exports.AllSpotMarketSummaryResponse = exports.AllSpotMarketSummaryRequest = exports.SpotMarketSummaryResponse = exports.SpotMarketSummaryRequest = exports.DerivativeMarketHistoryResponse = exports.DerivativeMarketHistoryRequest = exports.SpotMarketHistoryResponse = exports.SpotMarketHistoryRequest = exports.protobufPackage = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "injective_chart_rpc";
function createBaseSpotMarketHistoryRequest() {
    return { symbol: "", marketId: "", resolution: "", from: 0, to: 0, countback: 0 };
}
exports.SpotMarketHistoryRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.resolution !== "") {
            writer.uint32(26).string(message.resolution);
        }
        if (message.from !== 0) {
            writer.uint32(32).sint32(message.from);
        }
        if (message.to !== 0) {
            writer.uint32(40).sint32(message.to);
        }
        if (message.countback !== 0) {
            writer.uint32(48).sint32(message.countback);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketHistoryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.resolution = reader.string();
                    break;
                case 4:
                    message.from = reader.sint32();
                    break;
                case 5:
                    message.to = reader.sint32();
                    break;
                case 6:
                    message.countback = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
            from: isSet(object.from) ? Number(object.from) : 0,
            to: isSet(object.to) ? Number(object.to) : 0,
            countback: isSet(object.countback) ? Number(object.countback) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        message.from !== undefined && (obj.from = Math.round(message.from));
        message.to !== undefined && (obj.to = Math.round(message.to));
        message.countback !== undefined && (obj.countback = Math.round(message.countback));
        return obj;
    },
    create(base) {
        return exports.SpotMarketHistoryRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f;
        const message = createBaseSpotMarketHistoryRequest();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.resolution = (_c = object.resolution) !== null && _c !== void 0 ? _c : "";
        message.from = (_d = object.from) !== null && _d !== void 0 ? _d : 0;
        message.to = (_e = object.to) !== null && _e !== void 0 ? _e : 0;
        message.countback = (_f = object.countback) !== null && _f !== void 0 ? _f : 0;
        return message;
    },
};
function createBaseSpotMarketHistoryResponse() {
    return { t: [], o: [], h: [], l: [], c: [], v: [], s: "" };
}
exports.SpotMarketHistoryResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.t) {
            writer.sint32(v);
        }
        writer.ldelim();
        writer.uint32(18).fork();
        for (const v of message.o) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(26).fork();
        for (const v of message.h) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(34).fork();
        for (const v of message.l) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(42).fork();
        for (const v of message.c) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(50).fork();
        for (const v of message.v) {
            writer.double(v);
        }
        writer.ldelim();
        if (message.s !== "") {
            writer.uint32(58).string(message.s);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketHistoryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.t.push(reader.sint32());
                        }
                    }
                    else {
                        message.t.push(reader.sint32());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.o.push(reader.double());
                        }
                    }
                    else {
                        message.o.push(reader.double());
                    }
                    break;
                case 3:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.h.push(reader.double());
                        }
                    }
                    else {
                        message.h.push(reader.double());
                    }
                    break;
                case 4:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.l.push(reader.double());
                        }
                    }
                    else {
                        message.l.push(reader.double());
                    }
                    break;
                case 5:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.c.push(reader.double());
                        }
                    }
                    else {
                        message.c.push(reader.double());
                    }
                    break;
                case 6:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.v.push(reader.double());
                        }
                    }
                    else {
                        message.v.push(reader.double());
                    }
                    break;
                case 7:
                    message.s = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            t: Array.isArray(object === null || object === void 0 ? void 0 : object.t) ? object.t.map((e) => Number(e)) : [],
            o: Array.isArray(object === null || object === void 0 ? void 0 : object.o) ? object.o.map((e) => Number(e)) : [],
            h: Array.isArray(object === null || object === void 0 ? void 0 : object.h) ? object.h.map((e) => Number(e)) : [],
            l: Array.isArray(object === null || object === void 0 ? void 0 : object.l) ? object.l.map((e) => Number(e)) : [],
            c: Array.isArray(object === null || object === void 0 ? void 0 : object.c) ? object.c.map((e) => Number(e)) : [],
            v: Array.isArray(object === null || object === void 0 ? void 0 : object.v) ? object.v.map((e) => Number(e)) : [],
            s: isSet(object.s) ? String(object.s) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.t) {
            obj.t = message.t.map((e) => Math.round(e));
        }
        else {
            obj.t = [];
        }
        if (message.o) {
            obj.o = message.o.map((e) => e);
        }
        else {
            obj.o = [];
        }
        if (message.h) {
            obj.h = message.h.map((e) => e);
        }
        else {
            obj.h = [];
        }
        if (message.l) {
            obj.l = message.l.map((e) => e);
        }
        else {
            obj.l = [];
        }
        if (message.c) {
            obj.c = message.c.map((e) => e);
        }
        else {
            obj.c = [];
        }
        if (message.v) {
            obj.v = message.v.map((e) => e);
        }
        else {
            obj.v = [];
        }
        message.s !== undefined && (obj.s = message.s);
        return obj;
    },
    create(base) {
        return exports.SpotMarketHistoryResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBaseSpotMarketHistoryResponse();
        message.t = ((_a = object.t) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.o = ((_b = object.o) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        message.h = ((_c = object.h) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
        message.l = ((_d = object.l) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        message.c = ((_e = object.c) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
        message.v = ((_f = object.v) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.s = (_g = object.s) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseDerivativeMarketHistoryRequest() {
    return { symbol: "", marketId: "", resolution: "", from: 0, to: 0, countback: 0 };
}
exports.DerivativeMarketHistoryRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.resolution !== "") {
            writer.uint32(26).string(message.resolution);
        }
        if (message.from !== 0) {
            writer.uint32(32).sint32(message.from);
        }
        if (message.to !== 0) {
            writer.uint32(40).sint32(message.to);
        }
        if (message.countback !== 0) {
            writer.uint32(48).sint32(message.countback);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketHistoryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.resolution = reader.string();
                    break;
                case 4:
                    message.from = reader.sint32();
                    break;
                case 5:
                    message.to = reader.sint32();
                    break;
                case 6:
                    message.countback = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
            from: isSet(object.from) ? Number(object.from) : 0,
            to: isSet(object.to) ? Number(object.to) : 0,
            countback: isSet(object.countback) ? Number(object.countback) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        message.from !== undefined && (obj.from = Math.round(message.from));
        message.to !== undefined && (obj.to = Math.round(message.to));
        message.countback !== undefined && (obj.countback = Math.round(message.countback));
        return obj;
    },
    create(base) {
        return exports.DerivativeMarketHistoryRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f;
        const message = createBaseDerivativeMarketHistoryRequest();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.resolution = (_c = object.resolution) !== null && _c !== void 0 ? _c : "";
        message.from = (_d = object.from) !== null && _d !== void 0 ? _d : 0;
        message.to = (_e = object.to) !== null && _e !== void 0 ? _e : 0;
        message.countback = (_f = object.countback) !== null && _f !== void 0 ? _f : 0;
        return message;
    },
};
function createBaseDerivativeMarketHistoryResponse() {
    return { t: [], o: [], h: [], l: [], c: [], v: [], s: "" };
}
exports.DerivativeMarketHistoryResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.t) {
            writer.sint32(v);
        }
        writer.ldelim();
        writer.uint32(18).fork();
        for (const v of message.o) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(26).fork();
        for (const v of message.h) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(34).fork();
        for (const v of message.l) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(42).fork();
        for (const v of message.c) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(50).fork();
        for (const v of message.v) {
            writer.double(v);
        }
        writer.ldelim();
        if (message.s !== "") {
            writer.uint32(58).string(message.s);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketHistoryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.t.push(reader.sint32());
                        }
                    }
                    else {
                        message.t.push(reader.sint32());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.o.push(reader.double());
                        }
                    }
                    else {
                        message.o.push(reader.double());
                    }
                    break;
                case 3:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.h.push(reader.double());
                        }
                    }
                    else {
                        message.h.push(reader.double());
                    }
                    break;
                case 4:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.l.push(reader.double());
                        }
                    }
                    else {
                        message.l.push(reader.double());
                    }
                    break;
                case 5:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.c.push(reader.double());
                        }
                    }
                    else {
                        message.c.push(reader.double());
                    }
                    break;
                case 6:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.v.push(reader.double());
                        }
                    }
                    else {
                        message.v.push(reader.double());
                    }
                    break;
                case 7:
                    message.s = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            t: Array.isArray(object === null || object === void 0 ? void 0 : object.t) ? object.t.map((e) => Number(e)) : [],
            o: Array.isArray(object === null || object === void 0 ? void 0 : object.o) ? object.o.map((e) => Number(e)) : [],
            h: Array.isArray(object === null || object === void 0 ? void 0 : object.h) ? object.h.map((e) => Number(e)) : [],
            l: Array.isArray(object === null || object === void 0 ? void 0 : object.l) ? object.l.map((e) => Number(e)) : [],
            c: Array.isArray(object === null || object === void 0 ? void 0 : object.c) ? object.c.map((e) => Number(e)) : [],
            v: Array.isArray(object === null || object === void 0 ? void 0 : object.v) ? object.v.map((e) => Number(e)) : [],
            s: isSet(object.s) ? String(object.s) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.t) {
            obj.t = message.t.map((e) => Math.round(e));
        }
        else {
            obj.t = [];
        }
        if (message.o) {
            obj.o = message.o.map((e) => e);
        }
        else {
            obj.o = [];
        }
        if (message.h) {
            obj.h = message.h.map((e) => e);
        }
        else {
            obj.h = [];
        }
        if (message.l) {
            obj.l = message.l.map((e) => e);
        }
        else {
            obj.l = [];
        }
        if (message.c) {
            obj.c = message.c.map((e) => e);
        }
        else {
            obj.c = [];
        }
        if (message.v) {
            obj.v = message.v.map((e) => e);
        }
        else {
            obj.v = [];
        }
        message.s !== undefined && (obj.s = message.s);
        return obj;
    },
    create(base) {
        return exports.DerivativeMarketHistoryResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBaseDerivativeMarketHistoryResponse();
        message.t = ((_a = object.t) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.o = ((_b = object.o) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        message.h = ((_c = object.h) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
        message.l = ((_d = object.l) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        message.c = ((_e = object.c) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
        message.v = ((_f = object.v) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.s = (_g = object.s) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseSpotMarketSummaryRequest() {
    return { marketId: "", resolution: "" };
}
exports.SpotMarketSummaryRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.resolution !== "") {
            writer.uint32(18).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return exports.SpotMarketSummaryRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseSpotMarketSummaryRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.resolution = (_b = object.resolution) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseSpotMarketSummaryResponse() {
    return { marketId: "", open: 0, high: 0, low: 0, volume: 0, price: 0, change: 0 };
}
exports.SpotMarketSummaryResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.open !== 0) {
            writer.uint32(17).double(message.open);
        }
        if (message.high !== 0) {
            writer.uint32(25).double(message.high);
        }
        if (message.low !== 0) {
            writer.uint32(33).double(message.low);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        if (message.price !== 0) {
            writer.uint32(49).double(message.price);
        }
        if (message.change !== 0) {
            writer.uint32(57).double(message.change);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.open = reader.double();
                    break;
                case 3:
                    message.high = reader.double();
                    break;
                case 4:
                    message.low = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                case 6:
                    message.price = reader.double();
                    break;
                case 7:
                    message.change = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            open: isSet(object.open) ? Number(object.open) : 0,
            high: isSet(object.high) ? Number(object.high) : 0,
            low: isSet(object.low) ? Number(object.low) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
            price: isSet(object.price) ? Number(object.price) : 0,
            change: isSet(object.change) ? Number(object.change) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.open !== undefined && (obj.open = message.open);
        message.high !== undefined && (obj.high = message.high);
        message.low !== undefined && (obj.low = message.low);
        message.volume !== undefined && (obj.volume = message.volume);
        message.price !== undefined && (obj.price = message.price);
        message.change !== undefined && (obj.change = message.change);
        return obj;
    },
    create(base) {
        return exports.SpotMarketSummaryResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBaseSpotMarketSummaryResponse();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.open = (_b = object.open) !== null && _b !== void 0 ? _b : 0;
        message.high = (_c = object.high) !== null && _c !== void 0 ? _c : 0;
        message.low = (_d = object.low) !== null && _d !== void 0 ? _d : 0;
        message.volume = (_e = object.volume) !== null && _e !== void 0 ? _e : 0;
        message.price = (_f = object.price) !== null && _f !== void 0 ? _f : 0;
        message.change = (_g = object.change) !== null && _g !== void 0 ? _g : 0;
        return message;
    },
};
function createBaseAllSpotMarketSummaryRequest() {
    return { resolution: "" };
}
exports.AllSpotMarketSummaryRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.resolution !== "") {
            writer.uint32(10).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllSpotMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { resolution: isSet(object.resolution) ? String(object.resolution) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return exports.AllSpotMarketSummaryRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAllSpotMarketSummaryRequest();
        message.resolution = (_a = object.resolution) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseAllSpotMarketSummaryResponse() {
    return { field: [] };
}
exports.AllSpotMarketSummaryResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.field) {
            exports.MarketSummaryResp.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllSpotMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.field.push(exports.MarketSummaryResp.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { field: Array.isArray(object === null || object === void 0 ? void 0 : object.field) ? object.field.map((e) => exports.MarketSummaryResp.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.field) {
            obj.field = message.field.map((e) => e ? exports.MarketSummaryResp.toJSON(e) : undefined);
        }
        else {
            obj.field = [];
        }
        return obj;
    },
    create(base) {
        return exports.AllSpotMarketSummaryResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAllSpotMarketSummaryResponse();
        message.field = ((_a = object.field) === null || _a === void 0 ? void 0 : _a.map((e) => exports.MarketSummaryResp.fromPartial(e))) || [];
        return message;
    },
};
function createBaseMarketSummaryResp() {
    return { marketId: "", open: 0, high: 0, low: 0, volume: 0, price: 0, change: 0 };
}
exports.MarketSummaryResp = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.open !== 0) {
            writer.uint32(17).double(message.open);
        }
        if (message.high !== 0) {
            writer.uint32(25).double(message.high);
        }
        if (message.low !== 0) {
            writer.uint32(33).double(message.low);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        if (message.price !== 0) {
            writer.uint32(49).double(message.price);
        }
        if (message.change !== 0) {
            writer.uint32(57).double(message.change);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMarketSummaryResp();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.open = reader.double();
                    break;
                case 3:
                    message.high = reader.double();
                    break;
                case 4:
                    message.low = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                case 6:
                    message.price = reader.double();
                    break;
                case 7:
                    message.change = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            open: isSet(object.open) ? Number(object.open) : 0,
            high: isSet(object.high) ? Number(object.high) : 0,
            low: isSet(object.low) ? Number(object.low) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
            price: isSet(object.price) ? Number(object.price) : 0,
            change: isSet(object.change) ? Number(object.change) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.open !== undefined && (obj.open = message.open);
        message.high !== undefined && (obj.high = message.high);
        message.low !== undefined && (obj.low = message.low);
        message.volume !== undefined && (obj.volume = message.volume);
        message.price !== undefined && (obj.price = message.price);
        message.change !== undefined && (obj.change = message.change);
        return obj;
    },
    create(base) {
        return exports.MarketSummaryResp.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBaseMarketSummaryResp();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.open = (_b = object.open) !== null && _b !== void 0 ? _b : 0;
        message.high = (_c = object.high) !== null && _c !== void 0 ? _c : 0;
        message.low = (_d = object.low) !== null && _d !== void 0 ? _d : 0;
        message.volume = (_e = object.volume) !== null && _e !== void 0 ? _e : 0;
        message.price = (_f = object.price) !== null && _f !== void 0 ? _f : 0;
        message.change = (_g = object.change) !== null && _g !== void 0 ? _g : 0;
        return message;
    },
};
function createBaseDerivativeMarketSummaryRequest() {
    return { marketId: "", indexPrice: false, resolution: "" };
}
exports.DerivativeMarketSummaryRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.indexPrice === true) {
            writer.uint32(16).bool(message.indexPrice);
        }
        if (message.resolution !== "") {
            writer.uint32(26).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.indexPrice = reader.bool();
                    break;
                case 3:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            indexPrice: isSet(object.indexPrice) ? Boolean(object.indexPrice) : false,
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.indexPrice !== undefined && (obj.indexPrice = message.indexPrice);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return exports.DerivativeMarketSummaryRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseDerivativeMarketSummaryRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.indexPrice = (_b = object.indexPrice) !== null && _b !== void 0 ? _b : false;
        message.resolution = (_c = object.resolution) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseDerivativeMarketSummaryResponse() {
    return { marketId: "", open: 0, high: 0, low: 0, volume: 0, price: 0, change: 0 };
}
exports.DerivativeMarketSummaryResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.open !== 0) {
            writer.uint32(17).double(message.open);
        }
        if (message.high !== 0) {
            writer.uint32(25).double(message.high);
        }
        if (message.low !== 0) {
            writer.uint32(33).double(message.low);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        if (message.price !== 0) {
            writer.uint32(49).double(message.price);
        }
        if (message.change !== 0) {
            writer.uint32(57).double(message.change);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.open = reader.double();
                    break;
                case 3:
                    message.high = reader.double();
                    break;
                case 4:
                    message.low = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                case 6:
                    message.price = reader.double();
                    break;
                case 7:
                    message.change = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            open: isSet(object.open) ? Number(object.open) : 0,
            high: isSet(object.high) ? Number(object.high) : 0,
            low: isSet(object.low) ? Number(object.low) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
            price: isSet(object.price) ? Number(object.price) : 0,
            change: isSet(object.change) ? Number(object.change) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.open !== undefined && (obj.open = message.open);
        message.high !== undefined && (obj.high = message.high);
        message.low !== undefined && (obj.low = message.low);
        message.volume !== undefined && (obj.volume = message.volume);
        message.price !== undefined && (obj.price = message.price);
        message.change !== undefined && (obj.change = message.change);
        return obj;
    },
    create(base) {
        return exports.DerivativeMarketSummaryResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBaseDerivativeMarketSummaryResponse();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.open = (_b = object.open) !== null && _b !== void 0 ? _b : 0;
        message.high = (_c = object.high) !== null && _c !== void 0 ? _c : 0;
        message.low = (_d = object.low) !== null && _d !== void 0 ? _d : 0;
        message.volume = (_e = object.volume) !== null && _e !== void 0 ? _e : 0;
        message.price = (_f = object.price) !== null && _f !== void 0 ? _f : 0;
        message.change = (_g = object.change) !== null && _g !== void 0 ? _g : 0;
        return message;
    },
};
function createBaseAllDerivativeMarketSummaryRequest() {
    return { resolution: "" };
}
exports.AllDerivativeMarketSummaryRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.resolution !== "") {
            writer.uint32(10).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllDerivativeMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { resolution: isSet(object.resolution) ? String(object.resolution) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return exports.AllDerivativeMarketSummaryRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAllDerivativeMarketSummaryRequest();
        message.resolution = (_a = object.resolution) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseAllDerivativeMarketSummaryResponse() {
    return { field: [] };
}
exports.AllDerivativeMarketSummaryResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.field) {
            exports.MarketSummaryResp.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllDerivativeMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.field.push(exports.MarketSummaryResp.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { field: Array.isArray(object === null || object === void 0 ? void 0 : object.field) ? object.field.map((e) => exports.MarketSummaryResp.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.field) {
            obj.field = message.field.map((e) => e ? exports.MarketSummaryResp.toJSON(e) : undefined);
        }
        else {
            obj.field = [];
        }
        return obj;
    },
    create(base) {
        return exports.AllDerivativeMarketSummaryResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAllDerivativeMarketSummaryResponse();
        message.field = ((_a = object.field) === null || _a === void 0 ? void 0 : _a.map((e) => exports.MarketSummaryResp.fromPartial(e))) || [];
        return message;
    },
};
class InjectiveChartRPCClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.SpotMarketHistory = this.SpotMarketHistory.bind(this);
        this.DerivativeMarketHistory = this.DerivativeMarketHistory.bind(this);
        this.SpotMarketSummary = this.SpotMarketSummary.bind(this);
        this.AllSpotMarketSummary = this.AllSpotMarketSummary.bind(this);
        this.DerivativeMarketSummary = this.DerivativeMarketSummary.bind(this);
        this.AllDerivativeMarketSummary = this.AllDerivativeMarketSummary.bind(this);
    }
    SpotMarketHistory(request, metadata) {
        return this.rpc.unary(exports.InjectiveChartRPCSpotMarketHistoryDesc, exports.SpotMarketHistoryRequest.fromPartial(request), metadata);
    }
    DerivativeMarketHistory(request, metadata) {
        return this.rpc.unary(exports.InjectiveChartRPCDerivativeMarketHistoryDesc, exports.DerivativeMarketHistoryRequest.fromPartial(request), metadata);
    }
    SpotMarketSummary(request, metadata) {
        return this.rpc.unary(exports.InjectiveChartRPCSpotMarketSummaryDesc, exports.SpotMarketSummaryRequest.fromPartial(request), metadata);
    }
    AllSpotMarketSummary(request, metadata) {
        return this.rpc.unary(exports.InjectiveChartRPCAllSpotMarketSummaryDesc, exports.AllSpotMarketSummaryRequest.fromPartial(request), metadata);
    }
    DerivativeMarketSummary(request, metadata) {
        return this.rpc.unary(exports.InjectiveChartRPCDerivativeMarketSummaryDesc, exports.DerivativeMarketSummaryRequest.fromPartial(request), metadata);
    }
    AllDerivativeMarketSummary(request, metadata) {
        return this.rpc.unary(exports.InjectiveChartRPCAllDerivativeMarketSummaryDesc, exports.AllDerivativeMarketSummaryRequest.fromPartial(request), metadata);
    }
}
exports.InjectiveChartRPCClientImpl = InjectiveChartRPCClientImpl;
exports.InjectiveChartRPCDesc = { serviceName: "injective_chart_rpc.InjectiveChartRPC" };
exports.InjectiveChartRPCSpotMarketHistoryDesc = {
    methodName: "SpotMarketHistory",
    service: exports.InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.SpotMarketHistoryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.SpotMarketHistoryResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveChartRPCDerivativeMarketHistoryDesc = {
    methodName: "DerivativeMarketHistory",
    service: exports.InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.DerivativeMarketHistoryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.DerivativeMarketHistoryResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveChartRPCSpotMarketSummaryDesc = {
    methodName: "SpotMarketSummary",
    service: exports.InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.SpotMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.SpotMarketSummaryResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveChartRPCAllSpotMarketSummaryDesc = {
    methodName: "AllSpotMarketSummary",
    service: exports.InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.AllSpotMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.AllSpotMarketSummaryResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveChartRPCDerivativeMarketSummaryDesc = {
    methodName: "DerivativeMarketSummary",
    service: exports.InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.DerivativeMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.DerivativeMarketSummaryResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveChartRPCAllDerivativeMarketSummaryDesc = {
    methodName: "AllDerivativeMarketSummary",
    service: exports.InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.AllDerivativeMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.AllDerivativeMarketSummaryResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
