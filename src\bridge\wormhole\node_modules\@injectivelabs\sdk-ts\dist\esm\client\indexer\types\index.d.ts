import { IndexerErrorModule } from '@injectivelabs/exceptions';
export * from './mito.js';
export * from './swap.js';
export * from './spot.js';
export * from './oracle.js';
export * from './account.js';
export * from './auction.js';
export * from './trading.js';
export * from './archiver.js';
export * from './exchange.js';
export * from './explorer.js';
export * from './referral.js';
export * from './campaign.js';
export * from './spot-rest.js';
export * from './incentives.js';
export * from './derivatives.js';
export * from './explorer-rest.js';
export * from './insurance-funds.js';
export * from './derivatives-rest.js';
export * from './leaderboard-rest.js';
export * from './account-portfolio.js';
export * from './markets-history-rest.js';
export interface StreamStatusResponse {
    details: string;
    code: number;
    metadata: any;
}
export declare const IndexerModule: {
    Account: IndexerErrorModule.Account;
    Auction: IndexerErrorModule.Auction;
    Archiver: IndexerErrorModule.Archiver;
    Derivatives: IndexerErrorModule.Derivatives;
    Explorer: IndexerErrorModule.Explorer;
    InsuranceFund: IndexerErrorModule.InsuranceFund;
    Meta: IndexerErrorModule.Meta;
    Mito: IndexerErrorModule.Mito;
    Dmm: IndexerErrorModule.Dmm;
    OLP: IndexerErrorModule.OLP;
    Referral: IndexerErrorModule.Referral;
    Oracle: IndexerErrorModule.Oracle;
    Portfolio: IndexerErrorModule.Portfolio;
    Spot: IndexerErrorModule.Spot;
    Transaction: IndexerErrorModule.Transaction;
    Trading: IndexerErrorModule.Trading;
    ChronosDerivative: IndexerErrorModule.ChronosDerivative;
    ChronosSpot: IndexerErrorModule.ChronosSpot;
    ChronosMarkets: IndexerErrorModule.ChronosMarkets;
    Campaign: IndexerErrorModule.Campaign;
    Web3Gw: IndexerErrorModule.Web3Gw;
    Abacus: IndexerErrorModule.Abacus;
};
