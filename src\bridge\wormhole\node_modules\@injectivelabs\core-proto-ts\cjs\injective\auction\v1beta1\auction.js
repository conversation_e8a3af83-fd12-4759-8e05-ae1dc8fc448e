"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventAuctionStart = exports.EventAuctionResult = exports.EventBid = exports.LastAuctionResult = exports.Bid = exports.Params = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
exports.protobufPackage = "injective.auction.v1beta1";
function createBaseParams() {
    return { auctionPeriod: "0", minNextBidIncrementRate: "", injBasketMaxCap: "" };
}
exports.Params = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.auctionPeriod !== "0") {
            writer.uint32(8).int64(message.auctionPeriod);
        }
        if (message.minNextBidIncrementRate !== "") {
            writer.uint32(18).string(message.minNextBidIncrementRate);
        }
        if (message.injBasketMaxCap !== "") {
            writer.uint32(26).string(message.injBasketMaxCap);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.auctionPeriod = longToString(reader.int64());
                    break;
                case 2:
                    message.minNextBidIncrementRate = reader.string();
                    break;
                case 3:
                    message.injBasketMaxCap = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            auctionPeriod: isSet(object.auctionPeriod) ? String(object.auctionPeriod) : "0",
            minNextBidIncrementRate: isSet(object.minNextBidIncrementRate) ? String(object.minNextBidIncrementRate) : "",
            injBasketMaxCap: isSet(object.injBasketMaxCap) ? String(object.injBasketMaxCap) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.auctionPeriod !== undefined && (obj.auctionPeriod = message.auctionPeriod);
        message.minNextBidIncrementRate !== undefined && (obj.minNextBidIncrementRate = message.minNextBidIncrementRate);
        message.injBasketMaxCap !== undefined && (obj.injBasketMaxCap = message.injBasketMaxCap);
        return obj;
    },
    create: function (base) {
        return exports.Params.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseParams();
        message.auctionPeriod = (_a = object.auctionPeriod) !== null && _a !== void 0 ? _a : "0";
        message.minNextBidIncrementRate = (_b = object.minNextBidIncrementRate) !== null && _b !== void 0 ? _b : "";
        message.injBasketMaxCap = (_c = object.injBasketMaxCap) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseBid() {
    return { bidder: "", amount: undefined };
}
exports.Bid = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bidder !== "") {
            writer.uint32(10).string(message.bidder);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBid();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bidder = reader.string();
                    break;
                case 2:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bidder: isSet(object.bidder) ? String(object.bidder) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bidder !== undefined && (obj.bidder = message.bidder);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Bid.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseBid();
        message.bidder = (_a = object.bidder) !== null && _a !== void 0 ? _a : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseLastAuctionResult() {
    return { winner: "", amount: undefined, round: "0" };
}
exports.LastAuctionResult = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.winner !== "") {
            writer.uint32(10).string(message.winner);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(18).fork()).ldelim();
        }
        if (message.round !== "0") {
            writer.uint32(24).uint64(message.round);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLastAuctionResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.winner = reader.string();
                    break;
                case 2:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.round = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            winner: isSet(object.winner) ? String(object.winner) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
            round: isSet(object.round) ? String(object.round) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.winner !== undefined && (obj.winner = message.winner);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        message.round !== undefined && (obj.round = message.round);
        return obj;
    },
    create: function (base) {
        return exports.LastAuctionResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseLastAuctionResult();
        message.winner = (_a = object.winner) !== null && _a !== void 0 ? _a : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        message.round = (_b = object.round) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseEventBid() {
    return { bidder: "", amount: undefined, round: "0" };
}
exports.EventBid = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bidder !== "") {
            writer.uint32(10).string(message.bidder);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(18).fork()).ldelim();
        }
        if (message.round !== "0") {
            writer.uint32(24).uint64(message.round);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBid();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bidder = reader.string();
                    break;
                case 2:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.round = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bidder: isSet(object.bidder) ? String(object.bidder) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
            round: isSet(object.round) ? String(object.round) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bidder !== undefined && (obj.bidder = message.bidder);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        message.round !== undefined && (obj.round = message.round);
        return obj;
    },
    create: function (base) {
        return exports.EventBid.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventBid();
        message.bidder = (_a = object.bidder) !== null && _a !== void 0 ? _a : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        message.round = (_b = object.round) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseEventAuctionResult() {
    return { winner: "", amount: undefined, round: "0" };
}
exports.EventAuctionResult = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.winner !== "") {
            writer.uint32(10).string(message.winner);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(18).fork()).ldelim();
        }
        if (message.round !== "0") {
            writer.uint32(24).uint64(message.round);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAuctionResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.winner = reader.string();
                    break;
                case 2:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.round = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            winner: isSet(object.winner) ? String(object.winner) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
            round: isSet(object.round) ? String(object.round) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.winner !== undefined && (obj.winner = message.winner);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        message.round !== undefined && (obj.round = message.round);
        return obj;
    },
    create: function (base) {
        return exports.EventAuctionResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventAuctionResult();
        message.winner = (_a = object.winner) !== null && _a !== void 0 ? _a : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        message.round = (_b = object.round) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseEventAuctionStart() {
    return { round: "0", endingTimestamp: "0", newBasket: [] };
}
exports.EventAuctionStart = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.round !== "0") {
            writer.uint32(8).uint64(message.round);
        }
        if (message.endingTimestamp !== "0") {
            writer.uint32(16).int64(message.endingTimestamp);
        }
        try {
            for (var _b = __values(message.newBasket), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                coin_1.Coin.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAuctionStart();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.round = longToString(reader.uint64());
                    break;
                case 2:
                    message.endingTimestamp = longToString(reader.int64());
                    break;
                case 3:
                    message.newBasket.push(coin_1.Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            round: isSet(object.round) ? String(object.round) : "0",
            endingTimestamp: isSet(object.endingTimestamp) ? String(object.endingTimestamp) : "0",
            newBasket: Array.isArray(object === null || object === void 0 ? void 0 : object.newBasket) ? object.newBasket.map(function (e) { return coin_1.Coin.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.round !== undefined && (obj.round = message.round);
        message.endingTimestamp !== undefined && (obj.endingTimestamp = message.endingTimestamp);
        if (message.newBasket) {
            obj.newBasket = message.newBasket.map(function (e) { return e ? coin_1.Coin.toJSON(e) : undefined; });
        }
        else {
            obj.newBasket = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventAuctionStart.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventAuctionStart();
        message.round = (_a = object.round) !== null && _a !== void 0 ? _a : "0";
        message.endingTimestamp = (_b = object.endingTimestamp) !== null && _b !== void 0 ? _b : "0";
        message.newBasket = ((_c = object.newBasket) === null || _c === void 0 ? void 0 : _c.map(function (e) { return coin_1.Coin.fromPartial(e); })) || [];
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
