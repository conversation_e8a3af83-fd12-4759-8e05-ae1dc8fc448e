import { GoogleProtobufAny, CosmosAuthzV1Beta1Tx, CosmosAuthzV1Beta1Authz, GoogleProtobufTimestamp } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
/**
 * @deprecated please use MsgGrantWithAuthorization
 */
export declare namespace MsgGrant {
    interface Params {
        /**
         * @deprecated Use `authorization` instead - for generic authorizations,
         * use `getGenericAuthorizationFromMessageType` function
         * to get the authorization object from messageType
         */
        messageType?: string;
        authorization?: GoogleProtobufAny.Any;
        grantee: string;
        granter: string;
        expiration?: number;
        expiryInYears?: number;
        expiryInSeconds?: number;
    }
    type Proto = CosmosAuthzV1Beta1Tx.MsgGrant;
    type Object = Omit<CosmosAuthzV1Beta1Tx.MsgGrant, 'msgs'> & {
        msgs: any;
    };
}
/**
 * @category Messages
 */
export default class MsgGrant extends MsgBase<MsgGrant.Params, MsgGrant.Proto> {
    static fromJSON(params: MsgGrant.Params): MsgGrant;
    toProto(): CosmosAuthzV1Beta1Tx.MsgGrant;
    toData(): {
        granter: string;
        grantee: string;
        grant: CosmosAuthzV1Beta1Authz.Grant | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: MsgGrant.Object;
    };
    toDirectSign(): {
        type: string;
        message: CosmosAuthzV1Beta1Tx.MsgGrant;
    };
    toWeb3Gw(): {
        granter: string;
        grantee: string;
        grant: {
            authorization: {
                '@type': string;
                msg: string;
            };
            expiration: string;
        };
        '@type': string;
    };
    getTimestamp(): GoogleProtobufTimestamp.Timestamp;
    toBinary(): Uint8Array;
}
