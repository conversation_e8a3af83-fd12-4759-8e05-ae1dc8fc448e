"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcAccountPortfolioStream = void 0;
const index_js_1 = require("../transformers/index.js");
const BaseIndexerGrpcWebConsumer_js_1 = require("../../base/BaseIndexerGrpcWebConsumer.js");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
/**
 * @category Indexer Grpc Stream
 */
class IndexerGrpcAccountPortfolioStream {
    client;
    constructor(endpoint) {
        this.client = new indexer_proto_ts_1.InjectivePortfolioRpc.InjectivePortfolioRPCClientImpl((0, BaseIndexerGrpcWebConsumer_js_1.getGrpcIndexerWebImpl)(endpoint));
    }
    streamAccountPortfolio({ subaccountId, accountAddress, type, callback, onEndCallback, onStatusCallback, }) {
        const request = indexer_proto_ts_1.InjectivePortfolioRpc.StreamAccountPortfolioRequest.create();
        request.accountAddress = accountAddress;
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (type) {
            request.type = type;
        }
        const subscription = this.client.StreamAccountPortfolio(request).subscribe({
            next(response) {
                callback(index_js_1.IndexerAccountPortfolioStreamTransformer.accountPortfolioStreamCallback(response));
            },
            error(err) {
                if (onStatusCallback) {
                    onStatusCallback(err);
                }
            },
            complete() {
                if (onEndCallback) {
                    onEndCallback();
                }
            },
        });
        return subscription;
    }
}
exports.IndexerGrpcAccountPortfolioStream = IndexerGrpcAccountPortfolioStream;
