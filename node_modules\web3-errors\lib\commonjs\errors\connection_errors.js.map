{"version": 3, "file": "connection_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/connection_errors.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAKF,sDAS2B;AAC3B,8DAAsD;AAEtD,MAAa,eAAgB,SAAQ,kCAAa;IAKjD,YAAmB,OAAe,EAAE,KAAuB;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QALT,SAAI,GAAG,yBAAQ,CAAC;QAOtB,IAAI,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;QACjC,CAAC;IACF,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,IAAG;IACxF,CAAC;CACD;AAjBD,0CAiBC;AAED,MAAa,sBAAuB,SAAQ,eAAe;IAC1D,YAA0B,IAAY,EAAE,KAAuB;QAC9D,KAAK,CAAC,8CAA8C,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;QAD3C,SAAI,GAAJ,IAAI,CAAQ;QAErC,IAAI,CAAC,IAAI,GAAG,iCAAgB,CAAC;IAC9B,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,IAAI,EAAE,IAAI,CAAC,IAAI,IAAG;IAC/C,CAAC;CACD;AATD,wDASC;AAED,MAAa,sBAAuB,SAAQ,eAAe;IAC1D,YAA0B,QAAgB;QACzC,KAAK,CAAC,kCAAkC,QAAQ,aAAa,CAAC,CAAC;QADtC,aAAQ,GAAR,QAAQ,CAAQ;QAEzC,IAAI,CAAC,IAAI,GAAG,iCAAgB,CAAC;IAC9B,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAG;IACvD,CAAC;CACD;AATD,wDASC;AAED,MAAa,sBAAuB,SAAQ,eAAe;IAC1D,YAAmB,KAAuB;QACzC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,kCAAiB,CAAC;IAC/B,CAAC;CACD;AALD,wDAKC;AAED,MAAa,oBAAqB,SAAQ,eAAe;IACxD,YAAmB,KAAuB;;QACzC,KAAK,CACJ,mEACC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,mCAAI,EAChB,oCAAoC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,mCAAI,EAAE,EAAE,EACzD,KAAK,CACL,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,+BAAc,CAAC;IAC5B,CAAC;CACD;AAVD,oDAUC;AAED,MAAa,qCAAsC,SAAQ,eAAe;IACzE,YAAmB,gBAAwB;QAC1C,KAAK,CAAC,kDAAkD,gBAAgB,GAAG,CAAC,CAAC;QAC7E,IAAI,CAAC,IAAI,GAAG,sCAAqB,CAAC;IACnC,CAAC;CACD;AALD,sFAKC;AAED,MAAa,kCAAmC,SAAQ,eAAe;IACtE;QACC,KAAK,CAAC,mFAAmF,CAAC,CAAC;QAC3F,IAAI,CAAC,IAAI,GAAG,0CAAyB,CAAC;IACvC,CAAC;CACD;AALD,gFAKC;AAED,MAAa,uBAAwB,SAAQ,eAAe;IAC3D,YAAmB,EAAmB;QACrC,KAAK,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,IAAI,GAAG,qCAAoB,CAAC;IAClC,CAAC;CACD;AALD,0DAKC"}