import { Trie } from '@ethereumjs/trie';
import { CLRequestType, Withdrawal } from '@ethereumjs/util';
import { BlockHeader } from './header.js';
import type { BeaconPayloadJson } from './from-beacon-payload.js';
import type { BlockBytes, BlockData, BlockOptions, ExecutionPayload, JsonBlock, JsonRpcBlock } from './types.js';
import type { Common } from '@ethereumjs/common';
import type { TypedTransaction } from '@ethereumjs/tx';
import type { CLRequest, EthersProvider, VerkleExecutionWitness } from '@ethereumjs/util';
/**
 * An object that represents the block.
 */
export declare class Block {
    readonly header: BlockHeader;
    readonly transactions: TypedTransaction[];
    readonly uncleHeaders: BlockHeader[];
    readonly withdrawals?: Withdrawal[];
    readonly requests?: CLRequest<CLRequestType>[];
    readonly common: Common;
    protected keccakFunction: (msg: Uint8Array) => Uint8Array;
    /**
     * EIP-6800: Verkle Proof Data (experimental)
     * null implies that the non default executionWitness might exist but not available
     * and will not lead to execution of the block via vm with verkle stateless manager
     */
    readonly executionWitness?: VerkleExecutionWitness | null;
    protected cache: {
        txTrieRoot?: Uint8Array;
        withdrawalsTrieRoot?: Uint8Array;
        requestsRoot?: Uint8Array;
    };
    /**
     * Returns the withdrawals trie root for array of Withdrawal.
     * @param wts array of Withdrawal to compute the root of
     * @param optional emptyTrie to use to generate the root
     */
    static genWithdrawalsTrieRoot(wts: Withdrawal[], emptyTrie?: Trie): Promise<Uint8Array>;
    /**
     * Returns the txs trie root for array of TypedTransaction
     * @param txs array of TypedTransaction to compute the root of
     * @param optional emptyTrie to use to generate the root
     */
    static genTransactionsTrieRoot(txs: TypedTransaction[], emptyTrie?: Trie): Promise<Uint8Array>;
    /**
     * Returns the requests trie root for an array of CLRequests
     * @param requests - an array of CLRequests
     * @param emptyTrie optional empty trie used to generate the root
     * @returns a 32 byte Uint8Array representing the requests trie root
     */
    static genRequestsTrieRoot(requests: CLRequest<CLRequestType>[], emptyTrie?: Trie): Promise<Uint8Array>;
    /**
     * Static constructor to create a block from a block data dictionary
     *
     * @param blockData
     * @param opts
     */
    static fromBlockData(blockData?: BlockData, opts?: BlockOptions): Block;
    /**
     * Static constructor to create a block from a RLP-serialized block
     *
     * @param serialized
     * @param opts
     */
    static fromRLPSerializedBlock(serialized: Uint8Array, opts?: BlockOptions): Block;
    /**
     * Static constructor to create a block from an array of Bytes values
     *
     * @param values
     * @param opts
     */
    static fromValuesArray(values: BlockBytes, opts?: BlockOptions): Block;
    /**
     * Creates a new block object from Ethereum JSON RPC.
     *
     * @param blockParams - Ethereum JSON RPC of block (eth_getBlockByNumber)
     * @param uncles - Optional list of Ethereum JSON RPC of uncles (eth_getUncleByBlockHashAndIndex)
     * @param opts - An object describing the blockchain
     */
    static fromRPC(blockData: JsonRpcBlock, uncles?: any[], opts?: BlockOptions): Block;
    /**
     *  Method to retrieve a block from a JSON-RPC provider and format as a {@link Block}
     * @param provider either a url for a remote provider or an Ethers JsonRpcProvider object
     * @param blockTag block hash or block number to be run
     * @param opts {@link BlockOptions}
     * @returns the block specified by `blockTag`
     */
    static fromJsonRpcProvider: (provider: string | EthersProvider, blockTag: string | bigint, opts: BlockOptions) => Promise<Block>;
    /**
     *  Method to retrieve a block from an execution payload
     * @param execution payload constructed from beacon payload
     * @param opts {@link BlockOptions}
     * @returns the block constructed block
     */
    static fromExecutionPayload(payload: ExecutionPayload, opts?: BlockOptions): Promise<Block>;
    /**
     *  Method to retrieve a block from a beacon payload json
     * @param payload json of a beacon beacon fetched from beacon apis
     * @param opts {@link BlockOptions}
     * @returns the block constructed block
     */
    static fromBeaconPayloadJson(payload: BeaconPayloadJson, opts?: BlockOptions): Promise<Block>;
    /**
     * This constructor takes the values, validates them, assigns them and freezes the object.
     * Use the static factory methods to assist in creating a Block object from varying data types and options.
     */
    constructor(header?: BlockHeader, transactions?: TypedTransaction[], uncleHeaders?: BlockHeader[], withdrawals?: Withdrawal[], opts?: BlockOptions, requests?: CLRequest<CLRequestType>[], executionWitness?: VerkleExecutionWitness | null);
    /**
     * Returns a Array of the raw Bytes Arrays of this block, in order.
     */
    raw(): BlockBytes;
    /**
     * Returns the hash of the block.
     */
    hash(): Uint8Array;
    /**
     * Determines if this block is the genesis block.
     */
    isGenesis(): boolean;
    /**
     * Returns the rlp encoding of the block.
     */
    serialize(): Uint8Array;
    /**
     * Generates transaction trie for validation.
     */
    genTxTrie(): Promise<Uint8Array>;
    /**
     * Validates the transaction trie by generating a trie
     * and do a check on the root hash.
     * @returns True if the transaction trie is valid, false otherwise
     */
    transactionsTrieIsValid(): Promise<boolean>;
    requestsTrieIsValid(requestsInput?: CLRequest<CLRequestType>[]): Promise<boolean>;
    /**
     * Validates transaction signatures and minimum gas requirements.
     * @returns {string[]} an array of error strings
     */
    getTransactionsValidationErrors(): string[];
    /**
     * Validates transaction signatures and minimum gas requirements.
     * @returns True if all transactions are valid, false otherwise
     */
    transactionsAreValid(): boolean;
    /**
     * Validates the block data, throwing if invalid.
     * This can be checked on the Block itself without needing access to any parent block
     * It checks:
     * - All transactions are valid
     * - The transactions trie is valid
     * - The uncle hash is valid
     * @param onlyHeader if only passed the header, skip validating txTrie and unclesHash (default: false)
     * @param verifyTxs if set to `false`, will not check for transaction validation errors (default: true)
     */
    validateData(onlyHeader?: boolean, verifyTxs?: boolean): Promise<void>;
    /**
     * Validates that blob gas fee for each transaction is greater than or equal to the
     * blobGasPrice for the block and that total blob gas in block is less than maximum
     * blob gas per block
     * @param parentHeader header of parent block
     */
    validateBlobTransactions(parentHeader: BlockHeader): void;
    /**
     * Validates the uncle's hash.
     * @returns true if the uncle's hash is valid, false otherwise.
     */
    uncleHashIsValid(): boolean;
    /**
     * Validates the withdrawal root
     * @returns true if the withdrawals trie root is valid, false otherwise
     */
    withdrawalsTrieIsValid(): Promise<boolean>;
    /**
     * Consistency checks for uncles included in the block, if any.
     *
     * Throws if invalid.
     *
     * The rules for uncles checked are the following:
     * Header has at most 2 uncles.
     * Header does not count an uncle twice.
     */
    validateUncles(): void;
    /**
     * Returns the canonical difficulty for this block.
     *
     * @param parentBlock - the parent of this `Block`
     */
    ethashCanonicalDifficulty(parentBlock: Block): bigint;
    /**
     * Validates if the block gasLimit remains in the boundaries set by the protocol.
     * Throws if invalid
     *
     * @param parentBlock - the parent of this `Block`
     */
    validateGasLimit(parentBlock: Block): void;
    /**
     * Returns the block in JSON format.
     */
    toJSON(): JsonBlock;
    /**
     * Maps the block properties to the execution payload structure from the beacon chain,
     * see https://github.com/ethereum/consensus-specs/blob/dev/specs/bellatrix/beacon-chain.md#ExecutionPayload
     *
     * @returns dict with the execution payload parameters with camel case naming
     */
    toExecutionPayload(): ExecutionPayload;
    /**
     * Return a compact error string representation of the object
     */
    errorStr(): string;
    /**
     * Internal helper function to create an annotated error message
     *
     * @param msg Base error message
     * @hidden
     */
    protected _errorMsg(msg: string): string;
}
//# sourceMappingURL=block.d.ts.map