"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ASSET_PRICE_URL_BY_NETWORK = exports.PEGGY_GRAPH_URL_BY_NETWORK = exports.INJ_NAME_REVERSE_RESOLVER_CONTRACT_BY_NETWORK = exports.INJ_NAME_REGISTRY_CONTRACT_BY_NETWORK = exports.INCENTIVES_CONTRACT_BY_NETWORK = exports.CW20_SWAP_CONTRACT_BY_NETWORK = exports.CW20_ADAPTER_CONTRACT_BY_NETWORK = void 0;
const types_js_1 = require("./types.js");
exports.CW20_ADAPTER_CONTRACT_BY_NETWORK = {
    [types_js_1.Network.Mainnet]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [types_js_1.Network.MainnetLB]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [types_js_1.Network.MainnetOld]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [types_js_1.Network.MainnetK8s]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [types_js_1.Network.MainnetSentry]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [types_js_1.Network.Staging]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [types_js_1.Network.Internal]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [types_js_1.Network.Testnet]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [types_js_1.Network.TestnetK8s]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [types_js_1.Network.TestnetSentry]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [types_js_1.Network.TestnetOld]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [types_js_1.Network.Devnet]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [types_js_1.Network.Devnet1]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [types_js_1.Network.Devnet2]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [types_js_1.Network.Devnet3]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [types_js_1.Network.Local]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
};
exports.CW20_SWAP_CONTRACT_BY_NETWORK = {
    [types_js_1.Network.Mainnet]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [types_js_1.Network.MainnetLB]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [types_js_1.Network.MainnetOld]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [types_js_1.Network.MainnetK8s]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [types_js_1.Network.MainnetSentry]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [types_js_1.Network.Staging]: 'inj12yj3mtjarujkhcp6lg3klxjjfrx2v7v8yswgp9',
    [types_js_1.Network.Internal]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [types_js_1.Network.Testnet]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [types_js_1.Network.TestnetK8s]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [types_js_1.Network.TestnetSentry]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [types_js_1.Network.TestnetOld]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [types_js_1.Network.Devnet]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [types_js_1.Network.Devnet1]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [types_js_1.Network.Devnet2]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [types_js_1.Network.Devnet3]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [types_js_1.Network.Local]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
};
exports.INCENTIVES_CONTRACT_BY_NETWORK = {
    [types_js_1.Network.Mainnet]: '',
    [types_js_1.Network.MainnetLB]: '',
    [types_js_1.Network.MainnetOld]: '',
    [types_js_1.Network.MainnetK8s]: '',
    [types_js_1.Network.MainnetSentry]: '',
    [types_js_1.Network.Staging]: '',
    [types_js_1.Network.Internal]: '',
    [types_js_1.Network.Testnet]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [types_js_1.Network.TestnetK8s]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [types_js_1.Network.TestnetSentry]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [types_js_1.Network.TestnetOld]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [types_js_1.Network.Devnet]: '',
    [types_js_1.Network.Devnet1]: '',
    [types_js_1.Network.Devnet2]: '',
    [types_js_1.Network.Devnet3]: '',
    [types_js_1.Network.Local]: '',
};
exports.INJ_NAME_REGISTRY_CONTRACT_BY_NETWORK = {
    [types_js_1.Network.Mainnet]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [types_js_1.Network.MainnetLB]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [types_js_1.Network.MainnetK8s]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [types_js_1.Network.MainnetSentry]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [types_js_1.Network.MainnetOld]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [types_js_1.Network.Staging]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [types_js_1.Network.Internal]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [types_js_1.Network.Testnet]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.TestnetK8s]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.TestnetSentry]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.TestnetOld]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.Devnet]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.Devnet1]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.Devnet2]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.Devnet3]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [types_js_1.Network.Local]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
};
exports.INJ_NAME_REVERSE_RESOLVER_CONTRACT_BY_NETWORK = {
    [types_js_1.Network.Mainnet]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [types_js_1.Network.MainnetLB]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [types_js_1.Network.MainnetK8s]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [types_js_1.Network.MainnetSentry]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [types_js_1.Network.MainnetOld]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [types_js_1.Network.Staging]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [types_js_1.Network.Internal]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [types_js_1.Network.Testnet]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.TestnetK8s]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.TestnetSentry]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.TestnetOld]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.Devnet]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.Devnet1]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.Devnet2]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.Devnet3]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [types_js_1.Network.Local]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
};
exports.PEGGY_GRAPH_URL_BY_NETWORK = {
    [types_js_1.Network.Mainnet]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [types_js_1.Network.MainnetLB]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [types_js_1.Network.MainnetK8s]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [types_js_1.Network.MainnetSentry]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [types_js_1.Network.MainnetOld]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [types_js_1.Network.Staging]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [types_js_1.Network.Internal]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [types_js_1.Network.Testnet]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [types_js_1.Network.TestnetK8s]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [types_js_1.Network.TestnetSentry]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [types_js_1.Network.TestnetOld]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [types_js_1.Network.Devnet]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [types_js_1.Network.Devnet1]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [types_js_1.Network.Devnet2]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [types_js_1.Network.Devnet3]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [types_js_1.Network.Local]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
};
exports.ASSET_PRICE_URL_BY_NETWORK = {
    [types_js_1.Network.Mainnet]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.MainnetLB]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.MainnetK8s]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.MainnetSentry]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.MainnetOld]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.Staging]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.Internal]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.Testnet]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.TestnetK8s]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.TestnetSentry]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.TestnetOld]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [types_js_1.Network.Devnet]: 'https://devnet.asset.injective.dev/asset-price/v1',
    [types_js_1.Network.Devnet1]: 'https://devnet.api.injective.dev/asset-price/v1',
    [types_js_1.Network.Devnet2]: 'https://devnet.api.injective.dev/asset-price/v1',
    [types_js_1.Network.Devnet3]: 'https://devnet.api.injective.dev/asset-price/v1',
    [types_js_1.Network.Local]: 'https://devnet.api.injective.dev/asset-price/v1',
};
