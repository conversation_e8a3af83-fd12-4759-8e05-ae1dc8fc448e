{"version": 3, "file": "eip1559Transaction.js", "sourceRoot": "", "sources": ["../../src/eip1559Transaction.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AACrC,2CAWyB;AAEzB,6DAAsD;AACtD,qDAAoD;AACpD,qDAAoD;AACpD,qDAAoD;AACpD,mDAAkD;AAClD,yCAA4C;AAC5C,uCAAoD;AAepD;;;;;GAKG;AACH,MAAa,2BAA4B,SAAQ,oCAAiD;IAqGhG;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;QACrD,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,0BAAe,CAAC,gBAAgB,EAAE,EAAE,IAAI,CAAC,CAAA;QAClE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAAG,MAAM,CAAA;QAE1E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QAEpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAE5E,kCAAkC;QAClC,MAAM,cAAc,GAAG,qBAAW,CAAC,iBAAiB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,qBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,IAAI,CAAC,YAAY,GAAG,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,YAAY,CAAC,CAAC,CAAA;QACxD,IAAI,CAAC,oBAAoB,GAAG,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,oBAAoB,CAAC,CAAC,CAAA;QAExE,IAAI,CAAC,+BAA+B,CAAC;YACnC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;SAChD,CAAC,CAAA;QAEF,oCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,kBAAW,EAAE;YACnD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,6DAA6D,CAAC,CAAA;YACzF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,iGAAiG,CAClG,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QAE1B,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IAlJD;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC3D,OAAO,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACtD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QACzE,IACE,IAAA,kBAAW,EAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAA,qBAAW,EAAC,0BAAe,CAAC,gBAAgB,CAAC,CAAC;YACrF,KAAK,EACL;YACA,MAAM,IAAI,KAAK,CACb,sFACE,0BAAe,CAAC,gBAClB,eAAe,IAAA,iBAAU,EAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CACvD,CAAA;SACF;QAED,MAAM,MAAM,GAAG,SAAG,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;QAEjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,2BAA2B,CAAC,eAAe,CAAC,MAAuB,EAAE,IAAI,CAAC,CAAA;IACnF,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACvE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAA;SACF;QAED,MAAM,CACJ,OAAO,EACP,KAAK,EACL,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,CAAC,EACD,CAAC,EACD,CAAC,EACF,GAAG,MAAM,CAAA;QAEV,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACtC,IAAA,8BAAuB,EAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEhG,OAAO,IAAI,2BAA2B,CACpC;YACE,OAAO,EAAE,IAAA,oBAAa,EAAC,OAAO,CAAC;YAC/B,KAAK;YACL,oBAAoB;YACpB,YAAY;YACZ,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,IAAI,EAAE;YAC5B,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oBAAa,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACjD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IA2DD;;OAEG;IACH,UAAU;QACR,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IAED;;;OAGG;IACH,uBAAuB,CAAC,OAAe;QACrC,OAAO,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACvD,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,UAAkB,eAAQ;QACvC,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC9C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,4BAAqB,EAAC,IAAI,CAAC,OAAO,CAAC;YACnC,IAAA,4BAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAA,4BAAqB,EAAC,IAAI,CAAC,oBAAoB,CAAC;YAChD,IAAA,4BAAqB,EAAC,IAAI,CAAC,YAAY,CAAC;YACxC,IAAA,4BAAqB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACzD,IAAA,4BAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,4BAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,4BAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,4BAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;SACzE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS;QACP,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;;;;;OAUG;IACH,gBAAgB;QACd,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACxD,CAAC;IAED;;;;;;OAMG;IACH,sBAAsB;QACpB,OAAO,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED;;OAEG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;IACtC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC;IAED,YAAY,CACV,CAAS,EACT,CAAsB,EACtB,CAAsB,EACtB,WAAoB,KAAK;QAEzB,CAAC,GAAG,IAAA,cAAO,EAAC,CAAC,CAAC,CAAA;QACd,CAAC,GAAG,IAAA,cAAO,EAAC,CAAC,CAAC,CAAA;QACd,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,2BAA2B,CAAC,UAAU,CAC3C;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,EAAE,IAAA,oBAAa,EAAC,CAAC,CAAC;YACnB,CAAC,EAAE,IAAA,oBAAa,EAAC,CAAC,CAAC;SACpB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,cAAc,GAAG,qBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACrE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;QAE/B,OAAO;YACL,GAAG,QAAQ;YACX,OAAO,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,OAAO,CAAC;YAClC,oBAAoB,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5D,YAAY,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,YAAY,CAAC;YAC5C,UAAU,EAAE,cAAc;SAC3B,CAAA;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,iBAAiB,IAAI,CAAC,YAAY,yBAAyB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClG,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACnC,CAAC;CACF;AAlVD,kEAkVC"}