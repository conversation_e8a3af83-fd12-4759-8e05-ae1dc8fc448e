{"version": 3, "file": "transaction_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/transaction_errors.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAWF,OAAO,EACN,oBAAoB,EACpB,MAAM,EACN,oBAAoB,EACpB,0BAA0B,EAC1B,wBAAwB,EACxB,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,EACrB,wBAAwB,EACxB,mBAAmB,EACnB,yBAAyB,EACzB,6BAA6B,EAC7B,mCAAmC,EACnC,gCAAgC,EAChC,yBAAyB,EACzB,gCAAgC,EAChC,qBAAqB,EACrB,qBAAqB,EACrB,uBAAuB,EACvB,iCAAiC,EACjC,yBAAyB,EACzB,2BAA2B,EAC3B,8BAA8B,EAC9B,kBAAkB,EAClB,0BAA0B,EAC1B,gBAAgB,EAChB,iBAAiB,EACjB,sBAAsB,EACtB,mCAAmC,EACnC,wCAAwC,EACxC,yBAAyB,EACzB,yBAAyB,EACzB,4BAA4B,EAC5B,mBAAmB,EACnB,cAAc,EACd,+BAA+B,EAC/B,2BAA2B,EAC3B,uBAAuB,EACvB,sCAAsC,EACtC,kCAAkC,EAClC,8BAA8B,EAC9B,+BAA+B,GAC/B,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEzE,MAAM,OAAO,gBAAmD,SAAQ,aAAa;IAGpF,YAAmB,OAAe,EAAS,OAAqB;QAC/D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD2B,YAAO,GAAP,OAAO,CAAc;QAFzD,SAAI,GAAG,MAAM,CAAC;IAIrB,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAG;IACrD,CAAC;CACD;AAED,MAAM,OAAO,sBAAuB,SAAQ,aAAa;IAGxD,YAA0B,MAAc,EAAS,SAAiB;QACjE,KAAK,CAAC,+DAA+D,MAAM,EAAE,CAAC,CAAC;QADtD,WAAM,GAAN,MAAM,CAAQ;QAAS,cAAS,GAAT,SAAS,CAAQ;QAF3D,SAAI,GAAG,yBAAyB,CAAC;IAIxC,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,IAAG;IAC9E,CAAC;CACD;AAED,MAAM,OAAO,iCAEX,SAAQ,aAAa;IAGtB,YACQ,MAAc,EACd,SAAkB,EAClB,OAAqB,EACrB,IAAa;QAEpB,KAAK,CACJ,2CACC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,EAC3E,EAAE,CACF,CAAC;QATK,WAAM,GAAN,MAAM,CAAQ;QACd,cAAS,GAAT,SAAS,CAAS;QAClB,YAAO,GAAP,OAAO,CAAc;QACrB,SAAI,GAAJ,IAAI,CAAS;QANd,SAAI,GAAG,yBAAyB,CAAC;IAaxC,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,IAAI,EAAE,IAAI,CAAC,IAAI,IACd;IACH,CAAC;CACD;AAED;;;;GAIG;AACH,MAAM,OAAO,gCAEX,SAAQ,iCAA8C;IAGvD,YACQ,MAAc,EACd,eAAuB,EACvB,2BAAmC,EACnC,oBAA6C,EAC7C,SAAkB,EAClB,OAAqB,EACrB,IAAa;QAEpB,KAAK,CAAC,MAAM,CAAC,CAAC;QARP,WAAM,GAAN,MAAM,CAAQ;QACd,oBAAe,GAAf,eAAe,CAAQ;QACvB,gCAA2B,GAA3B,2BAA2B,CAAQ;QACnC,yBAAoB,GAApB,oBAAoB,CAAyB;QAC7C,cAAS,GAAT,SAAS,CAAS;QAClB,YAAO,GAAP,OAAO,CAAc;QACrB,SAAI,GAAJ,IAAI,CAAS;QATd,SAAI,GAAG,sCAAsC,CAAC;IAYrD,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,eAAe,EAAE,IAAI,CAAC,eAAe,EACrC,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,EAC7D,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAC/C,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,IAAI,EAAE,IAAI,CAAC,IAAI,IACd;IACH,CAAC;CACD;AAED,MAAM,OAAO,2BAA4B,SAAQ,gBAAgB;IAChE,YAAmB,OAA2B;QAC7C,KAAK,CAAC,4DAA4D,EAAE,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IACxC,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAG;IACrD,CAAC;CACD;AAED,MAAM,OAAO,0BAA2B,SAAQ,gBAAgB;IAC/D,YAAmB,OAA2B;QAC7C,KAAK,CAAC,oEAAoE,EAAE,OAAO,CAAC,CAAC;QACrF,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IACxC,CAAC;CACD;AAED,MAAM,OAAO,qCAEX,SAAQ,gBAA6B;IACtC,YAAmB,OAAqB;QACvC,KAAK,CACJ,2CACC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,EAC3E,EAAE,EACF,OAAO,CACP,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;IAC1C,CAAC;CACD;AAED,MAAM,OAAO,wBAAyB,SAAQ,gBAAgB;IAC7D,YAAmB,OAA2B;QAC7C,KAAK,CACJ,0DAA0D,IAAI,CAAC,SAAS,CACvE,OAAO,EACP,SAAS,EACT,CAAC,CACD,EAAE,EACH,OAAO,CACP,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAC/B,CAAC;CACD;AAED,MAAM,OAAO,4BAA6B,SAAQ,gBAAgB;IACjE;QACC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IAClC,CAAC;CACD;AACD,MAAM,OAAO,mBAAoB,SAAQ,gBAAgB;IACxD;QACC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC9B,CAAC;CACD;AAED,MAAM,OAAO,4BAA6B,SAAQ,iBAAiB;IAGlE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,yCAAyC,CAAC,CAAC;QAHlD,SAAI,GAAG,qBAAqB,CAAC;IAIpC,CAAC;CACD;AACD,MAAM,OAAO,8BAA+B,SAAQ,iBAAiB;IAGpE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,2CAA2C,CAAC,CAAC;QAHpD,SAAI,GAAG,uBAAuB,CAAC;IAItC,CAAC;CACD;AACD,MAAM,OAAO,sBAAuB,SAAQ,iBAAiB;IAG5D,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,0BAA0B,CAAC,CAAC;QAHnC,SAAI,GAAG,mBAAmB,CAAC;IAIlC,CAAC;CACD;AAED,MAAM,OAAO,uBAAwB,SAAQ,iBAAiB;IAG7D;QACC,KAAK,CACJ,yBAAyB,EACzB,6DAA6D,CAC7D,CAAC;QANI,SAAI,GAAG,2BAA2B,CAAC;IAO1C,CAAC;CACD;AAED,MAAM,OAAO,yBAA0B,SAAQ,iBAAiB;IAG/D;QACC,KAAK,CACJ,2BAA2B,EAC3B,+FAA+F,CAC/F,CAAC;QANI,SAAI,GAAG,8BAA8B,CAAC;IAO7C,CAAC;CACD;AAED,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IAG1D,YAAmB,KAAqD;QACvE,KAAK,CACJ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QACrB,kIAAkI;QAClI,mEAAmE,CACnE,CAAC;QAPI,SAAI,GAAG,wBAAwB,CAAC;IAQvC,CAAC;CACD;AAED,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IAGxD,YAAmB,KAA+C;QACjE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,oDAAoD,CAAC,CAAC;QAH7E,SAAI,GAAG,qBAAqB,CAAC;IAIpC,CAAC;CACD;AAED,MAAM,OAAO,qBAAsB,SAAQ,iBAAiB;IAG3D,YAAmB,KAAuD;QACzE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,yDAAyD,CAAC,CAAC;QAHlF,SAAI,GAAG,wBAAwB,CAAC;IAIvC,CAAC;CACD;AAED,MAAM,OAAO,6BAA8B,SAAQ,iBAAiB;IAGnE;QACC,KAAK,CACJ,+BAA+B,EAC/B,2FAA2F,CAC3F,CAAC;QANI,SAAI,GAAG,yBAAyB,CAAC;IAOxC,CAAC;CACD;AAED,MAAM,OAAO,2BAA4B,SAAQ,iBAAiB;IAGjE,YAAmB,KAAkE;;QACpF,KAAK,CACJ,6BAA6B,EAC7B,sFACC,MAAA,KAAK,CAAC,KAAK,mCAAI,WAChB,iBAAiB,MAAA,KAAK,CAAC,QAAQ,mCAAI,WAAW,EAAE,CAChD,CAAC;QARI,SAAI,GAAG,yBAAyB,CAAC;IASxC,CAAC;CACD;AAED,MAAM,OAAO,oBAAqB,SAAQ,aAAa;IAGtD;QACC,KAAK,CACJ,2KAA2K,CAC3K,CAAC;QALI,SAAI,GAAG,8BAA8B,CAAC;IAM7C,CAAC;CACD;AAED,MAAM,OAAO,eAAgB,SAAQ,iBAAiB;IAGrD,YAAmB,KAKlB;;QACA,KAAK,CACJ,QAAQ,MAAA,KAAK,CAAC,GAAG,mCAAI,WAAW,eAC/B,MAAA,KAAK,CAAC,QAAQ,mCAAI,WACnB,2BAA2B,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACnE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,kBAAkB,CAClB,CAAC;QAfI,SAAI,GAAG,kBAAkB,CAAC;QAgBhC,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACzC,CAAC;CACD;AAED,MAAM,OAAO,gCAAiC,SAAQ,aAAa;IAGlE;QACC,KAAK,CACJ,qLAAqL,CACrL,CAAC;QALI,SAAI,GAAG,+BAA+B,CAAC;IAM9C,CAAC;CACD;AAED,MAAM,OAAO,2BAA4B,SAAQ,iBAAiB;IAGjE,YAAmB,KAKlB;;QACA,KAAK,CACJ,QAAQ,MAAA,KAAK,CAAC,GAAG,mCAAI,WAAW,eAC/B,MAAA,KAAK,CAAC,QAAQ,mCAAI,WACnB,2BAA2B,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACnE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,wEAAwE,CACxE,CAAC;QAfI,SAAI,GAAG,mBAAmB,CAAC;QAgBjC,IAAI,CAAC,KAAK,GAAG,IAAI,gCAAgC,EAAE,CAAC;IACrD,CAAC;CACD;AAED,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IAG1D,YAAmB,KAAkE;;QACpF,KAAK,CACJ,QAAQ,MAAA,KAAK,CAAC,GAAG,mCAAI,WAAW,eAAe,MAAA,KAAK,CAAC,QAAQ,mCAAI,WAAW,EAAE,EAC9E,iCAAiC,CACjC,CAAC;QANI,SAAI,GAAG,yBAAyB,CAAC;IAOxC,CAAC;CACD;AAED,MAAM,OAAO,yCAA0C,SAAQ,iBAAiB;IAG/E,YAAmB,KAGlB;;QACA,KAAK,CACJ,yBAAyB,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACjE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,sDAAsD,CACtD,CAAC;QAXI,SAAI,GAAG,6BAA6B,CAAC;IAY5C,CAAC;CACD;AAED,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IAG1D,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,8CAA8C,CAAC,CAAC;QAHvD,SAAI,GAAG,mCAAmC,CAAC;IAIlD,CAAC;CACD;AAED,MAAM,OAAO,yBAA0B,SAAQ,iBAAiB;IAG/D,YAAmB,KAGlB;;QACA,KAAK,CACJ,yBAAyB,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACjE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,0EAA0E,CAC1E,CAAC;QAXI,SAAI,GAAG,gCAAgC,CAAC;IAY/C,CAAC;CACD;AAED,MAAM,OAAO,6BAA8B,SAAQ,iBAAiB;IAGnE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,4BAA4B,CAAC,CAAC;QAHrC,SAAI,GAAG,qBAAqB,CAAC;IAIpC,CAAC;CACD;AAED,MAAM,OAAO,0BAA2B,SAAQ,iBAAiB;IAGhE,YAAmB,KAAmE;;QACrF,KAAK,CACJ,UAAU,MAAA,KAAK,CAAC,KAAK,mCAAI,WAAW,cAAc,MAAA,KAAK,CAAC,OAAO,mCAAI,WAAW,EAAE,EAChF,kCAAkC,CAClC,CAAC;QANI,SAAI,GAAG,gCAAgC,CAAC;IAO/C,CAAC;CACD;AAED,MAAM,OAAO,0BAA2B,SAAQ,iBAAiB;IAGhE;QACC,KAAK,CAAC,4BAA4B,EAAE,qDAAqD,CAAC,CAAC;QAHrF,SAAI,GAAG,+BAA+B,CAAC;IAI9C,CAAC;CACD;AAED,MAAM,OAAO,wBAAyB,SAAQ,iBAAiB;IAG9D;QACC,KAAK,CAAC,0BAA0B,EAAE,kCAAkC,CAAC,CAAC;QAHhE,SAAI,GAAG,2BAA2B,CAAC;IAI1C,CAAC;CACD;AAED,MAAM,OAAO,+BAAgC,SAAQ,iBAAiB;IAGrE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;QAHvC,SAAI,GAAG,uBAAuB,CAAC;IAItC,CAAC;CACD;AAED,MAAM,OAAO,4BAA6B,SAAQ,iBAAiB;IAGlE,YAAmB,KAAoE;;QACtF,KAAK,CACJ,SAAS,MAAA,KAAK,CAAC,IAAI,mCAAI,WAAW,YAAY,MAAA,KAAK,CAAC,KAAK,mCAAI,WAAW,EAAE,EAC1E,iIAAiI,CACjI,CAAC;QANI,SAAI,GAAG,qBAAqB,CAAC;IAOpC,CAAC;CACD;AAED,MAAM,OAAO,2BAA4B,SAAQ,aAAa;IAG7D,YAAmB,KAA2D;QAC7E,KAAK,CACJ,sDACC,KAAK,CAAC,eACP,0LACC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,eAC5D,EAAE,CACF,CAAC;QATI,SAAI,GAAG,mBAAmB,CAAC;IAUlC,CAAC;CACD;AAED,SAAS,sBAAsB,CAAC,eAAuB;IACtD,OAAO,gMACN,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,eAChD,EAAE,CAAC;AACJ,CAAC;AAED,MAAM,OAAO,8BAA+B,SAAQ,aAAa;IAGhE,YAAmB,KAA0D;QAC5E,KAAK,CACJ,oCACC,KAAK,CAAC,eACP,aAAa,sBAAsB,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAC5D,CAAC;QAPI,SAAI,GAAG,sBAAsB,CAAC;IAQrC,CAAC;CACD;AAED,MAAM,OAAO,4BAA6B,SAAQ,aAAa;IAG9D,YAAmB,KAIlB;QACA,KAAK,CACJ,0BAA0B,KAAK,CAAC,kBAAkB,6BACjD,KAAK,CAAC,cACP,YAAY,sBAAsB,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAC3D,CAAC;QAXI,SAAI,GAAG,oBAAoB,CAAC;IAYnC,CAAC;CACD;AAED,MAAM,OAAO,yCAA0C,SAAQ,iBAAiB;IAG/E,YAAmB,KAIlB;;QACA,KAAK,CACJ,YAAY,IAAI,CAAC,SAAS,CACzB,KAAK,CAAC,OAAO,CACb,gBAAgB,MAAA,KAAK,CAAC,SAAS,0CAAE,QAAQ,EAAE,sBAAsB,MAAA,KAAK,CAAC,eAAe,0CAAE,QAAQ,EAAE,EAAE,EACrG,mCAAmC,CACnC,CAAC;QAZI,SAAI,GAAG,wCAAwC,CAAC;IAavD,CAAC;CACD;AAED,MAAM,OAAO,yCAA0C,SAAQ,iBAAiB;IAG/E,YAAmB,KAAsC;QACxD,KAAK,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,8BAA8B,CAAC,CAAC;QAH7E,SAAI,GAAG,mCAAmC,CAAC;IAIlD,CAAC;CACD;AAED,MAAM,OAAO,uBAAwB,SAAQ,aAAa;IAEzD,YAAmB,YAAoB;QACtC,KAAK,CAAC,uBAAuB,YAAY,GAAG,CAAC,CAAC;QAFxC,SAAI,GAAG,cAAc,CAAC;IAG7B,CAAC;CACD;AAED,MAAM,OAAO,4BAA6B,SAAQ,iBAAiB;IAGlE;QACC,KAAK,CACJ,8BAA8B,EAC9B,wEAAwE,CACxE,CAAC;QANI,SAAI,GAAG,iCAAiC,CAAC;IAOhD,CAAC;CACD;AACD,MAAM,OAAO,wCAAyC,SAAQ,aAAa;IAG1E,YACC,eAA4C,EAC5C,MAA6B;QAE7B,MAAM,oBAAoB,GAAa,EAAE,CAAC;QAC1C,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3E,KAAK,CACJ,iEAAiE,MAAM,KAAK,oBAAoB,CAAC,IAAI,CACpG,IAAI,CACJ,EAAE,CACH,CAAC;QAZI,SAAI,GAAG,kCAAkC,CAAC;IAajD,CAAC;CACD"}