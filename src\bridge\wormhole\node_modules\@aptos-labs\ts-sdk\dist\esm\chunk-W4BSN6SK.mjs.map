{"version": 3, "sources": ["../../src/api/transactionSubmission/helpers.ts"], "sourcesContent": ["/**\n * Validates the fee payer data when submitting a transaction to ensure that the fee payer authenticator is provided if a fee\n * payer address is specified.\n * This helps prevent errors in transaction submission related to fee payer authentication.\n *\n * @param target - The target object where the method is defined.\n * @param propertyKey - The name of the method being decorated.\n * @param descriptor - The property descriptor for the method.\n *\n * @example\n * ```typescript\n * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n *\n * const config = new AptosConfig({ network: Network.TESTNET });\n * const aptos = new Aptos(config);\n *\n * class TransactionHandler {\n *   @ValidateFeePayerDataOnSubmission\n *   async submitTransaction(methodArgs: { transaction: { feePayerAddress: string }, feePayerAuthenticator?: string }) {\n *     // Logic to submit the transaction\n *   }\n * }\n *\n * async function runExample() {\n *   const handler = new TransactionHandler();\n *\n *   // Attempt to submit a transaction without a fee payer authenticator\n *   try {\n *     await handler.submitTransaction({\n *       transaction: { feePayerAddress: \"0x1\" }, // replace with a real fee payer address\n *     });\n *   } catch (error) {\n *     console.error(error.message); // Should log the error message\n *   }\n *\n *   // Submit a transaction with a fee payer authenticator\n *   await handler.submitTransaction({\n *     transaction: { feePayerAddress: \"0x1\" }, // replace with a real fee payer address\n *     feePayerAuthenticator: \"authenticatorValue\", // replace with a real authenticator\n *   });\n *\n *   console.log(\"Transaction submitted successfully.\");\n * }\n * runExample().catch(console.error);\n * ```\n * @group Implementation\n */\nexport function ValidateFeePayerDataOnSubmission(target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {\n  const originalMethod = descriptor.value;\n  /* eslint-disable-next-line func-names, no-param-reassign */\n  descriptor.value = async function (...args: any[]) {\n    const [methodArgs] = args;\n\n    if (methodArgs.transaction.feePayerAddress && !methodArgs.feePayerAuthenticator) {\n      throw new Error(\"You are submitting a Fee Payer transaction but missing the feePayerAuthenticator\");\n    }\n\n    return originalMethod.apply(this, args);\n  };\n\n  return descriptor;\n}\n\n/**\n * Validates that the fee payer public key is provided when simulating a Fee Payer transaction.\n * This ensures that all necessary data is present for the simulation to proceed correctly.\n *\n * @param target - The target object where the method is defined.\n * @param propertyKey - The name of the method being decorated.\n * @param descriptor - The property descriptor for the method.\n *\n * @example\n * ```typescript\n * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n *\n * const config = new AptosConfig({ network: Network.TESTNET });\n * const aptos = new Aptos(config);\n *\n * async function runExample() {\n *   const methodArgs = {\n *     transaction: {\n *       feePayerAddress: \"0x1\", // replace with a real fee payer address\n *     },\n *     feePayerPublicKey: undefined, // missing fee payer public key\n *   };\n *\n *   try {\n *     // This will throw an error due to missing feePayerPublicKey\n *     await aptos.someMethod(methodArgs);\n *   } catch (error) {\n *     console.error(error.message); // Output the error message\n *   }\n * }\n * runExample().catch(console.error);\n * ```\n * @group Implementation\n */\nexport function ValidateFeePayerDataOnSimulation(target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {\n  const originalMethod = descriptor.value;\n  /* eslint-disable-next-line func-names, no-param-reassign */\n  descriptor.value = async function (...args: any[]) {\n    return originalMethod.apply(this, args);\n  };\n\n  return descriptor;\n}\n"], "mappings": "AA+CO,SAASA,EAAiCC,EAAiBC,EAAqBC,EAAgC,CACrH,IAAMC,EAAiBD,EAAW,MAElC,OAAAA,EAAW,MAAQ,kBAAmBE,EAAa,CACjD,GAAM,CAACC,CAAU,EAAID,EAErB,GAAIC,EAAW,YAAY,iBAAmB,CAACA,EAAW,sBACxD,MAAM,IAAI,MAAM,kFAAkF,EAGpG,OAAOF,EAAe,MAAM,KAAMC,CAAI,CACxC,EAEOF,CACT,CAoCO,SAASI,EAAiCN,EAAiBC,EAAqBC,EAAgC,CACrH,IAAMC,EAAiBD,EAAW,MAElC,OAAAA,EAAW,MAAQ,kBAAmBE,EAAa,CACjD,OAAOD,EAAe,MAAM,KAAMC,CAAI,CACxC,EAEOF,CACT", "names": ["ValidateFeePayerDataOnSubmission", "target", "propertyKey", "descriptor", "originalMethod", "args", "methodArgs", "ValidateFeePayerDataOnSimulation"]}