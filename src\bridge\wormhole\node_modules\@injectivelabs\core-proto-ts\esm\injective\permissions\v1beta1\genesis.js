/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { Params } from "./params.js";
import { AddressVoucher, Namespace } from "./permissions.js";
export const protobufPackage = "injective.permissions.v1beta1";
function createBaseGenesisState() {
    return { params: undefined, namespaces: [], vouchers: [] };
}
export const GenesisState = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.params !== undefined) {
            Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        for (const v of message.namespaces) {
            Namespace.encode(v, writer.uint32(18).fork()).ldelim();
        }
        for (const v of message.vouchers) {
            AddressVoucher.encode(v, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGenesisState();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.namespaces.push(Namespace.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.vouchers.push(AddressVoucher.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            params: isSet(object.params) ? Params.fromJSON(object.params) : undefined,
            namespaces: Array.isArray(object?.namespaces) ? object.namespaces.map((e) => Namespace.fromJSON(e)) : [],
            vouchers: Array.isArray(object?.vouchers) ? object.vouchers.map((e) => AddressVoucher.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.params !== undefined && (obj.params = message.params ? Params.toJSON(message.params) : undefined);
        if (message.namespaces) {
            obj.namespaces = message.namespaces.map((e) => e ? Namespace.toJSON(e) : undefined);
        }
        else {
            obj.namespaces = [];
        }
        if (message.vouchers) {
            obj.vouchers = message.vouchers.map((e) => e ? AddressVoucher.toJSON(e) : undefined);
        }
        else {
            obj.vouchers = [];
        }
        return obj;
    },
    create(base) {
        return GenesisState.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? Params.fromPartial(object.params)
            : undefined;
        message.namespaces = object.namespaces?.map((e) => Namespace.fromPartial(e)) || [];
        message.vouchers = object.vouchers?.map((e) => AddressVoucher.fromPartial(e)) || [];
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
