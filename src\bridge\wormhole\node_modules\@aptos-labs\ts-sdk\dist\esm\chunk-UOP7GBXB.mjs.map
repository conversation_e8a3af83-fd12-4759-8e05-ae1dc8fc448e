{"version": 3, "sources": ["../../src/client/post.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { aptosRequest } from \"./core\";\nimport { AptosResponse, AnyNumber, ClientConfig, MimeType } from \"../types\";\nimport { AptosApiType } from \"../utils/const\";\n\n/**\n * Options for making a POST request, including the API client configuration.\n * @group Implementation\n * @category Client\n */\nexport type PostRequestOptions = {\n  /**\n   * The config for the API client\n   * @group Implementation\n   * @category Client\n   */\n  aptosConfig: AptosConfig;\n  /**\n   * The type of API endpoint to call e.g. fullnode, indexer, etc\n   * @group Implementation\n   * @category Client\n   */\n  type: AptosApiType;\n  /**\n   * The name of the API method\n   * @group Implementation\n   * @category Client\n   */\n  originMethod: string;\n  /**\n   * The URL path to the API method\n   * @group Implementation\n   * @category Client\n   */\n  path: string;\n  /**\n   * The content type of the request body\n   * @group Implementation\n   * @category Client\n   */\n  contentType?: MimeType;\n  /**\n   * The accepted content type of the response of the API\n   * @group Implementation\n   * @category Client\n   */\n  acceptType?: MimeType;\n  /**\n   * The query parameters for the request\n   * @group Implementation\n   * @category Client\n   */\n  params?: Record<string, string | AnyNumber | boolean | undefined>;\n  /**\n   * The body of the request, should match the content type of the request\n   * @group Implementation\n   * @category Client\n   */\n  body?: any;\n  /**\n   * Specific client overrides for this request to override aptosConfig\n   * @group Implementation\n   * @category Client\n   */\n  overrides?: ClientConfig;\n};\n\n/**\n * Options for posting a request to Aptos, excluding the type field.\n * @group Implementation\n * @category Client\n */\nexport type PostAptosRequestOptions = Omit<PostRequestOptions, \"type\">;\n\n/**\n * Executes a POST request to the specified URL with the provided options.\n *\n * @param options - The options for the POST request.\n * @param options.type - The type of the request.\n * @param options.originMethod - The original method that initiated the request.\n * @param options.path - The path for the request.\n * @param options.body - The body content to be sent with the request.\n * @param options.acceptType - The type of response expected from the server.\n * @param options.contentType - The content type of the request body.\n * @param options.params - Additional parameters to include in the request.\n * @param options.aptosConfig - Configuration settings for the Aptos request.\n * @param options.overrides - Any overrides for the default request behavior.\n * @returns The response from the POST request.\n * @group Implementation\n * @category Client\n */\nexport async function post<Req extends {}, Res extends {}>(\n  options: PostRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { type, originMethod, path, body, acceptType, contentType, params, aptosConfig, overrides } = options;\n  const url = aptosConfig.getRequestUrl(type);\n\n  return aptosRequest<Req, Res>(\n    {\n      url,\n      method: \"POST\",\n      originMethod,\n      path,\n      body,\n      contentType,\n      acceptType,\n      params,\n      overrides,\n    },\n    aptosConfig,\n    options.type,\n  );\n}\n\n/**\n * Sends a request to the Aptos full node using the specified options.\n * This function allows you to interact with the Aptos blockchain by sending requests to the full node.\n *\n * @param options - The options for the request.\n * @param options.aptosConfig - Configuration settings for the Aptos client.\n * @param options.aptosConfig.clientConfig - Client-specific configuration settings.\n * @param options.aptosConfig.fullnodeConfig - Full node-specific configuration settings.\n * @param options.overrides - Additional overrides for the request.\n * @group Implementation\n * @category Client\n */\nexport async function postAptosFullNode<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n\n  return post<Req, Res>({\n    ...options,\n    type: AptosApiType.FULLNODE,\n    overrides: {\n      ...aptosConfig.clientConfig,\n      ...aptosConfig.fullnodeConfig,\n      ...options.overrides,\n      HEADERS: { ...aptosConfig.clientConfig?.HEADERS, ...aptosConfig.fullnodeConfig?.HEADERS },\n    },\n  });\n}\n\n/**\n * Sends a request to the Aptos indexer with the specified options.\n * This function allows you to interact with the Aptos indexer and customize the request using various configurations.\n *\n * @param options - The options for the request to the Aptos indexer.\n * @param options.aptosConfig - Configuration settings specific to the Aptos client and indexer.\n * @param options.aptosConfig.clientConfig - The client configuration settings.\n * @param options.aptosConfig.indexerConfig - The indexer configuration settings.\n * @param options.overrides - Additional overrides for the request.\n * @param options.overrides.HEADERS - Custom headers to include in the request.\n * @group Implementation\n * @category Client\n */\nexport async function postAptosIndexer<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n\n  return post<Req, Res>({\n    ...options,\n    type: AptosApiType.INDEXER,\n    overrides: {\n      ...aptosConfig.clientConfig,\n      ...aptosConfig.indexerConfig,\n      ...options.overrides,\n      HEADERS: { ...aptosConfig.clientConfig?.HEADERS, ...aptosConfig.indexerConfig?.HEADERS },\n    },\n  });\n}\n\n/**\n * Sends a request to the Aptos faucet to obtain test tokens.\n * This function modifies the provided configuration to ensure that the API_KEY is not included in the request.\n *\n * Note that only devnet has a publicly accessible faucet. For testnet, you must use\n * the minting page at https://aptos.dev/network/faucet.\n *\n * @param options - The options for the request.\n * @param options.aptosConfig - The configuration settings for the Aptos client.\n * @param options.aptosConfig.clientConfig - The client-specific configuration settings.\n * @param options.aptosConfig.clientConfig.HEADERS - Optional headers to include in the request.\n * @param options.aptosConfig.faucetConfig - The configuration settings specific to the faucet.\n * @param options.overrides - Additional overrides for the request configuration.\n * @group Implementation\n * @category Client\n */\nexport async function postAptosFaucet<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n  // Faucet does not support API_KEY\n  // Create a new object with the desired modification\n  const modifiedAptosConfig = {\n    ...aptosConfig,\n    clientConfig: { ...aptosConfig.clientConfig },\n  };\n  // Delete API_KEY config\n  delete modifiedAptosConfig?.clientConfig?.API_KEY;\n\n  return post<Req, Res>({\n    ...options,\n    type: AptosApiType.FAUCET,\n    overrides: {\n      ...modifiedAptosConfig.clientConfig,\n      ...modifiedAptosConfig.faucetConfig,\n      ...options.overrides,\n      HEADERS: { ...modifiedAptosConfig.clientConfig?.HEADERS, ...modifiedAptosConfig.faucetConfig?.HEADERS },\n    },\n  });\n}\n\n/**\n * Makes a post request to the pepper service.\n *\n * @param options - The options for the request.\n * @param options.url - The URL to which the request is sent.\n * @param options.headers - The headers to include in the request.\n * @param options.body - The body of the request.\n * @returns A promise that resolves to the response from the pepper service.\n * @group Implementation\n * @category Client\n */\nexport async function postAptosPepperService<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  return post<Req, Res>({ ...options, type: AptosApiType.PEPPER });\n}\n\n/**\n * Sends a request to the Aptos proving service with the specified options.\n *\n * @param options - The options for the request to the Aptos proving service.\n * @param options.type - The type of the request, which should be set to AptosApiType.PROVER.\n * @param options.data - The data to be included in the request.\n * @group Implementation\n * @category Client\n */\nexport async function postAptosProvingService<Req extends {}, Res extends {}>(\n  options: PostAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  return post<Req, Res>({ ...options, type: AptosApiType.PROVER });\n}\n"], "mappings": "yCA8FA,eAAsBA,EACpBC,EACkC,CAClC,GAAM,CAAE,KAAAC,EAAM,aAAAC,EAAc,KAAAC,EAAM,KAAAC,EAAM,WAAAC,EAAY,YAAAC,EAAa,OAAAC,EAAQ,YAAAC,EAAa,UAAAC,CAAU,EAAIT,EAC9FU,EAAMF,EAAY,cAAcP,CAAI,EAE1C,OAAOU,EACL,CACE,IAAAD,EACA,OAAQ,OACR,aAAAR,EACA,KAAAC,EACA,KAAAC,EACA,YAAAE,EACA,WAAAD,EACA,OAAAE,EACA,UAAAE,CACF,EACAD,EACAR,EAAQ,IACV,CACF,CAcA,eAAsBY,EACpBZ,EACkC,CAClC,GAAM,CAAE,YAAAQ,CAAY,EAAIR,EAExB,OAAOD,EAAe,CACpB,GAAGC,EACH,gBACA,UAAW,CACT,GAAGQ,EAAY,aACf,GAAGA,EAAY,eACf,GAAGR,EAAQ,UACX,QAAS,CAAE,GAAGQ,EAAY,cAAc,QAAS,GAAGA,EAAY,gBAAgB,OAAQ,CAC1F,CACF,CAAC,CACH,CAeA,eAAsBK,EACpBb,EACkC,CAClC,GAAM,CAAE,YAAAQ,CAAY,EAAIR,EAExB,OAAOD,EAAe,CACpB,GAAGC,EACH,eACA,UAAW,CACT,GAAGQ,EAAY,aACf,GAAGA,EAAY,cACf,GAAGR,EAAQ,UACX,QAAS,CAAE,GAAGQ,EAAY,cAAc,QAAS,GAAGA,EAAY,eAAe,OAAQ,CACzF,CACF,CAAC,CACH,CAkBA,eAAsBM,EACpBd,EACkC,CAClC,GAAM,CAAE,YAAAQ,CAAY,EAAIR,EAGlBe,EAAsB,CAC1B,GAAGP,EACH,aAAc,CAAE,GAAGA,EAAY,YAAa,CAC9C,EAEA,cAAOO,GAAqB,cAAc,QAEnChB,EAAe,CACpB,GAAGC,EACH,cACA,UAAW,CACT,GAAGe,EAAoB,aACvB,GAAGA,EAAoB,aACvB,GAAGf,EAAQ,UACX,QAAS,CAAE,GAAGe,EAAoB,cAAc,QAAS,GAAGA,EAAoB,cAAc,OAAQ,CACxG,CACF,CAAC,CACH,CAaA,eAAsBC,EACpBhB,EACkC,CAClC,OAAOD,EAAe,CAAE,GAAGC,EAAS,aAA0B,CAAC,CACjE,CAWA,eAAsBiB,EACpBjB,EACkC,CAClC,OAAOD,EAAe,CAAE,GAAGC,EAAS,aAA0B,CAAC,CACjE", "names": ["post", "options", "type", "originMethod", "path", "body", "acceptType", "contentType", "params", "aptosConfig", "overrides", "url", "aptosRequest", "postAptosFullNode", "postAptosIndexer", "postAptosFaucet", "modifiedAptosConfig", "postAptosPepperService", "postAptosProvingService"]}