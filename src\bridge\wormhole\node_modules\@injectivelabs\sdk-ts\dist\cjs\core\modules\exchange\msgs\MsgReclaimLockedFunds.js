"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const exceptions_1 = require("@injectivelabs/exceptions");
/**
 * @category Messages
 */
class MsgReclaimLockedFunds extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgReclaimLockedFunds(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds.create();
        message.sender = params.sender;
        message.lockedAccountPubKey = Buffer.from(params.lockedAccountPubKey, 'base64');
        message.signature = params.signature;
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgReclaimLockedFunds',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            sender: proto.sender,
            lockedAccountPubKey: Buffer.from(proto.lockedAccountPubKey),
            signature: Buffer.from(proto.signature),
        };
        return {
            type: 'exchange/MsgReclaimLockedFunds',
            value: message,
        };
    }
    toWeb3Gw() {
        throw new exceptions_1.GeneralException(new Error('EIP712 is not supported for MsgReclaimLockedFunds.'));
    }
    toEip712() {
        throw new exceptions_1.GeneralException(new Error('EIP712 is not supported for MsgReclaimLockedFunds.'));
    }
    toEip712V2() {
        throw new exceptions_1.GeneralException(new Error('EIP712 is not supported for MsgReclaimLockedFunds.'));
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgReclaimLockedFunds',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds.encode(this.toProto()).finish();
    }
}
exports.default = MsgReclaimLockedFunds;
