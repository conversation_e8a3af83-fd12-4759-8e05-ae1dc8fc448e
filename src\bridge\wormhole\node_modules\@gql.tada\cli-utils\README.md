# @gql.tada/cli-utils

**`@gql.tada/cli-utils`** is a support package for `gql.tada` that contains its
CLI utility and CLI main logic.

[Documentation for the CLI and CLI's exported helper functions.](https://gql-tada.0no.co/reference/gql-tada-cli)

> [!NOTE]
> You may use the CLI's exported functions to run part of the CLI's functionality
> programmatically. The exported API by this package is considered stable and public.
