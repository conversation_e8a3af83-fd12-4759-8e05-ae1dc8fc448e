{"version": 3, "file": "web3_base_provider.d.ts", "sourceRoot": "", "sources": ["../../src/web3_base_provider.ts"], "names": [], "mappings": "AAgBA,OAAO,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC;AAE7B,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAE9D,OAAO,EACN,mBAAmB,EACnB,cAAc,EACd,eAAe,EACf,wBAAwB,EACxB,yBAAyB,EACzB,aAAa,EACb,yBAAyB,EACzB,MAAM,kBAAkB,CAAC;AAE1B,OAAO,EACN,WAAW,EACX,aAAa,EACb,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACnB,gBAAgB,EAChB,eAAe,EACf,MAAM,kBAAkB,CAAC;AAE1B,OAAO,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AAEpE,OAAO,EAAE,4BAA4B,EAAE,MAAM,8BAA8B,CAAC;AAE5E,QAAA,MAAM,MAAM,eAAmC,CAAC;AAEhD,MAAM,WAAW,iBAAiB,CACjC,GAAG,SAAS,WAAW,EACvB,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,EACjC,YAAY;IAEZ,OAAO,EAAE,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACrC,eAAe,EAAE,4BAA4B,CAAC,YAAY,CAAC,CAAC;CAC5D;AAGD,MAAM,MAAM,kBAAkB,GAAG,YAAY,GAAG,WAAW,GAAG,cAAc,CAAC;AAE7E,MAAM,MAAM,yBAAyB,CAAC,CAAC,GAAG,aAAa,IAAI,CAC1D,KAAK,EAAE,KAAK,GAAG,gBAAgB,GAAG,SAAS,EAC3C,MAAM,CAAC,EAAE,yBAAyB,GAAG,mBAAmB,CAAC,CAAC,CAAC,KACvD,IAAI,CAAC;AAEV,MAAM,MAAM,gCAAgC,CAAC,CAAC,GAAG,aAAa,IAAI,CACjE,MAAM,CAAC,EAAE,yBAAyB,GAAG,mBAAmB,CAAC,CAAC,CAAC,KACvD,IAAI,CAAC;AAEV,MAAM,MAAM,gCAAgC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,CAAC;AAEpE,MAAM,MAAM,2BAA2B,CAAC,UAAU,GAAG,OAAO,IAAI,CAG/D,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,GAAG,IAAI,GAAG,wBAAwB,CAAC,KAAK,CAAC,EAChE,QAAQ,CAAC,EAAE,yBAAyB,CAAC,UAAU,CAAC,KAC5C,IAAI,CAAC;AAEV,MAAM,WAAW,kBAAkB;IAClC,IAAI,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,OAAO,EAClC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,EAG1B,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,KAAK,IAAI,GAClE,IAAI,CAAC;CACR;AAED,MAAM,WAAW,uBAAuB;IACvC,SAAS,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,OAAO,EACvC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,GACxB,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,qBAAqB;IACrC,OAAO,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,OAAO,EACrC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,EAE1B,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC,KAAK,IAAI,GACjE,IAAI,CAAC;CACR;AAED,MAAM,WAAW,cAAc,CAAC,GAAG,SAAS,WAAW;IACtD,OAAO,CAAC,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,EAAE,YAAY,GAAG,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,EACvF,IAAI,EAAE,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,GAC/B,OAAO,CAAC,yBAAyB,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,CAAC;CAC9D;AAED,MAAM,WAAW,YAAY;IAC5B,OAAO,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,MAAM,eAAe,GAAG,MAAM,CAAC;AAErC,MAAM,MAAM,gBAAgB,GAAG,MAAM,EAAE,CAAC;AAExC,MAAM,MAAM,gBAAgB,GACzB,SAAS,GACT,YAAY,GACZ,SAAS,GACT,cAAc,GACd,iBAAiB,CAAC;AAErB,MAAM,WAAW,eAAe,CAAC,GAAG,SAAS,WAAW,CAAE,SAAQ,cAAc,CAAC,GAAG,CAAC;IACpF,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI,CAAC;IACnE,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;IAC3E,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IACzE,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IAC9E,EAAE,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;IAEnF,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI,CAAC;IAC/E,cAAc,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;IACvF,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IACrF,cAAc,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IAC1F,cAAc,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;CAC/F;AAED,MAAM,WAAW,gBAAgB,CAAC,GAAG,SAAS,WAAW,CAAE,SAAQ,cAAc,CAAC,GAAG,CAAC;IACrF,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI,CAAC;IACnE,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;IAC3E,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IACzE,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IAC9E,EAAE,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;IAEnF,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI,CAAC;IAC/E,cAAc,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;IACvF,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IACrF,cAAc,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,KAAK,IAAI,GAAG,IAAI,CAAC;IAC1F,cAAc,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI,CAAC;IAC/F,UAAU,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,MAAM,iBAAiB,CAAC,GAAG,SAAS,WAAW,GAAG,eAAe,IAAI,IAAI,CAE9E,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,EACjC,mBAAmB,CACnB,GAAG;IACH,OAAO,CACN,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,EACjC,UAAU,GAAG,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,OAAO,EAErD,OAAO,EAAE,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,GAClC,OAAO,CAAC,UAAU,CAAC,CAAC;CACvB,CAAC;AAIF,8BAAsB,gBAAgB,CAAC,GAAG,SAAS,WAAW,GAAG,eAAe,CAC/E,YAAW,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,CAAC,GAAG,CAAC;WAE9D,cAAc,CAAC,QAAQ,EAAE,OAAO;IAY9C,IAAW,CAAC,MAAM,CAAC,YAElB;aAEe,SAAS,IAAI,kBAAkB;aAC/B,qBAAqB,IAAI,OAAO;IAEhD;;;;OAIG;IACI,IAAI,CAAC,UAAU,GAAG,aAAa,EAAE,CAAC,GAAG,OAAO,EAClD,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC,EAE1B,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,eAAe,CAAC,UAAU,CAAC,KAAK,IAAI;IAc9E;;;OAGG;IACU,SAAS,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;IAMjF;;;;;;;;;;;;;;;OAeG;IACI,iBAAiB,IAAI,iBAAiB,CAAC,GAAG,CAAC;aAmBlC,OAAO,CACtB,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,EACjC,UAAU,GAAG,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,OAAO,EACpD,IAAI,EAAE,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;aAIpE,EAAE,CACjB,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;aACS,EAAE,CAAC,CAAC,GAAG,aAAa,EACnC,IAAI,EAAE,SAAS,GAAG,MAAM,EACxB,QAAQ,EACL,gCAAgC,CAAC,eAAe,CAAC,GACjD,gCAAgC,CAAC,CAAC,CAAC,GACpC,IAAI;aAES,EAAE,CAAC,CAAC,GAAG,aAAa,EACnC,IAAI,EAAE,MAAM,GAAG,MAAM,EACrB,QAAQ,EACL,gCAAgC,CAAC,eAAe,CAAC,GACjD,gCAAgC,CAAC,CAAC,CAAC,GACpC,IAAI;aACS,EAAE,CACjB,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;aACS,EAAE,CACjB,IAAI,EAAE,cAAc,EACpB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAChD,IAAI;aACS,EAAE,CACjB,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAClD,IAAI;aACS,cAAc,CAC7B,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;aACS,cAAc,CAAC,CAAC,GAAG,aAAa,EAC/C,IAAI,EAAE,SAAS,GAAG,MAAM,EACxB,QAAQ,EAAE,gCAAgC,CAAC,eAAe,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC,GACxF,IAAI;aACS,cAAc,CAC7B,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;aACS,cAAc,CAC7B,IAAI,EAAE,cAAc,EACpB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAChD,IAAI;aACS,cAAc,CAC7B,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAClD,IAAI;aACS,IAAI,CACnB,IAAI,EAAE,YAAY,EAClB,QAAQ,EAAE,gCAAgC,CAAC,gBAAgB,CAAC,GAC1D,IAAI;aACS,IAAI,CAAC,CAAC,GAAG,aAAa,EACrC,IAAI,EAAE,SAAS,GAAG,MAAM,EACxB,QAAQ,EAAE,gCAAgC,CAAC,eAAe,CAAC,GAAG,yBAAyB,CAAC,CAAC,CAAC,GACxF,IAAI;aACS,IAAI,CACnB,IAAI,EAAE,SAAS,EACf,QAAQ,EAAE,gCAAgC,CAAC,mBAAmB,CAAC,GAC7D,IAAI;aACS,IAAI,CACnB,IAAI,EAAE,cAAc,EACpB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,CAAC,GAChD,IAAI;aACS,IAAI,CACnB,IAAI,EAAE,iBAAiB,EACvB,QAAQ,EAAE,gCAAgC,CAAC,MAAM,EAAE,CAAC,GAClD,IAAI;aACS,kBAAkB,CAAC,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;aACvC,OAAO,IAAI,IAAI;aACf,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;aAC9C,KAAK,IAAI,IAAI;CAC7B;AAED,MAAM,MAAM,kBAAkB,CAAC,GAAG,SAAS,WAAW,GAAG,mBAAmB,IACzE,eAAe,CAAC,GAAG,CAAC,GACpB,gBAAgB,CAAC,GAAG,CAAC,GACrB,qBAAqB,GACrB,kBAAkB,GAClB,uBAAuB,GACvB,cAAc,CAAC,GAAG,CAAC,GACnB,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAEzB,MAAM,MAAM,2BAA2B,GAAG,KAAK,GAAG,SAAS,WAAW,EACrE,GAAG,EAAE,MAAM,EACX,GAAG,CAAC,EAAE,MAAM,KACR,gBAAgB,CAAC,GAAG,CAAC,CAAC"}