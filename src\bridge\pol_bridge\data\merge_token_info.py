#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
from typing import Dict, Any, List, Optional

# 文件路径
TOKENS_JSON_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "tokens.json")
POLYGON_TOKENS_JSON_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "polygon_tokens.json")
OUTPUT_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mapped_tokens.json")

def load_json_file(file_path: str) -> Dict[str, Any]:
    """
    加载JSON文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        加载的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件 {file_path} 失败: {str(e)}")
        return {}

def get_polygon_address_by_eth_address(tokens_data: Dict[str, Dict[str, Dict[str, Any]]], eth_address: str) -> Optional[str]:
    """
    通过以太坊地址查找对应的Polygon地址
    
    Args:
        tokens_data: tokens.json中的数据
        eth_address: 以太坊地址
        
    Returns:
        对应的Polygon地址，如果未找到则返回None
    """
    eth_address = eth_address.lower()
    
    for symbol, chain_data in tokens_data.items():
        if "1" in chain_data and chain_data["1"].get("address", "").lower() == eth_address:
            if "137" in chain_data:
                return chain_data["137"].get("address", "").lower()
    
    return None

def get_eth_address_by_polygon_address(tokens_data: Dict[str, Dict[str, Dict[str, Any]]], polygon_address: str) -> Optional[str]:
    """
    通过Polygon地址查找对应的以太坊地址
    
    Args:
        tokens_data: tokens.json中的数据
        polygon_address: Polygon地址
        
    Returns:
        对应的以太坊地址，如果未找到则返回None
    """
    polygon_address = polygon_address.lower()
    
    for symbol, chain_data in tokens_data.items():
        if "137" in chain_data and chain_data["137"].get("address", "").lower() == polygon_address:
            if "1" in chain_data:
                return chain_data["1"].get("address", "").lower()
    
    return None

def get_token_info_by_polygon_address(tokens_data: Dict[str, Dict[str, Dict[str, Any]]], polygon_address: str) -> Optional[Dict[str, Any]]:
    """
    通过Polygon地址获取完整的代币信息
    
    Args:
        tokens_data: tokens.json中的数据
        polygon_address: Polygon地址
        
    Returns:
        代币的完整信息，如果未找到则返回None
    """
    polygon_address = polygon_address.lower()
    
    for symbol, chain_data in tokens_data.items():
        if "137" in chain_data and chain_data["137"].get("address", "").lower() == polygon_address:
            return {
                "symbol": symbol,
                "polygon_info": chain_data.get("137", {}),
                "ethereum_info": chain_data.get("1", {})
            }
    
    return None

def main():
    """主函数"""
    print("开始合并tokens.json和polygon_tokens.json中的代币信息...")
    
    # 加载数据
    tokens_data = load_json_file(TOKENS_JSON_PATH)
    polygon_tokens_data = load_json_file(POLYGON_TOKENS_JSON_PATH)
    
    if not tokens_data or not polygon_tokens_data:
        print("加载数据失败，请确保文件存在且格式正确")
        return
    
    # 存储匹配的代币信息
    matched_tokens = {}
    
    # 遍历polygon_tokens.json中的代币
    for polygon_address, token_info in polygon_tokens_data.items():
        polygon_address = polygon_address.lower()
        
        # 获取完整的代币信息
        full_token_info = get_token_info_by_polygon_address(tokens_data, polygon_address)
        
        if full_token_info:
            matched_tokens[polygon_address] = full_token_info
    
    # 保存结果
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(matched_tokens, f, indent=2, ensure_ascii=False)
    
    print(f"已找到 {len(matched_tokens)} 个匹配的代币，保存到 {OUTPUT_FILE}")
    
    # 输出一些统计信息
    total_tokens_in_polygon = len(polygon_tokens_data)
    total_tokens_in_main = len(tokens_data)
    match_percentage = (len(matched_tokens) / total_tokens_in_polygon) * 100 if total_tokens_in_polygon > 0 else 0
    
    print(f"统计信息:")
    print(f"  polygon_tokens.json 中的代币总数: {total_tokens_in_polygon}")
    print(f"  tokens.json 中的代币总数: {total_tokens_in_main}")
    print(f"  匹配率: {match_percentage:.2f}%")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}") 