{"version": 3, "file": "api-chunk.js", "sources": ["../../src/ts/index.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/CharacterStream.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/RuleHelpers.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/Rules.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/onlineParser.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/utils/Range.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/utils/validateWithCustomRules.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/getDiagnostics.js", "../../../../node_modules/.pnpm/@0no-co+graphql.web@1.0.4_graphql@16.8.1/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs", "../../src/ast/templates.ts", "../../src/ast/checks.ts", "../../src/ast/resolve.ts", "../../src/ast/index.ts", "../../../../node_modules/.pnpm/lru-cache@10.0.1/node_modules/lru-cache/dist/mjs/index.js", "../../../../node_modules/.pnpm/@sindresorhus+fnv1a@2.0.1/node_modules/@sindresorhus/fnv1a/index.js", "../../src/fieldUsage.ts", "../../src/checkImports.ts", "../../src/persisted.ts", "../../src/diagnostics.ts"], "sourcesContent": ["export var ts;\nexport function init(modules) {\n  ts = modules.typescript;\n}\n", "export default class CharacterStream {\n    constructor(sourceText) {\n        this._start = 0;\n        this._pos = 0;\n        this.getStartOfToken = () => this._start;\n        this.getCurrentPosition = () => this._pos;\n        this.eol = () => this._sourceText.length === this._pos;\n        this.sol = () => this._pos === 0;\n        this.peek = () => {\n            return this._sourceText.charAt(this._pos) || null;\n        };\n        this.next = () => {\n            const char = this._sourceText.charAt(this._pos);\n            this._pos++;\n            return char;\n        };\n        this.eat = (pattern) => {\n            const isMatched = this._testNextCharacter(pattern);\n            if (isMatched) {\n                this._start = this._pos;\n                this._pos++;\n                return this._sourceText.charAt(this._pos - 1);\n            }\n            return undefined;\n        };\n        this.eatWhile = (match) => {\n            let isMatched = this._testNextCharacter(match);\n            let didEat = false;\n            if (isMatched) {\n                didEat = isMatched;\n                this._start = this._pos;\n            }\n            while (isMatched) {\n                this._pos++;\n                isMatched = this._testNextCharacter(match);\n                didEat = true;\n            }\n            return didEat;\n        };\n        this.eatSpace = () => this.eatWhile(/[\\s\\u00a0]/);\n        this.skipToEnd = () => {\n            this._pos = this._sourceText.length;\n        };\n        this.skipTo = (position) => {\n            this._pos = position;\n        };\n        this.match = (pattern, consume = true, caseFold = false) => {\n            let token = null;\n            let match = null;\n            if (typeof pattern === 'string') {\n                const regex = new RegExp(pattern, caseFold ? 'i' : 'g');\n                match = regex.test(this._sourceText.slice(this._pos, this._pos + pattern.length));\n                token = pattern;\n            }\n            else if (pattern instanceof RegExp) {\n                match = this._sourceText.slice(this._pos).match(pattern);\n                token = match === null || match === void 0 ? void 0 : match[0];\n            }\n            if (match != null &&\n                (typeof pattern === 'string' ||\n                    (match instanceof Array &&\n                        this._sourceText.startsWith(match[0], this._pos)))) {\n                if (consume) {\n                    this._start = this._pos;\n                    if (token && token.length) {\n                        this._pos += token.length;\n                    }\n                }\n                return match;\n            }\n            return false;\n        };\n        this.backUp = (num) => {\n            this._pos -= num;\n        };\n        this.column = () => this._pos;\n        this.indentation = () => {\n            const match = this._sourceText.match(/\\s*/);\n            let indent = 0;\n            if (match && match.length !== 0) {\n                const whiteSpaces = match[0];\n                let pos = 0;\n                while (whiteSpaces.length > pos) {\n                    if (whiteSpaces.charCodeAt(pos) === 9) {\n                        indent += 2;\n                    }\n                    else {\n                        indent++;\n                    }\n                    pos++;\n                }\n            }\n            return indent;\n        };\n        this.current = () => this._sourceText.slice(this._start, this._pos);\n        this._sourceText = sourceText;\n    }\n    _testNextCharacter(pattern) {\n        const character = this._sourceText.charAt(this._pos);\n        let isMatched = false;\n        if (typeof pattern === 'string') {\n            isMatched = character === pattern;\n        }\n        else {\n            isMatched =\n                pattern instanceof RegExp\n                    ? pattern.test(character)\n                    : pattern(character);\n        }\n        return isMatched;\n    }\n}\n//# sourceMappingURL=CharacterStream.js.map", "export function opt(ofRule) {\n    return { ofRule };\n}\nexport function list(ofRule, separator) {\n    return { ofRule, isList: true, separator };\n}\nexport function butNot(rule, exclusions) {\n    const ruleMatch = rule.match;\n    rule.match = token => {\n        let check = false;\n        if (ruleMatch) {\n            check = ruleMatch(token);\n        }\n        return (check &&\n            exclusions.every(exclusion => exclusion.match && !exclusion.match(token)));\n    };\n    return rule;\n}\nexport function t(kind, style) {\n    return { style, match: (token) => token.kind === kind };\n}\nexport function p(value, style) {\n    return {\n        style: style || 'punctuation',\n        match: (token) => token.kind === 'Punctuation' && token.value === value,\n    };\n}\n//# sourceMappingURL=RuleHelpers.js.map", "import { opt, list, butNot, t, p } from './RuleHelpers';\nimport { Kind } from 'graphql';\nexport const isIgnored = (ch) => ch === ' ' ||\n    ch === '\\t' ||\n    ch === ',' ||\n    ch === '\\n' ||\n    ch === '\\r' ||\n    ch === '\\uFEFF' ||\n    ch === '\\u00A0';\nexport const LexRules = {\n    Name: /^[_A-Za-z][_0-9A-Za-z]*/,\n    Punctuation: /^(?:!|\\$|\\(|\\)|\\.\\.\\.|:|=|&|@|\\[|]|\\{|\\||\\})/,\n    Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,\n    String: /^(?:\"\"\"(?:\\\\\"\"\"|[^\"]|\"[^\"]|\"\"[^\"])*(?:\"\"\")?|\"(?:[^\"\\\\]|\\\\(?:\"|\\/|\\\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*\"?)/,\n    Comment: /^#.*/,\n};\nexport const ParseRules = {\n    Document: [list('Definition')],\n    Definition(token) {\n        switch (token.value) {\n            case '{':\n                return 'ShortQuery';\n            case 'query':\n                return 'Query';\n            case 'mutation':\n                return 'Mutation';\n            case 'subscription':\n                return 'Subscription';\n            case 'fragment':\n                return Kind.FRAGMENT_DEFINITION;\n            case 'schema':\n                return 'SchemaDef';\n            case 'scalar':\n                return 'ScalarDef';\n            case 'type':\n                return 'ObjectTypeDef';\n            case 'interface':\n                return 'InterfaceDef';\n            case 'union':\n                return 'UnionDef';\n            case 'enum':\n                return 'EnumDef';\n            case 'input':\n                return 'InputDef';\n            case 'extend':\n                return 'ExtendDef';\n            case 'directive':\n                return 'DirectiveDef';\n        }\n    },\n    ShortQuery: ['SelectionSet'],\n    Query: [\n        word('query'),\n        opt(name('def')),\n        opt('VariableDefinitions'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    Mutation: [\n        word('mutation'),\n        opt(name('def')),\n        opt('VariableDefinitions'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    Subscription: [\n        word('subscription'),\n        opt(name('def')),\n        opt('VariableDefinitions'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    VariableDefinitions: [p('('), list('VariableDefinition'), p(')')],\n    VariableDefinition: ['Variable', p(':'), 'Type', opt('DefaultValue')],\n    Variable: [p('$', 'variable'), name('variable')],\n    DefaultValue: [p('='), 'Value'],\n    SelectionSet: [p('{'), list('Selection'), p('}')],\n    Selection(token, stream) {\n        return token.value === '...'\n            ? stream.match(/[\\s\\u00a0,]*(on\\b|@|{)/, false)\n                ? 'InlineFragment'\n                : 'FragmentSpread'\n            : stream.match(/[\\s\\u00a0,]*:/, false)\n                ? 'AliasedField'\n                : 'Field';\n    },\n    AliasedField: [\n        name('property'),\n        p(':'),\n        name('qualifier'),\n        opt('Arguments'),\n        list('Directive'),\n        opt('SelectionSet'),\n    ],\n    Field: [\n        name('property'),\n        opt('Arguments'),\n        list('Directive'),\n        opt('SelectionSet'),\n    ],\n    Arguments: [p('('), list('Argument'), p(')')],\n    Argument: [name('attribute'), p(':'), 'Value'],\n    FragmentSpread: [p('...'), name('def'), list('Directive')],\n    InlineFragment: [\n        p('...'),\n        opt('TypeCondition'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    FragmentDefinition: [\n        word('fragment'),\n        opt(butNot(name('def'), [word('on')])),\n        'TypeCondition',\n        list('Directive'),\n        'SelectionSet',\n    ],\n    TypeCondition: [word('on'), 'NamedType'],\n    Value(token) {\n        switch (token.kind) {\n            case 'Number':\n                return 'NumberValue';\n            case 'String':\n                return 'StringValue';\n            case 'Punctuation':\n                switch (token.value) {\n                    case '[':\n                        return 'ListValue';\n                    case '{':\n                        return 'ObjectValue';\n                    case '$':\n                        return 'Variable';\n                    case '&':\n                        return 'NamedType';\n                }\n                return null;\n            case 'Name':\n                switch (token.value) {\n                    case 'true':\n                    case 'false':\n                        return 'BooleanValue';\n                }\n                if (token.value === 'null') {\n                    return 'NullValue';\n                }\n                return 'EnumValue';\n        }\n    },\n    NumberValue: [t('Number', 'number')],\n    StringValue: [\n        {\n            style: 'string',\n            match: (token) => token.kind === 'String',\n            update(state, token) {\n                if (token.value.startsWith('\"\"\"')) {\n                    state.inBlockstring = !token.value.slice(3).endsWith('\"\"\"');\n                }\n            },\n        },\n    ],\n    BooleanValue: [t('Name', 'builtin')],\n    NullValue: [t('Name', 'keyword')],\n    EnumValue: [name('string-2')],\n    ListValue: [p('['), list('Value'), p(']')],\n    ObjectValue: [p('{'), list('ObjectField'), p('}')],\n    ObjectField: [name('attribute'), p(':'), 'Value'],\n    Type(token) {\n        return token.value === '[' ? 'ListType' : 'NonNullType';\n    },\n    ListType: [p('['), 'Type', p(']'), opt(p('!'))],\n    NonNullType: ['NamedType', opt(p('!'))],\n    NamedType: [type('atom')],\n    Directive: [p('@', 'meta'), name('meta'), opt('Arguments')],\n    DirectiveDef: [\n        word('directive'),\n        p('@', 'meta'),\n        name('meta'),\n        opt('ArgumentsDef'),\n        word('on'),\n        list('DirectiveLocation', p('|')),\n    ],\n    InterfaceDef: [\n        word('interface'),\n        name('atom'),\n        opt('Implements'),\n        list('Directive'),\n        p('{'),\n        list('FieldDef'),\n        p('}'),\n    ],\n    Implements: [word('implements'), list('NamedType', p('&'))],\n    DirectiveLocation: [name('string-2')],\n    SchemaDef: [\n        word('schema'),\n        list('Directive'),\n        p('{'),\n        list('OperationTypeDef'),\n        p('}'),\n    ],\n    OperationTypeDef: [name('keyword'), p(':'), name('atom')],\n    ScalarDef: [word('scalar'), name('atom'), list('Directive')],\n    ObjectTypeDef: [\n        word('type'),\n        name('atom'),\n        opt('Implements'),\n        list('Directive'),\n        p('{'),\n        list('FieldDef'),\n        p('}'),\n    ],\n    FieldDef: [\n        name('property'),\n        opt('ArgumentsDef'),\n        p(':'),\n        'Type',\n        list('Directive'),\n    ],\n    ArgumentsDef: [p('('), list('InputValueDef'), p(')')],\n    InputValueDef: [\n        name('attribute'),\n        p(':'),\n        'Type',\n        opt('DefaultValue'),\n        list('Directive'),\n    ],\n    UnionDef: [\n        word('union'),\n        name('atom'),\n        list('Directive'),\n        p('='),\n        list('UnionMember', p('|')),\n    ],\n    UnionMember: ['NamedType'],\n    EnumDef: [\n        word('enum'),\n        name('atom'),\n        list('Directive'),\n        p('{'),\n        list('EnumValueDef'),\n        p('}'),\n    ],\n    EnumValueDef: [name('string-2'), list('Directive')],\n    InputDef: [\n        word('input'),\n        name('atom'),\n        list('Directive'),\n        p('{'),\n        list('InputValueDef'),\n        p('}'),\n    ],\n    ExtendDef: [word('extend'), 'ExtensionDefinition'],\n    ExtensionDefinition(token) {\n        switch (token.value) {\n            case 'schema':\n                return Kind.SCHEMA_EXTENSION;\n            case 'scalar':\n                return Kind.SCALAR_TYPE_EXTENSION;\n            case 'type':\n                return Kind.OBJECT_TYPE_EXTENSION;\n            case 'interface':\n                return Kind.INTERFACE_TYPE_EXTENSION;\n            case 'union':\n                return Kind.UNION_TYPE_EXTENSION;\n            case 'enum':\n                return Kind.ENUM_TYPE_EXTENSION;\n            case 'input':\n                return Kind.INPUT_OBJECT_TYPE_EXTENSION;\n        }\n    },\n    [Kind.SCHEMA_EXTENSION]: ['SchemaDef'],\n    [Kind.SCALAR_TYPE_EXTENSION]: ['ScalarDef'],\n    [Kind.OBJECT_TYPE_EXTENSION]: ['ObjectTypeDef'],\n    [Kind.INTERFACE_TYPE_EXTENSION]: ['InterfaceDef'],\n    [Kind.UNION_TYPE_EXTENSION]: ['UnionDef'],\n    [Kind.ENUM_TYPE_EXTENSION]: ['EnumDef'],\n    [Kind.INPUT_OBJECT_TYPE_EXTENSION]: ['InputDef'],\n};\nfunction word(value) {\n    return {\n        style: 'keyword',\n        match: (token) => token.kind === 'Name' && token.value === value,\n    };\n}\nfunction name(style) {\n    return {\n        style,\n        match: (token) => token.kind === 'Name',\n        update(state, token) {\n            state.name = token.value;\n        },\n    };\n}\nfunction type(style) {\n    return {\n        style,\n        match: (token) => token.kind === 'Name',\n        update(state, token) {\n            var _a;\n            if ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.prevState) {\n                state.name = token.value;\n                state.prevState.prevState.type = token.value;\n            }\n        },\n    };\n}\n//# sourceMappingURL=Rules.js.map", "import { LexRules, ParseRules, isIgnored } from './Rules';\nimport { Kind } from 'graphql';\nexport default function onlineParser(options = {\n    eatWhitespace: stream => stream.eatWhile(isIgnored),\n    lexRules: LexRules,\n    parseRules: ParseRules,\n    editorConfig: {},\n}) {\n    return {\n        startState() {\n            const initialState = {\n                level: 0,\n                step: 0,\n                name: null,\n                kind: null,\n                type: null,\n                rule: null,\n                needsSeparator: false,\n                prevState: null,\n            };\n            pushRule(options.parseRules, initialState, Kind.DOCUMENT);\n            return initialState;\n        },\n        token(stream, state) {\n            return getToken(stream, state, options);\n        },\n    };\n}\nfunction getToken(stream, state, options) {\n    var _a;\n    if (state.inBlockstring) {\n        if (stream.match(/.*\"\"\"/)) {\n            state.inBlockstring = false;\n            return 'string';\n        }\n        stream.skipToEnd();\n        return 'string';\n    }\n    const { lexRules, parseRules, eatWhitespace, editorConfig } = options;\n    if (state.rule && state.rule.length === 0) {\n        popRule(state);\n    }\n    else if (state.needsAdvance) {\n        state.needsAdvance = false;\n        advanceRule(state, true);\n    }\n    if (stream.sol()) {\n        const tabSize = (editorConfig === null || editorConfig === void 0 ? void 0 : editorConfig.tabSize) || 2;\n        state.indentLevel = Math.floor(stream.indentation() / tabSize);\n    }\n    if (eatWhitespace(stream)) {\n        return 'ws';\n    }\n    const token = lex(lexRules, stream);\n    if (!token) {\n        const matchedSomething = stream.match(/\\S+/);\n        if (!matchedSomething) {\n            stream.match(/\\s/);\n        }\n        pushRule(SpecialParseRules, state, 'Invalid');\n        return 'invalidchar';\n    }\n    if (token.kind === 'Comment') {\n        pushRule(SpecialParseRules, state, 'Comment');\n        return 'comment';\n    }\n    const backupState = assign({}, state);\n    if (token.kind === 'Punctuation') {\n        if (/^[{([]/.test(token.value)) {\n            if (state.indentLevel !== undefined) {\n                state.levels = (state.levels || []).concat(state.indentLevel + 1);\n            }\n        }\n        else if (/^[})\\]]/.test(token.value)) {\n            const levels = (state.levels = (state.levels || []).slice(0, -1));\n            if (state.indentLevel &&\n                levels.length > 0 &&\n                levels.at(-1) < state.indentLevel) {\n                state.indentLevel = levels.at(-1);\n            }\n        }\n    }\n    while (state.rule) {\n        let expected = typeof state.rule === 'function'\n            ? state.step === 0\n                ? state.rule(token, stream)\n                : null\n            : state.rule[state.step];\n        if (state.needsSeparator) {\n            expected = expected === null || expected === void 0 ? void 0 : expected.separator;\n        }\n        if (expected) {\n            if (expected.ofRule) {\n                expected = expected.ofRule;\n            }\n            if (typeof expected === 'string') {\n                pushRule(parseRules, state, expected);\n                continue;\n            }\n            if ((_a = expected.match) === null || _a === void 0 ? void 0 : _a.call(expected, token)) {\n                if (expected.update) {\n                    expected.update(state, token);\n                }\n                if (token.kind === 'Punctuation') {\n                    advanceRule(state, true);\n                }\n                else {\n                    state.needsAdvance = true;\n                }\n                return expected.style;\n            }\n        }\n        unsuccessful(state);\n    }\n    assign(state, backupState);\n    pushRule(SpecialParseRules, state, 'Invalid');\n    return 'invalidchar';\n}\nfunction assign(to, from) {\n    const keys = Object.keys(from);\n    for (let i = 0; i < keys.length; i++) {\n        to[keys[i]] = from[keys[i]];\n    }\n    return to;\n}\nconst SpecialParseRules = {\n    Invalid: [],\n    Comment: [],\n};\nfunction pushRule(rules, state, ruleKind) {\n    if (!rules[ruleKind]) {\n        throw new TypeError('Unknown rule: ' + ruleKind);\n    }\n    state.prevState = Object.assign({}, state);\n    state.kind = ruleKind;\n    state.name = null;\n    state.type = null;\n    state.rule = rules[ruleKind];\n    state.step = 0;\n    state.needsSeparator = false;\n}\nfunction popRule(state) {\n    if (!state.prevState) {\n        return;\n    }\n    state.kind = state.prevState.kind;\n    state.name = state.prevState.name;\n    state.type = state.prevState.type;\n    state.rule = state.prevState.rule;\n    state.step = state.prevState.step;\n    state.needsSeparator = state.prevState.needsSeparator;\n    state.prevState = state.prevState.prevState;\n}\nfunction advanceRule(state, successful) {\n    var _a;\n    if (isList(state) && state.rule) {\n        const step = state.rule[state.step];\n        if (step.separator) {\n            const { separator } = step;\n            state.needsSeparator = !state.needsSeparator;\n            if (!state.needsSeparator && separator.ofRule) {\n                return;\n            }\n        }\n        if (successful) {\n            return;\n        }\n    }\n    state.needsSeparator = false;\n    state.step++;\n    while (state.rule &&\n        !(Array.isArray(state.rule) && state.step < state.rule.length)) {\n        popRule(state);\n        if (state.rule) {\n            if (isList(state)) {\n                if ((_a = state.rule) === null || _a === void 0 ? void 0 : _a[state.step].separator) {\n                    state.needsSeparator = !state.needsSeparator;\n                }\n            }\n            else {\n                state.needsSeparator = false;\n                state.step++;\n            }\n        }\n    }\n}\nfunction isList(state) {\n    const step = Array.isArray(state.rule) &&\n        typeof state.rule[state.step] !== 'string' &&\n        state.rule[state.step];\n    return step && step.isList;\n}\nfunction unsuccessful(state) {\n    while (state.rule &&\n        !(Array.isArray(state.rule) && state.rule[state.step].ofRule)) {\n        popRule(state);\n    }\n    if (state.rule) {\n        advanceRule(state, false);\n    }\n}\nfunction lex(lexRules, stream) {\n    const kinds = Object.keys(lexRules);\n    for (let i = 0; i < kinds.length; i++) {\n        const match = stream.match(lexRules[kinds[i]]);\n        if (match && match instanceof Array) {\n            return { kind: kinds[i], value: match[0] };\n        }\n    }\n}\n//# sourceMappingURL=onlineParser.js.map", "export class Range {\n    constructor(start, end) {\n        this.containsPosition = (position) => {\n            if (this.start.line === position.line) {\n                return this.start.character <= position.character;\n            }\n            if (this.end.line === position.line) {\n                return this.end.character >= position.character;\n            }\n            return this.start.line <= position.line && this.end.line >= position.line;\n        };\n        this.start = start;\n        this.end = end;\n    }\n    setStart(line, character) {\n        this.start = new Position(line, character);\n    }\n    setEnd(line, character) {\n        this.end = new Position(line, character);\n    }\n}\nexport class Position {\n    constructor(line, character) {\n        this.lessThanOrEqualTo = (position) => this.line < position.line ||\n            (this.line === position.line && this.character <= position.character);\n        this.line = line;\n        this.character = character;\n    }\n    setLine(line) {\n        this.line = line;\n    }\n    setCharacter(character) {\n        this.character = character;\n    }\n}\nexport function offsetToPosition(text, loc) {\n    const EOL = '\\n';\n    const buf = text.slice(0, loc);\n    const lines = buf.split(EOL).length - 1;\n    const lastLineIndex = buf.lastIndexOf(EOL);\n    return new Position(lines, loc - lastLineIndex - 1);\n}\nexport function locToRange(text, loc) {\n    const start = offsetToPosition(text, loc.start);\n    const end = offsetToPosition(text, loc.end);\n    return new Range(start, end);\n}\n//# sourceMappingURL=Range.js.map", "import { specifiedRules, validate, NoUnusedFragmentsRule, KnownFragmentNamesRule, Kind, ExecutableDefinitionsRule, LoneSchemaDefinitionRule, UniqueOperationTypesRule, UniqueTypeNamesRule, UniqueEnumValueNamesRule, UniqueFieldDefinitionNamesRule, UniqueDirectiveNamesRule, KnownTypeNamesRule, KnownDirectivesRule, UniqueDirectivesPerLocationRule, PossibleTypeExtensionsRule, UniqueArgumentNamesRule, UniqueInputFieldNamesRule, } from 'graphql';\nconst specifiedSDLRules = [\n    LoneSchemaDefinitionRule,\n    UniqueOperationTypesRule,\n    UniqueTypeNamesRule,\n    UniqueEnumValueNamesRule,\n    UniqueFieldDefinitionNamesRule,\n    UniqueDirectiveNamesRule,\n    KnownTypeNamesRule,\n    KnownDirectivesRule,\n    UniqueDirectivesPerLocationRule,\n    PossibleTypeExtensionsRule,\n    UniqueArgumentNamesRule,\n    UniqueInputFieldNamesRule,\n];\nexport function validateWithCustomRules(schema, ast, customRules, isRelayCompatMode, isSchemaDocument) {\n    const rules = specifiedRules.filter(rule => {\n        if (rule === NoUnusedFragmentsRule || rule === ExecutableDefinitionsRule) {\n            return false;\n        }\n        if (isRelayCompatMode && rule === KnownFragmentNamesRule) {\n            return false;\n        }\n        return true;\n    });\n    if (customRules) {\n        Array.prototype.push.apply(rules, customRules);\n    }\n    if (isSchemaDocument) {\n        Array.prototype.push.apply(rules, specifiedSDLRules);\n    }\n    const errors = validate(schema, ast, rules);\n    return errors.filter(error => {\n        if (error.message.includes('Unknown directive') && error.nodes) {\n            const node = error.nodes[0];\n            if (node && node.kind === Kind.DIRECTIVE) {\n                const name = node.name.value;\n                if (name === 'arguments' || name === 'argumentDefinitions') {\n                    return false;\n                }\n            }\n        }\n        return true;\n    });\n}\n//# sourceMappingURL=validateWithCustomRules.js.map", "import { GraphQLError, print, validate, NoDeprecatedCustomRule, parse, } from 'graphql';\nimport { CharacterStream, onlineParser } from '../parser';\nimport { Range, validateWithCustomRules, Position } from '../utils';\nexport const SEVERITY = {\n    Error: 'Error',\n    Warning: 'Warning',\n    Information: 'Information',\n    Hint: 'Hint',\n};\nexport const DIAGNOSTIC_SEVERITY = {\n    [SEVERITY.Error]: 1,\n    [SEVERITY.Warning]: 2,\n    [SEVERITY.Information]: 3,\n    [SEVERITY.Hint]: 4,\n};\nconst invariant = (condition, message) => {\n    if (!condition) {\n        throw new Error(message);\n    }\n};\nexport function getDiagnostics(query, schema = null, customRules, isRelayCompatMode, externalFragments) {\n    var _a, _b;\n    let ast = null;\n    let fragments = '';\n    if (externalFragments) {\n        fragments =\n            typeof externalFragments === 'string'\n                ? externalFragments\n                : externalFragments.reduce((acc, node) => acc + print(node) + '\\n\\n', '');\n    }\n    const enhancedQuery = fragments ? `${query}\\n\\n${fragments}` : query;\n    try {\n        ast = parse(enhancedQuery);\n    }\n    catch (error) {\n        if (error instanceof GraphQLError) {\n            const range = getRange((_b = (_a = error.locations) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : { line: 0, column: 0 }, enhancedQuery);\n            return [\n                {\n                    severity: DIAGNOSTIC_SEVERITY.Error,\n                    message: error.message,\n                    source: 'GraphQL: Syntax',\n                    range,\n                },\n            ];\n        }\n        throw error;\n    }\n    return validateQuery(ast, schema, customRules, isRelayCompatMode);\n}\nexport function validateQuery(ast, schema = null, customRules, isRelayCompatMode) {\n    if (!schema) {\n        return [];\n    }\n    const validationErrorAnnotations = validateWithCustomRules(schema, ast, customRules, isRelayCompatMode).flatMap(error => annotations(error, DIAGNOSTIC_SEVERITY.Error, 'Validation'));\n    const deprecationWarningAnnotations = validate(schema, ast, [\n        NoDeprecatedCustomRule,\n    ]).flatMap(error => annotations(error, DIAGNOSTIC_SEVERITY.Warning, 'Deprecation'));\n    return validationErrorAnnotations.concat(deprecationWarningAnnotations);\n}\nfunction annotations(error, severity, type) {\n    if (!error.nodes) {\n        return [];\n    }\n    const highlightedNodes = [];\n    for (const [i, node] of error.nodes.entries()) {\n        const highlightNode = node.kind !== 'Variable' && 'name' in node && node.name !== undefined\n            ? node.name\n            : 'variable' in node && node.variable !== undefined\n                ? node.variable\n                : node;\n        if (highlightNode) {\n            invariant(error.locations, 'GraphQL validation error requires locations.');\n            const loc = error.locations[i];\n            const highlightLoc = getLocation(highlightNode);\n            const end = loc.column + (highlightLoc.end - highlightLoc.start);\n            highlightedNodes.push({\n                source: `GraphQL: ${type}`,\n                message: error.message,\n                severity,\n                range: new Range(new Position(loc.line - 1, loc.column - 1), new Position(loc.line - 1, end)),\n            });\n        }\n    }\n    return highlightedNodes;\n}\nexport function getRange(location, queryText) {\n    const parser = onlineParser();\n    const state = parser.startState();\n    const lines = queryText.split('\\n');\n    invariant(lines.length >= location.line, 'Query text must have more lines than where the error happened');\n    let stream = null;\n    for (let i = 0; i < location.line; i++) {\n        stream = new CharacterStream(lines[i]);\n        while (!stream.eol()) {\n            const style = parser.token(stream, state);\n            if (style === 'invalidchar') {\n                break;\n            }\n        }\n    }\n    invariant(stream, 'Expected Parser stream to be available.');\n    const line = location.line - 1;\n    const start = stream.getStartOfToken();\n    const end = stream.getCurrentPosition();\n    return new Range(new Position(line, start), new Position(line, end));\n}\nfunction getLocation(node) {\n    const typeCastedNode = node;\n    const location = typeCastedNode.loc;\n    invariant(location, 'Expected ASTNode to have a location.');\n    return location;\n}\n//# sourceMappingURL=getDiagnostics.js.map", "var e = {\n  NAME: \"Name\",\n  DOCUMENT: \"Document\",\n  OPERATION_DEFINITION: \"OperationDefinition\",\n  VARIABLE_DEFINITION: \"VariableDefinition\",\n  SELECTION_SET: \"SelectionSet\",\n  FIELD: \"Field\",\n  ARGUMENT: \"Argument\",\n  FRAGMENT_SPREAD: \"FragmentSpread\",\n  INLINE_FRAGMENT: \"InlineFragment\",\n  FRAGMENT_DEFINITION: \"FragmentDefinition\",\n  VARIABLE: \"Variable\",\n  INT: \"IntValue\",\n  FLOAT: \"FloatValue\",\n  STRING: \"StringValue\",\n  BOOLEAN: \"BooleanValue\",\n  NULL: \"NullValue\",\n  ENUM: \"EnumValue\",\n  LIST: \"ListValue\",\n  OBJECT: \"ObjectValue\",\n  OBJECT_FIELD: \"ObjectField\",\n  DIRECTIVE: \"Directive\",\n  NAMED_TYPE: \"NamedType\",\n  LIST_TYPE: \"ListType\",\n  NON_NULL_TYPE: \"NonNullType\"\n};\n\nvar r = {\n  QUERY: \"query\",\n  MUTATION: \"mutation\",\n  SUBSCRIPTION: \"subscription\"\n};\n\nclass GraphQLError extends Error {\n  constructor(e, r, i, n, a, t, o) {\n    super(e);\n    this.name = \"GraphQLError\";\n    this.message = e;\n    if (a) {\n      this.path = a;\n    }\n    if (r) {\n      this.nodes = Array.isArray(r) ? r : [ r ];\n    }\n    if (i) {\n      this.source = i;\n    }\n    if (n) {\n      this.positions = n;\n    }\n    if (t) {\n      this.originalError = t;\n    }\n    var l = o;\n    if (!l && t) {\n      var u = t.extensions;\n      if (u && \"object\" == typeof u) {\n        l = u;\n      }\n    }\n    this.extensions = l || {};\n  }\n  toJSON() {\n    return {\n      ...this,\n      message: this.message\n    };\n  }\n  toString() {\n    return this.message;\n  }\n  get [Symbol.toStringTag]() {\n    return \"GraphQLError\";\n  }\n}\n\nvar i;\n\nvar n;\n\nfunction error(e) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${n} in ${e}`);\n}\n\nfunction advance(e) {\n  e.lastIndex = n;\n  if (e.test(i)) {\n    return i.slice(n, n = e.lastIndex);\n  }\n}\n\nvar a = / +(?=[^\\s])/y;\n\nfunction blockString(e) {\n  var r = e.split(\"\\n\");\n  var i = \"\";\n  var n = 0;\n  var t = 0;\n  var o = r.length - 1;\n  for (var l = 0; l < r.length; l++) {\n    a.lastIndex = 0;\n    if (a.test(r[l])) {\n      if (l && (!n || a.lastIndex < n)) {\n        n = a.lastIndex;\n      }\n      t = t || l;\n      o = l;\n    }\n  }\n  for (var u = t; u <= o; u++) {\n    if (u !== t) {\n      i += \"\\n\";\n    }\n    i += r[u].slice(n).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return i;\n}\n\nfunction ignored() {\n  for (var e = 0 | i.charCodeAt(n++); 9 === e || 10 === e || 13 === e || 32 === e || 35 === e || 44 === e || 65279 === e; e = 0 | i.charCodeAt(n++)) {\n    if (35 === e) {\n      while (10 !== (e = i.charCodeAt(n++)) && 13 !== e) {}\n    }\n  }\n  n--;\n}\n\nvar t = /[_A-Za-z]\\w*/y;\n\nfunction name() {\n  var e;\n  if (e = advance(t)) {\n    return {\n      kind: \"Name\",\n      value: e\n    };\n  }\n}\n\nvar o = /(?:null|true|false)/y;\n\nvar l = /\\$[_A-Za-z]\\w*/y;\n\nvar u = /-?\\d+/y;\n\nvar v = /(?:\\.\\d+)?[eE][+-]?\\d+|\\.\\d+/y;\n\nvar d = /\\\\/g;\n\nvar s = /\"\"\"(?:\"\"\"|(?:[\\s\\S]*?[^\\\\])\"\"\")/y;\n\nvar c = /\"(?:\"|[^\\r\\n]*?[^\\\\]\")/y;\n\nfunction value(e) {\n  var r;\n  var a;\n  if (a = advance(o)) {\n    r = \"null\" === a ? {\n      kind: \"NullValue\"\n    } : {\n      kind: \"BooleanValue\",\n      value: \"true\" === a\n    };\n  } else if (!e && (a = advance(l))) {\n    r = {\n      kind: \"Variable\",\n      name: {\n        kind: \"Name\",\n        value: a.slice(1)\n      }\n    };\n  } else if (a = advance(u)) {\n    var f = a;\n    if (a = advance(v)) {\n      r = {\n        kind: \"FloatValue\",\n        value: f + a\n      };\n    } else {\n      r = {\n        kind: \"IntValue\",\n        value: f\n      };\n    }\n  } else if (a = advance(t)) {\n    r = {\n      kind: \"EnumValue\",\n      value: a\n    };\n  } else if (a = advance(s)) {\n    r = {\n      kind: \"StringValue\",\n      value: blockString(a.slice(3, -3)),\n      block: !0\n    };\n  } else if (a = advance(c)) {\n    r = {\n      kind: \"StringValue\",\n      value: d.test(a) ? JSON.parse(a) : a.slice(1, -1),\n      block: !1\n    };\n  } else if (r = function list(e) {\n    var r;\n    if (91 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      var a = [];\n      while (r = value(e)) {\n        a.push(r);\n      }\n      if (93 !== i.charCodeAt(n++)) {\n        throw error(\"ListValue\");\n      }\n      ignored();\n      return {\n        kind: \"ListValue\",\n        values: a\n      };\n    }\n  }(e) || function object(e) {\n    if (123 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      var r = [];\n      var a;\n      while (a = name()) {\n        ignored();\n        if (58 !== i.charCodeAt(n++)) {\n          throw error(\"ObjectField\");\n        }\n        ignored();\n        var t = value(e);\n        if (!t) {\n          throw error(\"ObjectField\");\n        }\n        r.push({\n          kind: \"ObjectField\",\n          name: a,\n          value: t\n        });\n      }\n      if (125 !== i.charCodeAt(n++)) {\n        throw error(\"ObjectValue\");\n      }\n      ignored();\n      return {\n        kind: \"ObjectValue\",\n        fields: r\n      };\n    }\n  }(e)) {\n    return r;\n  }\n  ignored();\n  return r;\n}\n\nfunction arguments_(e) {\n  var r = [];\n  ignored();\n  if (40 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var a;\n    while (a = name()) {\n      ignored();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"Argument\");\n      }\n      ignored();\n      var t = value(e);\n      if (!t) {\n        throw error(\"Argument\");\n      }\n      r.push({\n        kind: \"Argument\",\n        name: a,\n        value: t\n      });\n    }\n    if (!r.length || 41 !== i.charCodeAt(n++)) {\n      throw error(\"Argument\");\n    }\n    ignored();\n  }\n  return r;\n}\n\nfunction directives(e) {\n  var r = [];\n  ignored();\n  while (64 === i.charCodeAt(n)) {\n    n++;\n    var a = name();\n    if (!a) {\n      throw error(\"Directive\");\n    }\n    ignored();\n    r.push({\n      kind: \"Directive\",\n      name: a,\n      arguments: arguments_(e)\n    });\n  }\n  return r;\n}\n\nfunction field() {\n  var e = name();\n  if (e) {\n    ignored();\n    var r;\n    if (58 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      r = e;\n      if (!(e = name())) {\n        throw error(\"Field\");\n      }\n      ignored();\n    }\n    return {\n      kind: \"Field\",\n      alias: r,\n      name: e,\n      arguments: arguments_(!1),\n      directives: directives(!1),\n      selectionSet: selectionSet()\n    };\n  }\n}\n\nfunction type() {\n  var e;\n  ignored();\n  if (91 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var r = type();\n    if (!r || 93 !== i.charCodeAt(n++)) {\n      throw error(\"ListType\");\n    }\n    e = {\n      kind: \"ListType\",\n      type: r\n    };\n  } else if (e = name()) {\n    e = {\n      kind: \"NamedType\",\n      name: e\n    };\n  } else {\n    throw error(\"NamedType\");\n  }\n  ignored();\n  if (33 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    return {\n      kind: \"NonNullType\",\n      type: e\n    };\n  } else {\n    return e;\n  }\n}\n\nvar f = /on/y;\n\nfunction typeCondition() {\n  if (advance(f)) {\n    ignored();\n    var e = name();\n    if (!e) {\n      throw error(\"NamedType\");\n    }\n    ignored();\n    return {\n      kind: \"NamedType\",\n      name: e\n    };\n  }\n}\n\nvar p = /\\.\\.\\./y;\n\nfunction fragmentSpread() {\n  if (advance(p)) {\n    ignored();\n    var e = n;\n    var r;\n    if ((r = name()) && \"on\" !== r.value) {\n      return {\n        kind: \"FragmentSpread\",\n        name: r,\n        directives: directives(!1)\n      };\n    } else {\n      n = e;\n      var i = typeCondition();\n      var a = directives(!1);\n      var t = selectionSet();\n      if (!t) {\n        throw error(\"InlineFragment\");\n      }\n      return {\n        kind: \"InlineFragment\",\n        typeCondition: i,\n        directives: a,\n        selectionSet: t\n      };\n    }\n  }\n}\n\nfunction selectionSet() {\n  var e;\n  ignored();\n  if (123 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var r = [];\n    while (e = fragmentSpread() || field()) {\n      r.push(e);\n    }\n    if (!r.length || 125 !== i.charCodeAt(n++)) {\n      throw error(\"SelectionSet\");\n    }\n    ignored();\n    return {\n      kind: \"SelectionSet\",\n      selections: r\n    };\n  }\n}\n\nvar m = /fragment/y;\n\nfunction fragmentDefinition() {\n  if (advance(m)) {\n    ignored();\n    var e = name();\n    if (!e) {\n      throw error(\"FragmentDefinition\");\n    }\n    ignored();\n    var r = typeCondition();\n    if (!r) {\n      throw error(\"FragmentDefinition\");\n    }\n    var i = directives(!1);\n    var n = selectionSet();\n    if (!n) {\n      throw error(\"FragmentDefinition\");\n    }\n    return {\n      kind: \"FragmentDefinition\",\n      name: e,\n      typeCondition: r,\n      directives: i,\n      selectionSet: n\n    };\n  }\n}\n\nvar g = /(?:query|mutation|subscription)/y;\n\nfunction operationDefinition() {\n  var e;\n  var r;\n  var a = [];\n  var t = [];\n  if (e = advance(g)) {\n    ignored();\n    r = name();\n    a = function variableDefinitions() {\n      var e;\n      var r = [];\n      ignored();\n      if (40 === i.charCodeAt(n)) {\n        n++;\n        ignored();\n        while (e = advance(l)) {\n          ignored();\n          if (58 !== i.charCodeAt(n++)) {\n            throw error(\"VariableDefinition\");\n          }\n          var a = type();\n          var t = void 0;\n          if (61 === i.charCodeAt(n)) {\n            n++;\n            ignored();\n            if (!(t = value(!0))) {\n              throw error(\"VariableDefinition\");\n            }\n          }\n          ignored();\n          r.push({\n            kind: \"VariableDefinition\",\n            variable: {\n              kind: \"Variable\",\n              name: {\n                kind: \"Name\",\n                value: e.slice(1)\n              }\n            },\n            type: a,\n            defaultValue: t,\n            directives: directives(!0)\n          });\n        }\n        if (41 !== i.charCodeAt(n++)) {\n          throw error(\"VariableDefinition\");\n        }\n        ignored();\n      }\n      return r;\n    }();\n    t = directives(!1);\n  }\n  var o = selectionSet();\n  if (o) {\n    return {\n      kind: \"OperationDefinition\",\n      operation: e || \"query\",\n      name: r,\n      variableDefinitions: a,\n      directives: t,\n      selectionSet: o\n    };\n  }\n}\n\nfunction parse(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return function document() {\n    var e;\n    ignored();\n    var r = [];\n    while (e = fragmentDefinition() || operationDefinition()) {\n      r.push(e);\n    }\n    return {\n      kind: \"Document\",\n      definitions: r\n    };\n  }();\n}\n\nfunction parseValue(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  ignored();\n  var a = value(!1);\n  if (!a) {\n    throw error(\"ValueNode\");\n  }\n  return a;\n}\n\nfunction parseType(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return type();\n}\n\nvar h = {};\n\nfunction visit(e, r) {\n  var i = [];\n  var n = [];\n  try {\n    var a = function traverse(e, a, t) {\n      var o = !1;\n      var l = r[e.kind] && r[e.kind].enter || r[e.kind] || r.enter;\n      var u = l && l.call(r, e, a, t, n, i);\n      if (!1 === u) {\n        return e;\n      } else if (null === u) {\n        return null;\n      } else if (u === h) {\n        throw h;\n      } else if (u && \"string\" == typeof u.kind) {\n        o = u !== e;\n        e = u;\n      }\n      if (t) {\n        i.push(t);\n      }\n      var v;\n      var d = {\n        ...e\n      };\n      for (var s in e) {\n        n.push(s);\n        var c = e[s];\n        if (Array.isArray(c)) {\n          var f = [];\n          for (var p = 0; p < c.length; p++) {\n            if (null != c[p] && \"string\" == typeof c[p].kind) {\n              i.push(e);\n              n.push(p);\n              v = traverse(c[p], p, c);\n              n.pop();\n              i.pop();\n              if (null == v) {\n                o = !0;\n              } else {\n                o = o || v !== c[p];\n                f.push(v);\n              }\n            }\n          }\n          c = f;\n        } else if (null != c && \"string\" == typeof c.kind) {\n          if (void 0 !== (v = traverse(c, s, e))) {\n            o = o || c !== v;\n            c = v;\n          }\n        }\n        n.pop();\n        if (o) {\n          d[s] = c;\n        }\n      }\n      if (t) {\n        i.pop();\n      }\n      var m = r[e.kind] && r[e.kind].leave || r.leave;\n      var g = m && m.call(r, e, a, t, n, i);\n      if (g === h) {\n        throw h;\n      } else if (void 0 !== g) {\n        return g;\n      } else if (void 0 !== u) {\n        return o ? d : u;\n      } else {\n        return o ? d : e;\n      }\n    }(e);\n    return void 0 !== a && !1 !== a ? a : e;\n  } catch (r) {\n    if (r !== h) {\n      throw r;\n    }\n    return e;\n  }\n}\n\nfunction printString(e) {\n  return JSON.stringify(e);\n}\n\nfunction printBlockString(e) {\n  return '\"\"\"\\n' + e.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nvar hasItems = e => !(!e || !e.length);\n\nvar y = {\n  OperationDefinition(e) {\n    if (\"query\" === e.operation && !e.name && !hasItems(e.variableDefinitions) && !hasItems(e.directives)) {\n      return y.SelectionSet(e.selectionSet);\n    }\n    var r = e.operation;\n    if (e.name) {\n      r += \" \" + e.name.value;\n    }\n    if (hasItems(e.variableDefinitions)) {\n      if (!e.name) {\n        r += \" \";\n      }\n      r += \"(\" + e.variableDefinitions.map(y.VariableDefinition).join(\", \") + \")\";\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r + \" \" + y.SelectionSet(e.selectionSet);\n  },\n  VariableDefinition(e) {\n    var r = y.Variable(e.variable) + \": \" + print(e.type);\n    if (e.defaultValue) {\n      r += \" = \" + print(e.defaultValue);\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r;\n  },\n  Field(e) {\n    var r = (e.alias ? e.alias.value + \": \" : \"\") + e.name.value;\n    if (hasItems(e.arguments)) {\n      var i = e.arguments.map(y.Argument);\n      var n = r + \"(\" + i.join(\", \") + \")\";\n      r = n.length > 80 ? r + \"(\\n  \" + i.join(\"\\n\").replace(/\\n/g, \"\\n  \") + \"\\n)\" : n;\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return e.selectionSet ? r + \" \" + y.SelectionSet(e.selectionSet) : r;\n  },\n  StringValue: e => e.block ? printBlockString(e.value) : printString(e.value),\n  BooleanValue: e => \"\" + e.value,\n  NullValue: e => \"null\",\n  IntValue: e => e.value,\n  FloatValue: e => e.value,\n  EnumValue: e => e.value,\n  Name: e => e.value,\n  Variable: e => \"$\" + e.name.value,\n  ListValue: e => \"[\" + e.values.map(print).join(\", \") + \"]\",\n  ObjectValue: e => \"{\" + e.fields.map(y.ObjectField).join(\", \") + \"}\",\n  ObjectField: e => e.name.value + \": \" + print(e.value),\n  Document: e => hasItems(e.definitions) ? e.definitions.map(print).join(\"\\n\\n\") : \"\",\n  SelectionSet: e => \"{\\n  \" + e.selections.map(print).join(\"\\n\").replace(/\\n/g, \"\\n  \") + \"\\n}\",\n  Argument: e => e.name.value + \": \" + print(e.value),\n  FragmentSpread(e) {\n    var r = \"...\" + e.name.value;\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r;\n  },\n  InlineFragment(e) {\n    var r = \"...\";\n    if (e.typeCondition) {\n      r += \" on \" + e.typeCondition.name.value;\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r + \" \" + print(e.selectionSet);\n  },\n  FragmentDefinition(e) {\n    var r = \"fragment \" + e.name.value;\n    r += \" on \" + e.typeCondition.name.value;\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r + \" \" + print(e.selectionSet);\n  },\n  Directive(e) {\n    var r = \"@\" + e.name.value;\n    if (hasItems(e.arguments)) {\n      r += \"(\" + e.arguments.map(y.Argument).join(\", \") + \")\";\n    }\n    return r;\n  },\n  NamedType: e => e.name.value,\n  ListType: e => \"[\" + print(e.type) + \"]\",\n  NonNullType: e => print(e.type) + \"!\"\n};\n\nfunction print(e) {\n  return y[e.kind] ? y[e.kind](e) : \"\";\n}\n\nfunction valueFromASTUntyped(e, r) {\n  switch (e.kind) {\n   case \"NullValue\":\n    return null;\n\n   case \"IntValue\":\n    return parseInt(e.value, 10);\n\n   case \"FloatValue\":\n    return parseFloat(e.value);\n\n   case \"StringValue\":\n   case \"EnumValue\":\n   case \"BooleanValue\":\n    return e.value;\n\n   case \"ListValue\":\n    var i = [];\n    for (var n = 0, a = e.values; n < a.length; n += 1) {\n      i.push(valueFromASTUntyped(a[n], r));\n    }\n    return i;\n\n   case \"ObjectValue\":\n    var t = Object.create(null);\n    for (var o = 0, l = e.fields; o < l.length; o += 1) {\n      var u = l[o];\n      t[u.name.value] = valueFromASTUntyped(u.value, r);\n    }\n    return t;\n\n   case \"Variable\":\n    return r && r[e.name.value];\n  }\n}\n\nfunction valueFromTypeNode(e, r, i) {\n  if (\"Variable\" === e.kind) {\n    return i ? valueFromTypeNode(i[e.name.value], r, i) : void 0;\n  } else if (\"NonNullType\" === r.kind) {\n    return \"NullValue\" !== e.kind ? valueFromTypeNode(e, r, i) : void 0;\n  } else if (\"NullValue\" === e.kind) {\n    return null;\n  } else if (\"ListType\" === r.kind) {\n    if (\"ListValue\" === e.kind) {\n      var n = [];\n      for (var a = 0, t = e.values; a < t.length; a += 1) {\n        var o = valueFromTypeNode(t[a], r.type, i);\n        if (void 0 === o) {\n          return;\n        } else {\n          n.push(o);\n        }\n      }\n      return n;\n    }\n  } else if (\"NamedType\" === r.kind) {\n    switch (r.name.value) {\n     case \"Int\":\n     case \"Float\":\n     case \"String\":\n     case \"Bool\":\n      return r.name.value + \"Value\" === e.kind ? valueFromASTUntyped(e, i) : void 0;\n\n     default:\n      return valueFromASTUntyped(e, i);\n    }\n  }\n}\n\nexport { h as BREAK, GraphQLError, e as Kind, r as OperationTypeNode, parse, parseType, parseValue, print, printBlockString, printString, valueFromASTUntyped, valueFromTypeNode, visit };\n//# sourceMappingURL=graphql.web.mjs.map\n", "export const templates = new Set(['gql', 'graphql']);\n", "import { ts } from '../ts';\nimport { templates } from './templates';\n\n/** Checks for an immediately-invoked function expression */\nexport const isIIFE = (node: ts.Node): boolean =>\n  ts.isCallExpression(node) &&\n  node.arguments.length === 0 &&\n  (ts.isFunctionExpression(node.expression) ||\n    ts.isArrowFunction(node.expression)) &&\n  !node.expression.asteriskToken &&\n  !node.expression.modifiers?.length;\n\n/** Checks if node is a known identifier of graphql functions ('graphql' or 'gql') */\nexport const isGraphQLFunctionIdentifier = (\n  node: ts.Node\n): node is ts.Identifier =>\n  ts.isIdentifier(node) && templates.has(node.escapedText as string);\n\n/** If `checker` is passed, checks if node (as identifier/expression) is a gql.tada graphql() function */\nexport const isTadaGraphQLFunction = (\n  node: ts.Node,\n  checker: ts.TypeChecker | undefined\n): node is ts.LeftHandSideExpression => {\n  if (!ts.isLeftHandSideExpression(node)) return false;\n  const type = checker?.getTypeAtLocation(node);\n  // Any function that has both a `scalar` and `persisted` property\n  // is automatically considered a gql.tada graphql() function.\n  return (\n    type != null &&\n    type.getProperty('scalar') != null &&\n    type.getProperty('persisted') != null\n  );\n};\n\n/** If `checker` is passed, checks if node is a gql.tada graphql() call */\nexport const isTadaGraphQLCall = (\n  node: ts.CallExpression,\n  checker: ts.TypeChecker | undefined\n): boolean => {\n  // We expect graphql() to be called with either a string literal\n  // or a string literal and an array of fragments\n  if (!ts.isCallExpression(node)) {\n    return false;\n  } else if (node.arguments.length < 1 || node.arguments.length > 2) {\n    return false;\n  } else if (!ts.isStringLiteralLike(node.arguments[0]!)) {\n    return false;\n  }\n  return checker ? isTadaGraphQLFunction(node.expression, checker) : false;\n};\n\n/** Checks if node is a gql.tada graphql.persisted() call */\nexport const isTadaPersistedCall = (\n  node: ts.Node | undefined,\n  checker: ts.TypeChecker | undefined\n): node is ts.CallExpression => {\n  if (!node) {\n    return false;\n  } else if (!ts.isCallExpression(node)) {\n    return false;\n  } else if (!ts.isPropertyAccessExpression(node.expression)) {\n    return false; // rejecting non property access calls: <expression>.<name>()\n  } else if (\n    !ts.isIdentifier(node.expression.name) ||\n    node.expression.name.escapedText !== 'persisted'\n  ) {\n    return false; // rejecting calls on anyting but 'persisted': <expression>.persisted()\n  } else if (isGraphQLFunctionIdentifier(node.expression.expression)) {\n    return true;\n  } else {\n    return isTadaGraphQLFunction(node.expression.expression, checker);\n  }\n};\n\n// As per check in `isGraphQLCall()` below, enforces arguments length\nexport type GraphQLCallNode = ts.CallExpression & {\n  arguments: [ts.Expression] | [ts.Expression, ts.Expression];\n};\n\n/** Checks if node is a gql.tada or regular graphql() call */\nexport const isGraphQLCall = (\n  node: ts.Node,\n  checker: ts.TypeChecker | undefined\n): node is GraphQLCallNode => {\n  return (\n    ts.isCallExpression(node) &&\n    node.arguments.length >= 1 &&\n    node.arguments.length <= 2 &&\n    (isGraphQLFunctionIdentifier(node.expression) ||\n      isTadaGraphQLCall(node, checker))\n  );\n};\n\n/** Checks if node is a gql/graphql tagged template literal */\nexport const isGraphQLTag = (\n  node: ts.Node\n): node is ts.TaggedTemplateExpression =>\n  ts.isTaggedTemplateExpression(node) && isGraphQLFunctionIdentifier(node.tag);\n\n/** Retrieves the `__name` branded tag from gql.tada `graphql()` or `graphql.persisted()` calls */\nexport const getSchemaName = (\n  node: ts.CallExpression,\n  typeChecker: ts.TypeChecker | undefined,\n  isTadaPersistedCall = false\n): string | null => {\n  if (!typeChecker) return null;\n  const type = typeChecker.getTypeAtLocation(\n    // When calling `graphql.persisted`, we need to access the `graphql` part of\n    // the expression; `node.expression` is the `.persisted` part\n    isTadaPersistedCall ? node.getChildAt(0).getChildAt(0) : node.expression\n  );\n  if (type) {\n    const brandTypeSymbol = type.getProperty('__name');\n    if (brandTypeSymbol) {\n      const brand = typeChecker.getTypeOfSymbol(brandTypeSymbol);\n      if (brand.isUnionOrIntersection()) {\n        const found = brand.types.find(x => x.isStringLiteral());\n        return found && found.isStringLiteral() ? found.value : null;\n      } else if (brand.isStringLiteral()) {\n        return brand.value;\n      }\n    }\n  }\n  return null;\n};\n", "import { print } from '@0no-co/graphql.web';\nimport { ts } from '../ts';\nimport { findNode } from '.';\nimport { getSource } from '../ast';\n\ntype TemplateResult = {\n  combinedText: string;\n  resolvedSpans: Array<{\n    lines: number;\n    identifier: string;\n    original: { start: number; length: number };\n    new: { start: number; length: number };\n  }>;\n};\n\nexport function resolveTemplate(\n  node: ts.TaggedTemplateExpression | ts.StringLiteralLike,\n  filename: string,\n  info: ts.server.PluginCreateInfo\n): TemplateResult {\n  if (ts.isStringLiteralLike(node)) {\n    return { combinedText: node.getText().slice(1, -1), resolvedSpans: [] };\n  }\n\n  let templateText = node.template.getText().slice(1, -1);\n  if (\n    ts.isNoSubstitutionTemplateLiteral(node.template) ||\n    node.template.templateSpans.length === 0\n  ) {\n    return { combinedText: templateText, resolvedSpans: [] };\n  }\n\n  let addedCharacters = 0;\n  const resolvedSpans = node.template.templateSpans\n    .map(span => {\n      if (ts.isIdentifier(span.expression)) {\n        const definitions = info.languageService.getDefinitionAtPosition(\n          filename,\n          span.expression.getStart()\n        );\n\n        const def = definitions && definitions[0];\n        if (!def) return;\n\n        const src = getSource(info, def.fileName);\n        if (!src) return;\n\n        const node = findNode(src, def.textSpan.start);\n        if (!node || !node.parent) return;\n\n        const parent = node.parent;\n        if (ts.isVariableDeclaration(parent)) {\n          const identifierName = span.expression.escapedText;\n          // we reduce by two to account for the \"${\"\n          const originalStart = span.expression.getStart() - 2;\n          const originalRange = {\n            start: originalStart,\n            // we add 1 to account for the \"}\"\n            length: span.expression.end - originalStart + 1,\n          };\n          if (\n            parent.initializer &&\n            ts.isTaggedTemplateExpression(parent.initializer)\n          ) {\n            const text = resolveTemplate(\n              parent.initializer,\n              def.fileName,\n              info\n            );\n            templateText = templateText.replace(\n              '${' + span.expression.escapedText + '}',\n              text.combinedText\n            );\n\n            const alteredSpan = {\n              lines: text.combinedText.split('\\n').length,\n              identifier: identifierName,\n              original: originalRange,\n              new: {\n                start: originalRange.start + addedCharacters,\n                length: text.combinedText.length,\n              },\n            };\n            addedCharacters += text.combinedText.length - originalRange.length;\n            return alteredSpan;\n          } else if (\n            parent.initializer &&\n            ts.isAsExpression(parent.initializer) &&\n            ts.isTaggedTemplateExpression(parent.initializer.expression)\n          ) {\n            const text = resolveTemplate(\n              parent.initializer.expression,\n              def.fileName,\n              info\n            );\n            templateText = templateText.replace(\n              '${' + span.expression.escapedText + '}',\n              text.combinedText\n            );\n            const alteredSpan = {\n              lines: text.combinedText.split('\\n').length,\n              identifier: identifierName,\n              original: originalRange,\n              new: {\n                start: originalRange.start + addedCharacters,\n                length: text.combinedText.length,\n              },\n            };\n            addedCharacters += text.combinedText.length - originalRange.length;\n            return alteredSpan;\n          } else if (\n            parent.initializer &&\n            ts.isAsExpression(parent.initializer) &&\n            ts.isAsExpression(parent.initializer.expression) &&\n            ts.isObjectLiteralExpression(\n              parent.initializer.expression.expression\n            )\n          ) {\n            const astObject = JSON.parse(\n              parent.initializer.expression.expression.getText()\n            );\n            const resolvedTemplate = print(astObject);\n            templateText = templateText.replace(\n              '${' + span.expression.escapedText + '}',\n              resolvedTemplate\n            );\n            const alteredSpan = {\n              lines: resolvedTemplate.split('\\n').length,\n              identifier: identifierName,\n              original: originalRange,\n              new: {\n                start: originalRange.start + addedCharacters,\n                length: resolvedTemplate.length,\n              },\n            };\n            addedCharacters += resolvedTemplate.length - originalRange.length;\n            return alteredSpan;\n          }\n\n          return undefined;\n        }\n      }\n\n      return undefined;\n    })\n    .filter(Boolean) as TemplateResult['resolvedSpans'];\n\n  return { combinedText: templateText, resolvedSpans };\n}\n\nexport const resolveTadaFragmentArray = (\n  node: ts.Expression | undefined\n): undefined | readonly ts.Identifier[] => {\n  if (!node) return undefined;\n  // NOTE: Remove `as T`, users may commonly use `as const` for no reason\n  while (ts.isAsExpression(node)) node = node.expression;\n  if (!ts.isArrayLiteralExpression(node)) return undefined;\n  // NOTE: Let's avoid the allocation of another array here if we can\n  if (node.elements.every(ts.isIdentifier)) return node.elements;\n  const identifiers: ts.Identifier[] = [];\n  for (let element of node.elements) {\n    while (ts.isPropertyAccessExpression(element)) element = element.name;\n    if (ts.isIdentifier(element)) identifiers.push(element);\n  }\n  return identifiers;\n};\n", "import { ts } from '../ts';\nimport { FragmentDefinitionNode, parse } from 'graphql';\nimport * as checks from './checks';\nimport { resolveTadaFragmentArray } from './resolve';\n\nexport { getSchemaName } from './checks';\n\nexport function getSource(info: ts.server.PluginCreateInfo, filename: string) {\n  const program = info.languageService.getProgram();\n  if (!program) return undefined;\n\n  const source = program.getSourceFile(filename);\n  if (!source) return undefined;\n\n  return source;\n}\n\nexport function findNode(\n  sourceFile: ts.SourceFile,\n  position: number\n): ts.Node | undefined {\n  function find(node: ts.Node): ts.Node | undefined {\n    if (position >= node.getStart() && position < node.getEnd()) {\n      return ts.forEachChild(node, find) || node;\n    }\n  }\n  return find(sourceFile);\n}\n\nexport function findAllTaggedTemplateNodes(\n  sourceFile: ts.SourceFile | ts.Node\n): Array<ts.TaggedTemplateExpression | ts.NoSubstitutionTemplateLiteral> {\n  const result: Array<\n    ts.TaggedTemplateExpression | ts.NoSubstitutionTemplateLiteral\n  > = [];\n  function find(node: ts.Node) {\n    if (\n      checks.isGraphQLTag(node) ||\n      (ts.isNoSubstitutionTemplateLiteral(node) &&\n        checks.isGraphQLTag(node.parent))\n    ) {\n      result.push(node);\n      return;\n    } else {\n      ts.forEachChild(node, find);\n    }\n  }\n  find(sourceFile);\n  return result;\n}\n\nfunction resolveIdentifierToGraphQLCall(\n  input: ts.Identifier,\n  info: ts.server.PluginCreateInfo,\n  checker: ts.TypeChecker | undefined\n): checks.GraphQLCallNode | null {\n  let prevElement: ts.Node | undefined;\n  let element: ts.Node | undefined = input;\n  // NOTE: Under certain circumstances, resolving an identifier can loop\n  while (ts.isIdentifier(element) && element !== prevElement) {\n    prevElement = element;\n\n    const definitions = info.languageService.getDefinitionAtPosition(\n      element.getSourceFile().fileName,\n      element.getStart()\n    );\n\n    const fragment = definitions && definitions[0];\n    const externalSource = fragment && getSource(info, fragment.fileName);\n    if (!fragment || !externalSource) return null;\n\n    element = findNode(externalSource, fragment.textSpan.start);\n    if (!element) return null;\n\n    while (ts.isPropertyAccessExpression(element.parent))\n      element = element.parent;\n\n    if (\n      ts.isVariableDeclaration(element.parent) &&\n      element.parent.initializer &&\n      ts.isCallExpression(element.parent.initializer)\n    ) {\n      element = element.parent.initializer;\n    } else if (ts.isPropertyAssignment(element.parent)) {\n      element = element.parent.initializer;\n    } else if (ts.isBinaryExpression(element.parent)) {\n      element = ts.isPropertyAccessExpression(element.parent.right)\n        ? element.parent.right.name\n        : element.parent.right;\n    }\n    // If we find another Identifier, we continue resolving it\n  }\n  // Check whether we've got a `graphql()` or `gql()` call, by the\n  // call expression's identifier\n  return checks.isGraphQLCall(element, checker) ? element : null;\n}\n\nfunction unrollFragment(\n  element: ts.Identifier,\n  info: ts.server.PluginCreateInfo,\n  checker: ts.TypeChecker | undefined\n): Array<FragmentDefinitionNode> {\n  const fragments: FragmentDefinitionNode[] = [];\n  const elements: ts.Identifier[] = [element];\n  const seen = new WeakSet<ts.Identifier>();\n\n  const _unrollElement = (element: ts.Identifier): void => {\n    if (seen.has(element)) return;\n    seen.add(element);\n\n    const node = resolveIdentifierToGraphQLCall(element, info, checker);\n    if (!node) return;\n\n    const fragmentRefs = resolveTadaFragmentArray(node.arguments[1]);\n    if (fragmentRefs) elements.push(...fragmentRefs);\n\n    try {\n      const text = node.arguments[0];\n      const parsed = parse(text.getText().slice(1, -1), { noLocation: true });\n      parsed.definitions.forEach(definition => {\n        if (definition.kind === 'FragmentDefinition') {\n          fragments.push(definition);\n        }\n      });\n    } catch (_error) {\n      // NOTE: Assume graphql.parse errors can be ignored\n    }\n  };\n\n  let nextElement: ts.Identifier | undefined;\n  while ((nextElement = elements.shift()) !== undefined)\n    _unrollElement(nextElement);\n  return fragments;\n}\n\nexport function unrollTadaFragments(\n  fragmentsArray: ts.ArrayLiteralExpression,\n  wip: FragmentDefinitionNode[],\n  info: ts.server.PluginCreateInfo\n): FragmentDefinitionNode[] {\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  fragmentsArray.elements.forEach(element => {\n    if (ts.isIdentifier(element)) {\n      wip.push(...unrollFragment(element, info, typeChecker));\n    } else if (ts.isPropertyAccessExpression(element)) {\n      let el = element;\n      while (ts.isPropertyAccessExpression(el.expression)) el = el.expression;\n      if (ts.isIdentifier(el.name)) {\n        wip.push(...unrollFragment(el.name, info, typeChecker));\n      }\n    }\n  });\n\n  return wip;\n}\n\nexport function findAllCallExpressions(\n  sourceFile: ts.SourceFile,\n  info: ts.server.PluginCreateInfo,\n  shouldSearchFragments: boolean = true\n): {\n  nodes: Array<{\n    node: ts.StringLiteralLike;\n    schema: string | null;\n  }>;\n  fragments: Array<FragmentDefinitionNode>;\n} {\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  const result: Array<{\n    node: ts.StringLiteralLike;\n    schema: string | null;\n  }> = [];\n  let fragments: Array<FragmentDefinitionNode> = [];\n  let hasTriedToFindFragments = shouldSearchFragments ? false : true;\n\n  function find(node: ts.Node): void {\n    if (!ts.isCallExpression(node) || checks.isIIFE(node)) {\n      return ts.forEachChild(node, find);\n    }\n\n    // Check whether we've got a `graphql()` or `gql()` call, by the\n    // call expression's identifier\n    if (!checks.isGraphQLCall(node, typeChecker)) {\n      return ts.forEachChild(node, find);\n    }\n\n    const name = checks.getSchemaName(node, typeChecker);\n    const text = node.arguments[0];\n    const fragmentRefs = resolveTadaFragmentArray(node.arguments[1]);\n\n    if (!hasTriedToFindFragments && !fragmentRefs) {\n      hasTriedToFindFragments = true;\n      fragments.push(...getAllFragments(sourceFile.fileName, node, info));\n    } else if (fragmentRefs) {\n      for (const identifier of fragmentRefs) {\n        fragments.push(...unrollFragment(identifier, info, typeChecker));\n      }\n    }\n\n    if (text && ts.isStringLiteralLike(text)) {\n      result.push({ node: text, schema: name });\n    }\n  }\n  find(sourceFile);\n  return { nodes: result, fragments };\n}\n\nexport function findAllPersistedCallExpressions(\n  sourceFile: ts.SourceFile\n): Array<ts.CallExpression>;\nexport function findAllPersistedCallExpressions(\n  sourceFile: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Array<{ node: ts.CallExpression; schema: string | null }>;\n\nexport function findAllPersistedCallExpressions(\n  sourceFile: ts.SourceFile,\n  info?: ts.server.PluginCreateInfo\n) {\n  const result: Array<\n    ts.CallExpression | { node: ts.CallExpression; schema: string | null }\n  > = [];\n  const typeChecker = info?.languageService.getProgram()?.getTypeChecker();\n  function find(node: ts.Node): void {\n    if (!ts.isCallExpression(node) || checks.isIIFE(node)) {\n      return ts.forEachChild(node, find);\n    }\n\n    if (!checks.isTadaPersistedCall(node, typeChecker)) {\n      return;\n    } else if (info) {\n      const name = checks.getSchemaName(node, typeChecker, true);\n      result.push({ node, schema: name });\n    } else {\n      result.push(node);\n    }\n  }\n  find(sourceFile);\n  return result;\n}\n\nexport function getAllFragments(\n  fileName: string,\n  node: ts.Node,\n  info: ts.server.PluginCreateInfo\n) {\n  let fragments: Array<FragmentDefinitionNode> = [];\n\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  if (!ts.isCallExpression(node)) {\n    return fragments;\n  }\n\n  const fragmentRefs = resolveTadaFragmentArray(node.arguments[1]);\n  if (fragmentRefs) {\n    const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n    for (const identifier of fragmentRefs) {\n      fragments.push(...unrollFragment(identifier, info, typeChecker));\n    }\n    return fragments;\n  } else if (checks.isTadaGraphQLCall(node, typeChecker)) {\n    return fragments;\n  }\n\n  const definitions = info.languageService.getDefinitionAtPosition(\n    fileName,\n    node.expression.getStart()\n  );\n  if (!definitions || !definitions.length) return fragments;\n\n  const def = definitions[0];\n  if (!def) return fragments;\n  const src = getSource(info, def.fileName);\n  if (!src) return fragments;\n\n  ts.forEachChild(src, node => {\n    if (\n      ts.isVariableStatement(node) &&\n      node.declarationList &&\n      node.declarationList.declarations[0] &&\n      node.declarationList.declarations[0].name.getText() === 'documents'\n    ) {\n      const [declaration] = node.declarationList.declarations;\n      if (\n        declaration.initializer &&\n        ts.isObjectLiteralExpression(declaration.initializer)\n      ) {\n        declaration.initializer.properties.forEach(property => {\n          if (\n            ts.isPropertyAssignment(property) &&\n            ts.isStringLiteral(property.name)\n          ) {\n            try {\n              const possibleFragment = JSON.parse(\n                `${property.name.getText().replace(/'/g, '\"')}`\n              );\n\n              if (\n                possibleFragment.includes('fragment ') &&\n                possibleFragment.includes(' on ')\n              ) {\n                const parsed = parse(possibleFragment, {\n                  noLocation: true,\n                });\n                parsed.definitions.forEach(definition => {\n                  if (definition.kind === 'FragmentDefinition') {\n                    fragments.push(definition);\n                  }\n                });\n              }\n            } catch (e: any) {}\n          }\n        });\n      }\n    }\n  });\n\n  return fragments;\n}\n\nexport function findAllImports(\n  sourceFile: ts.SourceFile\n): Array<ts.ImportDeclaration> {\n  return sourceFile.statements.filter(ts.isImportDeclaration);\n}\n\nexport function bubbleUpTemplate(node: ts.Node): ts.Node {\n  while (\n    ts.isNoSubstitutionTemplateLiteral(node) ||\n    ts.isToken(node) ||\n    ts.isTemplateExpression(node) ||\n    ts.isTemplateSpan(node)\n  ) {\n    node = node.parent;\n  }\n\n  return node;\n}\n\nexport function bubbleUpCallExpression(node: ts.Node): ts.Node {\n  while (\n    ts.isStringLiteralLike(node) ||\n    ts.isToken(node) ||\n    ts.isTemplateExpression(node) ||\n    ts.isTemplateSpan(node)\n  ) {\n    node = node.parent;\n  }\n\n  return node;\n}\n", "/**\n * @module LRUCache\n */\nconst perf = typeof performance === 'object' &&\n    performance &&\n    typeof performance.now === 'function'\n    ? performance\n    : Date;\nconst warned = new Set();\n/* c8 ignore start */\nconst PROCESS = (typeof process === 'object' && !!process ? process : {});\n/* c8 ignore start */\nconst emitWarning = (msg, type, code, fn) => {\n    typeof PROCESS.emitWarning === 'function'\n        ? PROCESS.emitWarning(msg, type, code, fn)\n        : console.error(`[${code}] ${type}: ${msg}`);\n};\nlet AC = globalThis.AbortController;\nlet AS = globalThis.AbortSignal;\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n    //@ts-ignore\n    AS = class AbortSignal {\n        onabort;\n        _onabort = [];\n        reason;\n        aborted = false;\n        addEventListener(_, fn) {\n            this._onabort.push(fn);\n        }\n    };\n    //@ts-ignore\n    AC = class AbortController {\n        constructor() {\n            warnACPolyfill();\n        }\n        signal = new AS();\n        abort(reason) {\n            if (this.signal.aborted)\n                return;\n            //@ts-ignore\n            this.signal.reason = reason;\n            //@ts-ignore\n            this.signal.aborted = true;\n            //@ts-ignore\n            for (const fn of this.signal._onabort) {\n                fn(reason);\n            }\n            this.signal.onabort?.(reason);\n        }\n    };\n    let printACPolyfillWarning = PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1';\n    const warnACPolyfill = () => {\n        if (!printACPolyfillWarning)\n            return;\n        printACPolyfillWarning = false;\n        emitWarning('AbortController is not defined. If using lru-cache in ' +\n            'node 14, load an AbortController polyfill from the ' +\n            '`node-abort-controller` package. A minimal polyfill is ' +\n            'provided for use by LRUCache.fetch(), but it should not be ' +\n            'relied upon in other contexts (eg, passing it to other APIs that ' +\n            'use AbortController/AbortSignal might have undesirable effects). ' +\n            'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.', 'NO_ABORT_CONTROLLER', 'ENOTSUP', warnACPolyfill);\n    };\n}\n/* c8 ignore stop */\nconst shouldWarn = (code) => !warned.has(code);\nconst TYPE = Symbol('type');\nconst isPosInt = (n) => n && n === Math.floor(n) && n > 0 && isFinite(n);\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max) => !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n        ? Uint8Array\n        : max <= Math.pow(2, 16)\n            ? Uint16Array\n            : max <= Math.pow(2, 32)\n                ? Uint32Array\n                : max <= Number.MAX_SAFE_INTEGER\n                    ? ZeroArray\n                    : null;\n/* c8 ignore stop */\nclass ZeroArray extends Array {\n    constructor(size) {\n        super(size);\n        this.fill(0);\n    }\n}\nclass Stack {\n    heap;\n    length;\n    // private constructor\n    static #constructing = false;\n    static create(max) {\n        const HeapCls = getUintArray(max);\n        if (!HeapCls)\n            return [];\n        Stack.#constructing = true;\n        const s = new Stack(max, HeapCls);\n        Stack.#constructing = false;\n        return s;\n    }\n    constructor(max, HeapCls) {\n        /* c8 ignore start */\n        if (!Stack.#constructing) {\n            throw new TypeError('instantiate Stack using Stack.create(n)');\n        }\n        /* c8 ignore stop */\n        this.heap = new HeapCls(max);\n        this.length = 0;\n    }\n    push(n) {\n        this.heap[this.length++] = n;\n    }\n    pop() {\n        return this.heap[--this.length];\n    }\n}\n/**\n * Default export, the thing you're using this module to get.\n *\n * All properties from the options object (with the exception of\n * {@link OptionsBase.max} and {@link OptionsBase.maxSize}) are added as\n * normal public members. (`max` and `maxBase` are read-only getters.)\n * Changing any of these will alter the defaults for subsequent method calls,\n * but is otherwise safe.\n */\nexport class LRUCache {\n    // properties coming in from the options of these, only max and maxSize\n    // really *need* to be protected. The rest can be modified, as they just\n    // set defaults for various methods.\n    #max;\n    #maxSize;\n    #dispose;\n    #disposeAfter;\n    #fetchMethod;\n    /**\n     * {@link LRUCache.OptionsBase.ttl}\n     */\n    ttl;\n    /**\n     * {@link LRUCache.OptionsBase.ttlResolution}\n     */\n    ttlResolution;\n    /**\n     * {@link LRUCache.OptionsBase.ttlAutopurge}\n     */\n    ttlAutopurge;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnGet}\n     */\n    updateAgeOnGet;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnHas}\n     */\n    updateAgeOnHas;\n    /**\n     * {@link LRUCache.OptionsBase.allowStale}\n     */\n    allowStale;\n    /**\n     * {@link LRUCache.OptionsBase.noDisposeOnSet}\n     */\n    noDisposeOnSet;\n    /**\n     * {@link LRUCache.OptionsBase.noUpdateTTL}\n     */\n    noUpdateTTL;\n    /**\n     * {@link LRUCache.OptionsBase.maxEntrySize}\n     */\n    maxEntrySize;\n    /**\n     * {@link LRUCache.OptionsBase.sizeCalculation}\n     */\n    sizeCalculation;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n     */\n    noDeleteOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n     */\n    noDeleteOnStaleGet;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n     */\n    allowStaleOnFetchAbort;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n     */\n    allowStaleOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n     */\n    ignoreFetchAbort;\n    // computed properties\n    #size;\n    #calculatedSize;\n    #keyMap;\n    #keyList;\n    #valList;\n    #next;\n    #prev;\n    #head;\n    #tail;\n    #free;\n    #disposed;\n    #sizes;\n    #starts;\n    #ttls;\n    #hasDispose;\n    #hasFetchMethod;\n    #hasDisposeAfter;\n    /**\n     * Do not call this method unless you need to inspect the\n     * inner workings of the cache.  If anything returned by this\n     * object is modified in any way, strange breakage may occur.\n     *\n     * These fields are private for a reason!\n     *\n     * @internal\n     */\n    static unsafeExposeInternals(c) {\n        return {\n            // properties\n            starts: c.#starts,\n            ttls: c.#ttls,\n            sizes: c.#sizes,\n            keyMap: c.#keyMap,\n            keyList: c.#keyList,\n            valList: c.#valList,\n            next: c.#next,\n            prev: c.#prev,\n            get head() {\n                return c.#head;\n            },\n            get tail() {\n                return c.#tail;\n            },\n            free: c.#free,\n            // methods\n            isBackgroundFetch: (p) => c.#isBackgroundFetch(p),\n            backgroundFetch: (k, index, options, context) => c.#backgroundFetch(k, index, options, context),\n            moveToTail: (index) => c.#moveToTail(index),\n            indexes: (options) => c.#indexes(options),\n            rindexes: (options) => c.#rindexes(options),\n            isStale: (index) => c.#isStale(index),\n        };\n    }\n    // Protected read-only members\n    /**\n     * {@link LRUCache.OptionsBase.max} (read-only)\n     */\n    get max() {\n        return this.#max;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.maxSize} (read-only)\n     */\n    get maxSize() {\n        return this.#maxSize;\n    }\n    /**\n     * The total computed size of items in the cache (read-only)\n     */\n    get calculatedSize() {\n        return this.#calculatedSize;\n    }\n    /**\n     * The number of items stored in the cache (read-only)\n     */\n    get size() {\n        return this.#size;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n     */\n    get fetchMethod() {\n        return this.#fetchMethod;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.dispose} (read-only)\n     */\n    get dispose() {\n        return this.#dispose;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n     */\n    get disposeAfter() {\n        return this.#disposeAfter;\n    }\n    constructor(options) {\n        const { max = 0, ttl, ttlResolution = 1, ttlAutopurge, updateAgeOnGet, updateAgeOnHas, allowStale, dispose, disposeAfter, noDisposeOnSet, noUpdateTTL, maxSize = 0, maxEntrySize = 0, sizeCalculation, fetchMethod, noDeleteOnFetchRejection, noDeleteOnStaleGet, allowStaleOnFetchRejection, allowStaleOnFetchAbort, ignoreFetchAbort, } = options;\n        if (max !== 0 && !isPosInt(max)) {\n            throw new TypeError('max option must be a nonnegative integer');\n        }\n        const UintArray = max ? getUintArray(max) : Array;\n        if (!UintArray) {\n            throw new Error('invalid max value: ' + max);\n        }\n        this.#max = max;\n        this.#maxSize = maxSize;\n        this.maxEntrySize = maxEntrySize || this.#maxSize;\n        this.sizeCalculation = sizeCalculation;\n        if (this.sizeCalculation) {\n            if (!this.#maxSize && !this.maxEntrySize) {\n                throw new TypeError('cannot set sizeCalculation without setting maxSize or maxEntrySize');\n            }\n            if (typeof this.sizeCalculation !== 'function') {\n                throw new TypeError('sizeCalculation set to non-function');\n            }\n        }\n        if (fetchMethod !== undefined &&\n            typeof fetchMethod !== 'function') {\n            throw new TypeError('fetchMethod must be a function if specified');\n        }\n        this.#fetchMethod = fetchMethod;\n        this.#hasFetchMethod = !!fetchMethod;\n        this.#keyMap = new Map();\n        this.#keyList = new Array(max).fill(undefined);\n        this.#valList = new Array(max).fill(undefined);\n        this.#next = new UintArray(max);\n        this.#prev = new UintArray(max);\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free = Stack.create(max);\n        this.#size = 0;\n        this.#calculatedSize = 0;\n        if (typeof dispose === 'function') {\n            this.#dispose = dispose;\n        }\n        if (typeof disposeAfter === 'function') {\n            this.#disposeAfter = disposeAfter;\n            this.#disposed = [];\n        }\n        else {\n            this.#disposeAfter = undefined;\n            this.#disposed = undefined;\n        }\n        this.#hasDispose = !!this.#dispose;\n        this.#hasDisposeAfter = !!this.#disposeAfter;\n        this.noDisposeOnSet = !!noDisposeOnSet;\n        this.noUpdateTTL = !!noUpdateTTL;\n        this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection;\n        this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection;\n        this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort;\n        this.ignoreFetchAbort = !!ignoreFetchAbort;\n        // NB: maxEntrySize is set to maxSize if it's set\n        if (this.maxEntrySize !== 0) {\n            if (this.#maxSize !== 0) {\n                if (!isPosInt(this.#maxSize)) {\n                    throw new TypeError('maxSize must be a positive integer if specified');\n                }\n            }\n            if (!isPosInt(this.maxEntrySize)) {\n                throw new TypeError('maxEntrySize must be a positive integer if specified');\n            }\n            this.#initializeSizeTracking();\n        }\n        this.allowStale = !!allowStale;\n        this.noDeleteOnStaleGet = !!noDeleteOnStaleGet;\n        this.updateAgeOnGet = !!updateAgeOnGet;\n        this.updateAgeOnHas = !!updateAgeOnHas;\n        this.ttlResolution =\n            isPosInt(ttlResolution) || ttlResolution === 0\n                ? ttlResolution\n                : 1;\n        this.ttlAutopurge = !!ttlAutopurge;\n        this.ttl = ttl || 0;\n        if (this.ttl) {\n            if (!isPosInt(this.ttl)) {\n                throw new TypeError('ttl must be a positive integer if specified');\n            }\n            this.#initializeTTLTracking();\n        }\n        // do not allow completely unbounded caches\n        if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n            throw new TypeError('At least one of max, maxSize, or ttl is required');\n        }\n        if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n            const code = 'LRU_CACHE_UNBOUNDED';\n            if (shouldWarn(code)) {\n                warned.add(code);\n                const msg = 'TTL caching without ttlAutopurge, max, or maxSize can ' +\n                    'result in unbounded memory consumption.';\n                emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache);\n            }\n        }\n    }\n    /**\n     * Return the remaining TTL time for a given entry key\n     */\n    getRemainingTTL(key) {\n        return this.#keyMap.has(key) ? Infinity : 0;\n    }\n    #initializeTTLTracking() {\n        const ttls = new ZeroArray(this.#max);\n        const starts = new ZeroArray(this.#max);\n        this.#ttls = ttls;\n        this.#starts = starts;\n        this.#setItemTTL = (index, ttl, start = perf.now()) => {\n            starts[index] = ttl !== 0 ? start : 0;\n            ttls[index] = ttl;\n            if (ttl !== 0 && this.ttlAutopurge) {\n                const t = setTimeout(() => {\n                    if (this.#isStale(index)) {\n                        this.delete(this.#keyList[index]);\n                    }\n                }, ttl + 1);\n                // unref() not supported on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n        };\n        this.#updateItemAge = index => {\n            starts[index] = ttls[index] !== 0 ? perf.now() : 0;\n        };\n        this.#statusTTL = (status, index) => {\n            if (ttls[index]) {\n                const ttl = ttls[index];\n                const start = starts[index];\n                status.ttl = ttl;\n                status.start = start;\n                status.now = cachedNow || getNow();\n                const age = status.now - start;\n                status.remainingTTL = ttl - age;\n            }\n        };\n        // debounce calls to perf.now() to 1s so we're not hitting\n        // that costly call repeatedly.\n        let cachedNow = 0;\n        const getNow = () => {\n            const n = perf.now();\n            if (this.ttlResolution > 0) {\n                cachedNow = n;\n                const t = setTimeout(() => (cachedNow = 0), this.ttlResolution);\n                // not available on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n            return n;\n        };\n        this.getRemainingTTL = key => {\n            const index = this.#keyMap.get(key);\n            if (index === undefined) {\n                return 0;\n            }\n            const ttl = ttls[index];\n            const start = starts[index];\n            if (ttl === 0 || start === 0) {\n                return Infinity;\n            }\n            const age = (cachedNow || getNow()) - start;\n            return ttl - age;\n        };\n        this.#isStale = index => {\n            return (ttls[index] !== 0 &&\n                starts[index] !== 0 &&\n                (cachedNow || getNow()) - starts[index] > ttls[index]);\n        };\n    }\n    // conditionally set private methods related to TTL\n    #updateItemAge = () => { };\n    #statusTTL = () => { };\n    #setItemTTL = () => { };\n    /* c8 ignore stop */\n    #isStale = () => false;\n    #initializeSizeTracking() {\n        const sizes = new ZeroArray(this.#max);\n        this.#calculatedSize = 0;\n        this.#sizes = sizes;\n        this.#removeItemSize = index => {\n            this.#calculatedSize -= sizes[index];\n            sizes[index] = 0;\n        };\n        this.#requireSize = (k, v, size, sizeCalculation) => {\n            // provisionally accept background fetches.\n            // actual value size will be checked when they return.\n            if (this.#isBackgroundFetch(v)) {\n                return 0;\n            }\n            if (!isPosInt(size)) {\n                if (sizeCalculation) {\n                    if (typeof sizeCalculation !== 'function') {\n                        throw new TypeError('sizeCalculation must be a function');\n                    }\n                    size = sizeCalculation(v, k);\n                    if (!isPosInt(size)) {\n                        throw new TypeError('sizeCalculation return invalid (expect positive integer)');\n                    }\n                }\n                else {\n                    throw new TypeError('invalid size value (must be positive integer). ' +\n                        'When maxSize or maxEntrySize is used, sizeCalculation ' +\n                        'or size must be set.');\n                }\n            }\n            return size;\n        };\n        this.#addItemSize = (index, size, status) => {\n            sizes[index] = size;\n            if (this.#maxSize) {\n                const maxSize = this.#maxSize - sizes[index];\n                while (this.#calculatedSize > maxSize) {\n                    this.#evict(true);\n                }\n            }\n            this.#calculatedSize += sizes[index];\n            if (status) {\n                status.entrySize = size;\n                status.totalCalculatedSize = this.#calculatedSize;\n            }\n        };\n    }\n    #removeItemSize = _i => { };\n    #addItemSize = (_i, _s, _st) => { };\n    #requireSize = (_k, _v, size, sizeCalculation) => {\n        if (size || sizeCalculation) {\n            throw new TypeError('cannot set size without setting maxSize or maxEntrySize on cache');\n        }\n        return 0;\n    };\n    *#indexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#tail; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#head) {\n                    break;\n                }\n                else {\n                    i = this.#prev[i];\n                }\n            }\n        }\n    }\n    *#rindexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#head; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#tail) {\n                    break;\n                }\n                else {\n                    i = this.#next[i];\n                }\n            }\n        }\n    }\n    #isValidIndex(index) {\n        return (index !== undefined &&\n            this.#keyMap.get(this.#keyList[index]) === index);\n    }\n    /**\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from most recently used to least recently used.\n     */\n    *entries() {\n        for (const i of this.#indexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.entries}\n     *\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from least recently used to most recently used.\n     */\n    *rentries() {\n        for (const i of this.#rindexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the keys in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *keys() {\n        for (const i of this.#indexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.keys}\n     *\n     * Return a generator yielding the keys in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rkeys() {\n        for (const i of this.#rindexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the values in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *values() {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.values}\n     *\n     * Return a generator yielding the values in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rvalues() {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Iterating over the cache itself yields the same results as\n     * {@link LRUCache.entries}\n     */\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    /**\n     * Find a value for which the supplied fn method returns a truthy value,\n     * similar to Array.find().  fn is called as fn(value, key, cache).\n     */\n    find(fn, getOptions = {}) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            if (fn(value, this.#keyList[i], this)) {\n                return this.get(this.#keyList[i], getOptions);\n            }\n        }\n    }\n    /**\n     * Call the supplied function on each item in the cache, in order from\n     * most recently used to least recently used.  fn is called as\n     * fn(value, key, cache).  Does not update age or recenty of use.\n     * Does not iterate over stale values.\n     */\n    forEach(fn, thisp = this) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * The same as {@link LRUCache.forEach} but items are iterated over in\n     * reverse order.  (ie, less recently used items are iterated over first.)\n     */\n    rforEach(fn, thisp = this) {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * Delete any stale entries. Returns true if anything was removed,\n     * false otherwise.\n     */\n    purgeStale() {\n        let deleted = false;\n        for (const i of this.#rindexes({ allowStale: true })) {\n            if (this.#isStale(i)) {\n                this.delete(this.#keyList[i]);\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n     * passed to cache.load()\n     */\n    dump() {\n        const arr = [];\n        for (const i of this.#indexes({ allowStale: true })) {\n            const key = this.#keyList[i];\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined || key === undefined)\n                continue;\n            const entry = { value };\n            if (this.#ttls && this.#starts) {\n                entry.ttl = this.#ttls[i];\n                // always dump the start relative to a portable timestamp\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = perf.now() - this.#starts[i];\n                entry.start = Math.floor(Date.now() - age);\n            }\n            if (this.#sizes) {\n                entry.size = this.#sizes[i];\n            }\n            arr.unshift([key, entry]);\n        }\n        return arr;\n    }\n    /**\n     * Reset the cache and load in the items in entries in the order listed.\n     * Note that the shape of the resulting cache may be different if the\n     * same options are not used in both caches.\n     */\n    load(arr) {\n        this.clear();\n        for (const [key, entry] of arr) {\n            if (entry.start) {\n                // entry.start is a portable timestamp, but we may be using\n                // node's performance.now(), so calculate the offset, so that\n                // we get the intended remaining TTL, no matter how long it's\n                // been on ice.\n                //\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = Date.now() - entry.start;\n                entry.start = perf.now() - age;\n            }\n            this.set(key, entry.value, entry);\n        }\n    }\n    /**\n     * Add a value to the cache.\n     *\n     * Note: if `undefined` is specified as a value, this is an alias for\n     * {@link LRUCache#delete}\n     */\n    set(k, v, setOptions = {}) {\n        if (v === undefined) {\n            this.delete(k);\n            return this;\n        }\n        const { ttl = this.ttl, start, noDisposeOnSet = this.noDisposeOnSet, sizeCalculation = this.sizeCalculation, status, } = setOptions;\n        let { noUpdateTTL = this.noUpdateTTL } = setOptions;\n        const size = this.#requireSize(k, v, setOptions.size || 0, sizeCalculation);\n        // if the item doesn't fit, don't do anything\n        // NB: maxEntrySize set to maxSize by default\n        if (this.maxEntrySize && size > this.maxEntrySize) {\n            if (status) {\n                status.set = 'miss';\n                status.maxEntrySizeExceeded = true;\n            }\n            // have to delete, in case something is there already.\n            this.delete(k);\n            return this;\n        }\n        let index = this.#size === 0 ? undefined : this.#keyMap.get(k);\n        if (index === undefined) {\n            // addition\n            index = (this.#size === 0\n                ? this.#tail\n                : this.#free.length !== 0\n                    ? this.#free.pop()\n                    : this.#size === this.#max\n                        ? this.#evict(false)\n                        : this.#size);\n            this.#keyList[index] = k;\n            this.#valList[index] = v;\n            this.#keyMap.set(k, index);\n            this.#next[this.#tail] = index;\n            this.#prev[index] = this.#tail;\n            this.#tail = index;\n            this.#size++;\n            this.#addItemSize(index, size, status);\n            if (status)\n                status.set = 'add';\n            noUpdateTTL = false;\n        }\n        else {\n            // update\n            this.#moveToTail(index);\n            const oldVal = this.#valList[index];\n            if (v !== oldVal) {\n                if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n                    oldVal.__abortController.abort(new Error('replaced'));\n                    const { __staleWhileFetching: s } = oldVal;\n                    if (s !== undefined && !noDisposeOnSet) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(s, k, 'set');\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([s, k, 'set']);\n                        }\n                    }\n                }\n                else if (!noDisposeOnSet) {\n                    if (this.#hasDispose) {\n                        this.#dispose?.(oldVal, k, 'set');\n                    }\n                    if (this.#hasDisposeAfter) {\n                        this.#disposed?.push([oldVal, k, 'set']);\n                    }\n                }\n                this.#removeItemSize(index);\n                this.#addItemSize(index, size, status);\n                this.#valList[index] = v;\n                if (status) {\n                    status.set = 'replace';\n                    const oldValue = oldVal && this.#isBackgroundFetch(oldVal)\n                        ? oldVal.__staleWhileFetching\n                        : oldVal;\n                    if (oldValue !== undefined)\n                        status.oldValue = oldValue;\n                }\n            }\n            else if (status) {\n                status.set = 'update';\n            }\n        }\n        if (ttl !== 0 && !this.#ttls) {\n            this.#initializeTTLTracking();\n        }\n        if (this.#ttls) {\n            if (!noUpdateTTL) {\n                this.#setItemTTL(index, ttl, start);\n            }\n            if (status)\n                this.#statusTTL(status, index);\n        }\n        if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return this;\n    }\n    /**\n     * Evict the least recently used item, returning its value or\n     * `undefined` if cache is empty.\n     */\n    pop() {\n        try {\n            while (this.#size) {\n                const val = this.#valList[this.#head];\n                this.#evict(true);\n                if (this.#isBackgroundFetch(val)) {\n                    if (val.__staleWhileFetching) {\n                        return val.__staleWhileFetching;\n                    }\n                }\n                else if (val !== undefined) {\n                    return val;\n                }\n            }\n        }\n        finally {\n            if (this.#hasDisposeAfter && this.#disposed) {\n                const dt = this.#disposed;\n                let task;\n                while ((task = dt?.shift())) {\n                    this.#disposeAfter?.(...task);\n                }\n            }\n        }\n    }\n    #evict(free) {\n        const head = this.#head;\n        const k = this.#keyList[head];\n        const v = this.#valList[head];\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('evicted'));\n        }\n        else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n                this.#dispose?.(v, k, 'evict');\n            }\n            if (this.#hasDisposeAfter) {\n                this.#disposed?.push([v, k, 'evict']);\n            }\n        }\n        this.#removeItemSize(head);\n        // if we aren't about to use the index, then null these out\n        if (free) {\n            this.#keyList[head] = undefined;\n            this.#valList[head] = undefined;\n            this.#free.push(head);\n        }\n        if (this.#size === 1) {\n            this.#head = this.#tail = 0;\n            this.#free.length = 0;\n        }\n        else {\n            this.#head = this.#next[head];\n        }\n        this.#keyMap.delete(k);\n        this.#size--;\n        return head;\n    }\n    /**\n     * Check if a key is in the cache, without updating the recency of use.\n     * Will return false if the item is stale, even though it is technically\n     * in the cache.\n     *\n     * Will not update item age unless\n     * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n     */\n    has(k, hasOptions = {}) {\n        const { updateAgeOnHas = this.updateAgeOnHas, status } = hasOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v) &&\n                v.__staleWhileFetching === undefined) {\n                return false;\n            }\n            if (!this.#isStale(index)) {\n                if (updateAgeOnHas) {\n                    this.#updateItemAge(index);\n                }\n                if (status) {\n                    status.has = 'hit';\n                    this.#statusTTL(status, index);\n                }\n                return true;\n            }\n            else if (status) {\n                status.has = 'stale';\n                this.#statusTTL(status, index);\n            }\n        }\n        else if (status) {\n            status.has = 'miss';\n        }\n        return false;\n    }\n    /**\n     * Like {@link LRUCache#get} but doesn't update recency or delete stale\n     * items.\n     *\n     * Returns `undefined` if the item is stale, unless\n     * {@link LRUCache.OptionsBase.allowStale} is set.\n     */\n    peek(k, peekOptions = {}) {\n        const { allowStale = this.allowStale } = peekOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined &&\n            (allowStale || !this.#isStale(index))) {\n            const v = this.#valList[index];\n            // either stale and allowed, or forcing a refresh of non-stale value\n            return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n        }\n    }\n    #backgroundFetch(k, index, options, context) {\n        const v = index === undefined ? undefined : this.#valList[index];\n        if (this.#isBackgroundFetch(v)) {\n            return v;\n        }\n        const ac = new AC();\n        const { signal } = options;\n        // when/if our AC signals, then stop listening to theirs.\n        signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n            signal: ac.signal,\n        });\n        const fetchOpts = {\n            signal: ac.signal,\n            options,\n            context,\n        };\n        const cb = (v, updateCache = false) => {\n            const { aborted } = ac.signal;\n            const ignoreAbort = options.ignoreFetchAbort && v !== undefined;\n            if (options.status) {\n                if (aborted && !updateCache) {\n                    options.status.fetchAborted = true;\n                    options.status.fetchError = ac.signal.reason;\n                    if (ignoreAbort)\n                        options.status.fetchAbortIgnored = true;\n                }\n                else {\n                    options.status.fetchResolved = true;\n                }\n            }\n            if (aborted && !ignoreAbort && !updateCache) {\n                return fetchFail(ac.signal.reason);\n            }\n            // either we didn't abort, and are still here, or we did, and ignored\n            const bf = p;\n            if (this.#valList[index] === p) {\n                if (v === undefined) {\n                    if (bf.__staleWhileFetching) {\n                        this.#valList[index] = bf.__staleWhileFetching;\n                    }\n                    else {\n                        this.delete(k);\n                    }\n                }\n                else {\n                    if (options.status)\n                        options.status.fetchUpdated = true;\n                    this.set(k, v, fetchOpts.options);\n                }\n            }\n            return v;\n        };\n        const eb = (er) => {\n            if (options.status) {\n                options.status.fetchRejected = true;\n                options.status.fetchError = er;\n            }\n            return fetchFail(er);\n        };\n        const fetchFail = (er) => {\n            const { aborted } = ac.signal;\n            const allowStaleAborted = aborted && options.allowStaleOnFetchAbort;\n            const allowStale = allowStaleAborted || options.allowStaleOnFetchRejection;\n            const noDelete = allowStale || options.noDeleteOnFetchRejection;\n            const bf = p;\n            if (this.#valList[index] === p) {\n                // if we allow stale on fetch rejections, then we need to ensure that\n                // the stale value is not removed from the cache when the fetch fails.\n                const del = !noDelete || bf.__staleWhileFetching === undefined;\n                if (del) {\n                    this.delete(k);\n                }\n                else if (!allowStaleAborted) {\n                    // still replace the *promise* with the stale value,\n                    // since we are done with the promise at this point.\n                    // leave it untouched if we're still waiting for an\n                    // aborted background fetch that hasn't yet returned.\n                    this.#valList[index] = bf.__staleWhileFetching;\n                }\n            }\n            if (allowStale) {\n                if (options.status && bf.__staleWhileFetching !== undefined) {\n                    options.status.returnedStale = true;\n                }\n                return bf.__staleWhileFetching;\n            }\n            else if (bf.__returned === bf) {\n                throw er;\n            }\n        };\n        const pcall = (res, rej) => {\n            const fmp = this.#fetchMethod?.(k, v, fetchOpts);\n            if (fmp && fmp instanceof Promise) {\n                fmp.then(v => res(v === undefined ? undefined : v), rej);\n            }\n            // ignored, we go until we finish, regardless.\n            // defer check until we are actually aborting,\n            // so fetchMethod can override.\n            ac.signal.addEventListener('abort', () => {\n                if (!options.ignoreFetchAbort ||\n                    options.allowStaleOnFetchAbort) {\n                    res(undefined);\n                    // when it eventually resolves, update the cache.\n                    if (options.allowStaleOnFetchAbort) {\n                        res = v => cb(v, true);\n                    }\n                }\n            });\n        };\n        if (options.status)\n            options.status.fetchDispatched = true;\n        const p = new Promise(pcall).then(cb, eb);\n        const bf = Object.assign(p, {\n            __abortController: ac,\n            __staleWhileFetching: v,\n            __returned: undefined,\n        });\n        if (index === undefined) {\n            // internal, don't expose status.\n            this.set(k, bf, { ...fetchOpts.options, status: undefined });\n            index = this.#keyMap.get(k);\n        }\n        else {\n            this.#valList[index] = bf;\n        }\n        return bf;\n    }\n    #isBackgroundFetch(p) {\n        if (!this.#hasFetchMethod)\n            return false;\n        const b = p;\n        return (!!b &&\n            b instanceof Promise &&\n            b.hasOwnProperty('__staleWhileFetching') &&\n            b.__abortController instanceof AC);\n    }\n    async fetch(k, fetchOptions = {}) {\n        const { \n        // get options\n        allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, \n        // set options\n        ttl = this.ttl, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, \n        // fetch exclusive options\n        noDeleteOnFetchRejection = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection = this.allowStaleOnFetchRejection, ignoreFetchAbort = this.ignoreFetchAbort, allowStaleOnFetchAbort = this.allowStaleOnFetchAbort, context, forceRefresh = false, status, signal, } = fetchOptions;\n        if (!this.#hasFetchMethod) {\n            if (status)\n                status.fetch = 'get';\n            return this.get(k, {\n                allowStale,\n                updateAgeOnGet,\n                noDeleteOnStaleGet,\n                status,\n            });\n        }\n        const options = {\n            allowStale,\n            updateAgeOnGet,\n            noDeleteOnStaleGet,\n            ttl,\n            noDisposeOnSet,\n            size,\n            sizeCalculation,\n            noUpdateTTL,\n            noDeleteOnFetchRejection,\n            allowStaleOnFetchRejection,\n            allowStaleOnFetchAbort,\n            ignoreFetchAbort,\n            status,\n            signal,\n        };\n        let index = this.#keyMap.get(k);\n        if (index === undefined) {\n            if (status)\n                status.fetch = 'miss';\n            const p = this.#backgroundFetch(k, index, options, context);\n            return (p.__returned = p);\n        }\n        else {\n            // in cache, maybe already fetching\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                const stale = allowStale && v.__staleWhileFetching !== undefined;\n                if (status) {\n                    status.fetch = 'inflight';\n                    if (stale)\n                        status.returnedStale = true;\n                }\n                return stale ? v.__staleWhileFetching : (v.__returned = v);\n            }\n            // if we force a refresh, that means do NOT serve the cached value,\n            // unless we are already in the process of refreshing the cache.\n            const isStale = this.#isStale(index);\n            if (!forceRefresh && !isStale) {\n                if (status)\n                    status.fetch = 'hit';\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                if (status)\n                    this.#statusTTL(status, index);\n                return v;\n            }\n            // ok, it is stale or a forced refresh, and not already fetching.\n            // refresh the cache.\n            const p = this.#backgroundFetch(k, index, options, context);\n            const hasStale = p.__staleWhileFetching !== undefined;\n            const staleVal = hasStale && allowStale;\n            if (status) {\n                status.fetch = isStale ? 'stale' : 'refresh';\n                if (staleVal && isStale)\n                    status.returnedStale = true;\n            }\n            return staleVal ? p.__staleWhileFetching : (p.__returned = p);\n        }\n    }\n    /**\n     * Return a value from the cache. Will update the recency of the cache\n     * entry found.\n     *\n     * If the key is not found, get() will return `undefined`.\n     */\n    get(k, getOptions = {}) {\n        const { allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, status, } = getOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const value = this.#valList[index];\n            const fetching = this.#isBackgroundFetch(value);\n            if (status)\n                this.#statusTTL(status, index);\n            if (this.#isStale(index)) {\n                if (status)\n                    status.get = 'stale';\n                // delete only if not an in-flight background fetch\n                if (!fetching) {\n                    if (!noDeleteOnStaleGet) {\n                        this.delete(k);\n                    }\n                    if (status && allowStale)\n                        status.returnedStale = true;\n                    return allowStale ? value : undefined;\n                }\n                else {\n                    if (status &&\n                        allowStale &&\n                        value.__staleWhileFetching !== undefined) {\n                        status.returnedStale = true;\n                    }\n                    return allowStale ? value.__staleWhileFetching : undefined;\n                }\n            }\n            else {\n                if (status)\n                    status.get = 'hit';\n                // if we're currently fetching it, we don't actually have it yet\n                // it's not stale, which means this isn't a staleWhileRefetching.\n                // If it's not stale, and fetching, AND has a __staleWhileFetching\n                // value, then that means the user fetched with {forceRefresh:true},\n                // so it's safe to return that value.\n                if (fetching) {\n                    return value.__staleWhileFetching;\n                }\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                return value;\n            }\n        }\n        else if (status) {\n            status.get = 'miss';\n        }\n    }\n    #connect(p, n) {\n        this.#prev[n] = p;\n        this.#next[p] = n;\n    }\n    #moveToTail(index) {\n        // if tail already, nothing to do\n        // if head, move head to next[index]\n        // else\n        //   move next[prev[index]] to next[index] (head has no prev)\n        //   move prev[next[index]] to prev[index]\n        // prev[index] = tail\n        // next[tail] = index\n        // tail = index\n        if (index !== this.#tail) {\n            if (index === this.#head) {\n                this.#head = this.#next[index];\n            }\n            else {\n                this.#connect(this.#prev[index], this.#next[index]);\n            }\n            this.#connect(this.#tail, index);\n            this.#tail = index;\n        }\n    }\n    /**\n     * Deletes a key out of the cache.\n     * Returns true if the key was deleted, false otherwise.\n     */\n    delete(k) {\n        let deleted = false;\n        if (this.#size !== 0) {\n            const index = this.#keyMap.get(k);\n            if (index !== undefined) {\n                deleted = true;\n                if (this.#size === 1) {\n                    this.clear();\n                }\n                else {\n                    this.#removeItemSize(index);\n                    const v = this.#valList[index];\n                    if (this.#isBackgroundFetch(v)) {\n                        v.__abortController.abort(new Error('deleted'));\n                    }\n                    else if (this.#hasDispose || this.#hasDisposeAfter) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(v, k, 'delete');\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([v, k, 'delete']);\n                        }\n                    }\n                    this.#keyMap.delete(k);\n                    this.#keyList[index] = undefined;\n                    this.#valList[index] = undefined;\n                    if (index === this.#tail) {\n                        this.#tail = this.#prev[index];\n                    }\n                    else if (index === this.#head) {\n                        this.#head = this.#next[index];\n                    }\n                    else {\n                        this.#next[this.#prev[index]] = this.#next[index];\n                        this.#prev[this.#next[index]] = this.#prev[index];\n                    }\n                    this.#size--;\n                    this.#free.push(index);\n                }\n            }\n        }\n        if (this.#hasDisposeAfter && this.#disposed?.length) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Clear the cache entirely, throwing away all values.\n     */\n    clear() {\n        for (const index of this.#rindexes({ allowStale: true })) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                v.__abortController.abort(new Error('deleted'));\n            }\n            else {\n                const k = this.#keyList[index];\n                if (this.#hasDispose) {\n                    this.#dispose?.(v, k, 'delete');\n                }\n                if (this.#hasDisposeAfter) {\n                    this.#disposed?.push([v, k, 'delete']);\n                }\n            }\n        }\n        this.#keyMap.clear();\n        this.#valList.fill(undefined);\n        this.#keyList.fill(undefined);\n        if (this.#ttls && this.#starts) {\n            this.#ttls.fill(0);\n            this.#starts.fill(0);\n        }\n        if (this.#sizes) {\n            this.#sizes.fill(0);\n        }\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free.length = 0;\n        this.#calculatedSize = 0;\n        this.#size = 0;\n        if (this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n    }\n}\n//# sourceMappingURL=index.js.map", "'use strict';\n\n// FNV_PRIMES and FNV_OFFSETS from\n// http://www.isthe.com/chongo/tech/comp/fnv/index.html#FNV-param\n\nconst FNV_PRIMES = {\n\t32: 16777619n,\n\t64: 1099511628211n,\n\t128: 309485009821345068724781371n,\n\t256: 374144419156711147060143317175368453031918731002211n,\n\t512: 35835915874844867368919076489095108449946327955754392558399825615420669938882575126094039892345713852759n,\n\t1024: 5016456510113118655434598811035278955030765345404790744303017523831112055108147451509157692220295382716162651878526895249385292291816524375083746691371804094271873160484737966720260389217684476157468082573n\n};\n\nconst FNV_OFFSETS = {\n\t32: 2166136261n,\n\t64: 14695981039346656037n,\n\t128: 144066263297769815596495629667062367629n,\n\t256: 100029257958052580907070968620625704837092796014241193945225284501741471925557n,\n\t512: 9659303129496669498009435400716310466090418745672637896108374329434462657994582932197716438449813051892206539805784495328239340083876191928701583869517785n,\n\t1024: 14197795064947621068722070641403218320880622795441933960878474914617582723252296732303717722150864096521202355549365628174669108571814760471015076148029755969804077320157692458563003215304957150157403644460363550505412711285966361610267868082893823963790439336411086884584107735010676915n\n};\n\n// Legacy implementation for 32-bit + number types\nfunction fnv1a(string) {\n\t// Handle Unicode code points > 0x7f\n\tlet hash = Number(FNV_OFFSETS[32]);\n\tlet isUnicoded = false;\n\n\tfor (let i = 0; i < string.length; i++) {\n\t\tlet characterCode = string.charCodeAt(i);\n\n\t\t// Non-ASCII characters trigger the Unicode escape logic\n\t\tif (characterCode > 0x7F && !isUnicoded) {\n\t\t\tstring = unescape(encodeURIComponent(string));\n\t\t\tcharacterCode = string.charCodeAt(i);\n\t\t\tisUnicoded = true;\n\t\t}\n\n\t\thash ^= characterCode;\n\t\thash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n\t}\n\n\treturn hash >>> 0;\n}\n\nfunction bigInt(string, {size = 32} = {}) {\n\tif (!FNV_PRIMES[size]) {\n\t\tthrow new Error('The `size` option must be one of 32, 64, 128, 256, 512, or 1024');\n\t}\n\n\tlet hash = FNV_OFFSETS[size];\n\tconst fnvPrime = FNV_PRIMES[size];\n\n\t// Handle Unicode code points > 0x7f\n\tlet isUnicoded = false;\n\n\tfor (let i = 0; i < string.length; i++) {\n\t\tlet characterCode = string.charCodeAt(i);\n\n\t\t// Non-ASCII characters trigger the Unicode escape logic\n\t\tif (characterCode > 0x7F && !isUnicoded) {\n\t\t\tstring = unescape(encodeURIComponent(string));\n\t\t\tcharacterCode = string.charCodeAt(i);\n\t\t\tisUnicoded = true;\n\t\t}\n\n\t\thash ^= BigInt(characterCode);\n\t\thash = BigInt.asUintN(size, hash * fnvPrime);\n\t}\n\n\treturn hash;\n}\n\nmodule.exports = fnv1a;\nmodule.exports.bigInt = bigInt;\n", "import { ts } from './ts';\nimport { parse, visit } from 'graphql';\n\nimport { findNode } from './ast';\nimport { PropertyAccessExpression } from 'typescript';\n\nexport const UNUSED_FIELD_CODE = 52005;\n\nconst unwrapAbstractType = (type: ts.Type) => {\n  return type.isUnionOrIntersection()\n    ? type.types.find(type => type.flags & ts.TypeFlags.Object) || type\n    : type;\n};\n\nconst getVariableDeclaration = (\n  start: ts.Node\n): ts.VariableDeclaration | undefined => {\n  let node: ts.Node = start;\n  const seen = new Set();\n  while (node.parent && !seen.has(node)) {\n    seen.add(node);\n    if (ts.isBlock(node)) {\n      return; // NOTE: We never want to traverse up into a new function/module block\n    } else if (ts.isVariableDeclaration((node = node.parent))) {\n      return node;\n    }\n  }\n};\n\nconst traverseArrayDestructuring = (\n  node: ts.ArrayBindingPattern,\n  originalWip: Array<string>,\n  allFields: Array<string>,\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Array<string> => {\n  return node.elements.flatMap(element => {\n    if (ts.isOmittedExpression(element)) return [];\n\n    const wip = [...originalWip];\n    return ts.isIdentifier(element.name)\n      ? crawlScope(element.name, wip, allFields, source, info, false)\n      : ts.isObjectBindingPattern(element.name)\n      ? traverseDestructuring(element.name, wip, allFields, source, info)\n      : traverseArrayDestructuring(element.name, wip, allFields, source, info);\n  });\n};\n\nconst traverseDestructuring = (\n  node: ts.ObjectBindingPattern,\n  originalWip: Array<string>,\n  allFields: Array<string>,\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Array<string> => {\n  const results = [];\n  for (const binding of node.elements) {\n    if (ts.isObjectBindingPattern(binding.name)) {\n      const wip = [...originalWip];\n      if (\n        binding.propertyName &&\n        !originalWip.includes(binding.propertyName.getText())\n      ) {\n        const joined = [...wip, binding.propertyName.getText()].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          wip.push(binding.propertyName.getText());\n        }\n      }\n      const traverseResult = traverseDestructuring(\n        binding.name,\n        wip,\n        allFields,\n        source,\n        info\n      );\n\n      results.push(...traverseResult);\n    } else if (ts.isIdentifier(binding.name)) {\n      const wip = [...originalWip];\n      if (\n        binding.propertyName &&\n        !originalWip.includes(binding.propertyName.getText())\n      ) {\n        const joined = [...wip, binding.propertyName.getText()].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          wip.push(binding.propertyName.getText());\n        }\n      } else {\n        const joined = [...wip, binding.name.getText()].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          wip.push(binding.name.getText());\n        }\n      }\n\n      const crawlResult = crawlScope(\n        binding.name,\n        wip,\n        allFields,\n        source,\n        info,\n        false\n      );\n\n      results.push(...crawlResult);\n    }\n  }\n\n  return results;\n};\n\nconst arrayMethods = new Set([\n  'map',\n  'filter',\n  'forEach',\n  'reduce',\n  'every',\n  'some',\n  'find',\n  'flatMap',\n  'sort',\n]);\n\nconst crawlChainedExpressions = (\n  ref: ts.CallExpression,\n  pathParts: string[],\n  allFields: string[],\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): string[] => {\n  const isChained =\n    ts.isPropertyAccessExpression(ref.expression) &&\n    arrayMethods.has(ref.expression.name.text);\n  console.log('[GRAPHQLSP]: ', isChained, ref.getFullText());\n  if (isChained) {\n    const foundRef = ref.expression;\n    const isReduce = foundRef.name.text === 'reduce';\n    let func: ts.Expression | ts.FunctionDeclaration | undefined =\n      ref.arguments[0];\n\n    const res = [];\n    if (ts.isCallExpression(ref.parent.parent)) {\n      const nestedResult = crawlChainedExpressions(\n        ref.parent.parent,\n        pathParts,\n        allFields,\n        source,\n        info\n      );\n      if (nestedResult.length) {\n        res.push(...nestedResult);\n      }\n    }\n\n    if (func && ts.isIdentifier(func)) {\n      // TODO: Scope utilities in checkFieldUsageInFile to deduplicate\n      const checker = info.languageService.getProgram()!.getTypeChecker();\n\n      const declaration = checker.getSymbolAtLocation(func)?.valueDeclaration;\n      if (declaration && ts.isFunctionDeclaration(declaration)) {\n        func = declaration;\n      } else if (\n        declaration &&\n        ts.isVariableDeclaration(declaration) &&\n        declaration.initializer\n      ) {\n        func = declaration.initializer;\n      }\n    }\n\n    if (\n      func &&\n      (ts.isFunctionDeclaration(func) ||\n        ts.isFunctionExpression(func) ||\n        ts.isArrowFunction(func))\n    ) {\n      const param = func.parameters[isReduce ? 1 : 0];\n      if (param) {\n        const scopedResult = crawlScope(\n          param.name,\n          pathParts,\n          allFields,\n          source,\n          info,\n          true\n        );\n\n        if (scopedResult.length) {\n          res.push(...scopedResult);\n        }\n      }\n    }\n\n    return res;\n  }\n\n  return [];\n};\n\nconst crawlScope = (\n  node: ts.BindingName,\n  originalWip: Array<string>,\n  allFields: Array<string>,\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo,\n  inArrayMethod: boolean\n): Array<string> => {\n  if (ts.isObjectBindingPattern(node)) {\n    return traverseDestructuring(node, originalWip, allFields, source, info);\n  } else if (ts.isArrayBindingPattern(node)) {\n    return traverseArrayDestructuring(\n      node,\n      originalWip,\n      allFields,\n      source,\n      info\n    );\n  }\n\n  let results: string[] = [];\n\n  const references = info.languageService.getReferencesAtPosition(\n    source.fileName,\n    node.getStart()\n  );\n\n  if (!references) return results;\n\n  // Go over all the references tied to the result of\n  // accessing our equery and collect them as fully\n  // qualified paths (ideally ending in a leaf-node)\n  results = references.flatMap(ref => {\n    // If we get a reference to a different file we can bail\n    if (ref.fileName !== source.fileName) return [];\n    // We don't want to end back at our document so we narrow\n    // the scope.\n    if (\n      node.getStart() <= ref.textSpan.start &&\n      node.getEnd() >= ref.textSpan.start + ref.textSpan.length\n    )\n      return [];\n\n    let foundRef = findNode(source, ref.textSpan.start);\n    if (!foundRef) return [];\n\n    const pathParts = [...originalWip];\n    // In here we'll start crawling all the accessors of result\n    // and try to determine the total path\n    // - result.data.pokemon.name --> pokemon.name this is the easy route and never accesses\n    //   any of the recursive functions\n    // - const pokemon = result.data.pokemon --> this initiates a new crawl with a renewed scope\n    // - const { pokemon } = result.data --> this initiates a destructuring traversal which will\n    //   either end up in more destructuring traversals or a scope crawl\n    console.log('[GRAPHQLSP]: ', foundRef.getFullText());\n    while (\n      ts.isIdentifier(foundRef) ||\n      ts.isPropertyAccessExpression(foundRef) ||\n      ts.isElementAccessExpression(foundRef) ||\n      ts.isVariableDeclaration(foundRef) ||\n      ts.isBinaryExpression(foundRef) ||\n      ts.isReturnStatement(foundRef) ||\n      ts.isArrowFunction(foundRef)\n    ) {\n      if (\n        !inArrayMethod &&\n        (ts.isReturnStatement(foundRef) || ts.isArrowFunction(foundRef))\n      ) {\n        // When we are returning the ref or we are dealing with an implicit return\n        // we mark all its children as used (bail scenario)\n        const joined = pathParts.join('.');\n        const bailedFields = allFields.filter(x => x.startsWith(joined + '.'));\n        return bailedFields;\n      } else if (ts.isVariableDeclaration(foundRef)) {\n        return crawlScope(\n          foundRef.name,\n          pathParts,\n          allFields,\n          source,\n          info,\n          false\n        );\n      } else if (\n        ts.isIdentifier(foundRef) &&\n        !pathParts.includes(foundRef.text)\n      ) {\n        const joined = [...pathParts, foundRef.text].join('.');\n        if (allFields.find(x => x.startsWith(joined + '.'))) {\n          pathParts.push(foundRef.text);\n        }\n      } else if (\n        ts.isPropertyAccessExpression(foundRef) &&\n        foundRef.name.text === 'at' &&\n        ts.isCallExpression(foundRef.parent)\n      ) {\n        foundRef = foundRef.parent;\n      } else if (\n        ts.isPropertyAccessExpression(foundRef) &&\n        arrayMethods.has(foundRef.name.text) &&\n        ts.isCallExpression(foundRef.parent)\n      ) {\n        const callExpression = foundRef.parent;\n        const res = [];\n        const isSomeOrEvery =\n          foundRef.name.text === 'some' || foundRef.name.text === 'every';\n        console.log('[GRAPHQLSP]: ', foundRef.name.text);\n        const chainedResults = crawlChainedExpressions(\n          callExpression,\n          pathParts,\n          allFields,\n          source,\n          info\n        );\n        console.log('[GRAPHQLSP]: ', chainedResults.length);\n        if (chainedResults.length) {\n          res.push(...chainedResults);\n        }\n\n        if (ts.isVariableDeclaration(callExpression.parent) && !isSomeOrEvery) {\n          const varRes = crawlScope(\n            callExpression.parent.name,\n            pathParts,\n            allFields,\n            source,\n            info,\n            true\n          );\n          res.push(...varRes);\n        }\n\n        return res;\n      } else if (\n        ts.isPropertyAccessExpression(foundRef) &&\n        !pathParts.includes(foundRef.name.text)\n      ) {\n        const joined = [...pathParts, foundRef.name.text].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          pathParts.push(foundRef.name.text);\n        }\n      } else if (\n        ts.isElementAccessExpression(foundRef) &&\n        ts.isStringLiteral(foundRef.argumentExpression) &&\n        !pathParts.includes(foundRef.argumentExpression.text)\n      ) {\n        const joined = [...pathParts, foundRef.argumentExpression.text].join(\n          '.'\n        );\n        if (allFields.find(x => x.startsWith(joined))) {\n          pathParts.push(foundRef.argumentExpression.text);\n        }\n      }\n\n      if (ts.isNonNullExpression(foundRef.parent)) {\n        foundRef = foundRef.parent.parent;\n      } else {\n        foundRef = foundRef.parent;\n      }\n    }\n\n    return pathParts.join('.');\n  });\n\n  return results;\n};\n\nexport const checkFieldUsageInFile = (\n  source: ts.SourceFile,\n  nodes: ts.NoSubstitutionTemplateLiteral[],\n  info: ts.server.PluginCreateInfo\n) => {\n  const diagnostics: ts.Diagnostic[] = [];\n  const shouldTrackFieldUsage = info.config.trackFieldUsage ?? true;\n  if (!shouldTrackFieldUsage) return diagnostics;\n\n  const defaultReservedKeys = ['id', '_id', '__typename'];\n  const additionalKeys = info.config.reservedKeys ?? [];\n  const reservedKeys = new Set([...defaultReservedKeys, ...additionalKeys]);\n  const checker = info.languageService.getProgram()?.getTypeChecker();\n  if (!checker) return;\n\n  try {\n    nodes.forEach(node => {\n      const nodeText = node.getText();\n      // Bailing for mutations/subscriptions as these could have small details\n      // for normalised cache interactions\n      if (nodeText.includes('mutation') || nodeText.includes('subscription'))\n        return;\n\n      const variableDeclaration = getVariableDeclaration(node);\n      if (!variableDeclaration) return;\n\n      let dataType: ts.Type | undefined;\n\n      const type = checker.getTypeAtLocation(node.parent) as\n        | ts.TypeReference\n        | ts.Type;\n      // Attempt to retrieve type from internally resolve type arguments\n      if ('target' in type) {\n        const typeArguments = (type as any)\n          .resolvedTypeArguments as readonly ts.Type[];\n        dataType =\n          typeArguments && typeArguments.length > 1\n            ? typeArguments[0]\n            : undefined;\n      }\n      // Fallback to resolving the type from scratch\n      if (!dataType) {\n        const apiTypeSymbol = type.getProperty('__apiType');\n        if (apiTypeSymbol) {\n          let apiType = checker.getTypeOfSymbol(apiTypeSymbol);\n          let callSignature: ts.Signature | undefined =\n            type.getCallSignatures()[0];\n          if (apiType.isUnionOrIntersection()) {\n            for (const type of apiType.types) {\n              callSignature = type.getCallSignatures()[0];\n              if (callSignature) {\n                dataType = callSignature.getReturnType();\n                break;\n              }\n            }\n          }\n          dataType = callSignature && callSignature.getReturnType();\n        }\n      }\n\n      const references = info.languageService.getReferencesAtPosition(\n        source.fileName,\n        variableDeclaration.name.getStart()\n      );\n\n      if (!references) return;\n\n      const allAccess: string[] = [];\n      const inProgress: string[] = [];\n      const allPaths: string[] = [];\n      const fieldToLoc = new Map<string, { start: number; length: number }>();\n      // This visitor gets all the leaf-paths in the document\n      // as well as all fields that are part of the document\n      // We need the leaf-paths to check usage and we need the\n      // fields to validate whether an access on a given reference\n      // is valid given the current document...\n      visit(parse(node.getText().slice(1, -1)), {\n        Field: {\n          enter(node) {\n            const alias = node.alias ? node.alias.value : node.name.value;\n            const path = inProgress.length\n              ? `${inProgress.join('.')}.${alias}`\n              : alias;\n\n            if (!node.selectionSet && !reservedKeys.has(node.name.value)) {\n              allPaths.push(path);\n              fieldToLoc.set(path, {\n                start: node.name.loc!.start,\n                length: node.name.loc!.end - node.name.loc!.start,\n              });\n            } else if (node.selectionSet) {\n              inProgress.push(alias);\n              fieldToLoc.set(path, {\n                start: node.name.loc!.start,\n                length: node.name.loc!.end - node.name.loc!.start,\n              });\n            }\n          },\n          leave(node) {\n            if (node.selectionSet) {\n              inProgress.pop();\n            }\n          },\n        },\n      });\n\n      references.forEach(ref => {\n        if (ref.fileName !== source.fileName) return;\n\n        const targetNode = findNode(source, ref.textSpan.start);\n        if (!targetNode) return;\n        // Skip declaration as reference of itself\n        if (targetNode.parent === variableDeclaration) return;\n\n        const scopeSymbols = checker.getSymbolsInScope(\n          targetNode,\n          ts.SymbolFlags.BlockScopedVariable\n        );\n\n        let scopeDataSymbol: ts.Symbol | undefined;\n        for (let scopeSymbol of scopeSymbols) {\n          if (!scopeSymbol.valueDeclaration) continue;\n          let typeOfScopeSymbol = unwrapAbstractType(\n            checker.getTypeOfSymbol(scopeSymbol)\n          );\n          if (dataType === typeOfScopeSymbol) {\n            scopeDataSymbol = scopeSymbol;\n            break;\n          }\n\n          // NOTE: This is an aggressive fallback for hooks where the return value isn't destructured\n          // This is a last resort solution for patterns like react-query, where the fallback that\n          // would otherwise happen below isn't sufficient\n          if (typeOfScopeSymbol.flags & ts.TypeFlags.Object) {\n            const tuplePropertySymbol = typeOfScopeSymbol.getProperty('0');\n            if (tuplePropertySymbol) {\n              typeOfScopeSymbol = checker.getTypeOfSymbol(tuplePropertySymbol);\n              if (dataType === typeOfScopeSymbol) {\n                scopeDataSymbol = scopeSymbol;\n                break;\n              }\n            }\n\n            const dataPropertySymbol = typeOfScopeSymbol.getProperty('data');\n            if (dataPropertySymbol) {\n              typeOfScopeSymbol = unwrapAbstractType(\n                checker.getTypeOfSymbol(dataPropertySymbol)\n              );\n              if (dataType === typeOfScopeSymbol) {\n                scopeDataSymbol = scopeSymbol;\n                break;\n              }\n            }\n          }\n        }\n\n        const valueDeclaration = scopeDataSymbol?.valueDeclaration;\n        let name: ts.BindingName | undefined;\n        if (\n          valueDeclaration &&\n          'name' in valueDeclaration &&\n          !!valueDeclaration.name &&\n          (ts.isIdentifier(valueDeclaration.name as any) ||\n            ts.isBindingName(valueDeclaration.name as any))\n        ) {\n          name = valueDeclaration.name as ts.BindingName;\n        } else {\n          // Fall back to looking at the variable declaration directly,\n          // if we are on one.\n          const variableDeclaration = getVariableDeclaration(targetNode);\n          if (variableDeclaration) name = variableDeclaration.name;\n        }\n\n        if (name) {\n          const result = crawlScope(name, [], allPaths, source, info, false);\n          allAccess.push(...result);\n        }\n      });\n\n      if (!allAccess.length) {\n        return;\n      }\n\n      const unused = allPaths.filter(x => !allAccess.includes(x));\n      const aggregatedUnusedFields = new Set<string>();\n      const unusedChildren: { [key: string]: Set<string> } = {};\n      const unusedFragmentLeaf = new Set<string>();\n      unused.forEach(unusedField => {\n        const split = unusedField.split('.');\n        split.pop();\n        const parentField = split.join('.');\n        const loc = fieldToLoc.get(parentField);\n\n        if (loc) {\n          aggregatedUnusedFields.add(parentField);\n          if (unusedChildren[parentField]) {\n            unusedChildren[parentField]!.add(unusedField);\n          } else {\n            unusedChildren[parentField] = new Set([unusedField]);\n          }\n        } else {\n          unusedFragmentLeaf.add(unusedField);\n        }\n      });\n\n      aggregatedUnusedFields.forEach(field => {\n        const loc = fieldToLoc.get(field)!;\n        const unusedFields = unusedChildren[field]!;\n        diagnostics.push({\n          file: source,\n          length: loc.length,\n          start: node.getStart() + loc.start + 1,\n          category: ts.DiagnosticCategory.Warning,\n          code: UNUSED_FIELD_CODE,\n          messageText: `Field(s) ${[...unusedFields]\n            .map(x => `'${x}'`)\n            .join(', ')} are not used.`,\n        });\n      });\n\n      unusedFragmentLeaf.forEach(field => {\n        const loc = fieldToLoc.get(field)!;\n        diagnostics.push({\n          file: source,\n          length: loc.length,\n          start: node.getStart() + loc.start + 1,\n          category: ts.DiagnosticCategory.Warning,\n          code: UNUSED_FIELD_CODE,\n          messageText: `Field ${field} is not used.`,\n        });\n      });\n    });\n  } catch (e: any) {\n    console.error('[GraphQLSP]: ', e.message, e.stack);\n  }\n\n  return diagnostics;\n};\n", "import { ts } from './ts';\nimport { FragmentDefinitionNode, Kind, parse } from 'graphql';\n\nimport { findAllCallExpressions, findAllImports, getSource } from './ast';\nimport { resolveTemplate } from './ast/resolve';\nimport {\n  VariableDeclaration,\n  VariableStatement,\n  isSourceFile,\n} from 'typescript';\n\nexport const MISSING_FRAGMENT_CODE = 52003;\n\nexport const getColocatedFragmentNames = (\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Record<\n  string,\n  { start: number; length: number; fragments: Array<string> }\n> => {\n  const imports = findAllImports(source);\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n\n  const importSpecifierToFragments: Record<\n    string,\n    { start: number; length: number; fragments: Array<string> }\n  > = {};\n\n  if (!typeChecker) return importSpecifierToFragments;\n\n  if (imports.length) {\n    imports.forEach(imp => {\n      if (!imp.importClause) return;\n\n      if (imp.importClause.name) {\n        const definitions = info.languageService.getDefinitionAtPosition(\n          source.fileName,\n          imp.importClause.name.getStart()\n        );\n        const def = definitions && definitions[0];\n        if (def) {\n          if (def.fileName.includes('node_modules')) return;\n\n          const externalSource = getSource(info, def.fileName);\n          if (!externalSource) return;\n\n          const fragmentsForImport = getFragmentsInSource(\n            externalSource,\n            typeChecker,\n            info\n          );\n\n          const names = fragmentsForImport.map(fragment => fragment.name.value);\n          const key = imp.moduleSpecifier.getText();\n          let fragmentsEntry = importSpecifierToFragments[key];\n          if (names.length && fragmentsEntry) {\n            fragmentsEntry.fragments = fragmentsEntry.fragments.concat(names);\n          } else if (names.length && !fragmentsEntry) {\n            importSpecifierToFragments[key] = fragmentsEntry = {\n              start: imp.moduleSpecifier.getStart(),\n              length: imp.moduleSpecifier.getText().length,\n              fragments: names,\n            };\n          }\n        }\n      }\n\n      if (\n        imp.importClause.namedBindings &&\n        ts.isNamespaceImport(imp.importClause.namedBindings)\n      ) {\n        const definitions = info.languageService.getDefinitionAtPosition(\n          source.fileName,\n          imp.importClause.namedBindings.getStart()\n        );\n        const def = definitions && definitions[0];\n        if (def) {\n          if (def.fileName.includes('node_modules')) return;\n\n          const externalSource = getSource(info, def.fileName);\n          if (!externalSource) return;\n\n          const fragmentsForImport = getFragmentsInSource(\n            externalSource,\n            typeChecker,\n            info\n          );\n          const names = fragmentsForImport.map(fragment => fragment.name.value);\n          const key = imp.moduleSpecifier.getText();\n          let fragmentsEntry = importSpecifierToFragments[key];\n          if (names.length && fragmentsEntry) {\n            fragmentsEntry.fragments = fragmentsEntry.fragments.concat(names);\n          } else if (names.length && !fragmentsEntry) {\n            importSpecifierToFragments[key] = fragmentsEntry = {\n              start: imp.moduleSpecifier.getStart(),\n              length: imp.moduleSpecifier.getText().length,\n              fragments: names,\n            };\n          }\n        }\n      } else if (\n        imp.importClause.namedBindings &&\n        ts.isNamedImportBindings(imp.importClause.namedBindings)\n      ) {\n        imp.importClause.namedBindings.elements.forEach(el => {\n          const definitions = info.languageService.getDefinitionAtPosition(\n            source.fileName,\n            el.getStart()\n          );\n          const def = definitions && definitions[0];\n          if (def) {\n            if (def.fileName.includes('node_modules')) return;\n\n            const externalSource = getSource(info, def.fileName);\n            if (!externalSource) return;\n\n            const fragmentsForImport = getFragmentsInSource(\n              externalSource,\n              typeChecker,\n              info\n            );\n            const names = fragmentsForImport.map(\n              fragment => fragment.name.value\n            );\n            const key = imp.moduleSpecifier.getText();\n            let fragmentsEntry = importSpecifierToFragments[key];\n            if (names.length && fragmentsEntry) {\n              fragmentsEntry.fragments = fragmentsEntry.fragments.concat(names);\n            } else if (names.length && !fragmentsEntry) {\n              importSpecifierToFragments[key] = fragmentsEntry = {\n                start: imp.moduleSpecifier.getStart(),\n                length: imp.moduleSpecifier.getText().length,\n                fragments: names,\n              };\n            }\n          }\n        });\n      }\n    });\n  }\n\n  return importSpecifierToFragments;\n};\n\nfunction getFragmentsInSource(\n  src: ts.SourceFile,\n  typeChecker: ts.TypeChecker,\n  info: ts.server.PluginCreateInfo\n): Array<FragmentDefinitionNode> {\n  let fragments: Array<FragmentDefinitionNode> = [];\n  const callExpressions = findAllCallExpressions(src, info, false);\n\n  const symbol = typeChecker.getSymbolAtLocation(src);\n  if (!symbol) return [];\n\n  const exports = typeChecker.getExportsOfModule(symbol);\n  const exportedNames = exports.map(symb => symb.name);\n  const nodes = callExpressions.nodes.filter(x => {\n    let parent = x.node.parent;\n    while (\n      parent &&\n      !ts.isSourceFile(parent) &&\n      !ts.isVariableDeclaration(parent)\n    ) {\n      parent = parent.parent;\n    }\n\n    if (ts.isVariableDeclaration(parent)) {\n      return exportedNames.includes(parent.name.getText());\n    } else {\n      return false;\n    }\n  });\n\n  nodes.forEach(node => {\n    const text = resolveTemplate(node.node, src.fileName, info).combinedText;\n    try {\n      const parsed = parse(text, { noLocation: true });\n      if (parsed.definitions.every(x => x.kind === Kind.FRAGMENT_DEFINITION)) {\n        fragments = fragments.concat(parsed.definitions as any);\n      }\n    } catch (e) {\n      return;\n    }\n  });\n\n  return fragments;\n}\n", "import { ts } from './ts';\n\nimport { createHash } from 'crypto';\n\nimport * as checks from './ast/checks';\nimport {\n  findAllCallExpressions,\n  findNode,\n  getSource,\n  unrollTadaFragments,\n} from './ast';\nimport { resolveTemplate } from './ast/resolve';\nimport {\n  FragmentDefinitionNode,\n  Kind,\n  parse,\n  print,\n  visit,\n} from '@0no-co/graphql.web';\n\ntype PersistedAction = {\n  span: {\n    start: number;\n    length: number;\n  };\n  replacement: string;\n};\n\nexport function getPersistedCodeFixAtPosition(\n  filename: string,\n  position: number,\n  info: ts.server.PluginCreateInfo\n): PersistedAction | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  if (!isCallExpression) return undefined;\n\n  let source = getSource(info, filename);\n  if (!source) return undefined;\n\n  const node = findNode(source, position);\n  if (!node) return undefined;\n\n  let callExpression: ts.Node = node;\n  // We found a node and need to check where on the path we are\n  // we expect this to look a little bit like\n  // const persistedDoc = graphql.persisted<typeof x>()\n  // When we are on the left half of this statement we bubble down\n  // looking for the correct call-expression and on the right hand\n  // we bubble up.\n  if (ts.isVariableStatement(callExpression)) {\n    callExpression =\n      callExpression.declarationList.declarations.find(declaration => {\n        return (\n          ts.isVariableDeclaration(declaration) &&\n          declaration.initializer &&\n          ts.isCallExpression(declaration.initializer)\n        );\n      }) || node;\n  } else if (ts.isVariableDeclarationList(callExpression)) {\n    callExpression =\n      callExpression.declarations.find(declaration => {\n        return (\n          ts.isVariableDeclaration(declaration) &&\n          declaration.initializer &&\n          ts.isCallExpression(declaration.initializer)\n        );\n      }) || node;\n  } else if (\n    ts.isVariableDeclaration(callExpression) &&\n    callExpression.initializer &&\n    ts.isCallExpression(callExpression.initializer)\n  ) {\n    callExpression = callExpression.initializer;\n  } else {\n    while (callExpression && !ts.isCallExpression(callExpression)) {\n      callExpression = callExpression.parent;\n    }\n  }\n\n  // We want to ensure that we found a call-expression and that it looks\n  // like \"graphql.persisted\", in a future iteration when the API surface\n  // is more defined we will need to use the ts.Symbol to support re-exporting\n  // this function by means of \"export const peristed = graphql.persisted\".\n  if (!checks.isTadaPersistedCall(callExpression, typeChecker)) {\n    return undefined;\n  }\n\n  let foundNode,\n    foundFilename = filename;\n  if (callExpression.typeArguments) {\n    const [typeQuery] = callExpression.typeArguments;\n    if (!typeQuery || !ts.isTypeQueryNode(typeQuery)) return undefined;\n    const { node: found, filename: fileName } =\n      getDocumentReferenceFromTypeQuery(typeQuery, filename, info);\n    foundNode = found;\n    foundFilename = fileName;\n  } else if (callExpression.arguments[1]) {\n    if (\n      !ts.isIdentifier(callExpression.arguments[1]) &&\n      !ts.isCallExpression(callExpression.arguments[1])\n    )\n      return undefined;\n    const { node: found, filename: fileName } =\n      getDocumentReferenceFromDocumentNode(\n        callExpression.arguments[1],\n        filename,\n        info\n      );\n    foundNode = found;\n    foundFilename = fileName;\n  }\n\n  if (!foundNode) return undefined;\n\n  const initializer = foundNode;\n  if (\n    !initializer ||\n    !ts.isCallExpression(initializer) ||\n    !initializer.arguments[0] ||\n    !ts.isStringLiteralLike(initializer.arguments[0])\n  ) {\n    return undefined;\n  }\n\n  const hash = generateHashForDocument(\n    info,\n    initializer.arguments[0],\n    foundFilename,\n    initializer.arguments[1] &&\n      ts.isArrayLiteralExpression(initializer.arguments[1])\n      ? initializer.arguments[1]\n      : undefined\n  );\n  const existingHash = callExpression.arguments[0];\n  // We assume for now that this is either undefined or an existing string literal\n  if (!existingHash) {\n    // We have no persisted-identifier yet, suggest adding in a new one\n    return {\n      span: {\n        start: callExpression.arguments.pos,\n        length: 1,\n      },\n      replacement: `\"sha256:${hash}\")`,\n    };\n  } else if (\n    ts.isStringLiteral(existingHash) &&\n    existingHash.getText() !== `\"sha256:${hash}\"`\n  ) {\n    // We are out of sync, suggest replacing this with the updated hash\n    return {\n      span: {\n        start: existingHash.getStart(),\n        length: existingHash.end - existingHash.getStart(),\n      },\n      replacement: `\"sha256:${hash}\"`,\n    };\n  } else if (ts.isIdentifier(existingHash)) {\n    // Suggest replacing a reference with a static one\n    // this to make these easier to statically analyze\n    return {\n      span: {\n        start: existingHash.getStart(),\n        length: existingHash.end - existingHash.getStart(),\n      },\n      replacement: `\"sha256:${hash}\"`,\n    };\n  } else {\n    return undefined;\n  }\n}\n\nexport const generateHashForDocument = (\n  info: ts.server.PluginCreateInfo,\n  templateLiteral: ts.StringLiteralLike | ts.TaggedTemplateExpression,\n  foundFilename: string,\n  referencedFragments: ts.ArrayLiteralExpression | undefined\n): string | undefined => {\n  if (referencedFragments) {\n    const fragments: Array<FragmentDefinitionNode> = [];\n    unrollTadaFragments(referencedFragments, fragments, info);\n    let text = resolveTemplate(\n      templateLiteral,\n      foundFilename,\n      info\n    ).combinedText;\n    const parsed = parse(text);\n    const seen = new Set<unknown>();\n    for (const definition of parsed.definitions) {\n      if (\n        definition.kind === Kind.FRAGMENT_DEFINITION &&\n        !seen.has(definition)\n      ) {\n        stripUnmaskDirectivesFromDefinition(definition);\n      }\n    }\n\n    const deduplicatedFragments = fragments\n      .map(fragment => {\n        stripUnmaskDirectivesFromDefinition(fragment);\n        return print(fragment);\n      })\n      .filter((fragment, index, array) => array.indexOf(fragment) === index);\n\n    deduplicatedFragments.forEach(fragmentDefinition => {\n      text = `${text}\\n\\n${fragmentDefinition}`;\n    });\n    const fullText = print(parse(text));\n    return createHash('sha256').update(fullText).digest('hex');\n  } else {\n    const externalSource = getSource(info, foundFilename)!;\n    const { fragments } = findAllCallExpressions(externalSource, info);\n\n    const text = resolveTemplate(\n      templateLiteral,\n      foundFilename,\n      info\n    ).combinedText;\n\n    const parsed = parse(text);\n    const seen = new Set<unknown>();\n    for (const definition of parsed.definitions) {\n      if (\n        definition.kind === Kind.FRAGMENT_DEFINITION &&\n        !seen.has(definition)\n      ) {\n        stripUnmaskDirectivesFromDefinition(definition);\n      }\n    }\n\n    const spreads = new Set<string>();\n    visit(parsed, {\n      FragmentDefinition: node => {\n        fragments.push(node);\n      },\n      FragmentSpread: node => {\n        spreads.add(node.name.value);\n      },\n    });\n\n    let resolvedText = text;\n    const visited = new Set();\n    const traversedSpreads = [...spreads];\n\n    let spreadName: string | undefined;\n    while ((spreadName = traversedSpreads.shift())) {\n      visited.add(spreadName);\n      const fragmentDefinition = fragments.find(\n        x => x.name.value === spreadName\n      );\n      if (!fragmentDefinition) {\n        info.project.projectService.logger.info(\n          `[GraphQLSP] could not find fragment for spread ${spreadName}!`\n        );\n        return;\n      }\n\n      stripUnmaskDirectivesFromDefinition(fragmentDefinition);\n\n      visit(fragmentDefinition, {\n        FragmentSpread: node => {\n          if (!visited.has(node.name.value))\n            traversedSpreads.push(node.name.value);\n        },\n      });\n\n      resolvedText = `${resolvedText}\\n\\n${print(fragmentDefinition)}`;\n    }\n\n    return createHash('sha256')\n      .update(print(parse(resolvedText)))\n      .digest('hex');\n  }\n};\n\nexport const getDocumentReferenceFromTypeQuery = (\n  typeQuery: ts.TypeQueryNode,\n  filename: string,\n  info: ts.server.PluginCreateInfo\n): { node: ts.CallExpression | null; filename: string } => {\n  // We look for the references of the generic so that we can use the document\n  // to generate the hash.\n  const references = info.languageService.getReferencesAtPosition(\n    filename,\n    typeQuery.exprName.getStart()\n  );\n\n  if (!references) return { node: null, filename };\n\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  let found: ts.CallExpression | null = null;\n  let foundFilename = filename;\n  references.forEach(ref => {\n    if (found) return;\n\n    const source = getSource(info, ref.fileName);\n    if (!source) return;\n    const foundNode = findNode(source, ref.textSpan.start);\n    if (!foundNode) return;\n\n    if (\n      ts.isVariableDeclaration(foundNode.parent) &&\n      foundNode.parent.initializer &&\n      checks.isGraphQLCall(foundNode.parent.initializer, typeChecker)\n    ) {\n      found = foundNode.parent.initializer;\n      foundFilename = ref.fileName;\n    }\n  });\n\n  return { node: found, filename: foundFilename };\n};\n\nexport const getDocumentReferenceFromDocumentNode = (\n  documentNodeArgument: ts.Identifier | ts.CallExpression,\n  filename: string,\n  info: ts.server.PluginCreateInfo\n): { node: ts.CallExpression | null; filename: string } => {\n  if (ts.isIdentifier(documentNodeArgument)) {\n    // We look for the references of the generic so that we can use the document\n    // to generate the hash.\n    const references = info.languageService.getReferencesAtPosition(\n      filename,\n      documentNodeArgument.getStart()\n    );\n\n    if (!references) return { node: null, filename };\n\n    const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n    let found: ts.CallExpression | null = null;\n    let foundFilename = filename;\n    references.forEach(ref => {\n      if (found) return;\n\n      const source = getSource(info, ref.fileName);\n      if (!source) return;\n      const foundNode = findNode(source, ref.textSpan.start);\n      if (!foundNode) return;\n\n      if (\n        ts.isVariableDeclaration(foundNode.parent) &&\n        foundNode.parent.initializer &&\n        checks.isGraphQLCall(foundNode.parent.initializer, typeChecker)\n      ) {\n        found = foundNode.parent.initializer;\n        foundFilename = ref.fileName;\n      }\n    });\n\n    return { node: found, filename: foundFilename };\n  } else {\n    return { node: documentNodeArgument, filename };\n  }\n};\n\ntype writable<T> = { -readonly [K in keyof T]: T[K] };\n\nconst stripUnmaskDirectivesFromDefinition = (\n  definition: FragmentDefinitionNode\n) => {\n  (definition as writable<FragmentDefinitionNode>).directives =\n    definition.directives?.filter(\n      directive => directive.name.value !== '_unmask'\n    );\n};\n", "import { ts } from './ts';\nimport { Diagnostic, getDiagnostics } from 'graphql-language-service';\nimport {\n  FragmentDefinitionNode,\n  GraphQLSchema,\n  Kind,\n  OperationDefinitionNode,\n  parse,\n  visit,\n} from 'graphql';\nimport { LRUCache } from 'lru-cache';\nimport fnv1a from '@sindresorhus/fnv1a';\nimport { print } from '@0no-co/graphql.web';\n\nimport {\n  findAllCallExpressions,\n  findAllPersistedCallExpressions,\n  findAllTaggedTemplateNodes,\n  getSource,\n} from './ast';\nimport { resolveTemplate } from './ast/resolve';\nimport { UNUSED_FIELD_CODE, checkFieldUsageInFile } from './fieldUsage';\nimport {\n  MISSING_FRAGMENT_CODE,\n  getColocatedFragmentNames,\n} from './checkImports';\nimport {\n  generateHashForDocument,\n  getDocumentReferenceFromDocumentNode,\n  getDocumentReferenceFromTypeQuery,\n} from './persisted';\nimport { SchemaRef } from './graphql/getSchema';\n\nconst clientDirectives = new Set([\n  'populate',\n  'client',\n  '_unmask',\n  '_optional',\n  '_relayPagination',\n  '_simplePagination',\n  '_required',\n  'optional',\n  'required',\n  'arguments',\n  'argumentDefinitions',\n  'connection',\n  'refetchable',\n  'relay',\n  'required',\n  'inline',\n]);\n\nexport const SEMANTIC_DIAGNOSTIC_CODE = 52001;\nexport const MISSING_OPERATION_NAME_CODE = 52002;\nexport const USING_DEPRECATED_FIELD_CODE = 52004;\nexport const MISSING_PERSISTED_TYPE_ARG = 520100;\nexport const MISSING_PERSISTED_CODE_ARG = 520101;\nexport const MISSING_PERSISTED_DOCUMENT = 520102;\nexport const MISSMATCH_HASH_TO_DOCUMENT = 520103;\nexport const ALL_DIAGNOSTICS = [\n  SEMANTIC_DIAGNOSTIC_CODE,\n  MISSING_OPERATION_NAME_CODE,\n  USING_DEPRECATED_FIELD_CODE,\n  MISSING_FRAGMENT_CODE,\n  UNUSED_FIELD_CODE,\n  MISSING_PERSISTED_TYPE_ARG,\n  MISSING_PERSISTED_CODE_ARG,\n  MISSING_PERSISTED_DOCUMENT,\n  MISSMATCH_HASH_TO_DOCUMENT,\n];\n\nconst cache = new LRUCache<number, ts.Diagnostic[]>({\n  // how long to live in ms\n  ttl: 1000 * 60 * 15,\n  max: 5000,\n});\n\nexport function getGraphQLDiagnostics(\n  filename: string,\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.Diagnostic[] | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n\n  let source = getSource(info, filename);\n  if (!source) return undefined;\n\n  let fragments: Array<FragmentDefinitionNode> = [],\n    nodes: {\n      node: ts.StringLiteralLike | ts.TaggedTemplateExpression;\n      schema: string | null;\n    }[];\n  if (isCallExpression) {\n    const result = findAllCallExpressions(source, info);\n    fragments = result.fragments;\n    nodes = result.nodes;\n  } else {\n    nodes = findAllTaggedTemplateNodes(source).map(x => ({\n      node: x,\n      schema: null,\n    }));\n  }\n\n  const texts = nodes.map(({ node }) => {\n    if (\n      (ts.isNoSubstitutionTemplateLiteral(node) ||\n        ts.isTemplateExpression(node)) &&\n      !isCallExpression\n    ) {\n      if (ts.isTaggedTemplateExpression(node.parent)) {\n        node = node.parent;\n      } else {\n        return undefined;\n      }\n    }\n\n    return resolveTemplate(node, filename, info).combinedText;\n  });\n\n  const cacheKey = fnv1a(\n    isCallExpression\n      ? source.getText() +\n          fragments.map(x => print(x)).join('-') +\n          schema.version\n      : texts.join('-') + schema.version\n  );\n\n  let tsDiagnostics: ts.Diagnostic[];\n  if (cache.has(cacheKey)) {\n    tsDiagnostics = cache.get(cacheKey)!;\n  } else {\n    tsDiagnostics = runDiagnostics(source, { nodes, fragments }, schema, info);\n    cache.set(cacheKey, tsDiagnostics);\n  }\n\n  const shouldCheckForColocatedFragments =\n    info.config.shouldCheckForColocatedFragments ?? true;\n  let fragmentDiagnostics: ts.Diagnostic[] = [];\n\n  if (isCallExpression) {\n    const persistedCalls = findAllPersistedCallExpressions(source, info);\n    // We need to check whether the user has correctly inserted a hash,\n    // by means of providing an argument to the function and that they\n    // are establishing a reference to the document by means of the generic.\n    const persistedDiagnostics = persistedCalls\n      .map<ts.Diagnostic | null>(found => {\n        const { node: callExpression } = found;\n        if (!callExpression.typeArguments && !callExpression.arguments[1]) {\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_TYPE_ARG,\n            file: source,\n            messageText: 'Missing generic pointing at the GraphQL document.',\n            start: callExpression.getStart(),\n            length: callExpression.getEnd() - callExpression.getStart(),\n          };\n        }\n\n        let foundNode,\n          foundFilename = filename,\n          ref,\n          start,\n          length;\n        const typeQuery =\n          callExpression.typeArguments && callExpression.typeArguments[0];\n        if (typeQuery) {\n          start = typeQuery.getStart();\n          length = typeQuery.getEnd() - typeQuery.getStart();\n\n          if (!ts.isTypeQueryNode(typeQuery)) {\n            return {\n              category: ts.DiagnosticCategory.Warning,\n              code: MISSING_PERSISTED_TYPE_ARG,\n              file: source,\n              messageText:\n                'Provided generic should be a typeQueryNode in the shape of graphql.persisted<typeof document>.',\n              start,\n              length,\n            };\n          }\n          const { node: found, filename: fileName } =\n            getDocumentReferenceFromTypeQuery(typeQuery, filename, info);\n          foundNode = found;\n          foundFilename = fileName;\n          ref = typeQuery.getText();\n        } else if (callExpression.arguments[1]) {\n          start = callExpression.arguments[1].getStart();\n          length =\n            callExpression.arguments[1].getEnd() -\n            callExpression.arguments[1].getStart();\n          if (\n            !ts.isIdentifier(callExpression.arguments[1]) &&\n            !ts.isCallExpression(callExpression.arguments[1])\n          ) {\n            return {\n              category: ts.DiagnosticCategory.Warning,\n              code: MISSING_PERSISTED_TYPE_ARG,\n              file: source,\n              messageText:\n                'Provided argument should be an identifier or invocation of \"graphql\" in the shape of graphql.persisted(hash, document).',\n              start,\n              length,\n            };\n          }\n\n          const { node: found, filename: fileName } =\n            getDocumentReferenceFromDocumentNode(\n              callExpression.arguments[1],\n              filename,\n              info\n            );\n          foundNode = found;\n          foundFilename = fileName;\n          ref = callExpression.arguments[1].getText();\n        }\n\n        if (!foundNode) {\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_DOCUMENT,\n            file: source,\n            messageText: `Can't find reference to \"${ref}\".`,\n            start,\n            length,\n          };\n        }\n\n        const initializer = foundNode;\n        if (\n          !initializer ||\n          !ts.isCallExpression(initializer) ||\n          !initializer.arguments[0] ||\n          !ts.isStringLiteralLike(initializer.arguments[0])\n        ) {\n          // TODO: we can make this check more stringent where we also parse and resolve\n          // the accompanying template.\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_DOCUMENT,\n            file: source,\n            messageText: `Referenced type \"${ref}\" is not a GraphQL document.`,\n            start,\n            length,\n          };\n        }\n\n        if (!callExpression.arguments[0]) {\n          // TODO: this might be covered by the API enforcing the first\n          // argument so can possibly be removed.\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_CODE_ARG,\n            file: source,\n            messageText: `The call-expression is missing a hash for the persisted argument.`,\n            start: callExpression.arguments.pos,\n            length: callExpression.arguments.end - callExpression.arguments.pos,\n          };\n        }\n\n        const hash = callExpression.arguments[0].getText().slice(1, -1);\n        if (hash.startsWith('sha256:')) {\n          const generatedHash = generateHashForDocument(\n            info,\n            initializer.arguments[0],\n            foundFilename,\n            initializer.arguments[1] &&\n              ts.isArrayLiteralExpression(initializer.arguments[1])\n              ? initializer.arguments[1]\n              : undefined\n          );\n          if (!generatedHash) return null;\n\n          const upToDateHash = `sha256:${generatedHash}`;\n          if (upToDateHash !== hash) {\n            return {\n              category: ts.DiagnosticCategory.Warning,\n              code: MISSMATCH_HASH_TO_DOCUMENT,\n              file: source,\n              messageText: `The persisted document's hash is outdated`,\n              start: callExpression.arguments.pos,\n              length:\n                callExpression.arguments.end - callExpression.arguments.pos,\n            };\n          }\n        }\n\n        return null;\n      })\n      .filter(Boolean);\n\n    tsDiagnostics.push(...(persistedDiagnostics as ts.Diagnostic[]));\n  }\n\n  if (isCallExpression && shouldCheckForColocatedFragments) {\n    const moduleSpecifierToFragments = getColocatedFragmentNames(source, info);\n\n    const usedFragments = new Set();\n    nodes.forEach(({ node }) => {\n      try {\n        const parsed = parse(node.getText().slice(1, -1), {\n          noLocation: true,\n        });\n        visit(parsed, {\n          FragmentSpread: node => {\n            usedFragments.add(node.name.value);\n          },\n        });\n      } catch (e) {}\n    });\n\n    Object.keys(moduleSpecifierToFragments).forEach(moduleSpecifier => {\n      const {\n        fragments: fragmentNames,\n        start,\n        length,\n      } = moduleSpecifierToFragments[moduleSpecifier]!;\n      const missingFragments = Array.from(\n        new Set(fragmentNames.filter(x => !usedFragments.has(x)))\n      );\n      if (missingFragments.length) {\n        fragmentDiagnostics.push({\n          file: source,\n          length,\n          start,\n          category: ts.DiagnosticCategory.Warning,\n          code: MISSING_FRAGMENT_CODE,\n          messageText: `Unused co-located fragment definition(s) \"${missingFragments.join(\n            ', '\n          )}\" in ${moduleSpecifier}`,\n        });\n      }\n    });\n\n    return [...tsDiagnostics, ...fragmentDiagnostics];\n  } else {\n    return tsDiagnostics;\n  }\n}\n\nconst runDiagnostics = (\n  source: ts.SourceFile,\n  {\n    nodes,\n    fragments,\n  }: {\n    nodes: {\n      node: ts.TaggedTemplateExpression | ts.StringLiteralLike;\n      schema: string | null;\n    }[];\n    fragments: FragmentDefinitionNode[];\n  },\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.Diagnostic[] => {\n  const filename = source.fileName;\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n\n  const diagnostics = nodes\n    .map(originalNode => {\n      let node = originalNode.node;\n      if (\n        !isCallExpression &&\n        (ts.isNoSubstitutionTemplateLiteral(node) ||\n          ts.isTemplateExpression(node))\n      ) {\n        if (ts.isTaggedTemplateExpression(node.parent)) {\n          node = node.parent;\n        } else {\n          return undefined;\n        }\n      }\n\n      const { combinedText: text, resolvedSpans } = resolveTemplate(\n        node,\n        filename,\n        info\n      );\n      const lines = text.split('\\n');\n\n      let isExpression = false;\n      if (ts.isAsExpression(node.parent)) {\n        if (ts.isExpressionStatement(node.parent.parent)) {\n          isExpression = true;\n        }\n      } else if (ts.isExpressionStatement(node.parent)) {\n        isExpression = true;\n      }\n      // When we are dealing with a plain gql statement we have to add two these can be recognised\n      // by the fact that the parent is an expressionStatement\n\n      let startingPosition =\n        node.getStart() +\n        (isCallExpression\n          ? 0\n          : (node as ts.TaggedTemplateExpression).tag.getText().length +\n            (isExpression ? 2 : 0));\n      const endPosition = startingPosition + node.getText().length;\n      let docFragments = [...fragments];\n      if (isCallExpression) {\n        try {\n          const documentFragments = parse(text, {\n            noLocation: true,\n          }).definitions.filter(x => x.kind === Kind.FRAGMENT_DEFINITION);\n          docFragments = docFragments.filter(\n            x =>\n              !documentFragments.some(\n                y =>\n                  y.kind === Kind.FRAGMENT_DEFINITION &&\n                  y.name.value === x.name.value\n              )\n          );\n        } catch (e) {}\n      }\n\n      const schemaToUse =\n        originalNode.schema && schema.multi[originalNode.schema]\n          ? schema.multi[originalNode.schema]?.schema\n          : schema.current?.schema;\n\n      if (!schemaToUse) {\n        return undefined;\n      }\n\n      const graphQLDiagnostics = getDiagnostics(\n        text,\n        schemaToUse,\n        undefined,\n        undefined,\n        docFragments\n      )\n        .filter(diag => {\n          if (!diag.message.includes('Unknown directive')) return true;\n\n          const [message] = diag.message.split('(');\n          const matches =\n            message && /Unknown directive \"@([^)]+)\"/g.exec(message);\n          if (!matches) return true;\n          const directiveName = matches[1];\n          return directiveName && !clientDirectives.has(directiveName);\n        })\n        .map(x => {\n          const { start, end } = x.range;\n\n          // We add the start.line to account for newline characters which are\n          // split out\n          let startChar = startingPosition + start.line;\n          for (let i = 0; i <= start.line && i < lines.length; i++) {\n            if (i === start.line) startChar += start.character;\n            else if (lines[i]) startChar += lines[i]!.length;\n          }\n\n          let endChar = startingPosition + end.line;\n          for (let i = 0; i <= end.line && i < lines.length; i++) {\n            if (i === end.line) endChar += end.character;\n            else if (lines[i]) endChar += lines[i]!.length;\n          }\n\n          const locatedInFragment = resolvedSpans.find(x => {\n            const newEnd = x.new.start + x.new.length;\n            return startChar >= x.new.start && endChar <= newEnd;\n          });\n\n          if (!!locatedInFragment) {\n            return {\n              ...x,\n              start: locatedInFragment.original.start,\n              length: locatedInFragment.original.length,\n            };\n          } else {\n            if (startChar > endPosition) {\n              // we have to calculate the added length and fix this\n              const addedCharacters = resolvedSpans\n                .filter(x => x.new.start + x.new.length < startChar)\n                .reduce(\n                  (acc, span) => acc + (span.new.length - span.original.length),\n                  0\n                );\n              startChar = startChar - addedCharacters;\n              endChar = endChar - addedCharacters;\n              return {\n                ...x,\n                start: startChar + 1,\n                length: endChar - startChar,\n              };\n            } else {\n              return {\n                ...x,\n                start: startChar + 1,\n                length: endChar - startChar,\n              };\n            }\n          }\n        })\n        .filter(x => x.start + x.length <= endPosition);\n\n      try {\n        const parsed = parse(text, { noLocation: true });\n\n        if (\n          parsed.definitions.some(x => x.kind === Kind.OPERATION_DEFINITION)\n        ) {\n          const op = parsed.definitions.find(\n            x => x.kind === Kind.OPERATION_DEFINITION\n          ) as OperationDefinitionNode;\n          if (!op.name) {\n            graphQLDiagnostics.push({\n              message: 'Operation should contain a name.',\n              start: node.getStart(),\n              code: MISSING_OPERATION_NAME_CODE,\n              length: originalNode.node.getText().length,\n              range: {} as any,\n              severity: 2,\n            } as any);\n          }\n        }\n      } catch (e) {}\n\n      return graphQLDiagnostics;\n    })\n    .flat()\n    .filter(Boolean) as Array<Diagnostic & { length: number; start: number }>;\n\n  const tsDiagnostics = diagnostics.map(\n    diag =>\n      ({\n        file: source,\n        length: diag.length,\n        start: diag.start,\n        category:\n          diag.severity === 2\n            ? ts.DiagnosticCategory.Warning\n            : ts.DiagnosticCategory.Error,\n        code:\n          typeof diag.code === 'number'\n            ? diag.code\n            : diag.severity === 2\n            ? USING_DEPRECATED_FIELD_CODE\n            : SEMANTIC_DIAGNOSTIC_CODE,\n        messageText: diag.message.split('\\n')[0],\n      } as ts.Diagnostic)\n  );\n\n  if (isCallExpression) {\n    const usageDiagnostics =\n      checkFieldUsageInFile(\n        source,\n        nodes.map(x => x.node) as ts.NoSubstitutionTemplateLiteral[],\n        info\n      ) || [];\n\n    if (!usageDiagnostics) return tsDiagnostics;\n\n    return [...tsDiagnostics, ...usageDiagnostics];\n  } else {\n    return tsDiagnostics;\n  }\n};\n"], "names": ["ts", "CharacterStream", "constructor", "sourceText", "this", "_start", "_pos", "getStartOfToken", "getCurrentPosition", "eol", "_sourceText", "length", "sol", "peek", "char<PERSON>t", "next", "char", "eat", "pattern", "_testNextCharacter", "eat<PERSON>hile", "match", "isMatched", "didEat", "eatSpace", "skipToEnd", "skip<PERSON>o", "position", "consume", "caseFold", "token", "RegExp", "test", "slice", "Array", "startsWith", "backUp", "num", "column", "indentation", "indent", "whiteSpaces", "pos", "charCodeAt", "current", "character", "opt", "ofRule", "list", "separator", "isList", "t", "kind", "style", "p", "value", "isIgnored", "ch", "LexRules", "Name", "Punctuation", "Number", "String", "Comment", "ParseRules", "Document", "Definition", "Kind", "FRAGMENT_DEFINITION", "<PERSON><PERSON><PERSON><PERSON>", "Query", "word", "name", "Mutation", "Subscription", "VariableDefinitions", "VariableDefinition", "Variable", "DefaultValue", "SelectionSet", "Selection", "stream", "Ali<PERSON>dF<PERSON>", "Field", "Arguments", "Argument", "FragmentSpread", "InlineFragment", "FragmentDefinition", "but<PERSON>ot", "rule", "exclusions", "ruleMatch", "check", "every", "exclusion", "TypeCondition", "Value", "NumberValue", "StringValue", "update", "state", "inBlockstring", "endsWith", "BooleanValue", "Null<PERSON><PERSON>ue", "EnumValue", "ListValue", "ObjectValue", "ObjectField", "Type", "ListType", "NonNullType", "NamedType", "type", "_a", "prevState", "Directive", "DirectiveDef", "InterfaceDef", "Implements", "DirectiveLocation", "SchemaDef", "OperationTypeDef", "ScalarDef", "ObjectTypeDef", "FieldDef", "ArgumentsDef", "InputValueDef", "UnionDef", "UnionMember", "EnumDef", "EnumValueDef", "InputDef", "ExtendDef", "ExtensionDefinition", "SCHEMA_EXTENSION", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_EXTENSION", "onlineParser", "options", "eatWhitespace", "lexRules", "parseRules", "editorConfig", "startState", "initialState", "level", "step", "needsSeparator", "pushRule", "DOCUMENT", "getToken", "popRule", "needsAdvance", "advanceRule", "tabSize", "indentLevel", "Math", "floor", "lex", "kinds", "Object", "keys", "i", "SpecialParseRules", "backupState", "assign", "undefined", "levels", "concat", "at", "expected", "call", "unsuccessful", "to", "from", "Invalid", "rules", "ruleKind", "TypeError", "successful", "isArray", "Range", "start", "end", "containsPosition", "line", "setStart", "Position", "setEnd", "lessThanOrEqualTo", "setLine", "<PERSON><PERSON><PERSON><PERSON>", "specifiedSDLRules", "LoneSchemaDefinitionRule", "UniqueOperationTypesRule", "UniqueTypeNamesRule", "UniqueEnumValueNamesRule", "UniqueFieldDefinitionNamesRule", "UniqueDirectiveNamesRule", "KnownTypeNamesRule", "KnownDirectivesRule", "UniqueDirectivesPerLocationRule", "PossibleTypeExtensionsRule", "UniqueArgumentNamesRule", "UniqueInputFieldNamesRule", "DIAGNOSTIC_SEVERITY", "invariant", "condition", "message", "Error", "getDiagnostics", "query", "schema", "customRules", "isRelayCompatMode", "externalFragments", "ast", "fragments", "reduce", "acc", "node", "print", "enhancedQuery", "parse", "error", "GraphQLError", "range", "getRange", "location", "queryText", "parser", "lines", "split", "_b", "locations", "severity", "source", "validate<PERSON><PERSON>y", "validationErrorAnnotations", "validateWithCustomRules", "isSchemaDocument", "specifiedRules", "filter", "NoUnusedFragmentsRule", "ExecutableDefinitionsRule", "KnownFragmentNamesRule", "prototype", "push", "apply", "validate", "includes", "nodes", "DIRECTIVE", "flatMap", "annotations", "deprecationWarningAnnotations", "NoDeprecatedCustomRule", "Warning", "highlightedNodes", "entries", "highlightNode", "variable", "loc", "highlightLoc", "getLocation", "e", "templates", "Set", "isIIFE", "isCallExpression", "arguments", "isFunctionExpression", "expression", "isArrowFunction", "asteriskToken", "modifiers", "isGraphQLFunctionIdentifier", "isIdentifier", "has", "escapedText", "isTadaGraphQLFunction", "checker", "isLeftHandSideExpression", "getTypeAtLocation", "getProperty", "isTadaGraphQLCall", "isStringLiteralLike", "isTadaPersistedCall", "isPropertyAccessExpression", "isGraphQLCall", "isGraphQLTag", "isTaggedTemplateExpression", "tag", "getSchemaName", "typeC<PERSON>cker", "getChildAt", "brandTypeSymbol", "brand", "getTypeOfSymbol", "isUnionOrIntersection", "found", "types", "find", "x", "isStringLiteral", "resolveTemplate", "filename", "info", "combinedText", "getText", "resolvedSpans", "templateText", "template", "isNoSubstitutionTemplateLiteral", "templateSpans", "addedCharacters", "map", "span", "definitions", "languageService", "getDefinitionAtPosition", "getStart", "def", "src", "getSource", "fileName", "findNode", "textSpan", "parent", "isVariableDeclaration", "identifierName", "originalStart", "originalRange", "initializer", "text", "replace", "alteredSpan", "identifier", "original", "new", "isAsExpression", "isObjectLiteralExpression", "resolvedTemplate", "JSON", "Boolean", "resolveTadaFragmentArray", "isArrayLiteralExpression", "elements", "identifiers", "element", "program", "getProgram", "getSourceFile", "sourceFile", "getEnd", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "unrollFragment", "seen", "WeakSet", "_unrollElement", "add", "resolveIdentifierToGraphQLCall", "input", "prevElement", "fragment", "externalSource", "isPropertyAssignment", "isBinaryExpression", "right", "checks", "fragmentRefs", "noLocation", "for<PERSON>ach", "definition", "_error", "nextElement", "shift", "unrollTadaFragments", "fragmentsArray", "wip", "getType<PERSON><PERSON>cker", "el", "findAllCallExpressions", "shouldSearchFragments", "result", "hasTriedToFindFragments", "getAllFragments", "findAllPersistedCallExpressions", "isVariableStatement", "declarationList", "declarations", "declaration", "properties", "property", "possibleFragment", "perf", "performance", "now", "Date", "warned", "PROCESS", "process", "emitWarning", "msg", "code", "fn", "console", "AC", "globalThis", "AbortController", "AS", "AbortSignal", "_onabort", "aborted", "addEventListener", "_", "warnACPolyfill", "signal", "abort", "reason", "<PERSON>ab<PERSON>", "printACPolyfillWarning", "env", "LRU_CACHE_IGNORE_AC_WARNING", "isPosInt", "n", "isFinite", "getUintArray", "max", "pow", "Uint8Array", "Uint16Array", "Uint32Array", "MAX_SAFE_INTEGER", "ZeroArray", "size", "super", "fill", "<PERSON><PERSON>", "static", "create", "HeapCls", "constructing", "s", "heap", "pop", "L<PERSON><PERSON><PERSON>", "maxSize", "dispose", "disposeAfter", "fetch<PERSON><PERSON><PERSON>", "calculatedSize", "keyMap", "keyList", "valList", "prev", "head", "tail", "free", "disposed", "sizes", "starts", "ttls", "hasDispose", "hasFetchMethod", "hasDisposeAfter", "unsafeExposeInternals", "c", "isBackgroundFetch", "backgroundFetch", "k", "index", "context", "moveToTail", "indexes", "rindexes", "isStale", "ttl", "ttlResolution", "ttlAutopurge", "updateAgeOnGet", "updateAgeOnHas", "allowStale", "noDisposeOnSet", "noUpdateTTL", "maxEntrySize", "sizeCalculation", "noDeleteOnFetchRejection", "noDeleteOnStaleGet", "allowStaleOnFetchRejection", "allowStaleOnFetchAbort", "ignoreFetchAbort", "UintArray", "Map", "initializeSizeTracking", "initializeTTLTracking", "<PERSON><PERSON><PERSON><PERSON>", "getRemainingTTL", "key", "Infinity", "setItemTTL", "setTimeout", "delete", "unref", "updateItemAge", "statusTTL", "status", "cachedNow", "getNow", "remainingTTL", "get", "#updateItemAge", "#statusTTL", "#setItemTTL", "#isStale", "removeItemSize", "requireSize", "v", "addItemSize", "evict", "entrySize", "totalCalculatedSize", "_i", "#addItemSize", "_s", "_st", "#requireSize", "_k", "_v", "isValidIndex", "rentries", "rkeys", "values", "rvalues", "Symbol", "iterator", "getOptions", "__staleWhileFetching", "thisp", "rforEach", "purgeStale", "deleted", "dump", "arr", "entry", "age", "unshift", "load", "clear", "set", "setOptions", "maxEntrySizeExceeded", "oldVal", "__abortController", "oldValue", "dt", "task", "val", "hasOptions", "peekOptions", "ac", "fetchOpts", "cb", "updateCache", "ignoreAbort", "fetchAborted", "fetchError", "fetchAbortIgnored", "fetchResolved", "fetchFail", "fetchUpdated", "er", "allowStaleAborted", "bf", "returnedStale", "__returned", "fetchDispatched", "Promise", "pcall", "res", "rej", "fmp", "then", "fetchRejected", "b", "hasOwnProperty", "fetch", "fetchOptions", "forceRefresh", "stale", "staleVal", "fetching", "connect", "FNV_PRIMES", "FNV_OFFSETS", "fnv1aModule", "exports", "fnv1a", "string", "hash", "isUnicoded", "characterCode", "unescape", "encodeURIComponent", "bigInt", "fnvPrime", "BigInt", "asUintN", "UNUSED_FIELD_CODE", "unwrapAbstractType", "flags", "TypeFlags", "getVariableDeclaration", "isBlock", "traverseArrayDestructuring", "originalWip", "allFields", "isOmittedExpression", "crawlScope", "isObjectBindingPattern", "traverseDestructuring", "results", "_loop", "binding", "propertyName", "joined", "join", "traverseResult", "crawlResult", "arrayMethods", "crawlChainedExpressions", "ref", "pathParts", "<PERSON><PERSON><PERSON><PERSON>", "log", "getFullText", "isReduce", "func", "nested<PERSON><PERSON>ult", "getSymbolAtLocation", "valueDeclaration", "isFunctionDeclaration", "param", "parameters", "scopedResult", "inArrayMethod", "isArrayBindingPattern", "references", "getReferencesAtPosition", "foundRef", "_ret", "_loop2", "isReturnStatement", "callExpression", "isSomeOrEvery", "chainedResults", "varRes", "isElementAccessExpression", "argumentExpression", "isNonNullExpression", "getColocatedFragmentNames", "imports", "findAllImports", "statements", "isImportDeclaration", "importSpecifierToFragments", "imp", "importClause", "names", "getFragmentsInSource", "moduleSpecifier", "fragmentsEntry", "<PERSON><PERSON><PERSON><PERSON>", "isNamespaceImport", "isNamedImportBindings", "callExpressions", "symbol", "exportedNames", "getExportsOfModule", "symb", "isSourceFile", "parsed", "generateHashForDocument", "templateLiteral", "foundFilename", "referencedFragments", "stripUnmaskDirectivesFromDefinition", "array", "indexOf", "fragmentDefinition", "fullText", "createHash", "digest", "spreads", "visit", "resolvedText", "visited", "traversedSpreads", "spreadName", "project", "projectService", "logger", "getDocumentReferenceFromTypeQuery", "typeQuery", "exprName", "foundNode", "getDocumentReferenceFromDocumentNode", "documentNodeArgument", "directives", "directive", "clientDirectives", "MISSING_PERSISTED_TYPE_ARG", "MISSING_PERSISTED_CODE_ARG", "MISSING_PERSISTED_DOCUMENT", "MISSMATCH_HASH_TO_DOCUMENT", "ALL_DIAGNOSTICS", "cache", "runDiagnostics", "config", "templateIsCallExpression", "diagnostics", "originalNode", "isTemplateExpression", "isExpression", "isExpressionStatement", "startingPosition", "endPosition", "docFragments", "documentFragments", "some", "y", "schemaToUse", "multi", "graphQLDiagnostics", "diag", "matches", "exec", "directiveName", "startChar", "endChar", "locatedInFragment", "OPERATION_DEFINITION", "flat", "tsDiagnostics", "file", "category", "DiagnosticCategory", "messageText", "usageDiagnostics", "checkFieldUsageInFile", "trackFieldUsage", "reservedKeys", "nodeText", "variableDeclaration", "dataType", "typeArguments", "resolvedTypeArguments", "apiTypeSymbol", "apiType", "callSignature", "getCallSignatures", "getReturnType", "allAccess", "inProgress", "allPaths", "fieldToLoc", "enter", "alias", "path", "selectionSet", "leave", "targetNode", "scopeSymbols", "getSymbolsInScope", "SymbolFlags", "BlockScopedVariable", "scopeDataSymbol", "scopeSymbol", "typeOfScopeSymbol", "tuplePropertySymbol", "dataPropertySymbol", "isBindingName", "unused", "aggregatedUnusedFields", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unusedFragmentLeaf", "unusedField", "parentField", "field", "unusedFields", "stack", "bubbleUpCallExpression", "isToken", "isTemplateSpan", "bubbleUpTemplate", "getGraphQLDiagnostics", "findAllTaggedTemplateNodes", "texts", "cache<PERSON>ey", "version", "shouldCheckForColocatedFragments", "fragmentDiagnostics", "persistedDiagnostics", "isTypeQueryNode", "generatedHash", "moduleSpecifierToFragments", "usedFragments", "fragmentNames", "missingFragments", "getPersistedCodeFixAtPosition", "isVariableDeclarationList", "existingHash", "replacement", "init", "modules", "typescript"], "mappings": ";;;;AAAWA,QAAEA,UAAA;;ACqBC,MAAOC;EAKnBC,WAAAA,CAAYC;IAJJC,KAAAC,SAAS;IACTD,KAAAE,OAAO;IAORF,KAAAG,kBAAkB,MAAcH,KAAKC;IAErCD,KAAAI,qBAAqB,MAAcJ,KAAKE;IAgBxCF,KAAAK,MAAM,MAAeL,KAAKM,YAAYC,WAAWP,KAAKE;IAEtDF,KAAAQ,MAAM,MAA6B,MAAdR,KAAKE;IAE1BF,KAAAS,OAAO,MACLT,KAAKM,YAAYI,OAAOV,KAAKE,SAAS;IAGxCF,KAAAW,OAAO;MACZ,IAAMC,IAAOZ,KAAKM,YAAYI,OAAOV,KAAKE;MAC1CF,KAAKE;MACL,OAAOU;AAAI;IAGNZ,KAAAa,MAAOC;MAEZ,IADkBd,KAAKe,mBAAmBD,IAC3B;QACbd,KAAKC,SAASD,KAAKE;QACnBF,KAAKE;QACL,OAAOF,KAAKM,YAAYI,OAAOV,KAAKE,OAAO;;MAE7C;AAAgB;IAGXF,KAAAgB,WAAYC;MACjB,IAAIC,IAAYlB,KAAKe,mBAAmBE;MACxC,IAAIE,KAAS;MAGb,IAAID,GAAW;QACbC,IAASD;QACTlB,KAAKC,SAASD,KAAKE;;MAGrB,OAAOgB,GAAW;QAChBlB,KAAKE;QACLgB,IAAYlB,KAAKe,mBAAmBE;QACpCE,KAAS;;MAGX,OAAOA;AAAM;IAGRnB,KAAAoB,WAAW,MAAepB,KAAKgB,SAAS;IAExChB,KAAAqB,YAAY;MACjBrB,KAAKE,OAAOF,KAAKM,YAAYC;AAAM;IAG9BP,KAAAsB,SAAUC;MACfvB,KAAKE,OAAOqB;AAAQ;IAGfvB,KAAAiB,QAAQ,CACbH,GACAU,KAAsC,GACtCC,KAAuC;MAEvC,IAAIC,IAAQ;MACZ,IAAIT,IAAQ;MAEZ,IAAuB,mBAAZH,GAAsB;QAE/BG,IADc,IAAIU,OAAOb,GAASW,IAAW,MAAM,KACrCG,KACZ5B,KAAKM,YAAYuB,MAAM7B,KAAKE,MAAMF,KAAKE,OAAOY,EAAQP;QAExDmB,IAAQZ;AACT,aAAM,IAAIA,aAAmBa;QAE5BD,IAAQT,SADRA,IAAQjB,KAAKM,YAAYuB,MAAM7B,KAAKE,MAAMe,MAAMH,eACxCG,EAAQ;;MAGlB,IACW,QAATA,MACoB,mBAAZH,KACLG,aAAiBa,SAIhB9B,KAAKM,YAAYyB,WAAWd,EAAM,IAAIjB,KAAKE,QAC/C;QACA,IAAIsB,GAAS;UACXxB,KAAKC,SAASD,KAAKE;UAEnB,IAAIwB,KAASA,EAAMnB;YACjBP,KAAKE,QAAQwB,EAAMnB;;;QAGvB,OAAOU;;MAIT,QAAO;AAAK;IAGPjB,KAAAgC,SAAUC;MACfjC,KAAKE,QAAQ+B;AAAG;IAGXjC,KAAAkC,SAAS,MAAclC,KAAKE;IAE5BF,KAAAmC,cAAc;MACnB,IAAMlB,IAAQjB,KAAKM,YAAYW,MAAM;MACrC,IAAImB,IAAS;MACb,IAAInB,KAA0B,MAAjBA,EAAMV,QAAc;QAC/B,IAAM8B,IAAcpB,EAAM;QAC1B,IAAIqB,IAAM;QACV,OAAOD,EAAY9B,SAAS+B,GAAK;UAC/B,IAAoC,MAAhCD,EAAYE,WAAWD;YACzBF,KAAU;;YAEVA;;UAEFE;;;MAIJ,OAAOF;AAAM;IAGRpC,KAAAwC,UAAU,MAAcxC,KAAKM,YAAYuB,MAAM7B,KAAKC,QAAQD,KAAKE;IA7ItEF,KAAKM,cAAcP;AACrB;EAMQgB,kBAAAA,CAAmBD;IACzB,IAAM2B,IAAYzC,KAAKM,YAAYI,OAAOV,KAAKE;IAC/C,IAAIgB,KAAY;IAChB,IAAuB,mBAAZJ;MACTI,IAAYuB,MAAc3B;;MAE1BI,IACEJ,aAAmBa,SACfb,EAAQc,KAAKa,KACb3B,EAAQ2B;;IAEhB,OAAOvB;AACT;;;AChCI,SAAUwB,IAAIC;EAClB,OAAO;IAAEA;;AACX;;AAGM,SAAUC,KAAKD,GAAuBE;EAC1C,OAAO;IAAEF;IAAQG,SAAQ;IAAMD;;AACjC;;AAoBM,SAAUE,IAAEC,GAAcC;EAC9B,OAAO;IAAEA;IAAOhC,OAAQS,KAAiBA,EAAMsB,SAASA;;AAC1D;;AAGM,SAAUE,IAAEC,GAAeF;EAC/B,OAAO;IACLA,OAAOA,KAAS;IAChBhC,OAAQS,KACS,kBAAfA,EAAMsB,QAA0BtB,EAAMyB,UAAUA;;AAEtD;;ACnCO,IAAMC,YAAaC,KACjB,QAAPA,KACO,SAAPA,KACO,QAAPA,KACO,SAAPA,KACO,SAAPA,KACO,aAAPA,KACO,QAAPA;;AAKK,IAAMC,IAAW;EAEtBC,MAAM;EAGNC,aAAa;EAGbC,QAAQ;EAGRC,QACE;EAGFC,SAAS;;;AAQJ,IAAMC,IAA4C;EACvDC,UAAU,EAACjB,KAAK;EAChBkB,UAAAA,CAAWpC;IACT,QAAQA,EAAMyB;KACZ,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAOY,EAAAA,KAAKC;;KACd,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;AAEZ;EAEDC,YAAY,EAAC;EACbC,OAAO,EACLC,KAAK,UACLzB,IAAI0B,OAAK,SACT1B,IAAI,wBACJE,KAAK,cACL;EAGFyB,UAAU,EACRF,KAAK,aACLzB,IAAI0B,OAAK,SACT1B,IAAI,wBACJE,KAAK,cACL;EAGF0B,cAAc,EACZH,KAAK,iBACLzB,IAAI0B,OAAK,SACT1B,IAAI,wBACJE,KAAK,cACL;EAGF2B,qBAAqB,EAACrB,IAAE,MAAMN,KAAK,uBAAuBM,IAAE;EAC5DsB,oBAAoB,EAAC,YAAYtB,IAAE,MAAM,QAAQR,IAAI;EACrD+B,UAAU,EAACvB,IAAE,KAAK,aAAakB,OAAK;EACpCM,cAAc,EAACxB,IAAE,MAAM;EACvByB,cAAc,EAACzB,IAAE,MAAMN,KAAK,cAAcM,IAAE;EAC5C0B,WAASA,CAAClD,GAAcmD,MACC,UAAhBnD,EAAMyB,QACT0B,EAAO5D,MAAM,2BAA0B,KACrC,mBACA,mBACF4D,EAAO5D,MAAM,kBAAiB,KAC9B,iBACA;EAGN6D,cAAc,EACZV,OAAK,aACLlB,IAAE,MACFkB,OAAK,cACL1B,IAAI,cACJE,KAAK,cACLF,IAAI;EAGNqC,OAAO,EACLX,OAAK,aACL1B,IAAI,cACJE,KAAK,cACLF,IAAI;EAGNsC,WAAW,EAAC9B,IAAE,MAAMN,KAAK,aAAaM,IAAE;EACxC+B,UAAU,EAACb,OAAK,cAAclB,IAAE,MAAM;EACtCgC,gBAAgB,EAAChC,IAAE,QAAQkB,OAAK,QAAQxB,KAAK;EAC7CuC,gBAAgB,EACdjC,IAAE,QACFR,IAAI,kBACJE,KAAK,cACL;EAGFwC,oBAAoB,EAClBjB,KAAK,aACLzB,IDnIE,SAAU2C,OAAOC,GAAYC;IACjC,IAAMC,IAAYF,EAAKrE;IACvBqE,EAAKrE,QAAQS;MACX,IAAI+D,KAAQ;MACZ,IAAID;QACFC,IAAQD,EAAU9D;;MAEpB,OACE+D,KAEAF,EAAWG,OAAMC,KAAaA,EAAU1E,UAAU0E,EAAU1E,MAAMS;AAAO;IAG7E,OAAO4D;AACT,GCqHQD,CAAOjB,OAAK,QAAQ,EAACD,KAAK,WAC9B,iBACAvB,KAAK,cACL;EAGFgD,eAAe,EAACzB,KAAK,OAAO;EAE5B0B,KAAAA,CAAMnE;IACJ,QAAQA,EAAMsB;KACZ,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,QAAQtB,EAAMyB;OACZ,KAAK;QACH,OAAO;;OACT,KAAK;QACH,OAAO;;OACT,KAAK;QACH,OAAO;;OACT,KAAK;QACH,OAAO;;MAGX,OAAO;;KACT,KAAK;MACH,QAAQzB,EAAMyB;OACZ,KAAK;OACL,KAAK;QACH,OAAO;;MAGX,IAAoB,WAAhBzB,EAAMyB;QACR,OAAO;;MAET,OAAO;;AAEZ;EACD2C,aAAa,EAAC/C,IAAE,UAAU;EAC1BgD,aAAa,EACX;IACE9C,OAAO;IACPhC,OAAQS,KAAgC,aAAfA,EAAMsB;IAC/BgD,MAAAA,CAAOC,GAAcvE;MACnB,IAAIA,EAAMyB,MAAMpB,WAAW;QACzBkE,EAAMC,iBAAiBxE,EAAMyB,MAAMtB,MAAM,GAAGsE,SAAS;;AAEzD;;EAGJC,cAAc,EAACrD,IAAE,QAAQ;EACzBsD,WAAW,EAACtD,IAAE,QAAQ;EACtBuD,WAAW,EAAClC,OAAK;EACjBmC,WAAW,EAACrD,IAAE,MAAMN,KAAK,UAAUM,IAAE;EACrCsD,aAAa,EAACtD,IAAE,MAAMN,KAAK,gBAAgBM,IAAE;EAC7CuD,aAAa,EAACrC,OAAK,cAAclB,IAAE,MAAM;EACzCwD,MAAKhF,KACoB,QAAhBA,EAAMyB,QAAgB,aAAa;EAG5CwD,UAAU,EAACzD,IAAE,MAAM,QAAQA,IAAE,MAAMR,IAAIQ,IAAE;EACzC0D,aAAa,EAAC,aAAalE,IAAIQ,IAAE;EACjC2D,WAAW,EAsIb,SAASC,OAAK7D;IACZ,OAAO;MACLA;MACAhC,OAAQS,KAAgC,WAAfA,EAAMsB;MAC/BgD,MAAAA,CAAOC,GAAcvE;;QACnB,IAAmBqF,UAAfA,IAAAd,EAAMe,mBAASD,MAAAA,SAAAA,IAAAA,EAAEC,WAAW;UAC9Bf,EAAM7B,OAAO1C,EAAMyB;UACnB8C,EAAMe,UAAUA,UAAUF,OAAOpF,EAAMyB;;AAE3C;;AAEJ,GAjJc2D,CAAK;EACjBG,WAAW,EAAC/D,IAAE,KAAK,SAASkB,OAAK,SAAS1B,IAAI;EAC9CwE,cAAc,EACZ/C,KAAK,cACLjB,IAAE,KAAK,SACPkB,OAAK,SACL1B,IAAI,iBACJyB,KAAK,OACLvB,KAAK,qBAAqBM,IAAE;EAE9BiE,cAAc,EACZhD,KAAK,cACLC,OAAK,SACL1B,IAAI,eACJE,KAAK,cACLM,IAAE,MACFN,KAAK,aACLM,IAAE;EAEJkE,YAAY,EAACjD,KAAK,eAAevB,KAAK,aAAaM,IAAE;EACrDmE,mBAAmB,EAACjD,OAAK;EAEzBkD,WAAW,EACTnD,KAAK,WACLvB,KAAK,cACLM,IAAE,MACFN,KAAK,qBACLM,IAAE;EAGJqE,kBAAkB,EAACnD,OAAK,YAAYlB,IAAE,MAAMkB,OAAK;EACjDoD,WAAW,EAACrD,KAAK,WAAWC,OAAK,SAASxB,KAAK;EAC/C6E,eAAe,EACbtD,KAAK,SACLC,OAAK,SACL1B,IAAI,eACJE,KAAK,cACLM,IAAE,MACFN,KAAK,aACLM,IAAE;EAGJwE,UAAU,EACRtD,OAAK,aACL1B,IAAI,iBACJQ,IAAE,MACF,QACAN,KAAK;EAGP+E,cAAc,EAACzE,IAAE,MAAMN,KAAK,kBAAkBM,IAAE;EAChD0E,eAAe,EACbxD,OAAK,cACLlB,IAAE,MACF,QACAR,IAAI,iBACJE,KAAK;EAGPiF,UAAU,EACR1D,KAAK,UACLC,OAAK,SACLxB,KAAK,cACLM,IAAE,MACFN,KAAK,eAAeM,IAAE;EAGxB4E,aAAa,EAAC;EACdC,SAAS,EACP5D,KAAK,SACLC,OAAK,SACLxB,KAAK,cACLM,IAAE,MACFN,KAAK,iBACLM,IAAE;EAGJ8E,cAAc,EAAC5D,OAAK,aAAaxB,KAAK;EACtCqF,UAAU,EACR9D,KAAK,UACLC,OAAK,SACLxB,KAAK,cACLM,IAAE,MACFN,KAAK,kBACLM,IAAE;EAEJgF,WAAW,EAAC/D,KAAK,WAAW;EAC5BgE,mBAAAA,CAAoBzG;IAClB,QAAQA,EAAMyB;KACZ,KAAK;MACH,OAAOY,EAAAA,KAAKqE;;KACd,KAAK;MACH,OAAOrE,EAAAA,KAAKsE;;KACd,KAAK;MACH,OAAOtE,EAAAA,KAAKuE;;KACd,KAAK;MACH,OAAOvE,EAAAA,KAAKwE;;KACd,KAAK;MACH,OAAOxE,EAAAA,KAAKyE;;KACd,KAAK;MACH,OAAOzE,EAAAA,KAAK0E;;KACd,KAAK;MACH,OAAO1E,EAAAA,KAAK2E;;AAEjB;EACD,CAAC3E,EAAIA,KAACqE,mBAAmB,EAAC;EAC1B,CAACrE,EAAIA,KAACsE,wBAAwB,EAAC;EAC/B,CAACtE,EAAIA,KAACuE,wBAAwB,EAAC;EAC/B,CAACvE,EAAIA,KAACwE,2BAA2B,EAAC;EAClC,CAACxE,EAAIA,KAACyE,uBAAuB,EAAC;EAC9B,CAACzE,EAAIA,KAAC0E,sBAAsB,EAAC;EAC7B,CAAC1E,EAAIA,KAAC2E,8BAA8B,EAAC;;;AAIvC,SAASvE,KAAKhB;EACZ,OAAO;IACLF,OAAO;IACPhC,OAAQS,KAAgC,WAAfA,EAAMsB,QAAmBtB,EAAMyB,UAAUA;;AAEtE;;AAGA,SAASiB,OAAKnB;EACZ,OAAO;IACLA;IACAhC,OAAQS,KAAgC,WAAfA,EAAMsB;IAC/BgD,MAAAA,CAAOC,GAAcvE;MACnBuE,EAAM7B,OAAO1C,EAAMyB;AACrB;;AAEJ;;ACpTc,SAAUwF,aACtBC,IAAyB;EACvBC,eAAehE,KAAUA,EAAO7D,SAASoC;EACzC0F,UAAUxF;EACVyF,YAAYnF;EACZoF,cAAc,CAAA;;EAMhB,OAAO;IACLC,UAAAA;MACE,IAAMC,IAAe;QACnBC,OAAO;QACPC,MAAM;QACNhF,MAAM;QACNpB,MAAM;QACN8D,MAAM;QACNxB,MAAM;QACN+D,iBAAgB;QAChBrC,WAAW;;MAGbsC,SAASV,EAAQG,YAAYG,GAAcnF,EAAIA,KAACwF;MAChD,OAAOL;AACR;IACDxH,OAAKA,CAACmD,GAAyBoB,MAMnC,SAASuD,SACP3E,GACAoB,GACA2C;;MAEA,IAAI3C,EAAMC,eAAe;QAEvB,IAAIrB,EAAO5D,MAAM,UAAU;UACzBgF,EAAMC,iBAAgB;UACtB,OAAO;;QAETrB,EAAOxD;QACP,OAAO;;MAGT,KAAMyH,UAAEA,GAAQC,YAAEA,GAAUF,eAAEA,GAAaG,cAAEA,KAAiBJ;MAE9D,IAAI3C,EAAMX,QAA8B,MAAtBW,EAAMX,KAAK/E;QAC3BkJ,QAAQxD;aACH,IAAIA,EAAMyD,cAAc;QAC7BzD,EAAMyD,gBAAe;QACrBC,YAAY1D,IAAO;;MAIrB,IAAIpB,EAAOrE,OAAO;QAChB,IAAMoJ,KAAUZ,iBAAY,IAAZA,EAAcY,YAAW;QACzC3D,EAAM4D,cAAcC,KAAKC,MAAMlF,EAAO1C,gBAAgByH;;MAIxD,IAAIf,EAAchE;QAChB,OAAO;;MAIT,IAAMnD,IAuOR,SAASsI,IACPlB,GACAjE;QAEA,IAAMoF,IAAQC,OAAOC,KAAKrB;QAC1B,KAAK,IAAIsB,IAAI,GAAGA,IAAIH,EAAM1J,QAAQ6J,KAAK;UAGrC,IAAMnJ,IAAQ4D,EAAO5D,MAAM6H,EAASmB,EAAMG;UAC1C,IAAInJ,KAASA,aAAiBa;YAC5B,OAAO;cAAEkB,MAAMiH,EAAMG;cAAIjH,OAAOlC,EAAM;;;;AAG5C,OApPgB+I,CAAIlB,GAAUjE;MAG5B,KAAKnD,GAAO;QAEV,KADyBmD,EAAO5D,MAAM;UAIpC4D,EAAO5D,MAAM;;QAEfqI,SAASe,GAAmBpE,GAAO;QACnC,OAAO;;MAIT,IAAmB,cAAfvE,EAAMsB,MAAoB;QAC5BsG,SAASe,GAAmBpE,GAAO;QACnC,OAAO;;MAIT,IAAMqE,IAAcC,OAAO,CAAE,GAAEtE;MAG/B,IAAmB,kBAAfvE,EAAMsB;QACR,IAAI,SAASpB,KAAKF,EAAMyB;UACtB,SAA0BqH,MAAtBvE,EAAM4D;YAER5D,EAAMwE,UAAUxE,EAAMwE,UAAU,IAAIC,OAAOzE,EAAM4D,cAAc;;eAE5D,IAAI,UAAUjI,KAAKF,EAAMyB,QAAQ;UAItC,IAAMsH,IAAUxE,EAAMwE,UAAUxE,EAAMwE,UAAU,IAAI5I,MAAM,IAAI;UAG9D,IACEoE,EAAM4D,eACNY,EAAOlK,SAAS,KAChBkK,EAAOE,IAAI,KAAM1E,EAAM4D;YAEvB5D,EAAM4D,cAAcY,EAAOE,IAAI;;;;MAKrC,OAAO1E,EAAMX,MAAM;QAGjB,IAAIsF,IACoB,qBAAf3E,EAAMX,OACM,MAAfW,EAAMmD,OACJnD,EAAMX,KAAK5D,GAAOmD,KAClB,OACFoB,EAAMX,KAAKW,EAAMmD;QAGvB,IAAInD,EAAMoD;UACRuB,IAAWA,iBAAAA,IAAAA,EAAU/H;;QAGvB,IAAI+H,GAAU;UAEZ,IAAIA,EAASjI;YACXiI,IAAWA,EAASjI;;UAItB,IAAwB,mBAAbiI,GAAuB;YAChCtB,SAASP,GAAY9C,GAAO2E;YAC5B;;UAIF,IAAkB7D,UAAdA,IAAA6D,EAAS3J,eAAK8F,MAAAA,SAAAA,IAAAA,EAAA8D,KAAAD,GAAGlJ,IAAQ;YAC3B,IAAIkJ,EAAS5E;cACX4E,EAAS5E,OAAOC,GAAOvE;;YAMzB,IAAmB,kBAAfA,EAAMsB;cACR2G,YAAY1D,IAAO;;cAEnBA,EAAMyD,gBAAe;;YAGvB,OAAOkB,EAAS3H;;;QAGpB6H,aAAa7E;;MAIfsE,OAAOtE,GAAOqE;MACdhB,SAASe,GAAmBpE,GAAO;MACnC,OAAO;AACT,KA5IauD,CAAS3E,GAAQoB,GAAO2C;;AAGrC;;AA4IA,SAAS2B,OAAOQ,GAAYC;EAC1B,IAAMb,IAAOD,OAAOC,KAAKa;EACzB,KAAK,IAAIZ,IAAI,GAAGA,IAAID,EAAK5J,QAAQ6J;IAG/BW,EAAGZ,EAAKC,MAAMY,EAAKb,EAAKC;;EAE1B,OAAOW;AACT;;AAGA,IAAMV,IAAoB;EACxBY,SAAS;EACTtH,SAAS;;;AAIX,SAAS2F,SACP4B,GACAjF,GACAkF;EAEA,KAAKD,EAAMC;IACT,MAAM,IAAIC,UAAU,mBAAmBD;;EAEzClF,EAAMe,YAASkD,OAAAK,OAAA,CAAA,GAAQtE;EACvBA,EAAMjD,OAAOmI;EACblF,EAAM7B,OAAO;EACb6B,EAAMa,OAAO;EACbb,EAAMX,OAAO4F,EAAMC;EACnBlF,EAAMmD,OAAO;EACbnD,EAAMoD,kBAAiB;AACzB;;AAGA,SAASI,QAAQxD;EAEf,KAAKA,EAAMe;IACT;;EAEFf,EAAMjD,OAAOiD,EAAMe,UAAUhE;EAC7BiD,EAAM7B,OAAO6B,EAAMe,UAAU5C;EAC7B6B,EAAMa,OAAOb,EAAMe,UAAUF;EAC7Bb,EAAMX,OAAOW,EAAMe,UAAU1B;EAC7BW,EAAMmD,OAAOnD,EAAMe,UAAUoC;EAC7BnD,EAAMoD,iBAAiBpD,EAAMe,UAAUqC;EACvCpD,EAAMe,YAAYf,EAAMe,UAAUA;AACpC;;AAGA,SAAS2C,YAAY1D,GAAcoF;;EAGjC,IAAIvI,OAAOmD,MAAUA,EAAMX,MAAM;IAG/B,IAAM8D,IAAOnD,EAAMX,KAAKW,EAAMmD;IAC9B,IAAIA,EAAKvG,WAAW;MAClB,KAAMA,WAAEA,KAAcuG;MACtBnD,EAAMoD,kBAAkBpD,EAAMoD;MAE9B,KAAKpD,EAAMoD,kBAAkBxG,EAAUF;QACrC;;;IAIJ,IAAI0I;MACF;;;EAMJpF,EAAMoD,kBAAiB;EACvBpD,EAAMmD;EAGN,OACEnD,EAAMX,UACJxD,MAAMwJ,QAAQrF,EAAMX,SAASW,EAAMmD,OAAOnD,EAAMX,KAAK/E,SACvD;IACAkJ,QAAQxD;IAER,IAAIA,EAAMX;MAER,IAAIxC,OAAOmD;QAGT,IAAcc,UAAVA,IAAAd,EAAMX,cAAIyB,MAAAA,SAAAA,IAAAA,EAAGd,EAAMmD,MAAMvG;UAC3BoD,EAAMoD,kBAAkBpD,EAAMoD;;aAE3B;QACLpD,EAAMoD,kBAAiB;QACvBpD,EAAMmD;;;;AAId;;AAEA,SAAStG,OAAOmD;EACd,IAAMmD,IACJtH,MAAMwJ,QAAQrF,EAAMX,SACc,mBAA3BW,EAAMX,KAAKW,EAAMmD,SACvBnD,EAAMX,KAAKW,EAAMmD;EAEpB,OAAOA,KAAQA,EAAKtG;AACtB;;AAGA,SAASgI,aAAa7E;EAGpB,OACEA,EAAMX,UAGJxD,MAAMwJ,QAAQrF,EAAMX,UAASW,EAAMX,KAAKW,EAAMmD,MAAMzG;IAEtD8G,QAAQxD;;EAKV,IAAIA,EAAMX;IACRqE,YAAY1D,IAAO;;AAEvB;;;;;;ACvUM,MAAOsF;EAGXzL,WAAAA,CAAY0L,GAAkBC;IAa9BzL,KAAA0L,mBAAoBnK;MAClB,IAAIvB,KAAKwL,MAAMG,SAASpK,EAASoK;QAC/B,OAAO3L,KAAKwL,MAAM/I,aAAalB,EAASkB;;MAE1C,IAAIzC,KAAKyL,IAAIE,SAASpK,EAASoK;QAC7B,OAAO3L,KAAKyL,IAAIhJ,aAAalB,EAASkB;;MAExC,OAAOzC,KAAKwL,MAAMG,QAAQpK,EAASoK,QAAQ3L,KAAKyL,IAAIE,QAAQpK,EAASoK;AAAI;IAnBzE3L,KAAKwL,QAAQA;IACbxL,KAAKyL,MAAMA;AACb;EAEAG,QAAAA,CAASD,GAAclJ;IACrBzC,KAAKwL,QAAQ,IAAIK,SAASF,GAAMlJ;AAClC;EAEAqJ,MAAAA,CAAOH,GAAclJ;IACnBzC,KAAKyL,MAAM,IAAII,SAASF,GAAMlJ;AAChC;;;AAaI,MAAOoJ;EAGX/L,WAAAA,CAAY6L,GAAclJ;IAa1BzC,KAAA+L,oBAAqBxK,KACnBvB,KAAK2L,OAAOpK,EAASoK,QACpB3L,KAAK2L,SAASpK,EAASoK,QAAQ3L,KAAKyC,aAAalB,EAASkB;IAd3DzC,KAAK2L,OAAOA;IACZ3L,KAAKyC,YAAYA;AACnB;EAEAuJ,OAAAA,CAAQL;IACN3L,KAAK2L,OAAOA;AACd;EAEAM,YAAAA,CAAaxJ;IACXzC,KAAKyC,YAAYA;AACnB;;;AChBF,IAAMyJ,IAAoB,EACxBC,EAAwBA,0BACxBC,EAAwBA,0BACxBC,EAAmBA,qBACnBC,EAAwBA,0BACxBC,EAA8BA,gCAC9BC,EAAwBA,0BACxBC,EAAkBA,oBAClBC,EAAmBA,qBACnBC,EAA+BA,iCAC/BC,EAA0BA,4BAE1BC,EAAuBA,yBACvBC,EAAyBA;;ACLpB,IAAMC,IAAsB;EACjC,CAXO,UAWW;EAClB,CAXS,YAWW;EACpB,CAXa,gBAWW;EACxB,CAXM,SAWW;;;AAGnB,IAAMC,YAAYA,CAACC,GAAgBC;EACjC,KAAKD;IACH,MAAM,IAAIE,MAAMD;;;;AAId,SAAUE,eACdC,GACAC,IAA2C,MAC3CC,GACAC,GACAC;;EAEA,IAAIC,IAAM;EACV,IAAIC,IAAY;EAChB,IAAIF;IACFE,IAC+B,mBAAtBF,IACHA,IACAA,EAAkBG,QAChB,CAACC,GAAKC,MAASD,IAAME,EAAKA,MAACD,KAAQ,SACnC;;EAGV,IAAME,IAAgBL,IAAY,GAAGN,QAAYM,MAAcN;EAE/D;IACEK,IAAMO,EAAAA,MAAMD;AACb,IAAC,OAAOE;IACP,IAAIA,aAAiBC,EAAAA,cAAc;MACjC,IAAMC,IA0FN,SAAUC,SAASC,GAA0BC;QACjD,IAAMC,IAAS7F;QACf,IAAM1C,IAAQuI,EAAOvF;QACrB,IAAMwF,IAAQF,EAAUG,MAAM;QAE9B1B,UACEyB,EAAMlO,UAAU+N,EAAS3C,MACzB;QAGF,IAAI9G,IAAS;QAEb,KAAK,IAAIuF,IAAI,GAAGA,IAAIkE,EAAS3C,MAAMvB,KAAK;UACtCvF,IAAS,IAAIhF,gBAAgB4O,EAAMrE;UACnC,QAAQvF,EAAOxE,OAAO;YAEpB,IAAc,kBADAmO,EAAO9M,MAAMmD,GAAQoB;cAEjC;;;;QAKN+G,UAAUnI,GAAQ;QAClB,IAAM8G,IAAO2C,EAAS3C,OAAO;QAG7B,IAAMH,IAAQ3G,EAAO1E;QAGrB,IAAMsL,IAAM5G,EAAOzE;QACnB,OAAO,IAAImL,MAAM,IAAIM,SAASF,GAAMH,IAAQ,IAAIK,SAASF,GAAMF;AACjE,OAzHoB4C,CACQM,UAApBA,IAAe5H,UAAfA,IAAAmH,EAAMU,mBAAS7H,MAAAA,SAAAA,IAAAA,EAAG,YAAE4H,MAAAA,IAAAA,IAAI;QAAEhD,MAAM;QAAGzJ,QAAQ;SAC3C8L;MAGF,OAAO,EACL;QACEa,UAAU9B,EAAoBI;QAC9BD,SAASgB,EAAMhB;QACf4B,QAAQ;QACRV;;;IAIN,MAAMF;;EAGR,OAGI,SAAUa,cACdrB,GACAJ,IAA2C,MAC3CC,GACAC;IAGA,KAAKF;MACH,OAAO;;IAGT,IAAM0B,IDxDF,SAAUC,wBACd3B,GACAI,GACAH,GACAC,GACA0B;MAEA,IAAMhE,IAAQiE,EAAAA,eAAeC,QAAO9J;QAIlC,IAAIA,MAAS+J,EAAAA,yBAAyB/J,MAASgK;UAC7C,QAAO;;QAET,IAAI9B,KAAqBlI,MAASiK;UAChC,QAAO;;QAET,QAAO;AAAI;MAGb,IAAIhC;QACFzL,MAAM0N,UAAUC,KAAKC,MAAMxE,GAAOqC;;MAEpC,IAAI2B;QACFpN,MAAM0N,UAAUC,KAAKC,MAAMxE,GAAOgB;;MAGpC,OADeyD,EAAQA,SAACrC,GAAQI,GAAKxC,GACvBkE,QAAOlB;QACnB,IAAIA,EAAMhB,QAAQ0C,SAAS,wBAAwB1B,EAAM2B,OAAO;UAC9D,IAAM/B,IAAOI,EAAM2B,MAAM;UACzB,IAAI/B,KAAQA,EAAK9K,SAASe,EAAAA,KAAK+L,WAAW;YACxC,IAAM1L,IAAO0J,EAAK1J,KAAKjB;YACvB,IAAa,gBAATiB,KAAiC,0BAATA;cAC1B,QAAO;;;;QAIb,QAAO;AAAI;AAEf,KCiBqC6K,CACjC3B,GACAI,GACAH,GACAC,GACAuC,SAAQ7B,KACR8B,YAAY9B,GAAOnB,EAAoBI,OAAO;IAIhD,IAAM8C,IAAgCN,EAAAA,SAASrC,GAAQI,GAAK,EAC1DwC,EAAsBA,0BACrBH,SAAQ7B,KACT8B,YAAY9B,GAAOnB,EAAoBoD,SAAS;IAElD,OAAOnB,EAA2BtE,OAAOuF;AAC3C,GA9BSlB,CAAcrB,GAAKJ,GAAQC,GAAaC;AACjD;;AA+BA,SAASwC,YACP9B,GACAW,GACA/H;EAEA,KAAKoH,EAAM2B;IACT,OAAO;;EAET,IAAMO,IAAiC;EACvC,KAAK,KAAOhG,GAAG0D,MAASI,EAAM2B,MAAMQ,WAAW;IAC7C,IAAMC,IACU,eAAdxC,EAAK9K,QAAuB,UAAU8K,UAAsBtD,MAAdsD,EAAK1J,OAC/C0J,EAAK1J,OACL,cAAc0J,UAA0BtD,MAAlBsD,EAAKyC,WAC3BzC,EAAKyC,WACLzC;IACN,IAAIwC,GAAe;MACjBtD,UACEkB,EAAMU,WACN;MAKF,IAAM4B,IAAMtC,EAAMU,UAAUxE;MAC5B,IAAMqG,IAAeC,YAAYJ;MACjC,IAAM7E,IAAM+E,EAAItO,UAAUuO,EAAahF,MAAMgF,EAAajF;MAC1D4E,EAAiBX,KAAK;QACpBX,QAAQ,YAAYhI;QACpBoG,SAASgB,EAAMhB;QACf2B;QACAT,OAAO,IAAI7C,MACT,IAAIM,SAAS2E,EAAI7E,OAAO,GAAG6E,EAAItO,SAAS,IACxC,IAAI2J,SAAS2E,EAAI7E,OAAO,GAAGF;;;;EAKnC,OAAO2E;AACT;;AA0CA,SAASM,YAAY5C;EAEnB,IAAMQ,IADiBR,EACS0C;EAChCxD,UAAUsB,GAAU;EAGpB,OAAOA;AACT;;AC3NA,IAAAqC,IAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVO,IAAMC,IAAY,IAAIC,IAAI,EAAC,OAAO;;ACIlC,IAAMC,SAAUhD,KACrBlO,QAAAA,GAAGmR,iBAAiBjD,MACM,MAA1BA,EAAKkD,UAAUzQ,WACdX,WAAGqR,qBAAqBnD,EAAKoD,eAC5BtR,QAAAA,GAAGuR,gBAAgBrD,EAAKoD,iBACzBpD,EAAKoD,WAAWE,kBAChBtD,EAAKoD,WAAWG,WAAW9Q;;AAGvB,IAAM+Q,8BACXxD,KAEAlO,QAAEA,GAAC2R,aAAazD,MAAS8C,EAAUY,IAAI1D,EAAK2D;;AAGvC,IAAMC,wBAAwBA,CACnC5D,GACA6D;EAEA,KAAK/R,QAAAA,GAAGgS,yBAAyB9D;IAAO,QAAO;;EAC/C,IAAMhH,IAAO6K,GAASE,kBAAkB/D;EAGxC,OACU,QAARhH,KAC8B,QAA9BA,EAAKgL,YAAY,aACgB,QAAjChL,EAAKgL,YAAY;AAAoB;;AAKlC,IAAMC,oBAAoBA,CAC/BjE,GACA6D;EAIA,KAAK/R,QAAEA,GAACmR,iBAAiBjD;IACvB,QAAO;SACF,IAAIA,EAAKkD,UAAUzQ,SAAS,KAAKuN,EAAKkD,UAAUzQ,SAAS;IAC9D,QAAO;SACF,KAAKX,QAAAA,GAAGoS,oBAAoBlE,EAAKkD,UAAU;IAChD,QAAO;;EAET,OAAOW,IAAUD,sBAAsB5D,EAAKoD,YAAYS,MAAW;AAAK;;AAInE,IAAMM,sBAAsBA,CACjCnE,GACA6D;EAEA,KAAK7D;IACH,QAAO;SACF,KAAKlO,QAAAA,GAAGmR,iBAAiBjD;IAC9B,QAAO;SACF,KAAKlO,QAAEA,GAACsS,2BAA2BpE,EAAKoD;IAC7C,QAAO;SACF,KACJtR,QAAAA,GAAG2R,aAAazD,EAAKoD,WAAW9M,SACI,gBAArC0J,EAAKoD,WAAW9M,KAAKqN;IAErB,QAAO;SACF,IAAIH,4BAA4BxD,EAAKoD,WAAWA;IACrD,QAAO;;IAEP,OAAOQ,sBAAsB5D,EAAKoD,WAAWA,YAAYS;;AAC3D;;IASWQ,gBAAgBA,CAC3BrE,GACA6D,MAGE/R,QAAEA,GAACmR,iBAAiBjD,MACpBA,EAAKkD,UAAUzQ,UAAU,KACzBuN,EAAKkD,UAAUzQ,UAAU,MACxB+Q,4BAA4BxD,EAAKoD,eAChCa,kBAAkBjE,GAAM6D;;IAKjBS,eACXtE,KAEAlO,QAAAA,GAAGyS,2BAA2BvE,MAASwD,4BAA4BxD,EAAKwE;;AAGnE,IAAMC,gBAAgBA,CAC3BzE,GACA0E,GACAP,KAAsB;EAEtB,KAAKO;IAAa,OAAO;;EACzB,IAAM1L,IAAO0L,EAAYX,kBAGvBI,IAAsBnE,EAAK2E,WAAW,GAAGA,WAAW,KAAK3E,EAAKoD;EAEhE,IAAIpK,GAAM;IACR,IAAM4L,IAAkB5L,EAAKgL,YAAY;IACzC,IAAIY,GAAiB;MACnB,IAAMC,IAAQH,EAAYI,gBAAgBF;MAC1C,IAAIC,EAAME,yBAAyB;QACjC,IAAMC,IAAQH,EAAMI,MAAMC,MAAKC,KAAKA,EAAEC;QACtC,OAAOJ,KAASA,EAAMI,oBAAoBJ,EAAM3P,QAAQ;AAC1D,aAAO,IAAIwP,EAAMO;QACf,OAAOP,EAAMxP;;AAEjB;AACF;EACA,OAAO;AAAI;;AC5GN,SAASgQ,gBACdrF,GACAsF,GACAC;EAEA,IAAIzT,QAAEA,GAACoS,oBAAoBlE;IACzB,OAAO;MAAEwF,cAAcxF,EAAKyF,UAAU1R,MAAM,IAAI;MAAI2R,eAAe;;;EAGrE,IAAIC,IAAe3F,EAAK4F,SAASH,UAAU1R,MAAM,IAAI;EACrD,IACEjC,QAAEA,GAAC+T,gCAAgC7F,EAAK4F,aACD,MAAvC5F,EAAK4F,SAASE,cAAcrT;IAE5B,OAAO;MAAE+S,cAAcG;MAAcD,eAAe;;;EAGtD,IAAIK,IAAkB;EACtB,IAAML,IAAgB1F,EAAK4F,SAASE,cACjCE,KAAIC;IACH,IAAInU,WAAG2R,aAAawC,EAAK7C,aAAa;MACpC,IAAM8C,IAAcX,EAAKY,gBAAgBC,wBACvCd,GACAW,EAAK7C,WAAWiD;MAGlB,IAAMC,IAAMJ,KAAeA,EAAY;MACvC,KAAKI;QAAK;;MAEV,IAAMC,IAAMC,UAAUjB,GAAMe,EAAIG;MAChC,KAAKF;QAAK;;MAEV,IAAMvG,IAAO0G,SAASH,GAAKD,EAAIK,SAASjJ;MACxC,KAAKsC,MAASA,EAAK4G;QAAQ;;MAE3B,IAAMA,IAAS5G,EAAK4G;MACpB,IAAI9U,QAAEA,GAAC+U,sBAAsBD,IAAS;QACpC,IAAME,IAAiBb,EAAK7C,WAAWO;QAEvC,IAAMoD,IAAgBd,EAAK7C,WAAWiD,aAAa;QACnD,IAAMW,IAAgB;UACpBtJ,OAAOqJ;UAEPtU,QAAQwT,EAAK7C,WAAWzF,MAAMoJ,IAAgB;;QAEhD,IACEH,EAAOK,eACPnV,QAAAA,GAAGyS,2BAA2BqC,EAAOK,cACrC;UACA,IAAMC,IAAO7B,gBACXuB,EAAOK,aACPX,EAAIG,UACJlB;UAEFI,IAAeA,EAAawB,QAC1B,OAAOlB,EAAK7C,WAAWO,cAAc,KACrCuD,EAAK1B;UAGP,IAAM4B,IAAc;YAClBzG,OAAOuG,EAAK1B,aAAa5E,MAAM,MAAMnO;YACrC4U,YAAYP;YACZQ,UAAUN;YACVO,KAAK;cACH7J,OAAOsJ,EAActJ,QAAQqI;cAC7BtT,QAAQyU,EAAK1B,aAAa/S;;;UAG9BsT,KAAmBmB,EAAK1B,aAAa/S,SAASuU,EAAcvU;UAC5D,OAAO2U;AACR,eAAM,IACLR,EAAOK,eACPnV,QAAEA,GAAC0V,eAAeZ,EAAOK,gBACzBnV,QAAEA,GAACyS,2BAA2BqC,EAAOK,YAAY7D,aACjD;UACA,IAAM8D,IAAO7B,gBACXuB,EAAOK,YAAY7D,YACnBkD,EAAIG,UACJlB;UAEFI,IAAeA,EAAawB,QAC1B,OAAOlB,EAAK7C,WAAWO,cAAc,KACrCuD,EAAK1B;UAEP,IAAM4B,IAAc;YAClBzG,OAAOuG,EAAK1B,aAAa5E,MAAM,MAAMnO;YACrC4U,YAAYP;YACZQ,UAAUN;YACVO,KAAK;cACH7J,OAAOsJ,EAActJ,QAAQqI;cAC7BtT,QAAQyU,EAAK1B,aAAa/S;;;UAG9BsT,KAAmBmB,EAAK1B,aAAa/S,SAASuU,EAAcvU;UAC5D,OAAO2U;AACT,eAAO,IACLR,EAAOK,eACPnV,QAAEA,GAAC0V,eAAeZ,EAAOK,gBACzBnV,QAAAA,GAAG0V,eAAeZ,EAAOK,YAAY7D,eACrCtR,WAAG2V,0BACDb,EAAOK,YAAY7D,WAAWA,aAEhC;UAIA,IAAMsE,IAAmBzH,MAHP0H,KAAKxH,MACrByG,EAAOK,YAAY7D,WAAWA,WAAWqC;UAG3CE,IAAeA,EAAawB,QAC1B,OAAOlB,EAAK7C,WAAWO,cAAc,KACrC+D;UAEF,IAAMN,IAAc;YAClBzG,OAAO+G,EAAiB9G,MAAM,MAAMnO;YACpC4U,YAAYP;YACZQ,UAAUN;YACVO,KAAK;cACH7J,OAAOsJ,EAActJ,QAAQqI;cAC7BtT,QAAQiV,EAAiBjV;;;UAG7BsT,KAAmB2B,EAAiBjV,SAASuU,EAAcvU;UAC3D,OAAO2U;AACT;QAEA;AACF;AACF;IAEA;AAAgB,MAEjB9F,OAAOsG;EAEV,OAAO;IAAEpC,cAAcG;IAAcD;;AACvC;;AAEO,IAAMmC,2BACX7H;EAEA,KAAKA;IAAM;;EAEX,OAAOlO,QAAAA,GAAG0V,eAAexH;IAAOA,IAAOA,EAAKoD;;EAC5C,KAAKtR,QAAAA,GAAGgW,yBAAyB9H;IAAO;;EAExC,IAAIA,EAAK+H,SAASnQ,MAAM9F,QAAAA,GAAG2R;IAAe,OAAOzD,EAAK+H;;EACtD,IAAMC,IAA+B;EACrC,KAAK,IAAIC,KAAWjI,EAAK+H,UAAU;IACjC,OAAOjW,QAAAA,GAAGsS,2BAA2B6D;MAAUA,IAAUA,EAAQ3R;;IACjE,IAAIxE,QAAAA,GAAG2R,aAAawE;MAAUD,EAAYrG,KAAKsG;;AACjD;EACA,OAAOD;AAAW;;AC7Jb,SAASxB,UAAUjB,GAAkCD;EAC1D,IAAM4C,IAAU3C,EAAKY,gBAAgBgC;EACrC,KAAKD;IAAS;;EAEd,IAAMlH,IAASkH,EAAQE,cAAc9C;EACrC,KAAKtE;IAAQ;;EAEb,OAAOA;AACT;;AAEO,SAAS0F,SACd2B,GACA5U;EAOA,OALA,SAASyR,KAAKlF;IACZ,IAAIvM,KAAYuM,EAAKqG,cAAc5S,IAAWuM,EAAKsI;MACjD,OAAOxW,QAAEA,GAACyW,aAAavI,GAAMkF,SAASlF;;AAE1C,GACOkF,CAAKmD;AACd;;AAsEA,SAASG,eACPP,GACA1C,GACA1B;EAEA,IAAMhE,IAAsC;EAC5C,IAAMkI,IAA4B,EAACE;EACnC,IAAMQ,IAAO,IAAIC;EAEjB,IAAMC,iBAAkBV;IACtB,IAAIQ,EAAK/E,IAAIuE;MAAU;;IACvBQ,EAAKG,IAAIX;IAET,IAAMjI,IA3DV,SAAS6I,+BACPC,GACAvD,GACA1B;MAEA,IAAIkF;MACJ,IAAId,IAA+Ba;MAEnC,OAAOhX,QAAEA,GAAC2R,aAAawE,MAAYA,MAAYc,GAAa;QAC1DA,IAAcd;QAEd,IAAM/B,IAAcX,EAAKY,gBAAgBC,wBACvC6B,EAAQG,gBAAgB3B,UACxBwB,EAAQ5B;QAGV,IAAM2C,IAAW9C,KAAeA,EAAY;QAC5C,IAAM+C,IAAiBD,KAAYxC,UAAUjB,GAAMyD,EAASvC;QAC5D,KAAKuC,MAAaC;UAAgB,OAAO;;QAGzC,MADAhB,IAAUvB,SAASuC,GAAgBD,EAASrC,SAASjJ;UACvC,OAAO;;QAErB,OAAO5L,QAAAA,GAAGsS,2BAA2B6D,EAAQrB;UAC3CqB,IAAUA,EAAQrB;;QAEpB,IACE9U,QAAEA,GAAC+U,sBAAsBoB,EAAQrB,WACjCqB,EAAQrB,OAAOK,eACfnV,QAAEA,GAACmR,iBAAiBgF,EAAQrB,OAAOK;UAEnCgB,IAAUA,EAAQrB,OAAOK;eACpB,IAAInV,QAAAA,GAAGoX,qBAAqBjB,EAAQrB;UACzCqB,IAAUA,EAAQrB,OAAOK;eACpB,IAAInV,QAAAA,GAAGqX,mBAAmBlB,EAAQrB;UACvCqB,IAAUnW,QAAAA,GAAGsS,2BAA2B6D,EAAQrB,OAAOwC,SACnDnB,EAAQrB,OAAOwC,MAAM9S,OACrB2R,EAAQrB,OAAOwC;;AAGvB;MAGA,OAAOC,cAAqBpB,GAASpE,KAAWoE,IAAU;AAC5D,KAeiBY,CAA+BZ,GAAS1C,GAAM1B;IAC3D,KAAK7D;MAAM;;IAEX,IAAMsJ,IAAezB,yBAAyB7H,EAAKkD,UAAU;IAC7D,IAAIoG;MAAcvB,EAASpG,QAAQ2H;;IAEnC;MAEiBnJ,EAAKA,MADPH,EAAKkD,UAAU,GACFuC,UAAU1R,MAAM,IAAI,IAAI;QAAEwV,aAAY;SACzDrD,YAAYsD,SAAQC;QACzB,IAAwB,yBAApBA,EAAWvU;UACb2K,EAAU8B,KAAK8H;;AACjB;AAEH,MAAC,OAAOC,IACP;AAAA;EAIJ,IAAIC;EACJ,YAA4CjN,OAApCiN,IAAc5B,EAAS6B;IAC7BjB,eAAegB;;EACjB,OAAO9J;AACT;;AAEO,SAASgK,oBACdC,GACAC,GACAxE;EAEA,IAAMb,IAAca,EAAKY,gBAAgBgC,cAAc6B;EACvDF,EAAe/B,SAASyB,SAAQvB;IAC9B,IAAInW,QAAEA,GAAC2R,aAAawE;MAClB8B,EAAIpI,QAAQ6G,eAAeP,GAAS1C,GAAMb;WACrC,IAAI5S,QAAEA,GAACsS,2BAA2B6D,IAAU;MACjD,IAAIgC,IAAKhC;MACT,OAAOnW,QAAAA,GAAGsS,2BAA2B6F,EAAG7G;QAAa6G,IAAKA,EAAG7G;;MAC7D,IAAItR,WAAG2R,aAAawG,EAAG3T;QACrByT,EAAIpI,QAAQ6G,eAAeyB,EAAG3T,MAAMiP,GAAMb;;AAE9C;AAAA;EAGF,OAAOqF;AACT;;AAEO,SAASG,uBACd7B,GACA9C,GACA4E,KAAiC;EAQjC,IAAMzF,IAAca,EAAKY,gBAAgBgC,cAAc6B;EACvD,IAAMI,IAGD;EACL,IAAIvK,IAA2C;EAC/C,IAAIwK,IAA0BF,KAAwB,KAAQ;GAE9D,SAASjF,KAAKlF;IACZ,KAAKlO,QAAEA,GAACmR,iBAAiBjD,MAASqJ,OAAcrJ;MAC9C,OAAOlO,WAAGyW,aAAavI,GAAMkF;;IAK/B,KAAKmE,cAAqBrJ,GAAM0E;MAC9B,OAAO5S,WAAGyW,aAAavI,GAAMkF;;IAG/B,IAAM5O,IAAO+S,cAAqBrJ,GAAM0E;IACxC,IAAMwC,IAAOlH,EAAKkD,UAAU;IAC5B,IAAMoG,IAAezB,yBAAyB7H,EAAKkD,UAAU;IAE7D,KAAKmH,MAA4Bf,GAAc;MAC7Ce,KAA0B;MAC1BxK,EAAU8B,QAAQ2I,gBAAgBjC,EAAW5B,UAAUzG,GAAMuF;AAC9D,WAAM,IAAI+D;MACT,KAAK,IAAMjC,KAAciC;QACvBzJ,EAAU8B,QAAQ6G,eAAenB,GAAY9B,GAAMb;;;IAIvD,IAAIwC,KAAQpV,QAAAA,GAAGoS,oBAAoBgD;MACjCkD,EAAOzI,KAAK;QAAE3B,MAAMkH;QAAM1H,QAAQlJ;;;AAEtC,GACA4O,CAAKmD;EACL,OAAO;IAAEtG,OAAOqI;IAAQvK;;AAC1B;;AAUO,SAAS0K,gCACdlC,GACA9C;EAEA,IAAM6E,IAEF;EACJ,IAAM1F,IAAca,GAAMY,gBAAgBgC,cAAc6B;GACxD,SAAS9E,KAAKlF;IACZ,KAAKlO,QAAEA,GAACmR,iBAAiBjD,MAASqJ,OAAcrJ;MAC9C,OAAOlO,WAAGyW,aAAavI,GAAMkF;;IAG/B,KAAKmE,oBAA2BrJ,GAAM0E;MACpC;WACK,IAAIa,GAAM;MACf,IAAMjP,IAAO+S,cAAqBrJ,GAAM0E,IAAa;MACrD0F,EAAOzI,KAAK;QAAE3B;QAAMR,QAAQlJ;;AAC9B;MACE8T,EAAOzI,KAAK3B;;AAEhB,GACAkF,CAAKmD;EACL,OAAO+B;AACT;;AAEO,SAASE,gBACd7D,GACAzG,GACAuF;EAEA,IAAI1F,IAA2C;EAE/C,IAAM6E,IAAca,EAAKY,gBAAgBgC,cAAc6B;EACvD,KAAKlY,QAAEA,GAACmR,iBAAiBjD;IACvB,OAAOH;;EAGT,IAAMyJ,IAAezB,yBAAyB7H,EAAKkD,UAAU;EAC7D,IAAIoG,GAAc;IAChB,IAAM5E,IAAca,EAAKY,gBAAgBgC,cAAc6B;IACvD,KAAK,IAAM3C,KAAciC;MACvBzJ,EAAU8B,QAAQ6G,eAAenB,GAAY9B,GAAMb;;IAErD,OAAO7E;AACR,SAAM,IAAIwJ,kBAAyBrJ,GAAM0E;IACxC,OAAO7E;;EAGT,IAAMqG,IAAcX,EAAKY,gBAAgBC,wBACvCK,GACAzG,EAAKoD,WAAWiD;EAElB,KAAKH,MAAgBA,EAAYzT;IAAQ,OAAOoN;;EAEhD,IAAMyG,IAAMJ,EAAY;EACxB,KAAKI;IAAK,OAAOzG;;EACjB,IAAM0G,IAAMC,UAAUjB,GAAMe,EAAIG;EAChC,KAAKF;IAAK,OAAO1G;;EAEjB/N,WAAGyW,aAAahC,IAAKvG;IACnB,IACElO,QAAEA,GAAC0Y,oBAAoBxK,MACvBA,EAAKyK,mBACLzK,EAAKyK,gBAAgBC,aAAa,MACsB,gBAAxD1K,EAAKyK,gBAAgBC,aAAa,GAAGpU,KAAKmP,WAC1C;MACA,KAAOkF,KAAe3K,EAAKyK,gBAAgBC;MAC3C,IACEC,EAAY1D,eACZnV,QAAAA,GAAG2V,0BAA0BkD,EAAY1D;QAEzC0D,EAAY1D,YAAY2D,WAAWpB,SAAQqB;UACzC,IACE/Y,QAAEA,GAACoX,qBAAqB2B,MACxB/Y,QAAEA,GAACsT,gBAAgByF,EAASvU;YAE5B;cACE,IAAMwU,IAAmBnD,KAAKxH,MAC3B,GAAE0K,EAASvU,KAAKmP,UAAU0B,QAAQ,MAAM;cAG3C,IACE2D,EAAiBhJ,SAAS,gBAC1BgJ,EAAiBhJ,SAAS,SAC1B;gBACe3B,EAAKA,MAAC2K,GAAkB;kBACrCvB,aAAY;mBAEPrD,YAAYsD,SAAQC;kBACzB,IAAwB,yBAApBA,EAAWvU;oBACb2K,EAAU8B,KAAK8H;;AACjB;AAEJ;AACF,cAAE,OAAO5G,IAAS;;AACpB;;AAGN;AAAA;EAGF,OAAOhD;AACT;;ACxTA,IAAMkL,IACmB,mBAAhBC,eACPA,eAC2B,qBAApBA,YAAYC,MACfD,cACAE;;AAEN,IAAMC,IAAS,IAAIpI;;AAMnB,IAAMqI,IACe,mBAAZC,WAA0BA,UAAUA,UAAU;;AAIvD,IAAMC,cAAcA,CAClBC,GACAvS,GACAwS,GACAC;EAE+B,qBAAxBL,EAAQE,cACXF,EAAQE,YAAYC,GAAKvS,GAAMwS,GAAMC,KACrCC,QAAQtL,MAAM,IAAIoL,MAASxS,MAASuS;AAAM;;AAGhD,IAAII,IAAKC,WAAWC;;AACpB,IAAIC,IAAKF,WAAWG;;AAGpB,SAAkB,MAAPJ,GAAoB;EAE7BG,IAAK,MAAMC;IAETC,SAAqC;IAErCC,SAAmB;IACnBC,gBAAAA,CAAiBC,GAAWV;MAC1BvZ,KAAK8Z,SAASrK,KAAK8J;AACrB;;EAGFE,IAAK,MAAME;IACT7Z,WAAAA;MACEoa;AACF;IACAC,OAAS,IAAIP;IACbQ,KAAAA,CAAMC;MACJ,IAAIra,KAAKma,OAAOJ;QAAS;;MAEzB/Z,KAAKma,OAAOE,SAASA;MAErBra,KAAKma,OAAOJ,WAAU;MAEtB,KAAK,IAAMR,KAAMvZ,KAAKma,OAAOL;QAC3BP,EAAGc;;MAELra,KAAKma,OAAOG,UAAUD;AACxB;;EAEF,IAAIE,IAC2C,QAA7CrB,EAAQsB,KAAKC;EACf,IAAMP,iBAAiBA;IACrB,KAAKK;MAAwB;;IAC7BA,KAAyB;IACzBnB,YACE,oaAOA,uBACA,WACAc;AACD;;;AAWL,IAAMQ,WAAYC,KAChBA,KAAKA,MAAM7Q,KAAKC,MAAM4Q,MAAMA,IAAI,KAAKC,SAASD;;AAchD,IAAME,eAAgBC,MACnBJ,SAASI,KACN,OACAA,KAAOhR,KAAKiR,IAAI,GAAG,KACnBC,aACAF,KAAOhR,KAAKiR,IAAI,GAAG,MACnBE,cACAH,KAAOhR,KAAKiR,IAAI,GAAG,MACnBG,cACAJ,KAAOrX,OAAO0X,mBACdC,YACA;;AAGN,MAAMA,kBAAkBtZ;EACtBhC,WAAAA,CAAYub;IACVC,MAAMD;IACNrb,KAAKub,KAAK;AACZ;;;AAIF,MAAMC;EAIJC,WAAgC;EAChC,aAAOC,CAAOZ;IACZ,IAAMa,IAAUd,aAAaC;IAC7B,KAAKa;MAAS,OAAO;;IACrBH,OAAMI,KAAgB;IACtB,IAAMC,IAAI,IAAIL,MAAMV,GAAKa;IACzBH,OAAMI,KAAgB;IACtB,OAAOC;AACT;EACA/b,WAAAA,CACEgb,GACAa;IAGA,KAAKH,OAAMI;MACT,MAAM,IAAIxQ,UAAU;;IAGtBpL,KAAK8b,OAAO,IAAIH,EAAQb;IACxB9a,KAAKO,SAAS;AAChB;EACAkP,IAAAA,CAAKkL;IACH3a,KAAK8b,KAAK9b,KAAKO,YAAYoa;AAC7B;EACAoB,GAAAA;IACE,OAAO/b,KAAK8b,OAAO9b,KAAKO;AAC1B;;;AAwoBI,MAAOyb;EAIFlB;EACAmB;EACAC;EACAC;EACAC;EAkETf;EACAgB;EACAC;EACAC;EACAC;EACA7b;EACA8b;EACAC;EACAC;EACAC;EACAC;EACAC;EACAC;EACAC;EAEAC;EACAC;EACAC;EAWA,4BAAOC,CAILC;IACA,OAAO;MAELN,QAAQM,GAAEN;MACVC,MAAMK,GAAEL;MACRF,OAAOO,GAAEP;MACTR,QAAQe,GAAEf;MACVC,SAASc,GAAEd;MACXC,SAASa,GAAEb;MACX7b,MAAM0c,GAAE1c;MACR8b,MAAMY,GAAEZ;MACR,QAAIC;QACF,OAAOW,GAAEX;AACV;MACD,QAAIC;QACF,OAAOU,GAAEV;AACV;MACDC,MAAMS,GAAET;MAERU,mBAAoBpa,KAAWma,GAAEC,EAAmBpa;MACpDqa,iBAAiBA,CACfC,GACAC,GACA7U,GACA8U,MAEAL,GAAEE,EACAC,GACAC,GACA7U,GACA8U;MAEJC,YAAaF,KACXJ,GAAEM,EAAYF;MAChBG,SAAUhV,KACRyU,GAAEO,EAAShV;MACbiV,UAAWjV,KACTyU,GAAEQ,EAAUjV;MACdkV,SAAUL,KACRJ,GAAES,EAASL;;AAEjB;EAOA,OAAI3C;IACF,OAAO9a,MAAK8a;AACd;EAIA,WAAImB;IACF,OAAOjc,MAAKic;AACd;EAIA,kBAAII;IACF,OAAOrc,MAAKqc;AACd;EAIA,QAAIhB;IACF,OAAOrb,MAAKqb;AACd;EAIA,eAAIe;IACF,OAAOpc,MAAKoc;AACd;EAIA,WAAIF;IACF,OAAOlc,MAAKkc;AACd;EAIA,gBAAIC;IACF,OAAOnc,MAAKmc;AACd;EAEArc,WAAAA,CACE8I;IAEA,KAAMkS,KACJA,IAAM,GAACiD,KACPA,GAAGC,eACHA,IAAgB,GAACC,cACjBA,GAAYC,gBACZA,GAAcC,gBACdA,GAAcC,YACdA,GAAUlC,SACVA,GAAOC,cACPA,GAAYkC,gBACZA,GAAcC,aACdA,GAAWrC,SACXA,IAAU,GAACsC,cACXA,IAAe,GAACC,iBAChBA,GAAepC,aACfA,GAAWqC,0BACXA,GAAwBC,oBACxBA,GAAkBC,4BAClBA,GAA0BC,wBAC1BA,GAAsBC,kBACtBA,KACEjW;IAEJ,IAAY,MAARkS,MAAcJ,SAASI;MACzB,MAAM,IAAI1P,UAAU;;IAGtB,IAAM0T,IAAYhE,IAAMD,aAAaC,KAAOhZ;IAC5C,KAAKgd;MACH,MAAM,IAAI3R,MAAM,wBAAwB2N;;IAG1C9a,MAAK8a,IAAOA;IACZ9a,MAAKic,IAAWA;IAChBjc,KAAKue,eAAeA,KAAgBve,MAAKic;IACzCjc,KAAKwe,kBAAkBA;IACvB,IAAIxe,KAAKwe,iBAAiB;MACxB,KAAKxe,MAAKic,MAAajc,KAAKue;QAC1B,MAAM,IAAInT,UACR;;MAGJ,IAAoC,qBAAzBpL,KAAKwe;QACd,MAAM,IAAIpT,UAAU;;;IAIxB,SACkBZ,MAAhB4R,KACuB,qBAAhBA;MAEP,MAAM,IAAIhR,UACR;;IAGJpL,MAAKoc,IAAeA;IACpBpc,MAAKkd,MAAoBd;IAEzBpc,MAAKsc,IAAU,IAAIyC;IACnB/e,MAAKuc,IAAW,IAAIza,MAAMgZ,GAAKS,UAAK/Q;IACpCxK,MAAKwc,IAAW,IAAI1a,MAAMgZ,GAAKS,UAAK/Q;IACpCxK,MAAKW,IAAQ,IAAIme,EAAUhE;IAC3B9a,MAAKyc,IAAQ,IAAIqC,EAAUhE;IAC3B9a,MAAK0c,IAAQ;IACb1c,MAAK2c,IAAQ;IACb3c,MAAK4c,IAAQpB,MAAME,OAAOZ;IAC1B9a,MAAKqb,IAAQ;IACbrb,MAAKqc,IAAkB;IAEvB,IAAuB,qBAAZH;MACTlc,MAAKkc,IAAWA;;IAElB,IAA4B,qBAAjBC,GAA6B;MACtCnc,MAAKmc,IAAgBA;MACrBnc,MAAK6c,IAAY;WACZ;MACL7c,MAAKmc,SAAgB3R;MACrBxK,MAAK6c,SAAYrS;;IAEnBxK,MAAKid,MAAgBjd,MAAKkc;IAC1Blc,MAAKmd,MAAqBnd,MAAKmc;IAE/Bnc,KAAKqe,mBAAmBA;IACxBre,KAAKse,gBAAgBA;IACrBte,KAAKye,6BAA6BA;IAClCze,KAAK2e,+BAA+BA;IACpC3e,KAAK4e,2BAA2BA;IAChC5e,KAAK6e,qBAAqBA;IAG1B,IAA0B,MAAtB7e,KAAKue,cAAoB;MAC3B,IAAsB,MAAlBve,MAAKic;QACP,KAAKvB,SAAS1a,MAAKic;UACjB,MAAM,IAAI7Q,UACR;;;MAIN,KAAKsP,SAAS1a,KAAKue;QACjB,MAAM,IAAInT,UACR;;MAGJpL,MAAKgf;;IAGPhf,KAAKoe,eAAeA;IACpBpe,KAAK0e,uBAAuBA;IAC5B1e,KAAKke,mBAAmBA;IACxBle,KAAKme,mBAAmBA;IACxBne,KAAKge,gBACHtD,SAASsD,MAAoC,MAAlBA,IACvBA,IACA;IACNhe,KAAKie,iBAAiBA;IACtBje,KAAK+d,MAAMA,KAAO;IAClB,IAAI/d,KAAK+d,KAAK;MACZ,KAAKrD,SAAS1a,KAAK+d;QACjB,MAAM,IAAI3S,UACR;;MAGJpL,MAAKif;;IAIP,IAAkB,MAAdjf,MAAK8a,KAA2B,MAAb9a,KAAK+d,OAA+B,MAAlB/d,MAAKic;MAC5C,MAAM,IAAI7Q,UACR;;IAGJ,KAAKpL,KAAKie,iBAAiBje,MAAK8a,MAAS9a,MAAKic,GAAU;MACtD,IAAM3C,IAAO;MACb,IA3hCcA,OAAkBL,EAAOzH,IAAI8H,GA2hCvC4F,CAAW5F,IAAO;QACpBL,EAAOvC,IAAI4C;QAIXF,YAFE,iGAEe,yBAAyBE,GAAM0C;;;AAGtD;EAKAmD,eAAAA,CAAgBC;IACd,OAAOpf,MAAKsc,EAAQ9K,IAAI4N,KAAOC,QAAW;AAC5C;EAEA,EAAAJ;IACE,IAAMjC,IAAO,IAAI5B,UAAUpb,MAAK8a;IAChC,IAAMiC,IAAS,IAAI3B,UAAUpb,MAAK8a;IAClC9a,MAAKgd,IAAQA;IACbhd,MAAK+c,IAAUA;IAEf/c,MAAKsf,IAAc,CAAC7B,GAAOM,GAAKvS,IAAQqN,EAAKE;MAC3CgE,EAAOU,KAAiB,MAARM,IAAYvS,IAAQ;MACpCwR,EAAKS,KAASM;MACd,IAAY,MAARA,KAAa/d,KAAKie,cAAc;QAClC,IAAMlb,IAAIwc,YAAW;UACnB,IAAIvf,MAAK8d,EAASL;YAChBzd,KAAKwf,OAAOxf,MAAKuc,EAASkB;;YAE3BM,IAAM;QAGT,IAAIhb,EAAE0c;UACJ1c,EAAE0c;;;;IAMRzf,MAAK0f,IAAiBjC;MACpBV,EAAOU,KAAyB,MAAhBT,EAAKS,KAAe5E,EAAKE,QAAQ;AAAC;IAGpD/Y,MAAK2f,IAAa,CAACC,GAAQnC;MACzB,IAAIT,EAAKS,IAAQ;QACf,IAAMM,IAAMf,EAAKS;QACjB,IAAMjS,IAAQuR,EAAOU;QACrBmC,EAAO7B,MAAMA;QACb6B,EAAOpU,QAAQA;QACfoU,EAAO7G,MAAM8G,KAAaC;QAE1BF,EAAOG,eAAehC,KADV6B,EAAO7G,MAAMvN;;;IAO7B,IAAIqU,IAAY;IAChB,IAAMC,SAASA;MACb,IAAMnF,IAAI9B,EAAKE;MACf,IAAI/Y,KAAKge,gBAAgB,GAAG;QAC1B6B,IAAYlF;QACZ,IAAM5X,IAAIwc,YACR,MAAOM,IAAY,IACnB7f,KAAKge;QAIP,IAAIjb,EAAE0c;UACJ1c,EAAE0c;;;MAIN,OAAO9E;AAAC;IAGV3a,KAAKmf,kBAAkBC;MACrB,IAAM3B,IAAQzd,MAAKsc,EAAQ0D,IAAIZ;MAC/B,SAAc5U,MAAViT;QACF,OAAO;;MAET,IAAMM,IAAMf,EAAKS;MACjB,IAAMjS,IAAQuR,EAAOU;MACrB,IAAY,MAARM,KAAuB,MAAVvS;QACf,OAAO6T;;MAGT,OAAOtB,MADM8B,KAAaC,YAAYtU;AACtB;IAGlBxL,MAAK8d,IAAWL,KAEI,MAAhBT,EAAKS,MACa,MAAlBV,EAAOU,OACNoC,KAAaC,YAAY/C,EAAOU,KAAST,EAAKS;AAGrD;EAGAiC,GAAyCO;EACzCN,GACEO;EACFZ,GAMYa;EAGZrC,GAAsCsC,OAAM;EAE5C,EAAApB;IACE,IAAMlC,IAAQ,IAAI1B,UAAUpb,MAAK8a;IACjC9a,MAAKqc,IAAkB;IACvBrc,MAAK8c,IAASA;IACd9c,MAAKqgB,IAAkB5C;MACrBzd,MAAKqc,KAAmBS,EAAMW;MAC9BX,EAAMW,KAAS;AAAC;IAElBzd,MAAKsgB,IAAe,CAAC9C,GAAG+C,GAAGlF,GAAMmD;MAG/B,IAAIxe,MAAKsd,EAAmBiD;QAC1B,OAAO;;MAET,KAAK7F,SAASW;QACZ,IAAImD,GAAiB;UACnB,IAA+B,qBAApBA;YACT,MAAM,IAAIpT,UAAU;;UAEtBiQ,IAAOmD,EAAgB+B,GAAG/C;UAC1B,KAAK9C,SAASW;YACZ,MAAM,IAAIjQ,UACR;;;UAIJ,MAAM,IAAIA,UACR;;;MAMN,OAAOiQ;AAAI;IAEbrb,MAAKwgB,IAAe,CAClB/C,GACApC,GACAuE;MAEA9C,EAAMW,KAASpC;MACf,IAAIrb,MAAKic,GAAU;QACjB,IAAMA,IAAUjc,MAAKic,IAAWa,EAAMW;QACtC,OAAOzd,MAAKqc,IAAkBJ;UAC5Bjc,MAAKygB,GAAO;;;MAGhBzgB,MAAKqc,KAAmBS,EAAMW;MAC9B,IAAImC,GAAQ;QACVA,EAAOc,YAAYrF;QACnBuE,EAAOe,sBAAsB3gB,MAAKqc;;;AAGxC;EAEAgE,GAA0CO;EAC1CJ,GAIYK,CAACD,GAAIE,GAAIC,OAATF;EACZP,GAKqBU,CACnBC,GACAC,GACA7F,GACAmD;IAEA,IAAInD,KAAQmD;MACV,MAAM,IAAIpT,UACR;;IAGJ,OAAO;AAAC;EAGV,IAACwS,EAASQ,YAAEA,IAAape,KAAKoe,cAAe;IAC3C,IAAIpe,MAAKqb;MACP,KAAK,IAAIjR,IAAIpK,MAAK2c,GAAO,KAAQ;QAC/B,KAAK3c,MAAKmhB,EAAc/W;UACtB;;QAEF,IAAIgU,MAAepe,MAAK8d,EAAS1T;gBACzBA;;QAER,IAAIA,MAAMpK,MAAK0c;UACb;;UAEAtS,IAAIpK,MAAKyc,EAAMrS;;;;AAIvB;EAEA,IAACyT,EAAUO,YAAEA,IAAape,KAAKoe,cAAe;IAC5C,IAAIpe,MAAKqb;MACP,KAAK,IAAIjR,IAAIpK,MAAK0c,GAAO,KAAQ;QAC/B,KAAK1c,MAAKmhB,EAAc/W;UACtB;;QAEF,IAAIgU,MAAepe,MAAK8d,EAAS1T;gBACzBA;;QAER,IAAIA,MAAMpK,MAAK2c;UACb;;UAEAvS,IAAIpK,MAAKW,EAAMyJ;;;;AAIvB;EAEA,EAAA+W,CAAc1D;IACZ,YACYjT,MAAViT,KACAzd,MAAKsc,EAAQ0D,IAAIhgB,MAAKuc,EAASkB,QAAiBA;AAEpD;EAMA,SAACpN;IACC,KAAK,IAAMjG,KAAKpK,MAAK4d;MACnB,SACuBpT,MAArBxK,MAAKwc,EAASpS,WACOI,MAArBxK,MAAKuc,EAASnS,OACbpK,MAAKsd,EAAmBtd,MAAKwc,EAASpS;cAEjC,EAACpK,MAAKuc,EAASnS,IAAIpK,MAAKwc,EAASpS;;;AAG7C;EAQA,UAACgX;IACC,KAAK,IAAMhX,KAAKpK,MAAK6d;MACnB,SACuBrT,MAArBxK,MAAKwc,EAASpS,WACOI,MAArBxK,MAAKuc,EAASnS,OACbpK,MAAKsd,EAAmBtd,MAAKwc,EAASpS;cAEjC,EAACpK,MAAKuc,EAASnS,IAAIpK,MAAKwc,EAASpS;;;AAG7C;EAMA,MAACD;IACC,KAAK,IAAMC,KAAKpK,MAAK4d,KAAY;MAC/B,IAAMJ,IAAIxd,MAAKuc,EAASnS;MACxB,SACQI,MAANgT,MACCxd,MAAKsd,EAAmBtd,MAAKwc,EAASpS;cAEjCoT;;;AAGZ;EAQA,OAAC6D;IACC,KAAK,IAAMjX,KAAKpK,MAAK6d,KAAa;MAChC,IAAML,IAAIxd,MAAKuc,EAASnS;MACxB,SACQI,MAANgT,MACCxd,MAAKsd,EAAmBtd,MAAKwc,EAASpS;cAEjCoT;;;AAGZ;EAMA,QAAC8D;IACC,KAAK,IAAMlX,KAAKpK,MAAK4d,KAAY;MAE/B,SACQpT,MAFExK,MAAKwc,EAASpS,OAGrBpK,MAAKsd,EAAmBtd,MAAKwc,EAASpS;cAEjCpK,MAAKwc,EAASpS;;;AAG1B;EAQA,SAACmX;IACC,KAAK,IAAMnX,KAAKpK,MAAK6d,KAAa;MAEhC,SACQrT,MAFExK,MAAKwc,EAASpS,OAGrBpK,MAAKsd,EAAmBtd,MAAKwc,EAASpS;cAEjCpK,MAAKwc,EAASpS;;;AAG1B;EAMA,CAACoX,OAAOC;IACN,OAAOzhB,KAAKqQ;AACd;EAMA2C,IAAAA,CACEuG,GACAmI,IAA4C;IAE5C,KAAK,IAAMtX,KAAKpK,MAAK4d,KAAY;MAC/B,IAAM2C,IAAIvgB,MAAKwc,EAASpS;MACxB,IAAMjH,IAAQnD,MAAKsd,EAAmBiD,KAClCA,EAAEoB,uBACFpB;MACJ,SAAc/V,MAAVrH;QAAqB;;MACzB,IAAIoW,EAAGpW,GAAOnD,MAAKuc,EAASnS,IAASpK;QACnC,OAAOA,KAAKggB,IAAIhgB,MAAKuc,EAASnS,IAASsX;;;AAG7C;EAQApK,OAAAA,CACEiC,GACAqI,IAAa5hB;IAEb,KAAK,IAAMoK,KAAKpK,MAAK4d,KAAY;MAC/B,IAAM2C,IAAIvgB,MAAKwc,EAASpS;MACxB,IAAMjH,IAAQnD,MAAKsd,EAAmBiD,KAClCA,EAAEoB,uBACFpB;MACJ,SAAc/V,MAAVrH;QAAqB;;MACzBoW,EAAG1O,KAAK+W,GAAOze,GAAOnD,MAAKuc,EAASnS,IAASpK;;AAEjD;EAMA6hB,QAAAA,CACEtI,GACAqI,IAAa5hB;IAEb,KAAK,IAAMoK,KAAKpK,MAAK6d,KAAa;MAChC,IAAM0C,IAAIvgB,MAAKwc,EAASpS;MACxB,IAAMjH,IAAQnD,MAAKsd,EAAmBiD,KAClCA,EAAEoB,uBACFpB;MACJ,SAAc/V,MAAVrH;QAAqB;;MACzBoW,EAAG1O,KAAK+W,GAAOze,GAAOnD,MAAKuc,EAASnS,IAASpK;;AAEjD;EAMA8hB,UAAAA;IACE,IAAIC,KAAU;IACd,KAAK,IAAM3X,KAAKpK,MAAK6d,EAAU;MAAEO,aAAY;;MAC3C,IAAIpe,MAAK8d,EAAS1T,IAAI;QACpBpK,KAAKwf,OAAOxf,MAAKuc,EAASnS;QAC1B2X,KAAU;;;IAGd,OAAOA;AACT;EAMAC,IAAAA;IACE,IAAMC,IAAgC;IACtC,KAAK,IAAM7X,KAAKpK,MAAK4d,EAAS;MAAEQ,aAAY;QAAS;MACnD,IAAMgB,IAAMpf,MAAKuc,EAASnS;MAC1B,IAAMmW,IAAIvgB,MAAKwc,EAASpS;MACxB,IAAMjH,IAAuBnD,MAAKsd,EAAmBiD,KACjDA,EAAEoB,uBACFpB;MACJ,SAAc/V,MAAVrH,UAA+BqH,MAAR4U;QAAmB;;MAC9C,IAAM8C,IAA2B;QAAE/e;;MACnC,IAAInD,MAAKgd,KAAShd,MAAK+c,GAAS;QAC9BmF,EAAMnE,MAAM/d,MAAKgd,EAAM5S;QAGvB,IAAM+X,IAAMtJ,EAAKE,QAAQ/Y,MAAK+c,EAAQ3S;QACtC8X,EAAM1W,QAAQ1B,KAAKC,MAAMiP,KAAKD,QAAQoJ;;MAExC,IAAIniB,MAAK8c;QACPoF,EAAM7G,OAAOrb,MAAK8c,EAAO1S;;MAE3B6X,EAAIG,QAAQ,EAAChD,GAAK8C;;IAEpB,OAAOD;AACT;EAOAI,IAAAA,CAAKJ;IACHjiB,KAAKsiB;IACL,KAAK,KAAOlD,GAAK8C,MAAUD,GAAK;MAC9B,IAAIC,EAAM1W,OAAO;QAOf,IAAM2W,IAAMnJ,KAAKD,QAAQmJ,EAAM1W;QAC/B0W,EAAM1W,QAAQqN,EAAKE,QAAQoJ;;MAE7BniB,KAAKuiB,IAAInD,GAAK8C,EAAM/e,OAAO+e;;AAE/B;EAQAK,GAAAA,CACE/E,GACA+C,GACAiC,IAA4C,CAAA;IAE5C,SAAUhY,MAAN+V,GAAiB;MACnBvgB,KAAKwf,OAAOhC;MACZ,OAAOxd;;IAET,KAAM+d,KACJA,IAAM/d,KAAK+d,KAAGvS,OACdA,GAAK6S,gBACLA,IAAiBre,KAAKqe,gBAAcG,iBACpCA,IAAkBxe,KAAKwe,iBAAeoB,QACtCA,KACE4C;IACJ,KAAIlE,aAAEA,IAActe,KAAKse,eAAgBkE;IAEzC,IAAMnH,IAAOrb,MAAKsgB,EAChB9C,GACA+C,GACAiC,EAAWnH,QAAQ,GACnBmD;IAIF,IAAIxe,KAAKue,gBAAgBlD,IAAOrb,KAAKue,cAAc;MACjD,IAAIqB,GAAQ;QACVA,EAAO2C,MAAM;QACb3C,EAAO6C,wBAAuB;;MAGhCziB,KAAKwf,OAAOhC;MACZ,OAAOxd;;IAET,IAAIyd,IAAuB,MAAfzd,MAAKqb,SAAc7Q,IAAYxK,MAAKsc,EAAQ0D,IAAIxC;IAC5D,SAAchT,MAAViT,GAAqB;MAEvBA,IACiB,MAAfzd,MAAKqb,IACDrb,MAAK2c,IACiB,MAAtB3c,MAAK4c,EAAMrc,SACXP,MAAK4c,EAAMb,QACX/b,MAAKqb,MAAUrb,MAAK8a,IACpB9a,MAAKygB,GAAO,KACZzgB,MAAKqb;MAEXrb,MAAKuc,EAASkB,KAASD;MACvBxd,MAAKwc,EAASiB,KAAS8C;MACvBvgB,MAAKsc,EAAQiG,IAAI/E,GAAGC;MACpBzd,MAAKW,EAAMX,MAAK2c,KAASc;MACzBzd,MAAKyc,EAAMgB,KAASzd,MAAK2c;MACzB3c,MAAK2c,IAAQc;MACbzd,MAAKqb;MACLrb,MAAKwgB,EAAa/C,GAAOpC,GAAMuE;MAC/B,IAAIA;QAAQA,EAAO2C,MAAM;;MACzBjE,KAAc;WACT;MAELte,MAAK2d,EAAYF;MACjB,IAAMiF,IAAS1iB,MAAKwc,EAASiB;MAC7B,IAAI8C,MAAMmC,GAAQ;QAChB,IAAI1iB,MAAKkd,KAAmBld,MAAKsd,EAAmBoF,IAAS;UAC3DA,EAAOC,kBAAkBvI,MAAM,IAAIjN,MAAM;UACzC,KAAQwU,sBAAsB9F,KAAM6G;UACpC,SAAUlY,MAANqR,MAAoBwC,GAAgB;YACtC,IAAIre,MAAKid;cACPjd,MAAKkc,IAAWL,GAAQ2B,GAAG;;YAE7B,IAAIxd,MAAKmd;cACPnd,MAAK6c,GAAWpN,KAAK,EAACoM,GAAQ2B,GAAG;;;eAGhC,KAAKa,GAAgB;UAC1B,IAAIre,MAAKid;YACPjd,MAAKkc,IAAWwG,GAAalF,GAAG;;UAElC,IAAIxd,MAAKmd;YACPnd,MAAK6c,GAAWpN,KAAK,EAACiT,GAAalF,GAAG;;;QAG1Cxd,MAAKqgB,EAAgB5C;QACrBzd,MAAKwgB,EAAa/C,GAAOpC,GAAMuE;QAC/B5f,MAAKwc,EAASiB,KAAS8C;QACvB,IAAIX,GAAQ;UACVA,EAAO2C,MAAM;UACb,IAAMK,IACJF,KAAU1iB,MAAKsd,EAAmBoF,KAC9BA,EAAOf,uBACPe;UACN,SAAiBlY,MAAboY;YAAwBhD,EAAOgD,WAAWA;;;AAEjD,aAAM,IAAIhD;QACTA,EAAO2C,MAAM;;;IAGjB,IAAY,MAARxE,MAAc/d,MAAKgd;MACrBhd,MAAKif;;IAEP,IAAIjf,MAAKgd,GAAO;MACd,KAAKsB;QACHte,MAAKsf,EAAY7B,GAAOM,GAAKvS;;MAE/B,IAAIoU;QAAQ5f,MAAK2f,EAAWC,GAAQnC;;;IAEtC,KAAKY,KAAkBre,MAAKmd,KAAoBnd,MAAK6c,GAAW;MAC9D,IAAMgG,IAAK7iB,MAAK6c;MAChB,IAAIiG;MACJ,OAAQA,IAAOD,GAAInL;QACjB1X,MAAKmc,OAAmB2G;;;IAG5B,OAAO9iB;AACT;EAMA+b,GAAAA;IACE;MACE,OAAO/b,MAAKqb,GAAO;QACjB,IAAM0H,IAAM/iB,MAAKwc,EAASxc,MAAK0c;QAC/B1c,MAAKygB,GAAO;QACZ,IAAIzgB,MAAKsd,EAAmByF;UAC1B,IAAIA,EAAIpB;YACN,OAAOoB,EAAIpB;;eAER,SAAYnX,MAARuY;UACT,OAAOA;;;MAGH;MACR,IAAI/iB,MAAKmd,KAAoBnd,MAAK6c,GAAW;QAC3C,IAAMgG,IAAK7iB,MAAK6c;QAChB,IAAIiG;QACJ,OAAQA,IAAOD,GAAInL;UACjB1X,MAAKmc,OAAmB2G;;;;AAIhC;EAEA,EAAArC,CAAO7D;IACL,IAAMF,IAAO1c,MAAK0c;IAClB,IAAMc,IAAIxd,MAAKuc,EAASG;IACxB,IAAM6D,IAAIvgB,MAAKwc,EAASE;IACxB,IAAI1c,MAAKkd,KAAmBld,MAAKsd,EAAmBiD;MAClDA,EAAEoC,kBAAkBvI,MAAM,IAAIjN,MAAM;WAC/B,IAAInN,MAAKid,KAAejd,MAAKmd,GAAkB;MACpD,IAAInd,MAAKid;QACPjd,MAAKkc,IAAWqE,GAAG/C,GAAG;;MAExB,IAAIxd,MAAKmd;QACPnd,MAAK6c,GAAWpN,KAAK,EAAC8Q,GAAG/C,GAAG;;;IAGhCxd,MAAKqgB,EAAgB3D;IAErB,IAAIE,GAAM;MACR5c,MAAKuc,EAASG,UAAQlS;MACtBxK,MAAKwc,EAASE,UAAQlS;MACtBxK,MAAK4c,EAAMnN,KAAKiN;;IAElB,IAAmB,MAAf1c,MAAKqb,GAAa;MACpBrb,MAAK0c,IAAQ1c,MAAK2c,IAAQ;MAC1B3c,MAAK4c,EAAMrc,SAAS;;MAEpBP,MAAK0c,IAAQ1c,MAAKW,EAAM+b;;IAE1B1c,MAAKsc,EAAQkD,OAAOhC;IACpBxd,MAAKqb;IACL,OAAOqB;AACT;EAUAlL,GAAAA,CAAIgM,GAAMwF,IAA4C;IACpD,KAAM7E,gBAAEA,IAAiBne,KAAKme,gBAAcyB,QAAEA,KAC5CoD;IACF,IAAMvF,IAAQzd,MAAKsc,EAAQ0D,IAAIxC;IAC/B,SAAchT,MAAViT,GAAqB;MACvB,IAAM8C,IAAIvgB,MAAKwc,EAASiB;MACxB,IACEzd,MAAKsd,EAAmBiD,WACG/V,MAA3B+V,EAAEoB;QAEF,QAAO;;MAET,KAAK3hB,MAAK8d,EAASL,IAAQ;QACzB,IAAIU;UACFne,MAAK0f,EAAejC;;QAEtB,IAAImC,GAAQ;UACVA,EAAOpO,MAAM;UACbxR,MAAK2f,EAAWC,GAAQnC;;QAE1B,QAAO;AACR,aAAM,IAAImC,GAAQ;QACjBA,EAAOpO,MAAM;QACbxR,MAAK2f,EAAWC,GAAQnC;;AAE3B,WAAM,IAAImC;MACTA,EAAOpO,MAAM;;IAEf,QAAO;AACT;EASA/Q,IAAAA,CAAK+c,GAAMyF,IAA8C;IACvD,KAAM7E,YAAEA,IAAape,KAAKoe,cAAe6E;IACzC,IAAMxF,IAAQzd,MAAKsc,EAAQ0D,IAAIxC;IAC/B,SACYhT,MAAViT,MACCW,MAAepe,MAAK8d,EAASL,KAC9B;MACA,IAAM8C,IAAIvgB,MAAKwc,EAASiB;MAExB,OAAOzd,MAAKsd,EAAmBiD,KAAKA,EAAEoB,uBAAuBpB;;AAEjE;EAEA,EAAAhD,CACEC,GACAC,GACA7U,GACA8U;IAEA,IAAM6C,SAAc/V,MAAViT,SAAsBjT,IAAYxK,MAAKwc,EAASiB;IAC1D,IAAIzd,MAAKsd,EAAmBiD;MAC1B,OAAOA;;IAGT,IAAM2C,IAAK,IAAIzJ;IACf,KAAMU,QAAEA,KAAWvR;IAEnBuR,GAAQH,iBAAiB,UAAS,MAAMkJ,EAAG9I,MAAMD,EAAOE,UAAS;MAC/DF,QAAQ+I,EAAG/I;;IAGb,IAAMgJ,IAAY;MAChBhJ,QAAQ+I,EAAG/I;MACXvR;MACA8U;;IAGF,IAAM0F,KAAKA,CACT7C,GACA8C,KAAc;MAEd,KAAMtJ,SAAEA,KAAYmJ,EAAG/I;MACvB,IAAMmJ,IAAc1a,EAAQiW,yBAA0BrU,MAAN+V;MAChD,IAAI3X,EAAQgX;QACV,IAAI7F,MAAYsJ,GAAa;UAC3Bza,EAAQgX,OAAO2D,gBAAe;UAC9B3a,EAAQgX,OAAO4D,aAAaN,EAAG/I,OAAOE;UACtC,IAAIiJ;YAAa1a,EAAQgX,OAAO6D,qBAAoB;;;UAEpD7a,EAAQgX,OAAO8D,iBAAgB;;;MAGnC,IAAI3J,MAAYuJ,MAAgBD;QAC9B,OAAOM,UAAUT,EAAG/I,OAAOE;;MAI7B,IAAIra,MAAKwc,EAASiB,OAAoBva;QACpC,SAAUsH,MAAN+V;UACF,IAHOrd,EAGAye;YACL3hB,MAAKwc,EAASiB,KAJTva,EAI8Bye;;YAEnC3hB,KAAKwf,OAAOhC;;eAET;UACL,IAAI5U,EAAQgX;YAAQhX,EAAQgX,OAAOgE,gBAAe;;UAClD5jB,KAAKuiB,IAAI/E,GAAG+C,GAAG4C,EAAUva;;;MAG7B,OAAO2X;AAAC;IAWV,IAAMoD,YAAaE;MACjB,KAAM9J,SAAEA,KAAYmJ,EAAG/I;MACvB,IAAM2J,IACJ/J,KAAWnR,EAAQgW;MACrB,IAAMR,IACJ0F,KAAqBlb,EAAQ+V;MAE/B,IAAMoF,IAAK7gB;MACX,IAAIlD,MAAKwc,EAASiB,OAAoBva,GAAG;QAIvC,MANekb,KAAcxV,EAAQ6V,kCAKgBjU,MAA5BuZ,EAAGpC;UAE1B3hB,KAAKwf,OAAOhC;eACP,KAAKsG;UAKV9jB,MAAKwc,EAASiB,KAAkBsG,EAAGpC;;;MAGvC,IAAIvD,GAAY;QACd,IAAIxV,EAAQgX,eAAsCpV,MAA5BuZ,EAAGpC;UACvB/Y,EAAQgX,OAAOoE,iBAAgB;;QAEjC,OAAOD,EAAGpC;AACX,aAAM,IAAIoC,EAAGE,eAAeF;QAC3B,MAAMF;;;IA6BV,IAAIjb,EAAQgX;MAAQhX,EAAQgX,OAAOsE,mBAAkB;;IACrD,IAAMhhB,IAAI,IAAIihB,SA1BAC,CACZC,GACAC;MAEA,IAAMC,IAAMvkB,MAAKoc,IAAeoB,GAAG+C,GAAG4C;MACtC,IAAIoB,KAAOA,aAAeJ;QACxBI,EAAIC,MAAKjE,KAAK8D,OAAU7Z,MAAN+V,SAAkB/V,IAAY+V,KAAI+D;;MAKtDpB,EAAG/I,OAAOH,iBAAiB,UAAS;QAClC,KACGpR,EAAQiW,oBACTjW,EAAQgW,wBACR;UACAyF,OAAI7Z;UAEJ,IAAI5B,EAAQgW;YACVyF,IAAM9D,KAAK6C,GAAG7C,IAAG;;;;AAGrB,QAIyBiE,KAAKpB,KAlEtBS;MACV,IAAIjb,EAAQgX,QAAQ;QAClBhX,EAAQgX,OAAO6E,iBAAgB;QAC/B7b,EAAQgX,OAAO4D,aAAaK;;MAE9B,OAAOF,UAAUE;AAAG;IA8DtB,IAAME,IAAyB7Z,OAAOK,OAAOrH,GAAG;MAC9Cyf,mBAAmBO;MACnBvB,sBAAsBpB;MACtB0D,iBAAYzZ;;IAGd,SAAcA,MAAViT,GAAqB;MAEvBzd,KAAKuiB,IAAI/E,GAAGuG,GAAI;WAAKZ,EAAUva;QAASgX,aAAQpV;;MAChDiT,IAAQzd,MAAKsc,EAAQ0D,IAAIxC;;MAEzBxd,MAAKwc,EAASiB,KAASsG;;IAEzB,OAAOA;AACT;EAEA,EAAAzG,CAAmBpa;IACjB,KAAKlD,MAAKkd;MAAiB,QAAO;;IAClC,IAAMwH,IAAIxhB;IACV,SACIwhB,KACFA,aAAaP,WACbO,EAAEC,eAAe,2BACjBD,EAAE/B,6BAA6BlJ;AAEnC;EAwCA,WAAMmL,CACJpH,GACAqH,IAAgD;IAEhD,KAAMzG,YAEJA,IAAape,KAAKoe,YAAUF,gBAC5BA,IAAiBle,KAAKke,gBAAcQ,oBACpCA,IAAqB1e,KAAK0e,oBAAkBX,KAE5CA,IAAM/d,KAAK+d,KAAGM,gBACdA,IAAiBre,KAAKqe,gBAAchD,MACpCA,IAAO,GAACmD,iBACRA,IAAkBxe,KAAKwe,iBAAeF,aACtCA,IAActe,KAAKse,aAAWG,0BAE9BA,IAA2Bze,KAAKye,0BAAwBE,4BACxDA,IAA6B3e,KAAK2e,4BAA0BE,kBAC5DA,IAAmB7e,KAAK6e,kBAAgBD,wBACxCA,IAAyB5e,KAAK4e,wBAAsBlB,SACpDA,GAAOoH,cACPA,KAAe,GAAKlF,QACpBA,GAAMzF,QACNA,KACE0K;IAEJ,KAAK7kB,MAAKkd,GAAiB;MACzB,IAAI0C;QAAQA,EAAOgF,QAAQ;;MAC3B,OAAO5kB,KAAKggB,IAAIxC,GAAG;QACjBY;QACAF;QACAQ;QACAkB;;;IAIJ,IAAMhX,IAAU;MACdwV;MACAF;MACAQ;MACAX;MACAM;MACAhD;MACAmD;MACAF;MACAG;MACAE;MACAC;MACAC;MACAe;MACAzF;;IAGF,IAAIsD,IAAQzd,MAAKsc,EAAQ0D,IAAIxC;IAC7B,SAAchT,MAAViT,GAAqB;MACvB,IAAImC;QAAQA,EAAOgF,QAAQ;;MAC3B,IAAM1hB,IAAIlD,MAAKud,EAAiBC,GAAGC,GAAO7U,GAAS8U;MACnD,OAAQxa,EAAE+gB,aAAa/gB;WAClB;MAEL,IAAMqd,IAAIvgB,MAAKwc,EAASiB;MACxB,IAAIzd,MAAKsd,EAAmBiD,IAAI;QAC9B,IAAMwE,IACJ3G,UAAyC5T,MAA3B+V,EAAEoB;QAClB,IAAI/B,GAAQ;UACVA,EAAOgF,QAAQ;UACf,IAAIG;YAAOnF,EAAOoE,iBAAgB;;;QAEpC,OAAOe,IAAQxE,EAAEoB,uBAAwBpB,EAAE0D,aAAa1D;;MAK1D,IAAMzC,IAAU9d,MAAK8d,EAASL;MAC9B,KAAKqH,MAAiBhH,GAAS;QAC7B,IAAI8B;UAAQA,EAAOgF,QAAQ;;QAC3B5kB,MAAK2d,EAAYF;QACjB,IAAIS;UACFle,MAAK0f,EAAejC;;QAEtB,IAAImC;UAAQ5f,MAAK2f,EAAWC,GAAQnC;;QACpC,OAAO8C;;MAKT,IAAMrd,IAAIlD,MAAKud,EAAiBC,GAAGC,GAAO7U,GAAS8U;MAEnD,IAAMsH,SADsCxa,MAA3BtH,EAAEye,wBACUvD;MAC7B,IAAIwB,GAAQ;QACVA,EAAOgF,QAAQ9G,IAAU,UAAU;QACnC,IAAIkH,KAAYlH;UAAS8B,EAAOoE,iBAAgB;;;MAElD,OAAOgB,IAAW9hB,EAAEye,uBAAwBze,EAAE+gB,aAAa/gB;;AAE/D;EAQA8c,GAAAA,CAAIxC,GAAMkE,IAA4C;IACpD,KAAMtD,YACJA,IAAape,KAAKoe,YAAUF,gBAC5BA,IAAiBle,KAAKke,gBAAcQ,oBACpCA,IAAqB1e,KAAK0e,oBAAkBkB,QAC5CA,KACE8B;IACJ,IAAMjE,IAAQzd,MAAKsc,EAAQ0D,IAAIxC;IAC/B,SAAchT,MAAViT,GAAqB;MACvB,IAAMta,IAAQnD,MAAKwc,EAASiB;MAC5B,IAAMwH,IAAWjlB,MAAKsd,EAAmBna;MACzC,IAAIyc;QAAQ5f,MAAK2f,EAAWC,GAAQnC;;MACpC,IAAIzd,MAAK8d,EAASL,IAAQ;QACxB,IAAImC;UAAQA,EAAOI,MAAM;;QAEzB,KAAKiF,GAAU;UACb,KAAKvG;YACH1e,KAAKwf,OAAOhC;;UAEd,IAAIoC,KAAUxB;YAAYwB,EAAOoE,iBAAgB;;UACjD,OAAO5F,IAAajb,SAAQqH;eACvB;UACL,IACEoV,KACAxB,UAC+B5T,MAA/BrH,EAAMwe;YAEN/B,EAAOoE,iBAAgB;;UAEzB,OAAO5F,IAAajb,EAAMwe,4BAAuBnX;;aAE9C;QACL,IAAIoV;UAAQA,EAAOI,MAAM;;QAMzB,IAAIiF;UACF,OAAO9hB,EAAMwe;;QAEf3hB,MAAK2d,EAAYF;QACjB,IAAIS;UACFle,MAAK0f,EAAejC;;QAEtB,OAAOta;;AAEV,WAAM,IAAIyc;MACTA,EAAOI,MAAM;;AAEjB;EAEA,EAAAkF,CAAShiB,GAAUyX;IACjB3a,MAAKyc,EAAM9B,KAAKzX;IAChBlD,MAAKW,EAAMuC,KAAKyX;AAClB;EAEA,EAAAgD,CAAYF;IASV,IAAIA,MAAUzd,MAAK2c,GAAO;MACxB,IAAIc,MAAUzd,MAAK0c;QACjB1c,MAAK0c,IAAQ1c,MAAKW,EAAM8c;;QAExBzd,MAAKklB,EACHllB,MAAKyc,EAAMgB,IACXzd,MAAKW,EAAM8c;;MAGfzd,MAAKklB,EAASllB,MAAK2c,GAAOc;MAC1Bzd,MAAK2c,IAAQc;;AAEjB;EAMA+B,OAAOhC;IACL,IAAIuE,KAAU;IACd,IAAmB,MAAf/hB,MAAKqb,GAAa;MACpB,IAAMoC,IAAQzd,MAAKsc,EAAQ0D,IAAIxC;MAC/B,SAAchT,MAAViT,GAAqB;QACvBsE,KAAU;QACV,IAAmB,MAAf/hB,MAAKqb;UACPrb,KAAKsiB;eACA;UACLtiB,MAAKqgB,EAAgB5C;UACrB,IAAM8C,IAAIvgB,MAAKwc,EAASiB;UACxB,IAAIzd,MAAKsd,EAAmBiD;YAC1BA,EAAEoC,kBAAkBvI,MAAM,IAAIjN,MAAM;iBAC/B,IAAInN,MAAKid,KAAejd,MAAKmd,GAAkB;YACpD,IAAInd,MAAKid;cACPjd,MAAKkc,IAAWqE,GAAQ/C,GAAG;;YAE7B,IAAIxd,MAAKmd;cACPnd,MAAK6c,GAAWpN,KAAK,EAAC8Q,GAAQ/C,GAAG;;;UAGrCxd,MAAKsc,EAAQkD,OAAOhC;UACpBxd,MAAKuc,EAASkB,UAASjT;UACvBxK,MAAKwc,EAASiB,UAASjT;UACvB,IAAIiT,MAAUzd,MAAK2c;YACjB3c,MAAK2c,IAAQ3c,MAAKyc,EAAMgB;iBACnB,IAAIA,MAAUzd,MAAK0c;YACxB1c,MAAK0c,IAAQ1c,MAAKW,EAAM8c;iBACnB;YACLzd,MAAKW,EAAMX,MAAKyc,EAAMgB,MAAUzd,MAAKW,EAAM8c;YAC3Czd,MAAKyc,EAAMzc,MAAKW,EAAM8c,MAAUzd,MAAKyc,EAAMgB;;UAE7Czd,MAAKqb;UACLrb,MAAK4c,EAAMnN,KAAKgO;;;;IAItB,IAAIzd,MAAKmd,KAAoBnd,MAAK6c,GAAWtc,QAAQ;MACnD,IAAMsiB,IAAK7iB,MAAK6c;MAChB,IAAIiG;MACJ,OAAQA,IAAOD,GAAInL;QACjB1X,MAAKmc,OAAmB2G;;;IAG5B,OAAOf;AACT;EAKAO,KAAAA;IACE,KAAK,IAAM7E,KAASzd,MAAK6d,EAAU;MAAEO,aAAY;QAAS;MACxD,IAAMmC,IAAIvgB,MAAKwc,EAASiB;MACxB,IAAIzd,MAAKsd,EAAmBiD;QAC1BA,EAAEoC,kBAAkBvI,MAAM,IAAIjN,MAAM;aAC/B;QACL,IAAMqQ,IAAIxd,MAAKuc,EAASkB;QACxB,IAAIzd,MAAKid;UACPjd,MAAKkc,IAAWqE,GAAQ/C,GAAQ;;QAElC,IAAIxd,MAAKmd;UACPnd,MAAK6c,GAAWpN,KAAK,EAAC8Q,GAAQ/C,GAAQ;;;;IAK5Cxd,MAAKsc,EAAQgG;IACbtiB,MAAKwc,EAASjB,UAAK/Q;IACnBxK,MAAKuc,EAAShB,UAAK/Q;IACnB,IAAIxK,MAAKgd,KAAShd,MAAK+c,GAAS;MAC9B/c,MAAKgd,EAAMzB,KAAK;MAChBvb,MAAK+c,EAAQxB,KAAK;;IAEpB,IAAIvb,MAAK8c;MACP9c,MAAK8c,EAAOvB,KAAK;;IAEnBvb,MAAK0c,IAAQ;IACb1c,MAAK2c,IAAQ;IACb3c,MAAK4c,EAAMrc,SAAS;IACpBP,MAAKqc,IAAkB;IACvBrc,MAAKqb,IAAQ;IACb,IAAIrb,MAAKmd,KAAoBnd,MAAK6c,GAAW;MAC3C,IAAMgG,IAAK7iB,MAAK6c;MAChB,IAAIiG;MACJ,OAAQA,IAAOD,GAAInL;QACjB1X,MAAKmc,OAAmB2G;;;AAG9B;;;;;;;AC3wEF,IAAMqC,IAAa;EAClB,IAAI;EACJ,IAAI;EACJ,KAAK;EACL,KAAK;EACL,KAAK;EACL,MAAM;;;AAGP,IAAMC,IAAc;EACnB,IAAI;EACJ,IAAI;EACJ,KAAK;EACL,KAAK;EACL,KAAK;EACL,MAAM;;;AAsDPC,EAAcC,UAlDd,SAASC,MAAMC;EAEd,IAAIC,IAAOhiB,OAAO2hB,EAAY;EAC9B,IAAIM,KAAa;EAEjB,KAAK,IAAItb,IAAI,GAAGA,IAAIob,EAAOjlB,QAAQ6J,KAAK;IACvC,IAAIub,IAAgBH,EAAOjjB,WAAW6H;IAGtC,IAAIub,IAAgB,QAASD,GAAY;MAExCC,KADAH,IAASI,SAASC,mBAAmBL,KACdjjB,WAAW6H;MAClCsb,KAAa;AACb;IAEDD,KAAQE;IACRF,MAASA,KAAQ,MAAMA,KAAQ,MAAMA,KAAQ,MAAMA,KAAQ,MAAMA,KAAQ;AACzE;EAED,OAAOA,MAAS;AACjB;;AA+BAJ,EAAAC,QAAAQ,SA7BA,SAASA,OAAON,IAAQnK,MAACA,IAAO,MAAM;EACrC,KAAK8J,EAAW9J;IACf,MAAM,IAAIlO,MAAM;;EAGjB,IAAIsY,IAAOL,EAAY/J;EACvB,IAAM0K,IAAWZ,EAAW9J;EAG5B,IAAIqK,KAAa;EAEjB,KAAK,IAAItb,IAAI,GAAGA,IAAIob,EAAOjlB,QAAQ6J,KAAK;IACvC,IAAIub,IAAgBH,EAAOjjB,WAAW6H;IAGtC,IAAIub,IAAgB,QAASD,GAAY;MAExCC,KADAH,IAASI,SAASC,mBAAmBL,KACdjjB,WAAW6H;MAClCsb,KAAa;AACb;IAEDD,KAAQO,OAAOL;IACfF,IAAOO,OAAOC,QAAQ5K,GAAMoK,IAAOM;AACnC;EAED,OAAON;AACR;;;;AClEO,IAAMS,IAAoB;;AAEjC,IAAMC,qBAAsBrf,KACnBA,EAAK+L,0BACR/L,EAAKiM,MAAMC,MAAKlM,KAAQA,EAAKsf,QAAQxmB,QAAEA,GAACymB,UAAUnc,YAAWpD,IAC7DA;;AAGN,IAAMwf,yBACJ9a;EAEA,IAAIsC,IAAgBtC;EACpB,IAAM+K,IAAO,IAAI1F;EACjB,OAAO/C,EAAK4G,WAAW6B,EAAK/E,IAAI1D,IAAO;IACrCyI,EAAKG,IAAI5I;IACT,IAAIlO,QAAEA,GAAC2mB,QAAQzY;MACb;WACK,IAAIlO,QAAEA,GAAC+U,sBAAuB7G,IAAOA,EAAK4G;MAC/C,OAAO5G;;AAEX;AAAA;;AAGF,IAAM0Y,6BAA6BA,CACjC1Y,GACA2Y,GACAC,GACA5X,GACAuE,MAEOvF,EAAK+H,SAAS9F,SAAQgG;EAC3B,IAAInW,QAAEA,GAAC+mB,oBAAoB5Q;IAAU,OAAO;;EAE5C,IAAM8B,IAAM,KAAI4O;EAChB,OAAO7mB,QAAAA,GAAG2R,aAAawE,EAAQ3R,QAC3BwiB,WAAW7Q,EAAQ3R,MAAMyT,GAAK6O,GAAW5X,GAAQuE,IAAM,KACvDzT,QAAEA,GAACinB,uBAAuB9Q,EAAQ3R,QAClC0iB,sBAAsB/Q,EAAQ3R,MAAMyT,GAAK6O,GAAW5X,GAAQuE,KAC5DmT,2BAA2BzQ,EAAQ3R,MAAMyT,GAAK6O,GAAW5X,GAAQuE;AAAK;;AAI9E,IAAMyT,wBAAwBA,CAC5BhZ,GACA2Y,GACAC,GACA5X,GACAuE;EAEA,IAAM0T,IAAU;EAAG,IAAAC,QAAAA;IAEjB,IAAIpnB,WAAGinB,uBAAuBI,EAAQ7iB,OAAO;MAC3C,IAAMyT,IAAM,KAAI4O;MAChB,IACEQ,EAAQC,iBACPT,EAAY7W,SAASqX,EAAQC,aAAa3T,YAC3C;QACA,IAAM4T,IAAS,KAAItP,GAAKoP,EAAQC,aAAa3T,YAAW6T,KAAK;QAC7D,IAAIV,EAAU1T,MAAKC,KAAKA,EAAElR,WAAWolB;UACnCtP,EAAIpI,KAAKwX,EAAQC,aAAa3T;;AAElC;MACA,IAAM8T,IAAiBP,sBACrBG,EAAQ7iB,MACRyT,GACA6O,GACA5X,GACAuE;MAGF0T,EAAQtX,QAAQ4X;AACjB,WAAM,IAAIznB,QAAAA,GAAG2R,aAAa0V,EAAQ7iB,OAAO;MACxC,IAAMyT,IAAM,KAAI4O;MAChB,IACEQ,EAAQC,iBACPT,EAAY7W,SAASqX,EAAQC,aAAa3T,YAC3C;QACA,IAAM4T,IAAS,KAAItP,GAAKoP,EAAQC,aAAa3T,YAAW6T,KAAK;QAC7D,IAAIV,EAAU1T,MAAKC,KAAKA,EAAElR,WAAWolB;UACnCtP,EAAIpI,KAAKwX,EAAQC,aAAa3T;;AAElC,aAAO;QACL,IAAM4T,IAAS,KAAItP,GAAKoP,EAAQ7iB,KAAKmP,YAAW6T,KAAK;QACrD,IAAIV,EAAU1T,MAAKC,KAAKA,EAAElR,WAAWolB;UACnCtP,EAAIpI,KAAKwX,EAAQ7iB,KAAKmP;;AAE1B;MAEA,IAAM+T,IAAcV,WAClBK,EAAQ7iB,MACRyT,GACA6O,GACA5X,GACAuE,IACA;MAGF0T,EAAQtX,QAAQ6X;AAClB;;EAhDF,KAAK,IAAML,KAAWnZ,EAAK+H;IAAQmR;;EAmDnC,OAAOD;AAAO;;AAGhB,IAAMQ,IAAe,IAAI1W,IAAI,EAC3B,OACA,UACA,WACA,UACA,SACA,QACA,QACA,WACA;;AAGF,IAAM2W,0BAA0BA,CAC9BC,GACAC,GACAhB,GACA5X,GACAuE;EAEA,IAAMsU,IACJ/nB,QAAEA,GAACsS,2BAA2BuV,EAAIvW,eAClCqW,EAAa/V,IAAIiW,EAAIvW,WAAW9M,KAAK4Q;EACvCwE,QAAQoO,IAAI,iBAAiBD,GAAWF,EAAII;EAC5C,IAAIF,GAAW;IAEb,IAAMG,IAAkC,aADvBL,EAAIvW,WACK9M,KAAK4Q;IAC/B,IAAI+S,IACFN,EAAIzW,UAAU;IAEhB,IAAMqT,IAAM;IACZ,IAAIzkB,QAAEA,GAACmR,iBAAiB0W,EAAI/S,OAAOA,SAAS;MAC1C,IAAMsT,IAAeR,wBACnBC,EAAI/S,OAAOA,QACXgT,GACAhB,GACA5X,GACAuE;MAEF,IAAI2U,EAAaznB;QACf8jB,EAAI5U,QAAQuY;;AAEhB;IAEA,IAAID,KAAQnoB,QAAAA,GAAG2R,aAAawW,IAAO;MAEjC,IAAMpW,IAAU0B,EAAKY,gBAAgBgC,aAAc6B;MAEnD,IAAMW,IAAc9G,EAAQsW,oBAAoBF,IAAOG;MACvD,IAAIzP,KAAe7Y,QAAAA,GAAGuoB,sBAAsB1P;QAC1CsP,IAAOtP;aACF,IACLA,KACA7Y,WAAG+U,sBAAsB8D,MACzBA,EAAY1D;QAEZgT,IAAOtP,EAAY1D;;AAEvB;IAEA,IACEgT,MACCnoB,QAAEA,GAACuoB,sBAAsBJ,MACxBnoB,QAAEA,GAACqR,qBAAqB8W,MACxBnoB,QAAEA,GAACuR,gBAAgB4W,KACrB;MACA,IAAMK,IAAQL,EAAKM,WAAWP,IAAW,IAAI;MAC7C,IAAIM,GAAO;QACT,IAAME,IAAe1B,WACnBwB,EAAMhkB,MACNsjB,GACAhB,GACA5X,GACAuE,IACA;QAGF,IAAIiV,EAAa/nB;UACf8jB,EAAI5U,QAAQ6Y;;AAEhB;AACF;IAEA,OAAOjE;AACT;EAEA,OAAO;AAAE;;AAGX,IAAMuC,aAAaA,CACjB9Y,GACA2Y,GACAC,GACA5X,GACAuE,GACAkV;EAEA,IAAI3oB,QAAEA,GAACinB,uBAAuB/Y;IAC5B,OAAOgZ,sBAAsBhZ,GAAM2Y,GAAaC,GAAW5X,GAAQuE;SAC9D,IAAIzT,QAAEA,GAAC4oB,sBAAsB1a;IAClC,OAAO0Y,2BACL1Y,GACA2Y,GACAC,GACA5X,GACAuE;;EAIJ,IAAI0T,IAAoB;EAExB,IAAM0B,IAAapV,EAAKY,gBAAgByU,wBACtC5Z,EAAOyF,UACPzG,EAAKqG;EAGP,KAAKsU;IAAY,OAAO1B;;EAuIxB,OAlIAA,IAAU0B,EAAW1Y,SAAQ0X;IAE3B,IAAIA,EAAIlT,aAAazF,EAAOyF;MAAU,OAAO;;IAG7C,IACEzG,EAAKqG,cAAcsT,EAAIhT,SAASjJ,SAChCsC,EAAKsI,YAAYqR,EAAIhT,SAASjJ,QAAQic,EAAIhT,SAASlU;MAEnD,OAAO;;IAET,IAAIooB,IAAWnU,SAAS1F,GAAQ2Y,EAAIhT,SAASjJ;IAC7C,KAAKmd;MAAU,OAAO;;IAEtB,IAAMjB,IAAY,KAAIjB;IAQtBjN,QAAQoO,IAAI,iBAAiBe,EAASd;IAAe,IAuGpDe,GAvGoDC,SAAAA;MAUnD,KACGN,MACA3oB,QAAEA,GAACkpB,kBAAkBH,MAAa/oB,WAAGuR,gBAAgBwX,KACtD;QAGA,IAAMxB,IAASO,EAAUN,KAAK;QACyC,OAAA;UAAA7G,GAAlDmG,EAAUtX,QAAO6D,KAAKA,EAAElR,WAAWolB,IAAS;;AAElE,aAAM,IAAIvnB,QAAEA,GAAC+U,sBAAsBgU;QAAW,OAAA;UAAApI,GACtCqG,WACL+B,EAASvkB,MACTsjB,GACAhB,GACA5X,GACAuE,IACA;;aAEG,IACLzT,QAAAA,GAAG2R,aAAaoX,OACfjB,EAAU9X,SAAS+Y,EAAS3T,OAC7B;QACA,IAAMmS,IAAS,KAAIO,GAAWiB,EAAS3T,OAAMoS,KAAK;QAClD,IAAIV,EAAU1T,MAAKC,KAAKA,EAAElR,WAAWolB,IAAS;UAC5CO,EAAUjY,KAAKkZ,EAAS3T;;AAE3B,aAAM,IACLpV,QAAAA,GAAGsS,2BAA2ByW,MACP,SAAvBA,EAASvkB,KAAK4Q,QACdpV,QAAEA,GAACmR,iBAAiB4X,EAASjU;QAE7BiU,IAAWA,EAASjU;aACf,IACL9U,QAAEA,GAACsS,2BAA2ByW,MAC9BpB,EAAa/V,IAAImX,EAASvkB,KAAK4Q,SAC/BpV,QAAEA,GAACmR,iBAAiB4X,EAASjU,SAC7B;QACA,IAAMqU,IAAiBJ,EAASjU;QAChC,IAAM2P,IAAM;QACZ,IAAM2E,IACmB,WAAvBL,EAASvkB,KAAK4Q,QAA0C,YAAvB2T,EAASvkB,KAAK4Q;QACjDwE,QAAQoO,IAAI,iBAAiBe,EAASvkB,KAAK4Q;QAC3C,IAAMiU,IAAiBzB,wBACrBuB,GACArB,GACAhB,GACA5X,GACAuE;QAEFmG,QAAQoO,IAAI,iBAAiBqB,EAAe1oB;QAC5C,IAAI0oB,EAAe1oB;UACjB8jB,EAAI5U,QAAQwZ;;QAGd,IAAIrpB,QAAAA,GAAG+U,sBAAsBoU,EAAerU,YAAYsU,GAAe;UACrE,IAAME,IAAStC,WACbmC,EAAerU,OAAOtQ,MACtBsjB,GACAhB,GACA5X,GACAuE,IACA;UAEFgR,EAAI5U,QAAQyZ;AACd;QAAC,OAAA;UAAA3I,GAEM8D;;AACR,aAAM,IACLzkB,QAAEA,GAACsS,2BAA2ByW,OAC7BjB,EAAU9X,SAAS+Y,EAASvkB,KAAK4Q,OAClC;QACA,IAAMmS,IAAS,KAAIO,GAAWiB,EAASvkB,KAAK4Q,OAAMoS,KAAK;QACvD,IAAIV,EAAU1T,MAAKC,KAAKA,EAAElR,WAAWolB;UACnCO,EAAUjY,KAAKkZ,EAASvkB,KAAK4Q;;AAEjC,aAAO,IACLpV,QAAEA,GAACupB,0BAA0BR,MAC7B/oB,QAAAA,GAAGsT,gBAAgByV,EAASS,wBAC3B1B,EAAU9X,SAAS+Y,EAASS,mBAAmBpU,OAChD;QACA,IAAMmS,IAAS,KAAIO,GAAWiB,EAASS,mBAAmBpU,OAAMoS,KAC9D;QAEF,IAAIV,EAAU1T,MAAKC,KAAKA,EAAElR,WAAWolB;UACnCO,EAAUjY,KAAKkZ,EAASS,mBAAmBpU;;AAE/C;MAEA,IAAIpV,WAAGypB,oBAAoBV,EAASjU;QAClCiU,IAAWA,EAASjU,OAAOA;;QAE3BiU,IAAWA,EAASjU;;AAEvB;IAtGD,OACE9U,WAAG2R,aAAaoX,MAChB/oB,QAAAA,GAAGsS,2BAA2ByW,MAC9B/oB,QAAEA,GAACupB,0BAA0BR,MAC7B/oB,QAAEA,GAAC+U,sBAAsBgU,MACzB/oB,QAAAA,GAAGqX,mBAAmB0R,MACtB/oB,QAAAA,GAAGkpB,kBAAkBH,MACrB/oB,QAAAA,GAAGuR,gBAAgBwX;MAAS,IAAAC,IAAAC;QAAA,OAAAD,EAAArI;;;IAiG9B,OAAOmH,EAAUN,KAAK;AAAI;AAGd;;AC3VT,IAAMkC,4BAA4BA,CACvCxa,GACAuE;EAKA,IAAMkW,IJ4SD,SAASC,eACdrT;IAEA,OAAOA,EAAWsT,WAAWra,OAAOxP,QAAEA,GAAC8pB;AACzC,GIhTkBF,CAAe1a;EAC/B,IAAM0D,IAAca,EAAKY,gBAAgBgC,cAAc6B;EAEvD,IAAM6R,IAGF,CAAA;EAEJ,KAAKnX;IAAa,OAAOmX;;EAEzB,IAAIJ,EAAQhpB;IACVgpB,EAAQjS,SAAQsS;MACd,KAAKA,EAAIC;QAAc;;MAEvB,IAAID,EAAIC,aAAazlB,MAAM;QACzB,IAAM4P,IAAcX,EAAKY,gBAAgBC,wBACvCpF,EAAOyF,UACPqV,EAAIC,aAAazlB,KAAK+P;QAExB,IAAMC,IAAMJ,KAAeA,EAAY;QACvC,IAAII,GAAK;UACP,IAAIA,EAAIG,SAAS3E,SAAS;YAAiB;;UAE3C,IAAMmH,IAAiBzC,UAAUjB,GAAMe,EAAIG;UAC3C,KAAKwC;YAAgB;;UAQrB,IAAM+S,IANqBC,qBACzBhT,GACAvE,GACAa,GAG+BS,KAAIgD,KAAYA,EAAS1S,KAAKjB;UAC/D,IAAMic,IAAMwK,EAAII,gBAAgBzW;UAChC,IAAI0W,IAAiBN,EAA2BvK;UAChD,IAAI0K,EAAMvpB,UAAU0pB;YAClBA,EAAetc,YAAYsc,EAAetc,UAAUjD,OAAOof;iBACtD,IAAIA,EAAMvpB,WAAW0pB;YAC1BN,EAA2BvK,KAAO6K,IAAiB;cACjDze,OAAOoe,EAAII,gBAAgB7V;cAC3B5T,QAAQqpB,EAAII,gBAAgBzW,UAAUhT;cACtCoN,WAAWmc;;;AAGjB;AACF;MAEA,IACEF,EAAIC,aAAaK,iBACjBtqB,QAAAA,GAAGuqB,kBAAkBP,EAAIC,aAAaK,gBACtC;QACA,IAAMlW,IAAcX,EAAKY,gBAAgBC,wBACvCpF,EAAOyF,UACPqV,EAAIC,aAAaK,cAAc/V;QAEjC,IAAMC,IAAMJ,KAAeA,EAAY;QACvC,IAAII,GAAK;UACP,IAAIA,EAAIG,SAAS3E,SAAS;YAAiB;;UAE3C,IAAMmH,IAAiBzC,UAAUjB,GAAMe,EAAIG;UAC3C,KAAKwC;YAAgB;;UAOrB,IAAM+S,IALqBC,qBACzBhT,GACAvE,GACAa,GAE+BS,KAAIgD,KAAYA,EAAS1S,KAAKjB;UAC/D,IAAMic,IAAMwK,EAAII,gBAAgBzW;UAChC,IAAI0W,IAAiBN,EAA2BvK;UAChD,IAAI0K,EAAMvpB,UAAU0pB;YAClBA,EAAetc,YAAYsc,EAAetc,UAAUjD,OAAOof;iBACtD,IAAIA,EAAMvpB,WAAW0pB;YAC1BN,EAA2BvK,KAAO6K,IAAiB;cACjDze,OAAOoe,EAAII,gBAAgB7V;cAC3B5T,QAAQqpB,EAAII,gBAAgBzW,UAAUhT;cACtCoN,WAAWmc;;;AAGjB;AACF,aAAO,IACLF,EAAIC,aAAaK,iBACjBtqB,QAAAA,GAAGwqB,sBAAsBR,EAAIC,aAAaK;QAE1CN,EAAIC,aAAaK,cAAcrU,SAASyB,SAAQS;UAC9C,IAAM/D,IAAcX,EAAKY,gBAAgBC,wBACvCpF,EAAOyF,UACPwD,EAAG5D;UAEL,IAAMC,IAAMJ,KAAeA,EAAY;UACvC,IAAII,GAAK;YACP,IAAIA,EAAIG,SAAS3E,SAAS;cAAiB;;YAE3C,IAAMmH,IAAiBzC,UAAUjB,GAAMe,EAAIG;YAC3C,KAAKwC;cAAgB;;YAOrB,IAAM+S,IALqBC,qBACzBhT,GACAvE,GACAa,GAE+BS,KAC/BgD,KAAYA,EAAS1S,KAAKjB;YAE5B,IAAMic,IAAMwK,EAAII,gBAAgBzW;YAChC,IAAI0W,IAAiBN,EAA2BvK;YAChD,IAAI0K,EAAMvpB,UAAU0pB;cAClBA,EAAetc,YAAYsc,EAAetc,UAAUjD,OAAOof;mBACtD,IAAIA,EAAMvpB,WAAW0pB;cAC1BN,EAA2BvK,KAAO6K,IAAiB;gBACjDze,OAAOoe,EAAII,gBAAgB7V;gBAC3B5T,QAAQqpB,EAAII,gBAAgBzW,UAAUhT;gBACtCoN,WAAWmc;;;AAGjB;AAAA;;AAEJ;;EAIJ,OAAOH;AAA0B;;AAGnC,SAASI,qBACP1V,GACA7B,GACAa;EAEA,IAAI1F,IAA2C;EAC/C,IAAM0c,IAAkBrS,uBAAuB3D,GAAKhB,IAAM;EAE1D,IAAMiX,IAAS9X,EAAYyV,oBAAoB5T;EAC/C,KAAKiW;IAAQ,OAAO;;EAGpB,IAAMC,IADU/X,EAAYgY,mBAAmBF,GACjBxW,KAAI2W,KAAQA,EAAKrmB;EACjCimB,EAAgBxa,MAAMT,QAAO6D;IACzC,IAAIyB,IAASzB,EAAEnF,KAAK4G;IACpB,OACEA,MACC9U,QAAEA,GAAC8qB,aAAahW,OAChB9U,QAAEA,GAAC+U,sBAAsBD;MAE1BA,IAASA,EAAOA;;IAGlB,IAAI9U,QAAEA,GAAC+U,sBAAsBD;MAC3B,OAAO6V,EAAc3a,SAAS8E,EAAOtQ,KAAKmP;;MAE1C,QAAO;;AACT,MAGI+D,SAAQxJ;IACZ,IAAMkH,IAAO7B,gBAAgBrF,EAAKA,MAAMuG,EAAIE,UAAUlB,GAAMC;IAC5D;MACE,IAAMqX,IAAS1c,EAAKA,MAAC+G,GAAM;QAAEqC,aAAY;;MACzC,IAAIsT,EAAO3W,YAAYtO,OAAMuN,KAAKA,EAAEjQ,SAASe,OAAKC;QAChD2J,IAAYA,EAAUjD,OAAOigB,EAAO3W;;AAEvC,MAAC,OAAOrD;MACP;AACF;AAAA;EAGF,OAAOhD;AACT;;ACfO,IAAMid,0BAA0BA,CACrCvX,GACAwX,GACAC,GACAC;EAEA,IAAIA,GAAqB;IACvB,IAAMpd,IAA2C;IACjDgK,oBAAoBoT,GAAqBpd,GAAW0F;IACpD,IAAI2B,IAAO7B,gBACT0X,GACAC,GACAzX,GACAC;IACF,IAAMqX,IAAS1c,MAAM+G;IACrB,IAAMuB,IAAO,IAAI1F;IACjB,KAAK,IAAM0G,KAAcoT,EAAO3W;MAC9B,IACEuD,EAAWvU,SAASe,MACnBwS,EAAK/E,IAAI+F;QAEVyT,oCAAoCzT;;;IAIV5J,EAC3BmG,KAAIgD;MACHkU,oCAAoClU;MACpC,OAAO/I,MAAM+I;AAAS,QAEvB1H,QAAO,CAAC0H,GAAU2G,GAAOwN,MAAUA,EAAMC,QAAQpU,OAAc2G,IAE5CnG,SAAQ6T;MAC5BnW,IAAQ,GAAEA,QAAWmW;AAAoB;IAE3C,IAAMC,IAAWrd,MAAME,MAAM+G;IAC7B,OAAOqW,EAAAA,WAAW,UAAUrlB,OAAOolB,GAAUE,OAAO;AACtD,SAAO;IACL,IAAMvU,IAAiBzC,UAAUjB,GAAMyX;IACvC,KAAQnd,WAAAA,KAAcqK,uBAAuBjB,GAAgB1D;IAE7D,IAAM2B,IAAO7B,gBACX0X,GACAC,GACAzX,GACAC;IAEF,IAAMqX,IAAS1c,MAAM+G;IACrB,IAAMuB,IAAO,IAAI1F;IACjB,KAAK,IAAM0G,KAAcoT,EAAO3W;MAC9B,IACEuD,EAAWvU,SAASe,MACnBwS,EAAK/E,IAAI+F;QAEVyT,oCAAoCzT;;;IAIxC,IAAMgU,IAAU,IAAI1a;IACpB2a,MAAMb,GAAQ;MACZvlB,oBAAoB0I;QAClBH,EAAU8B,KAAK3B;AAAK;MAEtB5I,gBAAgB4I;QACdyd,EAAQ7U,IAAI5I,EAAK1J,KAAKjB;AAAM;;IAIhC,IAAIsoB,IAAezW;IACnB,IAAM0W,IAAU,IAAI7a;IACpB,IAAM8a,IAAmB,KAAIJ;IAE7B,IAAIK;IACJ,OAAQA,IAAaD,EAAiBjU,SAAU;MAC9CgU,EAAQhV,IAAIkV;MACZ,IAAMT,IAAqBxd,EAAUqF,MACnCC,KAAKA,EAAE7O,KAAKjB,UAAUyoB;MAExB,KAAKT,GAAoB;QACvB9X,EAAKwY,QAAQC,eAAeC,OAAO1Y,KAChC,kDAAiDuY;QAEpD;AACF;MAEAZ,oCAAoCG;MAEpCK,MAAML,GAAoB;QACxBjmB,gBAAgB4I;UACd,KAAK4d,EAAQla,IAAI1D,EAAK1J,KAAKjB;YACzBwoB,EAAiBlc,KAAK3B,EAAK1J,KAAKjB;;AAAM;;MAI5CsoB,IAAgB,GAAEA,QAAmB1d,MAAMod;AAC7C;IAEA,OAAOE,aAAW,UACfrlB,OAAO+H,MAAME,MAAMwd,KACnBH,OAAO;AACZ;AAAA;;AAGK,IAAMU,oCAAoCA,CAC/CC,GACA7Y,GACAC;EAIA,IAAMoV,IAAapV,EAAKY,gBAAgByU,wBACtCtV,GACA6Y,EAAUC,SAAS/X;EAGrB,KAAKsU;IAAY,OAAO;MAAE3a,MAAM;MAAMsF;;;EAEtC,IAAMZ,IAAca,EAAKY,gBAAgBgC,cAAc6B;EACvD,IAAIhF,IAAkC;EACtC,IAAIgY,IAAgB1X;EACpBqV,EAAWnR,SAAQmQ;IACjB,IAAI3U;MAAO;;IAEX,IAAMhE,IAASwF,UAAUjB,GAAMoU,EAAIlT;IACnC,KAAKzF;MAAQ;;IACb,IAAMqd,IAAY3X,SAAS1F,GAAQ2Y,EAAIhT,SAASjJ;IAChD,KAAK2gB;MAAW;;IAEhB,IACEvsB,QAAEA,GAAC+U,sBAAsBwX,EAAUzX,WACnCyX,EAAUzX,OAAOK,eACjBoC,cAAqBgV,EAAUzX,OAAOK,aAAavC,IACnD;MACAM,IAAQqZ,EAAUzX,OAAOK;MACzB+V,IAAgBrD,EAAIlT;AACtB;AAAA;EAGF,OAAO;IAAEzG,MAAMgF;IAAOM,UAAU0X;;AAAe;;AAG1C,IAAMsB,uCAAuCA,CAClDC,GACAjZ,GACAC;EAEA,IAAIzT,QAAEA,GAAC2R,aAAa8a,IAAuB;IAGzC,IAAM5D,IAAapV,EAAKY,gBAAgByU,wBACtCtV,GACAiZ,EAAqBlY;IAGvB,KAAKsU;MAAY,OAAO;QAAE3a,MAAM;QAAMsF;;;IAEtC,IAAMZ,IAAca,EAAKY,gBAAgBgC,cAAc6B;IACvD,IAAIhF,IAAkC;IACtC,IAAIgY,IAAgB1X;IACpBqV,EAAWnR,SAAQmQ;MACjB,IAAI3U;QAAO;;MAEX,IAAMhE,IAASwF,UAAUjB,GAAMoU,EAAIlT;MACnC,KAAKzF;QAAQ;;MACb,IAAMqd,IAAY3X,SAAS1F,GAAQ2Y,EAAIhT,SAASjJ;MAChD,KAAK2gB;QAAW;;MAEhB,IACEvsB,QAAEA,GAAC+U,sBAAsBwX,EAAUzX,WACnCyX,EAAUzX,OAAOK,eACjBoC,cAAqBgV,EAAUzX,OAAOK,aAAavC,IACnD;QACAM,IAAQqZ,EAAUzX,OAAOK;QACzB+V,IAAgBrD,EAAIlT;AACtB;AAAA;IAGF,OAAO;MAAEzG,MAAMgF;MAAOM,UAAU0X;;AAClC;IACE,OAAO;MAAEhd,MAAMue;MAAsBjZ;;;AACvC;;AAKF,IAAM4X,sCACJzT;EAECA,EAAgD+U,aAC/C/U,EAAW+U,YAAYld,QACrBmd,KAAsC,cAAzBA,EAAUnoB,KAAKjB;AAC7B;;AC1UL,IAAMqpB,IAAmB,IAAI3b,IAAI,EAC/B,YACA,UACA,WACA,aACA,oBACA,qBACA,aACA,YACA,YACA,aACA,uBACA,cACA,eACA,SACA,YACA;;AAMK,IAAM4b,IAA6B;;AACnC,IAAMC,IAA6B;;AACnC,IAAMC,IAA6B;;AACnC,IAAMC,IAA6B;;AACnC,IAAMC,IAAkB,EAPS,OACG,OACA,OF3CN,OEqDnC3G,GACAuG,GACAC,GACAC,GACAC;;AAGF,IAAME,IAAQ,IAAI9Q,SAAkC;EAElD+B,KAAK;EACLjD,KAAK;;;AAyQP,IAAMiS,iBAAiBA,CACrBje,IAEEe,UACAlC,eAQFL,GACA+F;EAEA,IAAMD,IAAWtE,EAAOyF;EACxB,IAAMxD,IAAmBsC,EAAK2Z,OAAOC,6BAA4B;EAEjE,IAAMC,IAAcrd,EACjBiE,KAAIqZ;IACH,IAAIrf,IAAOqf,EAAarf;IACxB,KACGiD,MACAnR,QAAEA,GAAC+T,gCAAgC7F,MAClClO,WAAGwtB,qBAAqBtf;MAE1B,IAAIlO,WAAGyS,2BAA2BvE,EAAK4G;QACrC5G,IAAOA,EAAK4G;;QAEZ;;;IAIJ,KAAQpB,cAAc0B,GAAIxB,eAAEA,KAAkBL,gBAC5CrF,GACAsF,GACAC;IAEF,IAAM5E,IAAQuG,EAAKtG,MAAM;IAEzB,IAAI2e,KAAe;IACnB,IAAIztB,WAAG0V,eAAexH,EAAK4G;MACzB,IAAI9U,QAAEA,GAAC0tB,sBAAsBxf,EAAK4G,OAAOA;QACvC2Y,KAAe;;WAEZ,IAAIztB,QAAAA,GAAG0tB,sBAAsBxf,EAAK4G;MACvC2Y,KAAe;;IAKjB,IAAIE,IACFzf,EAAKqG,cACJpD,IACG,IACCjD,EAAqCwE,IAAIiB,UAAUhT,UACnD8sB,IAAe,IAAI;IAC1B,IAAMG,IAAcD,IAAmBzf,EAAKyF,UAAUhT;IACtD,IAAIktB,IAAe,KAAI9f;IACvB,IAAIoD;MACF;QACE,IAAM2c,IAAoBzf,EAAKA,MAAC+G,GAAM;UACpCqC,aAAY;WACXrD,YAAY5E,QAAO6D,KAAKA,EAAEjQ,SAASe,EAAAA,KAAKC;QAC3CypB,IAAeA,EAAare,QAC1B6D,MACGya,EAAkBC,MACjBC,KACEA,EAAE5qB,SAASe,EAAAA,KAAKC,uBAChB4pB,EAAExpB,KAAKjB,UAAU8P,EAAE7O,KAAKjB;AAGlC,QAAE,OAAOwN,IAAI;;IAGf,IAAMkd,IACJV,EAAa7f,UAAUA,EAAOwgB,MAAMX,EAAa7f,UAC7CA,EAAOwgB,MAAMX,EAAa7f,SAASA,SACnCA,EAAO9K,SAAS8K;IAEtB,KAAKugB;MACH;;IAGF,IAAME,IAAqB3gB,eACzB4H,GACA6Y,QACArjB,QACAA,GACAijB,GAECre,QAAO4e;MACN,KAAKA,EAAK9gB,QAAQ0C,SAAS;QAAsB,QAAO;;MAExD,KAAO1C,KAAW8gB,EAAK9gB,QAAQwB,MAAM;MACrC,IAAMuf,IACJ/gB,KAAW,gCAAgCghB,KAAKhhB;MAClD,KAAK+gB;QAAS,QAAO;;MACrB,IAAME,IAAgBF,EAAQ;MAC9B,OAAOE,MAAkB3B,EAAiBhb,IAAI2c;AAAc,QAE7Dra,KAAIb;MACH,KAAMzH,OAAEA,GAAKC,KAAEA,KAAQwH,EAAE7E;MAIzB,IAAIggB,IAAYb,IAAmB/hB,EAAMG;MACzC,KAAK,IAAIvB,IAAI,GAAGA,KAAKoB,EAAMG,QAAQvB,IAAIqE,EAAMlO,QAAQ6J;QACnD,IAAIA,MAAMoB,EAAMG;UAAMyiB,KAAa5iB,EAAM/I;eACpC,IAAIgM,EAAMrE;UAAIgkB,KAAa3f,EAAMrE,GAAI7J;;;MAG5C,IAAI8tB,IAAUd,IAAmB9hB,EAAIE;MACrC,KAAK,IAAIvB,IAAI,GAAGA,KAAKqB,EAAIE,QAAQvB,IAAIqE,EAAMlO,QAAQ6J;QACjD,IAAIA,MAAMqB,EAAIE;UAAM0iB,KAAW5iB,EAAIhJ;eAC9B,IAAIgM,EAAMrE;UAAIikB,KAAW5f,EAAMrE,GAAI7J;;;MAG1C,IAAM+tB,IAAoB9a,EAAcR,MAAKC,KAEpCmb,KAAanb,EAAEoC,IAAI7J,SAAS6iB,KADpBpb,EAAEoC,IAAI7J,QAAQyH,EAAEoC,IAAI9U;MAIrC,IAAM+tB;QACJ,OAAO;aACFrb;UACHzH,OAAO8iB,EAAkBlZ,SAAS5J;UAClCjL,QAAQ+tB,EAAkBlZ,SAAS7U;;aAGrC,IAAI6tB,IAAYZ,GAAa;QAE3B,IAAM3Z,IAAkBL,EACrBpE,QAAO6D,KAAKA,EAAEoC,IAAI7J,QAAQyH,EAAEoC,IAAI9U,SAAS6tB,IACzCxgB,QACC,CAACC,GAAKkG,MAASlG,KAAOkG,EAAKsB,IAAI9U,SAASwT,EAAKqB,SAAS7U,UACtD;QAEJ6tB,KAAwBva;QACxBwa,KAAoBxa;QACpB,OAAO;aACFZ;UACHzH,OAAO4iB,IAAY;UACnB7tB,QAAQ8tB,IAAUD;;AAEtB;QACE,OAAO;aACFnb;UACHzH,OAAO4iB,IAAY;UACnB7tB,QAAQ8tB,IAAUD;;;AAGxB,QAEDhf,QAAO6D,KAAKA,EAAEzH,QAAQyH,EAAE1S,UAAUitB;IAErC;MACE,IAAM7C,IAAS1c,EAAKA,MAAC+G,GAAM;QAAEqC,aAAY;;MAEzC,IACEsT,EAAO3W,YAAY2Z,MAAK1a,KAAKA,EAAEjQ,SAASe,OAAKwqB,wBAC7C;QAIA,KAHW5D,EAAO3W,YAAYhB,MAC5BC,KAAKA,EAAEjQ,SAASe,OAAKwqB,uBAEfnqB;UACN2pB,EAAmBte,KAAK;YACtBvC,SAAS;YACT1B,OAAOsC,EAAKqG;YACZmF,MAvc6B;YAwc7B/Y,QAAQ4sB,EAAarf,KAAKyF,UAAUhT;YACpC6N,OAAO,CAAS;YAChBS,UAAU;;;AAGhB;AACF,MAAE,OAAO8B,IAAI;IAEb,OAAOod;AAAkB,MAE1BS,OACApf,OAAOsG;EAEV,IAAM+Y,IAAgBvB,EAAYpZ,KAChCka,MACG;IACCU,MAAM5f;IACNvO,QAAQytB,EAAKztB;IACbiL,OAAOwiB,EAAKxiB;IACZmjB,UACoB,MAAlBX,EAAKnf,WACDjP,QAAAA,GAAGgvB,mBAAmBze,UACtBvQ,WAAGgvB,mBAAmBzhB;IAC5BmM,MACuB,mBAAd0U,EAAK1U,OACR0U,EAAK1U,OACa,MAAlB0U,EAAKnf,WAjewB,QAFH;IAsehCggB,aAAab,EAAK9gB,QAAQwB,MAAM,MAAM;;EAI5C,IAAIqC,GAAkB;IACpB,IAAM+d,IHpL2BC,EACnCjgB,GACAe,GACAwD;MAEA,IAAM6Z,IAA+B;MAErC,MAD8B7Z,EAAK2Z,OAAOgC,mBAAmB;QACjC,OAAO9B;;MAInC,IAAM+B,IAAe,IAAIpe,IAAI,EAFA,MAAM,OAAO,iBACnBwC,EAAK2Z,OAAOiC,gBAAgB;MAEnD,IAAMtd,IAAU0B,EAAKY,gBAAgBgC,cAAc6B;MACnD,KAAKnG;QAAS;;MAEd;QACE9B,EAAMyH,SAAQxJ;UACZ,IAAMohB,IAAWphB,EAAKyF;UAGtB,IAAI2b,EAAStf,SAAS,eAAesf,EAAStf,SAAS;YACrD;;UAEF,IAAMuf,IAAsB7I,uBAAuBxY;UACnD,KAAKqhB;YAAqB;;UAE1B,IAAIC;UAEJ,IAAMtoB,IAAO6K,EAAQE,kBAAkB/D,EAAK4G;UAI5C,IAAI,YAAY5N,GAAM;YACpB,IAAMuoB,IAAiBvoB,EACpBwoB;YACHF,IACEC,KAAiBA,EAAc9uB,SAAS,IACpC8uB,EAAc,UACd7kB;AACR;UAEA,KAAK4kB,GAAU;YACb,IAAMG,IAAgBzoB,EAAKgL,YAAY;YACvC,IAAIyd,GAAe;cACjB,IAAIC,IAAU7d,EAAQiB,gBAAgB2c;cACtC,IAAIE,IACF3oB,EAAK4oB,oBAAoB;cAC3B,IAAIF,EAAQ3c;gBACV,KAAK,IAAM/L,KAAQ0oB,EAAQzc;kBAEzB,IADA0c,IAAgB3oB,EAAK4oB,oBAAoB,IACtB;oBACjBN,IAAWK,EAAcE;oBACzB;AACF;;;cAGJP,IAAWK,KAAiBA,EAAcE;AAC5C;AACF;UAEA,IAAMlH,IAAapV,EAAKY,gBAAgByU,wBACtC5Z,EAAOyF,UACP4a,EAAoB/qB,KAAK+P;UAG3B,KAAKsU;YAAY;;UAEjB,IAAMmH,IAAsB;UAC5B,IAAMC,IAAuB;UAC7B,IAAMC,IAAqB;UAC3B,IAAMC,IAAa,IAAIhR;UAMvByM,QAAMvd,EAAAA,MAAMH,EAAKyF,UAAU1R,MAAM,IAAI,KAAK;YACxCkD,OAAO;cACLirB,KAAAA,CAAMliB;gBACJ,IAAMmiB,IAAQniB,EAAKmiB,QAAQniB,EAAKmiB,MAAM9sB,QAAQ2K,EAAK1J,KAAKjB;gBACxD,IAAM+sB,IAAOL,EAAWtvB,SACnB,GAAEsvB,EAAWzI,KAAK,QAAQ6I,MAC3BA;gBAEJ,KAAKniB,EAAKqiB,iBAAiBlB,EAAazd,IAAI1D,EAAK1J,KAAKjB,QAAQ;kBAC5D2sB,EAASrgB,KAAKygB;kBACdH,EAAWxN,IAAI2N,GAAM;oBACnB1kB,OAAOsC,EAAK1J,KAAKoM,IAAKhF;oBACtBjL,QAAQuN,EAAK1J,KAAKoM,IAAK/E,MAAMqC,EAAK1J,KAAKoM,IAAKhF;;AAEhD,uBAAO,IAAIsC,EAAKqiB,cAAc;kBAC5BN,EAAWpgB,KAAKwgB;kBAChBF,EAAWxN,IAAI2N,GAAM;oBACnB1kB,OAAOsC,EAAK1J,KAAKoM,IAAKhF;oBACtBjL,QAAQuN,EAAK1J,KAAKoM,IAAK/E,MAAMqC,EAAK1J,KAAKoM,IAAKhF;;AAEhD;AACD;cACD4kB,KAAAA,CAAMtiB;gBACJ,IAAIA,EAAKqiB;kBACPN,EAAW9T;;AAEf;;;UAIJ0M,EAAWnR,SAAQmQ;YACjB,IAAIA,EAAIlT,aAAazF,EAAOyF;cAAU;;YAEtC,IAAM8b,IAAa7b,SAAS1F,GAAQ2Y,EAAIhT,SAASjJ;YACjD,KAAK6kB;cAAY;;YAEjB,IAAIA,EAAW3b,WAAWya;cAAqB;;YAE/C,IAAMmB,IAAe3e,EAAQ4e,kBAC3BF,GACAzwB,WAAG4wB,YAAYC;YAGjB,IAAIC;YACJ,KAAK,IAAIC,KAAeL,GAAc;cACpC,KAAKK,EAAYzI;gBAAkB;;cACnC,IAAI0I,IAAoBzK,mBACtBxU,EAAQiB,gBAAgB+d;cAE1B,IAAIvB,MAAawB,GAAmB;gBAClCF,IAAkBC;gBAClB;AACF;cAKA,IAAIC,EAAkBxK,QAAQxmB,WAAGymB,UAAUnc,QAAQ;gBACjD,IAAM2mB,IAAsBD,EAAkB9e,YAAY;gBAC1D,IAAI+e,GAAqB;kBACvBD,IAAoBjf,EAAQiB,gBAAgBie;kBAC5C,IAAIzB,MAAawB,GAAmB;oBAClCF,IAAkBC;oBAClB;AACF;AACF;gBAEA,IAAMG,IAAqBF,EAAkB9e,YAAY;gBACzD,IAAIgf,GAAoB;kBACtBF,IAAoBzK,mBAClBxU,EAAQiB,gBAAgBke;kBAE1B,IAAI1B,MAAawB,GAAmB;oBAClCF,IAAkBC;oBAClB;AACF;AACF;AACF;AACF;YAEA,IAAMzI,IAAmBwI,GAAiBxI;YAC1C,IAAI9jB;YACJ,IACE8jB,KACA,UAAUA,KACRA,EAAiB9jB,SAClBxE,QAAEA,GAAC2R,aAAa2W,EAAiB9jB,SAChCxE,QAAEA,GAACmxB,cAAc7I,EAAiB9jB;cAEpCA,IAAO8jB,EAAiB9jB;mBACnB;cAGL,IAAM+qB,IAAsB7I,uBAAuB+J;cACnD,IAAIlB;gBAAqB/qB,IAAO+qB,EAAoB/qB;;AACtD;YAEA,IAAIA,GAAM;cACR,IAAM8T,IAAS0O,WAAWxiB,GAAM,IAAI0rB,GAAUhhB,GAAQuE,IAAM;cAC5Duc,EAAUngB,QAAQyI;AACpB;AAAA;UAGF,KAAK0X,EAAUrvB;YACb;;UAGF,IAAMywB,IAASlB,EAAS1gB,QAAO6D,MAAM2c,EAAUhgB,SAASqD;UACxD,IAAMge,IAAyB,IAAIpgB;UACnC,IAAMqgB,IAAiD,CAAA;UACvD,IAAMC,IAAqB,IAAItgB;UAC/BmgB,EAAO1Z,SAAQ8Z;YACb,IAAM1iB,IAAQ0iB,EAAY1iB,MAAM;YAChCA,EAAMqN;YACN,IAAMsV,IAAc3iB,EAAM0Y,KAAK;YAG/B,IAFY2I,EAAW/P,IAAIqR,IAElB;cACPJ,EAAuBva,IAAI2a;cAC3B,IAAIH,EAAeG;gBACjBH,EAAeG,GAAc3a,IAAI0a;;gBAEjCF,EAAeG,KAAe,IAAIxgB,IAAI,EAACugB;;AAE3C;cACED,EAAmBza,IAAI0a;;AACzB;UAGFH,EAAuB3Z,SAAQga;YAC7B,IAAM9gB,IAAMuf,EAAW/P,IAAIsR;YAC3B,IAAMC,IAAeL,EAAeI;YACpCpE,EAAYzd,KAAK;cACfif,MAAM5f;cACNvO,QAAQiQ,EAAIjQ;cACZiL,OAAOsC,EAAKqG,aAAa3D,EAAIhF,QAAQ;cACrCmjB,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;cAChCmJ,MAAM4M;cACN2I,aAAc,YAAW,KAAI0C,IAC1Bzd,KAAIb,KAAM,IAAGA,OACbmU,KAAK;;AACR;UAGJ+J,EAAmB7Z,SAAQga;YACzB,IAAM9gB,IAAMuf,EAAW/P,IAAIsR;YAC3BpE,EAAYzd,KAAK;cACfif,MAAM5f;cACNvO,QAAQiQ,EAAIjQ;cACZiL,OAAOsC,EAAKqG,aAAa3D,EAAIhF,QAAQ;cACrCmjB,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;cAChCmJ,MAAM4M;cACN2I,aAAc,SAAQyC;;AACtB;AACF;AAEL,QAAC,OAAO3gB;QACP6I,QAAQtL,MAAM,iBAAiByC,EAAEzD,SAASyD,EAAE6gB;AAC9C;MAEA,OAAOtE;AAAW,MGvDd6B,CACEjgB,GACAe,EAAMiE,KAAIb,KAAKA,EAAEnF,QACjBuF,MACG;IAEP,KAAKyb;MAAkB,OAAOL;;IAE9B,OAAO,KAAIA,MAAkBK;AAC/B;IACE,OAAOL;;AACT;;;;;;iCNxNK,SAASgD,uBAAuB3jB;EACrC,OACElO,QAAAA,GAAGoS,oBAAoBlE,MACvBlO,QAAEA,GAAC8xB,QAAQ5jB,MACXlO,QAAAA,GAAGwtB,qBAAqBtf,MACxBlO,QAAEA,GAAC+xB,eAAe7jB;IAElBA,IAAOA,EAAK4G;;EAGd,OAAO5G;AACT;;2BAxBO,SAAS8jB,iBAAiB9jB;EAC/B,OACElO,QAAAA,GAAG+T,gCAAgC7F,MACnClO,QAAEA,GAAC8xB,QAAQ5jB,MACXlO,QAAAA,GAAGwtB,qBAAqBtf,MACxBlO,QAAEA,GAAC+xB,eAAe7jB;IAElBA,IAAOA,EAAK4G;;EAGd,OAAO5G;AACT;;;;;;;;;;;;;;gCMpQO,SAAS+jB,sBACdze,GACA9F,GACA+F;EAEA,IAAMtC,IAAmBsC,EAAK2Z,OAAOC,6BAA4B;EAEjE,IAAIne,IAASwF,UAAUjB,GAAMD;EAC7B,KAAKtE;IAAQ;;EAEb,IACEe,GADElC,IAA2C;EAK/C,IAAIoD,GAAkB;IACpB,IAAMmH,IAASF,uBAAuBlJ,GAAQuE;IAC9C1F,IAAYuK,EAAOvK;IACnBkC,IAAQqI,EAAOrI;AACjB;IACEA,INpEG,SAASiiB,2BACd3b;MAEA,IAAM+B,IAEF;OACJ,SAASlF,KAAKlF;QACZ,IACEqJ,aAAoBrJ,MACnBlO,WAAG+T,gCAAgC7F,MAClCqJ,aAAoBrJ,EAAK4G,SAC3B;UACAwD,EAAOzI,KAAK3B;UACZ;AACF;UACElO,QAAAA,GAAGyW,aAAavI,GAAMkF;;AAE1B,OACAA,CAAKmD;MACL,OAAO+B;AACT,KMgDY4Z,CAA2BhjB,GAAQgF,KAAIb,MAAM;MACnDnF,MAAMmF;MACN3F,QAAQ;;;EAIZ,IAAMykB,IAAQliB,EAAMiE,KAAI,EAAGhG;IACzB,KACGlO,QAAAA,GAAG+T,gCAAgC7F,MAClClO,QAAEA,GAACwtB,qBAAqBtf,QACzBiD;MAED,IAAInR,WAAGyS,2BAA2BvE,EAAK4G;QACrC5G,IAAOA,EAAK4G;;QAEZ;;;IAIJ,OAAOvB,gBAAgBrF,GAAMsF,GAAUC,GAAMC;AAAY;EAG3D,IAAM0e,IAAWzM,EACfxU,IACIjC,EAAOyE,YACL5F,EAAUmG,KAAIb,KAAKlF,MAAMkF,KAAImU,KAAK,OAClC9Z,EAAO2kB,UACTF,EAAM3K,KAAK,OAAO9Z,EAAO2kB;EAG/B,IAAIxD;EACJ,IAAI3B,EAAMtb,IAAIwgB;IACZvD,IAAgB3B,EAAM9M,IAAIgS;SACrB;IACLvD,IAAgB1B,eAAeje,GAAQ;MAAEe;MAAOlC;OAAaL,GAAQ+F;IACrEyZ,EAAMvK,IAAIyP,GAAUvD;AACtB;EAEA,IAAMyD,IACJ7e,EAAK2Z,OAAOkF,qCAAoC;EAClD,IAAIC,IAAuC;EAE3C,IAAIphB,GAAkB;IAKpB,IAAMqhB,IAJiB/Z,gCAAgCvJ,GAAQuE,GAK5DS,KAA0BhB;MACzB,KAAQhF,MAAMib,KAAmBjW;MACjC,KAAKiW,EAAesG,kBAAkBtG,EAAe/X,UAAU;QAC7D,OAAO;UACL2d,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;UAChCmJ,MAAMmT;UACNiC,MAAM5f;UACN+f,aAAa;UACbrjB,OAAOud,EAAe5U;UACtB5T,QAAQwoB,EAAe3S,WAAW2S,EAAe5U;;;MAIrD,IAAIgY,GAEF1E,GACAjc,GACAjL,GAHAuqB,IAAgB1X;MAIlB,IAAM6Y,IACJlD,EAAesG,iBAAiBtG,EAAesG,cAAc;MAC/D,IAAIpD,GAAW;QACbzgB,IAAQygB,EAAU9X;QAClB5T,IAAS0rB,EAAU7V,WAAW6V,EAAU9X;QAExC,KAAKvU,QAAEA,GAACyyB,gBAAgBpG;UACtB,OAAO;YACL0C,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;YAChCmJ,MAAMmT;YACNiC,MAAM5f;YACN+f,aACE;YACFrjB;YACAjL;;;QAGJ,KAAQuN,MAAMgF,GAAOM,UAAUmB,KAC7ByX,kCAAkCC,GAAW7Y,GAAUC;QACzD8Y,IAAYrZ;QACZgY,IAAgBvW;QAChBkT,IAAMwE,EAAU1Y;AACjB,aAAM,IAAIwV,EAAe/X,UAAU,IAAI;QACtCxF,IAAQud,EAAe/X,UAAU,GAAGmD;QACpC5T,IACEwoB,EAAe/X,UAAU,GAAGoF,WAC5B2S,EAAe/X,UAAU,GAAGmD;QAC9B,KACGvU,QAAAA,GAAG2R,aAAawX,EAAe/X,UAAU,QACzCpR,QAAAA,GAAGmR,iBAAiBgY,EAAe/X,UAAU;UAE9C,OAAO;YACL2d,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;YAChCmJ,MAAMmT;YACNiC,MAAM5f;YACN+f,aACE;YACFrjB;YACAjL;;;QAIJ,KAAQuN,MAAMgF,GAAOM,UAAUmB,KAC7B6X,qCACErD,EAAe/X,UAAU,IACzBoC,GACAC;QAEJ8Y,IAAYrZ;QACZgY,IAAgBvW;QAChBkT,IAAMsB,EAAe/X,UAAU,GAAGuC;AACpC;MAEA,KAAK4Y;QACH,OAAO;UACLwC,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;UAChCmJ,MAAMqT;UACN+B,MAAM5f;UACN+f,aAAc,4BAA2BpH;UACzCjc;UACAjL;;;MAIJ,IAAMwU,IAAcoX;MACpB,MACGpX,KACAnV,QAAEA,GAACmR,iBAAiBgE,MACpBA,EAAY/D,UAAU,MACtBpR,QAAAA,GAAGoS,oBAAoB+C,EAAY/D,UAAU;QAI9C,OAAO;UACL2d,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;UAChCmJ,MAAMqT;UACN+B,MAAM5f;UACN+f,aAAc,oBAAmBpH;UACjCjc;UACAjL;;;MAIJ,KAAKwoB,EAAe/X,UAAU;QAG5B,OAAO;UACL2d,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;UAChCmJ,MAAMoT;UACNgC,MAAM5f;UACN+f,aAAc;UACdrjB,OAAOud,EAAe/X,UAAU1O;UAChC/B,QAAQwoB,EAAe/X,UAAUvF,MAAMsd,EAAe/X,UAAU1O;;;MAIpE,IAAMmjB,IAAOsD,EAAe/X,UAAU,GAAGuC,UAAU1R,MAAM,IAAI;MAC7D,IAAI4jB,EAAK1jB,WAAW,YAAY;QAC9B,IAAMuwB,IAAgB1H,wBACpBvX,GACA0B,EAAY/D,UAAU,IACtB8Z,GACA/V,EAAY/D,UAAU,MACpBpR,QAAEA,GAACgW,yBAAyBb,EAAY/D,UAAU,MAChD+D,EAAY/D,UAAU,UACtBxG;QAEN,KAAK8nB;UAAe,OAAO;;QAG3B,IADsB,UAASA,QACV7M;UACnB,OAAO;YACLkJ,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;YAChCmJ,MAAMsT;YACN8B,MAAM5f;YACN+f,aAAc;YACdrjB,OAAOud,EAAe/X,UAAU1O;YAChC/B,QACEwoB,EAAe/X,UAAUvF,MAAMsd,EAAe/X,UAAU1O;;;AAGhE;MAEA,OAAO;AAAI,QAEZ8M,OAAOsG;IAEV+Y,EAAchf,QAAS2iB;AACzB;EAEA,IAAIrhB,KAAoBmhB,GAAkC;IACxD,IAAMK,IAA6BjJ,0BAA0Bxa,GAAQuE;IAErE,IAAMmf,IAAgB,IAAI3hB;IAC1BhB,EAAMyH,SAAQ,EAAGxJ;MACf;QACE,IAAM6c,IAAS1c,EAAKA,MAACH,EAAKyF,UAAU1R,MAAM,IAAI,IAAI;UAChDwV,aAAY;;QAEdmU,EAAAA,MAAMb,GAAQ;UACZzlB,gBAAgB4I;YACd0kB,EAAc9b,IAAI5I,EAAK1J,KAAKjB;AAAM;;AAGxC,QAAE,OAAOwN,IAAI;AAAA;IAGfzG,OAAOC,KAAKooB,GAA4Bjb,SAAQ0S;MAC9C,KACErc,WAAW8kB,GAAajnB,OACxBA,GAAKjL,QACLA,KACEgyB,EAA2BvI;MAC/B,IAAM0I,IAAmB5wB,MAAMkJ,KAC7B,IAAI6F,IAAI4hB,EAAcrjB,QAAO6D,MAAMuf,EAAchhB,IAAIyB;MAEvD,IAAIyf,EAAiBnyB;QACnB4xB,EAAoB1iB,KAAK;UACvBif,MAAM5f;UACNvO;UACAiL;UACAmjB,UAAU/uB,QAAAA,GAAGgvB,mBAAmBze;UAChCmJ,MF1T2B;UE2T3BuV,aAAc,6CAA4C6D,EAAiBtL,KACzE,aACO4C;;;AAEb;IAGF,OAAO,KAAIyE,MAAkB0D;AAC/B;IACE,OAAO1D;;AAEX;;wCDrTO,SAASkE,8BACdvf,GACA7R,GACA8R;EAEA,IAAMtC,IAAmBsC,EAAK2Z,OAAOC,6BAA4B;EACjE,IAAMza,IAAca,EAAKY,gBAAgBgC,cAAc6B;EACvD,KAAK/G;IAAkB;;EAEvB,IAAIjC,IAASwF,UAAUjB,GAAMD;EAC7B,KAAKtE;IAAQ;;EAEb,IAAMhB,IAAO0G,SAAS1F,GAAQvN;EAC9B,KAAKuM;IAAM;;EAEX,IAAIib,IAA0Bjb;EAO9B,IAAIlO,QAAEA,GAAC0Y,oBAAoByQ;IACzBA,IACEA,EAAexQ,gBAAgBC,aAAaxF,MAAKyF,KAE7C7Y,WAAG+U,sBAAsB8D,MACzBA,EAAY1D,eACZnV,QAAAA,GAAGmR,iBAAiB0H,EAAY1D,kBAE9BjH;SACH,IAAIlO,QAAEA,GAACgzB,0BAA0B7J;IACtCA,IACEA,EAAevQ,aAAaxF,MAAKyF,KAE7B7Y,WAAG+U,sBAAsB8D,MACzBA,EAAY1D,eACZnV,QAAAA,GAAGmR,iBAAiB0H,EAAY1D,kBAE9BjH;SACH,IACLlO,QAAEA,GAAC+U,sBAAsBoU,MACzBA,EAAehU,eACfnV,WAAGmR,iBAAiBgY,EAAehU;IAEnCgU,IAAiBA,EAAehU;;IAEhC,OAAOgU,MAAmBnpB,QAAAA,GAAGmR,iBAAiBgY;MAC5CA,IAAiBA,EAAerU;;;EAQpC,KAAKyC,oBAA2B4R,GAAgBvW;IAC9C;;EAGF,IAAI2Z,GACFrB,IAAgB1X;EAClB,IAAI2V,EAAesG,eAAe;IAChC,KAAOpD,KAAalD,EAAesG;IACnC,KAAKpD,MAAcrsB,QAAEA,GAACyyB,gBAAgBpG;MAAY;;IAClD,KAAQne,MAAMgF,GAAOM,UAAUmB,KAC7ByX,kCAAkCC,GAAW7Y,GAAUC;IACzD8Y,IAAYrZ;IACZgY,IAAgBvW;AACjB,SAAM,IAAIwU,EAAe/X,UAAU,IAAI;IACtC,KACGpR,QAAEA,GAAC2R,aAAawX,EAAe/X,UAAU,QACzCpR,QAAAA,GAAGmR,iBAAiBgY,EAAe/X,UAAU;MAE9C;;IACF,KAAQlD,MAAMgF,GAAOM,UAAUmB,KAC7B6X,qCACErD,EAAe/X,UAAU,IACzBoC,GACAC;IAEJ8Y,IAAYrZ;IACZgY,IAAgBvW;AAClB;EAEA,KAAK4X;IAAW;;EAEhB,IAAMpX,IAAcoX;EACpB,MACGpX,KACAnV,QAAEA,GAACmR,iBAAiBgE,MACpBA,EAAY/D,UAAU,MACtBpR,QAAAA,GAAGoS,oBAAoB+C,EAAY/D,UAAU;IAE9C;;EAGF,IAAMyU,IAAOmF,wBACXvX,GACA0B,EAAY/D,UAAU,IACtB8Z,GACA/V,EAAY/D,UAAU,MACpBpR,QAAEA,GAACgW,yBAAyBb,EAAY/D,UAAU,MAChD+D,EAAY/D,UAAU,UACtBxG;EAEN,IAAMqoB,IAAe9J,EAAe/X,UAAU;EAE9C,KAAK6hB;IAEH,OAAO;MACL9e,MAAM;QACJvI,OAAOud,EAAe/X,UAAU1O;QAChC/B,QAAQ;;MAEVuyB,aAAc,WAAUrN;;SAErB,IACL7lB,QAAEA,GAACsT,gBAAgB2f,MACnBA,EAAatf,cAAe,WAAUkS;IAGtC,OAAO;MACL1R,MAAM;QACJvI,OAAOqnB,EAAa1e;QACpB5T,QAAQsyB,EAAapnB,MAAMonB,EAAa1e;;MAE1C2e,aAAc,WAAUrN;;SAErB,IAAI7lB,QAAEA,GAAC2R,aAAashB;IAGzB,OAAO;MACL9e,MAAM;QACJvI,OAAOqnB,EAAa1e;QACpB5T,QAAQsyB,EAAapnB,MAAMonB,EAAa1e;;MAE1C2e,aAAc,WAAUrN;;;IAG1B;;AAEJ;;;;;;ejBzKO,SAASsN,KAAKC;EACnBpzB,QAAEA,KAAGozB,EAAQC;AACf;;;;;;;;;;;;;;", "x_google_ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 13, 14]}