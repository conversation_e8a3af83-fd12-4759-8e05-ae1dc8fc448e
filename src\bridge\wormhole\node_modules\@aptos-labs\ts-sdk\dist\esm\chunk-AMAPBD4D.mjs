import{c as s}from"./chunk-GBNAG7KK.mjs";import{w as i,x as m}from"./chunk-VHNX2NUR.mjs";import{b as n}from"./chunk-UOP7GBXB.mjs";async function b(t){let{aptosConfig:a,handle:e,data:o,options:r}=t;return(await n({aptosConfig:a,originMethod:"getTableItem",path:`tables/${e}/item`,params:{ledger_version:r?.ledgerVersion},body:o})).data}async function f(t){let{aptosConfig:a,options:e}=t,o={query:i,variables:{where_condition:e?.where,offset:e?.offset,limit:e?.limit,order_by:e?.orderBy}};return(await s({aptosConfig:a,query:o,originMethod:"getTableItemsData"})).table_items}async function T(t){let{aptosConfig:a,options:e}=t,o={query:m,variables:{where_condition:e?.where,offset:e?.offset,limit:e?.limit,order_by:e?.orderBy}};return(await s({aptosConfig:a,query:o,originMethod:"getTableItemsMetadata"})).table_metadatas}export{b as a,f as b,T as c};
//# sourceMappingURL=chunk-AMAPBD4D.mjs.map