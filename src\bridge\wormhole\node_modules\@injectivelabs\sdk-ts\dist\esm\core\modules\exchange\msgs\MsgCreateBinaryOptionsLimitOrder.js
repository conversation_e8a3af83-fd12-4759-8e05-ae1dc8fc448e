import { MsgBase } from '../../MsgBase.js';
import { amountToCosmosSdkDecAmount, numberToCosmosSdkDecString, } from '../../../../utils/numbers.js';
import snakecaseKeys from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Tx, InjectiveExchangeV1Beta1Exchange, } from '@injectivelabs/core-proto-ts';
const createLimitOrder = (params) => {
    const orderInfo = InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
    orderInfo.subaccountId = params.subaccountId;
    orderInfo.feeRecipient = params.feeRecipient;
    orderInfo.price = params.price;
    orderInfo.quantity = params.quantity;
    if (params.cid) {
        orderInfo.cid = params.cid;
    }
    const derivativeOrder = InjectiveExchangeV1Beta1Exchange.DerivativeOrder.create();
    derivativeOrder.marketId = params.marketId;
    derivativeOrder.orderInfo = orderInfo;
    derivativeOrder.orderType = params.orderType;
    derivativeOrder.margin = params.margin;
    derivativeOrder.triggerPrice = params.triggerPrice || '0';
    const message = InjectiveExchangeV1Beta1Tx.MsgCreateBinaryOptionsLimitOrder.create();
    message.sender = params.injectiveAddress;
    message.order = derivativeOrder;
    return InjectiveExchangeV1Beta1Tx.MsgCreateBinaryOptionsLimitOrder.fromPartial(message);
};
/**
 * @category Messages
 */
export default class MsgCreateBinaryOptionsLimitOrder extends MsgBase {
    static fromJSON(params) {
        return new MsgCreateBinaryOptionsLimitOrder(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            price: amountToCosmosSdkDecAmount(initialParams.price).toFixed(),
            margin: amountToCosmosSdkDecAmount(initialParams.margin).toFixed(),
            triggerPrice: amountToCosmosSdkDecAmount(initialParams.triggerPrice || 0).toFixed(),
            quantity: amountToCosmosSdkDecAmount(initialParams.quantity).toFixed(),
        };
        return createLimitOrder(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgCreateBinaryOptionsLimitOrder',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const order = createLimitOrder(params);
        const message = {
            ...snakecaseKeys(order),
        };
        return {
            type: 'exchange/MsgCreateBinaryOptionsLimitOrder',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgCreateBinaryOptionsLimitOrder',
            ...value,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const order = web3gw.order;
        const messageAdjusted = {
            ...web3gw,
            order: {
                ...order,
                order_info: {
                    ...order.order_info,
                    price: numberToCosmosSdkDecString(params.price),
                    quantity: numberToCosmosSdkDecString(params.quantity),
                },
                margin: numberToCosmosSdkDecString(params.margin),
                trigger_price: numberToCosmosSdkDecString(params.triggerPrice || '0'),
                order_type: InjectiveExchangeV1Beta1Exchange.orderTypeToJSON(params.orderType),
            },
        };
        return messageAdjusted;
    }
    toEip712() {
        const { params } = this;
        const amino = this.toAmino();
        const { value, type } = amino;
        const messageAdjusted = {
            ...value,
            order: {
                ...value.order,
                order_info: {
                    ...value.order?.order_info,
                    price: amountToCosmosSdkDecAmount(params.price).toFixed(),
                    quantity: amountToCosmosSdkDecAmount(params.quantity).toFixed(),
                },
                margin: amountToCosmosSdkDecAmount(params.margin).toFixed(),
                trigger_price: amountToCosmosSdkDecAmount(params.triggerPrice || '0').toFixed(),
            },
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgCreateBinaryOptionsLimitOrder',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgCreateBinaryOptionsLimitOrder.encode(this.toProto()).finish();
    }
}
