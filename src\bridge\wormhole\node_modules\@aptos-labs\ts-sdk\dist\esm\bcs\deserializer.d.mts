import { HexInput, Uint8, Uint16, Uint32, Uint64, Uint128, Uint256 } from '../types/types.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../utils/apiEndpoints.mjs';

/**
 * This interface exists to define Deserializable<T> inputs for functions that
 * deserialize a byte buffer into a type T.
 * It is not intended to be implemented or extended, because Typescript has no support
 * for static methods in interfaces.
 *
 * @template T - The type that this will deserialize into.
 * @group Implementation
 * @category BCS
 */
interface Deserializable<T> {
    /**
     * Deserializes the buffered bytes into an instance of the specified class type.
     * This function provides an alternative syntax for deserialization, allowing users to call
     * `deserializer.deserialize(MyClass)` instead of `MyClass.deserialize(deserializer)`.
     *
     * @param deserializer - The deserializer instance with the buffered bytes.
     * @returns The deserialized value of class type T.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));
     * const value = deserializer.deserialize(MyClass); // where MyClass has a `deserialize` function
     * // value is now an instance of MyClass
     * // equivalent to `const value = MyClass.deserialize(deserializer)`
     * ```
     * @group Implementation
     * @category BCS
     */
    deserialize(deserializer: Deserializer): T;
}
/**
 * A class that provides methods for deserializing various data types from a byte buffer.
 * It supports deserialization of primitive types, strings, and complex objects using a BCS (Binary Common Serialization) layout.
 * @group Implementation
 * @category BCS
 */
declare class Deserializer {
    private buffer;
    private offset;
    /**
     * Creates a new instance of the class with a copy of the provided data buffer.
     * This prevents outside mutation of the buffer.
     *
     * @param data - The data to be copied into the internal buffer as a Uint8Array.
     * @group Implementation
     * @category BCS
     */
    constructor(data: Uint8Array);
    static fromHex(hex: HexInput): Deserializer;
    /**
     * Reads a specified number of bytes from the buffer and advances the offset.
     *
     * @param length - The number of bytes to read from the buffer.
     * @throws Throws an error if the read operation exceeds the buffer's length.
     * @group Implementation
     * @category BCS
     */
    private read;
    /**
     * Returns the number of bytes remaining in the buffer.
     *
     * This information is useful to determine if there's more data to be read.
     *
     * @returns The number of bytes remaining in the buffer.
     * @group Implementation
     * @category BCS
     */
    remaining(): number;
    /**
     * Asserts that the buffer has no remaining bytes.
     *
     * @throws {Error} Throws an error if there are remaining bytes in the buffer.
     * @group Implementation
     * @category BCS
     */
    assertFinished(): void;
    /**
     * Deserializes a UTF-8 encoded string from a byte array. It first reads the length of the string in bytes,
     * followed by the actual byte content, and decodes it into a string.
     *
     * BCS layout for "string": string_length | string_content
     * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.
     *
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([8, 49, 50, 51, 52, 97, 98, 99, 100]));
     * assert(deserializer.deserializeStr() === "1234abcd");
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeStr(): string;
    /**
     * @deprecated use `deserializeOption("string")` instead.
     *
     * The BCS layout for Optional<String> is 0 if none, else 1 followed by the string length and string content.
     * @returns The deserialized string if it exists, otherwise undefined.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x00]));
     * assert(deserializer.deserializeOptionStr() === undefined);
     * const deserializer = new Deserializer(new Uint8Array([1, 8, 49, 50, 51, 52, 97, 98, 99, 100]));
     * assert(deserializer.deserializeOptionStr() === "1234abcd");
     * ```
     */
    deserializeOptionStr(): string | undefined;
    /**
     * Deserializes an optional value from the buffer.
     *
     * The BCS layout for Optional<T> starts with a boolean byte (0 if none, 1 if some),
     * followed by the value if present.
     *
     * @template T - The type of the value to deserialize
     * @param type - Either a Deserializable class or one of the string literals: "string", "bytes", or "fixedBytes"
     * @param len - Required length when type is "fixedBytes", ignored otherwise
     * @returns The deserialized value if present, undefined otherwise
     *
     * @throws {Error} When "fixedBytes" is specified without a length
     *
     * @example
     * ```typescript
     * // Deserialize an optional string
     * const deserializer = new Deserializer(new Uint8Array([1, 3, 97, 98, 99]));
     * const optStr = deserializer.deserializeOption("string");
     * // optStr === "abc"
     *
     * // Deserialize an optional custom type
     * const deserializer = new Deserializer(new Uint8Array([0]));
     * const optValue = deserializer.deserializeOption(MyClass);
     * // optValue === undefined
     *
     * // Deserialize optional bytes
     * const deserializer = new Deserializer(new Uint8Array([1, 3, 1, 2, 3]));
     * const optBytes = deserializer.deserializeOption("bytes");
     * // optBytes === Uint8Array[1, 2, 3]
     *
     * // Deserialize optional fixed bytes
     * const deserializer = new Deserializer(new Uint8Array([1, 1, 2, 3, 4]));
     * const optBytes = deserializer.deserializeOption("fixedBytes", 4);
     * // optBytes === Uint8Array[1, 2, 3, 4]
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeOption(type: "string"): string | undefined;
    deserializeOption(type: "bytes"): Uint8Array | undefined;
    deserializeOption(type: "fixedBytes", len: number): Uint8Array | undefined;
    deserializeOption<T>(type: Deserializable<T>): T | undefined;
    /**
     * Deserializes an array of bytes.
     *
     * The BCS layout for "bytes" consists of a bytes_length followed by the bytes themselves, where bytes_length is a u32 integer
     * encoded as a uleb128 integer, indicating the length of the bytes array.
     *
     * @returns {Uint8Array} The deserialized array of bytes.
     * @group Implementation
     * @category BCS
     */
    deserializeBytes(): Uint8Array;
    /**
     * Deserializes an array of bytes of a specified length.
     *
     * @param len - The number of bytes to read from the source.
     * @group Implementation
     * @category BCS
     */
    deserializeFixedBytes(len: number): Uint8Array;
    /**
     * Deserializes a boolean value from a byte stream.
     *
     * The BCS layout for a boolean uses one byte, where "0x01" represents true and "0x00" represents false.
     * An error is thrown if the byte value is not valid.
     *
     * @returns The deserialized boolean value.
     * @throws Throws an error if the boolean value is invalid.
     * @group Implementation
     * @category BCS
     */
    deserializeBool(): boolean;
    /**
     * Deserializes a uint8 number from the binary data.
     *
     * BCS layout for "uint8": One byte. Binary format in little-endian representation.
     *
     * @returns {number} The deserialized uint8 number.
     * @group Implementation
     * @category BCS
     */
    deserializeU8(): Uint8;
    /**
     * Deserializes a uint16 number from a binary format in little-endian representation.
     *
     * BCS layout for "uint16": Two bytes.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x34, 0x12]));
     * assert(deserializer.deserializeU16() === 4660);
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeU16(): Uint16;
    /**
     * Deserializes a uint32 number from a binary format in little-endian representation.
     *
     * BCS layout for "uint32": Four bytes.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x78, 0x56, 0x34, 0x12]));
     * assert(deserializer.deserializeU32() === 305419896);
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeU32(): Uint32;
    /**
     * Deserializes a uint64 number.
     *
     * This function combines two 32-bit values to return a 64-bit unsigned integer in little-endian representation.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x00, 0xEF, 0xCD, 0xAB, 0x78, 0x56, 0x34, 0x12]));
     * assert(deserializer.deserializeU64() === 1311768467750121216);
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeU64(): Uint64;
    /**
     * Deserializes a uint128 number from its binary representation.
     * This function combines two 64-bit values to return a single uint128 value in little-endian format.
     *
     * @returns {BigInt} The deserialized uint128 number.
     * @group Implementation
     * @category BCS
     */
    deserializeU128(): Uint128;
    /**
     * Deserializes a uint256 number from its binary representation.
     *
     * The BCS layout for "uint256" consists of thirty-two bytes in little-endian format.
     *
     * @returns {BigInt} The deserialized uint256 number.
     * @group Implementation
     * @category BCS
     */
    deserializeU256(): Uint256;
    /**
     * Deserializes a uleb128 encoded uint32 number.
     *
     * This function is used for interpreting lengths of variable-length sequences and tags of enum values in BCS encoding.
     *
     * @throws {Error} Throws an error if the parsed value exceeds the maximum uint32 number.
     * @returns {number} The deserialized uint32 value.
     * @group Implementation
     * @category BCS
     */
    deserializeUleb128AsU32(): Uint32;
    /**
     * Helper function that primarily exists to support alternative syntax for deserialization.
     * That is, if we have a `const deserializer: new Deserializer(...)`, instead of having to use
     * `MyClass.deserialize(deserializer)`, we can call `deserializer.deserialize(MyClass)`.
     *
     * @example const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));
     * const value = deserializer.deserialize(MyClass); // where MyClass has a `deserialize` function
     * // value is now an instance of MyClass
     * // equivalent to `const value = MyClass.deserialize(deserializer)`
     * @param cls The BCS-deserializable class to deserialize the buffered bytes into.
     *
     * @returns the deserialized value of class type T
     * @group Implementation
     * @category BCS
     */
    deserialize<T>(cls: Deserializable<T>): T;
    /**
     * Deserializes an array of BCS Deserializable values given an existing Deserializer instance with a loaded byte buffer.
     *
     * @param cls The BCS-deserializable class to deserialize the buffered bytes into.
     * @returns An array of deserialized values of type T.
     * @example
     * // serialize a vector of addresses
     * const addresses = new Array<AccountAddress>(
     *   AccountAddress.from("0x1"),
     *   AccountAddress.from("0x2"),
     *   AccountAddress.from("0xa"),
     *   AccountAddress.from("0xb"),
     * );
     * const serializer = new Serializer();
     * serializer.serializeVector(addresses);
     * const serializedBytes = serializer.toUint8Array();
     *
     * // deserialize the bytes into an array of addresses
     * const deserializer = new Deserializer(serializedBytes);
     * const deserializedAddresses = deserializer.deserializeVector(AccountAddress);
     * // deserializedAddresses is now an array of AccountAddress instances
     * @group Implementation
     * @category BCS
     */
    deserializeVector<T>(cls: Deserializable<T>): Array<T>;
}

export { type Deserializable, Deserializer };
