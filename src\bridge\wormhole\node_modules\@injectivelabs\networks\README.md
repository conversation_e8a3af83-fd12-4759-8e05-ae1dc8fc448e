# 🌟 Injective Protocol - Networks and Endpoints

[![downloads](https://img.shields.io/npm/dm/@injectivelabs/networks.svg)](https://www.npmjs.com/package/@injectivelabs/networks)
[![npm-version](https://img.shields.io/npm/v/@injectivelabs/networks.svg)](https://www.npmjs.com/package/@injectivelabs/networks)
[![license](https://img.shields.io/npm/l/express.svg)]()

_Accessing decentralized finance through TypeScript (for Web, Node and React Native environment)_

`@injectivelabs/networks` is a TypeScript package for providing a pre-defined set of endpoints which can be used to fetch data from different data sources, broadcast transactions to sentry nodes, use the Tendermint RPC, etc, for different environments (Mainnet, Testnet, Devnet, etc).

### 📚 Installation

```bash
yarn add @injectivelabs/networks
```

---

## 📖 Documentation

Read more and find example usages on our [Networks Docs](https://docs.ts.injective.network/readme/networks)

---

## ⛑ Support

Reach out to us at one of the following places!

- Website at <a href="https://injective.com" target="_blank">`injective.com`</a>
- Twitter at <a href="https://twitter.com/Injective_" target="_blank">`@Injective`</a>
- Discord at <a href="https://discord.com/invite/NK4qdbv" target="_blank">`Discord`</a>
- Telegram at <a href="https://t.me/joininjective" target="_blank">`Telegram`</a>

---

## 🔓 License

Copyright © 2021 - 2022 Injective Labs Inc. (https://injectivelabs.org/)

<a href="https://iili.io/mNneZN.md.png"><img src="https://iili.io/mNneZN.md.png" style="width: 300px; max-width: 100%; height: auto" />

Originally released by Injective Labs Inc. under: <br />
Apache License <br />
Version 2.0, January 2004 <br />
http://www.apache.org/licenses/

<p>&nbsp;</p>
<div align="center">
  <sub><em>Powering the future of decentralized finance.</em></sub>
</div>
