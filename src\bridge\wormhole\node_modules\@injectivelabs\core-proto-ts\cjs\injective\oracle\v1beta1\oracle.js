"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignedPriceOfAssetPair = exports.AssetPair = exports.PriceAttestation = exports.MetadataStatistics = exports.PriceRecord = exports.PriceRecords = exports.LastPriceTimestamps = exports.SymbolPriceTimestamp = exports.BandIBCParams = exports.BandOracleRequest = exports.PythPriceState = exports.PriceState = exports.StorkPriceState = exports.CoinbasePriceState = exports.PriceFeedPrice = exports.PriceFeedInfo = exports.ProviderPriceState = exports.ProviderState = exports.ProviderInfo = exports.PriceFeedState = exports.BandPriceState = exports.ChainlinkPriceState = exports.OracleInfo = exports.Params = exports.OracleType = exports.protobufPackage = void 0;
exports.oracleTypeFromJSON = oracleTypeFromJSON;
exports.oracleTypeToJSON = oracleTypeToJSON;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
exports.protobufPackage = "injective.oracle.v1beta1";
var OracleType;
(function (OracleType) {
    OracleType[OracleType["Unspecified"] = 0] = "Unspecified";
    OracleType[OracleType["Band"] = 1] = "Band";
    OracleType[OracleType["PriceFeed"] = 2] = "PriceFeed";
    OracleType[OracleType["Coinbase"] = 3] = "Coinbase";
    OracleType[OracleType["Chainlink"] = 4] = "Chainlink";
    OracleType[OracleType["Razor"] = 5] = "Razor";
    OracleType[OracleType["Dia"] = 6] = "Dia";
    OracleType[OracleType["API3"] = 7] = "API3";
    OracleType[OracleType["Uma"] = 8] = "Uma";
    OracleType[OracleType["Pyth"] = 9] = "Pyth";
    OracleType[OracleType["BandIBC"] = 10] = "BandIBC";
    OracleType[OracleType["Provider"] = 11] = "Provider";
    OracleType[OracleType["Stork"] = 12] = "Stork";
    OracleType[OracleType["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(OracleType || (exports.OracleType = OracleType = {}));
function oracleTypeFromJSON(object) {
    switch (object) {
        case 0:
        case "Unspecified":
            return OracleType.Unspecified;
        case 1:
        case "Band":
            return OracleType.Band;
        case 2:
        case "PriceFeed":
            return OracleType.PriceFeed;
        case 3:
        case "Coinbase":
            return OracleType.Coinbase;
        case 4:
        case "Chainlink":
            return OracleType.Chainlink;
        case 5:
        case "Razor":
            return OracleType.Razor;
        case 6:
        case "Dia":
            return OracleType.Dia;
        case 7:
        case "API3":
            return OracleType.API3;
        case 8:
        case "Uma":
            return OracleType.Uma;
        case 9:
        case "Pyth":
            return OracleType.Pyth;
        case 10:
        case "BandIBC":
            return OracleType.BandIBC;
        case 11:
        case "Provider":
            return OracleType.Provider;
        case 12:
        case "Stork":
            return OracleType.Stork;
        case -1:
        case "UNRECOGNIZED":
        default:
            return OracleType.UNRECOGNIZED;
    }
}
function oracleTypeToJSON(object) {
    switch (object) {
        case OracleType.Unspecified:
            return "Unspecified";
        case OracleType.Band:
            return "Band";
        case OracleType.PriceFeed:
            return "PriceFeed";
        case OracleType.Coinbase:
            return "Coinbase";
        case OracleType.Chainlink:
            return "Chainlink";
        case OracleType.Razor:
            return "Razor";
        case OracleType.Dia:
            return "Dia";
        case OracleType.API3:
            return "API3";
        case OracleType.Uma:
            return "Uma";
        case OracleType.Pyth:
            return "Pyth";
        case OracleType.BandIBC:
            return "BandIBC";
        case OracleType.Provider:
            return "Provider";
        case OracleType.Stork:
            return "Stork";
        case OracleType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseParams() {
    return { pythContract: "" };
}
exports.Params = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.pythContract !== "") {
            writer.uint32(10).string(message.pythContract);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pythContract = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { pythContract: isSet(object.pythContract) ? String(object.pythContract) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.pythContract !== undefined && (obj.pythContract = message.pythContract);
        return obj;
    },
    create: function (base) {
        return exports.Params.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseParams();
        message.pythContract = (_a = object.pythContract) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseOracleInfo() {
    return { symbol: "", oracleType: 0 };
}
exports.OracleInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.oracleType !== 0) {
            writer.uint32(16).int32(message.oracleType);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOracleInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.oracleType = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            oracleType: isSet(object.oracleType) ? oracleTypeFromJSON(object.oracleType) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.oracleType !== undefined && (obj.oracleType = oracleTypeToJSON(message.oracleType));
        return obj;
    },
    create: function (base) {
        return exports.OracleInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseOracleInfo();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.oracleType = (_b = object.oracleType) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseChainlinkPriceState() {
    return { feedId: "", answer: "", timestamp: "0", priceState: undefined };
}
exports.ChainlinkPriceState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.answer !== "") {
            writer.uint32(18).string(message.answer);
        }
        if (message.timestamp !== "0") {
            writer.uint32(24).uint64(message.timestamp);
        }
        if (message.priceState !== undefined) {
            exports.PriceState.encode(message.priceState, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseChainlinkPriceState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.answer = reader.string();
                    break;
                case 3:
                    message.timestamp = longToString(reader.uint64());
                    break;
                case 4:
                    message.priceState = exports.PriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            answer: isSet(object.answer) ? String(object.answer) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            priceState: isSet(object.priceState) ? exports.PriceState.fromJSON(object.priceState) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.answer !== undefined && (obj.answer = message.answer);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? exports.PriceState.toJSON(message.priceState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ChainlinkPriceState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseChainlinkPriceState();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.answer = (_b = object.answer) !== null && _b !== void 0 ? _b : "";
        message.timestamp = (_c = object.timestamp) !== null && _c !== void 0 ? _c : "0";
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? exports.PriceState.fromPartial(object.priceState)
            : undefined;
        return message;
    },
};
function createBaseBandPriceState() {
    return { symbol: "", rate: "", resolveTime: "0", requestID: "0", priceState: undefined };
}
exports.BandPriceState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.rate !== "") {
            writer.uint32(18).string(message.rate);
        }
        if (message.resolveTime !== "0") {
            writer.uint32(24).uint64(message.resolveTime);
        }
        if (message.requestID !== "0") {
            writer.uint32(32).uint64(message.requestID);
        }
        if (message.priceState !== undefined) {
            exports.PriceState.encode(message.priceState, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBandPriceState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.rate = reader.string();
                    break;
                case 3:
                    message.resolveTime = longToString(reader.uint64());
                    break;
                case 4:
                    message.requestID = longToString(reader.uint64());
                    break;
                case 5:
                    message.priceState = exports.PriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            rate: isSet(object.rate) ? String(object.rate) : "",
            resolveTime: isSet(object.resolveTime) ? String(object.resolveTime) : "0",
            requestID: isSet(object.requestID) ? String(object.requestID) : "0",
            priceState: isSet(object.priceState) ? exports.PriceState.fromJSON(object.priceState) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.rate !== undefined && (obj.rate = message.rate);
        message.resolveTime !== undefined && (obj.resolveTime = message.resolveTime);
        message.requestID !== undefined && (obj.requestID = message.requestID);
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? exports.PriceState.toJSON(message.priceState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.BandPriceState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseBandPriceState();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.rate = (_b = object.rate) !== null && _b !== void 0 ? _b : "";
        message.resolveTime = (_c = object.resolveTime) !== null && _c !== void 0 ? _c : "0";
        message.requestID = (_d = object.requestID) !== null && _d !== void 0 ? _d : "0";
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? exports.PriceState.fromPartial(object.priceState)
            : undefined;
        return message;
    },
};
function createBasePriceFeedState() {
    return { base: "", quote: "", priceState: undefined, relayers: [] };
}
exports.PriceFeedState = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.base !== "") {
            writer.uint32(10).string(message.base);
        }
        if (message.quote !== "") {
            writer.uint32(18).string(message.quote);
        }
        if (message.priceState !== undefined) {
            exports.PriceState.encode(message.priceState, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.relayers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceFeedState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.base = reader.string();
                    break;
                case 2:
                    message.quote = reader.string();
                    break;
                case 3:
                    message.priceState = exports.PriceState.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.relayers.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            base: isSet(object.base) ? String(object.base) : "",
            quote: isSet(object.quote) ? String(object.quote) : "",
            priceState: isSet(object.priceState) ? exports.PriceState.fromJSON(object.priceState) : undefined,
            relayers: Array.isArray(object === null || object === void 0 ? void 0 : object.relayers) ? object.relayers.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.base !== undefined && (obj.base = message.base);
        message.quote !== undefined && (obj.quote = message.quote);
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? exports.PriceState.toJSON(message.priceState) : undefined);
        if (message.relayers) {
            obj.relayers = message.relayers.map(function (e) { return e; });
        }
        else {
            obj.relayers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.PriceFeedState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBasePriceFeedState();
        message.base = (_a = object.base) !== null && _a !== void 0 ? _a : "";
        message.quote = (_b = object.quote) !== null && _b !== void 0 ? _b : "";
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? exports.PriceState.fromPartial(object.priceState)
            : undefined;
        message.relayers = ((_c = object.relayers) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseProviderInfo() {
    return { provider: "", relayers: [] };
}
exports.ProviderInfo = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.provider !== "") {
            writer.uint32(10).string(message.provider);
        }
        try {
            for (var _b = __values(message.relayers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseProviderInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.provider = reader.string();
                    break;
                case 2:
                    message.relayers.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            provider: isSet(object.provider) ? String(object.provider) : "",
            relayers: Array.isArray(object === null || object === void 0 ? void 0 : object.relayers) ? object.relayers.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.provider !== undefined && (obj.provider = message.provider);
        if (message.relayers) {
            obj.relayers = message.relayers.map(function (e) { return e; });
        }
        else {
            obj.relayers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ProviderInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseProviderInfo();
        message.provider = (_a = object.provider) !== null && _a !== void 0 ? _a : "";
        message.relayers = ((_b = object.relayers) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseProviderState() {
    return { providerInfo: undefined, providerPriceStates: [] };
}
exports.ProviderState = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.providerInfo !== undefined) {
            exports.ProviderInfo.encode(message.providerInfo, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.providerPriceStates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.ProviderPriceState.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseProviderState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.providerInfo = exports.ProviderInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.providerPriceStates.push(exports.ProviderPriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            providerInfo: isSet(object.providerInfo) ? exports.ProviderInfo.fromJSON(object.providerInfo) : undefined,
            providerPriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.providerPriceStates)
                ? object.providerPriceStates.map(function (e) { return exports.ProviderPriceState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.providerInfo !== undefined &&
            (obj.providerInfo = message.providerInfo ? exports.ProviderInfo.toJSON(message.providerInfo) : undefined);
        if (message.providerPriceStates) {
            obj.providerPriceStates = message.providerPriceStates.map(function (e) { return e ? exports.ProviderPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.providerPriceStates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ProviderState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseProviderState();
        message.providerInfo = (object.providerInfo !== undefined && object.providerInfo !== null)
            ? exports.ProviderInfo.fromPartial(object.providerInfo)
            : undefined;
        message.providerPriceStates = ((_a = object.providerPriceStates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.ProviderPriceState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseProviderPriceState() {
    return { symbol: "", state: undefined };
}
exports.ProviderPriceState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.state !== undefined) {
            exports.PriceState.encode(message.state, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseProviderPriceState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.state = exports.PriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            state: isSet(object.state) ? exports.PriceState.fromJSON(object.state) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.state !== undefined && (obj.state = message.state ? exports.PriceState.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ProviderPriceState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseProviderPriceState();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.state = (object.state !== undefined && object.state !== null)
            ? exports.PriceState.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBasePriceFeedInfo() {
    return { base: "", quote: "" };
}
exports.PriceFeedInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.base !== "") {
            writer.uint32(10).string(message.base);
        }
        if (message.quote !== "") {
            writer.uint32(18).string(message.quote);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceFeedInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.base = reader.string();
                    break;
                case 2:
                    message.quote = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            base: isSet(object.base) ? String(object.base) : "",
            quote: isSet(object.quote) ? String(object.quote) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.base !== undefined && (obj.base = message.base);
        message.quote !== undefined && (obj.quote = message.quote);
        return obj;
    },
    create: function (base) {
        return exports.PriceFeedInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePriceFeedInfo();
        message.base = (_a = object.base) !== null && _a !== void 0 ? _a : "";
        message.quote = (_b = object.quote) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBasePriceFeedPrice() {
    return { price: "" };
}
exports.PriceFeedPrice = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceFeedPrice();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { price: isSet(object.price) ? String(object.price) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        return obj;
    },
    create: function (base) {
        return exports.PriceFeedPrice.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBasePriceFeedPrice();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseCoinbasePriceState() {
    return { kind: "", timestamp: "0", key: "", value: "0", priceState: undefined };
}
exports.CoinbasePriceState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.kind !== "") {
            writer.uint32(10).string(message.kind);
        }
        if (message.timestamp !== "0") {
            writer.uint32(16).uint64(message.timestamp);
        }
        if (message.key !== "") {
            writer.uint32(26).string(message.key);
        }
        if (message.value !== "0") {
            writer.uint32(32).uint64(message.value);
        }
        if (message.priceState !== undefined) {
            exports.PriceState.encode(message.priceState, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCoinbasePriceState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.kind = reader.string();
                    break;
                case 2:
                    message.timestamp = longToString(reader.uint64());
                    break;
                case 3:
                    message.key = reader.string();
                    break;
                case 4:
                    message.value = longToString(reader.uint64());
                    break;
                case 5:
                    message.priceState = exports.PriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            kind: isSet(object.kind) ? String(object.kind) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? String(object.value) : "0",
            priceState: isSet(object.priceState) ? exports.PriceState.fromJSON(object.priceState) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.kind !== undefined && (obj.kind = message.kind);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value);
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? exports.PriceState.toJSON(message.priceState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.CoinbasePriceState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseCoinbasePriceState();
        message.kind = (_a = object.kind) !== null && _a !== void 0 ? _a : "";
        message.timestamp = (_b = object.timestamp) !== null && _b !== void 0 ? _b : "0";
        message.key = (_c = object.key) !== null && _c !== void 0 ? _c : "";
        message.value = (_d = object.value) !== null && _d !== void 0 ? _d : "0";
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? exports.PriceState.fromPartial(object.priceState)
            : undefined;
        return message;
    },
};
function createBaseStorkPriceState() {
    return { timestamp: "0", symbol: "", value: "", priceState: undefined };
}
exports.StorkPriceState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.timestamp !== "0") {
            writer.uint32(8).uint64(message.timestamp);
        }
        if (message.symbol !== "") {
            writer.uint32(18).string(message.symbol);
        }
        if (message.value !== "") {
            writer.uint32(26).string(message.value);
        }
        if (message.priceState !== undefined) {
            exports.PriceState.encode(message.priceState, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseStorkPriceState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.timestamp = longToString(reader.uint64());
                    break;
                case 2:
                    message.symbol = reader.string();
                    break;
                case 3:
                    message.value = reader.string();
                    break;
                case 5:
                    message.priceState = exports.PriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            value: isSet(object.value) ? String(object.value) : "",
            priceState: isSet(object.priceState) ? exports.PriceState.fromJSON(object.priceState) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.value !== undefined && (obj.value = message.value);
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? exports.PriceState.toJSON(message.priceState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.StorkPriceState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseStorkPriceState();
        message.timestamp = (_a = object.timestamp) !== null && _a !== void 0 ? _a : "0";
        message.symbol = (_b = object.symbol) !== null && _b !== void 0 ? _b : "";
        message.value = (_c = object.value) !== null && _c !== void 0 ? _c : "";
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? exports.PriceState.fromPartial(object.priceState)
            : undefined;
        return message;
    },
};
function createBasePriceState() {
    return { price: "", cumulativePrice: "", timestamp: "0" };
}
exports.PriceState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.cumulativePrice !== "") {
            writer.uint32(18).string(message.cumulativePrice);
        }
        if (message.timestamp !== "0") {
            writer.uint32(24).int64(message.timestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.cumulativePrice = reader.string();
                    break;
                case 3:
                    message.timestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            cumulativePrice: isSet(object.cumulativePrice) ? String(object.cumulativePrice) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.cumulativePrice !== undefined && (obj.cumulativePrice = message.cumulativePrice);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create: function (base) {
        return exports.PriceState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBasePriceState();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.cumulativePrice = (_b = object.cumulativePrice) !== null && _b !== void 0 ? _b : "";
        message.timestamp = (_c = object.timestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function createBasePythPriceState() {
    return { priceId: "", emaPrice: "", emaConf: "", conf: "", publishTime: "0", priceState: undefined };
}
exports.PythPriceState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.priceId !== "") {
            writer.uint32(10).string(message.priceId);
        }
        if (message.emaPrice !== "") {
            writer.uint32(18).string(message.emaPrice);
        }
        if (message.emaConf !== "") {
            writer.uint32(26).string(message.emaConf);
        }
        if (message.conf !== "") {
            writer.uint32(34).string(message.conf);
        }
        if (message.publishTime !== "0") {
            writer.uint32(40).uint64(message.publishTime);
        }
        if (message.priceState !== undefined) {
            exports.PriceState.encode(message.priceState, writer.uint32(50).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePythPriceState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceId = reader.string();
                    break;
                case 2:
                    message.emaPrice = reader.string();
                    break;
                case 3:
                    message.emaConf = reader.string();
                    break;
                case 4:
                    message.conf = reader.string();
                    break;
                case 5:
                    message.publishTime = longToString(reader.uint64());
                    break;
                case 6:
                    message.priceState = exports.PriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceId: isSet(object.priceId) ? String(object.priceId) : "",
            emaPrice: isSet(object.emaPrice) ? String(object.emaPrice) : "",
            emaConf: isSet(object.emaConf) ? String(object.emaConf) : "",
            conf: isSet(object.conf) ? String(object.conf) : "",
            publishTime: isSet(object.publishTime) ? String(object.publishTime) : "0",
            priceState: isSet(object.priceState) ? exports.PriceState.fromJSON(object.priceState) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.priceId !== undefined && (obj.priceId = message.priceId);
        message.emaPrice !== undefined && (obj.emaPrice = message.emaPrice);
        message.emaConf !== undefined && (obj.emaConf = message.emaConf);
        message.conf !== undefined && (obj.conf = message.conf);
        message.publishTime !== undefined && (obj.publishTime = message.publishTime);
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? exports.PriceState.toJSON(message.priceState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.PythPriceState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBasePythPriceState();
        message.priceId = (_a = object.priceId) !== null && _a !== void 0 ? _a : "";
        message.emaPrice = (_b = object.emaPrice) !== null && _b !== void 0 ? _b : "";
        message.emaConf = (_c = object.emaConf) !== null && _c !== void 0 ? _c : "";
        message.conf = (_d = object.conf) !== null && _d !== void 0 ? _d : "";
        message.publishTime = (_e = object.publishTime) !== null && _e !== void 0 ? _e : "0";
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? exports.PriceState.fromPartial(object.priceState)
            : undefined;
        return message;
    },
};
function createBaseBandOracleRequest() {
    return {
        requestId: "0",
        oracleScriptId: "0",
        symbols: [],
        askCount: "0",
        minCount: "0",
        feeLimit: [],
        prepareGas: "0",
        executeGas: "0",
        minSourceCount: "0",
    };
}
exports.BandOracleRequest = {
    encode: function (message, writer) {
        var e_4, _a, e_5, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.requestId !== "0") {
            writer.uint32(8).uint64(message.requestId);
        }
        if (message.oracleScriptId !== "0") {
            writer.uint32(16).int64(message.oracleScriptId);
        }
        try {
            for (var _c = __values(message.symbols), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_4) throw e_4.error; }
        }
        if (message.askCount !== "0") {
            writer.uint32(32).uint64(message.askCount);
        }
        if (message.minCount !== "0") {
            writer.uint32(40).uint64(message.minCount);
        }
        try {
            for (var _e = __values(message.feeLimit), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                coin_1.Coin.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_5) throw e_5.error; }
        }
        if (message.prepareGas !== "0") {
            writer.uint32(56).uint64(message.prepareGas);
        }
        if (message.executeGas !== "0") {
            writer.uint32(64).uint64(message.executeGas);
        }
        if (message.minSourceCount !== "0") {
            writer.uint32(72).uint64(message.minSourceCount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBandOracleRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.requestId = longToString(reader.uint64());
                    break;
                case 2:
                    message.oracleScriptId = longToString(reader.int64());
                    break;
                case 3:
                    message.symbols.push(reader.string());
                    break;
                case 4:
                    message.askCount = longToString(reader.uint64());
                    break;
                case 5:
                    message.minCount = longToString(reader.uint64());
                    break;
                case 6:
                    message.feeLimit.push(coin_1.Coin.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.prepareGas = longToString(reader.uint64());
                    break;
                case 8:
                    message.executeGas = longToString(reader.uint64());
                    break;
                case 9:
                    message.minSourceCount = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            requestId: isSet(object.requestId) ? String(object.requestId) : "0",
            oracleScriptId: isSet(object.oracleScriptId) ? String(object.oracleScriptId) : "0",
            symbols: Array.isArray(object === null || object === void 0 ? void 0 : object.symbols) ? object.symbols.map(function (e) { return String(e); }) : [],
            askCount: isSet(object.askCount) ? String(object.askCount) : "0",
            minCount: isSet(object.minCount) ? String(object.minCount) : "0",
            feeLimit: Array.isArray(object === null || object === void 0 ? void 0 : object.feeLimit) ? object.feeLimit.map(function (e) { return coin_1.Coin.fromJSON(e); }) : [],
            prepareGas: isSet(object.prepareGas) ? String(object.prepareGas) : "0",
            executeGas: isSet(object.executeGas) ? String(object.executeGas) : "0",
            minSourceCount: isSet(object.minSourceCount) ? String(object.minSourceCount) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.requestId !== undefined && (obj.requestId = message.requestId);
        message.oracleScriptId !== undefined && (obj.oracleScriptId = message.oracleScriptId);
        if (message.symbols) {
            obj.symbols = message.symbols.map(function (e) { return e; });
        }
        else {
            obj.symbols = [];
        }
        message.askCount !== undefined && (obj.askCount = message.askCount);
        message.minCount !== undefined && (obj.minCount = message.minCount);
        if (message.feeLimit) {
            obj.feeLimit = message.feeLimit.map(function (e) { return e ? coin_1.Coin.toJSON(e) : undefined; });
        }
        else {
            obj.feeLimit = [];
        }
        message.prepareGas !== undefined && (obj.prepareGas = message.prepareGas);
        message.executeGas !== undefined && (obj.executeGas = message.executeGas);
        message.minSourceCount !== undefined && (obj.minSourceCount = message.minSourceCount);
        return obj;
    },
    create: function (base) {
        return exports.BandOracleRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseBandOracleRequest();
        message.requestId = (_a = object.requestId) !== null && _a !== void 0 ? _a : "0";
        message.oracleScriptId = (_b = object.oracleScriptId) !== null && _b !== void 0 ? _b : "0";
        message.symbols = ((_c = object.symbols) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.askCount = (_d = object.askCount) !== null && _d !== void 0 ? _d : "0";
        message.minCount = (_e = object.minCount) !== null && _e !== void 0 ? _e : "0";
        message.feeLimit = ((_f = object.feeLimit) === null || _f === void 0 ? void 0 : _f.map(function (e) { return coin_1.Coin.fromPartial(e); })) || [];
        message.prepareGas = (_g = object.prepareGas) !== null && _g !== void 0 ? _g : "0";
        message.executeGas = (_h = object.executeGas) !== null && _h !== void 0 ? _h : "0";
        message.minSourceCount = (_j = object.minSourceCount) !== null && _j !== void 0 ? _j : "0";
        return message;
    },
};
function createBaseBandIBCParams() {
    return {
        bandIbcEnabled: false,
        ibcRequestInterval: "0",
        ibcSourceChannel: "",
        ibcVersion: "",
        ibcPortId: "",
        legacyOracleIds: [],
    };
}
exports.BandIBCParams = {
    encode: function (message, writer) {
        var e_6, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bandIbcEnabled === true) {
            writer.uint32(8).bool(message.bandIbcEnabled);
        }
        if (message.ibcRequestInterval !== "0") {
            writer.uint32(16).int64(message.ibcRequestInterval);
        }
        if (message.ibcSourceChannel !== "") {
            writer.uint32(26).string(message.ibcSourceChannel);
        }
        if (message.ibcVersion !== "") {
            writer.uint32(34).string(message.ibcVersion);
        }
        if (message.ibcPortId !== "") {
            writer.uint32(42).string(message.ibcPortId);
        }
        writer.uint32(50).fork();
        try {
            for (var _b = __values(message.legacyOracleIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.int64(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_6) throw e_6.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBandIBCParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bandIbcEnabled = reader.bool();
                    break;
                case 2:
                    message.ibcRequestInterval = longToString(reader.int64());
                    break;
                case 3:
                    message.ibcSourceChannel = reader.string();
                    break;
                case 4:
                    message.ibcVersion = reader.string();
                    break;
                case 5:
                    message.ibcPortId = reader.string();
                    break;
                case 6:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.legacyOracleIds.push(longToString(reader.int64()));
                        }
                    }
                    else {
                        message.legacyOracleIds.push(longToString(reader.int64()));
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bandIbcEnabled: isSet(object.bandIbcEnabled) ? Boolean(object.bandIbcEnabled) : false,
            ibcRequestInterval: isSet(object.ibcRequestInterval) ? String(object.ibcRequestInterval) : "0",
            ibcSourceChannel: isSet(object.ibcSourceChannel) ? String(object.ibcSourceChannel) : "",
            ibcVersion: isSet(object.ibcVersion) ? String(object.ibcVersion) : "",
            ibcPortId: isSet(object.ibcPortId) ? String(object.ibcPortId) : "",
            legacyOracleIds: Array.isArray(object === null || object === void 0 ? void 0 : object.legacyOracleIds) ? object.legacyOracleIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bandIbcEnabled !== undefined && (obj.bandIbcEnabled = message.bandIbcEnabled);
        message.ibcRequestInterval !== undefined && (obj.ibcRequestInterval = message.ibcRequestInterval);
        message.ibcSourceChannel !== undefined && (obj.ibcSourceChannel = message.ibcSourceChannel);
        message.ibcVersion !== undefined && (obj.ibcVersion = message.ibcVersion);
        message.ibcPortId !== undefined && (obj.ibcPortId = message.ibcPortId);
        if (message.legacyOracleIds) {
            obj.legacyOracleIds = message.legacyOracleIds.map(function (e) { return e; });
        }
        else {
            obj.legacyOracleIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BandIBCParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseBandIBCParams();
        message.bandIbcEnabled = (_a = object.bandIbcEnabled) !== null && _a !== void 0 ? _a : false;
        message.ibcRequestInterval = (_b = object.ibcRequestInterval) !== null && _b !== void 0 ? _b : "0";
        message.ibcSourceChannel = (_c = object.ibcSourceChannel) !== null && _c !== void 0 ? _c : "";
        message.ibcVersion = (_d = object.ibcVersion) !== null && _d !== void 0 ? _d : "";
        message.ibcPortId = (_e = object.ibcPortId) !== null && _e !== void 0 ? _e : "";
        message.legacyOracleIds = ((_f = object.legacyOracleIds) === null || _f === void 0 ? void 0 : _f.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseSymbolPriceTimestamp() {
    return { oracle: 0, symbolId: "", timestamp: "0" };
}
exports.SymbolPriceTimestamp = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.oracle !== 0) {
            writer.uint32(8).int32(message.oracle);
        }
        if (message.symbolId !== "") {
            writer.uint32(18).string(message.symbolId);
        }
        if (message.timestamp !== "0") {
            writer.uint32(24).int64(message.timestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSymbolPriceTimestamp();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.oracle = reader.int32();
                    break;
                case 2:
                    message.symbolId = reader.string();
                    break;
                case 3:
                    message.timestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            oracle: isSet(object.oracle) ? oracleTypeFromJSON(object.oracle) : 0,
            symbolId: isSet(object.symbolId) ? String(object.symbolId) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.oracle !== undefined && (obj.oracle = oracleTypeToJSON(message.oracle));
        message.symbolId !== undefined && (obj.symbolId = message.symbolId);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create: function (base) {
        return exports.SymbolPriceTimestamp.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSymbolPriceTimestamp();
        message.oracle = (_a = object.oracle) !== null && _a !== void 0 ? _a : 0;
        message.symbolId = (_b = object.symbolId) !== null && _b !== void 0 ? _b : "";
        message.timestamp = (_c = object.timestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function createBaseLastPriceTimestamps() {
    return { lastPriceTimestamps: [] };
}
exports.LastPriceTimestamps = {
    encode: function (message, writer) {
        var e_7, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.lastPriceTimestamps), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.SymbolPriceTimestamp.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLastPriceTimestamps();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.lastPriceTimestamps.push(exports.SymbolPriceTimestamp.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            lastPriceTimestamps: Array.isArray(object === null || object === void 0 ? void 0 : object.lastPriceTimestamps)
                ? object.lastPriceTimestamps.map(function (e) { return exports.SymbolPriceTimestamp.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.lastPriceTimestamps) {
            obj.lastPriceTimestamps = message.lastPriceTimestamps.map(function (e) { return e ? exports.SymbolPriceTimestamp.toJSON(e) : undefined; });
        }
        else {
            obj.lastPriceTimestamps = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.LastPriceTimestamps.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseLastPriceTimestamps();
        message.lastPriceTimestamps = ((_a = object.lastPriceTimestamps) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.SymbolPriceTimestamp.fromPartial(e); })) || [];
        return message;
    },
};
function createBasePriceRecords() {
    return { oracle: 0, symbolId: "", latestPriceRecords: [] };
}
exports.PriceRecords = {
    encode: function (message, writer) {
        var e_8, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.oracle !== 0) {
            writer.uint32(8).int32(message.oracle);
        }
        if (message.symbolId !== "") {
            writer.uint32(18).string(message.symbolId);
        }
        try {
            for (var _b = __values(message.latestPriceRecords), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.PriceRecord.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceRecords();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.oracle = reader.int32();
                    break;
                case 2:
                    message.symbolId = reader.string();
                    break;
                case 3:
                    message.latestPriceRecords.push(exports.PriceRecord.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            oracle: isSet(object.oracle) ? oracleTypeFromJSON(object.oracle) : 0,
            symbolId: isSet(object.symbolId) ? String(object.symbolId) : "",
            latestPriceRecords: Array.isArray(object === null || object === void 0 ? void 0 : object.latestPriceRecords)
                ? object.latestPriceRecords.map(function (e) { return exports.PriceRecord.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.oracle !== undefined && (obj.oracle = oracleTypeToJSON(message.oracle));
        message.symbolId !== undefined && (obj.symbolId = message.symbolId);
        if (message.latestPriceRecords) {
            obj.latestPriceRecords = message.latestPriceRecords.map(function (e) { return e ? exports.PriceRecord.toJSON(e) : undefined; });
        }
        else {
            obj.latestPriceRecords = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.PriceRecords.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBasePriceRecords();
        message.oracle = (_a = object.oracle) !== null && _a !== void 0 ? _a : 0;
        message.symbolId = (_b = object.symbolId) !== null && _b !== void 0 ? _b : "";
        message.latestPriceRecords = ((_c = object.latestPriceRecords) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.PriceRecord.fromPartial(e); })) || [];
        return message;
    },
};
function createBasePriceRecord() {
    return { timestamp: "0", price: "" };
}
exports.PriceRecord = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.timestamp !== "0") {
            writer.uint32(8).int64(message.timestamp);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceRecord();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.timestamp = longToString(reader.int64());
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            price: isSet(object.price) ? String(object.price) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.price !== undefined && (obj.price = message.price);
        return obj;
    },
    create: function (base) {
        return exports.PriceRecord.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePriceRecord();
        message.timestamp = (_a = object.timestamp) !== null && _a !== void 0 ? _a : "0";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMetadataStatistics() {
    return {
        groupCount: 0,
        recordsSampleSize: 0,
        mean: "",
        twap: "",
        firstTimestamp: "0",
        lastTimestamp: "0",
        minPrice: "",
        maxPrice: "",
        medianPrice: "",
    };
}
exports.MetadataStatistics = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.groupCount !== 0) {
            writer.uint32(8).uint32(message.groupCount);
        }
        if (message.recordsSampleSize !== 0) {
            writer.uint32(16).uint32(message.recordsSampleSize);
        }
        if (message.mean !== "") {
            writer.uint32(26).string(message.mean);
        }
        if (message.twap !== "") {
            writer.uint32(34).string(message.twap);
        }
        if (message.firstTimestamp !== "0") {
            writer.uint32(40).int64(message.firstTimestamp);
        }
        if (message.lastTimestamp !== "0") {
            writer.uint32(48).int64(message.lastTimestamp);
        }
        if (message.minPrice !== "") {
            writer.uint32(58).string(message.minPrice);
        }
        if (message.maxPrice !== "") {
            writer.uint32(66).string(message.maxPrice);
        }
        if (message.medianPrice !== "") {
            writer.uint32(74).string(message.medianPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMetadataStatistics();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.groupCount = reader.uint32();
                    break;
                case 2:
                    message.recordsSampleSize = reader.uint32();
                    break;
                case 3:
                    message.mean = reader.string();
                    break;
                case 4:
                    message.twap = reader.string();
                    break;
                case 5:
                    message.firstTimestamp = longToString(reader.int64());
                    break;
                case 6:
                    message.lastTimestamp = longToString(reader.int64());
                    break;
                case 7:
                    message.minPrice = reader.string();
                    break;
                case 8:
                    message.maxPrice = reader.string();
                    break;
                case 9:
                    message.medianPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            groupCount: isSet(object.groupCount) ? Number(object.groupCount) : 0,
            recordsSampleSize: isSet(object.recordsSampleSize) ? Number(object.recordsSampleSize) : 0,
            mean: isSet(object.mean) ? String(object.mean) : "",
            twap: isSet(object.twap) ? String(object.twap) : "",
            firstTimestamp: isSet(object.firstTimestamp) ? String(object.firstTimestamp) : "0",
            lastTimestamp: isSet(object.lastTimestamp) ? String(object.lastTimestamp) : "0",
            minPrice: isSet(object.minPrice) ? String(object.minPrice) : "",
            maxPrice: isSet(object.maxPrice) ? String(object.maxPrice) : "",
            medianPrice: isSet(object.medianPrice) ? String(object.medianPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.groupCount !== undefined && (obj.groupCount = Math.round(message.groupCount));
        message.recordsSampleSize !== undefined && (obj.recordsSampleSize = Math.round(message.recordsSampleSize));
        message.mean !== undefined && (obj.mean = message.mean);
        message.twap !== undefined && (obj.twap = message.twap);
        message.firstTimestamp !== undefined && (obj.firstTimestamp = message.firstTimestamp);
        message.lastTimestamp !== undefined && (obj.lastTimestamp = message.lastTimestamp);
        message.minPrice !== undefined && (obj.minPrice = message.minPrice);
        message.maxPrice !== undefined && (obj.maxPrice = message.maxPrice);
        message.medianPrice !== undefined && (obj.medianPrice = message.medianPrice);
        return obj;
    },
    create: function (base) {
        return exports.MetadataStatistics.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseMetadataStatistics();
        message.groupCount = (_a = object.groupCount) !== null && _a !== void 0 ? _a : 0;
        message.recordsSampleSize = (_b = object.recordsSampleSize) !== null && _b !== void 0 ? _b : 0;
        message.mean = (_c = object.mean) !== null && _c !== void 0 ? _c : "";
        message.twap = (_d = object.twap) !== null && _d !== void 0 ? _d : "";
        message.firstTimestamp = (_e = object.firstTimestamp) !== null && _e !== void 0 ? _e : "0";
        message.lastTimestamp = (_f = object.lastTimestamp) !== null && _f !== void 0 ? _f : "0";
        message.minPrice = (_g = object.minPrice) !== null && _g !== void 0 ? _g : "";
        message.maxPrice = (_h = object.maxPrice) !== null && _h !== void 0 ? _h : "";
        message.medianPrice = (_j = object.medianPrice) !== null && _j !== void 0 ? _j : "";
        return message;
    },
};
function createBasePriceAttestation() {
    return { priceId: "", price: "0", conf: "0", expo: 0, emaPrice: "0", emaConf: "0", emaExpo: 0, publishTime: "0" };
}
exports.PriceAttestation = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.priceId !== "") {
            writer.uint32(10).string(message.priceId);
        }
        if (message.price !== "0") {
            writer.uint32(16).int64(message.price);
        }
        if (message.conf !== "0") {
            writer.uint32(24).uint64(message.conf);
        }
        if (message.expo !== 0) {
            writer.uint32(32).int32(message.expo);
        }
        if (message.emaPrice !== "0") {
            writer.uint32(40).int64(message.emaPrice);
        }
        if (message.emaConf !== "0") {
            writer.uint32(48).uint64(message.emaConf);
        }
        if (message.emaExpo !== 0) {
            writer.uint32(56).int32(message.emaExpo);
        }
        if (message.publishTime !== "0") {
            writer.uint32(64).int64(message.publishTime);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceAttestation();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceId = reader.string();
                    break;
                case 2:
                    message.price = longToString(reader.int64());
                    break;
                case 3:
                    message.conf = longToString(reader.uint64());
                    break;
                case 4:
                    message.expo = reader.int32();
                    break;
                case 5:
                    message.emaPrice = longToString(reader.int64());
                    break;
                case 6:
                    message.emaConf = longToString(reader.uint64());
                    break;
                case 7:
                    message.emaExpo = reader.int32();
                    break;
                case 8:
                    message.publishTime = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceId: isSet(object.priceId) ? String(object.priceId) : "",
            price: isSet(object.price) ? String(object.price) : "0",
            conf: isSet(object.conf) ? String(object.conf) : "0",
            expo: isSet(object.expo) ? Number(object.expo) : 0,
            emaPrice: isSet(object.emaPrice) ? String(object.emaPrice) : "0",
            emaConf: isSet(object.emaConf) ? String(object.emaConf) : "0",
            emaExpo: isSet(object.emaExpo) ? Number(object.emaExpo) : 0,
            publishTime: isSet(object.publishTime) ? String(object.publishTime) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.priceId !== undefined && (obj.priceId = message.priceId);
        message.price !== undefined && (obj.price = message.price);
        message.conf !== undefined && (obj.conf = message.conf);
        message.expo !== undefined && (obj.expo = Math.round(message.expo));
        message.emaPrice !== undefined && (obj.emaPrice = message.emaPrice);
        message.emaConf !== undefined && (obj.emaConf = message.emaConf);
        message.emaExpo !== undefined && (obj.emaExpo = Math.round(message.emaExpo));
        message.publishTime !== undefined && (obj.publishTime = message.publishTime);
        return obj;
    },
    create: function (base) {
        return exports.PriceAttestation.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBasePriceAttestation();
        message.priceId = (_a = object.priceId) !== null && _a !== void 0 ? _a : "";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "0";
        message.conf = (_c = object.conf) !== null && _c !== void 0 ? _c : "0";
        message.expo = (_d = object.expo) !== null && _d !== void 0 ? _d : 0;
        message.emaPrice = (_e = object.emaPrice) !== null && _e !== void 0 ? _e : "0";
        message.emaConf = (_f = object.emaConf) !== null && _f !== void 0 ? _f : "0";
        message.emaExpo = (_g = object.emaExpo) !== null && _g !== void 0 ? _g : 0;
        message.publishTime = (_h = object.publishTime) !== null && _h !== void 0 ? _h : "0";
        return message;
    },
};
function createBaseAssetPair() {
    return { assetId: "", signedPrices: [] };
}
exports.AssetPair = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.assetId !== "") {
            writer.uint32(10).string(message.assetId);
        }
        try {
            for (var _b = __values(message.signedPrices), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.SignedPriceOfAssetPair.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAssetPair();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.assetId = reader.string();
                    break;
                case 2:
                    message.signedPrices.push(exports.SignedPriceOfAssetPair.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            assetId: isSet(object.assetId) ? String(object.assetId) : "",
            signedPrices: Array.isArray(object === null || object === void 0 ? void 0 : object.signedPrices)
                ? object.signedPrices.map(function (e) { return exports.SignedPriceOfAssetPair.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.assetId !== undefined && (obj.assetId = message.assetId);
        if (message.signedPrices) {
            obj.signedPrices = message.signedPrices.map(function (e) { return e ? exports.SignedPriceOfAssetPair.toJSON(e) : undefined; });
        }
        else {
            obj.signedPrices = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.AssetPair.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseAssetPair();
        message.assetId = (_a = object.assetId) !== null && _a !== void 0 ? _a : "";
        message.signedPrices = ((_b = object.signedPrices) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.SignedPriceOfAssetPair.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSignedPriceOfAssetPair() {
    return { publisherKey: "", timestamp: "0", price: "", signature: new Uint8Array() };
}
exports.SignedPriceOfAssetPair = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.publisherKey !== "") {
            writer.uint32(10).string(message.publisherKey);
        }
        if (message.timestamp !== "0") {
            writer.uint32(16).uint64(message.timestamp);
        }
        if (message.price !== "") {
            writer.uint32(26).string(message.price);
        }
        if (message.signature.length !== 0) {
            writer.uint32(34).bytes(message.signature);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSignedPriceOfAssetPair();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.publisherKey = reader.string();
                    break;
                case 2:
                    message.timestamp = longToString(reader.uint64());
                    break;
                case 3:
                    message.price = reader.string();
                    break;
                case 4:
                    message.signature = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            publisherKey: isSet(object.publisherKey) ? String(object.publisherKey) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            price: isSet(object.price) ? String(object.price) : "",
            signature: isSet(object.signature) ? bytesFromBase64(object.signature) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.publisherKey !== undefined && (obj.publisherKey = message.publisherKey);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.price !== undefined && (obj.price = message.price);
        message.signature !== undefined &&
            (obj.signature = base64FromBytes(message.signature !== undefined ? message.signature : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.SignedPriceOfAssetPair.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseSignedPriceOfAssetPair();
        message.publisherKey = (_a = object.publisherKey) !== null && _a !== void 0 ? _a : "";
        message.timestamp = (_b = object.timestamp) !== null && _b !== void 0 ? _b : "0";
        message.price = (_c = object.price) !== null && _c !== void 0 ? _c : "";
        message.signature = (_d = object.signature) !== null && _d !== void 0 ? _d : new Uint8Array();
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
