{"version": 3, "file": "idl.js", "sourceRoot": "", "sources": ["../../../../src/coder/borsh/idl.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,OAAO,KAAK,KAAK,MAAM,kBAAkB,CAAC;AAE1C,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE1C,MAAM,OAAO,QAAQ;IACZ,MAAM,CAAC,WAAW,CACvB,KAAiD,EACjD,KAAoB;QAEpB,MAAM,SAAS,GACb,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/D,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,IAAI,CAAC,CAAC;gBACT,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;aAC5B;YACD,KAAK,IAAI,CAAC,CAAC;gBACT,OAAO,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;aAC5B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,KAAK,CAAC,CAAC;gBACV,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC9B;YACD,KAAK,OAAO,CAAC,CAAC;gBACZ,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;aAC/B;YACD,KAAK,QAAQ,CAAC,CAAC;gBACb,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aAC7B;YACD,KAAK,WAAW,CAAC,CAAC;gBAChB,OAAO,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;aACnC;YACD,OAAO,CAAC,CAAC;gBACP,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;oBACvB,OAAO,KAAK,CAAC,GAAG,CACd,QAAQ,CAAC,WAAW,CAClB;wBACE,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG;qBACrB,EACD,KAAK,CACN,EACD,SAAS,CACV,CAAC;iBACH;qBAAM,IAAI,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE;oBACjC,OAAO,KAAK,CAAC,MAAM,CACjB,QAAQ,CAAC,WAAW,CAClB;wBACE,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM;qBACxB,EACD,KAAK,CACN,EACD,SAAS,CACV,CAAC;iBACH;qBAAM,IAAI,SAAS,IAAI,KAAK,CAAC,IAAI,EAAE;oBAClC,qBAAqB;oBACrB,IAAI,CAAC,KAAK,EAAE;wBACV,MAAM,IAAI,QAAQ,CAAC,iCAAiC,CAAC,CAAC;qBACvD;oBAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;oBACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;oBACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;wBACzB,MAAM,IAAI,QAAQ,CAAC,mBAAmB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;qBAChE;oBACD,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;iBAC9D;qBAAM,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;oBAChC,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,CACpC;wBACE,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,OAAO;qBACd,EACD,KAAK,CACN,CAAC;oBACF,OAAO,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;iBACtD;qBAAM;oBACL,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,EAAE,CAAC,CAAC;iBAClD;aACF;SACF;IACH,CAAC;IAEM,MAAM,CAAC,aAAa,CACzB,OAAmB,EACnB,QAAsB,EAAE,EACxB,IAAa;QAEb,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;YACzB,KAAK,QAAQ,CAAC,CAAC;gBACb,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBACrD,OAAO,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;gBACH,OAAO,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;aACzC;YAED,KAAK,MAAM,CAAC,CAAC;gBACX,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAuB,EAAE,EAAE;oBACnE,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACrC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;wBACnB,OAAO,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;qBAC/B;oBAED,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CACrC,CAAC,CAAqB,EAAE,CAAS,EAAE,EAAE;wBACnC,IAAK,CAAc,aAAd,CAAC,uBAAD,CAAC,CAAe,IAAI,EAAE;4BACzB,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAa,EAAE,KAAK,CAAC,CAAC;yBACnD;wBAED,OAAO,QAAQ,CAAC,WAAW,CACzB,EAAE,IAAI,EAAE,CAAY,EAAE,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,EAC1C,KAAK,CACN,CAAC;oBACJ,CAAC,CACF,CAAC;oBACF,OAAO,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;gBAC1C,CAAC,CAAC,CAAC;gBAEH,IAAI,IAAI,KAAK,SAAS,EAAE;oBACtB,mEAAmE;oBACnE,wBAAwB;oBACxB,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;iBACjD;gBAED,OAAO,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;aACvC;YAED,KAAK,OAAO,CAAC,CAAC;gBACZ,OAAO,QAAQ,CAAC,WAAW,CACzB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAChD,KAAK,CACN,CAAC;aACH;SACF;IACH,CAAC;CACF"}