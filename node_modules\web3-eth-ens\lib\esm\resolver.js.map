{"version": 3, "file": "resolver.js", "sourceRoot": "", "sources": ["../../src/resolver.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;AAEF,OAAO,EAAE,0BAA0B,EAAE,MAAM,aAAa,CAAC;AAEzD,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAG7C,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAE/D,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAGtC,2BAA2B;AAC3B,oFAAoF;AAEpF,MAAM,OAAO,QAAQ;IAGpB,YAAmB,QAAkB;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAEa,0BAA0B,CAAC,OAAe;;YACvD,kLAAkL;YAClL,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;KAAA;IAED,0CAA0C;IAC1C,kDAAkD;IACrC,qBAAqB,CACjC,gBAAoD,EACpD,UAAkB;;;YAElB,IAAI,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBACtC,MAAM,IAAI,0BAA0B,CACnC,MAAA,gBAAgB,CAAC,OAAO,CAAC,OAAO,mCAAI,EAAE,EACtC,UAAU,CACV,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,OAAO;iBAC9C,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;iBAC3C,IAAI,EAAE,CAAC;YAET,IAAI,CAAC,SAAS;gBACb,MAAM,IAAI,0BAA0B,CACnC,MAAA,gBAAgB,CAAC,OAAO,CAAC,OAAO,mCAAI,EAAE,EACtC,UAAU,CACV,CAAC;;KACH;IAEY,iBAAiB,CAAC,OAAe,EAAE,WAAmB;;;YAClE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,IAAI,gBAAgB,GAAG,WAAW,CAAC;YAEnC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;gBACnC,gBAAgB,GAAG,MAAA,IAAI,CAAC,WAAW,CAAC,mCAAI,EAAE,CAAC;gBAE3C,IAAI,WAAW,KAAK,EAAE;oBAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAEhE,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;aACjD;YAED,OAAO,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE,CAAC;;KAC3E;IAED,kEAAkE;IACrD,UAAU,CAAC,OAAe,EAAE,WAAmB,EAAE;;YAC7D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE5E,OAAO,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAC1E,CAAC;KAAA;IAEY,SAAS,CAAC,OAAe;;YACrC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE9E,OAAO,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAClE,CAAC;KAAA;IAEY,cAAc,CAAC,OAAe;;YAC1C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAEnF,OAAO,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACvE,CAAC;KAAA;IAEY,UAAU,CACtB,OAAe,EACf,OAAgB,EAChB,QAA4B;;YAE5B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YACxE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAE/E,OAAO,gBAAgB,CAAC,OAAO;iBAC7B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;iBACnC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClB,CAAC;KAAA;IAEY,OAAO,CACnB,OAAe,EACf,GAAW;;YAEX,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YACxE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE5E,OAAO,gBAAgB,CAAC,OAAO;iBAC7B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAA;QACtC,CAAC;KAAA;IAEY,OAAO,CACnB,OAAe,EACf,qBAAqB,GAAG,IAAI;;YAE5B,MAAM,WAAW,GAAG,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC;YAEzE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAE5E,IAAG,qBAAqB;gBACvB,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE7E,OAAO,gBAAgB,CAAC,OAAO;iBAC7B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QACrC,CAAC;KAAA;CACD"}