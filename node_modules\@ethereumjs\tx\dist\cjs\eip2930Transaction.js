"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccessListEIP2930Transaction = void 0;
const rlp_1 = require("@ethereumjs/rlp");
const util_1 = require("@ethereumjs/util");
const baseTransaction_js_1 = require("./baseTransaction.js");
const EIP2718 = require("./capabilities/eip2718.js");
const EIP2930 = require("./capabilities/eip2930.js");
const Legacy = require("./capabilities/legacy.js");
const types_js_1 = require("./types.js");
const util_js_1 = require("./util.js");
/**
 * Typed transaction with optional access lists
 *
 * - TransactionType: 1
 * - EIP: [EIP-2930](https://eips.ethereum.org/EIPS/eip-2930)
 */
class AccessListEIP2930Transaction extends baseTransaction_js_1.BaseTransaction {
    /**
     * This constructor takes the values, validates them, assigns them and freezes the object.
     *
     * It is not recommended to use this constructor directly. Instead use
     * the static factory methods to assist in creating a Transaction object from
     * varying data types.
     */
    constructor(txData, opts = {}) {
        super({ ...txData, type: types_js_1.TransactionType.AccessListEIP2930 }, opts);
        const { chainId, accessList, gasPrice } = txData;
        this.common = this._getCommon(opts.common, chainId);
        this.chainId = this.common.chainId();
        // EIP-2718 check is done in Common
        if (!this.common.isActivatedEIP(2930)) {
            throw new Error('EIP-2930 not enabled on Common');
        }
        this.activeCapabilities = this.activeCapabilities.concat([2718, 2930]);
        // Populate the access list fields
        const accessListData = util_js_1.AccessLists.getAccessListData(accessList ?? []);
        this.accessList = accessListData.accessList;
        this.AccessListJSON = accessListData.AccessListJSON;
        // Verify the access list format.
        util_js_1.AccessLists.verifyAccessList(this.accessList);
        this.gasPrice = (0, util_1.bytesToBigInt)((0, util_1.toBytes)(gasPrice));
        this._validateCannotExceedMaxInteger({
            gasPrice: this.gasPrice,
        });
        baseTransaction_js_1.BaseTransaction._validateNotArray(txData);
        if (this.gasPrice * this.gasLimit > util_1.MAX_INTEGER) {
            const msg = this._errorMsg('gasLimit * gasPrice cannot exceed MAX_INTEGER');
            throw new Error(msg);
        }
        EIP2718.validateYParity(this);
        Legacy.validateHighS(this);
        const freeze = opts?.freeze ?? true;
        if (freeze) {
            Object.freeze(this);
        }
    }
    /**
     * Instantiate a transaction from a data dictionary.
     *
     * Format: { chainId, nonce, gasPrice, gasLimit, to, value, data, accessList,
     * v, r, s }
     *
     * Notes:
     * - `chainId` will be set automatically if not provided
     * - All parameters are optional and have some basic default values
     */
    static fromTxData(txData, opts = {}) {
        return new AccessListEIP2930Transaction(txData, opts);
    }
    /**
     * Instantiate a transaction from the serialized tx.
     *
     * Format: `0x01 || rlp([chainId, nonce, gasPrice, gasLimit, to, value, data, accessList,
     * signatureYParity (v), signatureR (r), signatureS (s)])`
     */
    static fromSerializedTx(serialized, opts = {}) {
        if ((0, util_1.equalsBytes)(serialized.subarray(0, 1), (0, util_js_1.txTypeBytes)(types_js_1.TransactionType.AccessListEIP2930)) ===
            false) {
            throw new Error(`Invalid serialized tx input: not an EIP-2930 transaction (wrong tx type, expected: ${types_js_1.TransactionType.AccessListEIP2930}, received: ${(0, util_1.bytesToHex)(serialized.subarray(0, 1))}`);
        }
        const values = rlp_1.RLP.decode(Uint8Array.from(serialized.subarray(1)));
        if (!Array.isArray(values)) {
            throw new Error('Invalid serialized tx input: must be array');
        }
        return AccessListEIP2930Transaction.fromValuesArray(values, opts);
    }
    /**
     * Create a transaction from a values array.
     *
     * Format: `[chainId, nonce, gasPrice, gasLimit, to, value, data, accessList,
     * signatureYParity (v), signatureR (r), signatureS (s)]`
     */
    static fromValuesArray(values, opts = {}) {
        if (values.length !== 8 && values.length !== 11) {
            throw new Error('Invalid EIP-2930 transaction. Only expecting 8 values (for unsigned tx) or 11 values (for signed tx).');
        }
        const [chainId, nonce, gasPrice, gasLimit, to, value, data, accessList, v, r, s] = values;
        this._validateNotArray({ chainId, v });
        (0, util_1.validateNoLeadingZeroes)({ nonce, gasPrice, gasLimit, value, v, r, s });
        const emptyAccessList = [];
        return new AccessListEIP2930Transaction({
            chainId: (0, util_1.bytesToBigInt)(chainId),
            nonce,
            gasPrice,
            gasLimit,
            to,
            value,
            data,
            accessList: accessList ?? emptyAccessList,
            v: v !== undefined ? (0, util_1.bytesToBigInt)(v) : undefined,
            r,
            s,
        }, opts);
    }
    getEffectivePriorityFee(baseFee) {
        return Legacy.getEffectivePriorityFee(this.gasPrice, baseFee);
    }
    /**
     * The amount of gas paid for the data in this tx
     */
    getDataFee() {
        return EIP2930.getDataFee(this);
    }
    /**
     * The up front amount that an account must have for this transaction to be valid
     */
    getUpfrontCost() {
        return this.gasLimit * this.gasPrice + this.value;
    }
    /**
     * Returns a Uint8Array Array of the raw Bytes of the EIP-2930 transaction, in order.
     *
     * Format: `[chainId, nonce, gasPrice, gasLimit, to, value, data, accessList,
     * signatureYParity (v), signatureR (r), signatureS (s)]`
     *
     * Use {@link AccessListEIP2930Transaction.serialize} to add a transaction to a block
     * with {@link Block.fromValuesArray}.
     *
     * For an unsigned tx this method uses the empty Bytes values for the
     * signature parameters `v`, `r` and `s` for encoding. For an EIP-155 compliant
     * representation for external signing use {@link AccessListEIP2930Transaction.getMessageToSign}.
     */
    raw() {
        return [
            (0, util_1.bigIntToUnpaddedBytes)(this.chainId),
            (0, util_1.bigIntToUnpaddedBytes)(this.nonce),
            (0, util_1.bigIntToUnpaddedBytes)(this.gasPrice),
            (0, util_1.bigIntToUnpaddedBytes)(this.gasLimit),
            this.to !== undefined ? this.to.bytes : new Uint8Array(0),
            (0, util_1.bigIntToUnpaddedBytes)(this.value),
            this.data,
            this.accessList,
            this.v !== undefined ? (0, util_1.bigIntToUnpaddedBytes)(this.v) : new Uint8Array(0),
            this.r !== undefined ? (0, util_1.bigIntToUnpaddedBytes)(this.r) : new Uint8Array(0),
            this.s !== undefined ? (0, util_1.bigIntToUnpaddedBytes)(this.s) : new Uint8Array(0),
        ];
    }
    /**
     * Returns the serialized encoding of the EIP-2930 transaction.
     *
     * Format: `0x01 || rlp([chainId, nonce, gasPrice, gasLimit, to, value, data, accessList,
     * signatureYParity (v), signatureR (r), signatureS (s)])`
     *
     * Note that in contrast to the legacy tx serialization format this is not
     * valid RLP any more due to the raw tx type preceding and concatenated to
     * the RLP encoding of the values.
     */
    serialize() {
        return EIP2718.serialize(this);
    }
    /**
     * Returns the raw serialized unsigned tx, which can be used
     * to sign the transaction (e.g. for sending to a hardware wallet).
     *
     * Note: in contrast to the legacy tx the raw message format is already
     * serialized and doesn't need to be RLP encoded any more.
     *
     * ```javascript
     * const serializedMessage = tx.getMessageToSign() // use this for the HW wallet input
     * ```
     */
    getMessageToSign() {
        return EIP2718.serialize(this, this.raw().slice(0, 8));
    }
    /**
     * Returns the hashed serialized unsigned tx, which can be used
     * to sign the transaction (e.g. for sending to a hardware wallet).
     *
     * Note: in contrast to the legacy tx the raw message format is already
     * serialized and doesn't need to be RLP encoded any more.
     */
    getHashedMessageToSign() {
        return EIP2718.getHashedMessageToSign(this);
    }
    /**
     * Computes a sha3-256 hash of the serialized tx.
     *
     * This method can only be used for signed txs (it throws otherwise).
     * Use {@link AccessListEIP2930Transaction.getMessageToSign} to get a tx hash for the purpose of signing.
     */
    hash() {
        return Legacy.hash(this);
    }
    /**
     * Computes a sha3-256 hash which can be used to verify the signature
     */
    getMessageToVerifySignature() {
        return this.getHashedMessageToSign();
    }
    /**
     * Returns the public key of the sender
     */
    getSenderPublicKey() {
        return Legacy.getSenderPublicKey(this);
    }
    addSignature(v, r, s, convertV = false) {
        r = (0, util_1.toBytes)(r);
        s = (0, util_1.toBytes)(s);
        const opts = { ...this.txOptions, common: this.common };
        return AccessListEIP2930Transaction.fromTxData({
            chainId: this.chainId,
            nonce: this.nonce,
            gasPrice: this.gasPrice,
            gasLimit: this.gasLimit,
            to: this.to,
            value: this.value,
            data: this.data,
            accessList: this.accessList,
            v: convertV ? v - util_1.BIGINT_27 : v,
            r: (0, util_1.bytesToBigInt)(r),
            s: (0, util_1.bytesToBigInt)(s),
        }, opts);
    }
    /**
     * Returns an object with the JSON representation of the transaction
     */
    toJSON() {
        const accessListJSON = util_js_1.AccessLists.getAccessListJSON(this.accessList);
        const baseJson = super.toJSON();
        return {
            ...baseJson,
            chainId: (0, util_1.bigIntToHex)(this.chainId),
            gasPrice: (0, util_1.bigIntToHex)(this.gasPrice),
            accessList: accessListJSON,
        };
    }
    /**
     * Return a compact error string representation of the object
     */
    errorStr() {
        let errorStr = this._getSharedErrorPostfix();
        // Keep ? for this.accessList since this otherwise causes Hardhat E2E tests to fail
        errorStr += ` gasPrice=${this.gasPrice} accessListCount=${this.accessList?.length ?? 0}`;
        return errorStr;
    }
    /**
     * Internal helper function to create an annotated error message
     *
     * @param msg Base error message
     * @hidden
     */
    _errorMsg(msg) {
        return Legacy.errorMsg(this, msg);
    }
}
exports.AccessListEIP2930Transaction = AccessListEIP2930Transaction;
//# sourceMappingURL=eip2930Transaction.js.map