import os
import json
from typing import Dict, Optional, List
from typing_extensions import TypedDict

class TokenInfo(TypedDict, total=False):
    address: str
    name: str
    symbol: str
    decimals: int
    logoURI: Optional[str]
    chainId: int
    tags: List[str]

class PolygonBridgeTokens:
    """
    提供Polygon和Ethereum网络上的常用代币列表及其映射关系
    用于在两个网络间进行代币桥接操作
    """
    
    # Polygon主网与以太坊主网链ID
    POLYGON_CHAIN_ID = 137
    ETHEREUM_CHAIN_ID = 1
    
    # 代币数据缓存
    _token_data = None
    
    @staticmethod
    def _load_token_data() -> Dict[str, Dict[int, TokenInfo]]:
        """从JSON文件加载代币数据"""
        if PolygonBridgeTokens._token_data is None:
            # 获取tokens.json文件的路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            json_path = os.path.join(current_dir, 'data', 'tokens.json')
            
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)
                
                # 转换键为整数类型（链ID）
                processed_data = {}
                for symbol, chain_data in raw_data.items():
                    processed_data[symbol] = {}
                    for chain_id_str, token_info in chain_data.items():
                        try:
                            chain_id = int(chain_id_str)
                            processed_data[symbol][chain_id] = token_info
                        except ValueError as e:
                            print(f"警告: 跳过无效的链ID '{chain_id_str}' 对于代币 {symbol}: {e}")
                
                PolygonBridgeTokens._token_data = processed_data
            except FileNotFoundError:
                # 尝试查找其他可能的路径
                alternative_paths = [
                    os.path.join(current_dir, 'tokens.json'),
                    os.path.join(os.path.dirname(current_dir), 'data', 'tokens.json'),
                    os.path.join(os.path.dirname(os.path.dirname(current_dir)), 'data', 'tokens.json')
                ]
                
                for path in alternative_paths:
                    try:
                        print(f"尝试加载替代路径: {path}")
                        with open(path, 'r', encoding='utf-8') as f:
                            raw_data = json.load(f)
                        
                        # 转换键为整数类型（链ID）
                        processed_data = {}
                        for symbol, chain_data in raw_data.items():
                            processed_data[symbol] = {}
                            for chain_id_str, token_info in chain_data.items():
                                try:
                                    chain_id = int(chain_id_str)
                                    processed_data[symbol][chain_id] = token_info
                                except ValueError as e:
                                    print(f"警告: 跳过无效的链ID '{chain_id_str}' 对于代币 {symbol}: {e}")
                        
                        PolygonBridgeTokens._token_data = processed_data
                        break
                    except FileNotFoundError:
                        continue
                
                if PolygonBridgeTokens._token_data is None:
                    raise ValueError(f"在所有可能的路径中都找不到tokens.json文件")
            except Exception as e:
                raise ValueError(f"无法加载代币数据: {str(e)}")
        
        return PolygonBridgeTokens._token_data
    
    @staticmethod
    def get_common_tokens() -> Dict[str, Dict[int, TokenInfo]]:
        """
        获取在Polygon和Ethereum网络上都有的常用代币及其映射关系
        
        Returns:
            Dict[str, Dict[int, TokenInfo]]: 以符号为键，包含不同链上对应代币信息的字典
        """
        return PolygonBridgeTokens._load_token_data()
    
    @staticmethod
    def get_token_by_symbol(symbol: str, chain_id: int) -> Optional[TokenInfo]:
        """
        通过代币符号和链ID获取代币信息
        
        Args:
            symbol: 代币符号，例如 "USDT"
            chain_id: 链ID，例如 1(以太坊主网)或137(Polygon主网)
            
        Returns:
            Optional[TokenInfo]: 代币信息，如果未找到则返回None
        """
        tokens = PolygonBridgeTokens.get_common_tokens()
        if symbol in tokens and chain_id in tokens[symbol]:
            return tokens[symbol][chain_id]
        return None
    
    @staticmethod
    def get_token_by_address(address: str, chain_id: int) -> Optional[TokenInfo]:
        """
        通过代币地址和链ID获取代币信息
        
        Args:
            address: 代币合约地址
            chain_id: 链ID，例如 1(以太坊主网)或137(Polygon主网)
            
        Returns:
            Optional[TokenInfo]: 代币信息，如果未找到则返回None
        """
        tokens = PolygonBridgeTokens.get_common_tokens()
        address = address.lower()
        
        for symbol, chain_tokens in tokens.items():
            if chain_id in chain_tokens and chain_tokens[chain_id]["address"].lower() == address:
                return chain_tokens[chain_id]
        
        return None
    
    @staticmethod
    def get_equivalent_token(token_address: str, source_chain_id: int, target_chain_id: int) -> Optional[TokenInfo]:
        """
        获取跨链等价代币信息
        
        Args:
            token_address: 源链上的代币地址
            source_chain_id: 源链ID
            target_chain_id: 目标链ID
            
        Returns:
            Optional[TokenInfo]: 目标链上的等价代币信息，如果未找到则返回None
        """
        token = PolygonBridgeTokens.get_token_by_address(token_address, source_chain_id)
        if token is None:
            return None
            
        symbol = token["symbol"]
        tokens = PolygonBridgeTokens.get_common_tokens()
        
        if symbol in tokens and target_chain_id in tokens[symbol]:
            return tokens[symbol][target_chain_id]
            
        return None 