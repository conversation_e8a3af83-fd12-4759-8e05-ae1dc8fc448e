import { Network, NetworkEndpoints } from '@injectivelabs/networks';
import MsgExecuteContractCompat from '../../../core/modules/wasm/msgs/MsgExecuteContractCompat.js';
import { AssetInfo, AssetInfoWithPrice } from './types.js';
export declare class NeptuneService {
    private client;
    private priceOracleContract;
    /**
     * Constructs a new NeptuneService instan ce.
     * @param network The network to use (default: Mainnet).
     * @param endpoints Optional custom network endpoints.
     */
    constructor(network?: Network, endpoints?: NetworkEndpoints);
    /**
     * Fetch prices for given assets from the Neptune Price Oracle contract.
     * @param assets Array of AssetInfo objects.
     * @returns Array of Price objects.
     */
    fetchPrices(assets: AssetInfo[]): Promise<AssetInfoWithPrice[]>;
    /**
     * Fetch the redemption ratio based on CW20 and native asset prices.
     * @param cw20Asset AssetInfo for the CW20 token.
     * @param nativeAsset AssetInfo for the native token.
     * @returns Redemption ratio as a number.
     */
    fetchRedemptionRatio({ cw20Asset, nativeAsset, }: {
        cw20Asset: AssetInfo;
        nativeAsset: AssetInfo;
    }): Promise<number>;
    /**
     * Convert CW20 nUSDT to bank nUSDT using the redemption ratio.
     * @param amountCW20 Amount in CW20 nUSDT.
     * @param redemptionRatio Redemption ratio.
     * @returns Amount in bank nUSDT.
     */
    calculateBankAmount(amountCW20: number, redemptionRatio: number): number;
    /**
     * Convert bank nUSDT to CW20 nUSDT using the redemption ratio.
     * @param amountBank Amount in bank nUSDT.
     * @param redemptionRatio Redemption ratio.
     * @returns Amount in CW20 nUSDT.
     */
    calculateCw20Amount(amountBank: number, redemptionRatio: number): number;
    /**
     * Create a deposit message.
     * @param sender Sender's Injective address.
     * @param contractAddress USDT market contract address.
     * @param denom Denomination of the asset.
     * @param amount Amount to deposit as a string.
     * @returns MsgExecuteContractCompat message.
     */
    createDepositMsg({ denom, amount, sender, contractAddress, }: {
        denom: string;
        amount: string;
        sender: string;
        contractAddress?: string;
    }): MsgExecuteContractCompat;
    /**
     * Create a withdraw message.
     * @param sender Sender's Injective address.
     * @param contractAddress nUSDT contract address.
     * @param amount Amount to withdraw as a string.
     * @returns MsgExecuteContractCompat message.
     */
    createWithdrawMsg({ amount, sender, cw20ContractAddress, marketContractAddress, }: {
        amount: string;
        sender: string;
        cw20ContractAddress?: string;
        marketContractAddress?: string;
    }): MsgExecuteContractCompat;
    /**
     * Fetch lending rates with optional pagination parameters.
     * @param limit Maximum number of lending rates to fetch.
     * @param startAfter AssetInfo to start after for pagination.
     * @returns Array of [AssetInfo, Decimal256] tuples.
     */
    getLendingRates({ limit, startAfter, contractAddress, }: {
        limit?: number;
        startAfter?: AssetInfo;
        contractAddress?: string;
    }): Promise<Array<{
        assetInfo: AssetInfo;
        lendingRate: string;
    }>>;
    /**
     * Fetch the lending rate for a specific denom by querying the smart contract with pagination.
     * @param denom The denomination string of the asset to find the lending rate for.
     * @returns Lending rate as a string.
     */
    getLendingRateByDenom({ denom, contractAddress, }: {
        denom: string;
        contractAddress?: string;
    }): Promise<string | undefined>;
    /**
     * Calculates APY from APR and compounding frequency.
     *
     * @param apr - The annual percentage rate as a decimal (e.g., 0.10 for 10%)
     * @param compoundingFrequency - Number of times interest is compounded per year
     * @returns The annual percentage yield as a decimal
     */
    calculateAPY(apr: number): number;
}
