import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { OracleType } from "../../oracle/v1beta1/oracle";
import { Params } from "./insurance";
export declare const protobufPackage = "injective.insurance.v1beta1";
/**
 * MsgCreateInsuranceFund a message to create an insurance fund for a derivative
 * market.
 */
export interface MsgCreateInsuranceFund {
    /** Creator of the insurance fund. */
    sender: string;
    /** Ticker for the derivative market. */
    ticker: string;
    /** Coin denom to use for the market quote denom */
    quoteDenom: string;
    /**
     * Oracle base currency of the derivative market OR the oracle symbol for the
     * binary options market.
     */
    oracleBase: string;
    /**
     * Oracle quote currency of the derivative market OR the oracle provider for
     * the binary options market.
     */
    oracleQuote: string;
    /** Oracle type of the binary options or derivative market */
    oracleType: OracleType;
    /**
     * Expiration time of the derivative market. Should be -1 for perpetual or -2
     * for binary options markets.
     */
    expiry: string;
    /** Initial deposit of the insurance fund */
    initialDeposit: Coin | undefined;
}
export interface MsgCreateInsuranceFundResponse {
}
/**
 * MsgUnderwrite defines a message for depositing coins to underwrite an
 * insurance fund
 */
export interface MsgUnderwrite {
    /** Address of the underwriter. */
    sender: string;
    /** MarketID of the insurance fund. */
    marketId: string;
    /** Amount of quote_denom to underwrite the insurance fund. */
    deposit: Coin | undefined;
}
export interface MsgUnderwriteResponse {
}
/**
 * MsgRequestRedemption defines a message for requesting a redemption of the
 * sender's insurance fund tokens
 */
export interface MsgRequestRedemption {
    /** Address of the underwriter requesting a redemption. */
    sender: string;
    /** MarketID of the insurance fund. */
    marketId: string;
    /** Insurance fund share token amount to be redeemed. */
    amount: Coin | undefined;
}
export interface MsgRequestRedemptionResponse {
}
export interface MsgUpdateParams {
    /** authority is the address of the governance account. */
    authority: string;
    /**
     * params defines the insurance parameters to update.
     *
     * NOTE: All parameters must be supplied.
     */
    params: Params | undefined;
}
export interface MsgUpdateParamsResponse {
}
export declare const MsgCreateInsuranceFund: {
    encode(message: MsgCreateInsuranceFund, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateInsuranceFund;
    fromJSON(object: any): MsgCreateInsuranceFund;
    toJSON(message: MsgCreateInsuranceFund): unknown;
    create(base?: DeepPartial<MsgCreateInsuranceFund>): MsgCreateInsuranceFund;
    fromPartial(object: DeepPartial<MsgCreateInsuranceFund>): MsgCreateInsuranceFund;
};
export declare const MsgCreateInsuranceFundResponse: {
    encode(_: MsgCreateInsuranceFundResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateInsuranceFundResponse;
    fromJSON(_: any): MsgCreateInsuranceFundResponse;
    toJSON(_: MsgCreateInsuranceFundResponse): unknown;
    create(base?: DeepPartial<MsgCreateInsuranceFundResponse>): MsgCreateInsuranceFundResponse;
    fromPartial(_: DeepPartial<MsgCreateInsuranceFundResponse>): MsgCreateInsuranceFundResponse;
};
export declare const MsgUnderwrite: {
    encode(message: MsgUnderwrite, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUnderwrite;
    fromJSON(object: any): MsgUnderwrite;
    toJSON(message: MsgUnderwrite): unknown;
    create(base?: DeepPartial<MsgUnderwrite>): MsgUnderwrite;
    fromPartial(object: DeepPartial<MsgUnderwrite>): MsgUnderwrite;
};
export declare const MsgUnderwriteResponse: {
    encode(_: MsgUnderwriteResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUnderwriteResponse;
    fromJSON(_: any): MsgUnderwriteResponse;
    toJSON(_: MsgUnderwriteResponse): unknown;
    create(base?: DeepPartial<MsgUnderwriteResponse>): MsgUnderwriteResponse;
    fromPartial(_: DeepPartial<MsgUnderwriteResponse>): MsgUnderwriteResponse;
};
export declare const MsgRequestRedemption: {
    encode(message: MsgRequestRedemption, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRequestRedemption;
    fromJSON(object: any): MsgRequestRedemption;
    toJSON(message: MsgRequestRedemption): unknown;
    create(base?: DeepPartial<MsgRequestRedemption>): MsgRequestRedemption;
    fromPartial(object: DeepPartial<MsgRequestRedemption>): MsgRequestRedemption;
};
export declare const MsgRequestRedemptionResponse: {
    encode(_: MsgRequestRedemptionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRequestRedemptionResponse;
    fromJSON(_: any): MsgRequestRedemptionResponse;
    toJSON(_: MsgRequestRedemptionResponse): unknown;
    create(base?: DeepPartial<MsgRequestRedemptionResponse>): MsgRequestRedemptionResponse;
    fromPartial(_: DeepPartial<MsgRequestRedemptionResponse>): MsgRequestRedemptionResponse;
};
export declare const MsgUpdateParams: {
    encode(message: MsgUpdateParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParams;
    fromJSON(object: any): MsgUpdateParams;
    toJSON(message: MsgUpdateParams): unknown;
    create(base?: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
    fromPartial(object: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
};
export declare const MsgUpdateParamsResponse: {
    encode(_: MsgUpdateParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParamsResponse;
    fromJSON(_: any): MsgUpdateParamsResponse;
    toJSON(_: MsgUpdateParamsResponse): unknown;
    create(base?: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
    fromPartial(_: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
};
/** Msg defines the insurance Msg service. */
export interface Msg {
    /** CreateInsuranceFund defines a method for creating an insurance fund */
    CreateInsuranceFund(request: DeepPartial<MsgCreateInsuranceFund>, metadata?: grpc.Metadata): Promise<MsgCreateInsuranceFundResponse>;
    /**
     * Underwrite defines a method for depositing tokens to underwrite an
     * insurance fund
     */
    Underwrite(request: DeepPartial<MsgUnderwrite>, metadata?: grpc.Metadata): Promise<MsgUnderwriteResponse>;
    /**
     * RequestRedemption defines a method for requesting a redemption of the
     * sender's insurance fund tokens
     */
    RequestRedemption(request: DeepPartial<MsgRequestRedemption>, metadata?: grpc.Metadata): Promise<MsgRequestRedemptionResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    CreateInsuranceFund(request: DeepPartial<MsgCreateInsuranceFund>, metadata?: grpc.Metadata): Promise<MsgCreateInsuranceFundResponse>;
    Underwrite(request: DeepPartial<MsgUnderwrite>, metadata?: grpc.Metadata): Promise<MsgUnderwriteResponse>;
    RequestRedemption(request: DeepPartial<MsgRequestRedemption>, metadata?: grpc.Metadata): Promise<MsgRequestRedemptionResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgCreateInsuranceFundDesc: UnaryMethodDefinitionish;
export declare const MsgUnderwriteDesc: UnaryMethodDefinitionish;
export declare const MsgRequestRedemptionDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateParamsDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
