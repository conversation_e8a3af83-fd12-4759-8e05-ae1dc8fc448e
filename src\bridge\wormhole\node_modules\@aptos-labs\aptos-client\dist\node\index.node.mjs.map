{"version": 3, "sources": ["../../src/index.node.ts", "../../src/cookieJar.ts"], "sourcesContent": ["import got, {\n  OptionsOfBufferResponseBody,\n  OptionsOfJSONResponseBody,\n  RequestError,\n  Response,\n} from \"got\";\nimport { CookieJar } from \"./cookieJar\";\nimport { AptosClientRequest, AptosClientResponse } from \"./types\";\n\nconst cookieJar = new CookieJar();\n\n/**\n * Used for JSON responses\n * @param requestOptions\n */\nexport default async function aptosClient<Res>(\n  requestOptions: AptosClientRequest,\n): Promise<AptosClientResponse<Res>> {\n  return jsonRequest<Res>(requestOptions);\n}\n\nexport async function jsonRequest<Res>(\n  requestOptions: AptosClientRequest,\n): Promise<AptosClientResponse<Res>> {\n  const { params, method, url, headers, body } = requestOptions;\n\n  const request: OptionsOfJSONResponseBody = {\n    http2: true,\n    searchParams: convertBigIntToString(params),\n    method,\n    url,\n    responseType: \"json\",\n    headers,\n    hooks: {\n      beforeRequest: [\n        (options) => {\n          const cookies = cookieJar.getCookies(new URL(options.url!));\n\n          if (cookies?.length > 0 && options.headers) {\n            /* eslint-disable no-param-reassign */\n            options.headers.cookie = cookies\n              .map((cookie: any) => `${cookie.name}=${cookie.value}`)\n              .join(\"; \");\n          }\n        },\n      ],\n      afterResponse: [\n        (response) => {\n          if (Array.isArray(response.headers[\"set-cookie\"])) {\n            response.headers[\"set-cookie\"].forEach((c) => {\n              cookieJar.setCookie(new URL(response.url!), c);\n            });\n          }\n          return response;\n        },\n      ],\n    },\n  };\n\n  if (body) {\n    if (body instanceof Uint8Array) {\n      request.body = Buffer.from(body);\n    } else {\n      request.body = Buffer.from(JSON.stringify(body));\n    }\n  }\n\n  try {\n    const response = await got<Res>(request);\n    return parseResponse<Res>(response);\n  } catch (error) {\n    const gotError = error as RequestError;\n    if (gotError.response) {\n      return parseResponse<Res>(gotError.response as Response<Res>);\n    }\n    throw error;\n  }\n}\n\n/**\n * Used for binary responses, such as BCS outputs\n *\n * @experimental\n * @param requestOptions\n */\nexport async function bcsRequest(\n  requestOptions: AptosClientRequest,\n): Promise<AptosClientResponse<Buffer>> {\n  const { params, method, url, headers, body } = requestOptions;\n\n  const request: OptionsOfBufferResponseBody = {\n    http2: true,\n    searchParams: convertBigIntToString(params),\n    method,\n    url,\n    responseType: \"buffer\",\n    headers,\n    hooks: {\n      beforeRequest: [\n        (options) => {\n          const cookies = cookieJar.getCookies(new URL(options.url!));\n\n          if (cookies?.length > 0 && options.headers) {\n            /* eslint-disable no-param-reassign */\n            options.headers.cookie = cookies\n              .map((cookie: any) => `${cookie.name}=${cookie.value}`)\n              .join(\"; \");\n          }\n        },\n      ],\n      afterResponse: [\n        (response) => {\n          if (Array.isArray(response.headers[\"set-cookie\"])) {\n            response.headers[\"set-cookie\"].forEach((c) => {\n              cookieJar.setCookie(new URL(response.url!), c);\n            });\n          }\n          return response;\n        },\n      ],\n    },\n  };\n\n  if (body) {\n    if (body instanceof Uint8Array) {\n      request.body = Buffer.from(body);\n    } else {\n      request.body = Buffer.from(JSON.stringify(body));\n    }\n  }\n\n  try {\n    switch (method) {\n      case \"GET\":\n        return parseResponse<Buffer>(await got.get(request));\n      case \"POST\":\n        return parseResponse<Buffer>(await got.post(request));\n      default:\n      // Do nothing, fall through\n    }\n  } catch (error) {\n    const gotError = error as RequestError;\n    if (gotError.response) {\n      return parseResponse<Buffer>(gotError.response as Response<Buffer>);\n    }\n    throw error;\n  }\n\n  throw new Error(`Unsupported method: ${method}`);\n}\n\nfunction parseResponse<Res>(response: Response<Res>): AptosClientResponse<Res> {\n  return {\n    status: response.statusCode,\n    statusText: response.statusMessage || \"\",\n    data: response.body,\n    config: response.request.options,\n    request: response.request,\n    response,\n    headers: response.headers,\n  };\n}\n\n/**\n * got supports only - string | number | boolean | null | undefined as searchParam value,\n * so if we have bigint type, convert it to string\n */\nfunction convertBigIntToString(obj: any): any {\n  const result: any = {};\n  if (!obj) return result;\n\n  Object.entries(obj).forEach(([key, value]) => {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      if (typeof value === \"bigint\") {\n        result[key] = String(value);\n      } else {\n        result[key] = value;\n      }\n    }\n  });\n\n  return result;\n}\n", "interface Cookie {\n  name: string;\n  value: string;\n  expires?: Date;\n  path?: string;\n  sameSite?: \"Lax\" | \"None\" | \"Strict\";\n  secure?: boolean;\n}\n\nexport class CookieJar {\n  constructor(private jar = new Map<string, Cookie[]>()) {}\n\n  setCookie(url: URL, cookieStr: string) {\n    const key = url.origin.toLowerCase();\n    if (!this.jar.has(key)) {\n      this.jar.set(key, []);\n    }\n\n    const cookie = CookieJar.parse(cookieStr);\n    this.jar.set(key, [\n      ...(this.jar.get(key)?.filter((c) => c.name !== cookie.name) || []),\n      cookie,\n    ]);\n  }\n\n  getCookies(url: URL): <PERSON>ie[] {\n    const key = url.origin.toLowerCase();\n    if (!this.jar.get(key)) {\n      return [];\n    }\n\n    // Filter out expired cookies\n    return (\n      this.jar\n        .get(key)\n        ?.filter((cookie) => !cookie.expires || cookie.expires > new Date()) ||\n      []\n    );\n  }\n\n  static parse(str: string): <PERSON><PERSON> {\n    if (typeof str !== \"string\") {\n      throw new Error(\"argument str must be a string\");\n    }\n\n    const parts = str.split(\";\").map((part) => part.trim());\n\n    let cookie: Cookie;\n\n    if (parts.length > 0) {\n      const [name, value] = parts[0].split(\"=\");\n      if (!name || !value) {\n        throw new Error(\"Invalid cookie\");\n      }\n\n      cookie = {\n        name,\n        value,\n      };\n    } else {\n      throw new Error(\"Invalid cookie\");\n    }\n\n    parts.slice(1).forEach((part) => {\n      const [name, value] = part.split(\"=\");\n      if (!name.trim()) {\n        throw new Error(\"Invalid cookie\");\n      }\n\n      const nameLow = name.toLowerCase();\n      const val =\n        // eslint-disable-next-line quotes\n        value?.charAt(0) === \"'\" || value?.charAt(0) === '\"'\n          ? value?.slice(1, -1)\n          : value;\n      if (nameLow === \"expires\") {\n        cookie.expires = new Date(val);\n      }\n      if (nameLow === \"path\") {\n        cookie.path = val;\n      }\n      if (nameLow === \"samesite\") {\n        if (val !== \"Lax\" && val !== \"None\" && val !== \"Strict\") {\n          throw new Error(\"Invalid cookie SameSite value\");\n        }\n        cookie.sameSite = val;\n      }\n      if (nameLow === \"secure\") {\n        cookie.secure = true;\n      }\n    });\n\n    return cookie;\n  }\n}\n"], "mappings": ";AAAA,OAAO,SAKA;;;ACIA,IAAM,YAAN,MAAM,WAAU;AAAA,EACrB,YAAoB,MAAM,oBAAI,IAAsB,GAAG;AAAnC;AAAA,EAAoC;AAAA,EAExD,UAAU,KAAU,WAAmB;AAZzC;AAaI,UAAM,MAAM,IAAI,OAAO,YAAY;AACnC,QAAI,CAAC,KAAK,IAAI,IAAI,GAAG,GAAG;AACtB,WAAK,IAAI,IAAI,KAAK,CAAC,CAAC;AAAA,IACtB;AAEA,UAAM,SAAS,WAAU,MAAM,SAAS;AACxC,SAAK,IAAI,IAAI,KAAK;AAAA,MAChB,KAAI,UAAK,IAAI,IAAI,GAAG,MAAhB,mBAAmB,OAAO,CAAC,MAAM,EAAE,SAAS,OAAO,UAAS,CAAC;AAAA,MACjE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,WAAW,KAAoB;AAzBjC;AA0BI,UAAM,MAAM,IAAI,OAAO,YAAY;AACnC,QAAI,CAAC,KAAK,IAAI,IAAI,GAAG,GAAG;AACtB,aAAO,CAAC;AAAA,IACV;AAGA,aACE,UAAK,IACF,IAAI,GAAG,MADV,mBAEI,OAAO,CAAC,WAAW,CAAC,OAAO,WAAW,OAAO,UAAU,oBAAI,KAAK,OACpE,CAAC;AAAA,EAEL;AAAA,EAEA,OAAO,MAAM,KAAqB;AAChC,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AAEA,UAAM,QAAQ,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC;AAEtD,QAAI;AAEJ,QAAI,MAAM,SAAS,GAAG;AACpB,YAAM,CAAC,MAAM,KAAK,IAAI,MAAM,CAAC,EAAE,MAAM,GAAG;AACxC,UAAI,CAAC,QAAQ,CAAC,OAAO;AACnB,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AAEA,eAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,gBAAgB;AAAA,IAClC;AAEA,UAAM,MAAM,CAAC,EAAE,QAAQ,CAAC,SAAS;AAC/B,YAAM,CAAC,MAAM,KAAK,IAAI,KAAK,MAAM,GAAG;AACpC,UAAI,CAAC,KAAK,KAAK,GAAG;AAChB,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AAEA,YAAM,UAAU,KAAK,YAAY;AACjC,YAAM;AAAA;AAAA,SAEJ,+BAAO,OAAO,QAAO,QAAO,+BAAO,OAAO,QAAO,MAC7C,+BAAO,MAAM,GAAG,MAChB;AAAA;AACN,UAAI,YAAY,WAAW;AACzB,eAAO,UAAU,IAAI,KAAK,GAAG;AAAA,MAC/B;AACA,UAAI,YAAY,QAAQ;AACtB,eAAO,OAAO;AAAA,MAChB;AACA,UAAI,YAAY,YAAY;AAC1B,YAAI,QAAQ,SAAS,QAAQ,UAAU,QAAQ,UAAU;AACvD,gBAAM,IAAI,MAAM,+BAA+B;AAAA,QACjD;AACA,eAAO,WAAW;AAAA,MACpB;AACA,UAAI,YAAY,UAAU;AACxB,eAAO,SAAS;AAAA,MAClB;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AACF;;;ADrFA,IAAM,YAAY,IAAI,UAAU;AAMhC,eAAO,YACL,gBACmC;AACnC,SAAO,YAAiB,cAAc;AACxC;AAEA,eAAsB,YACpB,gBACmC;AACnC,QAAM,EAAE,QAAQ,QAAQ,KAAK,SAAS,KAAK,IAAI;AAE/C,QAAM,UAAqC;AAAA,IACzC,OAAO;AAAA,IACP,cAAc,sBAAsB,MAAM;AAAA,IAC1C;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,OAAO;AAAA,MACL,eAAe;AAAA,QACb,CAAC,YAAY;AACX,gBAAM,UAAU,UAAU,WAAW,IAAI,IAAI,QAAQ,GAAI,CAAC;AAE1D,eAAI,mCAAS,UAAS,KAAK,QAAQ,SAAS;AAE1C,oBAAQ,QAAQ,SAAS,QACtB,IAAI,CAAC,WAAgB,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK,EAAE,EACrD,KAAK,IAAI;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb,CAAC,aAAa;AACZ,cAAI,MAAM,QAAQ,SAAS,QAAQ,YAAY,CAAC,GAAG;AACjD,qBAAS,QAAQ,YAAY,EAAE,QAAQ,CAAC,MAAM;AAC5C,wBAAU,UAAU,IAAI,IAAI,SAAS,GAAI,GAAG,CAAC;AAAA,YAC/C,CAAC;AAAA,UACH;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM;AACR,QAAI,gBAAgB,YAAY;AAC9B,cAAQ,OAAO,OAAO,KAAK,IAAI;AAAA,IACjC,OAAO;AACL,cAAQ,OAAO,OAAO,KAAK,KAAK,UAAU,IAAI,CAAC;AAAA,IACjD;AAAA,EACF;AAEA,MAAI;AACF,UAAM,WAAW,MAAM,IAAS,OAAO;AACvC,WAAO,cAAmB,QAAQ;AAAA,EACpC,SAAS,OAAO;AACd,UAAM,WAAW;AACjB,QAAI,SAAS,UAAU;AACrB,aAAO,cAAmB,SAAS,QAAyB;AAAA,IAC9D;AACA,UAAM;AAAA,EACR;AACF;AAQA,eAAsB,WACpB,gBACsC;AACtC,QAAM,EAAE,QAAQ,QAAQ,KAAK,SAAS,KAAK,IAAI;AAE/C,QAAM,UAAuC;AAAA,IAC3C,OAAO;AAAA,IACP,cAAc,sBAAsB,MAAM;AAAA,IAC1C;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,OAAO;AAAA,MACL,eAAe;AAAA,QACb,CAAC,YAAY;AACX,gBAAM,UAAU,UAAU,WAAW,IAAI,IAAI,QAAQ,GAAI,CAAC;AAE1D,eAAI,mCAAS,UAAS,KAAK,QAAQ,SAAS;AAE1C,oBAAQ,QAAQ,SAAS,QACtB,IAAI,CAAC,WAAgB,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK,EAAE,EACrD,KAAK,IAAI;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb,CAAC,aAAa;AACZ,cAAI,MAAM,QAAQ,SAAS,QAAQ,YAAY,CAAC,GAAG;AACjD,qBAAS,QAAQ,YAAY,EAAE,QAAQ,CAAC,MAAM;AAC5C,wBAAU,UAAU,IAAI,IAAI,SAAS,GAAI,GAAG,CAAC;AAAA,YAC/C,CAAC;AAAA,UACH;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM;AACR,QAAI,gBAAgB,YAAY;AAC9B,cAAQ,OAAO,OAAO,KAAK,IAAI;AAAA,IACjC,OAAO;AACL,cAAQ,OAAO,OAAO,KAAK,KAAK,UAAU,IAAI,CAAC;AAAA,IACjD;AAAA,EACF;AAEA,MAAI;AACF,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,eAAO,cAAsB,MAAM,IAAI,IAAI,OAAO,CAAC;AAAA,MACrD,KAAK;AACH,eAAO,cAAsB,MAAM,IAAI,KAAK,OAAO,CAAC;AAAA,MACtD;AAAA,IAEF;AAAA,EACF,SAAS,OAAO;AACd,UAAM,WAAW;AACjB,QAAI,SAAS,UAAU;AACrB,aAAO,cAAsB,SAAS,QAA4B;AAAA,IACpE;AACA,UAAM;AAAA,EACR;AAEA,QAAM,IAAI,MAAM,uBAAuB,MAAM,EAAE;AACjD;AAEA,SAAS,cAAmB,UAAmD;AAC7E,SAAO;AAAA,IACL,QAAQ,SAAS;AAAA,IACjB,YAAY,SAAS,iBAAiB;AAAA,IACtC,MAAM,SAAS;AAAA,IACf,QAAQ,SAAS,QAAQ;AAAA,IACzB,SAAS,SAAS;AAAA,IAClB;AAAA,IACA,SAAS,SAAS;AAAA,EACpB;AACF;AAMA,SAAS,sBAAsB,KAAe;AAC5C,QAAM,SAAc,CAAC;AACrB,MAAI,CAAC,IAAK,QAAO;AAEjB,SAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC5C,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAClD,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO,GAAG,IAAI,OAAO,KAAK;AAAA,MAC5B,OAAO;AACL,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;", "names": []}