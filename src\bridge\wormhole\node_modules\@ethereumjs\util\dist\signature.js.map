{"version": 3, "file": "signature.js", "sourceRoot": "", "sources": ["../src/signature.ts"], "names": [], "mappings": ";;;AAAA,yDAAwD;AACxD,+DAA2D;AAE3D,mCAA2F;AAC3F,2CAAoE;AACpE,uCAA0C;AAQ1C;;;;;GAKG;AACH,SAAgB,MAAM,CAAC,OAAe,EAAE,UAAkB,EAAE,OAAgB;IAC1E,MAAM,GAAG,GAAG,qBAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IAC/C,MAAM,GAAG,GAAG,GAAG,CAAC,iBAAiB,EAAE,CAAA;IACnC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IACvC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;IAExC,MAAM,CAAC,GACL,OAAO,KAAK,SAAS;QACnB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAS,GAAG,EAAE,CAAC;QAC5B,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAS,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;IAE9D,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAA;AACpB,CAAC;AAZD,wBAYC;AAED,SAAS,oBAAoB,CAAC,CAAS,EAAE,OAAgB;IACvD,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC;QAAE,OAAO,CAAC,CAAA;IAEhD,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAA;KACtB;IACD,OAAO,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;AAC/C,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB;IAC1C,OAAO,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,CAAA;AACzD,CAAC;AAED;;;;GAIG;AACI,MAAM,SAAS,GAAG,UACvB,OAAe,EACf,CAAS,EACT,CAAS,EACT,CAAS,EACT,OAAgB;IAEhB,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IACjF,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,MAAM,GAAG,GAAG,qBAAS,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;IACvF,MAAM,YAAY,GAAG,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;IAClD,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC7D,CAAC,CAAA;AAhBY,QAAA,SAAS,aAgBrB;AAED;;;;GAIG;AACI,MAAM,QAAQ,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,OAAgB;IACjF,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,6EAA6E;IAC7E,OAAO,IAAA,mBAAW,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9F,CAAC,CAAA;AARY,QAAA,QAAQ,YAQpB;AAED;;;;GAIG;AACI,MAAM,YAAY,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,OAAgB;IACrF,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;KAC7C;IAED,IAAI,EAAE,GAAG,CAAC,CAAA;IACV,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE;QAC1F,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACd;IAED,OAAO,IAAA,mBAAW,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAA,qBAAa,EAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAA,qBAAa,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;AAClF,CAAC,CAAA;AAbY,QAAA,YAAY,gBAaxB;AAED;;;;;;;GAOG;AACI,MAAM,UAAU,GAAG,UAAU,GAAW;IAC7C,MAAM,GAAG,GAAW,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAA;IAEjC,IAAI,CAAS,CAAA;IACb,IAAI,CAAS,CAAA;IACb,IAAI,CAAS,CAAA;IACb,IAAI,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE;QACpB,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACpB,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QACrB,CAAC,GAAG,IAAA,sBAAc,EAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;KAClC;SAAM,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE;QAC5B,6EAA6E;QAC7E,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACpB,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;QACrB,CAAC,GAAG,MAAM,CAAC,IAAA,mBAAW,EAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/C,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;KACb;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;KAC5C;IAED,gDAAgD;IAChD,IAAI,CAAC,GAAG,EAAE,EAAE;QACV,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAA;KACnB;IAED,OAAO;QACL,CAAC;QACD,CAAC;QACD,CAAC;KACF,CAAA;AACH,CAAC,CAAA;AA9BY,QAAA,UAAU,cA8BtB;AAED;;;;GAIG;AACI,MAAM,gBAAgB,GAAG,UAC9B,CAAS,EACT,CAAS,EACT,CAAS,EACT,mBAA4B,IAAI,EAChC,OAAgB;IAEhB,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;QACtC,OAAO,KAAK,CAAA;KACb;IAED,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE;QACzD,OAAO,KAAK,CAAA;KACb;IAED,MAAM,OAAO,GAAG,IAAA,sBAAc,EAAC,CAAC,CAAC,CAAA;IACjC,MAAM,OAAO,GAAG,IAAA,sBAAc,EAAC,CAAC,CAAC,CAAA;IAEjC,IACE,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,2BAAe;QAC1B,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,2BAAe,EAC1B;QACA,OAAO,KAAK,CAAA;KACb;IAED,IAAI,gBAAgB,IAAI,OAAO,IAAI,iCAAqB,EAAE;QACxD,OAAO,KAAK,CAAA;KACb;IAED,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAhCY,QAAA,gBAAgB,oBAgC5B;AAED;;;;;GAKG;AACI,MAAM,mBAAmB,GAAG,UAAU,OAAe;IAC1D,IAAA,wBAAc,EAAC,OAAO,CAAC,CAAA;IACvB,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAA;IACxF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AACjE,CAAC,CAAA;AAJY,QAAA,mBAAmB,uBAI/B"}