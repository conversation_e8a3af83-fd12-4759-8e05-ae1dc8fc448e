import { grpc } from "@injectivelabs/grpc-web";
import { ApplySnapshotChunkRequest, ApplySnapshotChunkResponse, CheckTxRequest, CheckTxResponse, CommitRequest, CommitResponse, EchoRequest, EchoResponse, ExtendVoteRequest, ExtendVoteResponse, FinalizeBlockRequest, FinalizeBlockResponse, FlushRequest, FlushResponse, InfoRequest, InfoResponse, InitChainRequest, InitChainResponse, ListSnapshotsRequest, ListSnapshotsResponse, LoadSnapshotChunkRequest, LoadSnapshotChunkResponse, OfferSnapshotRequest, OfferSnapshotResponse, PrepareProposalRequest, PrepareProposalResponse, ProcessProposalRequest, ProcessProposalResponse, QueryRequest, QueryResponse, VerifyVoteExtensionRequest, VerifyVoteExtensionResponse } from "./types";
export declare const protobufPackage = "cometbft.abci.v1";
/** ABCIService is a service for an ABCI application. */
export interface ABCIService {
    /** Echo returns back the same message it is sent. */
    Echo(request: DeepPartial<EchoRequest>, metadata?: grpc.Metadata): Promise<EchoResponse>;
    /** Flush flushes the write buffer. */
    Flush(request: DeepPartial<FlushRequest>, metadata?: grpc.Metadata): Promise<FlushResponse>;
    /** Info returns information about the application state. */
    Info(request: DeepPartial<InfoRequest>, metadata?: grpc.Metadata): Promise<InfoResponse>;
    /** CheckTx validates a transaction. */
    CheckTx(request: DeepPartial<CheckTxRequest>, metadata?: grpc.Metadata): Promise<CheckTxResponse>;
    /** Query queries the application state. */
    Query(request: DeepPartial<QueryRequest>, metadata?: grpc.Metadata): Promise<QueryResponse>;
    /** Commit commits a block of transactions. */
    Commit(request: DeepPartial<CommitRequest>, metadata?: grpc.Metadata): Promise<CommitResponse>;
    /** InitChain initializes the blockchain. */
    InitChain(request: DeepPartial<InitChainRequest>, metadata?: grpc.Metadata): Promise<InitChainResponse>;
    /** ListSnapshots lists all the available snapshots. */
    ListSnapshots(request: DeepPartial<ListSnapshotsRequest>, metadata?: grpc.Metadata): Promise<ListSnapshotsResponse>;
    /** OfferSnapshot sends a snapshot offer. */
    OfferSnapshot(request: DeepPartial<OfferSnapshotRequest>, metadata?: grpc.Metadata): Promise<OfferSnapshotResponse>;
    /** LoadSnapshotChunk returns a chunk of snapshot. */
    LoadSnapshotChunk(request: DeepPartial<LoadSnapshotChunkRequest>, metadata?: grpc.Metadata): Promise<LoadSnapshotChunkResponse>;
    /** ApplySnapshotChunk applies a chunk of snapshot. */
    ApplySnapshotChunk(request: DeepPartial<ApplySnapshotChunkRequest>, metadata?: grpc.Metadata): Promise<ApplySnapshotChunkResponse>;
    /** PrepareProposal returns a proposal for the next block. */
    PrepareProposal(request: DeepPartial<PrepareProposalRequest>, metadata?: grpc.Metadata): Promise<PrepareProposalResponse>;
    /** ProcessProposal validates a proposal. */
    ProcessProposal(request: DeepPartial<ProcessProposalRequest>, metadata?: grpc.Metadata): Promise<ProcessProposalResponse>;
    /** ExtendVote extends a vote with application-injected data (vote extensions). */
    ExtendVote(request: DeepPartial<ExtendVoteRequest>, metadata?: grpc.Metadata): Promise<ExtendVoteResponse>;
    /** VerifyVoteExtension verifies a vote extension. */
    VerifyVoteExtension(request: DeepPartial<VerifyVoteExtensionRequest>, metadata?: grpc.Metadata): Promise<VerifyVoteExtensionResponse>;
    /** FinalizeBlock finalizes a block. */
    FinalizeBlock(request: DeepPartial<FinalizeBlockRequest>, metadata?: grpc.Metadata): Promise<FinalizeBlockResponse>;
}
export declare class ABCIServiceClientImpl implements ABCIService {
    private readonly rpc;
    constructor(rpc: Rpc);
    Echo(request: DeepPartial<EchoRequest>, metadata?: grpc.Metadata): Promise<EchoResponse>;
    Flush(request: DeepPartial<FlushRequest>, metadata?: grpc.Metadata): Promise<FlushResponse>;
    Info(request: DeepPartial<InfoRequest>, metadata?: grpc.Metadata): Promise<InfoResponse>;
    CheckTx(request: DeepPartial<CheckTxRequest>, metadata?: grpc.Metadata): Promise<CheckTxResponse>;
    Query(request: DeepPartial<QueryRequest>, metadata?: grpc.Metadata): Promise<QueryResponse>;
    Commit(request: DeepPartial<CommitRequest>, metadata?: grpc.Metadata): Promise<CommitResponse>;
    InitChain(request: DeepPartial<InitChainRequest>, metadata?: grpc.Metadata): Promise<InitChainResponse>;
    ListSnapshots(request: DeepPartial<ListSnapshotsRequest>, metadata?: grpc.Metadata): Promise<ListSnapshotsResponse>;
    OfferSnapshot(request: DeepPartial<OfferSnapshotRequest>, metadata?: grpc.Metadata): Promise<OfferSnapshotResponse>;
    LoadSnapshotChunk(request: DeepPartial<LoadSnapshotChunkRequest>, metadata?: grpc.Metadata): Promise<LoadSnapshotChunkResponse>;
    ApplySnapshotChunk(request: DeepPartial<ApplySnapshotChunkRequest>, metadata?: grpc.Metadata): Promise<ApplySnapshotChunkResponse>;
    PrepareProposal(request: DeepPartial<PrepareProposalRequest>, metadata?: grpc.Metadata): Promise<PrepareProposalResponse>;
    ProcessProposal(request: DeepPartial<ProcessProposalRequest>, metadata?: grpc.Metadata): Promise<ProcessProposalResponse>;
    ExtendVote(request: DeepPartial<ExtendVoteRequest>, metadata?: grpc.Metadata): Promise<ExtendVoteResponse>;
    VerifyVoteExtension(request: DeepPartial<VerifyVoteExtensionRequest>, metadata?: grpc.Metadata): Promise<VerifyVoteExtensionResponse>;
    FinalizeBlock(request: DeepPartial<FinalizeBlockRequest>, metadata?: grpc.Metadata): Promise<FinalizeBlockResponse>;
}
export declare const ABCIServiceDesc: {
    serviceName: string;
};
export declare const ABCIServiceEchoDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceFlushDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceInfoDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceCheckTxDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceQueryDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceCommitDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceInitChainDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceListSnapshotsDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceOfferSnapshotDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceLoadSnapshotChunkDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceApplySnapshotChunkDesc: UnaryMethodDefinitionish;
export declare const ABCIServicePrepareProposalDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceProcessProposalDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceExtendVoteDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceVerifyVoteExtensionDesc: UnaryMethodDefinitionish;
export declare const ABCIServiceFinalizeBlockDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
