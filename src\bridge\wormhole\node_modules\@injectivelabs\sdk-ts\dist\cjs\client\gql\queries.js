"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.USER_BRIDGE_DEPOSITS = exports.USER_DEPOSITS = void 0;
const client_1 = require("@apollo/client");
/**
 * @hidden
 */
exports.USER_DEPOSITS = (0, client_1.gql) `
  query Deposits($destination: Bytes!) {
    deposits(
      orderBy: timestamp
      orderDirection: desc
      first: 10
      where: { destination: $destination }
    ) {
      id
      tokenContract
      sender
      destination
      eventNonce
      amount
      timestamp
      blockHeight
    }
  }
`;
exports.USER_BRIDGE_DEPOSITS = (0, client_1.gql) `
  query Deposits($sender: Bytes!, $timestamp: Int!) {
    deposits(
      orderBy: timestamp
      orderDirection: desc
      where: { timestamp_gte: $timestamp, sender: $sender }
    ) {
      id
      tokenContract
      sender
      destination
      eventNonce
      amount
      timestamp
      blockHeight
    }
  }
`;
