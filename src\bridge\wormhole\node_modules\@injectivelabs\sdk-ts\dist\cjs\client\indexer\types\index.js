"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerModule = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
__exportStar(require("./mito.js"), exports);
__exportStar(require("./swap.js"), exports);
__exportStar(require("./spot.js"), exports);
__exportStar(require("./oracle.js"), exports);
__exportStar(require("./account.js"), exports);
__exportStar(require("./auction.js"), exports);
__exportStar(require("./trading.js"), exports);
__exportStar(require("./archiver.js"), exports);
__exportStar(require("./exchange.js"), exports);
__exportStar(require("./explorer.js"), exports);
__exportStar(require("./referral.js"), exports);
__exportStar(require("./campaign.js"), exports);
__exportStar(require("./spot-rest.js"), exports);
__exportStar(require("./incentives.js"), exports);
__exportStar(require("./derivatives.js"), exports);
__exportStar(require("./explorer-rest.js"), exports);
__exportStar(require("./insurance-funds.js"), exports);
__exportStar(require("./derivatives-rest.js"), exports);
__exportStar(require("./leaderboard-rest.js"), exports);
__exportStar(require("./account-portfolio.js"), exports);
__exportStar(require("./markets-history-rest.js"), exports);
exports.IndexerModule = { ...exceptions_1.IndexerErrorModule };
