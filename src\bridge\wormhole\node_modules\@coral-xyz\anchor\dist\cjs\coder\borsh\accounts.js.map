{"version": 3, "file": "accounts.js", "sourceRoot": "", "sources": ["../../../../src/coder/borsh/accounts.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AACxB,mCAAgC;AAEhC,0DAAkC;AAElC,qCAAoC;AAEpC,4CAA2C;AAC3C,yDAAuE;AAEvE;;GAEG;AACH,MAAa,kBAAkB;IAa7B,YAAmB,GAAQ;QACzB,IAAI,GAAG,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;YAChC,OAAO;SACR;QACD,MAAM,OAAO,GAAkB,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACtD,OAAO,CAAC,GAAG,CAAC,IAAS,EAAE,iBAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,MAAM,CAAU,WAAc,EAAE,OAAU;QACrD,MAAM,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,8BAA8B;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,EAAE,CAAC,CAAC;SACpD;QACD,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3C,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACvC,IAAI,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACzE,OAAO,eAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,MAAM,CAAU,WAAc,EAAE,IAAY;QACjD,+CAA+C;QAC/C,MAAM,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC3E,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,SAAS,CAAU,IAAY;QACpC,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CACtE,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAC1E,CAAC;QACF,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACpD;QAED,OAAO,IAAI,CAAC,eAAe,CAAI,WAAkB,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAEM,eAAe,CAAU,WAAc,EAAE,EAAU;QACxD,8CAA8C;QAC9C,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,qCAAkB,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,EAAE,CAAC,CAAC;SACpD;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEM,MAAM,CAAC,WAAc,EAAE,UAAmB;QAC/C,MAAM,aAAa,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC3E,OAAO;YACL,MAAM,EAAE,CAAC;YACT,KAAK,EAAE,cAAI,CAAC,MAAM,CAChB,UAAU,CAAC,CAAC,CAAC,eAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CACxE;SACF,CAAC;IACJ,CAAC;IAEM,IAAI,CAAC,UAAsB;;QAChC,OAAO,qCAAkB,GAAG,CAAC,MAAA,IAAA,uBAAW,EAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,mCAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,oBAAoB,CAAC,IAAY;QAC7C,MAAM,qBAAqB,GAAG,WAAW,IAAA,mBAAS,EAAC,IAAI,EAAE;YACvD,UAAU,EAAE,IAAI;YAChB,4BAA4B,EAAE,IAAI;SACnC,CAAC,EAAE,CAAC;QACL,OAAO,IAAA,gCAAa,EAAC,qBAAqB,CAAC,CAAC;IAC9C,CAAC;CACF;AA/FD,gDA+FC"}