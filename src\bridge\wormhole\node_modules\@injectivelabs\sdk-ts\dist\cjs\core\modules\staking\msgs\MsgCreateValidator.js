"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const index_js_1 = require("../../../tx/index.js");
const exceptions_1 = require("@injectivelabs/exceptions");
const numbers_js_1 = require("../../../../utils/numbers.js");
/**
 * @category Messages
 */
class MsgCreateValidator extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgCreateValidator(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.CosmosStakingV1Beta1Tx.MsgCreateValidator.create();
        if (params.description) {
            const description = core_proto_ts_1.CosmosStakingV1Beta1Staking.Description.create();
            if (params.description.moniker) {
                description.moniker = params.description.moniker;
            }
            if (params.description.identity) {
                description.identity = params.description.identity;
            }
            if (params.description.website) {
                description.website = params.description.website;
            }
            if (params.description.securityContact) {
                description.securityContact = params.description.securityContact;
            }
            if (params.description.details) {
                description.details = params.description.details;
            }
            message.description = description;
        }
        if (params.commission) {
            const commissionRate = core_proto_ts_1.CosmosStakingV1Beta1Staking.CommissionRates.create();
            commissionRate.rate = params.commission.rate;
            commissionRate.maxRate = params.commission.maxRate;
            commissionRate.maxChangeRate = params.commission.maxChangeRate;
            message.commission = commissionRate;
        }
        if (params.minSelfDelegation) {
            message.minSelfDelegation = params.minSelfDelegation;
        }
        message.delegatorAddress = params.delegatorAddress;
        message.validatorAddress = params.validatorAddress;
        if (params.pubKey) {
            const pubKeyAny = (0, index_js_1.createAny)(Buffer.from(params.pubKey.value, 'base64'), params.pubKey.type);
            message.pubkey = pubKeyAny;
        }
        if (params.value) {
            const coin = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
            coin.denom = params.value.denom;
            coin.amount = params.value.amount;
            message.value = coin;
        }
        return core_proto_ts_1.CosmosStakingV1Beta1Tx.MsgCreateValidator.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.staking.v1beta1.MsgCreateValidator',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'cosmos-sdk/MsgCreateValidator',
            value: message,
        };
    }
    toWeb3Gw() {
        const { params } = this;
        const { value } = this.toAmino();
        const messageWithPubKeyType = {
            ...value,
            pubkey: {
                '@type': params.pubKey.type,
                key: params.pubKey.value,
            },
        };
        return {
            '@type': '/cosmos.staking.v1beta1.MsgCreateValidator',
            ...messageWithPubKeyType,
        };
    }
    toEip712() {
        throw new exceptions_1.GeneralException(new Error('EIP712_v1 is not supported for MsgCreateValidator. Please use EIP712_v2'));
    }
    toEip712V2() {
        const web3gw = this.toWeb3Gw();
        const commission = web3gw.commission;
        const messageAdjusted = {
            ...web3gw,
            commission: web3gw.commission
                ? {
                    rate: (0, numbers_js_1.numberToCosmosSdkDecString)(commission.rate),
                    max_rate: (0, numbers_js_1.numberToCosmosSdkDecString)(commission.max_rate),
                    max_change_rate: (0, numbers_js_1.numberToCosmosSdkDecString)(commission.max_change_rate),
                }
                : undefined,
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.staking.v1beta1.MsgCreateValidator',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosStakingV1Beta1Tx.MsgCreateValidator.encode(this.toProto()).finish();
    }
}
exports.default = MsgCreateValidator;
