// @flow strict

export type Version = 0 | 1
export type Codec = string
export type Multihash = Buffer
export type BaseEncodedString = string
export type MultibaseName = string

declare class CID<a> {
  constructor(Version, Codec, Multihash, multibaseName?:MultibaseName): void;
  constructor(BaseEncodedString): void;
  constructor(Buffer): void;

  +codec: Codec;
  +multihash: Multihash;
  +buffer: Buffer;
  +prefix: Buffer;

  toV0(): CID<a>;
  toV1(): CID<a>;
  toBaseEncodedString(base?: string): BaseEncodedString;
  toString(): BaseEncodedString;
  toJSON(): { codec: Codec, version: Version, hash: Multihash };

  equals(mixed): boolean;

  static codecs: { [string]: Codec };
  static isCID(mixed): boolean;
  static validateCID(mixed): void;
}

export default CID
export type { CID }
