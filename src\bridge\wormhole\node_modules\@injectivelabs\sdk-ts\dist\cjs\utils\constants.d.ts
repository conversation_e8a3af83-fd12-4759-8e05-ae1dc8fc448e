import { DEFAULT_STD_FEE, DEFAULT_STD_FEE_BY_DENOM } from '@injectivelabs/utils';
export declare const BECH32_PUBKEY_ACC_PREFIX = "injpub";
export declare const BECH32_PUBKEY_VAL_PREFIX = "injvaloperpub";
export declare const BECH32_PUBKEY_CONS_PREFIX = "injvalconspub";
export declare const BECH32_ADDR_ACC_PREFIX = "inj";
export declare const BECH32_ADDR_VAL_PREFIX = "injvaloper";
export declare const BECH32_ADDR_CONS_PREFIX = "injvalcons";
export declare const DEFAULT_DERIVATION_PATH = "m/44'/60'/0'/0/0";
export { DEFAULT_STD_FEE, DEFAULT_STD_FEE_BY_DENOM };
