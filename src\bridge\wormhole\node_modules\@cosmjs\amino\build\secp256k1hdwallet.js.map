{"version": 3, "file": "secp256k1hdwallet.js", "sourceRoot": "", "sources": ["../src/secp256k1hdwallet.ts"], "names": [], "mappings": ";;;AAAA,2CAYwB;AACxB,+CAAoF;AACpF,yCAAwD;AAExD,2CAA6D;AAC7D,mCAA4C;AAC5C,2CAAuD;AACvD,uCAAyD;AAEzD,qCAOkB;AAMlB,MAAM,mBAAmB,GAAG,oBAAoB,CAAC;AAEjD;;;GAGG;AACH,MAAM,2BAA2B,GAAqB;IACpD,SAAS,EAAE,UAAU;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,EAAE;QAChB,QAAQ,EAAE,EAAE;QACZ,WAAW,EAAE,EAAE,GAAG,IAAI;KACvB;CACF,CAAC;AA0BF,SAAS,gBAAgB,CAAC,KAAc;IACtC,IAAI,CAAC,IAAA,uBAAe,EAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAC1C,IAAI,OAAQ,KAA4B,CAAC,MAAM,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAC3E,IAAI,OAAQ,KAA4B,CAAC,MAAM,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAC3E,OAAO,IAAI,CAAC;AACd,CAAC;AAWD,SAAS,yBAAyB,CAAC,GAAQ;IACzC,OAAO,GAAG,CAAC,GAAG,CAAC;AACjB,CAAC;AAED,SAAgB,uBAAuB,CAAC,aAAqB;IAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACvC,IAAI,CAAC,IAAA,uBAAe,EAAC,IAAI,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAE/E,QAAS,IAAY,CAAC,IAAI,EAAE;QAC1B,KAAK,mBAAmB;YACtB,OAAO,yBAAyB,CAAC,IAAI,CAAC,CAAC;QACzC;YACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;KACrD;AACH,CAAC;AAVD,0DAUC;AAwBD,MAAM,cAAc,GAA6B;IAC/C,aAAa,EAAE,EAAE;IACjB,OAAO,EAAE,CAAC,IAAA,yBAAiB,EAAC,CAAC,CAAC,CAAC;IAC/B,MAAM,EAAE,QAAQ;CACjB,CAAC;AAEF,MAAa,iBAAiB;IAC5B;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,YAAY,CAC9B,QAAgB,EAChB,UAA6C,EAAE;QAE/C,MAAM,eAAe,GAAG,IAAI,wBAAe,CAAC,QAAQ,CAAC,CAAC;QACtD,MAAM,IAAI,GAAG,MAAM,cAAK,CAAC,cAAc,CAAC,eAAe,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAChF,OAAO,IAAI,iBAAiB,CAAC,eAAe,EAAE;YAC5C,GAAG,OAAO;YACV,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAC1B,SAAiC,EAAE,EACnC,UAA6C,EAAE;QAE/C,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;QACzD,MAAM,OAAO,GAAG,eAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC/C,MAAM,QAAQ,GAAG,cAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,OAAO,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,aAAqB,EAAE,QAAgB;QACrE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACvC,IAAI,CAAC,IAAA,uBAAe,EAAC,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAC/E,QAAS,IAAY,CAAC,IAAI,EAAE;YAC1B,KAAK,mBAAmB;gBACtB,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YACtE;gBACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACrD;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAC9C,aAAqB,EACrB,aAAyB;QAEzB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACvC,IAAI,CAAC,IAAA,uBAAe,EAAC,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAC/E,MAAM,WAAW,GAAQ,IAAI,CAAC;QAC9B,QAAQ,WAAW,CAAC,IAAI,EAAE;YACxB,KAAK,mBAAmB,CAAC,CAAC;gBACxB,MAAM,cAAc,GAAG,MAAM,IAAA,gBAAO,EAClC,IAAA,qBAAU,EAAC,WAAW,CAAC,IAAI,CAAC,EAC5B,aAAa,EACb,WAAW,CAAC,UAAU,CACvB,CAAC;gBACF,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,mBAAQ,EAAC,cAAc,CAAC,CAAC,CAAC;gBAC/D,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,iBAAiB,CAAC;gBACjD,IAAA,cAAM,EAAC,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;gBACrF,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE;oBAC3D,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;iBAC1D;gBACD,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBACvC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE;oBAC3D,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;iBAC7D;gBACD,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,IAAA,qBAAY,EAAC,MAAM,CAAC,CAAC,CAAC;gBACnE,OAAO,iBAAiB,CAAC,YAAY,CAAC,QAAQ,EAAE;oBAC9C,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,WAAW;iBACpB,CAAC,CAAC;aACJ;YACD;gBACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACrD;IACH,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,iBAAiB,CACpC,aAAqB,EACrB,QAAgB;QAEhB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACvC,IAAI,CAAC,IAAA,uBAAe,EAAC,IAAI,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAC/E,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAU,EAAC,QAAQ,EAAG,IAAY,CAAC,GAAG,CAAC,CAAC;QACpE,OAAO,iBAAiB,CAAC,4BAA4B,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IACtF,CAAC;IASD,YAAsB,QAAyB,EAAE,OAA4C;QAC3F,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC;QAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,CAAC;QACvD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACvC,MAAM,EAAE,MAAM;YACd,MAAM;SACP,CAAC,CAAC,CAAC;IACN,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,WAAW;QACtB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAClE,OAAO,oBAAoB,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,aAAqB,EAAE,OAAmB;QAC/D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACtD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,KAAK,aAAa,CAAC,CAAC;QAC1E,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,WAAW,aAAa,sBAAsB,CAAC,CAAC;SACjE;QACD,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QACpC,MAAM,OAAO,GAAG,IAAA,eAAM,EAAC,IAAA,0BAAgB,EAAC,OAAO,CAAC,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,MAAM,kBAAS,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACpE,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChF,OAAO;YACL,MAAM,EAAE,OAAO;YACf,SAAS,EAAE,IAAA,oCAAwB,EAAC,MAAM,EAAE,cAAc,CAAC;SAC5D,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,SAAS,CAAC,QAAgB;QACrC,MAAM,gBAAgB,GAAG,2BAA2B,CAAC;QACrD,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAU,EAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,0BAA0B,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,0BAA0B,CACrC,aAAyB,EACzB,gBAAkC;QAElC,MAAM,aAAa,GAA0B;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;gBACnD,MAAM,EAAE,IAAA,qBAAY,EAAC,MAAM,CAAC;gBAC5B,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;SACJ,CAAC;QACF,MAAM,gBAAgB,GAAG,IAAA,iBAAM,EAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;QAE/D,MAAM,uBAAuB,GAA4B;YACvD,SAAS,EAAE,4BAAmB,CAAC,qBAAqB;SACrD,CAAC;QACF,MAAM,aAAa,GAAG,MAAM,IAAA,gBAAO,EAAC,gBAAgB,EAAE,aAAa,EAAE,uBAAuB,CAAC,CAAC;QAE9F,MAAM,GAAG,GAAmC;YAC1C,IAAI,EAAE,mBAAmB;YACzB,GAAG,EAAE,gBAAgB;YACrB,UAAU,EAAE,uBAAuB;YACnC,IAAI,EAAE,IAAA,mBAAQ,EAAC,aAAa,CAAC;SAC9B,CAAC;QACF,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,MAAc;QACrC,MAAM,EAAE,OAAO,EAAE,GAAG,eAAM,CAAC,UAAU,CAAC,oBAAW,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAChF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,kBAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACxD,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,kBAAS,CAAC,cAAc,CAAC,MAAM,CAAC;SACzC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,OAAO,OAAO,CAAC,GAAG,CAChB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE;YAC7C,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC1D,MAAM,OAAO,GAAG,IAAA,mBAAQ,EAAC,MAAM,EAAE,IAAA,0CAA8B,EAAC,MAAM,CAAC,CAAC,CAAC;YACzE,OAAO;gBACL,IAAI,EAAE,WAAoB;gBAC1B,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,OAAO;aACjB,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF;AAjOD,8CAiOC"}