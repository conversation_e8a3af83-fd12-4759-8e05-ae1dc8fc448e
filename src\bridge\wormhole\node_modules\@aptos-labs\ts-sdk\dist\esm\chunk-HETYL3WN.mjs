import{e as r}from"./chunk-6WDVDEQZ.mjs";import{a as d}from"./chunk-VEGW6HP5.mjs";async function A(s,u){let{url:t,method:n,body:i,contentType:a,params:o,overrides:e,originMethod:R}=s,p={...e?.HEADERS,"x-aptos-client":`aptos-typescript-sdk/${d}`,"content-type":a??"application/json","x-aptos-typescript-sdk-origin-method":R};return e?.AUTH_TOKEN&&(p.Authorization=`Bearer ${e?.AUTH_TOKEN}`),e?.API_KEY&&(p.Authorization=`Bearer ${e?.API_KEY}`),u.provider({url:t,method:n,body:i,params:o,headers:p,overrides:e})}async function P(s,u,t){let{url:n,path:i}=s,a=i?`${n}/${i}`:n,o=await A({...s,url:a},u.client),e={status:o.status,statusText:o.statusText??"No status text provided",data:o.data,headers:o.headers,config:o.config,request:o.request,url:a};if(e.status===401)throw new r({apiType:t,aptosRequest:s,aptosResponse:e});if(t==="Indexer"){let R=e.data;if(R.errors)throw new r({apiType:t,aptosRequest:s,aptosResponse:e});e.data=R.data}else if((t==="Pepper"||t==="Prover")&&e.status>=400)throw new r({apiType:t,aptosRequest:s,aptosResponse:e});if(e.status>=200&&e.status<300)return e;throw new r({apiType:t,aptosRequest:s,aptosResponse:e})}export{A as a,P as b};
//# sourceMappingURL=chunk-HETYL3WN.mjs.map