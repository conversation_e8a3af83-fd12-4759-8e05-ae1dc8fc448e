import _m0 from "protobufjs/minimal.js";
import { Block } from "../../types/v1beta1/block";
export declare const protobufPackage = "cometbft.blocksync.v1beta1";
/** BlockRequest requests a block for a specific height */
export interface BlockRequest {
    height: string;
}
/** NoBlockResponse informs the node that the peer does not have block at the requested height */
export interface NoBlockResponse {
    height: string;
}
/** BlockResponse returns block to the requested */
export interface BlockResponse {
    block: Block | undefined;
}
/** StatusRequest requests the status of a peer. */
export interface StatusRequest {
}
/** StatusResponse is a peer response to inform their status. */
export interface StatusResponse {
    height: string;
    base: string;
}
/** Message is an abstract blocksync message. */
export interface Message {
    blockRequest?: BlockRequest | undefined;
    noBlockResponse?: NoBlockResponse | undefined;
    blockResponse?: BlockResponse | undefined;
    statusRequest?: StatusRequest | undefined;
    statusResponse?: StatusResponse | undefined;
}
export declare const BlockRequest: {
    encode(message: BlockRe<PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BlockRequest;
    fromJSON(object: any): BlockRequest;
    toJSON(message: BlockRequest): unknown;
    create(base?: DeepPartial<BlockRequest>): BlockRequest;
    fromPartial(object: DeepPartial<BlockRequest>): BlockRequest;
};
export declare const NoBlockResponse: {
    encode(message: NoBlockResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): NoBlockResponse;
    fromJSON(object: any): NoBlockResponse;
    toJSON(message: NoBlockResponse): unknown;
    create(base?: DeepPartial<NoBlockResponse>): NoBlockResponse;
    fromPartial(object: DeepPartial<NoBlockResponse>): NoBlockResponse;
};
export declare const BlockResponse: {
    encode(message: BlockResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BlockResponse;
    fromJSON(object: any): BlockResponse;
    toJSON(message: BlockResponse): unknown;
    create(base?: DeepPartial<BlockResponse>): BlockResponse;
    fromPartial(object: DeepPartial<BlockResponse>): BlockResponse;
};
export declare const StatusRequest: {
    encode(_: StatusRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StatusRequest;
    fromJSON(_: any): StatusRequest;
    toJSON(_: StatusRequest): unknown;
    create(base?: DeepPartial<StatusRequest>): StatusRequest;
    fromPartial(_: DeepPartial<StatusRequest>): StatusRequest;
};
export declare const StatusResponse: {
    encode(message: StatusResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StatusResponse;
    fromJSON(object: any): StatusResponse;
    toJSON(message: StatusResponse): unknown;
    create(base?: DeepPartial<StatusResponse>): StatusResponse;
    fromPartial(object: DeepPartial<StatusResponse>): StatusResponse;
};
export declare const Message: {
    encode(message: Message, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Message;
    fromJSON(object: any): Message;
    toJSON(message: Message): unknown;
    create(base?: DeepPartial<Message>): Message;
    fromPartial(object: DeepPartial<Message>): Message;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
