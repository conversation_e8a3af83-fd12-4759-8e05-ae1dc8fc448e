import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cometbft.store.v1";
/** BlockStoreState represents the state of the block store. */
export interface BlockStoreState {
    base: string;
    height: string;
}
export declare const BlockStoreState: {
    encode(message: BlockStoreState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BlockStoreState;
    fromJSON(object: any): BlockStoreState;
    toJSON(message: BlockStoreState): unknown;
    create(base?: DeepPartial<BlockStoreState>): BlockStoreState;
    fromPartial(object: DeepPartial<BlockStoreState>): BlockStoreState;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
