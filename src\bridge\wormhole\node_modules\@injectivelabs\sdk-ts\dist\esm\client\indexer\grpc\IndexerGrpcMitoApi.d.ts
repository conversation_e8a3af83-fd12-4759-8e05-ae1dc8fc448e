import { Mito<PERSON><PERSON> } from '@injectivelabs/mito-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcMitoApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: MitoApi.MitoAPIClientImpl;
    constructor(endpoint: string);
    fetchVault({ contractAddress, slug, }: {
        contractAddress?: string;
        slug?: string;
    }): Promise<import("../types/mito.js").MitoVault>;
    fetchVaults({ limit, codeId, pageIndex, }: {
        limit?: number;
        codeId?: string;
        pageIndex?: number;
    }): Promise<{
        vaults: import("../types/mito.js").MitoVault[];
        pagination?: import("../types/mito.js").MitoPagination;
    }>;
    fetchLpTokenPriceChart({ to, from, vaultAddress, }: {
        to?: string;
        from?: string;
        vaultAddress: string;
    }): Promise<import("../types/mito.js").MitoPriceSnapshot[]>;
    fetchTVLChartRequest({ to, from, vaultAddress, }: {
        to?: string;
        from?: string;
        vaultAddress: string;
    }): Promise<import("../types/mito.js").MitoPriceSnapshot[]>;
    fetchVaultsByHolderAddress({ skip, limit, holderAddress, vaultAddress, }: {
        skip?: number;
        limit?: number;
        holderAddress: string;
        vaultAddress?: string;
    }): Promise<{
        subscriptions: import("../types/mito.js").MitoSubscription[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchLPHolders({ skip, limit, vaultAddress, stakingContractAddress, }: {
        skip?: number;
        limit?: number;
        vaultAddress: string;
        stakingContractAddress: string;
    }): Promise<{
        holders: import("../types/mito.js").MitoHolders[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchHolderPortfolio({ holderAddress, stakingContractAddress, }: {
        holderAddress: string;
        stakingContractAddress: string;
    }): Promise<import("../types/mito.js").MitoPortfolio>;
    fetchLeaderboard(epochId?: number): Promise<import("../types/mito.js").MitoLeaderboard>;
    fetchTransferHistory({ vault, account, limit, toNumber, fromNumber, }: {
        vault?: string;
        account?: string;
        limit?: number;
        toNumber?: number;
        fromNumber?: number;
    }): Promise<{
        transfers: import("../types/mito.js").MitoTransfer[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchLeaderboardEpochs({ limit, toEpochId, fromEpochId, }: {
        limit?: number;
        toEpochId?: number;
        fromEpochId?: number;
    }): Promise<{
        epochs: import("../types/mito.js").MitoLeaderboardEpoch[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchStakingPools({ staker, stakingContractAddress, }: {
        staker?: string;
        stakingContractAddress: string;
    }): Promise<{
        pools: import("../types/mito.js").MitoStakingPool[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchStakingHistory({ staker, toNumber, limit, fromNumber, }?: {
        staker?: string;
        limit?: number;
        toNumber?: number;
        fromNumber?: number;
    }): Promise<{
        activities: {
            action: string;
            txHash: string;
            staker: string;
            vaultAddress: string;
            numberByAccount: number;
            timestamp: number;
            rewardedTokens: import("@injectivelabs/ts-types").Coin[];
            stakeAmount: import("@injectivelabs/ts-types").Coin | undefined;
        }[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchStakingRewardsByAccount({ staker, stakingContractAddress, }: {
        staker: string;
        stakingContractAddress: string;
    }): Promise<{
        rewards: {
            apr: number;
            vaultName: string;
            vaultAddress: string;
            lockTimestamp: number;
            claimableRewards: import("@injectivelabs/ts-types").Coin[];
            stakedAmount: import("@injectivelabs/ts-types").Coin | undefined;
            lockedAmount: import("@injectivelabs/ts-types").Coin | undefined;
        }[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchMissions({ accountAddress }: {
        accountAddress: string;
    }): Promise<import("../types/mito.js").MitoMission[]>;
    fetchMissionLeaderboard(userAddress?: string): Promise<import("../types/mito.js").MitoMissionLeaderboard>;
    fetchIDO({ contractAddress, accountAddress, }: {
        contractAddress: string;
        accountAddress?: string;
    }): Promise<{
        ido: import("../types/mito.js").MitoIDO | undefined;
    }>;
    fetchIDOs({ status, limit, toNumber, accountAddress, ownerAddress, }?: {
        status?: string;
        limit?: number;
        toNumber?: number;
        accountAddress?: string;
        ownerAddress?: string;
    }): Promise<{
        idos: import("../types/mito.js").MitoIDO[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchIDOSubscribers({ skip, limit, sortBy, contractAddress, }: {
        skip?: number;
        limit?: number;
        sortBy?: string;
        contractAddress: string;
    }): Promise<{
        marketId: string;
        quoteDenom: string;
        subscribers: import("../types/mito.js").MitoIDOSubscriber[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
        tokenInfo: import("../types/mito.js").MitoTokenInfo | undefined;
    }>;
    fetchIDOSubscription({ contractAddress, accountAddress, }: {
        contractAddress: string;
        accountAddress: string;
    }): Promise<{
        subscription: import("../types/mito.js").MitoIDOSubscription | undefined;
    }>;
    fetchIDOActivities({ contractAddress, accountAddress, limit, toNumber, }?: {
        contractAddress?: string;
        accountAddress?: string;
        limit?: number;
        toNumber?: string;
    }): Promise<{
        activities: import("../types/mito.js").MitoIDOSubscriptionActivity[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchIDOWhitelist({ skip, limit, idoAddress, }: {
        skip?: number;
        limit?: number;
        idoAddress: string;
    }): Promise<{
        idoAddress: string | undefined;
        accounts: import("../types/mito.js").MitoWhitelistAccount[];
        pagination: import("../types/mito.js").MitoPagination | undefined;
    }>;
    fetchClaimReferences({ skip, limit, idoAddress, accountAddress, }: {
        skip?: number;
        limit?: number;
        idoAddress: string;
        accountAddress: string;
    }): Promise<{
        claimReferences: import("../types/mito.js").MitoClaimReference[];
        pagination?: import("../types/mito.js").MitoPagination;
    }>;
}
