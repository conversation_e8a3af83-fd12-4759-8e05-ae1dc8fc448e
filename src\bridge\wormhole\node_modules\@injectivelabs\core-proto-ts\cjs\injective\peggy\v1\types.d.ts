import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective.peggy.v1";
/** BridgeValidator represents a validator's ETH address and its power */
export interface BridgeValidator {
    power: string;
    ethereumAddress: string;
}
/**
 * Valset is the Ethereum Bridge Multsig Set, each peggy validator also
 * maintains an ETH key to sign messages, these are used to check signatures on
 * ETH because of the significant gas savings
 */
export interface Valset {
    nonce: string;
    members: BridgeValidator[];
    height: string;
    rewardAmount: string;
    /** the reward token in it's Ethereum hex address representation */
    rewardToken: string;
}
/**
 * LastObservedEthereumBlockHeight stores the last observed
 * Ethereum block height along with the Cosmos block height that
 * it was observed at. These two numbers can be used to project
 * outward and always produce batches with timeouts in the future
 * even if no Ethereum block height has been relayed for a long time
 */
export interface LastObservedEthereumBlockHeight {
    cosmosBlockHeight: string;
    ethereumBlockHeight: string;
}
/** LastClaimEvent stores last claim event details of validator. */
export interface LastClaimEvent {
    ethereumEventNonce: string;
    ethereumEventHeight: string;
}
/**
 * This records the relationship between an ERC20 token and the denom
 * of the corresponding Cosmos originated asset
 */
export interface ERC20ToDenom {
    erc20: string;
    denom: string;
}
export declare const BridgeValidator: {
    encode(message: BridgeValidator, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BridgeValidator;
    fromJSON(object: any): BridgeValidator;
    toJSON(message: BridgeValidator): unknown;
    create(base?: DeepPartial<BridgeValidator>): BridgeValidator;
    fromPartial(object: DeepPartial<BridgeValidator>): BridgeValidator;
};
export declare const Valset: {
    encode(message: Valset, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Valset;
    fromJSON(object: any): Valset;
    toJSON(message: Valset): unknown;
    create(base?: DeepPartial<Valset>): Valset;
    fromPartial(object: DeepPartial<Valset>): Valset;
};
export declare const LastObservedEthereumBlockHeight: {
    encode(message: LastObservedEthereumBlockHeight, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LastObservedEthereumBlockHeight;
    fromJSON(object: any): LastObservedEthereumBlockHeight;
    toJSON(message: LastObservedEthereumBlockHeight): unknown;
    create(base?: DeepPartial<LastObservedEthereumBlockHeight>): LastObservedEthereumBlockHeight;
    fromPartial(object: DeepPartial<LastObservedEthereumBlockHeight>): LastObservedEthereumBlockHeight;
};
export declare const LastClaimEvent: {
    encode(message: LastClaimEvent, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LastClaimEvent;
    fromJSON(object: any): LastClaimEvent;
    toJSON(message: LastClaimEvent): unknown;
    create(base?: DeepPartial<LastClaimEvent>): LastClaimEvent;
    fromPartial(object: DeepPartial<LastClaimEvent>): LastClaimEvent;
};
export declare const ERC20ToDenom: {
    encode(message: ERC20ToDenom, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ERC20ToDenom;
    fromJSON(object: any): ERC20ToDenom;
    toJSON(message: ERC20ToDenom): unknown;
    create(base?: DeepPartial<ERC20ToDenom>): ERC20ToDenom;
    fromPartial(object: DeepPartial<ERC20ToDenom>): ERC20ToDenom;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
