import { MsgBase } from '../../MsgBase.js';
import { SnakeCaseKeys } from 'snakecase-keys';
import { CosmosStakingV1Beta1Tx, CosmosStakingV1Beta1Staking } from '@injectivelabs/core-proto-ts';
export declare namespace MsgEditValidator {
    interface Params {
        description: {
            moniker: string;
            identity: string;
            website: string;
            securityContact?: string;
            details: string;
        };
        validatorAddress: string;
        commissionRate?: string;
        minSelfDelegation?: string;
    }
    type Proto = CosmosStakingV1Beta1Tx.MsgEditValidator;
}
/**
 * @category Messages
 */
export default class MsgEditValidator extends MsgBase<MsgEditValidator.Params, MsgEditValidator.Proto> {
    static fromJSON(params: MsgEditValidator.Params): MsgEditValidator;
    toProto(): CosmosStakingV1Beta1Tx.MsgEditValidator;
    toData(): {
        description: CosmosStakingV1Beta1Staking.Description | undefined;
        validatorAddress: string;
        commissionRate: string;
        minSelfDelegation: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<CosmosStakingV1Beta1Tx.MsgEditValidator>;
    };
    toWeb3Gw(): {
        description: {
            moniker: string;
            identity: string;
            website: string;
            security_contact: string;
            details: string;
        } | undefined;
        validator_address: string;
        commission_rate: string;
        min_self_delegation: string;
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: {
            commission_rate: string;
            description: {
                moniker: string;
                identity: string;
                website: string;
                security_contact: string;
                details: string;
            } | undefined;
            validator_address: string;
            min_self_delegation: string;
        };
    };
    toEip712V2(): {
        commission_rate: string;
        description: {
            moniker: string;
            identity: string;
            website: string;
            security_contact: string;
            details: string;
        } | undefined;
        validator_address: string;
        min_self_delegation: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: CosmosStakingV1Beta1Tx.MsgEditValidator;
    };
    toBinary(): Uint8Array;
}
