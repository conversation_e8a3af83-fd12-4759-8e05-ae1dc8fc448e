#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
import requests
import yaml
from pathlib import Path
from web3 import Web3

def load_config():
    """
    加载配置文件
    
    Returns:
        配置字典
    """
    config_path = os.path.join("config", "config.yaml")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return {}

def get_web3_provider(network):
    """
    获取Web3提供者
    
    Args:
        network: 网络名称 (ETH, BSC, MATIC, BASEEVM)
        
    Returns:
        Web3实例或None
    """
    config = load_config()
    
    network_rpc_mapping = {
        "ETH": config.get("dex", {}).get("ethereum", {}).get("rpc_url", "https://ethereum-rpc.publicnode.com"),
        "BSC": "https://bsc-dataseed.binance.org",
        "MATIC": config.get("dex", {}).get("polygon", {}).get("rpc_url", "https://polygon-rpc.com"),
        "BASEEVM": "https://mainnet.base.org"
    }
    
    if network not in network_rpc_mapping:
        print(f"不支持的网络: {network}")
        return None
    
    rpc_url = network_rpc_mapping[network]
    print(f"使用RPC: {rpc_url}")
    
    try:
        web3 = Web3(Web3.HTTPProvider(rpc_url))
        if web3.is_connected():
            print(f"已连接到{network}网络")
            return web3
        else:
            print(f"无法连接到{network}网络")
            return None
    except Exception as e:
        print(f"创建Web3实例时出错: {str(e)}")
        return None

def get_token_decimals_from_contract(web3, token_address):
    """
    直接从合约获取代币精度
    
    Args:
        web3: Web3实例
        token_address: 代币合约地址
        
    Returns:
        代币精度或None
    """
    if not web3:
        return None
    
    # ERC20代币decimals函数的ABI
    abi = [
        {
            "constant": True,
            "inputs": [],
            "name": "decimals",
            "outputs": [{"name": "", "type": "uint8"}],
            "type": "function"
        }
    ]
    
    try:
        # 检查地址格式
        if not Web3.is_address(token_address):
            print(f"无效的合约地址: {token_address}")
            return None
        
        # 创建合约实例
        checksum_address = web3.to_checksum_address(token_address)
        contract = web3.eth.contract(address=checksum_address, abi=abi)
        
        # 调用decimals函数
        decimals = contract.functions.decimals().call()
        print(f"从合约获取到代币精度: {decimals}")
        return decimals
    except Exception as e:
        print(f"从合约获取代币精度时出错: {str(e)}")
        return None

def get_token_decimals_from_explorer(network, token_address):
    """
    从区块浏览器API获取代币精度
    
    Args:
        network: 网络名称 (ETH, BSC, MATIC, BASEEVM)
        token_address: 代币合约地址
        
    Returns:
        代币精度或None
    """
    # 加载配置文件获取API密钥
    config = load_config()
    api_keys = config.get("api_keys", {})
    
    # 不同网络的API配置
    explorer_apis = {
        "ETH": {
            "url": "https://api.etherscan.io/api",
            "key": api_keys.get("etherscan", {}).get("key", "")
        },
        "BSC": {
            "url": "https://api.bscscan.com/api",
            "key": api_keys.get("bscscan", {}).get("key", "")
        },
        "MATIC": {
            "url": "https://api.polygonscan.com/api",
            "key": api_keys.get("polygonscan", {}).get("key", "")
        },
        "BASEEVM": {
            "url": "https://api.basescan.org/api",
            "key": api_keys.get("basescan", {}).get("key", "")
        }
    }
    
    if network not in explorer_apis:
        return None
    
    api_config = explorer_apis[network]
    
    # 检查API密钥是否配置
    if not api_config["key"]:
        print(f"警告: 未配置{network}网络的区块浏览器API密钥")
        return None
    
    # 尝试不同的API方法获取代币精度
    methods = [
        {
            "module": "token",
            "action": "tokeninfo",
            "address_param": "contractaddress"
        },
        {
            "module": "contract",
            "action": "getabi",
            "address_param": "address"
        }
    ]
    
    for method in methods:
        try:
            # 构建API请求
            params = {
                "module": method["module"],
                "action": method["action"],
                method["address_param"]: token_address,
                "apikey": api_config["key"]
            }
            
            print(f"尝试使用{method['module']}.{method['action']}获取代币精度...")
            response = requests.get(api_config["url"], params=params, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                
                if method["action"] == "tokeninfo" and result.get("status") == "1" and isinstance(result.get("result"), list):
                    for token_info in result["result"]:
                        if "decimals" in token_info:
                            try:
                                return int(token_info["decimals"])
                            except (ValueError, TypeError):
                                pass
                
                elif method["action"] == "getabi" and result.get("status") == "1" and "result" in result:
                    try:
                        contract_abi = json.loads(result["result"])
                        for item in contract_abi:
                            if item.get("name") == "decimals" and item.get("type") == "function":
                                print("在ABI中找到decimals函数，尝试从RPC获取精度...")
                                # 我们找到了decimals函数，尝试使用Web3调用
                                web3 = get_web3_provider(network)
                                if web3:
                                    return get_token_decimals_from_contract(web3, token_address)
                    except json.JSONDecodeError:
                        pass
                        
            # 检查是否需要等待以避免API限制
            if "rate limit" in response.text.lower() or response.status_code == 429:
                print("触发API速率限制，等待5秒...")
                time.sleep(5)
                
        except Exception as e:
            print(f"使用{method['action']}方法获取代币精度时出错: {str(e)}")
    
    # 尝试从数据库获取常见代币精度
    return get_common_token_decimals(network, token_address)

def get_token_decimals_from_cmc(token_symbol):
    """
    尝试从CoinMarketCap API获取代币精度
    
    Args:
        token_symbol: 代币符号
        
    Returns:
        代币精度或None
    """
    # 使用公共API端点，没有密钥限制
    url = f"https://web-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"
    
    params = {
        "symbol": token_symbol,
        "convert": "USD"
    }
    
    headers = {
        "Accepts": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if "data" in data and token_symbol in data["data"]:
                token_data = data["data"][token_symbol]
                if "platform" in token_data and token_data["platform"]:
                    if "token_address" in token_data["platform"]:
                        # 有些API响应包含代币精度信息
                        if "decimals" in token_data["platform"]:
                            return int(token_data["platform"]["decimals"])
            
        return None
    except Exception as e:
        print(f"从CoinMarketCap获取代币精度时出错: {str(e)}")
        return None

def get_common_token_decimals(network, token_address):
    """
    获取常见代币的精度信息
    
    Args:
        network: 网络名称
        token_address: 代币地址
        
    Returns:
        代币精度或None
    """
    # 检查常见稳定币地址
    network_stablecoins = {
        "ETH": {
            "******************************************": 6,  # USDT
            "******************************************": 6,  # USDC
            "******************************************": 18  # DAI
        },
        "BSC": {
            "******************************************": 18,  # USDT
            "******************************************": 18,  # USDC
            "******************************************": 18   # BUSD
        },
        "MATIC": {
            "******************************************": 6,   # USDT
            "******************************************": 6,   # USDC
            "******************************************": 18   # DAI
        },
        "BASEEVM": {
            "******************************************": 6,   # USDT
            "******************************************": 6    # USDC
        }
    }
    
    # 检查特定网络的已知代币地址
    if network in network_stablecoins:
        token_address_lower = token_address.lower()
        if token_address_lower in network_stablecoins[network]:
            decimals = network_stablecoins[network][token_address_lower]
            print(f"使用已知地址的代币精度: {decimals}")
            return decimals
    
    return None

def main():
    """
    更新decimals为null的代币精度信息
    """
    # 输入文件路径
    input_path = "data/utils/token/gate_tokens_with_decimals.json"
    
    print(f"正在从 {input_path} 读取代币信息...")
    
    # 检查文件是否存在
    if not os.path.exists(input_path):
        print(f"错误: 找不到文件 {input_path}")
        return
    
    # 读取源数据
    with open(input_path, "r", encoding="utf-8") as f:
        tokens_data = json.load(f)
    
    # 统计数据
    total_tokens = 0
    missing_decimals = 0
    updated_tokens = 0
    
    # 处理每个网络的代币
    for network, tokens in tokens_data.items():
        print(f"\n处理 {network} 网络...")
        
        total_network_tokens = len(tokens)
        total_tokens += total_network_tokens
        
        network_missing = 0
        network_updated = 0
        
        # 为该网络创建Web3实例
        web3 = get_web3_provider(network)
        if not web3:
            print(f"无法为网络 {network} 创建Web3实例，跳过该网络")
            continue
            
        for i, token in enumerate(tokens):
            if token.get("decimals") is None:
                # 计数
                missing_decimals += 1
                network_missing += 1
                
                symbol = token.get("symbol", "未知")
                address = token.get("contract_address", "")
                
                print(f"\n[{i+1}/{total_network_tokens}] 尝试更新代币 {symbol} ({address}) 的精度信息...")
                
                # 首先尝试从已知代币列表获取
                decimals = get_common_token_decimals(network, address)
                
                # 如果未找到，直接从合约获取
                if decimals is None:
                    decimals = get_token_decimals_from_contract(web3, address)
                
                # 更新代币精度
                if decimals is not None:
                    print(f"✅ 成功获取到代币 {symbol} 的精度: {decimals}")
                    token["decimals"] = decimals
                    updated_tokens += 1
                    network_updated += 1
                else:
                    print(f"❌ 无法获取代币 {symbol} 的精度，保留null值")
                
                # 添加延迟，避免API限制
                if i < total_network_tokens - 1:
                    time.sleep(0.2)  # 降低延迟，因为直接调用合约不太可能有速率限制
        
        print(f"\n{network} 网络处理完成:")
        print(f"  - 总代币数: {total_network_tokens}")
        print(f"  - 缺失精度: {network_missing}")
        print(f"  - 更新成功: {network_updated}")
    
    # 直接保存回原文件
    if updated_tokens > 0:
        with open(input_path, "w", encoding="utf-8") as f:
            json.dump(tokens_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n全部处理完成!")
        print(f"总代币数: {total_tokens}")
        print(f"缺失精度: {missing_decimals}")
        print(f"更新成功: {updated_tokens}")
        print(f"已直接更新原文件: {input_path}")
    else:
        print("\n没有成功获取任何代币精度，原文件未修改")

if __name__ == "__main__":
    main() 