import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import snakecaseKeys from 'snakecase-keys';
import { getEthereumAddress, toUtf8 } from '../../../../utils/index.js';
/**
 * @category Messages
 */
export default class MsgSignData extends MsgBase {
    static fromJSON(params) {
        return new MsgSignData(params);
    }
    toProto() {
        const { params } = this;
        const message = InjectiveExchangeV1Beta1Tx.MsgSignData.create();
        message.Signer = Buffer.from(getEthereumAddress(params.sender), 'hex');
        message.Data = Buffer.from(toUtf8(params.data), 'utf-8');
        return InjectiveExchangeV1Beta1Tx.MsgSignData.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgSignData',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...snakecaseKeys(proto),
        };
        return {
            type: 'sign/MsgSignData',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgSignData',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgSignData',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgSignData.encode(this.toProto()).finish();
    }
}
