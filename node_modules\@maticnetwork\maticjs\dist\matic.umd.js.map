{"version": 3, "file": "matic.umd.js", "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;;ACVA;AACA,aAAa,mBAAO,CAAC,sBAAQ;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA,EAAE,cAAc;AAChB;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AC7DyC;AAEzC;IAAA;IAiBA,CAAC;IAhBU,kBAAI,GAAX,UAAY,KAAK;QACb,OAAO,sDAAmB,EAAW,CAAC;IAC1C,CAAC;IAcL,oBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;AChBD;IAEI,sBAAmB,OAAe,EAAS,MAAa;QAArC,YAAO,GAAP,OAAO,CAAQ;QAAS,WAAM,GAAN,MAAM,CAAO;IAExD,CAAC;IAGL,mBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;ACND;IAGI,wBAAmB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAEjC,CAAC;IAwBD,oCAAW,GAAX,UAAa,UAAkB,EAAE,QAAgB;QAC7C,OAAO,IAAI,CAAC,cAAc,CAAC;YACvB,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC9C,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE;SAC3B,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,6CAAoB,GAApB;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;YACvB,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,EAAE;YACV,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE;SAC3B,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,OAAO,CAAC,MAAM,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAQL,qBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;AC1DD;IACI,4BAAmB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAEjC,CAAC;IAML,yBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;ACZiC;AACC;AACH;AACE;;;;;;;;;;;;;;;;ACH3B,IAAM,MAAM,GAAG;IACpB,WAAW,EAAE,4CAA4C;IACzD,kBAAkB,EAAE,6CAA6C;CAClE;;;;;;;;;;;;;;;;;;;;;;;ACHM,IAAM,UAAU,GAAG,oEAAoE,CAAC;AACxF,IAAM,YAAY,GAAG,4CAA4C,CAAC;AAClE,IAAM,mBAAmB,GAAG,oEAAoE,CAAC;AACjG,IAAM,wBAAwB,GAAG,oEAAoE,CAAC;AACtG,IAAM,wBAAwB,GAAG,oEAAoE,CAAC;AACtG,IAAM,uBAAuB,GAAG,oEAAoE,CAAC;AACrG,IAAM,0BAA0B,GAAG,MAAM,CAAC,UAAC,EAAI,EAAE,EAAC,CAAC;AAC1D,IAAY,MAIX;AAJD,WAAY,MAAM;IACd,qBAAW;IACX,+BAAqB;IACrB,6BAAmB;AACvB,CAAC,EAJW,MAAM,KAAN,MAAM,QAIjB;;;;;;;;;;;;;;;;;;ACXiC;AACG;AAE9B,IAAM,aAAa,GAAG;IACzB,KAAK,EAAE,yCAAK;IACZ,GAAG;IACH,SAAS;CACZ,CAAC;;;;;;;;;;;;;;;;ACPF,IAAY,UAYX;AAZD,WAAY,UAAU;IAClB,+CAAiC;IACjC,iDAAmC;IACnC,iCAAmB;IACnB,kDAAoC;IACpC,yEAA2D;IAC3D,gEAAkD;IAClD,4DAA8C;IAC9C,yDAA2C;IAC3C,sEAAwD;IACxD,qDAAuC;IACvC,yEAA2D;AAC/D,CAAC,EAZW,UAAU,KAAV,UAAU,QAYrB;;;;;;;;;;;;;;;;;;;ACZqC;AACT;;;;;;;;;;;;;;;;ACD7B,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC3B,sGAAsG;IACtG,uGAAuG;IACvG,2GAAoF;IACpF,4GAAqF;IACrF,6GAAsF;IACtF,iHAA0F;IAC1F,kHAA2F;IAC3F,wHAAiG;AACrG,CAAC,EATW,mBAAmB,KAAnB,mBAAmB,QAS9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTwC;AACI;AAE7C;IAAoC,kCAAa;IAE7C,wBAAY,KAAK;eACb,iBAAO;IACX,CAAC;IAED,iCAAQ,GAAR,UAAS,IAAK;QACV,OAAO,sDAAmB,EAAU,CAAC;IACzC,CAAC;IAED,iCAAQ,GAAR;QACI,OAAO,sDAAmB,EAAU,CAAC;IACzC,CAAC;IAED,4BAAG,GAAH,UAAI,KAAoB;QACpB,OAAO,sDAAmB,EAAiB,CAAC;IAChD,CAAC;IAED,4BAAG,GAAH,UAAI,KAAoB;QACpB,OAAO,sDAAmB,EAAiB,CAAC;IAChD,CAAC;IAED,4BAAG,GAAH,UAAI,KAAoB;QACpB,OAAO,sDAAmB,EAAiB,CAAC;IAChD,CAAC;IAED,4BAAG,GAAH,UAAI,KAAoB;QACpB,OAAO,sDAAmB,EAAiB,CAAC;IAChD,CAAC;IAED,4BAAG,GAAH,UAAI,KAAoB;QACpB,OAAO,sDAAmB,EAAW,CAAC;IAC1C,CAAC;IAED,2BAAE,GAAF,UAAG,KAAoB;QACnB,OAAO,sDAAmB,EAAW,CAAC;IAE1C,CAAC;IAED,4BAAG,GAAH,UAAI,KAAoB;QACpB,OAAO,sDAAmB,EAAW,CAAC;IAE1C,CAAC;IAED,2BAAE,GAAF,UAAG,KAAoB;QACnB,OAAO,sDAAmB,EAAW,CAAC;IAE1C,CAAC;IAED,2BAAE,GAAF,UAAG,KAAoB;QACnB,OAAO,sDAAmB,EAAW,CAAC;IAC1C,CAAC;IACL,qBAAC;AAAD,CAAC,CApDmC,qDAAa,GAoDhD;;;;;;;;;;;;;;;;;;ACvDoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAqB;AAElB;AACA;AACF;AACO;AACL;AACG;AACC;AACD;AACH;AAExB,iEAAe,mDAAa,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AUZJ;AACA;AACY;AACM;AACN;AACA;AACC;AACT;AACO;AACD;AACX;AACiB;AACH;AACC;AACF;AACA;AACb;AACQ;AACE;AACa;AACF;AACH;AACJ;AACJ;AACU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AiBvB8B;AACnC;AACQ;AAI/C;IAA6B,2BAAQ;IAQjC,iBACI,YAAoB,EACpB,QAAiB,EACjB,MAA6C,EAC7C,YAAiC;eAEjC,kBAAM;YACF,QAAQ;YACR,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,cAAc;YACpB,UAAU,EAAE,KAAK;SACpB,EAAE,MAAM,EAAE,YAAY,CAAC;IAE5B,CAAC;IAjBD,sBAAI,kCAAa;aAAjB;YACI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;QAC5C,CAAC;;;OAAA;IAiBO,6BAAW,GAAnB,UAAoB,KAAa;QAC7B,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC;QACrC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;YAClB,OAAO,sDAAc,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;OAQG;IACH,4BAAU,GAAV,UAAW,WAAmB,EAAE,OAAoB,EAAE,MAA2B;QAAjF,iBASC;QARG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,WAAW,EACX,WAAW,EACX,6CAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAC3B,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,+BAAa,GAAb,UAAc,WAAmB,EAAE,MAA2B;QAA9D,iBAaC;QAZG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAEnC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACrE,YAAQ,GAAsB,MAAM,GAA5B,EAAE,gBAAgB,GAAI,MAAM,GAAV,CAAW;YAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,kBAAkB,EAClB,WAAW,EACX,gBAAgB,CACnB,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAU,MAAM,EAAE,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IAEP,CAAC;IAEO,6BAAW,GAAnB,UAAoB,uBAAwC,EAAE,MAA0B;QAAxF,iBAYC;QAXG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YAClE,YAAQ,GAAsB,MAAM,GAA5B,EAAE,gBAAgB,GAAI,MAAM,GAAV,CAAW;YAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,mBAAmB,EACnB,gBAAgB,EAChB,IAAI,CACP,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,4BAAU,GAAV,UAAW,MAA2B;QAClC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC,WAAW,CACnB,IAAI,CAAC,mBAAmB,EAAE,EAAE,MAAM,CACrC,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACH,uCAAqB,GAArB,UAAsB,MAA2B;QAC7C,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QACxC,IAAM,WAAW,GAAG,iDAAiD,CAAC;QACtE,OAAO,IAAI,CAAC,WAAW,CACnB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE,MAAM,CACxC,CAAC;IACN,CAAC;IAED;;;;;;;OAOG;IACH,yBAAO,GAAP,UAAQ,KAA6B,EAAE,MAA2B;QAC9D,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC,WAAW,CAAC;YACpB,OAAO,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;YACvB,QAAQ,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;YACzB,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,IAAI,EAAE,KAAK,CAAC,IAAI;SACnB,EAAE,MAAM,CAAC,CAAC;IACf,CAAC;IAED;;;;;;;OAOG;IACH,6BAAW,GAAX,UAAY,KAAkC,EAAE,MAA2B;QACvE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAEzB,YAAQ,GAAiC,KAAK,SAAtC,EAAE,OAAO,GAAwB,KAAK,QAA7B,EAAE,IAAI,GAAkB,KAAK,KAAvB,EAAE,WAAW,GAAK,KAAK,YAAV,CAAW;QACvD,IAAM,QAAQ,GAAG,6CAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD;YACI,QAAQ,CAAC,GAAG,CAAC,WAAC,IAAI,oDAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,WAAC,IAAI,oDAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC;YACpC,IAAI,IAAI,QAAQ;SACnB,EACD,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC,CACtC,CAAC;QAEF,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAChC,WAAW,EACX,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,MAAM,CACT,CAAC;IAEN,CAAC;IAED;;;;;;;;OAQG;IACH,+BAAa,GAAb,UAAc,OAAoB,EAAE,MAAmB,EAAE,MAA2B;QAApF,iBAWC;QAVG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,gBAAgB,EAChB,6CAAS,CAAC,KAAK,CAAC,OAAO,CAAC,EACxB,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAC1B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,mCAAiB,GAAjB,UAAkB,QAAuB,EAAE,OAAsB,EAAE,MAA2B;QAA9F,iBAkBC;QAjBG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAC;YAC9B,OAAO,6CAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,IAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,WAAC;YAC9B,OAAO,6CAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,eAAe,EACf,WAAW,EACX,YAAY,CACf,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,8BAAY,GAAZ,UAAa,mBAA2B,EAAE,MAA2B;QACjE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC,eAAe,CACvB,mBAAmB,EACnB,uDAAmB,CAAC,eAAe,EACnC,KAAK,EACL,MAAM,CACT,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACH,oCAAkB,GAAlB,UAAmB,mBAA2B,EAAE,MAA2B;QACvE,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC,eAAe,CACvB,mBAAmB,EACnB,uDAAmB,CAAC,eAAe,EACnC,IAAI,EACJ,MAAM,CACT,CAAC;IACN,CAAC;IAED;;;;;;;OAOG;IACH,kCAAgB,GAAhB,UAAiB,mBAA2B,EAAE,MAA2B;QACrE,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAGtC,OAAO,IAAI,CAAC,eAAe,CACvB,mBAAmB,EACnB,uDAAmB,CAAC,oBAAoB,EACxC,KAAK,EACL,MAAM,CACT,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACH,wCAAsB,GAAtB,UAAuB,mBAA2B,EAAE,MAA2B;QAC3E,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;QAG5C,OAAO,IAAI,CAAC,eAAe,CACvB,mBAAmB,EACnB,uDAAmB,CAAC,oBAAoB,EACxC,IAAI,EACJ,MAAM,CACT,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACH,kCAAgB,GAAhB,UAAiB,MAAc;QAC3B,OAAO,IAAI,CAAC,WAAW,CACnB,MAAM,EAAE,uDAAmB,CAAC,eAAe,CAC9C,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACH,sCAAoB,GAApB,UAAqB,MAAc;QAC/B,OAAO,IAAI,CAAC,WAAW,CACnB,MAAM,EAAE,uDAAmB,CAAC,oBAAoB,CACnD,CAAC;IACN,CAAC;IAED;;;;;;;OAOG;IACH,0BAAQ,GAAR,UAAS,KAA8B,EAAE,MAA2B;QAChE,OAAO,IAAI,CAAC,eAAe,CACvB,KAAK,EAAE,MAAM,CAChB,CAAC;IACN,CAAC;IACL,cAAC;AAAD,CAAC,CApV4B,gDAAQ,GAoVpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1VyD;AACnB;AAEoB;AACX;AAGhD;IAA2B,yBAAQ;IAE/B,eACI,YAAoB,EACpB,QAAiB,EACjB,MAA6C,EAC7C,YAAiC;eAEjC,kBAAM;YACF,QAAQ;YACR,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,KAAK;SACpB,EAAE,MAAM,EAAE,YAAY,CAAC;IAC5B,CAAC;IAED,0BAAU,GAAV,UAAW,WAAmB,EAAE,MAA2B;QAA3D,iBAQC;QAPG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,WAAW,EACX,WAAW,CACd,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,4BAAY,GAAZ,UAAa,WAAmB,EAAE,MAAwC;QAA1E,iBAcC;QAdiC,oCAAwC;QACtE,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAE7C,IAAM,gBAAgB,GAAG,cAAc,CAAC,CAAC,CAAC,iDAAc,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEtG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC3D,oBAAgB,GAAc,MAAM,GAApB,EAAE,QAAQ,GAAI,MAAM,GAAV,CAAW;YAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,WAAW,EACX,WAAW,EACX,gBAAgB,CACnB,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACP,CAAC;IAED,uBAAO,GAAP,UAAQ,MAAmB,EAAE,MAAsC;QAAnE,iBAkBC;QAlB4B,oCAAsC;QAC/D,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAE7C,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YACjD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,kBAAkB,CAAC,CAAC,KAAK,EAAE,CAAC;SACnE;QAED,IAAM,gBAAgB,GAAG,cAAc,CAAC,CAAC,CAAC,iDAAc,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEtG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC3D,oBAAgB,GAAc,MAAM,GAApB,EAAE,QAAQ,GAAI,MAAM,GAAV,CAAW;YAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,SAAS,EACT,gBAAgB,EAChB,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAC1B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,0BAAU,GAAV,UAAW,MAAsC;QAAtC,oCAAsC;QAC7C,OAAO,IAAI,CAAC,OAAO,CACf,yCAAU,EACR,MAAM,CACX,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACH,uBAAO,GAAP,UAAQ,MAAmB,EAAE,WAAmB,EAAE,MAA2B;QACzE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAChC,WAAW,EACX,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,MAAM,CACT,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACH,8BAAc,GAAd,UAAe,MAAmB,EAAE,WAAmB,EAAE,aAA0B,EAAE,YAAoB,EAAE,MAA2B;QAAtI,iBAuBC;QAtBG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAC,OAAe;YAC1C,IAAI,OAAO,KAAK,CAAC,EAAE;gBACf,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,CAAC;aACjE;YACD,IAAM,WAAW,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;YAEF,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAE9C,OAAO,KAAI,CAAC,UAAU,CAAC,cAAc,CACjC,KAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,WAAW,EACX,YAAY,EACZ,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IAEP,CAAC;IAEO,6BAAa,GAArB,UAAsB,MAAmB,EAAE,WAAmB,EAAE,MAA+B;QAA/F,iBAQC;QAR+D,oCAA+B;QAC3F,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAGlC,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC3E,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oCAAoB,GAA5B,UAA6B,MAAmB,EAAE,WAAmB,EAAE,aAA0B,EAAE,YAAoB,EAAE,MAA+B;QAAxJ,iBA0BC;QA1BwH,oCAA+B;QACpJ,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAC,OAAe;YAC1C,IAAI,OAAO,KAAK,CAAC,EAAE;gBACf,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,CAAC;aACjE;YACD,IAAM,WAAW,GAAG,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;YAEF,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAC1B,6CAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CACtB,6CAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC,CACJ,CAAC;YAEF,OAAO,KAAI,CAAC,UAAU,CAAC,cAAc,CACjC,4CAA4C,EAC5C,WAAW,EACX,WAAW,EACX,YAAY,EACZ,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,6BAAa,GAAb,UAAc,MAAmB,EAAE,MAA2B;QAA9D,iBAWC;QAVG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAGpC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,UAAU,EACV,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAC1B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,6BAAa,GAArB,UAAsB,mBAA2B,EAAE,MAAe,EAAE,MAAmC;QAAvG,iBAaC;QAbmE,oCAAmC;QACnG,IAAM,cAAc,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAC9C,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,uDAAmB,CAAC,aAAa,CAAC;QAElE,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACpC,mBAAmB,EACnB,cAAc,EACd,MAAM,CACT,CAAC,IAAI,CAAC,iBAAO;YACV,OAAO,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAC7B,OAAO,EAAE,MAAM,CAClB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,4BAAY,GAAZ,UAAa,mBAA2B,EAAE,MAA+B;QACrE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;;;OASG;IACH,kCAAkB,GAAlB,UAAmB,mBAA2B,EAAE,MAA+B;QAC3E,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;OAMG;IACH,gCAAgB,GAAhB,UAAiB,UAAkB;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,uDAAmB,CAAC,aAAa,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;OAQG;IACH,wBAAQ,GAAR,UAAS,MAAmB,EAAE,EAAU,EAAE,MAA2B;QACjE,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAEL,YAAC;AAAD,CAAC,CAvQ0B,gDAAQ,GAuQlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7QyD;AACnB;AAGQ;AAE/C;IAA4B,0BAAQ;IAGhC,gBACI,YAAoB,EACpB,QAAiB,EACjB,MAA6C,EAC7C,YAAiC;eAEjC,kBAAM;YACF,QAAQ;YACR,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,aAAa;YACnB,UAAU,EAAE,KAAK;SACpB,EAAE,MAAM,EAAE,YAAY,CAAC;IAC5B,CAAC;IAEO,8BAAa,GAArB,UAAsB,QAAQ;QAC1B,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SAC1D;QACD,OAAO,QAAQ,CAAC,GAAG,CAAC,iBAAO;YACvB,OAAO,6CAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,+BAAc,GAAd,UAAe,WAAmB,EAAE,OAA4B;QAAhE,iBAUC;QATG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,WAAW,EACX,WAAW,CACd,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,IAAI,CAAC,eAAK;YACT,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,yCAAwB,GAAxB,UAAyB,KAAa,EAAE,WAAmB,EAAE,OAA4B;QAAzF,iBAUC;QATG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,qBAAqB,EACrB,WAAW,EACX,KAAK,CACR,CAAC;YAEF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,6BAAY,GAAZ,UAAa,WAAmB,EAAE,KAAgB;QAAlD,iBAgBC;QAhBiC,wCAAgB;QAC9C,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,eAAK;YAC9C,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACtB,IAAI,KAAK,GAAG,KAAK,EAAE;gBACf,KAAK,GAAG,KAAK,CAAC;aACjB;YACD,IAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC5B,QAAQ,CAAC,IAAI,CACT,KAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,WAAW,CAAC,CAChD,CAAC;aACL;YACD,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,2BAAU,GAAV,UAAW,OAAe,EAAE,MAA2B;QAAvD,iBAeC;QAdG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,aAAa,EACb,OAAO,CACV,CAAC;YACF,OAAO,OAAO,CAAC,GAAG,CAAC;gBACf,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,MAAM,CAAC;gBACxC,KAAI,CAAC,mBAAmB,EAAE;aAC7B,CAAC,CAAC,IAAI,CAAC,gBAAM;gBACV,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED,8BAAa,GAAb,UAAc,WAAmB,EAAE,MAA2B;QAA9D,iBAaC;QAZG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAEnC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACrE,YAAQ,GAAsB,MAAM,GAA5B,EAAE,gBAAgB,GAAI,MAAM,GAAV,CAAW;YAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,kBAAkB,EAClB,WAAW,EACX,gBAAgB,CACnB,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAU,MAAM,EAAE,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IAEP,CAAC;IAED,wBAAO,GAAP,UAAQ,OAAoB,EAAE,MAA2B;QAAzD,iBAYC;QAXG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACrE,YAAQ,GAAsB,MAAM,GAA5B,EAAE,gBAAgB,GAAI,MAAM,GAAV,CAAW;YAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,SAAS,EACT,gBAAgB,EAChB,6CAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAC3B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,2BAAU,GAAV,UAAW,MAA2B;QAAtC,iBAYC;QAXG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAEhC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACrE,YAAQ,GAAsB,MAAM,GAA5B,EAAE,gBAAgB,GAAI,MAAM,GAAV,CAAW;YAC5C,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,mBAAmB,EACnB,gBAAgB,EAChB,IAAI,CACP,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAGD,wBAAO,GAAP,UAAQ,OAAoB,EAAE,WAAmB,EAAE,MAA2B;QAC1E,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAC1B,CAAC,SAAS,CAAC,CACd,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAChC,WAAW,EACX,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,MAAM,CACT,CAAC;IACN,CAAC;IAED,4BAAW,GAAX,UAAY,QAAuB,EAAE,WAAmB,EAAE,MAA2B;QACjF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;QAEjC,IAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAEjD,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,CAChB,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAChC,WAAW,EACX,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,MAAM,CACT,CAAC;IACN,CAAC;IAED,8BAAa,GAAb,UAAc,OAAoB,EAAE,MAA2B;QAA/D,iBAWC;QAVG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAGpC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,UAAU,EACV,6CAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAC3B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,0CAAyB,GAAzB,UAA0B,OAAoB,EAAE,MAA2B;QAA3E,iBAWC;QAVG,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;QAGhD,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,sBAAsB,EACtB,6CAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAC3B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,kCAAiB,GAAjB,UAAkB,QAAuB,EAAE,MAA2B;QAAtE,iBAYC;QAXG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QAExC,IAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAEjD,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,eAAe,EACf,WAAW,CACd,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,6BAAY,GAAZ,UAAa,mBAA2B,EAAE,MAA2B;QAArE,iBAYC;QAXG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACpC,mBAAmB,EACnB,uDAAmB,CAAC,cAAc,EAClC,KAAK,CACR,CAAC,IAAI,CAAC,iBAAO;YACV,OAAO,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAC7B,OAAO,EAAE,MAAM,CAClB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,oCAAmB,GAAnB,UAAoB,mBAA2B,EAAE,KAAa,EAAE,MAA2B;QAA3F,iBAaC;QAZC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACpC,mBAAmB,EACnB,uDAAmB,CAAC,cAAc,EAClC,KAAK,EACL,KAAK,CACR,CAAC,IAAI,CAAC,iBAAO;YACV,OAAO,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAC7B,OAAO,EAAE,MAAM,CAClB,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAED,qFAAqF;IACrF,6CAA6C;IAE7C,yDAAyD;IACzD,+BAA+B;IAC/B,mDAAmD;IACnD,gBAAgB;IAChB,iCAAiC;IACjC,8BAA8B;IAC9B,eAAe;IACf,qCAAqC;IACrC,qDAAqD;IACrD,kCAAkC;IAClC,cAAc;IACd,YAAY;IACZ,uCAAuC;IACvC,cAAc;IACd,IAAI;IAEJ,mCAAkB,GAAlB,UAAmB,mBAA2B,EAAE,MAA2B;QAA3E,iBAYC;QAXG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACpC,mBAAmB,EACnB,uDAAmB,CAAC,cAAc,EAClC,IAAI,CACP,CAAC,IAAI,CAAC,iBAAO;YACV,OAAO,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAC7B,OAAO,EAAE,MAAM,CAClB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,qFAAqF;IACrF,mDAAmD;IAGnD,gDAAgD;IAChD,+BAA+B;IAC/B,mDAAmD;IACnD,eAAe;IACf,0BAA0B;IAC1B,6CAA6C;IAC7C,8BAA8B;IAC9B,aAAa;IACb,UAAU;IACV,IAAI;IAEJ,iCAAgB,GAAhB,UAAiB,MAAc;QAC3B,OAAO,IAAI,CAAC,WAAW,CACnB,MAAM,EAAE,uDAAmB,CAAC,cAAc,CAC7C,CAAC;IACN,CAAC;IAED,qCAAoB,GAApB,UAAqB,MAAc;QAC/B,OAAO,IAAI,CAAC,WAAW,CACnB,MAAM,EAAE,uDAAmB,CAAC,mBAAmB,CAClD,CAAC;IACN,CAAC;IAED,wCAAuB,GAAvB,UAAwB,MAAc,EAAE,KAAa;QACjD,OAAO,IAAI,CAAC,kBAAkB,CAC1B,MAAM,EAAE,KAAK,EAAE,uDAAmB,CAAC,cAAc,CACpD,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG;IACH,yBAAQ,GAAR,UAAS,OAAe,EAAE,IAAY,EAAE,EAAU,EAAE,MAA2B;QAC3E,OAAO,IAAI,CAAC,cAAc,CACtB,IAAI,EACJ,EAAE,EACF,OAAO,EACP,MAAM,CACT,CAAC;IACN,CAAC;IAEL,aAAC;AAAD,CAAC,CApV2B,gDAAQ,GAoVnC;;;;;;;;;;;;;;;;;;;;;;;;AC3VoE;AAClB;AAC7B;AAEgB;AAEc;AACsB;AAS1E;IAQI,kBAAY,MAA8C,EAAE,SAAoB;QAC5E,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;IACxD,CAAC;IAEO,+BAAY,GAApB,UAAqB,WAAmB,EAAE,OAA4B;QAClE,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;QAElB,QAAQ,WAAW,EAAE;YACjB,KAAK,oEAAoE,CAAC;YAC1E,KAAK,oEAAoE;gBACrE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAC7B,aAAG;oBACC,UAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE;wBACzD,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,oEAAoE;gBADpG,CACoG,CAC3G,CAAC;gBACF,MAAM;YAEV,KAAK,oEAAoE,CAAC;YAC1E,KAAK,oEAAoE;gBACrE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAC7B,aAAG;oBACC,UAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE;wBACzD,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,oEAAoE;gBADpG,CACoG,CAC3G,CAAC;gBACF,MAAM;YAEV;gBACI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,aAAG,IAAI,UAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE,EAAzD,CAAyD,CAAC,CAAC;SAC3G;QACD,IAAI,QAAQ,GAAG,CAAC,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC/C;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,oCAAiB,GAAzB,UAA0B,WAAmB,EAAE,OAA4B;QACvE,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,QAAQ,WAAW,EAAE;YACjB,KAAK,oEAAoE,CAAC;YAC1E,KAAK,oEAAoE;gBACrE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAC5B,UAAC,CAAC,EAAE,GAAG,EAAE,KAAK;oBACd,QAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE;wBACvD,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,oEAAoE,CAAC;wBACrG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC;gBAFvC,CAEuC,EAAE,EAAE,CAC9C,CAAC;gBACF,MAAM;YAEV,KAAK,oEAAoE,CAAC;YAC1E,KAAK,oEAAoE;gBACrE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAC5B,UAAC,CAAC,EAAE,GAAG,EAAE,KAAK;oBACd,QAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE;wBACvD,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,oEAAoE,CAAC;wBACrG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC;gBAFvC,CAEuC,EAAE,EAAE,CAC9C,CAAC;gBACF,MAAM;YAEV,KAAK,oEAAoE;gBACrE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAC5B,UAAC,CAAC,EAAE,GAAG,EAAE,KAAK;oBACd,QAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,oEAAoE;wBAClG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,oEAAoE,CAAC;wBACrG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC;gBAFvC,CAEuC,EAAE,EAAE,CAC9C,CAAC;gBACF,MAAM;YAEV;gBACI,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAC5B,UAAC,CAAC,EAAE,GAAG,EAAE,KAAK;oBACd,QAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,WAAW,EAAE,CAAC;wBACxD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC;gBADvC,CACuC,EAAE,EAAE,CAC9C,CAAC;SACT;QACD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC/C;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,oCAAiB,GAAjB,UAAkB,UAAkB;QAChC,OAAO,OAAO,CAAC,GAAG,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;YAClC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC;SAC/C,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,OAAO;gBACH,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;gBACzB,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW;aACpB,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kCAAe,GAAvB,UAAwB,IAAqB;QACzC,iEAAiE;QACjE,OAAO,IAAI,oCAAK,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CACxC,IAAI,oCAAK,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CACnC,CAAC;IACN,CAAC;IAED,iCAAc,GAAd,UAAe,UAAkB;QAAjC,iBAQC;QAPG,OAAO,IAAI,CAAC,iBAAiB,CACzB,UAAU,CACb,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,eAAe,CACvB,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;OAUG;IACK,mCAAgB,GAAxB,UAAyB,aAAqB;QAA9C,iBAyBC;QAxBG,mDAAmD;QACnD,IAAI,eAA8B,CAAC;QACnC,OAAO,IAAI,CAAC,SAAS,CAAC,sBAAsB,CACxC,aAAa,CAChB,CAAC,IAAI,CAAC,qBAAW;YACd,eAAe,GAAG,WAAW,CAAC;YAC9B,OAAO,KAAI,CAAC,SAAS,CAAC,MAAM,CACxB,cAAc,EACd,6CAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAC/B,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,OAAO,MAAM,CAAC,IAAI,EAAkB,CAAC;QACzC,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAa;YACjB,OAAO;gBACH,sEAAsE;gBACtE,iBAAiB,EAAE,eAAe;gBAClC,iBAAiB;gBACjB,yBAAyB;gBACzB,GAAG,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACjC,6BAA6B;gBAC7B,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE;aACtB,CAAC;QACxB,CAAC,CAAC,CAAC;IAEP,CAAC;IAEO,0CAAuB,GAA/B,UAAgC,aAAqB;QAArD,iBAeC;QAdG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACtD,OAAO,8CAAO,CAAC,OAAO,CAAC,gBAAgB,CACnC,IAAI,CAAC,MAAM,CAAC,OAAO,EACnB,aAAa,CAChB,CAAC,IAAI,CAAC,qBAAW;YACd,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC;YACnE,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE;gBAC1F,MAAM,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACpC;YACD,OAAO,WAAW,CAAC;QACvB,CAAC,CAAC,CAAC,KAAK,CAAC,aAAG;YACR,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;YACzD,OAAO,KAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gCAAa,GAArB,UAAsB,aAAqB,EAAE,aAA6B;QACtE,OAAO,6CAAS,CAAC,eAAe,CAC5B,IAAI,CAAC,YAAY,EACjB,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,EACjC,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,EAC/B,QAAQ,CAAC,aAAa,GAAG,EAAE,EAAE,EAAE,CAAC,CACnC,CAAC;IACN,CAAC;IAEO,uCAAoB,GAA5B,UAA6B,aAAqB,EAAE,aAA6B;QAAjF,iBAgBC;QAdG,OAAO,8CAAO,CAAC,OAAO,CAAC,QAAQ,CAC3B,IAAI,CAAC,MAAM,CAAC,OAAO,EACnB,aAAa,CAAC,KAAK,EACnB,aAAa,CAAC,GAAG,EACjB,aAAa,CAChB,CAAC,IAAI,CAAC,oBAAU;YACb,IAAI,CAAC,UAAU,EAAE;gBACb,MAAM,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACpC;YACD,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACvD,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC,CAAC,KAAK,CAAC,WAAC;YACN,OAAO,KAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,sCAAmB,GAA3B,UAA4B,QAAgB,EAAE,cAAsB;QAApE,iBAaC;QAXG,OAAO,8CAAO,CAAC,OAAO,CAAC,YAAY,CAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAChD,CAAC,IAAI,CAAC,mBAAS;YACZ,IAAI,CAAC,SAAS,EAAE;gBACZ,MAAM,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACpC;YACD,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACtD,OAAO,SAAS,CAAC;QACrB,CAAC,CAAC,CAAC,KAAK,CAAC,WAAC;YACN,OAAO,KAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACP,CAAC;IAED,sCAAmB,GAAnB,UAAoB,UAAkB,EAAE,WAAmB,EAAE,MAAe,EAAE,KAAS;QAAvF,iBAmGC;QAnG6E,iCAAS;QAEnF,IAAI,MAAM,IAAI,CAAC,8CAAO,CAAC,OAAO,EAAE;YAC5B,IAAI,4DAAW,CAAC,yCAAU,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;SACtD;QAED,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SAC3D;QAED,IAAI,aAAqB,EACrB,aAA6B,EAC7B,OAA4B,EAC5B,KAA4B,EAC5B,UAAU,CAAC;QAEf,IAAI,MAAM,EAAE;YACR,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;SAC5D;QAED,OAAO,IAAI,CAAC,iBAAiB,CACzB,UAAU,CACb,CAAC,IAAI,CAAC,mBAAS;YACZ,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;gBAClC,MAAM,IAAI,KAAK,CACX,mDAAmD,CACtD,CAAC;aACL;YAED,kDAAkD;YAClD,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;YACxC,oDAAoD;YACpD,sCAAsC;YACtC,OAAO,OAAO,CAAC,GAAG,CAAC;gBACf,KAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,UAAU,CAAC;gBACnD,KAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,aAAa,CAAC;aAC3D,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,GAAW,MAAM,GAAjB,EAAE,KAAK,GAAI,MAAM,GAAV,CAAW;YAC1B,+DAA+D;YAC/D,OAAO,KAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,IAAI,CAAC,6BAAmB;YACvB,aAAa,GAAG,mBAAmB,CAAC;YACpC,6BAA6B;YAC7B,OAAO,KAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,IAAI,CAAC,0BAAgB;YACpB,UAAU,GAAG,gBAAgB,CAAC;YAC9B,+BAA+B;YAC/B,OAAO,6CAAS,CAAC,eAAe,CAC5B,OAAO,EACP,KAAK,EACL,KAAI,CAAC,YAAY,EACjB,KAAI,CAAC,kBAAkB,CAC1B,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,UAAC,YAAiB;YACtB,4CAA4C;YAE5C,4BAA4B;YAC5B,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,IAAM,UAAU,GAAG,KAAI,CAAC,iBAAiB,CACrC,WAAW,EAAE,OAAO,CACvB,CAAC;gBAEF,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE;oBAC5B,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;iBACrF;gBAED,OAAO,KAAI,CAAC,cAAc,CACtB,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAC1C,UAAU,EACV,aAAa,EACb,KAAK,CAAC,SAAS,EACf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EACnD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAC/C,6CAAS,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,cAAc;gBAClD,YAAY,CAAC,WAAW,EACxB,YAAY,CAAC,IAAI,EACjB,UAAU,CAAC,KAAK,CAAC,CACpB,CAAC;aACL;YAED,wBAAwB;YACxB,IAAM,QAAQ,GAAG,KAAI,CAAC,YAAY,CAC9B,WAAW,EAAE,OAAO,CACvB,CAAC;YAEF,OAAO,KAAI,CAAC,cAAc,CACtB,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAC1C,UAAU,EACV,aAAa,EACb,KAAK,CAAC,SAAS,EACf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EACnD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAC/C,6CAAS,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,cAAc;YAClD,YAAY,CAAC,WAAW,EACxB,YAAY,CAAC,IAAI,EACjB,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,+CAA4B,GAA5B,UAA6B,UAAkB,EAAE,WAAmB,EAAE,MAAe;QAArF,iBA8EC;QA5EG,IAAI,MAAM,IAAI,CAAC,8CAAO,CAAC,OAAO,EAAE;YAC5B,IAAI,4DAAW,CAAC,yCAAU,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;SACtD;QAED,IAAI,aAAqB,EACrB,aAA6B,EAC7B,OAA4B,EAC5B,KAA4B,EAC5B,UAAU,CAAC;QAEf,OAAO,IAAI,CAAC,iBAAiB,CACzB,UAAU,CACb,CAAC,IAAI,CAAC,mBAAS;YACZ,IAAI,CAAC,MAAM,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE;gBAC7C,MAAM,IAAI,KAAK,CACX,mDAAmD,CACtD,CAAC;aACL;YAED,kDAAkD;YAClD,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;YACxC,oDAAoD;YACpD,sCAAsC;YACtC,OAAO,OAAO,CAAC,GAAG,CAAC;gBACf,KAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,UAAU,CAAC;gBACnD,KAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,aAAa,CAAC;aAC3D,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,GAAW,MAAM,GAAjB,EAAE,KAAK,GAAI,MAAM,GAAV,CAAW;YAC1B,+DAA+D;YAC/D,OAAO,CACH,MAAM,CAAC,CAAC,CAAC,KAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,CAAC;gBAClD,KAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAC3C,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,6BAAmB;YACvB,aAAa,GAAG,mBAAmB,CAAC;YACpC,6BAA6B;YAC7B,OAAO,CACH,MAAM,CAAC,CAAC,CAAC,KAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC;gBAC9D,KAAI,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,CACvD,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,0BAAgB;YACpB,UAAU,GAAG,gBAAgB,CAAC;YAC9B,+BAA+B;YAC/B,OAAO,6CAAS,CAAC,eAAe,CAC5B,OAAO,EACP,KAAK,EACL,KAAI,CAAC,YAAY,EACjB,KAAI,CAAC,kBAAkB,CAC1B,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,UAAC,YAAiB;YACtB,IAAM,UAAU,GAAG,KAAI,CAAC,iBAAiB,CACrC,WAAW,EAAE,OAAO,CACvB,CAAC;YACF,IAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,6CAA6C;YAC7C,KAAuB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU,EAAE;gBAA9B,IAAM,QAAQ;gBACf,QAAQ,CAAC,IAAI,CACT,KAAI,CAAC,cAAc,CACf,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAC1C,UAAU,EACV,aAAa,EACb,KAAK,CAAC,SAAS,EACf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EACnD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAC/C,6CAAS,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,cAAc;gBAClD,YAAY,CAAC,WAAW,EACxB,YAAY,CAAC,IAAI,EACjB,QAAQ,CACX,CACJ,CAAC;aACL;YAED,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,iCAAc,GAAtB,UACI,YAAY,EACZ,eAAe,EACf,WAAW,EACX,SAAS,EACT,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,kBAAkB,EAClB,IAAI,EACJ,QAAQ;QAER,OAAO,2DAAU,CAAC,WAAW,CACzB,iDAAU,CAAC;YACP,YAAY;YACZ,eAAe;YACf,WAAW;YACX,SAAS;YACT,2DAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACxC,2DAAU,CAAC,WAAW,CAAC,YAAY,CAAC;YACpC,2DAAU,CAAC,WAAW,CAAC,OAAO,CAAC;YAC/B,2DAAU,CAAC,WAAW,CAAC,iDAAU,CAAC,kBAAkB,CAAW,CAAC;YAChE,2DAAU,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YACvE,QAAQ;SACX,CAAW,CACf,CAAC;IACN,CAAC;IAED,8BAAW,GAAX,UAAY,UAAU,EAAE,KAAK,EAAE,WAAW;QAA1C,iBA4CC;QA3CG,IAAI,cAAsB,EACtB,OAA4B,EAC5B,KAA4B,CAAC;QAEjC,OAAO,OAAO,CAAC,GAAG,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;YAClC,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,UAAU,CAAC;SACtD,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3B,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,OAAO,KAAI,CAAC,YAAY,CAAC,uBAAuB,CAC5C,OAAO,CAAC,WAAW,CACtB,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAW;YACf,KAAK,GAAG,WAAW,CAAC;YACpB,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE;gBAC/F,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAU,CAAC,qBAAqB,CAAC,CAAC,KAAK,EAAE,CAAC;aAC5E;YACD,OAAO,6CAAS,CAAC,eAAe,CAC5B,OAAO,EACP,KAAK,EACL,KAAI,CAAC,YAAY,EACjB,KAAI,CAAC,kBAAkB,CAC1B,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,UAAC,YAAiB;YACtB,IAAI,QAAQ,CAAC;YACb,IAAM,SAAS,GAAG,EAAE,CAAC;YACrB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,cAAI;gBAC1B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBACrE,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;YAEH,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,IAAM,UAAU,GAAG,KAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBAChE,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;aAChC;YAED,QAAQ,GAAG,KAAI,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAEnD,OAAO,KAAI,CAAC,YAAY,CAAC,YAAY,CACjC,OAAO,CAAC,WAAW,EAAE,2DAAU,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAClF,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IACL,eAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1eyD;AAG1D;IAAgC,8BAA2B;IAEvD,oBAAY,OAA8C,EAAE,OAAe;eACvE,kBAAM;YACF,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,IAAI;SACjB,EAAE,OAAO,CAAC;IACf,CAAC;IAED,2BAAM,GAAN,UAAO,UAAkB;QAAE,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,OAAO,QAAQ,CAAC,MAAM,OAAf,QAAQ,iBAAQ,UAAU,GAAK,IAAI,UAAE;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAED,mCAAc,GAAd,UACI,YAAoB,EACpB,aAAqB,EACrB,WAAmB,EACnB,YAAoB,EACpB,MAA2B;QAL/B,iBAgBC;QATG,OAAO,IAAI,CAAC,MAAM,CACd,eAAe,EACf,YAAY,EACZ,aAAa,EACb,WAAW,EACX,YAAY,CACf,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAEL,iBAAC;AAAD,CAAC,CAnC+B,6CAAS,GAmCxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtC+B;AACwB;AAChB;AAED;AACE;AACP;AAEE;AACO;AAEf;AACS;AACR;AACC;AAE9B;IAA+B,6BAA8B;IAA7D;;IAiGA,CAAC;IA5FG,wBAAI,GAAJ,UAAK,MAAwB;QAA7B,iBAoCC;QAnCG,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAC;YAC7B,IAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;YACjD,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM,CAClC;gBACI,gBAAgB,EAAE,gBAAgB,CAAC,qBAAqB;gBACxD,SAAS,EAAE,MAAM,CAAC,mBAAmB,CAAC,cAAc;gBACpD,UAAU,EAAE,gBAAgB,CAAC,UAAU;aACtB,EACrB,MAAM,CACT,CAAC;YAEF,KAAI,CAAC,gBAAgB,GAAG,IAAI,iEAAgB,CACxC,KAAI,CAAC,MAAM,EACX,MAAM,CAAC,gBAAgB,CAC1B,CAAC;YAEF,IAAM,SAAS,GAAG,IAAI,kDAAS,CAC3B,KAAI,CAAC,MAAM,EACX,MAAM,CAAC,SAAS,CACnB,CAAC;YAEF,KAAI,CAAC,QAAQ,GAAG,IAAI,gDAAQ,CACxB,KAAI,CAAC,MAAM,EACX,SAAS,CACZ,CAAC;YAEF,KAAI,CAAC,UAAU,GAAG,IAAI,oDAAU,CAC5B,KAAI,CAAC,MAAM,EACX,MAAM,CAAC,UAAU,CACpB,CAAC;YAEF,OAAO,KAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,yBAAK,GAAL,UAAM,YAAY,EAAE,QAAkB;QAClC,OAAO,IAAI,yCAAK,CACZ,YAAY,EACZ,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;IACN,CAAC;IAED,0BAAM,GAAN,UAAO,YAAY,EAAE,QAAkB;QACnC,OAAO,IAAI,2CAAM,CACb,YAAY,EACZ,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;IACN,CAAC;IAED,2BAAO,GAAP,UAAQ,YAAY,EAAE,QAAkB;QACpC,OAAO,IAAI,6CAAO,CACd,YAAY,EACZ,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;IACN,CAAC;IAED,gCAAY,GAAZ,UAAa,MAAmB,EAAE,WAAmB,EAAE,MAA0B;QAC7E,OAAO,IAAI,yCAAK,CACZ,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IAED,uCAAmB,GAAnB,UACI,MAAmB,EACnB,WAAmB,EACnB,aAA0B,EAC1B,YAAoB,EACpB,MAA0B;QAE1B,OAAO,IAAI,yCAAK,CACZ,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC,sBAAsB,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;IACxF,CAAC;IAEO,iCAAa,GAArB;QACI,OAAO;YACH,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,UAAU,EAAE,IAAI,CAAC,UAAU;SACb,CAAC;IACvB,CAAC;IACL,gBAAC;AAAD,CAAC,CAjG8B,gDAAY,GAiG1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjHyE;AAI1E;IAA8B,4BAA2B;IAIrD,kBACI,aAAiC,EACjC,MAA6C,EACnC,eAAoC;QAHlD,YAKI,kBAAM,aAAa,EAAE,MAAM,CAAC,SAC/B;QAHa,qBAAe,GAAf,eAAe,CAAqB;;IAGlD,CAAC;IAED,sBAAc,sCAAgB;aAA9B;YACI,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,gBAAgB,CAAC;QACnD,CAAC;;;OAAA;IAED,sBAAc,gCAAU;aAAxB;YACI,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC;QAC7C,CAAC;;;OAAA;IAED,sBAAc,8BAAQ;aAAtB;YACI,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC;QAC3C,CAAC;;;OAAA;IAGD,sCAAmB,GAAnB;QAAA,iBAsBC;QArBG,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,sDAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAChD;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAC/B,aAAa,EACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAC7B,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAS;YACb,IAAI,CAAC,SAAS,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;aACzC;YACD,OAAO,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAC/B,iBAAiB,EAAE,SAAS,CAC/B,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,+BAAqB;YACzB,OAAO,qBAAqB,CAAC,IAAI,EAAU,CAAC;QAChD,CAAC,CAAC,CAAC,IAAI,CAAC,0BAAgB;YACpB,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;YACzC,OAAO,gBAAgB,CAAC;QAC5B,CAAC,CAAC,CAAC;IACP,CAAC;IAES,8BAAW,GAArB,UAAsB,MAAc,EAAE,cAAsB;QAA5D,iBAWC;QAVG,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAC5B,MAAM,EAAE,CAAC,EAAE,cAAc,CAC5B,CAAC,IAAI,CAAC,kBAAQ;YACX,OAAO,KAAI,CAAC,gBAAgB,CAAC,eAAe,CACxC,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAES,qCAAkB,GAA5B,UAA6B,MAAc,EAAE,KAAa,EAAE,cAAsB;QAAlF,iBAWD;QAVG,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAC5B,MAAM,EAAE,KAAK,EAAE,cAAc,CAChC,CAAC,IAAI,CAAC,kBAAQ;YACX,OAAO,KAAI,CAAC,gBAAgB,CAAC,eAAe,CACxC,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEW,kCAAe,GAAzB,UAA0B,UAAkB,EAAE,cAAsB,EAAE,MAAe,EAAE,MAA0B;QAAjH,iBAUC;QATG,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CACpC,UAAU,EACV,cAAc,EACd,MAAM,CACT,CAAC,IAAI,CAAC,iBAAO;YACV,OAAO,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAC7B,OAAO,EAAE,MAAM,CAClB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IACL,eAAC;AAAD,CAAC,CAtF6B,6CAAS,GAsFtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1FgE;AAKjE;IAA+B,6BAA2B;IAEtD,mBAAY,OAA8C,EAAE,OAAe;eACvE,kBAAM;YACF,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,IAAI;SACjB,EAAE,OAAO,CAAC;IACf,CAAC;IAED,0BAAM,GAAN,UAAO,UAAkB;QAAE,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,OAAO,QAAQ,CAAC,MAAM,OAAf,QAAQ,iBAAQ,UAAU,GAAK,IAAI,UAAE;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAED,qCAAiB,GAAjB;QAAA,iBAIC;QAHG,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC/C,OAAO,MAAM,CAAC,IAAI,CAAS,EAAE,EAAE,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACP,CAAC;IAEK,0CAAsB,GAA5B,UAA6B,gBAA6B;;;;;;wBAChD,MAAM,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBACzB,MAAM,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBACzB,kBAAkB,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;wBAE/C,gBAAgB,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;wBAE9C,KAAK,GAAG,MAAM,CAAC;wBAGJ,qBAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;;wBAAhD,MAAM,GAAG,SAAuC;wBAC3B,qBAAM,MAAM,CAAC,IAAI,EAAU;;wBAAhD,kBAAkB,GAAG,SAA2B;wBAClD,GAAG,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAC1C,kBAAkB,CACrB,CAAC;;;6BAIK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;wBACjB,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;4BACf,GAAG,GAAG,KAAK,CAAC;4BACZ,wBAAM;yBACT;wBACK,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;wBACZ,qBAAM,IAAI,CAAC,MAAM,CACxC,cAAc,EACd,GAAG,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,QAAQ,EAAE,CACzC;;wBAHK,kBAAkB,GAAG,SAG1B;wBACmB,qBAAM,kBAAkB,CAAC,IAAI,EAAkC;;wBAA7E,WAAW,GAAG,SAA+D;wBAE7E,WAAW,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;wBAC9C,SAAS,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBAEhD,IAAI,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;4BACtE,oGAAoG;4BACpG,GAAG,GAAG,GAAG,CAAC;4BACV,wBAAM;yBACT;6BAAM,IAAI,WAAW,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE;4BACzC,uDAAuD;4BACvD,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;yBACzB;6BAAM,IAAI,SAAS,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE;4BACvC,sDAAsD;4BACtD,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;yBAC3B;;4BAEL,sBAAO,GAAG,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAC;;;;KACtC;IAEL,gBAAC;AAAD,CAAC,CAtE8B,6CAAS,GAsEvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3EyD;AAG1D;IAAsC,oCAA2B;IAE7D,0BAAY,OAA8C,EAAE,OAAe;eACvE,kBAAM;YACF,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,kBAAkB;YACxB,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,IAAI;SACjB,EAAE,OAAO,CAAC;IACf,CAAC;IAED,iCAAM,GAAN,UAAO,UAAkB;QAAE,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,OAAO,QAAQ,CAAC,MAAM,OAAf,QAAQ,iBAAQ,UAAU,GAAK,IAAI,UAAE;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAED,kCAAO,GAAP,UAAQ,WAAmB,EAAE,YAAoB,EAAE,WAAmB,EAAE,MAA2B;QAAnG,iBASC;QARG,OAAO,IAAI,CAAC,MAAM,CACd,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,WAAW,CACd,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,+BAAI,GAAJ,UAAK,WAAmB,EAAE,MAA0B;QAApD,iBAOC;QANG,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC/C,OAAO,KAAI,CAAC,YAAY,CACpB,MAAM,EACN,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,0CAAe,GAAf,UAAgB,QAAgB;QAAhC,iBAMC;QALG,OAAO,IAAI,CAAC,MAAM,CACd,gBAAgB,EAAE,QAAQ,CAC7B,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,WAAW,CAAU,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAEL,uBAAC;AAAD,CAAC,CA7CqC,6CAAS,GA6C9C;;;;;;;;;;;;;;;;;;AChDsC;AAEvC;IAGI,oBAAY,OAAe;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,+CAAW,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,2BAAM,GAAN,UAAO,OAAe,EAAE,OAAe,EAAE,UAAkB,EAAE,YAAoB;QAC7E,IAAM,GAAG,GAAG,UAAG,OAAO,cAAI,OAAO,wBAAc,UAAU,cAAI,YAAY,UAAO,CAAC;QACjF,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAC,MAAW;YAC9C,OAAO,MAAM,CAAC,GAAG,CAAC;QACtB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,+BAAU,GAAV,UAAW,OAAe,EAAE,OAAe;QACvC,IAAM,GAAG,GAAG,UAAG,OAAO,cAAI,OAAO,gBAAa,CAAC;QAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IACL,iBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;ACpB0C;AACR;AAGD;AAElC;IAAA;IAIA,CAAC;IAAD,cAAC;AAAD,CAAC;AAEM,IAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;AACrC,OAAO,CAAC,GAAG,GAAG,IAAI,oDAAU,CAAC,2CAAM,CAAC,WAAW,CAAC,CAAC;;;;;;;;;;;;;;;;;;ACbP;AACH;AAEvC;IAGI,wBAAY,OAAe;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,+CAAW,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAEO,wCAAe,GAAvB,UAAwB,OAAe,EAAE,GAAW;QAChD,OAAO,UAAG,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,SAAG,GAAG,CAAE,CAAC;IAC3D,CAAC;IAEO,0CAAiB,GAAzB,UAA0B,OAAe,EAAE,GAAW;QAClD,OAAO,UAAG,OAAO,cAAI,GAAG,CAAE,CAAC;IAC/B,CAAC;IAED,yCAAgB,GAAhB,UAAiB,OAAe,EAAE,WAAmB;QACjD,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,0BAAmB,WAAW,CAAE,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAIxB,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAM;YACf,IAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAkC,CAAC;YACpE,IAAM,wBAAwB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,CAC9E,iBAAiB,EAAE,EAAE,CACxB,CAAC,CAAC,CAAC,iBAAiB,CAAC;YACtB,MAAM,CAAC,iBAAiB,GAAG,IAAI,oCAAK,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC;YAClE,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,qCAAY,GAAZ,UAAa,OAAe,EAAE,UAAkB,EAAE,cAAsB;QACpE,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,wBAAiB,UAAU,6BAAmB,cAAc,CAAE,CAAC,CAAC;QAC1G,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAM,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC7C,OAAO,MAAM,CAAC,MAAM,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,iCAAQ,GAAR,UAAS,OAAe,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW;QAC7C,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,mCAA4B,KAAK,kBAAQ,GAAG,qBAAW,WAAW,CAAE,CAAC,CAAC;QAChH,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAM,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC7C,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,+CAAsB,GAAtB,UAAuB,OAAe,EAAE,SAAiB,EAAE,YAAoB;QAC3E,IAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,8BAAuB,SAAS,0BAAgB,YAAY,CAAE,CAAC,CAAC;QAC5G,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAM,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC7C,OAAO,MAAM,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,oDAA2B,GAA3B,UAA4B,OAAe,EAAE,SAAiB,EAAE,YAAoB;QAChF,IAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,wBAAiB,SAAS,0BAAgB,YAAY,CAAE,CAAC,CAAC;QACtG,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAM,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAM;YAC7C,OAAO,MAAM,CAAC,OAAO,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IACL,qBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;AC3D2C;AACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AGHP;AACM;AAe5C,IAAM,KAAK,GAAgB,EAAE,CAAC;AAE9B;IACI,oBAAmB,WAAmB,EAAS,OAAe;QAA3C,gBAAW,GAAX,WAAW,CAAQ;QAAS,YAAO,GAAP,OAAO,CAAQ;IAE9D,CAAC;IAED,yBAAI,GAAJ;QAAA,iBAWC;QAVG,OAAO,8CAAO,CAAC,GAAG,CAAC,UAAU,CACzB,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CACjC,CAAC,IAAI,CAAC,gBAAM;;YACT,KAAK,CAAC,KAAI,CAAC,WAAW,CAAC;gBACnB,GAAC,KAAI,CAAC,OAAO,IAAG;oBACZ,OAAO,EAAE,MAAM;oBACf,GAAG,EAAE,EAAE;iBACV;mBACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,8BAAS,GAAT,UAAU,IAAY;QAClB,OAAO,0CAAO,CACV,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAC7C,IAAI,CACP,CAAC;IACN,CAAC;IAED,2BAAM,GAAN,UAAO,YAAoB,EAAE,UAAqB;QAAlD,iBA0BC;QA1B4B,kDAAqB;QAC9C,IAAI,oBAAoB,CAAC;QAEzB,IACI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;YAChE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAC3C;YACE,oBAAoB,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SAChF;QAGD,IAAI,oBAAoB,EAAE;YACtB,IAAM,cAAc,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,cAAc,EAAE;gBAChB,OAAO,iDAAc,CAAM,cAAc,CAAC,CAAC;aAC9C;SACJ;QACD,OAAO,8CAAO,CAAC,GAAG,CAAC,MAAM,CACrB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,EACZ,UAAU,EACV,YAAY,CACf,CAAC,IAAI,CAAC,gBAAM;YACT,KAAI,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAC9C,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,2BAAM,GAAN,UAAO,YAAoB,EAAE,UAAkB,EAAE,GAAQ;QACrD,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC;QAC3D,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YACvB,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAC7B;QACD,QAAQ,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC;IAC7C,CAAC;IACL,iBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3EkD;AACA;AACb;AAEO;AACF;AAS3C;IAKI,mBACc,aAAiC,EACjC,MAA4C;QAD5C,kBAAa,GAAb,aAAa,CAAoB;QACjC,WAAM,GAAN,MAAM,CAAsC;IAE1D,CAAC;IAED,sBAAI,sCAAe;aAAnB;YACI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACtC,CAAC;;;OAAA;IAED,+BAAW,GAAX;QAAA,iBAgBC;QAfG,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,gEAAc,CAAe,IAAI,CAAC,SAAgB,CAAC,CAAC;SAC9D;QACD,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CACrB,aAAa,CAAC,IAAI,EAClB,aAAa,CAAC,UAAU,CAC3B,CAAC,IAAI,CAAC,aAAG;YACN,KAAI,CAAC,SAAS,GAAG,KAAI,CAAC,YAAY,CAAC;gBAC/B,GAAG;gBACH,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,YAAY,EAAE,aAAa,CAAC,OAAO;aACtC,CAAC,CAAC;YACH,OAAO,KAAI,CAAC,SAAS,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,8BAAU,GAAV;QAAA,iBASC;QARG,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,gEAAc,CAAS,IAAI,CAAC,QAAe,CAAC,CAAC;SACvD;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC3D,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,iBAAO;YACnC,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,OAAO,KAAI,CAAC,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAES,gCAAY,GAAtB,UAAuB,MAA0B,EAAE,MAA+B;QAAlF,iBAuBC;QAvBkD,oCAA+B;QAC9E,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE/B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,uBAAuB,CAC/B;YACI,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,IAAI;YACb,MAAM;YACN,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;SACxC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAC/C,IAAI,MAAM,CAAC,iBAAiB,EAAE;gBAC1B,OAAO,6CAAK,CAAC,MAAM,EAAE;oBACjB,IAAI,EAAE,MAAM,CAAC,SAAS,EAAE;oBACxB,EAAE,EAAE,MAAM,CAAC,OAAO;iBACQ,CAAC,CAAC;aACnC;YACD,IAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAC7B,MAAM,CACT,CAAC;YACF,OAAO,YAAY,CAAC;QACxB,CAAC,CAAC,CAAC;IACX,CAAC;IAES,mCAAe,GAAzB,UAA0B,MAA+B;QAA/B,oCAA+B;QACrD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE/B,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC7C,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC,uBAAuB,CAC/B;YACI,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAW;YACnB,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;SACxC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAC1C,IAAI,MAAM,CAAC,iBAAiB,EAAE;gBAC1B,OAAO,MAAa,CAAC;aACxB;YACD,IAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAC7B,MAAM,CACT,CAAC;YACF,OAAO,YAAY,CAAC;QACxB,CAAC,CAAC,CAAC;IACX,CAAC;IAES,mCAAe,GAAzB,UAA0B,MAA+B;QAA/B,oCAA+B;QACrD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC7C,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,uBAAuB,CAC/B;YACI,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAW;YACnB,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;SACxC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YAC7C,IAAI,MAAM,CAAC,iBAAiB,EAAE;gBAC1B,OAAO,MAAa,CAAC;aACxB;YACD,OAAO,MAAM,CAAC,IAAI,CACd,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,qCAAiB,GAAzB,UAA0B,MAA0B;QAChD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACrD,IAAI,sDAAW,CAAC,8CAAU,CAAC,0BAA0B,CAAC,CAAC,KAAK,EAAE,CAAC;SAClE;IACL,CAAC;IAES,+BAAW,GAArB,UAAyB,MAA0B,EAAE,MAA+B;QAApF,iBAqBC;QArBoD,oCAA+B;QAChF,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,uBAAuB,CAC/B;YACI,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,KAAK;YACd,MAAM;YACN,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;SACxC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACjD,IAAI,MAAM,CAAC,iBAAiB,EAAE;gBAC1B,OAAO,6CAAK,CAAC,MAAM,EAAE;oBACjB,IAAI,EAAE,MAAM,CAAC,SAAS,EAAE;oBACxB,EAAE,EAAE,KAAI,CAAC,SAAS,CAAC,OAAO;iBACA,CAAC,CAAC;aACnC;YACD,OAAO,MAAM,CAAC,IAAI,CACd,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACX,CAAC;IAES,6BAAS,GAAnB,UAAoB,QAAQ;QACxB,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEO,gCAAY,GAApB,UAAqB,EAA+B;YAA7B,QAAQ,gBAAE,YAAY,oBAAE,GAAG;QAC9C,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,OAAO,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,sBAAc,0CAAmB;aAAjC;YACI,IAAM,MAAM,GAAsB,IAAI,CAAC,MAAM,CAAC,MAAa,CAAC;YAC5D,OAAO,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;QACvC,CAAC;;;OAAA;IAED,sBAAc,yCAAkB;aAAhC;YACI,IAAM,MAAM,GAAsB,IAAI,CAAC,MAAM,CAAC,MAAa,CAAC;YAC5D,OAAO,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC;QACtC,CAAC;;;OAAA;IAES,2CAAuB,GAAjC,UAAkC,EAAgE;QAAlG,iBA2CC;YA3CmC,QAAQ,gBAAE,MAAM,cAAE,QAAQ,gBAAE,OAAO;QACnE,IAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC;QACpF,QAAQ,GAAG,6CAAK,CAAC,aAAa,EAAE,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;QAClD,IAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QACtB,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAChF,IAAM,WAAW,GAAG,UAAO,MAAiC;;;;;6BACzC,MAAM,EAAN,wBAAM;wBAAG,qBAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;;wBAAhC,cAAgC;;4BAAG,qBAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;;wBAAhC,cAAgC;;;wBAArF,MAAM,KAA+E;wBAC3F,sBAAO,IAAI,yCAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAC;;;aACrE,CAAC;QACF,+DAA+D;QAC/D,IAAI,OAAO,EAAE;YACT,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,uBAAa;gBAC/B,gBAAY,GAA2B,QAAQ,aAAnC,EAAE,oBAAoB,GAAK,QAAQ,qBAAb,CAAc;gBAExD,IAAM,kBAAkB,GAAG,KAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;gBACzE,IAAM,gBAAgB,GAAG,CAAC,YAAY,IAAI,oBAAoB,CAAC,CAAC;gBAChE,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,aAAa,CAAC;gBAErD,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,EAAE;oBACzC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;iBACzE;gBACD,6BAA6B;gBAC7B,OAAO,OAAO,CAAC,GAAG,CAAC;oBACf,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;wBAChB,CAAC,CAAC,WAAW,CAAC;4BACV,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE;yBAC9D,CAAC;wBACF,CAAC,CAAC,QAAQ,CAAC,QAAQ;oBACvB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;wBACb,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC;wBACpD,CAAC,CAAC,QAAQ,CAAC,KAAK;iBACvB,CAAC,CAAC,IAAI,CAAC,gBAAM;oBACH,YAAQ,GAAW,MAAM,GAAjB,EAAE,KAAK,GAAI,MAAM,GAAV,CAAW;oBACjC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;oBAEpC,QAAQ,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;oBACrC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;oBACvB,OAAO,QAAQ,CAAC;gBACpB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;QACD,OAAO,gEAAc,CAA4B,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAES,iCAAa,GAAvB,UAAwB,EAAU,EAAE,MAAmB,EAAE,MAA2B;QAApF,iBAWC;QAVG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,UAAU,EACV,EAAE,EACF,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAC1B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CACpB,MAAM,EAAE,MAAM,CACjB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAES,kCAAc,GAAxB,UAAyB,IAAY,EAAE,EAAU,EAAE,OAAe,EAAE,MAA0B;QAA9F,iBAYC;QAXG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,cAAc,EACd,IAAI,EACJ,EAAE,EACF,OAAO,CACV,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CACpB,MAAM,EAAE,MAAM,CACjB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAES,qCAAiB,GAA3B,UAA4B,UAAU;QAClC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,mDAAY,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;SACrF;IACL,CAAC;IAES,gCAAY,GAAtB,UAAuB,UAAU;QAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;SAC1E;IACL,CAAC;IAES,iCAAa,GAAvB,UAAwB,UAAU;QAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;SAC3E;IACL,CAAC;IAES,uCAAmB,GAA7B,UAA8B,UAAU;QACpC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8CAAU,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;SAClF;IACL,CAAC;IAES,mCAAe,GAAzB,UAA0B,KAA8B,EAAE,MAA0B;QAApF,iBAcC;QAbG,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,kBAAkB,EAClB,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,EAAE,EACR,6CAAS,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAC9B,6CAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,EAC7B,KAAK,CAAC,IAAI,IAAI,IAAI,CACrB,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CACpB,MAAM,EAAE,MAAM,CACjB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEL,gBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;ACtS8C;AAET;AAEtC;IAAA;QAEI,WAAM,GAA2B,IAAI,uDAAmB,EAAE,CAAC;IAiD/D,CAAC;IA7CG;;;;;;OAMG;IACH,qCAAc,GAAd,UAAe,MAAc;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAC/B,MAAM,CACT,CAAC;IACN,CAAC;IAED,kCAAW,GAAX,UAAY,aAAqB;QAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,IAAM,KAAK,GAAG,IAAI,wCAAS,CAAC;YACxB,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,sCAAsC,CAAC;YAC5E,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,eAAe;YACrB,UAAU,EAAE,SAAS;SACxB,EAAE,MAAM,CAAC,CAAC;QAEX,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACpC,OAAO,OAAO,CAAC,GAAG,CAAC;gBACf,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,aAAa,CAAC;gBAClD,KAAK,CAAC,aAAa,CAAC,CAChB,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CACjC;aACJ,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACH,WAAO,GAAiB,MAAM,GAAvB,EAAE,WAAW,GAAI,MAAM,GAAV,CAAW;YACtC,IAAM,cAAc,GAAG,oEAAoE,CAAC;YAC5F,IAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAC,IAAI,QAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,cAAc,EAA9B,CAA8B,CAAC,CAAC;YACzE,IAAI,CAAC,SAAS,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;aAClD;YACD,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,IAAM,aAAa,GAAG,oCAAK,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,oCAAK,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;YAC3F,OAAO,IAAI,oCAAK,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,GAAG,CAChC,aAAa,CAChB,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEL,mBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;AClDgB;AAcjB;IAAA;IA4GA,CAAC;IApGU,oBAAS,GAAhB,UAAiB,KAAa;QAC1B,IAAI,CAAC,GAAG,KAAK,CAAC;QAEd,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,4DAAqD,OAAO,CAAC,CAAE,CAAC,CAAC;SACpF;QAED,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,CAAC,GAAG,WAAI,CAAC,CAAE,CAAC;QAE9B,OAAO,CAAC,CAAC;IACb,CAAC;IAEM,wBAAa,GAApB,UAAqB,GAAW;QAC5B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,qEAA8D,OAAO,GAAG,CAAE,CAAC,CAAC;SAC/F;QAED,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;IAC5C,CAAC;IAoBM,sBAAW,GAAlB,UAAmB,KAAa,EAAE,MAAe;QAC7C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC;YAAE,OAAO,KAAK,CAAC;QAEhF,IAAI,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM;YAAE,OAAO,KAAK,CAAC;QAE5D,OAAO,IAAI,CAAC;IAChB,CAAC;IAnDM,mBAAQ,GAAG,UAAU,CAAS;QACjC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,4CAAqC,CAAC,CAAE,CAAC,CAAC;SAC7D;QACD,OAAO,YAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAE,CAAC;IACjC,CAAC,CAAC;IAsBK,yBAAc,GAAG,UAAC,GAAW;QAChC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,iEAA0D,OAAO,GAAG,CAAE,CAAC,CAAC;SAC3F;QAED,OAAO,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAC9D,CAAC;IAED;;;;OAIG;IACI,sBAAW,GAAG,UAAU,CAAS;QACpC,IAAM,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC,CAAC;IAWK,mBAAQ,GAAG,UAAU,CAAqB;QAC7C,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE;YAC/B,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAChC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACpB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACzB;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,UAAU,EAAE;YAC7C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAe,CAAC,CAAC;SACvC;QAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACvB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;gBAC5B,MAAM,IAAI,KAAK,CACX,qHAA8G,CAAC,CAAE,CACpH,CAAC;aACL;YACD,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SACjF;QAED,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACvB,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SACpC;QAED,IAAI,sCAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,IAAI,KAAK,CAAC,uDAAgD,CAAC,CAAE,CAAC,CAAC;aACxE;YACD,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,CAAC,OAAO,EAAE;YACX,4BAA4B;YAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,CAAC,QAAQ,EAAE;YACZ,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;SACpC;QAED,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC,CAAC;IAEF;;;OAGG;IACI,sBAAW,GAAG,UAAU,GAAW;QACtC,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC/B,OAAO,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC,CAAC;IACN,iBAAC;CAAA;AA5GsB;;;;;;;;;;;;;;;;;AClBU;AAEjC;IAAA;IA+BA,CAAC;IA9BU,eAAK,GAAZ,UAAa,MAAuC;QAChD,IAAM,QAAQ,GAAG,OAAO,MAAM,CAAC;QAC/B,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACvB,MAAM,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACjC;aAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE;YAC9B,IAAK,MAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;gBACzC,OAAO,MAAM,CAAC;aACjB;YACD,MAAM,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACjC;QACD,IAAI,yCAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACvB,OAAO,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SACrC;aACI;YACD,MAAM,IAAI,KAAK,CAAC,wBAAiB,MAAM,6BAA0B,CAAC,CAAC;SACtE;IACL,CAAC;IAEM,cAAI,GAAX,UAAY,MAAuC;QAC/C,IAAM,QAAQ,GAAG,OAAO,MAAM,CAAC;QAC/B,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACvB,IAAK,MAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;gBACzC,MAAM,GAAG,QAAQ,CAAC,MAAgB,EAAE,EAAE,CAAC,CAAC;aAC3C;SACJ;QACD,IAAI,CAAC,yCAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACxB,MAAM,GAAG,IAAI,yCAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACjC;QACD,OAAO,MAAuB,CAAC;IACnC,CAAC;IACL,gBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;AClCqC;AAGtC;IAII,qBAAY,IAAgB,EAAE,IAAK;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,2BAAK,GAAL;QACI,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,yBAAG,GAAH;QACI,OAAO;YACH,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI;SACR,CAAC;IAChB,CAAC;IAEO,6BAAO,GAAf,UAAgB,IAAI;QAChB,IAAI,MAAc,CAAC;QACnB,QAAQ,IAAI,CAAC,IAAI,EAAE;YACf,KAAK,8CAAU,CAAC,cAAc;gBAC1B,MAAM,GAAG,qBAAc,IAAI,qCAAkC,CAAC;gBAC9D,MAAM;YACV,KAAK,8CAAU,CAAC,aAAa;gBACzB,MAAM,GAAG,qBAAc,IAAI,oCAAiC,CAAC;gBAC7D,MAAM;YACV,KAAK,8CAAU,CAAC,gBAAgB;gBAC5B,MAAM,GAAG,+CAA+C,CAAC;gBACzD,MAAM;YACV,KAAK,8CAAU,CAAC,cAAc;gBAC1B,MAAM,GAAG,2DAAyD,CAAC;gBACnE,MAAM;YACV,KAAK,8CAAU,CAAC,qBAAqB;gBACjC,MAAM,GAAG,mDAAmD,CAAC;gBAC7D,MAAM;YACV,KAAK,8CAAU,CAAC,mBAAmB;gBAC/B,MAAM,GAAG,UAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,oCAAiC,CAAC;gBACrE,MAAM;YACV,KAAK,8CAAU,CAAC,kBAAkB;gBAC9B,MAAM,GAAG,iCAAiC,CAAC;gBAC3C,MAAM;YACV;gBACI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;oBACZ,IAAI,CAAC,IAAI,GAAG,8CAAU,CAAC,OAAO,CAAC;iBAClC;gBACD,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;gBACtB,MAAM;SACb;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IACL,kBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AClDM,IAAM,eAAe,GAAG,UAAa,QAAiG;IACzI,IAAM,OAAO,GAAwB,IAAI,OAAO,CAAC,QAAQ,CAAQ,CAAC;IAClE,IAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAChC,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF;IAEI,kBAAY,GAAI;QAMR,YAAO,GAEX,EAAE,CAAC;QAPH,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IACpB,CAAC;IAQD,qBAAE,GAAF,UAAG,KAAa,EAAE,EAAY;QAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;SAC5B;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,sBAAG,GAAH,UAAI,KAAa,EAAE,EAAY;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACrB,IAAI,EAAE,EAAE;gBACJ,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC9C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACxC;iBACI;gBACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;aAC5B;SACJ;IACL,CAAC;IAED,uBAAI,GAAJ,UAAK,KAAa;QAAlB,iBAQC;QARmB,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC,GAAG,CACd,MAAM,CAAC,GAAG,CAAC,YAAE;YACT,IAAM,MAAM,GAAG,EAAE,CAAC,IAAI,OAAP,EAAE,iBAAM,KAAI,CAAC,IAAI,GAAK,IAAI,SAAC,CAAC;YAC3C,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACpE,CAAC,CAAC,CACL,CAAC;IACN,CAAC;IAED,0BAAO,GAAP;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;IACL,eAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;AC5DD,IAAM,KAAK,GACP,CAAC;IACG,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,MAAM,EAAE;QAClC,OAAO,gEAA6B,CAAC;KACxC;IACD,OAAO,MAAM,CAAC,KAAK,CAAC;AACxB,CAAC,CAAC,EAAE,CAAC;AAGT;IAGI,qBAAY,MAAgD;QAAhD,kCAAuC,EAAS;QAF5D,YAAO,GAAG,EAAE,CAAC;QAGT,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC;YAClC,OAAO,EAAE,MAAM;SAClB,CAAC,CAAC,CAAC,MAAM,CAAC;QAEX,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;SACjC;IACL,CAAC;IAED,yBAAG,GAAH,UAAO,GAAQ,EAAE,KAAU;QAApB,8BAAQ;QAAE,kCAAU;QACvB,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACzC,GAAG,CAAC,aAAG,IAAI,iBAAG,kBAAkB,CAAC,GAAG,CAAC,cAAI,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAE,EAA9D,CAA8D,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEzF,OAAO,KAAK,CAAC,GAAG,EAAE;YACd,MAAM,EAAE,KAAK;YACb,OAAO,EAAE;gBACL,cAAc,EAAE,kBAAkB;gBAClC,QAAQ,EAAE,kBAAkB;aAC/B;SACJ,CAAC,CAAC,IAAI,CAAC,aAAG;YACP,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,0BAAI,GAAJ,UAAK,GAAQ,EAAE,IAAI;QAAd,8BAAQ;QACT,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;QAEzB,OAAO,KAAK,CAAC,GAAG,EAAE;YACd,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,kBAAkB;gBAClC,QAAQ,EAAE,kBAAkB;aAC/B;YACD,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;SAC3C,CAAC,CAAC,IAAI,CAAC,aAAG;YACP,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;IACP,CAAC;IACL,kBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnD6C;AACK;AACX;AAElB;AACM;AACH;AACD;AACM;AACD;AACE;AACH;AACa;AACZ;AACO;AACV;AACQ;AACF;AACF;AACI;AACI;AACP;AACN;AACD;AAEjB,IAAM,KAAK,GAAG;IACjB,SAAS,EAAE,iDAAS;IACpB,UAAU,EAAE,sDAAc;IAC1B,EAAE,EAAE,2DAAc;IAClB,kBAAkB,EAAE,MAAM;CAC7B,CAAC;;;;;;;;;;;;;;;;;AC9BgG;AAElG;IAAA;IA6CA,CAAC;IA5CG;;;OAGG;IACI,qBAAc,GAAG,UAAU,KAAa;QAC3C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC7B,IAAM,GAAG,GAAG,0DAAmD,KAAK,CAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACpB;IACL,CAAC,CAAC;IAEF;;;;OAIG;IACI,aAAM,GAAG,UAAU,CAAS,EAAE,IAAU;QAAV,iCAAU;QAC3C,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzB,QAAQ,IAAI,EAAE;YACV,KAAK,GAAG,CAAC,CAAC;gBACN,OAAO,MAAM,CAAC,IAAI,CAAC,uEAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACpC;YACD,KAAK,GAAG,CAAC,CAAC;gBACN,OAAO,MAAM,CAAC,IAAI,CAAC,uEAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/B;YACD,KAAK,GAAG,CAAC,CAAC;gBACN,OAAO,MAAM,CAAC,IAAI,CAAC,uEAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACpC;YACD,KAAK,GAAG,CAAC,CAAC;gBACN,OAAO,MAAM,CAAC,IAAI,CAAC,uEAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACpC;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,kCAA2B,IAAI,CAAE,CAAC,CAAC;aACtD;SACJ;IACL,CAAC,CAAC;IAEF;;;OAGG;IACI,gBAAS,GAAG,UAAU,CAAS;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC;IACN,aAAC;CAAA;AA7CkB;;;;;;;;;;;;;;;;;ACD0B;AAE7C;IAAA;IAiBA,CAAC;IAbG,0BAAS,GAAT,UAAU,KAAK;QACX,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1C,CAAC;IAED,oBAAG,GAAH;QAAI,iBAAU;aAAV,UAAU,EAAV,qBAAU,EAAV,IAAU;YAAV,4BAAU;;QACV,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,CAAC,GAAG,OAAX,OAAO,EAAQ,OAAO,EAAE;SAC3B;IACL,CAAC;IAED,sBAAK,GAAL,UAAM,IAAgB,EAAE,IAAK;QACzB,OAAO,IAAI,sDAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IACL,aAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;ACpBmC;AAGpC,IAAM,WAAW,GAAG,UAAC,QAA6B,EAAE,SAAmB;IACrE,IAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,GAAG,EAAE,KAAK;QACnC,OAAO,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC,CAAC;AAEK,SAAS,UAAU,CAAC,MAAa,EAAE,SAAmB,EAAE,MAAqC;IAArC,kCAA4B,EAAS;IAClG,IAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;IACnC,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,YAAY,CAAC;IAEvD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAM,eAAe,GAAuB;QAC1C,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAC/C,OAAO,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAa;YACxD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAEtC,OAAO,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;gBACnC,eAAe,EAAE,CAAC,CAAC,CAAC,iDAAc,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,eAAe,EAAE,CAAC;AAC3B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;AC1BM,IAAM,KAAK,GAAG;IAAC,aAAM;SAAN,UAAM,EAAN,qBAAM,EAAN,IAAM;QAAN,wBAAM;;IACxB,OAAO,MAAM,CAAC,MAAM,OAAb,MAAM,iBAAQ,EAAE,GAAK,GAAG,UAAE;AACrC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;ACFuC;AACP;AAClC,IAAM,IAAI,GAAG,2CAAM,CAAC,SAAS,CAAC;AAEqB;AAEnD;IAII,oBAAY,MAAW;QAAX,oCAAW;QACnB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC5C;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,KAAK,GAAG,EAAE,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CACvB,KAAK,CAAC,IAAI;QACN,2BAA2B;QAC3B,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EACzC,cAAM,8DAAK,CAAC,EAAE,CAAC,EAAT,CAAS,CAClB,CACJ,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,iCAAY,GAAZ,UAAa,KAAK;QACd,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,KAAK,CAAC;SAChB;QAGD,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACtC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAE3B,IAAM,IAAI,GAAG,+CAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;YAC9C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAyB,CAAC,CAAC,CAAC;SACnD;QAED,yBAAyB;QACzB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5B,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IAED,8BAAS,GAAT;QACI,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,8BAAS,GAAT;QACI,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,4BAAO,GAAP;QACI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,6BAAQ,GAAR,UAAS,IAAI;QACT,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,+CAAU,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBAChD,KAAK,GAAG,CAAC,CAAC;aACb;SACJ;QAED,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE;YAClC,IAAI,YAAY,UAAC;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;oBACjB,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC;iBAC5B;qBAAM;oBACH,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC;iBAC5B;gBACD,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC9B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;aAC5C;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,2BAAM,GAAN,UAAO,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK;QAC5B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;YAC1C,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,IAAI,GAAG,KAAK,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;gBACjB,IAAI,GAAG,IAAI,CAAC,+CAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAsB,CAAC,CAAC;aACrE;iBAAM;gBACH,IAAI,GAAG,IAAI,CAAC,+CAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAsB,CAAC,CAAC;aACrE;YAED,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;SACjC;QAED,OAAO,+CAAU,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IACL,iBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;AC9GM,IAAM,mBAAmB,GAAG;IAC/B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACnC,OAAO,EAAc,CAAC;AAC1B,CAAC,CAAC;;;;;;;;;;;;;;;;;ACHK,IAAM,cAAc,GAAG,UAAI,KAAM;IACpC,OAAO,OAAO,CAAC,OAAO,CAAI,KAAK,CAAC,CAAC;AACrC,CAAC,CAAC;AAEK,IAAM,UAAU,GAAG,UAAC,aAAa;IACpC,IAAM,aAAa,GAAG,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACtD,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,sBAAsB;IACtB,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;QAC/B,aAAa,CAAC,OAAO,CAAC,UAAC,OAAO;YAC1B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;iBACnB,IAAI,CAAC,OAAO,CAAC,CAAC,kDAAkD;iBAChE,KAAK,CAAC,UAAC,KAAK;gBACT,aAAa,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;gBAC/B,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;gBACtB,IAAI,OAAO,KAAK,aAAa,CAAC,MAAM,EAAE;oBAClC,8CAA8C;oBAC9C,MAAM,CAAC,aAAa,CAAC,CAAC;iBACzB;YACL,CAAC,CAAC,CAAC,CAAC,gDAAgD;QAC5D,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtByC;AACM;AACf;AACU;AACtB;AAEqB;AACK;AACA;AACM;AACO;AAE7D,yFAAyF;AACzF,6CAA6C;AAE7C;IAAA;IAwNA,CAAC;IAtNgB,4BAAkB,GAA/B,UACI,IAAoB,EACpB,WAAmB,EACnB,UAAkB,EAClB,QAAgB;;;;;;wBAEV,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;wBAGlE,aAAa,GAAa,EAAE,CAAC;wBAE7B,MAAM,GAAG,UAAU,CAAC;wBACpB,WAAW,GAAG,WAAW,GAAG,MAAM,CAAC;wBACrC,SAAS,GAAG,CAAC,CAAC;wBACd,UAAU,GAAG,QAAQ,GAAG,MAAM,CAAC;4CAE1B,KAAK;;;;;wCACJ,OAAO,GAAG,UAAC,EAAI,CAAC,eAAe,GAAG,KAAK,CAAC,EAAC;wCAGzC,SAAS,GAAG,SAAS,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;6CAE1C,YAAW,GAAG,SAAS,GAAvB,wBAAuB;wCAEjB,YAAY,GAAG,SAAS,GAAG,CAAC,CAAC;wCAET,qBAAM,OAAK,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,EAAE,MAAM,GAAG,SAAS,CAAC;;wCAA1F,iBAAiB,GAAG,SAAsE;wCAChG,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wCACtC,SAAS,GAAG,YAAY,CAAC;;;wCAMnB,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;wCAGhD,cAAc,GAAG,eAAe,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;6CACjD,WAAU,IAAI,SAAS,GAAvB,wBAAuB;wCAEjB,iBAAiB,GAAG,OAAK,iBAAiB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;wCACvE,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;;;wCAGhC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC;wCAG7D,gBAAgB,GAAG,cAAc,GAAG,aAAa,CAAC;wCAO7B,qBAAM,OAAK,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC;;wCAAhG,kBAAkB,GAAG,SAA2E;wCAGhG,cAAY,OAAK,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;wCAGxD,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAC,EAAI,gBAAgB,GAAE,EAAE,cAAM,4DAAU,CAAC,QAAQ,CAAC,WAAS,CAAC,EAA9B,CAA8B,CAAC,CAAC;wCACnG,MAAM,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC;wCACzB,iBAAiB,GAAG,IAAI,oDAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;wCAC3D,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;;;wCAE1C,UAAU,GAAG,aAAa,CAAC;;;;;;;wBAjD1B,KAAK,GAAG,CAAC;;;6BAAE,MAAK,GAAG,eAAe;sDAAlC,KAAK;;;;;wBAA+B,KAAK,IAAI,CAAC;;4BAqDvD,sBAAO,aAAa,CAAC,OAAO,EAAE,EAAC;;;;KAClC;IAEM,yBAAe,GAAtB,UAAuB,SAAyB,EAAE,UAAkB,EAAE,QAAgB,EAAE,WAAmB;QACvG,OAAO,SAAS,CAAC,kBAAkB,CAC/B,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAC/C,CAAC,IAAI,CAAC,eAAK;YACR,OAAO,qDAAU,CAAC,WAAW,CACzB,MAAM,CAAC,MAAM,CACT,KAAK,CAAC,GAAG,CAAC,WAAC;gBACP,OAAO,qDAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CACL,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,uBAAa,GAApB,UAAqB,MAAsB,EAAE,UAAkB,EAAE,QAAgB;QAC7E,OAAO,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAQ;YACzD,OAAO,qDAAU,CAAC,QAAQ,CAAC,YAAK,QAAQ,CAAE,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,KAAK,CAAC,WAAC;YACN,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,2BAAiB,GAAxB,UAAyB,CAAS,EAAE,MAAsB;QACtD,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,oEAAoE,CAAC;QACzF,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;QACtD,OAAO,2CAAM,CAAC,SAAS,CACnB,qDAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAE,CAAC,CAC5F,CAAC;IACN,CAAC;IAEM,yBAAe,GAAtB,UAAuB,OAA4B,EAAE,KAA4B,EAAE,IAAoB,EAAE,kBAA6B,EAAE,WAAmC;QAAlE,kEAA6B;QAClI,IAAM,eAAe,GAAG,qDAAU,CAAC,WAAW,CAAC,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;QACpF,IAAM,YAAY,GAAG,IAAI,kDAAI,EAAE,CAAC;QAChC,IAAI,cAA8C,CAAC;QACnD,IAAI,CAAC,WAAW,EAAE;YACd,IAAM,iBAAe,GAAG,EAAE,CAAC;YAC3B,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,YAAE;gBACzB,IAAI,EAAE,CAAC,eAAe,KAAK,eAAe,EAAE;oBACxC,yCAAyC;oBACzC,OAAO;iBACV;gBACD,iBAAe,CAAC,IAAI,CAChB,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,eAAe,CAAC,CACjD,CAAC;YACN,CAAC,CAAC,CAAC;YACH,cAAc,GAAG,wDAAU,CACvB,iBAAe,EACf,aAAG;gBACC,OAAO,GAAG,CAAC;YACf,CAAC,EACD;gBACI,WAAW,EAAE,kBAAkB;aAClC,CACJ,CAAC;SACL;aACI;YACD,cAAc,GAAG,iDAAc,CAAC,WAAW,CAAC,CAAC;SAChD;QAED,OAAO,cAAc,CAAC,IAAI,CAAC,kBAAQ;YAC/B,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CAAC,GAAG,CAAC,wBAAc;gBACvB,IAAM,IAAI,GAAG,iDAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;gBACzD,IAAM,UAAU,GAAG,SAAS,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;gBAC7D,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC9C,CAAC,CAAC,CACL,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,WAAC;YACL,OAAO,YAAY,CAAC,QAAQ,CAAC,iDAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACV,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;aACpD;YACD,oBAAoB;YACpB,IAAM,GAAG,GAAG;gBACR,SAAS,EAAE,qDAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;gBACjD,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,WAAC,IAAI,QAAC,CAAC,GAAG,EAAE,EAAP,CAAO,CAAC;gBAC3C,IAAI,EAAE,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,WAAW;gBAC/C,IAAI,EAAE,iDAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC;gBAC1C,KAAK,EAAE,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,iDAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;aAC1G,CAAC;YACF,OAAO,GAAG,CAAC;QACf,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,wBAAc,GAArB,UAAsB,OAA4B;QAC9C,IAAM,OAAO,GAAG,wCAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,CAAC;IAC3E,CAAC;IAED,oEAAoE;IACpE,gEAAgE;IAChE,4DAA4D;IAC5D,wDAAwD;IACxD,EAAE;IACF,+CAA+C;IAC/C,mFAAmF;IAC5E,4BAAkB,GAAzB,UAA0B,KAAK;QAC3B,OAAO,2CAAM,CAAC,SAAS,CACnB,MAAM,CAAC,MAAM,CAAC;YACV,yBAAyB;YACzB,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC;YAC1C,+DAAa,CAAC,qDAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACnD,qDAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,aAAa;SACjD,CAAC,CACL,CAAC;IACN,CAAC;IAEM,yBAAe,GAAtB,UAAuB,OAA4B;QAC/C,IAAI,WAAW,GAAG,iDAAU,CAAC;YACzB,qDAAU,CAAC,QAAQ,CACf,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAC1G;YACD,qDAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAC9C,qDAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;YACtC,oBAAoB;YACpB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,WAAC;gBACd,kCAAkC;gBAClC,OAAO;oBACH,qDAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;oBAC9B,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,qDAAU,CAAC,QAAQ,CAAC;oBACjC,qDAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,yBAAyB;iBACzD,CAAC;YACN,CAAC,CAAC;SACL,CAAC,CAAC;QACH,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YACnC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,qDAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;SACjF;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,sBAAY,GAAnB,UAAoB,MAAM;QACtB,MAAM,CAAC,UAAU,GAAG,wCAAS,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAQ,CAAC;QAC9D,IAAM,MAAM,GAAG,IAAI,sDAAM,CAAC;YACtB,KAAK,EAAE,qDAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,wDAAQ,CAAC,MAAM;SAClD,CAAC,CAAC;QACH,IAAM,SAAS,GAAG,0DAAW,CAAC,cAAc,CAAC,MAAM,EAAE;YACjD,MAAM,EAAE,MAAM;YACd,6BAA6B,EAAE,IAAI;SACtC,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;IACrB,CAAC;IACL,gBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;ACxOM,SAAS,OAAO,CAAC,GAAG,EAAE,IAAI;IAC7B,IAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChE,OAAO,UAAU,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,IAAI,IAAK,WAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAlB,CAAkB,EAAE,GAAG,CAAC,CAAC;AACtE,CAAC;;;;;;;;;;;;;;;;;;ACHqD;AAE/C,IAAM,WAAW,GAAG,UAAC,GAAW;IACnC,IAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAI,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QAC5B,GAAG,IAAI,GAAG,CAAC;KACd;IACD,GAAG,IAAI,SAAS,CAAC;IACjB,8CAAO,CAAC,OAAO,GAAG,IAAI,qDAAc,CAAC,GAAG,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEK,IAAM,gBAAgB,GAAG,UAAC,GAAW;IACxC,IAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAI,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QAC5B,GAAG,IAAI,GAAG,CAAC;KACd;IACD,GAAG,IAAI,YAAY,CAAC;IACpB,8CAAO,CAAC,YAAY,GAAG,IAAI,qDAAc,CAAC,GAAG,CAAC,CAAC;AACnD,CAAC,CAAC;;;;;;;;;;;;;;;;;;AClBqB;AAEvB;;GAEG;AACW;;;;;;;;;;;;;;;;;;;;;;;;;;ACJ6B;AAEpC,IAAM,GAAG,GAAG,UAAC,MAAM;IAAE,iBAAU;SAAV,UAAU,EAAV,qBAAU,EAAV,IAAU;QAAV,gCAAU;;IAClC,IAAM,cAAc,GAAY,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;IACrF,OAAO,cAAc,CAAC,KAAK,OAApB,cAAc,iBAAO,mDAAa,GAAK,OAAO,UAAE;AAC3D,CAAC,CAAC;;;;;;;;;;;;;;;;;;;ACJoC;AACJ;AACP;AAE3B,IAAM,mBAAmB,GAAG;IACxB,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;IACT,QAAQ,EAAE,MAAM;IAChB,GAAG,EAAE,OAAO;IACZ,KAAK,EAAE,OAAO;IACd,KAAK,EAAE,OAAO;IACd,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;CAChB,CAAC;AAEF;IAAA;QAQI,WAAM,GAAG,IAAI,2CAAM,EAAE,CAAC;IA+D1B,CAAC;IA5DG,kCAAI,GAAJ,UAAK,MAAyB;QAC1B,MAAM,GAAG,MAAM,IAAI,EAAS,CAAC;QAC7B,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,IAAI,EAAS,CAAC;QACvE,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,EAAS,CAAC;QACrE,IAAI,CAAC,MAAM,GAAG,MAAa,CAAC;QAE5B,2BAA2B;QAC3B,IAAM,UAAU,GAAG,oCAAK,CAAC,UAAU,CAAC;QAEpC,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC5C;QAED,IAAI,oCAAK,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,UAAU,GAAG,oCAAK,CAAC,kBAAkB,CAAC;SAC9C;QAED,IAAI,CAAC,MAAM,GAAG,IAAK,UAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3E,IAAI,CAAC,KAAK,GAAG,IAAK,UAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAElC,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC/B,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU;YAC9B,IAAI,8CAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAC3C,OAAO,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,aAAG;YAC9B,MAAM,IAAI,KAAK,CAAC,kBAAW,OAAO,gBAAM,OAAO,sBAAmB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACP,CAAC;IAED,oCAAM,GAAN,UAAO,IAAY,EAAE,IAAa;QAC9B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,uCAAS,GAAT,UAAU,IAAY;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,sBAAI,oDAAmB;aAAvB;YACI,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC;;;OAAA;IAED,sBAAI,iDAAgB;aAApB;YACI,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC/C,CAAC;;;OAAA;IAED,sBAAI,mDAAkB;aAAtB;YACI,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC;;;OAAA;IAED,sBAAI,+CAAc;aAAlB;YACI,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC;;;OAAA;IAED,gDAAkB,GAAlB,UAAmB,OAAe;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,UAAG,mBAAmB,CAAC,OAAO,CAAC,qBAAkB,CAAC,CAAC;IAC7E,CAAC;IAEL,0BAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;ACzFuC;AAEF;AAGtC;IAAA;QAEI,WAAM,GAA2C,IAAI,kDAAmB,EAAE,CAAC;IA6E/E,CAAC;IAxEG;;;;;;OAMG;IACH,8CAAkB,GAAlB,UAAmB,MAAc;QAAjC,iBAYC;QAXG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAClF,MAAM,EAAE,IAAI,CACf,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACX,OAAO,8CAAO,CAAC,YAAY,CAAC,2BAA2B,CACnD,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAC1B,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CACzB,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,OAAO,CAAC,eAAe,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,8CAAkB,GAAlB,UAAmB,MAAc;QAAjC,iBAYC;QAXG,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CACnF,MAAM,EAAE,KAAK,CAChB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACX,OAAO,8CAAO,CAAC,YAAY,CAAC,2BAA2B,CACnD,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAC1B,MAAM,CAAC,CAAC,CAAC,EACT,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CACzB,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,OAAO,CAAC,eAAe,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,uCAAW,GAAX,UAAY,MAAc;QAA1B,iBAMC;QALG,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,CACnC,MAAM,EAAE,IAAI,CACf,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,oCAAQ,GAAR,UAAS,MAAc;QAAvB,iBAMC;QALG,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,CACnC,MAAM,EAAE,KAAK,CAChB,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACP,CAAC;IAEL,wBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;ACnFqC;AAC6B;AAoCnE;IAII,oBAAY,MAA8C;QAFlD,iBAAY,GAAG,oEAAoE,CAAC;QAGxF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAEO,uCAAkB,GAA1B,UAA2B,IAAY,EAAE,QAAiB;QACtD,IAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACnE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,aAAG;YAC9D,IAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,eAAK,IAAI,YAAK,CAAC,IAAI,KAAK,aAAa,EAA5B,CAA4B,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;aACvC;YACD,IAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC5D,YAAQ,GAA+G,WAAW,GAA1H,EAAE,aAAa,GAAgG,WAAW,GAA3G,EAAE,kBAAkB,GAA4E,WAAW,GAAvF,EAAE,kBAAkB,GAAwD,WAAW,GAAnE,EAAE,kBAAkB,GAAoC,WAAW,GAA/C,EAAE,MAAM,GAA4B,WAAW,GAAvC,EAAE,QAAQ,GAAkB,WAAW,GAA7B,EAAE,YAAY,GAAI,WAAW,GAAf,CAAgB;YAC1I,OAAO;gBACH,QAAQ;gBACR,aAAa;gBACb,kBAAkB;gBAClB,kBAAkB;gBAClB,kBAAkB;gBAClB,MAAM;gBACN,QAAQ,EAAE,QAAQ,IAAI,IAAI;gBAC1B,YAAY;aACK,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,sCAAiB,GAAzB,UAA0B,eAAuB,EAAE,QAAiB;QAApE,iBAYC;QAXG,IAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACnE,OAAO,MAAM,CAAC,qBAAqB,CAAC,eAAe,CAAC;aAC/C,IAAI,CAAC,iBAAO;YACT,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,aAAG,IAAI,UAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,KAAI,CAAC,YAAY,EAAjD,CAAiD,CAAC,CAAC;YAC3F,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;aAC/C;YAED,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,KAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,8BAAS,GAAjB,UAAkB,SAAiB,EAAE,YAAoB;QACrD,OAAO,8CAAO,CAAC,YAAY,CAAC,sBAAsB,CAC9C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAC3B,SAAS,EACT,YAAY,CACf,CAAC,IAAI,CAAC,eAAK;YACR,OAAO,KAAqB,CAAC;QACjC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAC;YACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,qCAAgB,GAAhB,UAAiB,eAAuB,EAAE,QAAiB;QACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,uCAAkB,GAAlB,UAAmB,UAAkB,EAAE,WAAmB,EAAE,eAAuB;QAC/E,IAAI,MAAM,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;YACvC,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,yDAA0B,CAAC;SAC1D;aAAM;YACH,OAAO,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,UAAC,EAAI,EAAE,EAAC,CAAC;SACrE;IACL,CAAC;IAED,yCAAoB,GAApB,UAAqB,eAAuB,EAAE,QAAiB,EAAE,SAAiB;QAAlF,iBA0BC;QAzBG,OAAO,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAI;YAE1D,iBAAa,GAMI,IAAI,cANR,EACb,kBAAkB,GAKD,IAAI,mBALH,EAClB,kBAAkB,GAID,IAAI,mBAJH,EAClB,kBAAkB,GAGD,IAAI,mBAHH,EAClB,MAAM,GAEW,IAAI,OAFf,EACN,QAAQ,GACS,IAAI,SADb,EACR,YAAY,GAAK,IAAI,aAAT,CAAU;YAC1B,OAAO,KAAI,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,eAAK;gBACrD,IAAM,OAAO,GAAG,EAAmB,CAAC;gBACpC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC;gBACtC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC,mBAAmB,CAAC;gBACnD,OAAO,CAAC,WAAW,GAAG,KAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,kBAAkB,EAAE,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACtG,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC,cAAc,CAAC;gBAC/C,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC,gBAAgB,CAAC;gBAChD,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;gBACtC,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;gBAChD,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;gBAChD,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;gBAChD,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;gBACxB,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAC5B,OAAO,OAAO,CAAC;YACnB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IACL,iBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrI8C;AAEuB;AAC3B;AAGuJ;AAEvI;AAE3D;IAA2B,yBAAU;IAEjC,eACI,YAAoB,EACpB,QAAiB,EACjB,oBAAoB,EACpB,MAA+C,EAC/C,YAAmC;QALvC,YAOI,kBAAM;YACF,QAAQ;YACR,OAAO,EAAE,YAAY;YACrB,oBAAoB;YACpB,IAAI,EAAE,OAAO;YACb,UAAU,EAAE,OAAO;SACtB,EAAE,MAAM,EAAE,YAAY,CAAC,SAQ3B;QAPG,IAAI,oBAAoB,EAAE;YACtB,KAAI,CAAC,aAAa,GAAG,IAAI,oEAAkB,CACvC,KAAI,CAAC,MAAM,EACX,oBAAoB,EACpB,QAAQ,CACX,CAAC;SACL;;IACL,CAAC;IAED;;;;;OAKG;IACH,gCAAgB,GAAhB;QACI,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAClF,OAAO,MAAM,CAAC,eAAe,CAAC;IAClC,CAAC;IAED,4BAAY,GAAZ;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,2CAAY,CAAC;IACvD,CAAC;IAED;;;;;;;OAOG;IACH,0BAAU,GAAV,UAAW,WAAmB,EAAE,MAA2B;QAA3D,iBAcC;QAbG,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACrB,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YACpF,OAAO,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;SACzC;aAAM;YACH,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;gBACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,WAAW,EACX,WAAW,CACd,CAAC;gBACF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,MAAM,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;SACN;IAEL,CAAC;IAED;;;;;OAKG;IACH,gCAAgB,GAAhB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACrB,OAAO,KAAK,CAAC;SAChB;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAElF,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;aACvD,IAAI,CAAC,mBAAS;YACX,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,2CAAY,CAAC;QACzC,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;;;;;OAOG;IACH,4BAAY,GAAZ,UAAa,WAAmB,EAAE,MAAwC;QAA1E,iBAYC;QAZiC,oCAAwC;QACtE,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QACvC,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE/F,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,WAAW,EACX,WAAW,EACX,cAAc,CACjB,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,uBAAO,GAAP,UAAQ,MAAmB,EAAE,MAAsC;QAAnE,iBAYC;QAZ4B,oCAAsC;QAC/D,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAClC,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE/F,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,SAAS,EACT,cAAc,EACd,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAC1B,CAAC;YACF,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,0BAAU,GAAV,UAAW,MAAsC;QAAtC,oCAAsC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC,OAAO,CACf,yCAAU,EACV,MAAM,CACT,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACH,uBAAO,GAAP,UAAQ,MAAmB,EAAE,WAAmB,EAAE,MAAqC;QAAvF,iBAyBC;QAzBiD,oCAAqC;QACnF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC;QAC7C,IAAM,yBAAyB,GAAG,MAAM,CAAC,yBAAyB,IAAI,IAAI,CAAC;QAE3E,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACrB,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;YAC9C,OAAO,KAAI,CAAC,YAAY,CAAC,WAAW,CAChC,SAAS,EACT,WAAW,EACX,WAAW,EACX,KAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,yBAAyB,EACzB,UAAU,EACV,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,8BAAc,GAAd,UAAe,MAAmB,EAAE,WAAmB,EAAE,YAAyB,EAAE,MAAqC;QAArC,oCAAqC;QACrH,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAE7B,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC7C,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CACzC,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,WAAW,EACX,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,EACpD,MAAM,CAAC,CAAC,EACR,MAAM,CAAC,CAAC,EACR,MAAM,CAAC,CAAC,EACR,MAAM,CACT,CAAC;SACL;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,CACnC,IAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,WAAW,EACX,MAAM,CACT,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACH,oCAAoB,GAApB,UAAqB,MAAmB,EAAE,WAAmB,EAAE,YAAyB,EAAE,MAAqC;QAA/H,iBAyBC;QAzByF,oCAAqC;QAC3H,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAExC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAE7C,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,IAAI,CACjF,yBAAe;YACX,OAAO,KAAI,CAAC,YAAY,CAAC,oBAAoB,CACzC,KAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,WAAW,EACX,WAAW,EACX,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,EACpD,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,EACjB,MAAM,CACT,CAAC;QACN,CAAC,CACJ,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACH,iCAAiB,GAAjB,UAAkB,MAAmB,EAAE,WAAmB,EAAE,MAAsC;QAAlG,iBAwBC;QAxB2D,oCAAsC;QAC9F,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;QAE5C,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,IAAM,yBAAyB,GAAG,MAAM,CAAC,yBAAyB,IAAI,IAAI,CAAC;QAE3E,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAU;YAC1D,OAAO,KAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;gBAC9C,OAAO,KAAI,CAAC,YAAY,CAAC,WAAW,CAChC,SAAS,EACT,WAAW,EACX,WAAW,EACX,KAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,yBAAyB,EACzB,UAAU,EACV,MAAM,CACT,CAAC;YACN,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,kCAAkB,GAAlB,UAAmB,MAAmB,EAAE,WAAmB,EAAE,yBAAgC;QAAhC,4EAAgC;QACzF,kDAAkD;QAClD,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;QAC/C,gDAAgD;QAChD,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,yBAAyB,CAAC,CAAC;IAC1F,CAAC;IAED;;;;;;OAMG;IACH,uCAAuB,GAAvB,UAAwB,eAAuB,EAAE,MAA2B;QAA5E,iBAsBC;QArBG,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;YAC/C,OAAO,KAAI,CAAC,UAAU,CAAC,oBAAoB,CACvC,eAAe,EAAE,IAAI,EAAE,SAAS,CACnC,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,KAAI,CAAC,WAAW,CAAC,YAAY,CAChC,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,QAAQ,EAChB,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAGD;;;;;;;OAOG;IACH,4BAAY,GAAZ,UAAa,eAAuB,EAAE,MAA2B;QAAjE,iBAsBC;QArBG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;YAC/C,OAAO,KAAI,CAAC,UAAU,CAAC,oBAAoB,CACvC,eAAe,EAAE,IAAI,EAAE,SAAS,CACnC,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,KAAI,CAAC,WAAW,CAAC,UAAU,CAC9B,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,QAAQ,EAChB,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,wBAAQ,GAAR,UAAS,MAAmB,EAAE,WAAmB,EAAE,MAAqC;QAAxF,iBAyBC;QAzBkD,oCAAqC;QACpF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC/B,IAAM,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC;QAC7C,IAAM,yBAAyB,GAAG,MAAM,CAAC,yBAAyB,IAAI,IAAI,CAAC;QAE3E,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACrB,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAC1C;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;YAC/C,OAAO,KAAI,CAAC,WAAW,CAAC,WAAW,CAC/B,SAAS,EACT,WAAW,EACX,WAAW,EACX,KAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,yBAAyB,EACzB,UAAU,EACV,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,mCAAmB,GAAnB,UAAoB,MAAmB,EAAE,WAAmB,EAAE,yBAAgC;QAAhC,4EAAgC;QAC1F,kDAAkD;QAClD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAC1C,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;QAC/C,gDAAgD;QAChD,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,yBAAyB,CAAC,CAAC;IAC1F,CAAC;IAED;;;;;;OAMG;IACH,uCAAuB,GAAvB,UAAwB,mBAA2B,EAAE,MAA2B;QAAhF,iBAsBC;QArBG,IAAI,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;YAC9C,OAAO,KAAI,CAAC,UAAU,CAAC,oBAAoB,CACvC,mBAAmB,EAAE,KAAK,EAAE,SAAS,CACxC,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,KAAI,CAAC,YAAY,CAAC,YAAY,CACjC,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,QAAQ,EAChB,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,kCAAkB,GAAlB,UAAmB,MAAmB,EAAE,WAAmB,EAAE,MAAsC;QAAnG,iBAuBC;QAvB4D,oCAAsC;QAC/F,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAE/B,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,IAAM,yBAAyB,GAAG,MAAM,CAAC,yBAAyB,IAAI,IAAI,CAAC;QAE3E,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,oBAAU;YAC1D,OAAO,KAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;gBAC/C,OAAO,KAAI,CAAC,WAAW,CAAC,WAAW,CAC/B,SAAS,EACT,WAAW,EACX,WAAW,EACX,KAAI,CAAC,aAAa,CAAC,OAAO,EAC1B,yBAAyB,EACzB,UAAU,EACV,MAAM,CACT,CAAC;YACN,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,4BAAY,GAAZ,UAAa,mBAA2B,EAAE,MAA2B;QAArE,iBAsBC;QArBG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,mBAAS;YAC9C,OAAO,KAAI,CAAC,UAAU,CAAC,oBAAoB,CACvC,mBAAmB,EAAE,KAAK,EAAE,SAAS,CACxC,CAAC;QACN,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAO;YACX,OAAO,KAAI,CAAC,YAAY,CAAC,UAAU,CAC/B,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,kBAAkB,EAC1B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,QAAQ,EAChB,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACH,wBAAQ,GAAR,UAAS,MAAmB,EAAE,EAAU,EAAE,MAA+B;QAA/B,oCAA+B;QACrE,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,2CAAY,EAAE;YAC7C,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,GAAG,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACvC,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACvC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;;;;OAKG;IACK,yBAAS,GAAjB;QAAA,iBAqCC;QApCG,IAAI,QAAsB,CAAC;QAC3B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,0BAAgB;YAC3C,QAAQ,GAAG,gBAAgB,CAAC;YAC5B,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAC1B,iBAAiB,CACpB,CAAC;YACF,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAc;YAClB,QAAQ,cAAc,EAAE;gBACpB,KAAK,kDAAmB,CAAC,CAAC;oBACtB,OAAO,qCAAM,CAAC,GAAG,CAAC;iBACrB;gBACD,KAAK,uDAAwB,CAAC,CAAC;oBAC3B,IAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;oBAC3D,IAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;oBAC/D,OAAO,kDAAU,CAAC,CAAC,KAAI,CAAC,WAAW,CAAS,eAAe,CAAC,EAAE,KAAI,CAAC,WAAW,CAAS,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAC5G,UAAC,cAAc;wBACX,QAAQ,cAAc,EAAE;4BACpB,KAAK,uDAAwB,CAAC,CAAC;gCAC3B,OAAO,qCAAM,CAAC,QAAQ,CAAC;6BAC1B;4BACD,KAAK,sDAAuB,CAAC,CAAC;gCAC1B,OAAO,qCAAM,CAAC,OAAO,CAAC;6BACzB;4BACD,OAAO,CAAC,CAAC;gCACL,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAgC,cAAc,CAAE,CAAC,CAAC,CAAC;6BACtF;yBACJ;oBACL,CAAC,CACJ,CAAC;iBACL;gBACD,OAAO,CAAC,CAAC;oBACL,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,uCAAgC,cAAc,CAAE,CAAC,CAAC,CAAC;iBACtF;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,6BAAa,GAArB,UAAsB,UAAkB,EAAE,OAAe,EAAE,OAAe,EAAE,IAAY,EAAE,KAAa,EAAE,cAAsB,EAAE,MAAc;QAC3I,IAAM,SAAS,GAAG;YACd,KAAK,EAAE;gBACH,YAAY,EAAE;oBACV,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAChC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACnC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;oBACpC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;iBACjD;gBACD,MAAM,EAAE,EAAE;aACb;YACD,WAAW,EAAE,QAAQ;YACrB,MAAM,EAAE;gBACJ,IAAI;gBACJ,OAAO,EAAE,GAAG;gBACZ,OAAO;gBACP,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO;aAChD;YACD,OAAO,EAAE,EAAE;SACd,CAAC;QACF,QAAQ,UAAU,EAAE;YAChB,KAAK,qCAAM,CAAC,GAAG;gBACX,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG;oBACrB,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;oBACnC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;oBACpC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;oBAClC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE;oBACnC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE;iBACpC,CAAC;gBACF,SAAS,CAAC,OAAO,GAAG;oBAChB,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE,cAAc;oBACvB,KAAK;oBACL,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC;oBACjD,OAAO,EAAE,IAAI;iBAChB,CAAC;YACN,KAAK,qCAAM,CAAC,QAAQ,CAAC;YACrB,KAAK,qCAAM,CAAC,OAAO;gBAEf,IAAI,UAAU,KAAK,qCAAM,CAAC,OAAO,EAAE;oBAC/B,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG;wBAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAChC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;wBACpC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;qBACjD,CAAC;oBACF,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC;iBACnC;gBACD,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG;oBACrB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;oBAClC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;oBACpC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;oBAClC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;oBAClC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE;iBACxC,CAAC;gBACF,SAAS,CAAC,OAAO,GAAG;oBAChB,KAAK,EAAE,OAAO;oBACd,OAAO,EAAE,cAAc;oBACvB,KAAK,EAAE,MAAM;oBACb,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC;iBACtD,CAAC;SACT;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACK,uCAAuB,GAA/B,UAAgC,MAAsB,EAAE,SAAiB;QACrE,IAAI,CAAC,6DAAW,CAAC,SAAS,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CACX,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,8BAA8B,CAAC,CACpE,CAAC;SACL;QAED,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;YAChC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACtC;QAED,IAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACjC,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAQ,CAAC,EAAE;YAC9B,CAAC,IAAI,EAAE,CAAC;SACX;QACD,OAAO;YACH,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;SACP,CAAC;IACN,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,yCAAyB,GAAjC,UAAkC,QAAsB,EAAE,UAAkB,EAAE,eAAoB,EAAE,cAAsB,EAAE,OAAe,EAAE,KAAa,EAAE,MAAc;QAC9J,KAAC,GAAW,eAAe,EAA1B,EAAE,CAAC,GAAQ,eAAe,EAAvB,EAAE,CAAC,GAAK,eAAe,EAApB,CAAqB;QACpC,IAAI,MAA0B,CAAC;QAC/B,QAAQ,UAAU,EAAE;YAChB,KAAK,qCAAM,CAAC,GAAG;gBACX,MAAM,GAAG,QAAQ,CAAC,MAAM,CACpB,QAAQ,EACR,OAAO,EACP,cAAc,EACd,KAAK,EACL,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,EACzC,IAAI,EACJ,CAAC,EACD,CAAC,EACD,CAAC,CACJ,CAAC;gBACF,MAAM;YAEV,KAAK,qCAAM,CAAC,QAAQ,CAAC;YACrB,KAAK,qCAAM,CAAC,OAAO;gBACf,MAAM,GAAG,QAAQ,CAAC,MAAM,CACpB,QAAQ,EACR,OAAO,EACP,cAAc,EACd,MAAM,EACN,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,EACzC,CAAC,EACD,CAAC,EACD,CAAC,CACJ,CAAC;gBACF,MAAM;SACb;QACD,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAEO,yCAAyB,GAAjC,UAAkC,MAAmB,EAAE,cAAsB;QAA7E,iBA8BC;QA7BG,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QACpF,IAAI,OAAe,CAAC;QACpB,IAAI,OAAe,CAAC;QACpB,IAAI,UAAkB,CAAC;QACvB,IAAI,QAAsB,CAAC;QAC3B,IAAI,KAAa,CAAC;QAElB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACtK,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACvB,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACvD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,KAAI,CAAC,WAAW,CAAS,UAAU,CAAC,EAAE,KAAI,CAAC,WAAW,CAAS,WAAW,CAAC,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC,IAAI,CAAC,cAAI;YACR,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,KAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAS;YACb,OAAO,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAS;YACb,OAAO,KAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACK,8BAAc,GAAtB,UAAuB,MAAmB,EAAE,cAAsB;QAAlE,iBAkCC;QAhCG,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACnD,CAAC,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EACzB,CAAC,SAAS,CAAC,CACd,CAAC;QAEF,IAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QACpF,IAAI,OAAe,CAAC;QACpB,IAAI,OAAe,CAAC;QACpB,IAAI,UAAkB,CAAC;QACvB,IAAI,QAAsB,CAAC;QAC3B,IAAI,KAAa,CAAC;QAElB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAM;YACtK,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACpB,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACvB,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACvD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,KAAI,CAAC,WAAW,CAAS,UAAU,CAAC,EAAE,KAAI,CAAC,WAAW,CAAS,WAAW,CAAC,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC,IAAI,CAAC,cAAI;YACR,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAChB,OAAO,KAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAS;YACb,OAAO,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAS;YACb,IAAM,mBAAmB,GAAG,KAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC5E,OAAO,KAAI,CAAC,yBAAyB,CACjC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CACzF,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,6BAAa,GAAb,UAAc,MAAmB,EAAE,MAAsC;QAAtC,oCAAsC;QACrE,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAExC,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE/F,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IACvD,CAAC;IACL,YAAC;AAAD,CAAC,CA9zB0B,oDAAU,GA8zBpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACx0B+B;AACa;AACF;AACE;AAEG;AACM;AACP;AAEhB;AACD;AACE;AAEhC;IAAiC,+BAAiB;IAAlD;;IAgFA,CAAC;IA5EG,0BAAI,GAAJ,UAAK,MAA0B;QAA/B,iBA8CC;QA7CG,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAC;YAC7B,IAAM,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;YACrD,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YAC7C,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM,CAClC;gBACI,YAAY,EAAE,kBAAkB,CAAC,uBAAuB;gBACxD,WAAW,EAAE,cAAc,CAAC,kBAAkB;gBAC9C,YAAY,EAAE,kBAAkB,CAAC,YAAY;aAC1B,EACvB,MAAM,CACT,CAAC;YAEF,KAAI,CAAC,eAAe,GAAG,IAAI,sDAAW,CAClC,KAAI,CAAC,MAAM,EACX,MAAM,CAAC,YAAY,EACnB,IAAI,CACP,CAAC;YAEF,KAAI,CAAC,gBAAgB,GAAG,IAAI,sDAAW,CACnC,KAAI,CAAC,MAAM,EACX,MAAM,CAAC,WAAW,EAClB,KAAK,CACR,CAAC;YAEF,KAAI,CAAC,YAAY,GAAG,IAAI,wDAAY,CAChC,KAAI,CAAC,MAAM,EACX,MAAM,CAAC,YAAY,CACtB,CAAC;YAEF,KAAI,CAAC,UAAU,GAAG,IAAI,oDAAU,CAC5B,KAAI,CAAC,MAAM,CACd,CAAC;YAEF,IAAI,CAAC,8CAAO,CAAC,YAAY,EAAE;gBACvB,IAAI,2CAAS,CAAC,kBAAkB,CAAC,2CAAS,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;oBAC/E,2CAAS,CAAC,kBAAkB,IAAI,GAAG,CAAC;iBACvC;gBACD,2CAAS,CAAC,kBAAkB,IAAI,YAAY,CAAC;gBAC7C,8CAAO,CAAC,YAAY,GAAG,IAAI,qDAAc,CAAC,2CAAS,CAAC,kBAAkB,CAAC,CAAC;aAC3E;YAED,OAAO,KAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;OASG;IACH,2BAAK,GAAL,UAAM,YAAoB,EAAE,QAAkB,EAAE,oBAA6B;QACzE,OAAO,IAAI,yCAAK,CACZ,YAAY,EACZ,QAAQ,EACR,oBAAoB,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAChC,CAAC;IACN,CAAC;IAEO,mCAAa,GAArB;QACI,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,eAAe;YAClC,WAAW,EAAE,IAAI,CAAC,gBAAgB;YAClC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,YAAY,EAAE,IAAI,CAAC,YAAY;SACf,CAAC;IACzB,CAAC;IACL,kBAAC;AAAD,CAAC,CAhFgC,qDAAiB,GAgFjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7FoF;AAIrF;IAAiC,+BAA6B;IAI1D,qBAAY,OAAgD,EAAE,OAAe,EAAE,QAAiB;eAC5F,kBAAM;YACF,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,oBAAoB;YAC1B,UAAU,EAAE,OAAO;YACnB,QAAQ,EAAE,QAAQ;SACrB,EAAE,OAAO,CAAC;IACf,CAAC;IAED,4BAAM,GAAN,UAAO,UAAkB;QAAE,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,OAAO,QAAQ,CAAC,MAAM,OAAf,QAAQ,iBAAQ,UAAU,GAAK,IAAI,UAAE;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,iCAAW,GAAX,UACI,kBAA0B,EAC1B,kBAA0B,EAC1B,MAAmB,EACnB,KAAa,EACb,yBAAkC,EAClC,UAAiB,EACjB,MAA2B;QAP/B,iBAoBC;QAdG,8CAAiB;QAGjB,OAAO,IAAI,CAAC,MAAM,CACd,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EAClB,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EACvB,KAAK,EACL,yBAAyB,EACzB,UAAU,CACb,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,gCAAU,GAAV,UACI,QAAkB,EAClB,cAAwB,EACxB,WAAmB,EACnB,eAAuB,EACvB,cAAsB,EACtB,aAAqB,EACrB,kBAA0B,EAC1B,kBAA0B,EAC1B,kBAA0B,EAC1B,MAAmB,EACnB,QAAgB,EAChB,MAA0B;QAZ9B,iBAiCC;QAnBG,OAAO,IAAI,CAAC,MAAM,CACd,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,WAAW,EACX,eAAe,EACf,cAAc,EACd,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,MAAM,EACN,QAAQ,CACX,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CACpB,MAAM,EACN,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;OAUG;IACH,mCAAa,GAAb,UACI,kBAA0B,EAC1B,kBAA0B,EAC1B,yBAAkC,EAClC,UAAiB,EACjB,MAA2B;QAL/B,iBAgBC;QAZG,8CAAiB;QAGjB,OAAO,IAAI,CAAC,MAAM,CACd,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,yBAAyB,EACzB,UAAU,CACb,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,kCAAY,GAAZ,UACI,QAAkB,EAClB,cAAwB,EACxB,WAAmB,EACnB,eAAuB,EACvB,cAAsB,EACtB,aAAqB,EACrB,kBAA0B,EAC1B,kBAA0B,EAC1B,kBAA0B,EAC1B,MAAmB,EACnB,QAAgB,EAChB,MAA0B;QAZ9B,iBAgCC;QAnBG,OAAO,IAAI,CAAC,MAAM,CACd,cAAc,EACd,QAAQ,EACR,cAAc,EACd,WAAW,EACX,eAAe,EACf,cAAc,EACd,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EAClB,MAAM,EACN,QAAQ,CACX,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CACpB,MAAM,EACN,MAAM,CACT,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,wCAAkB,GAAlB,UACI,aAAqB,EACrB,kBAA0B;QAF9B,iBASC;QALG,OAAO,IAAI,CAAC,MAAM,CACd,wBAAwB,EAAE,aAAa,EAAE,kBAAkB,CAC9D,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,+BAAS,GAAT,UACI,KAAa,EACb,mBAA2B;QAF/B,iBASC;QALG,OAAO,IAAI,CAAC,MAAM,CACd,WAAW,EAAE,KAAK,EAAE,mBAAmB,CAC1C,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,kDAA4B,GAA5B,UACI,aAAqB,EACrB,kBAA0B;QAF9B,iBASC;QALG,OAAO,IAAI,CAAC,MAAM,CACd,6BAA6B,EAAE,aAAa,EAAE,kBAAkB,CACnE,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACH,wCAAkB,GAAlB,UAAmB,YAAoB;QAAvC,iBAMC;QALG,OAAO,IAAI,CAAC,MAAM,CACd,yBAAyB,EAAE,YAAY,CAC1C,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,WAAW,CAAmB,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACH,+BAAS,GAAT;QAAA,iBAYC;QAXG,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,sDAAc,CAAS,IAAI,CAAC,UAAiB,CAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC,MAAM,CACd,WAAW,CACd,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,WAAW,CAAS,MAAM,CAAC,CAAC,IAAI,CAAC,UAAC,SAAS;gBACnD,KAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,OAAO,SAAS,CAAC;YACrB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEL,kBAAC;AAAD,CAAC,CAzRgC,6CAAS,GAyRzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5RoE;AAGrE;;;GAGG;AACH;IAAwC,sCAA6B;IAEnE,4BAAY,OAAgD,EAAE,OAAe,EAAE,QAAiB;eAC9F,kBACE;YACE,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,oBAAoB;YAC1B,UAAU,EAAE,OAAO;YACnB,QAAQ,EAAE,QAAQ,EAAE,wDAAwD;SAC7E,EACD,OAAO,CACR;IACH,CAAC;IAED,mCAAM,GAAN,UAAO,UAAkB;QAAE,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QAChC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACrC,OAAO,QAAQ,CAAC,MAAM,OAAf,QAAQ,iBAAQ,UAAU,GAAK,IAAI,UAAE;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACH,wCAAW,GAAX,UACE,SAAiB,EACjB,MAAmB,EACnB,yBAAmC,EACnC,MAA2B;QAJ7B,iBAWC;QALC,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,6CAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,yBAAyB,CAAC,CAAC,IAAI,CACnG,gBAAM;YACJ,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC,CACF,CAAC;IACJ,CAAC;IACH,yBAAC;AAAD,CAAC,CA1CuC,6CAAS,GA0ChD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClDyD;AAI1D;IAAgC,8BAA6B;IAEzD,oBACI,aAAiC,EACjC,MAA+C,EACrC,iBAAwC;QAHtD,YAKI,kBAAM,aAAa,EAAE,MAAM,CAAC,SAC/B;QAHa,uBAAiB,GAAjB,iBAAiB,CAAuB;;IAGtD,CAAC;IAED,sBAAc,oCAAY;aAA1B;YACI,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC;QACjD,CAAC;;;OAAA;IAED,sBAAc,oCAAY;aAA1B;YACI,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,YAAY,CAAC;QACjD,CAAC;;;OAAA;IAED,sBAAc,mCAAW;aAAzB;YACI,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,WAAW,CAAC;QAChD,CAAC;;;OAAA;IAED,sBAAc,kCAAU;aAAxB;YACI,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,UAAU,CAAC;QAC/C,CAAC;;;OAAA;IAEL,iBAAC;AAAD,CAAC,CA1B+B,6CAAS,GA0BxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9ByD;AAG1D;IAAkC,gCAA6B;IAE3D,sBAAY,OAAgD,EAAE,OAAe;eACzE,kBAAM;YACF,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE,cAAc;YACpB,UAAU,EAAE,OAAO;YACnB,QAAQ,EAAE,IAAI;SACjB,EAAE,OAAO,CAAC;IACf,CAAC;IAED,6BAAM,GAAN,UAAO,UAAkB;QAAE,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,6BAAO;;QAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,kBAAQ;YACnC,OAAO,QAAQ,CAAC,MAAM,OAAf,QAAQ,iBAAQ,UAAU,GAAK,IAAI,UAAE;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAED,qCAAc,GAAd,UACI,YAAoB,EACpB,aAAqB,EACrB,WAAmB,EACnB,MAA2B;QAJ/B,iBAcC;QARG,OAAO,IAAI,CAAC,MAAM,CACd,SAAS,EACT,YAAY,EACZ,aAAa,EACb,WAAW,CACd,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAED,2CAAoB,GAApB,UACI,YAAoB,EACpB,aAAqB,EACrB,WAAmB,EACnB,QAAgB,EAChB,CAAS,EACT,CAAS,EACT,CAAS,EACT,MAA2B;QAR/B,iBAsBC;QAZG,OAAO,IAAI,CAAC,MAAM,CACd,SAAS,EACT,YAAY,EACZ,aAAa,EACb,WAAW,EACX,QAAQ,EACR,CAAC,EACD,CAAC,EACD,CAAC,CACJ,CAAC,IAAI,CAAC,gBAAM;YACT,OAAO,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACP,CAAC;IAEL,mBAAC;AAAD,CAAC,CAzDiC,6CAAS,GAyD1C;;;;;;;;;;;;;AC5DD;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA,0DAA0D,EAAE;AAC5D;AACA;AACA;AACA,iDAAiD,EAAE;AACnD;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,QAAQ,kBAAkB,SAAS;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iFAAiF,IAAI;AACrF;AACA;AACqD;AACrD,iBAAiB;AACjB,iEAAe,MAAM,EAAC;AACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzCA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC,gBAAgB,OAAO;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACiL;AACjL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iEAAe,GAAG,EAAC;AACnB;;;;;;;;;;;;;;;AC7DO;AACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACD6D;AACK;AAC8C;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,YAAY;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,8CAAK;AACxD;AACA,qCAAqC,+CAAM,YAAY,+CAAM;AAC7D,qCAAqC,+CAAM,YAAY,+CAAM;AAC7D;AACO;AACP;AACA;AACA,kCAAkC,YAAY;AAC9C;AACA,wBAAwB,QAAQ;AAChC;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC,4BAA4B,QAAQ;AACpC;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,qBAAqB,2CAAI;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,kDAAM;AACd;AACA;AACA;AACA;AACA,uBAAuB,8CAAG;AAC1B;AACA;AACA,aAAa,2CAAI;AACjB,YAAY,qDAAU;AACtB;AACA,aAAa,2CAAI;AACjB,YAAY,qDAAU;AACtB;AACA;AACA;AACA;AACA,QAAQ,kDAAM;AACd,gBAAgB,kBAAkB;AAClC,eAAe,kDAAO;AACtB;AACA,0BAA0B,UAAU;AACpC;AACA,4BAA4B,UAAU;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,+BAA+B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,kDAAM;AACd,QAAQ,iDAAK;AACb;AACA;AACA,gBAAgB,WAAW;AAC3B,4CAA4C,UAAU;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,kDAAM;AACd;AACA;AACA;AACA,QAAQ,kDAAM;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,iDAAiD;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,0DAAe;AACrD;AACP;AACA;AACA;AACA;AACO;AACA;AACA;AACA;AACP;AACA;AACA;AACA;AACO;AACA;AACA;AACP,kDAAkD,qEAA0B,WAAW;AAChF;AACA;AACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrNA;AACA;AACA;AACA;AACA;AACA;AACA;AAC8C;AACC;AAC/C,YAAY,UAAU;AACtB;AACO;AACP;AACA;AACA;AACA;AACO;AACA;AACP;AACO;AACP;AACO;AACP;AACO;AACA;AACP;AACO;AACP;AACA;AACA;AACA;AACO;AACP;AACO;AACP,oBAAoB,gBAAgB;AACpC;AACA;AACA;AACA;AACA,2CAA2C,aAAa;AACxD;AACA;AACA;AACO;AACP,IAAI,iDAAM;AACV;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACO;AACP;AACA,oBAAoB,WAAW;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,4DAA4D,WAAW;AACvE,0DAA0D;AAC1D;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA,IAAI,iDAAM;AACV;AACA;AACA;AACA;AACA;AACO;AACP;AACA,oBAAoB,mBAAmB;AACvC;AACA,QAAQ,iDAAM;AACd;AACA;AACA;AACA,6BAA6B,mBAAmB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA,gBAAgB;AACT;AACP;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACO;AACP;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP,QAAQ,wDAAM,WAAW,wDAAM;AAC/B,eAAe,wDAAM;AACrB;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AC1LoF;AAC9C;AAC/B,kBAAkB,mDAAQ,CAAC,0DAAU;AACrC;AACP,cAAc,mDAAQ,CAAC,0DAAU;AACjC,eAAe,0DAAU;AACzB;AACA,CAAC;AACM,kBAAkB,mDAAQ,CAAC,0DAAU;AACrC,kBAAkB,mDAAQ,CAAC,0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTD;AACqB;AAChE,mBAAmB,iEAAW;AAC9B,oBAAoB,kEAAY;AACG;AACyE;AAC5G;AACO;AACP;AACA,oEAAoE,YAAY;AAChF;AACA;AACA;AACO;AACP;AACA,WAAW,+DAAW;AACtB;AACA;AACO;AACP;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA,QAAQ,kEAAY;AACpB;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;UC9CD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;UENA;UACA;UACA;UACA", "sources": ["webpack://matic/webpack/universalModuleDefinition", "webpack://matic/./node_modules/safe-buffer/index.js", "webpack://matic/./src/abstracts/base_big_number.ts", "webpack://matic/./src/abstracts/base_contract.ts", "webpack://matic/./src/abstracts/base_web3_client.ts", "webpack://matic/./src/abstracts/contract_method.ts", "webpack://matic/./src/abstracts/index.ts", "webpack://matic/./src/config.ts", "webpack://matic/./src/constant.ts", "webpack://matic/./src/default.ts", "webpack://matic/./src/enums/error_type.ts", "webpack://matic/./src/enums/index.ts", "webpack://matic/./src/enums/log_event_signature.ts", "webpack://matic/./src/implementation/bn.ts", "webpack://matic/./src/implementation/index.ts", "webpack://matic/./src/index.ts", "webpack://matic/./src/interfaces/allowance_transaction_option.ts", "webpack://matic/./src/interfaces/approve_transaction_option.ts", "webpack://matic/./src/interfaces/base_client_config.ts", "webpack://matic/./src/interfaces/block.ts", "webpack://matic/./src/interfaces/block_with_transaction.ts", "webpack://matic/./src/interfaces/bridge_transaction_option.ts", "webpack://matic/./src/interfaces/contract_init_param.ts", "webpack://matic/./src/interfaces/error.ts", "webpack://matic/./src/interfaces/exit_transaction_option.ts", "webpack://matic/./src/interfaces/index.ts", "webpack://matic/./src/interfaces/map_promise_option.ts", "webpack://matic/./src/interfaces/method.ts", "webpack://matic/./src/interfaces/plugin.ts", "webpack://matic/./src/interfaces/pos_client_config.ts", "webpack://matic/./src/interfaces/pos_contracts.ts", "webpack://matic/./src/interfaces/root_block_info.ts", "webpack://matic/./src/interfaces/rpc_request_payload.ts", "webpack://matic/./src/interfaces/rpc_response_payload.ts", "webpack://matic/./src/interfaces/transaction_config.ts", "webpack://matic/./src/interfaces/transaction_data.ts", "webpack://matic/./src/interfaces/transaction_option.ts", "webpack://matic/./src/interfaces/transaction_result.ts", "webpack://matic/./src/interfaces/transaction_write_result.ts", "webpack://matic/./src/interfaces/tx_receipt.ts", "webpack://matic/./src/interfaces/zkevm_client_config.ts", "webpack://matic/./src/interfaces/zkevm_contracts.ts", "webpack://matic/./src/pos/erc1155.ts", "webpack://matic/./src/pos/erc20.ts", "webpack://matic/./src/pos/erc721.ts", "webpack://matic/./src/pos/exit_util.ts", "webpack://matic/./src/pos/gas_swapper.ts", "webpack://matic/./src/pos/index.ts", "webpack://matic/./src/pos/pos_token.ts", "webpack://matic/./src/pos/root_chain.ts", "webpack://matic/./src/pos/root_chain_manager.ts", "webpack://matic/./src/services/abi_service.ts", "webpack://matic/./src/services/index.ts", "webpack://matic/./src/services/network_service.ts", "webpack://matic/./src/types/index.ts", "webpack://matic/./src/types/pos_erc1155_deposit_param.ts", "webpack://matic/./src/types/pos_erc1155_transfer_param.ts", "webpack://matic/./src/utils/abi_manager.ts", "webpack://matic/./src/utils/base_token.ts", "webpack://matic/./src/utils/bridge_client.ts", "webpack://matic/./src/utils/buffer-utils.ts", "webpack://matic/./src/utils/converter.ts", "webpack://matic/./src/utils/error_helper.ts", "webpack://matic/./src/utils/event_bus.ts", "webpack://matic/./src/utils/http_request.ts", "webpack://matic/./src/utils/index.ts", "webpack://matic/./src/utils/keccak.ts", "webpack://matic/./src/utils/logger.ts", "webpack://matic/./src/utils/map_promise.ts", "webpack://matic/./src/utils/merge.ts", "webpack://matic/./src/utils/merkle_tree.ts", "webpack://matic/./src/utils/not_implemented.ts", "webpack://matic/./src/utils/promise_resolve.ts", "webpack://matic/./src/utils/proof_util.ts", "webpack://matic/./src/utils/resolve.ts", "webpack://matic/./src/utils/set_proof_api_url.ts", "webpack://matic/./src/utils/types.ts", "webpack://matic/./src/utils/use.ts", "webpack://matic/./src/utils/web3_side_chain_client.ts", "webpack://matic/./src/utils/zkevm_bridge_client.ts", "webpack://matic/./src/zkevm/bridge_util.ts", "webpack://matic/./src/zkevm/erc20.ts", "webpack://matic/./src/zkevm/index.ts", "webpack://matic/./src/zkevm/zkevm_bridge.ts", "webpack://matic/./src/zkevm/zkevm_custom_bridge.ts", "webpack://matic/./src/zkevm/zkevm_token.ts", "webpack://matic/./src/zkevm/zkevm_wrapper.ts", "webpack://matic/external umd \"@ethereumjs/block\"", "webpack://matic/external umd \"@ethereumjs/common\"", "webpack://matic/external umd \"@ethereumjs/trie\"", "webpack://matic/external umd \"@ethereumjs/util\"", "webpack://matic/external umd \"bn.js\"", "webpack://matic/external umd \"buffer\"", "webpack://matic/external umd \"node-fetch\"", "webpack://matic/external umd \"rlp\"", "webpack://matic/./node_modules/@noble/hashes/esm/_assert.js", "webpack://matic/./node_modules/@noble/hashes/esm/_u64.js", "webpack://matic/./node_modules/@noble/hashes/esm/crypto.js", "webpack://matic/./node_modules/@noble/hashes/esm/sha3.js", "webpack://matic/./node_modules/@noble/hashes/esm/utils.js", "webpack://matic/./node_modules/ethereum-cryptography/esm/keccak.js", "webpack://matic/./node_modules/ethereum-cryptography/esm/utils.js", "webpack://matic/webpack/bootstrap", "webpack://matic/webpack/runtime/compat get default export", "webpack://matic/webpack/runtime/define property getters", "webpack://matic/webpack/runtime/hasOwnProperty shorthand", "webpack://matic/webpack/runtime/make namespace object", "webpack://matic/webpack/before-startup", "webpack://matic/webpack/startup", "webpack://matic/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"@ethereumjs/util\"), require(\"buffer\"), require(\"bn.js\"), require(\"rlp\"), require(\"@ethereumjs/trie\"), require(\"@ethereumjs/block\"), require(\"@ethereumjs/common\"), require(\"node-fetch\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"matic\", [\"@ethereumjs/util\", \"buffer\", \"bn.js\", \"rlp\", \"@ethereumjs/trie\", \"@ethereumjs/block\", \"@ethereumjs/common\", \"node-fetch\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"matic\"] = factory(require(\"@ethereumjs/util\"), require(\"buffer\"), require(\"bn.js\"), require(\"rlp\"), require(\"@ethereumjs/trie\"), require(\"@ethereumjs/block\"), require(\"@ethereumjs/common\"), require(\"node-fetch\"));\n\telse\n\t\troot[\"matic\"] = factory(root[\"@ethereumjs/util\"], root[\"buffer\"], root[\"bn.js\"], root[\"rlp\"], root[\"@ethereumjs/trie\"], root[\"@ethereumjs/block\"], root[\"@ethereumjs/common\"], root[\"node-fetch\"]);\n})(self, (__WEBPACK_EXTERNAL_MODULE__ethereumjs_util__, __WEBPACK_EXTERNAL_MODULE_buffer__, __WEBPACK_EXTERNAL_MODULE_bn_js__, __WEBPACK_EXTERNAL_MODULE_rlp__, __WEBPACK_EXTERNAL_MODULE__ethereumjs_trie__, __WEBPACK_EXTERNAL_MODULE__ethereumjs_block__, __WEBPACK_EXTERNAL_MODULE__ethereumjs_common__, __WEBPACK_EXTERNAL_MODULE_node_fetch__) => {\nreturn ", "/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n", "import { throwNotImplemented } from \"..\";\n\nexport abstract class BaseBigNumber {\n    static isBN(value) {\n        return throwNotImplemented<boolean>();\n    }\n\n    abstract toString(): string;\n    abstract toNumber(): number;\n    abstract add(value: BaseBigNumber): BaseBigNumber;\n    abstract sub(value: BaseBigNumber): BaseBigNumber;\n    abstract mul(value: BaseBigNumber): BaseBigNumber;\n    abstract div(value: BaseBigNumber): BaseBigNumber;\n\n    abstract lte(value: BaseBigNumber): boolean;\n    abstract lt(value: BaseBigNumber): boolean;\n    abstract gte(value: BaseBigNumber): boolean;\n    abstract gt(value: BaseBigNumber): boolean;\n    abstract eq(value: BaseBigNumber): boolean;\n}\n", "import { BaseContractMethod } from \"../abstracts\";\nimport { Logger } from \"../utils\";\n\nexport abstract class BaseContract {\n\n    constructor(public address: string, public logger:Logger) {\n\n    }\n\n    abstract method(methodName: string, ...args): BaseContractMethod;\n}", "import { BaseContract } from \"../abstracts\";\nimport { ITransactionRequestConfig, ITransactionReceipt, ITransactionData, IBlock, IBlockWithTransaction, IJsonRpcRequestPayload, IJsonRpcResponse, ITransactionWriteResult } from \"../interfaces\";\nimport { Logger } from \"../utils\";\n\nexport abstract class BaseWeb3Client {\n    abstract name: string;\n\n    constructor(public logger: Logger) {\n\n    }\n\n    abstract getContract(address: string, abi: any): BaseContract;\n\n    abstract read(config: ITransactionRequestConfig): Promise<string>;\n\n    abstract write(config: ITransactionRequestConfig): ITransactionWriteResult;\n    abstract getGasPrice(): Promise<string>;\n    abstract estimateGas(config: ITransactionRequestConfig): Promise<number>;\n    abstract getChainId(): Promise<number>;\n    abstract getTransactionCount(address: string, blockNumber: any): Promise<number>;\n\n    abstract getTransaction(transactionHash: string): Promise<ITransactionData>;\n    abstract getTransactionReceipt(transactionHash: string): Promise<ITransactionReceipt>;\n    // abstract extend(property: string, methods: IMethod[])\n\n    abstract getBlock(blockHashOrBlockNumber): Promise<IBlock>;\n    abstract getBlockWithTransaction(blockHashOrBlockNumber): Promise<IBlockWithTransaction>;\n    abstract hexToNumber(value: any): number;\n    abstract hexToNumberString(value: any): string;\n    abstract getBalance(address: string): Promise<string>;\n    abstract getAccounts(): Promise<string[]>;\n    abstract signTypedData(signer: string, typedData: object): Promise<string>;\n\n    getRootHash?(startBlock: number, endBlock: number) {\n        return this.sendRPCRequest({\n            jsonrpc: '2.0',\n            method: 'eth_getRootHash',\n            params: [Number(startBlock), Number(endBlock)],\n            id: new Date().getTime()\n        }).then(payload => {\n            return String(payload.result);\n        });\n    }\n\n    getAccountsUsingRPC_() {\n        return this.sendRPCRequest({\n            jsonrpc: '2.0',\n            method: 'eth_accounts',\n            params: [],\n            id: new Date().getTime()\n        }).then(payload => {\n            return payload.result;\n        });\n    }\n\n    abstract sendRPCRequest(request: IJsonRpcRequestPayload): Promise<IJsonRpcResponse>;\n\n    abstract encodeParameters(params: any[], types: any[]): string;\n    abstract decodeParameters(hexString: string, types: any[]): any[];\n    abstract etheriumSha3(...value): string;\n\n}\n", "import { ITransactionRequestConfig, ITransactionWriteResult } from \"../interfaces\";\nimport { Logger } from \"../utils\";\n\nexport abstract class BaseContractMethod {\n    constructor(public logger: Logger) {\n\n    }\n    abstract get address(): string;\n    abstract read<T>(tx?: ITransactionRequestConfig, defaultBlock?: number | string): Promise<T>;\n    abstract write(tx: ITransactionRequestConfig,): ITransactionWriteResult;\n    abstract estimateGas(tx: ITransactionRequestConfig,): Promise<number>;\n    abstract encodeABI(): any;\n}", "export * from \"./contract_method\";\nexport * from \"./base_web3_client\";\nexport * from \"./base_contract\";\nexport * from \"./base_big_number\";", "export const config = {\n  abiStoreUrl: 'https://static.polygon.technology/network/',\n  zkEvmBridgeService: 'https://proof-generator.polygon.technology/',\n}\n", "export const MAX_AMOUNT = '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff';\nexport const ADDRESS_ZERO = '******************************************';\nexport const DAI_PERMIT_TYPEHASH = \"0xea2aa0a1be11a07ed86d755c93467f4f82362b452371d1ba94d1715123511acb\";\nexport const EIP_2612_PERMIT_TYPEHASH = \"0x6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9\";\nexport const EIP_2612_DOMAIN_TYPEHASH = \"0x8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f\";\nexport const UNISWAP_DOMAIN_TYPEHASH = \"0x8cad95687ba82c2ce50e74f7b754645e5117c3a5bec8151c0726d5857980a866\";\nexport const _GLOBAL_INDEX_MAINNET_FLAG = BigInt(2 ** 64);\nexport enum Permit {\n    DAI = \"DAI\",\n    EIP_2612 = \"EIP_2612\",\n    UNISWAP = \"UNISWAP\",\n}\n", "import { POSClient } from \"./pos\";\nimport { use, utils } from \"./utils\";\n\nexport const defaultExport = {\n    utils: utils,\n    use,\n    POSClient,\n};", "export enum ERROR_TYPE {\n    AllowedOnRoot = \"allowed_on_root\",\n    AllowedOnChild = \"allowed_on_child\",\n    Unknown = \"unknown\",\n    ProofAPINotSet = \"proof_api_not_set\",\n    TransactionOptionNotObject = \"transation_object_not_object\",\n    BurnTxNotCheckPointed = \"burn_tx_not_checkpointed\",\n    EIP1559NotSupported = \"eip-1559_not_supported\",\n    NullSpenderAddress = \"null_spender_address\",\n    AllowedOnNonNativeTokens = \"allowed_on_non_native_token\",\n    AllowedOnMainnet = \"allowed_on_mainnet\",\n    BridgeAdapterNotFound = \"bridge_adapter_address_not_passed\"\n}\n", "export * from \"./log_event_signature\";\nexport * from \"./error_type\";", "export enum Log_Event_Signature {\n    // PlasmaErc20WithdrawEventSig = '0xebff2602b3f468259e1e99f613fed6691f3a6526effe6ef3e768ba7ae7a36c4f',\n    // PlasmaErc721WithdrawEventSig = '0x9b1bfa7fa9ee420a16e124f794c35ac9f90472acc99140eb2f6447c714cad8eb',\n    Erc20Transfer = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',\n    Erc721Transfer = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',\n    Erc1155Transfer = '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62',\n    Erc721BatchTransfer = '0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df',\n    Erc1155BatchTransfer = '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb',\n    Erc721TransferWithMetadata = '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14',\n}\n", "import { throwNotImplemented } from \"..\";\nimport { BaseBigNumber } from \"../abstracts\";\n\nexport class EmptyBigNumber extends BaseBigNumber {\n\n    constructor(value) {\n        super();\n    }\n\n    toString(base?) {\n        return throwNotImplemented<string>();\n    }\n\n    toNumber() {\n        return throwNotImplemented<number>();\n    }\n\n    add(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    sub(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    mul(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    div(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    lte(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n    }\n\n    lt(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n\n    }\n\n    gte(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n\n    }\n\n    gt(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n\n    }\n\n    eq(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n    }\n}\n", "export * from \"./bn\";", "import { defaultExport } from \"./default\";\n\nexport * from \"./utils\";\nexport * from \"./enums\";\nexport * from \"./pos\";\nexport * from \"./interfaces\";\nexport * from \"./types\";\nexport * from \"./constant\";\nexport * from \"./abstracts\";\nexport * from \"./services\";\nexport * from \"./zkevm\";\n\nexport default defaultExport;\n", "import { ITransactionOption } from \"./transaction_option\";\n\nexport interface IAllowanceTransactionOption extends ITransactionOption {\n\n    /**\n     * address of spender \n     * \n     * **spender** - third-party user or a smart contract which can transfer your token on your behalf.\n     *\n     * @type {string}\n     * @memberof IAllowanceTransactionOption\n     */\n    spenderAddress?: string;\n}", "import { ITransactionOption } from \"./transaction_option\";\n\nexport interface IApproveTransactionOption extends ITransactionOption {\n\n    /**\n     * address of spender \n     * \n     * **spender** - third-party user or a smart contract which can transfer your token on your behalf.\n     *\n     * @type {string}\n     * @memberof IAllowanceTransactionOption\n     */\n    spenderAddress?: string;\n    forceUpdateGlobalExitRoot?: boolean;\n}", "export interface IBaseClientConfig {\n    network: string;\n    version: string;\n    parent?: {\n        provider: any;\n        defaultConfig: {\n            from: string;\n        }\n    };\n    child?: {\n        provider: any;\n        defaultConfig: {\n            from: string;\n        }\n    };\n    log?: boolean;\n    requestConcurrency?: number;\n\n}\n", "export interface IBaseBlock {\n    size: number;\n    difficulty: number;\n    totalDifficulty: number;\n    uncles: string[];\n    number: number;\n    hash: string;\n    parentHash: string;\n    nonce: string;\n    sha3Uncles: string;\n    logsBloom: string;\n    transactionsRoot: string;\n    stateRoot: string;\n    receiptsRoot: string;\n    miner: string;\n    extraData: string;\n    gasLimit: number;\n    gasUsed: number;\n    timestamp: number | string;\n    baseFeePerGas?: string;\n}\n\nexport interface IBlock extends IBaseBlock {\n    transactions: string[];\n}", "import { IBlock, IBase<PERSON>lock } from \"./block\";\nimport { ITransactionData } from \"./transaction_data\";\n\nexport interface IBlockWithTransaction extends IBaseBlock {\n    transactions: ITransactionData[];\n}", "import { ITransactionOption } from \"./transaction_option\";\n\nexport interface IBridgeTransactionOption extends ITransactionOption {\n\n    /**\n     * address of spender \n     * \n     * **spender** - third-party user or a smart contract which can transfer your token on your behalf.\n     *\n     * @type {string}\n     * @memberof IBridgeTransactionOption\n     */\n    permitData?: string;\n    forceUpdateGlobalExitRoot?: boolean;\n    v?: number;\n    r?: string;\n    s?: string;\n}\n", "export interface IContractInitParam {\n    address: string;\n    isParent: boolean;\n    bridgeAdapterAddress?: string;\n    /**\n     * used to get the predicate\n     *\n     * @type {string}\n     * @memberof IContractInitParam\n     */\n    name: string;\n    bridgeType?: string;\n}\n", "import { ERROR_TYPE } from \"../enums\";\n\nexport interface IError {\n    type: ERROR_TYPE;\n    message: string;\n}", "import { ITransactionOption } from \"./transaction_option\";\n\nexport interface IExitTransactionOption extends ITransactionOption {\n    /**\n     * event signature for burn transaction\n     *\n     * @type {string}\n     * @memberof IExitTransactionOption\n     */\n    burnEventSignature?: string;\n}", "export * from \"./plugin\";\nexport * from \"./method\";\nexport * from \"./transaction_config\";\nexport * from \"./transaction_write_result\";\nexport * from \"./transaction_result\";\nexport * from \"./transaction_option\";\nexport * from \"./contract_init_param\";\nexport * from \"./tx_receipt\";\nexport * from \"./pos_client_config\";\nexport * from \"./transaction_data\";\nexport * from \"./block\";\nexport * from \"./block_with_transaction\";\nexport * from \"./rpc_request_payload\";\nexport * from \"./rpc_response_payload\";\nexport * from \"./map_promise_option\";\nexport * from \"./base_client_config\";\nexport * from \"./error\";\nexport * from \"./pos_contracts\";\nexport * from \"./root_block_info\";\nexport * from \"./allowance_transaction_option\";\nexport * from \"./approve_transaction_option\";\nexport * from \"./exit_transaction_option\";\nexport * from \"./zkevm_client_config\";\nexport * from \"./zkevm_contracts\";\nexport * from \"./bridge_transaction_option\";\n", "export interface IMapPromiseOption {\n    concurrency: number;\n}\n", "export interface IMethod {\n    name: string;\n    call: string;\n    params?: number;\n    inputFormatter?: Array<(() => void) | null>;\n    outputFormatter?: () => void;\n    transformPayload?: () => void;\n    extraFormatters?: any;\n    defaultBlock?: string;\n    defaultAccount?: string | null;\n    abiCoder?: any;\n    handleRevert?: boolean;\n}\n", "import { defaultExport } from \"../default\";\n\nexport interface IPlugin {\n    setup(matic: typeof defaultExport, ...payload);\n}", "import { IBaseClientConfig } from \"./base_client_config\";\n\nexport interface IPOSERC1155Address {\n    mintablePredicate?: string;\n}\n\nexport interface IPOSClientConfig extends IBaseClientConfig {\n    rootChainManager?: string;\n    rootChain?: string;\n    erc1155?: IPOSERC1155Address;\n    rootChainDefaultBlock?:string;\n    gasSwapper?: string;\n}\n", "import { ExitUtil, RootChainManager, GasSwapper } from \"../pos\";\n\nexport interface IPOSContracts {\n    rootChainManager: RootChainManager;\n    exitUtil: ExitUtil;\n    gasSwapper: GasSwapper;\n}", "import { BaseBigNumber } from \"../abstracts\";\n\nexport interface IRootBlockInfo {\n    start: string;\n    end: string;\n    headerBlockNumber: BaseBigNumber;\n}", "export interface IJsonRpcRequestPayload {\n    jsonrpc: string;\n    method: string;\n    params: any[];\n    id?: string | number;\n}", "export interface IJsonRpcResponse {\n    jsonrpc: string;\n    id: number;\n    result?: any;\n    error?: string;\n}", "import { BaseBigNumber } from \"../abstracts\";\n\nexport interface ITransactionRequestConfig {\n    from?: string;\n    to?: string;\n    value?: number | string | BaseBigNumber;\n    gasLimit?: number | string;\n    gasPrice?: number | string | BaseBigNumber;\n    data?: string;\n    nonce?: number;\n    chainId?: number;\n    chain?: string;\n    hardfork?: string;\n    maxFeePerGas?: number | string;\n    maxPriorityFeePerGas?: number | string;\n    type?: number;\n}", "export interface ITransactionData {\n    transactionHash: string;\n    nonce: number;\n    blockHash: string | null;\n    blockNumber: number | null;\n    transactionIndex: number | null;\n    from: string;\n    to: string | null;\n    value: string;\n    gasPrice: string;\n    gas: number;\n    input: string;\n}", "import { ITransactionRequestConfig } from \"./transaction_config\";\n\nexport interface ITransactionOption extends ITransactionRequestConfig {\n    returnTransaction?: boolean;\n}", "import { ITransactionRequestConfig } from \"./transaction_config\";\n\nexport interface ITransactionResult {\n\testimateGas(tx?: ITransactionRequestConfig): Promise<number>;\n\tencodeABI(): string;\n}", "import { ITransactionReceipt } from \"./tx_receipt\";\n\nexport interface ITransactionWriteResult {\n\n    getTransactionHash: () => Promise<string>;\n    getReceipt: () => Promise<ITransactionReceipt>;\n}", "export interface ITransactionReceipt {\n    transactionHash: string;\n    transactionIndex: number;\n    blockHash: string;\n    blockNumber: number;\n    from: string;\n    to: string;\n    contractAddress: string;\n    cumulativeGasUsed: number;\n    gasUsed: number;\n    logs?: ILog[];\n    events?: {\n        [eventName: string]: IEventLog;\n    };\n    status: boolean;\n    logsBloom: string;\n    root: string;\n    type: string;\n}\n\nexport interface ILog {\n    address: string;\n    data: string;\n    topics: string[];\n    logIndex: number;\n    transactionHash: string;\n    transactionIndex: number;\n    blockHash: string;\n    blockNumber: number;\n}\n\nexport interface IEventLog {\n    event: string;\n    address: string;\n    returnValues: any;\n    logIndex: number;\n    transactionIndex: number;\n    transactionHash: string;\n    blockHash: string;\n    blockNumber: number;\n    raw?: { data: string; topics: string[] };\n}", "import { IBaseClientConfig } from \"./base_client_config\";\n\nexport interface IZkEvmClientConfig extends IBaseClientConfig {\n    parentBridge?: string;\n    childBridge?: string;\n    zkEVMWrapper?: string;\n}\n", "import { ZkEvmBridge, BridgeUtil, ZkEVMWrapper } from \"../zkevm\";\n\nexport interface IZkEvmContracts {\n    parentBridge: ZkEvmBridge;\n    childBridge: ZkEvmBridge;\n    bridgeUtil: BridgeUtil;\n    zkEVMWrapper: ZkEVMWrapper;\n}\n", "import { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\nimport { Converter, promiseResolve, Web3SideChainClient } from \"../utils\";\nimport { POSToken } from \"./pos_token\";\nimport { Log_Event_Signature } from \"../enums\";\nimport { IPOSContracts, IPOSERC1155Address } from \"../interfaces\";\nimport { POSERC1155DepositBatchParam, POSERC1155DepositParam, POSERC1155TransferParam, TYPE_AMOUNT } from \"..\";\n\nexport class ERC1155 extends POSToken {\n\n    mintablePredicateAddress: string;\n\n    get addressConfig(): IPOSERC1155Address {\n        return this.client.config.erc1155 || {};\n    }\n\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        getContracts: () => IPOSContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n            bridgeType: 'pos'\n        }, client, getContracts);\n\n    }\n\n    private getAddress_(value: string) {\n        const addresses = this.addressConfig;\n        if (addresses[value]) {\n            return promiseResolve(addresses[value]);\n        }\n\n        return this.client.getConfig(value);\n    }\n\n    /**\n     * get balance of a user for supplied token\n     *\n     * @param {string} userAddress\n     * @param {TYPE_AMOUNT} tokenId\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    getBalance(userAddress: string, tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"balanceOf\",\n                userAddress,\n                Converter.toHex(tokenId)\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    /**\n     * check if a user is approved for all tokens\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    isApprovedAll(userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"isApprovedAll\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"isApprovedForAll\",\n                userAddress,\n                predicateAddress\n            );\n            return this.processRead<boolean>(method, option);\n        });\n\n    }\n\n    private approveAll_(predicateAddressPromise: Promise<string>, option: ITransactionOption) {\n        this.checkForRoot(\"approve\");\n\n        return Promise.all([this.getContract(), predicateAddressPromise]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"setApprovalForAll\",\n                predicateAddress,\n                true\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * approve all tokens \n     *\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    approveAll(option?: ITransactionOption) {\n        this.checkForRoot(\"approve\");\n\n        return this.approveAll_(\n            this.getPredicateAddress(), option\n        );\n    }\n\n    /**\n     * approve all tokens for mintable token\n     *\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    approveAllForMintable(option?: ITransactionOption) {\n        this.checkForRoot(\"approveForMintable\");\n        const addressPath = \"Main.POSContracts.MintableERC1155PredicateProxy\";\n        return this.approveAll_(\n            this.getAddress_(addressPath), option\n        );\n    }\n\n    /**\n     * deposit supplied amount of token for a user \n     *\n     * @param {POSERC1155DepositParam} param\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    deposit(param: POSERC1155DepositParam, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n        return this.depositMany({\n            amounts: [param.amount],\n            tokenIds: [param.tokenId],\n            userAddress: param.userAddress,\n            data: param.data\n        }, option);\n    }\n\n    /**\n     * deposit supplied amount of multiple token for user\n     *\n     * @param {POSERC1155DepositBatchParam} param\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    depositMany(param: POSERC1155DepositBatchParam, option?: ITransactionOption) {\n        this.checkForRoot(\"depositMany\");\n\n        const { tokenIds, amounts, data, userAddress } = param;\n        const emptyHex = Converter.toHex(0);\n        const amountInABI = this.client.parent.encodeParameters(\n            [\n                tokenIds.map(t => Converter.toHex(t)),\n                amounts.map(a => Converter.toHex(a)),\n                data || emptyHex\n            ],\n            ['uint256[]', 'uint256[]', 'bytes'],\n        );\n\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n\n    }\n\n    /**\n     * start withdraw process by burning the required amount for a token\n     *\n     * @param {string} tokenId\n     * @param {TYPE_AMOUNT} amount\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawStart(tokenId: TYPE_AMOUNT, amount: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStart\");\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawSingle\",\n                Converter.toHex(tokenId),\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * start the withdraw process by burning the supplied amount of multiple token at a time\n     *\n     * @param {TYPE_AMOUNT[]} tokenIds\n     * @param {TYPE_AMOUNT[]} amounts\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawStartMany(tokenIds: TYPE_AMOUNT[], amounts: TYPE_AMOUNT[], option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStartMany\");\n\n        const tokensInHex = tokenIds.map(t => {\n            return Converter.toHex(t);\n        });\n        const amountsInHex = amounts.map(t => {\n            return Converter.toHex(t);\n        });\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawBatch\",\n                tokensInHex,\n                amountsInHex\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * exit the withdraw process and get the burned amount on root chain\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155Transfer,\n            false,\n            option\n        );\n    }\n\n    /**\n     * exit the withdraw process and get the burned amount on root chain\n     * \n     * the process is faster because it uses proof api\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExitFaster(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitFaster\");\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155Transfer,\n            true,\n            option\n        );\n    }\n\n    /**\n     * exit the withdraw process for many burned transaction and get the burned amount on root chain\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExitMany(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitMany\");\n\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155BatchTransfer,\n            false,\n            option\n        );\n    }\n\n    /**\n     * exit the withdraw process for many burned transaction and get the burned amount on root chain\n     *\n     * the process is faster because it uses proof api\n     * \n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExitFasterMany(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitFasterMany\");\n\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155BatchTransfer,\n            true,\n            option\n        );\n    }\n\n    /**\n     * check if exit has been completed for a transaction hash\n     *\n     * @param {string} burnTxHash\n     * @return {*} \n     * @memberof ERC1155\n     */\n    isWithdrawExited(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc1155Transfer\n        );\n    }\n\n    /**\n     * check if batch exit has been completed for a transaction hash\n     *\n     * @param {string} txHash\n     * @return {*} \n     * @memberof ERC1155\n     */\n    isWithdrawExitedMany(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc1155BatchTransfer\n        );\n    }\n\n    /**\n     * transfer the required amount of a token to another user\n     *\n     * @param {POSERC1155TransferParam} param\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    transfer(param: POSERC1155TransferParam, option?: ITransactionOption) {\n        return this.transferERC1155(\n            param, option\n        );\n    }\n}", "import { ITransactionOption } from \"../interfaces\";\nimport { Converter, Web3SideChainClient } from \"../utils\";\nimport { POSToken } from \"./pos_token\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { ERROR_TYPE, Log_Event_Signature } from \"../enums\";\nimport { MAX_AMOUNT, promiseResolve } from \"..\";\nimport { IAllowanceTransactionOption, IApproveTransactionOption, IExitTransactionOption, IPOSClientConfig, IPOSContracts } from \"../interfaces\";\n\nexport class ERC20 extends POSToken {\n\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        getContracts: () => IPOSContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            name: 'ChildERC20',\n            bridgeType: 'pos'\n        }, client, getContracts);\n    }\n\n    getBalance(userAddress: string, option?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"balanceOf\",\n                userAddress\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    /**\n     * get allowance of user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    getAllowance(userAddress: string, option: IAllowanceTransactionOption = {}) {\n        const spenderAddress = option.spenderAddress;\n\n        const predicatePromise = spenderAddress ? promiseResolve(spenderAddress) : this.getPredicateAddress();\n\n        return Promise.all([predicatePromise, this.getContract()]).then(result => {\n            const [predicateAddress, contract] = result;\n            const method = contract.method(\n                \"allowance\",\n                userAddress,\n                predicateAddress,\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    approve(amount: TYPE_AMOUNT, option: IApproveTransactionOption = {}) {\n        const spenderAddress = option.spenderAddress;\n\n        if (!spenderAddress && !this.contractParam.isParent) {\n            this.client.logger.error(ERROR_TYPE.NullSpenderAddress).throw();\n        }\n\n        const predicatePromise = spenderAddress ? promiseResolve(spenderAddress) : this.getPredicateAddress();\n\n        return Promise.all([predicatePromise, this.getContract()]).then(result => {\n            const [predicateAddress, contract] = result;\n            const method = contract.method(\n                \"approve\",\n                predicateAddress,\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    approveMax(option: IApproveTransactionOption = {}) {\n        return this.approve(\n            MAX_AMOUNT\n            , option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    deposit(amount: TYPE_AMOUNT, userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user along with ETHER for gas token\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositWithGas(amount: TYPE_AMOUNT, userAddress: string, swapEthAmount: TYPE_AMOUNT, swapCallData: string, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n\n        return this.getChainId().then((chainId: number) => {\n            if (chainId !== 1) {\n                this.client.logger.error(ERROR_TYPE.AllowedOnMainnet).throw();\n            }\n            const amountInABI = this.client.parent.encodeParameters(\n                [Converter.toHex(amount)],\n                ['uint256'],\n            );\n\n            option.value = Converter.toHex(swapEthAmount);\n\n            return this.gasSwapper.depositWithGas(\n                this.contractParam.address,\n                amountInABI,\n                userAddress,\n                swapCallData,\n                option\n            );\n        });\n\n    }\n\n    private depositEther_(amount: TYPE_AMOUNT, userAddress: string, option: ITransactionOption = {}) {\n        this.checkForRoot(\"depositEther\");\n\n\n        option.value = Converter.toHex(amount);\n        return this.rootChainManager.method(\"depositEtherFor\", userAddress).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    private depositEtherWithGas_(amount: TYPE_AMOUNT, userAddress: string, swapEthAmount: TYPE_AMOUNT, swapCallData: string, option: ITransactionOption = {}) {\n        this.checkForRoot(\"depositEtherWithGas\");\n\n        return this.getChainId().then((chainId: number) => {\n            if (chainId !== 1) {\n                this.client.logger.error(ERROR_TYPE.AllowedOnMainnet).throw();\n            }\n            const amountInABI = this.client.parent.encodeParameters(\n                [Converter.toHex(amount)],\n                ['uint256'],\n            );\n\n            option.value = Converter.toHex(\n                Converter.toBN(amount).add(\n                    Converter.toBN(swapEthAmount)\n                )\n            );\n\n            return this.gasSwapper.depositWithGas(\n                \"******************************************\",\n                amountInABI,\n                userAddress,\n                swapCallData,\n                option\n            );\n        });\n    }\n\n    /**\n     * initiate withdraw by burning provided amount\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawStart(amount: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStart\");\n\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdraw\",\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    private withdrawExit_(burnTransactionHash: string, isFast: boolean, option: IExitTransactionOption = {}) {\n        const eventSignature = option.burnEventSignature ?\n            option.burnEventSignature : Log_Event_Signature.Erc20Transfer;\n\n        return this.exitUtil.buildPayloadForExit(\n            burnTransactionHash,\n            eventSignature,\n            isFast\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n\n    /**\n     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawExit(burnTransactionHash: string, option?: IExitTransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n\n        return this.withdrawExit_(burnTransactionHash, false, option);\n    }\n\n    /**\n     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.\n     *\n     *  Note:- It create the proof in api call for fast exit.\n     * \n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawExitFaster(burnTransactionHash: string, option?: IExitTransactionOption) {\n        this.checkForRoot(\"withdrawExitFaster\");\n\n        return this.withdrawExit_(burnTransactionHash, true, option);\n    }\n\n    /**\n     * check if exit has been completed for a transaction hash\n     *\n     * @param {string} burnTxHash\n     * @returns\n     * @memberof ERC20\n     */\n    isWithdrawExited(burnTxHash: string) {\n        return this.isWithdrawn(burnTxHash, Log_Event_Signature.Erc20Transfer);\n    }\n\n    /**\n     * transfer amount to another user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} to\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    transfer(amount: TYPE_AMOUNT, to: string, option?: ITransactionOption) {\n        return this.transferERC20(to, amount, option);\n    }\n\n}\n", "import { IPOSClientConfig, IPOSContracts, ITransactionOption } from \"../interfaces\";\nimport { RootChainManager } from \"./root_chain_manager\";\nimport { Converter, Web3SideChainClient } from \"../utils\";\nimport { POSToken } from \"./pos_token\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { ExitUtil } from \"./exit_util\";\nimport { Log_Event_Signature } from \"../enums\";\n\nexport class ERC721 extends POSToken {\n\n\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        getContracts: () => IPOSContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            name: 'ChildERC721',\n            bridgeType: 'pos'\n        }, client, getContracts);\n    }\n\n    private validateMany_(tokenIds) {\n        if (tokenIds.length > 20) {\n            throw new Error('can not process more than 20 tokens');\n        }\n        return tokenIds.map(tokenId => {\n            return Converter.toHex(tokenId);\n        });\n    }\n\n    /**\n     * get tokens count for the user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [options]\n     * @returns\n     * @memberof ERC721\n     */\n    getTokensCount(userAddress: string, options?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"balanceOf\",\n                userAddress\n            );\n            return this.processRead<string>(method, options);\n        }).then(count => {\n            return Number(count);\n        });\n    }\n\n    /**\n     * returns token id on supplied index for user\n     *\n     * @param {number} index\n     * @param {string} userAddress\n     * @param {ITransactionOption} [options]\n     * @returns\n     * @memberof ERC721\n     */\n    getTokenIdAtIndexForUser(index: number, userAddress: string, options?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"tokenOfOwnerByIndex\",\n                userAddress,\n                index\n            );\n\n            return this.processRead<string>(method, options);\n        });\n    }\n\n    /**\n     * get all tokens for user\n     *\n     * @param {string} userAddress\n     * @param {*} [limit=Infinity]\n     * @returns\n     * @memberof ERC721\n     */\n    getAllTokens(userAddress: string, limit = Infinity) {\n        return this.getTokensCount(userAddress).then(count => {\n            count = Number(count);\n            if (count > limit) {\n                count = limit;\n            }\n            const promises = [];\n            for (let i = 0; i < count; i++) {\n                promises.push(\n                    this.getTokenIdAtIndexForUser(i, userAddress)\n                );\n            }\n            return Promise.all(\n                promises\n            );\n        });\n    }\n\n    isApproved(tokenId: string, option?: ITransactionOption) {\n        this.checkForRoot(\"isApproved\");\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"getApproved\",\n                tokenId\n            );\n            return Promise.all([\n                this.processRead<string>(method, option),\n                this.getPredicateAddress()\n            ]).then(result => {\n                return result[0] === result[1];\n            });\n        });\n    }\n\n    isApprovedAll(userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"isApprovedAll\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"isApprovedForAll\",\n                userAddress,\n                predicateAddress\n            );\n            return this.processRead<boolean>(method, option);\n        });\n\n    }\n\n    approve(tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForRoot(\"approve\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"approve\",\n                predicateAddress,\n                Converter.toHex(tokenId)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    approveAll(option?: ITransactionOption) {\n        this.checkForRoot(\"approveAll\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"setApprovalForAll\",\n                predicateAddress,\n                true\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n\n    deposit(tokenId: TYPE_AMOUNT, userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(tokenId)],\n            ['uint256'],\n        );\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n    }\n\n    depositMany(tokenIds: TYPE_AMOUNT[], userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"depositMany\");\n\n        const tokensInHex = this.validateMany_(tokenIds);\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [tokensInHex],\n            ['uint256[]'],\n        );\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n    }\n\n    withdrawStart(tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStart\");\n\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdraw\",\n                Converter.toHex(tokenId)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    withdrawStartWithMetaData(tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStartWithMetaData\");\n\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawWithMetadata\",\n                Converter.toHex(tokenId)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    withdrawStartMany(tokenIds: TYPE_AMOUNT[], option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStartMany\");\n\n        const tokensInHex = this.validateMany_(tokenIds);\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawBatch\",\n                tokensInHex\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    withdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n\n        return this.exitUtil.buildPayloadForExit(\n            burnTransactionHash,\n            Log_Event_Signature.Erc721Transfer,\n            false\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n\n    withdrawExitOnIndex(burnTransactionHash: string, index: number, option?: ITransactionOption) {\n      this.checkForRoot(\"withdrawExit\");\n\n      return this.exitUtil.buildPayloadForExit(\n          burnTransactionHash,\n          Log_Event_Signature.Erc721Transfer,\n          false,\n          index\n      ).then(payload => {\n          return this.rootChainManager.exit(\n              payload, option\n          );\n      });\n    }\n\n    // async withdrawExitMany(burnTransactionHash: string, option?: ITransactionOption) {\n    //     this.checkForRoot(\"withdrawExitMany\");\n\n    //     return this.exitUtil.buildMultiplePayloadsForExit(\n    //         burnTransactionHash,\n    //         Log_Event_Signature.Erc721BatchTransfer,\n    //         false\n    //     ).then(async payloads => {\n    //         const exitTxs = [];\n    //         if()\n    //         for(const i in payloads) {\n    //           exitTxs.push(this.rootChainManager.exit(\n    //             payloads[i], option\n    //         ));\n    //         }\n    //         return Promise.all(exitTxs);\n    //         });\n    // }\n\n    withdrawExitFaster(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitFaster\");\n\n        return this.exitUtil.buildPayloadForExit(\n            burnTransactionHash,\n            Log_Event_Signature.Erc721Transfer,\n            true\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n\n    // withdrawExitFasterMany(burnTransactionHash: string, option?: ITransactionOption) {\n    //     this.checkForRoot(\"withdrawExitFasterMany\");\n\n\n    //     return this.exitUtil.buildPayloadForExit(\n    //         burnTransactionHash,\n    //         Log_Event_Signature.Erc721BatchTransfer,\n    //         true\n    //     ).then(payload => {\n    //         return this.rootChainManager.exit(\n    //             payload, option\n    //         );\n    //     });\n    // }\n\n    isWithdrawExited(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc721Transfer\n        );\n    }\n\n    isWithdrawExitedMany(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc721BatchTransfer\n        );\n    }\n\n    isWithdrawExitedOnIndex(txHash: string, index: number) {\n        return this.isWithdrawnOnIndex(\n            txHash, index, Log_Event_Signature.Erc721Transfer\n        );\n    }\n\n    /**\n     * transfer to another user\n     *\n     * @param {string} tokenId\n     * @param {string} from\n     * @param {string} to\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC721\n     */\n    transfer(tokenId: string, from: string, to: string, option?: ITransactionOption) {\n        return this.transferERC721(\n            from,\n            to,\n            tokenId,\n            option\n        );\n    }\n\n}\n", "import { <PERSON><PERSON><PERSON><PERSON> } from \"./root_chain\";\nimport { Converter, ProofUtil, Web3SideChainClient } from \"../utils\";\nimport { BufferUtil } from \"../utils/buffer-utils\";\nimport rlp from \"rlp\";\nimport { IBlockWithTransaction, ITransactionReceipt } from \"../interfaces\";\nimport { service } from \"../services\";\nimport { BaseBigNumber, BaseWeb3Client } from \"../abstracts\";\nimport { ErrorHelper } from \"../utils/error_helper\";\nimport { ERROR_TYPE, IBaseClientConfig, IRootBlockInfo, utils } from \"..\";\n\ninterface IChainBlockInfo {\n    lastChildBlock: string;\n    txBlockNumber: number;\n}\n\n\n\nexport class ExitUtil {\n    private maticClient_: BaseWeb3Client;\n\n    rootChain: RootChain;\n\n    requestConcurrency: number;\n    config: IBaseClientConfig;\n\n    constructor(client: Web3SideChainClient<IBaseClientConfig>, rootChain: <PERSON><PERSON>hain) {\n        this.maticClient_ = client.child;\n        this.rootChain = rootChain;\n        const config = client.config;\n        this.config = config;\n        this.requestConcurrency = config.requestConcurrency;\n    }\n\n    private getLogIndex_(logEventSig: string, receipt: ITransactionReceipt) {\n        let logIndex = -1;\n\n        switch (logEventSig) {\n            case '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef':\n            case '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14':\n                logIndex = receipt.logs.findIndex(\n                    log =>\n                        log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[2].toLowerCase() === '******************************************000000000000000000000000'\n                );\n                break;\n\n            case '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62':\n            case '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb':\n                logIndex = receipt.logs.findIndex(\n                    log =>\n                        log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[3].toLowerCase() === '******************************************000000000000000000000000'\n                );\n                break;\n\n            default:\n                logIndex = receipt.logs.findIndex(log => log.topics[0].toLowerCase() === logEventSig.toLowerCase());\n        }\n        if (logIndex < 0) {\n            throw new Error(\"Log not found in receipt\");\n        }\n        return logIndex;\n    }\n\n    private getAllLogIndices_(logEventSig: string, receipt: ITransactionReceipt) {\n        let logIndices = [];\n\n        switch (logEventSig) {\n            case '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef':\n            case '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14':\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[2].toLowerCase() === '******************************************000000000000000000000000') &&\n                        logIndices.push(index), logIndices), []\n                );\n                break;\n\n            case '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62':\n            case '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb':\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[3].toLowerCase() === '******************************************000000000000000000000000') &&\n                        logIndices.push(index), logIndices), []\n                );\n                break;\n\n            case '0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df':\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef' &&\n                        log.topics[2].toLowerCase() === '******************************************000000000000000000000000') &&\n                        logIndices.push(index), logIndices), []\n                );\n                break;\n\n            default:\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === logEventSig.toLowerCase()) &&\n                        logIndices.push(index), logIndices), []\n                );\n        }\n        if (logIndices.length === 0) {\n            throw new Error(\"Log not found in receipt\");\n        }\n        return logIndices;\n    }\n\n    getChainBlockInfo(burnTxHash: string) {\n        return Promise.all([\n            this.rootChain.getLastChildBlock(),\n            this.maticClient_.getTransaction(burnTxHash),\n        ]).then(result => {\n            return {\n                lastChildBlock: result[0],\n                txBlockNumber: result[1].blockNumber\n            } as IChainBlockInfo;\n        });\n    }\n\n    private isCheckPointed_(data: IChainBlockInfo) {\n        // lastchild block is greater equal to transaction block number; \n        return new utils.BN(data.lastChildBlock).gte(\n            new utils.BN(data.txBlockNumber)\n        );\n    }\n\n    isCheckPointed(burnTxHash: string) {\n        return this.getChainBlockInfo(\n            burnTxHash\n        ).then(result => {\n            return this.isCheckPointed_(\n                result\n            );\n        });\n    }\n\n    /**\n     * returns info about block number existence on parent chain\n     * 1. root block number, \n     * 2. start block number, \n     * 3. end block number \n     *\n     * @private\n     * @param {number} txBlockNumber - transaction block number on child chain\n     * @return {*} \n     * @memberof ExitUtil\n     */\n    private getRootBlockInfo(txBlockNumber: number) {\n        // find in which block child was included in parent\n        let rootBlockNumber: BaseBigNumber;\n        return this.rootChain.findRootBlockFromChild(\n            txBlockNumber\n        ).then(blockNumber => {\n            rootBlockNumber = blockNumber;\n            return this.rootChain.method(\n                \"headerBlocks\",\n                Converter.toHex(blockNumber)\n            );\n        }).then(method => {\n            return method.read<IRootBlockInfo>();\n        }).then(rootBlockInfo => {\n            return {\n                // header block number - root block number in which child block exist \n                headerBlockNumber: rootBlockNumber,\n                // range of block\n                // end - block end number\n                end: rootBlockInfo.end.toString(),\n                // start - block start number\n                start: rootBlockInfo.start.toString(),\n            } as IRootBlockInfo;\n        });\n\n    }\n\n    private getRootBlockInfoFromAPI(txBlockNumber: number) {\n        this.maticClient_.logger.log(\"block info from API 1\");\n        return service.network.getBlockIncluded(\n            this.config.version,\n            txBlockNumber\n        ).then(headerBlock => {\n            this.maticClient_.logger.log(\"block info from API 2\", headerBlock);\n            if (!headerBlock || !headerBlock.start || !headerBlock.end || !headerBlock.headerBlockNumber) {\n                throw Error('Network API Error');\n            }\n            return headerBlock;\n        }).catch(err => {\n            this.maticClient_.logger.log(\"block info from API\", err);\n            return this.getRootBlockInfo(txBlockNumber);\n        });\n    }\n\n    private getBlockProof(txBlockNumber: number, rootBlockInfo: { start, end }) {\n        return ProofUtil.buildBlockProof(\n            this.maticClient_,\n            parseInt(rootBlockInfo.start, 10),\n            parseInt(rootBlockInfo.end, 10),\n            parseInt(txBlockNumber + '', 10)\n        );\n    }\n\n    private getBlockProofFromAPI(txBlockNumber: number, rootBlockInfo: { start, end }) {\n\n        return service.network.getProof(\n            this.config.version,\n            rootBlockInfo.start,\n            rootBlockInfo.end,\n            txBlockNumber\n        ).then(blockProof => {\n            if (!blockProof) {\n                throw Error('Network API Error');\n            }\n            this.maticClient_.logger.log(\"block proof from API 1\");\n            return blockProof;\n        }).catch(_ => {\n            return this.getBlockProof(txBlockNumber, rootBlockInfo);\n        });\n    }\n\n    private getExitProofFromAPI(burnHash: string, eventSignature: string) {\n\n        return service.network.getExitProof(\n            this.config.version, burnHash, eventSignature\n        ).then(exitProof => {\n            if (!exitProof) {\n                throw Error('Network API Error');\n            }\n            this.maticClient_.logger.log(\"exit proof from API 1\");\n            return exitProof;\n        }).catch(_ => {\n            return this.buildPayloadForExit(burnHash, eventSignature, false);\n        });\n    }\n\n    buildPayloadForExit(burnTxHash: string, logEventSig: string, isFast: boolean, index = 0) {\n\n        if (isFast && !service.network) {\n            new ErrorHelper(ERROR_TYPE.ProofAPINotSet).throw();\n        }\n\n        if (index < 0) {\n            throw new Error('Index must not be a negative integer');\n        }\n\n        let txBlockNumber: number,\n            rootBlockInfo: IRootBlockInfo,\n            receipt: ITransactionReceipt,\n            block: IBlockWithTransaction,\n            blockProof;\n\n        if (isFast) {\n            return this.getExitProofFromAPI(burnTxHash, logEventSig);\n        }\n\n        return this.getChainBlockInfo(\n            burnTxHash\n        ).then(blockInfo => {\n            if (!this.isCheckPointed_(blockInfo)) {\n                throw new Error(\n                    'Burn transaction has not been checkpointed as yet'\n                );\n            }\n\n            // step 1 - Get Block number from transaction hash\n            txBlockNumber = blockInfo.txBlockNumber;\n            // step 2-  get transaction receipt from txhash and \n            // block information from block number\n            return Promise.all([\n                this.maticClient_.getTransactionReceipt(burnTxHash),\n                this.maticClient_.getBlockWithTransaction(txBlockNumber)\n            ]);\n        }).then(result => {\n            [receipt, block] = result;\n            // step  3 - get information about block saved in parent chain \n            return this.getRootBlockInfo(txBlockNumber);\n        }).then(rootBlockInfoResult => {\n            rootBlockInfo = rootBlockInfoResult;\n            // step 4 - build block proof\n            return this.getBlockProof(txBlockNumber, rootBlockInfo);\n        }).then(blockProofResult => {\n            blockProof = blockProofResult;\n            // step 5- create receipt proof\n            return ProofUtil.getReceiptProof(\n                receipt,\n                block,\n                this.maticClient_,\n                this.requestConcurrency\n            );\n        }).then((receiptProof: any) => {\n            // step 6 - encode payload, convert into hex\n\n            // when token index is not 0\n            if (index > 0) {\n                const logIndices = this.getAllLogIndices_(\n                    logEventSig, receipt\n                );\n\n                if (index >= logIndices.length) {\n                    throw new Error('Index is greater than the number of tokens in this transaction');\n                }\n\n                return this.encodePayload_(\n                    rootBlockInfo.headerBlockNumber.toNumber(),\n                    blockProof,\n                    txBlockNumber,\n                    block.timestamp,\n                    Buffer.from(block.transactionsRoot.slice(2), 'hex'),\n                    Buffer.from(block.receiptsRoot.slice(2), 'hex'),\n                    ProofUtil.getReceiptBytes(receipt), // rlp encoded\n                    receiptProof.parentNodes,\n                    receiptProof.path,\n                    logIndices[index]\n                );\n            }\n\n            // when token index is 0\n            const logIndex = this.getLogIndex_(\n                logEventSig, receipt\n            );\n\n            return this.encodePayload_(\n                rootBlockInfo.headerBlockNumber.toNumber(),\n                blockProof,\n                txBlockNumber,\n                block.timestamp,\n                Buffer.from(block.transactionsRoot.slice(2), 'hex'),\n                Buffer.from(block.receiptsRoot.slice(2), 'hex'),\n                ProofUtil.getReceiptBytes(receipt), // rlp encoded\n                receiptProof.parentNodes,\n                receiptProof.path,\n                logIndex\n            );\n        });\n    }\n\n    buildMultiplePayloadsForExit(burnTxHash: string, logEventSig: string, isFast: boolean) {\n\n        if (isFast && !service.network) {\n            new ErrorHelper(ERROR_TYPE.ProofAPINotSet).throw();\n        }\n\n        let txBlockNumber: number,\n            rootBlockInfo: IRootBlockInfo,\n            receipt: ITransactionReceipt,\n            block: IBlockWithTransaction,\n            blockProof;\n\n        return this.getChainBlockInfo(\n            burnTxHash\n        ).then(blockInfo => {\n            if (!isFast && !this.isCheckPointed_(blockInfo)) {\n                throw new Error(\n                    'Burn transaction has not been checkpointed as yet'\n                );\n            }\n\n            // step 1 - Get Block number from transaction hash\n            txBlockNumber = blockInfo.txBlockNumber;\n            // step 2-  get transaction receipt from txhash and \n            // block information from block number\n            return Promise.all([\n                this.maticClient_.getTransactionReceipt(burnTxHash),\n                this.maticClient_.getBlockWithTransaction(txBlockNumber)\n            ]);\n        }).then(result => {\n            [receipt, block] = result;\n            // step  3 - get information about block saved in parent chain \n            return (\n                isFast ? this.getRootBlockInfoFromAPI(txBlockNumber) :\n                    this.getRootBlockInfo(txBlockNumber)\n            );\n        }).then(rootBlockInfoResult => {\n            rootBlockInfo = rootBlockInfoResult;\n            // step 4 - build block proof\n            return (\n                isFast ? this.getBlockProofFromAPI(txBlockNumber, rootBlockInfo) :\n                    this.getBlockProof(txBlockNumber, rootBlockInfo)\n            );\n        }).then(blockProofResult => {\n            blockProof = blockProofResult;\n            // step 5- create receipt proof\n            return ProofUtil.getReceiptProof(\n                receipt,\n                block,\n                this.maticClient_,\n                this.requestConcurrency\n            );\n        }).then((receiptProof: any) => {\n            const logIndices = this.getAllLogIndices_(\n                logEventSig, receipt\n            );\n            const payloads: string[] = [];\n\n            // step 6 - encode payloads, convert into hex\n            for (const logIndex of logIndices) {\n                payloads.push(\n                    this.encodePayload_(\n                        rootBlockInfo.headerBlockNumber.toNumber(),\n                        blockProof,\n                        txBlockNumber,\n                        block.timestamp,\n                        Buffer.from(block.transactionsRoot.slice(2), 'hex'),\n                        Buffer.from(block.receiptsRoot.slice(2), 'hex'),\n                        ProofUtil.getReceiptBytes(receipt), // rlp encoded\n                        receiptProof.parentNodes,\n                        receiptProof.path,\n                        logIndex\n                    )\n                );\n            }\n\n            return payloads;\n        });\n    }\n\n    private encodePayload_(\n        headerNumber,\n        buildBlockProof,\n        blockNumber,\n        timestamp,\n        transactionsRoot,\n        receiptsRoot,\n        receipt,\n        receiptParentNodes,\n        path,\n        logIndex\n    ) {\n        return BufferUtil.bufferToHex(\n            rlp.encode([\n                headerNumber,\n                buildBlockProof,\n                blockNumber,\n                timestamp,\n                BufferUtil.bufferToHex(transactionsRoot),\n                BufferUtil.bufferToHex(receiptsRoot),\n                BufferUtil.bufferToHex(receipt),\n                BufferUtil.bufferToHex(rlp.encode(receiptParentNodes) as Buffer),\n                BufferUtil.bufferToHex(Buffer.concat([Buffer.from('00', 'hex'), path])),\n                logIndex,\n            ]) as Buffer\n        );\n    }\n\n    getExitHash(burnTxHash, index, logEventSig) {\n        let lastChildBlock: string,\n            receipt: ITransactionReceipt,\n            block: IBlockWithTransaction;\n\n        return Promise.all([\n            this.rootChain.getLastChildBlock(),\n            this.maticClient_.getTransactionReceipt(burnTxHash)\n        ]).then(result => {\n            lastChildBlock = result[0];\n            receipt = result[1];\n            return this.maticClient_.getBlockWithTransaction(\n                receipt.blockNumber\n            );\n        }).then(blockResult => {\n            block = blockResult;\n            if (!this.isCheckPointed_({ lastChildBlock: lastChildBlock, txBlockNumber: receipt.blockNumber })) {\n                this.maticClient_.logger.error(ERROR_TYPE.BurnTxNotCheckPointed).throw();\n            }\n            return ProofUtil.getReceiptProof(\n                receipt,\n                block,\n                this.maticClient_,\n                this.requestConcurrency\n            );\n        }).then((receiptProof: any) => {\n            let logIndex;\n            const nibbleArr = [];\n            receiptProof.path.forEach(byte => {\n                nibbleArr.push(Buffer.from('0' + (byte / 0x10).toString(16), 'hex'));\n                nibbleArr.push(Buffer.from('0' + (byte % 0x10).toString(16), 'hex'));\n            });\n\n            if (index > 0) {\n                const logIndices = this.getAllLogIndices_(logEventSig, receipt);\n                logIndex = logIndices[index];\n            }\n\n            logIndex = this.getLogIndex_(logEventSig, receipt);\n\n            return this.maticClient_.etheriumSha3(\n                receipt.blockNumber, BufferUtil.bufferToHex(Buffer.concat(nibbleArr)), logIndex\n            );\n        });\n    }\n}\n", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\n\nexport class GasSwapper extends BaseToken<IPOSClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string) {\n        super({\n            address: address,\n            name: 'GasSwapper',\n            bridgeType: 'pos',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    depositWithGas(\n        tokenAddress: string,\n        depositAmount: string,\n        userAddress: string,\n        swapCallData: string,\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"swapAndBridge\",\n            tokenAddress,\n            depositAmount,\n            userAddress,\n            swapCallData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n}\n", "import { ERC20 } from \"./erc20\";\nimport { Root<PERSON>hainManager } from \"./root_chain_manager\";\nimport { BridgeClient } from \"../utils\";\nimport { IPOSClientConfig, IPOSContracts, ITransactionOption } from \"../interfaces\";\nimport { ExitUtil } from \"./exit_util\";\nimport { RootChain } from \"./root_chain\";\nimport { ERC721 } from \"./erc721\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { ERC1155 } from \"./erc1155\";\nimport { GasSwapper } from \"./gas_swapper\";\n\nexport * from \"./exit_util\";\nexport * from \"./root_chain_manager\";\nexport * from \"./root_chain\";\nexport * from \"./gas_swapper\";\n\nexport class POSClient extends BridgeClient<IPOSClientConfig> {\n\n    rootChainManager: RootChainManager;\n    gasSwapper: GasSwapper;\n\n    init(config: IPOSClientConfig) {\n        const client = this.client;\n\n        return client.init(config).then(_ => {\n            const mainPOSContracts = client.mainPOSContracts;\n            client.config = config = Object.assign(\n                {\n                    rootChainManager: mainPOSContracts.RootChainManagerProxy,\n                    rootChain: client.mainPlasmaContracts.RootChainProxy,\n                    gasSwapper: mainPOSContracts.GasSwapper\n                } as IPOSClientConfig,\n                config\n            );\n\n            this.rootChainManager = new RootChainManager(\n                this.client,\n                config.rootChainManager,\n            );\n\n            const rootChain = new RootChain(\n                this.client,\n                config.rootChain,\n            );\n\n            this.exitUtil = new ExitUtil(\n                this.client,\n                rootChain\n            );\n\n            this.gasSwapper = new GasSwapper(\n                this.client,\n                config.gasSwapper\n            );\n\n            return this;\n        });\n    }\n\n    erc20(tokenAddress, isParent?: boolean) {\n        return new ERC20(\n            tokenAddress,\n            isParent,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    erc721(tokenAddress, isParent?: boolean) {\n        return new ERC721(\n            tokenAddress,\n            isParent,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    erc1155(tokenAddress, isParent?: boolean) {\n        return new ERC1155(\n            tokenAddress,\n            isParent,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    depositEther(amount: TYPE_AMOUNT, userAddress: string, option: ITransactionOption) {\n        return new ERC20(\n            '', true, this.client,\n            this.getContracts_.bind(this),\n        )['depositEther_'](amount, userAddress, option);\n    }\n\n    depositEtherWithGas(\n        amount: TYPE_AMOUNT,\n        userAddress: string,\n        swapEthAmount: TYPE_AMOUNT,\n        swapCallData: string,\n        option: ITransactionOption\n    ) {\n        return new ERC20(\n            '', true, this.client,\n            this.getContracts_.bind(this),\n        )['depositEtherWithGas_'](amount, userAddress, swapEthAmount, swapCallData, option);\n    }\n\n    private getContracts_() {\n        return {\n            exitUtil: this.exitUtil,\n            rootChainManager: this.rootChainManager,\n            gasSwapper: this.gasSwapper\n        } as IPOSContracts;\n    }\n}", "import { BaseToken, Web3SideChainClient, promiseResolve } from \"../utils\";\nimport { IContractInitParam, IPOSClientConfig, ITransactionOption } from \"../interfaces\";\nimport { IPOSContracts } from \"../interfaces\";\n\nexport class POSToken extends BaseToken<IPOSClientConfig> {\n\n    private predicateAddress: string;\n\n    constructor(\n        contractParam: IContractInitParam,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        protected getPOSContracts: () => IPOSContracts\n    ) {\n        super(contractParam, client);\n    }\n\n    protected get rootChainManager() {\n        return this.getPOSContracts().rootChainManager;\n    }\n\n    protected get gasSwapper() {\n        return this.getPOSContracts().gasSwapper;\n    }\n\n    protected get exitUtil() {\n        return this.getPOSContracts().exitUtil;\n    }\n\n\n    getPredicateAddress(): Promise<string> {\n        if (this.predicateAddress) {\n            return promiseResolve(this.predicateAddress);\n        }\n        return this.rootChainManager.method(\n            \"tokenToType\",\n            this.contractParam.address\n        ).then(method => {\n            return method.read();\n        }).then(tokenType => {\n            if (!tokenType) {\n                throw new Error('Invalid Token Type');\n            }\n            return this.rootChainManager.method(\n                \"typeToPredicate\", tokenType\n            );\n        }).then(typeToPredicateMethod => {\n            return typeToPredicateMethod.read<string>();\n        }).then(predicateAddress => {\n            this.predicateAddress = predicateAddress;\n            return predicateAddress;\n        });\n    }\n\n    protected isWithdrawn(txHash: string, eventSignature: string) {\n        if (!txHash) {\n            throw new Error(`txHash not provided`);\n        }\n        return this.exitUtil.getExitHash(\n            txHash, 0, eventSignature\n        ).then(exitHash => {\n            return this.rootChainManager.isExitProcessed(\n                exitHash\n            );\n        });\n    }\n\n    protected isWithdrawnOnIndex(txHash: string, index: number, eventSignature: string) {\n      if (!txHash) {\n          throw new Error(`txHash not provided`);\n      }\n      return this.exitUtil.getExitHash(\n          txHash, index, eventSignature\n      ).then(exitHash => {\n          return this.rootChainManager.isExitProcessed(\n              exitHash\n          );\n      });\n  }\n\n    protected withdrawExitPOS(burnTxHash: string, eventSignature: string, isFast: boolean, option: ITransactionOption) {\n        return this.exitUtil.buildPayloadForExit(\n            burnTxHash,\n            eventSignature,\n            isFast\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n}\n", "import { BaseToken, utils, Web3SideChainClient } from \"../utils\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\nimport { BaseBigNumber } from \"..\";\n\nexport class <PERSON><PERSON>hain extends BaseToken<IPOSClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string) {\n        super({\n            address: address,\n            name: '<PERSON><PERSON>hai<PERSON>',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    getLastChildBlock() {\n        return this.method(\"getLastChildBlock\").then(method => {\n            return method.read<string>({}, this.client.config.rootChainDefaultBlock || 'safe');\n        });\n    }\n\n    async findRootBlockFromChild(childBlockNumber: TYPE_AMOUNT): Promise<BaseBigNumber> {\n        const bigOne = new utils.BN(1);\n        const bigtwo = new utils.BN(2);\n        const checkPointInterval = new utils.BN(10000);\n\n        childBlockNumber = new utils.BN(childBlockNumber);\n        // first checkpoint id = start * 10000\n        let start = bigOne;\n\n        // last checkpoint id = end * 10000\n        const method = await this.method(\"currentHeaderBlock\");\n        const currentHeaderBlock = await method.read<string>();\n        let end = new utils.BN(currentHeaderBlock).div(\n            checkPointInterval\n        );\n\n        // binary search on all the checkpoints to find the checkpoint that contains the childBlockNumber\n        let ans;\n        while (start.lte(end)) {\n            if (start.eq(end)) {\n                ans = start;\n                break;\n            }\n            const mid = start.add(end).div(bigtwo);\n            const headerBlocksMethod = await this.method(\n                \"headerBlocks\",\n                mid.mul(checkPointInterval).toString()\n            );\n            const headerBlock = await headerBlocksMethod.read<{ start: number, end: number }>();\n\n            const headerStart = new utils.BN(headerBlock.start);\n            const headerEnd = new utils.BN(headerBlock.end);\n\n            if (headerStart.lte(childBlockNumber) && childBlockNumber.lte(headerEnd)) {\n                // if childBlockNumber is between the upper and lower bounds of the headerBlock, we found our answer\n                ans = mid;\n                break;\n            } else if (headerStart.gt(childBlockNumber)) {\n                // childBlockNumber was checkpointed before this header\n                end = mid.sub(bigOne);\n            } else if (headerEnd.lt(childBlockNumber)) {\n                // childBlockNumber was checkpointed after this header\n                start = mid.add(bigOne);\n            }\n        }\n        return ans.mul(checkPointInterval);\n    }\n\n}\n", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\n\nexport class RootChainManager extends BaseToken<IPOSClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string) {\n        super({\n            address: address,\n            name: 'RootChainManager',\n            bridgeType: 'pos',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    deposit(userAddress: string, tokenAddress: string, depositData: string, option?: ITransactionOption) {\n        return this.method(\n            \"depositFor\",\n            userAddress,\n            tokenAddress,\n            depositData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    exit(exitPayload: string, option: ITransactionOption) {\n        return this.method(\"exit\", exitPayload).then(method => {\n            return this.processWrite(\n                method,\n                option\n            );\n        });\n    }\n\n    isExitProcessed(exitHash: string) {\n        return this.method(\n            \"processedExits\", exitHash\n        ).then(method => {\n            return this.processRead<boolean>(method);\n        });\n    }\n\n}\n", "import { HttpRequest } from \"../utils\";\n\nexport class ABIService {\n    httpRequest: HttpRequest;\n\n    constructor(baseUrl: string) {\n        this.httpRequest = new HttpRequest(baseUrl);\n    }\n\n    getABI(network: string, version: string, bridgeType: string, contractName: string) {\n        const url = `${network}/${version}/artifacts/${bridgeType}/${contractName}.json`;\n        return this.httpRequest.get(url).then((result: any) => {\n            return result.abi;\n        });\n    }\n\n    getAddress(network: string, version: string) {\n        const url = `${network}/${version}/index.json`;\n        return this.httpRequest.get(url);\n    }\n}\n", "import { ABIService } from \"./abi_service\";\nimport { config } from \"../config\";\nimport { NetworkService } from \"./network_service\";\n\nexport * from \"./network_service\";\n\nclass Service {\n    network: NetworkService;\n    zkEvmNetwork: NetworkService;\n    abi: ABIService;\n}\n\nexport const service = new Service();\nservice.abi = new ABIService(config.abiStoreUrl);\n\n\n\n", "import { BaseBigNumber, utils } from \"..\";\nimport { HttpRequest } from \"../utils\";\n\nexport class NetworkService {\n    httpRequest: HttpRequest;\n\n    constructor(baseUrl: string) {\n        this.httpRequest = new HttpRequest(baseUrl);\n    }\n\n    private createUrlForPos(version: string, url: string) {\n        return `${version === 'v1' ? 'matic' : version}${url}`;\n    }\n\n    private createUrlForZkEvm(version: string, url: string) {\n        return `${version}/${url}`;\n    }\n\n    getBlockIncluded(version: string, blockNumber: number) {\n        const url = this.createUrlForPos(version, `/block-included/${blockNumber}`);\n        return this.httpRequest.get<{\n            start: string;\n            end: string;\n            headerBlockNumber: BaseBigNumber;\n        }>(url).then(result => {\n            const headerBlockNumber = result.headerBlockNumber as any as string;\n            const decimalHeaderBlockNumber = headerBlockNumber.slice(0, 2) === '0x' ? parseInt(\n                headerBlockNumber, 16\n            ) : headerBlockNumber;\n            result.headerBlockNumber = new utils.BN(decimalHeaderBlockNumber);\n            return result;\n        });\n    }\n\n    getExitProof(version: string, burnTxHash: string, eventSignature: string) {\n        const url = this.createUrlForPos(version, `/exit-payload/${burnTxHash}?eventSignature=${eventSignature}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.result;\n        });\n    }\n\n    getProof(version: string, start, end, blockNumber) {\n        const url = this.createUrlForPos(version, `/fast-merkle-proof?start=${start}&end=${end}&number=${blockNumber}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.proof;\n        });\n    }\n\n    getMerkleProofForZkEvm(version: string, networkID: number, depositCount: number) {\n        const url = this.createUrlForZkEvm(version, `merkle-proof?net_id=${networkID}&deposit_cnt=${depositCount}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.proof;\n        });\n    }\n\n    getBridgeTransactionDetails(version: string, networkID: number, depositCount: number) {\n        const url = this.createUrlForZkEvm(version, `bridge?net_id=${networkID}&deposit_cnt=${depositCount}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.deposit;\n        });\n    }\n}\n", "import { BaseBigNumber } from \"../abstracts\";\n\nexport * from \"./pos_erc1155_deposit_param\";\nexport * from \"./pos_erc1155_transfer_param\";\n\nexport type TYPE_AMOUNT = BaseBigNumber | string | number;", "import { TYPE_AMOUNT } from \".\";\n\nexport type POSERC1155DepositParam = {\n    tokenId: TYPE_AMOUNT;\n    amount: TYPE_AMOUNT;\n    userAddress: string;\n    data?: string,\n};\n\nexport type POSERC1155DepositBatchParam = {\n    tokenIds: TYPE_AMOUNT[];\n    amounts: TYPE_AMOUNT[];\n    userAddress: string;\n    data?: string,\n};", "import { TYPE_AMOUNT } from \".\";\n\nexport type POSERC1155TransferParam = {\n    tokenId: TYPE_AMOUNT;\n    amount: TYPE_AMOUNT;\n    from: string;\n    to: string;\n    data?: string;\n};", "import { service } from \"../services\";\nimport { resolve, promiseResolve } from \".\";\n\ntype T_ABI_CACHE = {\n    [networkName: string]: {\n        [version: string]: {\n            address: any,\n            abi: {\n                [bridgeType: string]: {\n                    [contractName: string]: any\n                }\n            }\n        }\n    }\n};\n\nconst cache: T_ABI_CACHE = {};\n\nexport class ABIManager {\n    constructor(public networkName: string, public version: string) {\n\n    }\n\n    init() {\n        return service.abi.getAddress(\n            this.networkName, this.version\n        ).then(result => {\n            cache[this.networkName] = {\n                [this.version]: {\n                    address: result,\n                    abi: {}\n                }\n            };\n        });\n    }\n\n    getConfig(path: string) {\n        return resolve(\n            cache[this.networkName][this.version].address,\n            path\n        );\n    }\n\n    getABI(contractName: string, bridgeType = 'plasma'): Promise<any> {\n        let targetBridgeABICache;\n\n        if (\n            cache[this.networkName] && cache[this.networkName][this.version] &&\n            cache[this.networkName][this.version].abi\n        ) {\n            targetBridgeABICache = cache[this.networkName][this.version].abi[bridgeType];\n        }\n\n\n        if (targetBridgeABICache) {\n            const abiForContract = targetBridgeABICache[contractName];\n            if (abiForContract) {\n                return promiseResolve<any>(abiForContract);\n            }\n        }\n        return service.abi.getABI(\n            this.networkName,\n            this.version,\n            bridgeType,\n            contractName\n        ).then(result => {\n            this.setABI(contractName, bridgeType, result);\n            return result;\n        });\n    }\n\n    setABI(contractName: string, bridgeType: string, abi: any) {\n        const abiStore = cache[this.networkName][this.version].abi;\n        if (!abiStore[bridgeType]) {\n            abiStore[bridgeType] = {};\n        }\n        abiStore[bridgeType][contractName] = abi;\n    }\n}", "import { Web3SideChainClient } from \"./web3_side_chain_client\";\nimport { ITransactionRequestConfig, ITransactionOption, IContractInitParam, IBaseClientConfig, ITransactionWriteResult } from \"../interfaces\";\nimport { BaseContractMethod, BaseContract } from \"../abstracts\";\nimport { Converter, merge, utils } from \"../utils\";\nimport { promiseResolve } from \"./promise_resolve\";\nimport { ERROR_TYPE } from \"../enums\";\nimport { POSERC1155TransferParam, TYPE_AMOUNT } from \"../types\";\nimport { <PERSON>rror<PERSON>elper } from \"./error_helper\";\nimport { ADDRESS_ZERO } from '../constant';\n\nexport interface ITransactionConfigParam {\n    txConfig: ITransactionRequestConfig;\n    method?: BaseContractMethod;\n    isWrite?: boolean;\n    isParent?: boolean;\n}\n\nexport class BaseToken<T_CLIENT_CONFIG> {\n\n    private contract_: BaseContract;\n    private chainId_: number;\n\n    constructor(\n        protected contractParam: IContractInitParam,\n        protected client: Web3SideChainClient<T_CLIENT_CONFIG>,\n    ) {\n    }\n\n    get contractAddress() {\n        return this.contractParam.address;\n    }\n\n    getContract(): Promise<BaseContract> {\n        if (this.contract_) {\n            return promiseResolve<BaseContract>(this.contract_ as any);\n        }\n        const contractParam = this.contractParam;\n        return this.client.getABI(\n            contractParam.name,\n            contractParam.bridgeType,\n        ).then(abi => {\n            this.contract_ = this.getContract_({\n                abi,\n                isParent: contractParam.isParent,\n                tokenAddress: contractParam.address\n            });\n            return this.contract_;\n        });\n    }\n\n    getChainId(): Promise<number> {\n        if (this.chainId_) {\n            return promiseResolve<number>(this.chainId_ as any);\n        }\n        const client = this.getClient(this.contractParam.isParent);\n        return client.getChainId().then(chainId => {\n            this.chainId_ = chainId;\n            return this.chainId_;\n        });\n    }\n\n    protected processWrite(method: BaseContractMethod, option: ITransactionOption = {}): Promise<ITransactionWriteResult> {\n        this.validateTxOption_(option);\n\n        this.client.logger.log(\"process write\");\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: true,\n                method,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                this.client.logger.log(\"process write config\");\n                if (option.returnTransaction) {\n                    return merge(config, {\n                        data: method.encodeABI(),\n                        to: method.address\n                    } as ITransactionRequestConfig);\n                }\n                const methodResult = method.write(\n                    config,\n                );\n                return methodResult;\n            });\n    }\n\n    protected sendTransaction(option: ITransactionOption = {}): Promise<ITransactionWriteResult> {\n        this.validateTxOption_(option);\n\n        const isParent = this.contractParam.isParent;\n        const client = this.getClient(isParent);\n        client.logger.log(\"process write\");\n\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: true,\n                method: null as any,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                client.logger.log(\"process write config\");\n                if (option.returnTransaction) {\n                    return config as any;\n                }\n                const methodResult = client.write(\n                    config,\n                );\n                return methodResult;\n            });\n    }\n\n    protected readTransaction(option: ITransactionOption = {}): Promise<ITransactionWriteResult> {\n        this.validateTxOption_(option);\n        const isParent = this.contractParam.isParent;\n        const client = this.getClient(isParent);\n        client.logger.log(\"process read\");\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: true,\n                method: null as any,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                client.logger.log(\"write tx config created\");\n                if (option.returnTransaction) {\n                    return config as any;\n                }\n                return client.read(\n                    config,\n                );\n            });\n    }\n\n    private validateTxOption_(option: ITransactionOption) {\n        if (typeof option !== 'object' || Array.isArray(option)) {\n            new ErrorHelper(ERROR_TYPE.TransactionOptionNotObject).throw();\n        }\n    }\n\n    protected processRead<T>(method: BaseContractMethod, option: ITransactionOption = {}): Promise<T> {\n        this.validateTxOption_(option);\n        this.client.logger.log(\"process read\");\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: false,\n                method,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                this.client.logger.log(\"read tx config created\");\n                if (option.returnTransaction) {\n                    return merge(config, {\n                        data: method.encodeABI(),\n                        to: this.contract_.address\n                    } as ITransactionRequestConfig);\n                }\n                return method.read(\n                    config,\n                );\n            });\n    }\n\n    protected getClient(isParent) {\n        return isParent ? this.client.parent :\n            this.client.child;\n    }\n\n    private getContract_({ isParent, tokenAddress, abi }) {\n        const client = this.getClient(isParent);\n        return client.getContract(tokenAddress, abi);\n    }\n\n    protected get parentDefaultConfig() {\n        const config: IBaseClientConfig = this.client.config as any;\n        return config.parent.defaultConfig;\n    }\n\n    protected get childDefaultConfig() {\n        const config: IBaseClientConfig = this.client.config as any;\n        return config.child.defaultConfig;\n    }\n\n    protected createTransactionConfig({ txConfig, method, isParent, isWrite }: ITransactionConfigParam) {\n        const defaultConfig = isParent ? this.parentDefaultConfig : this.childDefaultConfig;\n        txConfig = merge(defaultConfig, (txConfig || {}));\n        const client = isParent ? this.client.parent :\n            this.client.child;\n        client.logger.log(\"txConfig\", txConfig, \"onRoot\", isParent, \"isWrite\", isWrite);\n        const estimateGas = async (config: ITransactionRequestConfig) => {\n            const result = method ? await method.estimateGas(config) : await client.estimateGas(config);\n            return new utils.BN(Math.trunc(Number(result) * 1.15)).toString();\n        };\n        // txConfig.chainId = Converter.toHex(txConfig.chainId) as any;\n        if (isWrite) {\n            return this.getChainId().then(clientChainId => {\n                const { maxFeePerGas, maxPriorityFeePerGas } = txConfig;\n\n                const isEIP1559Supported = this.client.isEIP1559Supported(clientChainId);\n                const isMaxFeeProvided = (maxFeePerGas || maxPriorityFeePerGas);\n                txConfig.chainId = txConfig.chainId || clientChainId;\n\n                if (!isEIP1559Supported && isMaxFeeProvided) {\n                    client.logger.error(ERROR_TYPE.EIP1559NotSupported, isParent).throw();\n                }\n                // const [gasLimit, nonce] = \n                return Promise.all([\n                    !(txConfig.gasLimit)\n                        ? estimateGas({\n                            from: txConfig.from, value: txConfig.value, to: txConfig.to\n                        })\n                        : txConfig.gasLimit,\n                    !txConfig.nonce ?\n                        client.getTransactionCount(txConfig.from, 'pending')\n                        : txConfig.nonce\n                ]).then(result => {\n                    const [gasLimit, nonce] = result;\n                    client.logger.log(\"options filled\");\n\n                    txConfig.gasLimit = Number(gasLimit);\n                    txConfig.nonce = nonce;\n                    return txConfig;\n                });\n            });\n        }\n        return promiseResolve<ITransactionRequestConfig>(txConfig);\n    }\n\n    protected transferERC20(to: string, amount: TYPE_AMOUNT, option?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"transfer\",\n                to,\n                Converter.toHex(amount)\n            );\n            return this.processWrite(\n                method, option\n            );\n        });\n    }\n\n    protected transferERC721(from: string, to: string, tokenId: string, option: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"transferFrom\",\n                from,\n                to,\n                tokenId\n            );\n            return this.processWrite(\n                method, option\n            );\n        });\n    }\n\n    protected checkForNonNative(methodName) {\n        if (this.contractParam.address === ADDRESS_ZERO) {\n            this.client.logger.error(ERROR_TYPE.AllowedOnNonNativeTokens, methodName).throw();\n        }\n    }\n\n    protected checkForRoot(methodName) {\n        if (!this.contractParam.isParent) {\n            this.client.logger.error(ERROR_TYPE.AllowedOnRoot, methodName).throw();\n        }\n    }\n\n    protected checkForChild(methodName) {\n        if (this.contractParam.isParent) {\n            this.client.logger.error(ERROR_TYPE.AllowedOnChild, methodName).throw();\n        }\n    }\n\n    protected checkAdapterPresent(methodName) {\n        if (!this.contractParam.bridgeAdapterAddress) {\n            this.client.logger.error(ERROR_TYPE.BridgeAdapterNotFound, methodName).throw();\n        }\n    }\n\n    protected transferERC1155(param: POSERC1155TransferParam, option: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"safeTransferFrom\",\n                param.from,\n                param.to,\n                Converter.toHex(param.tokenId),\n                Converter.toHex(param.amount),\n                param.data || '0x'\n            );\n            return this.processWrite(\n                method, option\n            );\n        });\n    }\n\n}\n", "import { Web3SideChainClient } from \"../utils\";\nimport { ExitUtil } from \"../pos\";\nimport { BaseToken, utils } from \"..\";\n\nexport class BridgeClient<T> {\n\n    client: Web3SideChainClient<T> = new Web3SideChainClient();\n\n    exitUtil: ExitUtil;\n\n    /**\n     * check whether a txHash is checkPointed \n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof BridgeClient\n     */\n    isCheckPointed(txHash: string) {\n        return this.exitUtil.isCheckPointed(\n            txHash\n        );\n    }\n\n    isDeposited(depositTxHash: string) {\n        const client = this.client;\n\n        const token = new BaseToken({\n            address: client.abiManager.getConfig(\"Matic.GenesisContracts.StateReceiver\"),\n            isParent: false,\n            name: 'StateReceiver',\n            bridgeType: 'genesis'\n        }, client);\n\n        return token.getContract().then(contract => {\n            return Promise.all([\n                client.parent.getTransactionReceipt(depositTxHash),\n                token['processRead']<string>(\n                    contract.method(\"lastStateId\")\n                )\n            ]);\n        }).then(result => {\n            const [receipt, lastStateId] = result;\n            const eventSignature = `0x103fed9db65eac19c4d870f49ab7520fe03b99f1838e5996caf47e9e43308392`;\n            const targetLog = receipt.logs.find(q => q.topics[0] === eventSignature);\n            if (!targetLog) {\n                throw new Error(\"StateSynced event not found\");\n            }\n            const rootStateId = client.child.decodeParameters(targetLog.topics[1], ['uint256'])[0];\n            const rootStateIdBN = utils.BN.isBN(rootStateId) ? rootStateId : new utils.BN(rootStateId);\n            return new utils.BN(lastStateId).gte(\n                rootStateIdBN\n            );\n        });\n    }\n\n}", "import {\n    ITransformableToArray,\n    PrefixedHexString,\n    ITransformableToBuffer,\n    BN\n} from \"./types\";\n\nexport type ToBufferInputTypes =\n    | PrefixedHexString\n    | number\n    | BN\n    | Buffer\n    | Uint8Array\n    | number[]\n    | ITransformableToArray\n    | ITransformableToBuffer\n    | null\n    | undefined;\n\nexport class BufferUtil {\n    static intToHex = function (i: number) {\n        if (!Number.isSafeInteger(i) || i < 0) {\n            throw new Error(`Received an invalid integer type: ${i}`);\n        }\n        return `0x${i.toString(16)}`;\n    };\n\n    static padToEven(value: string): string {\n        let a = value;\n\n        if (typeof a !== 'string') {\n            throw new Error(`[padToEven] value must be type 'string', received ${typeof a}`);\n        }\n\n        if (a.length % 2) a = `0${a}`;\n\n        return a;\n    }\n\n    static isHexPrefixed(str: string): boolean {\n        if (typeof str !== 'string') {\n            throw new Error(`[isHexPrefixed] input must be type 'string', received type ${typeof str}`);\n        }\n\n        return str[0] === '0' && str[1] === 'x';\n    }\n\n    static stripHexPrefix = (str: string): string => {\n        if (typeof str !== 'string') {\n            throw new Error(`[stripHexPrefix] input must be type 'string', received ${typeof str}`);\n        }\n\n        return BufferUtil.isHexPrefixed(str) ? str.slice(2) : str;\n    }\n\n    /**\n     * Converts an `Number` to a `Buffer`\n     * @param {Number} i\n     * @return {Buffer}\n     */\n    static intToBuffer = function (i: number) {\n        const hex = BufferUtil.intToHex(i);\n        return Buffer.from(BufferUtil.padToEven(hex.slice(2)), 'hex');\n    };\n\n    static isHexString(value: string, length?: number): boolean {\n        if (typeof value !== 'string' || !value.match(/^0x[0-9A-Fa-f]*$/)) return false;\n\n        if (length && value.length !== 2 + 2 * length) return false;\n\n        return true;\n    }\n\n\n    static toBuffer = function (v: ToBufferInputTypes): Buffer {\n        if (v === null || v === undefined) {\n            return Buffer.allocUnsafe(0);\n        }\n\n        if (Buffer.isBuffer(v)) {\n            return Buffer.from(v);\n        }\n\n        if (Array.isArray(v) || v instanceof Uint8Array) {\n            return Buffer.from(v as Uint8Array);\n        }\n\n        if (typeof v === 'string') {\n            if (!BufferUtil.isHexString(v)) {\n                throw new Error(\n                    `Cannot convert string to buffer. toBuffer only supports 0x-prefixed hex strings and this string was given: ${v}`\n                );\n            }\n            return Buffer.from(BufferUtil.padToEven(BufferUtil.stripHexPrefix(v)), 'hex');\n        }\n\n        if (typeof v === 'number') {\n            return BufferUtil.intToBuffer(v);\n        }\n\n        if (BN.isBN(v)) {\n            if (v.isNeg()) {\n                throw new Error(`Cannot convert negative BN to buffer. Given: ${v}`);\n            }\n            return v.toArrayLike(Buffer);\n        }\n\n        if (v.toArray) {\n            // converts a BN to a Buffer\n            return Buffer.from(v.toArray());\n        }\n\n        if (v.toBuffer) {\n            return Buffer.from(v.toBuffer());\n        }\n\n        throw new Error('invalid type');\n    };\n\n    /**\n     * Converts a `Buffer` into a `0x`-prefixed hex `String`.\n     * @param buf `Buffer` object to convert\n     */\n    static bufferToHex = function (buf: Buffer): string {\n        buf = BufferUtil.toBuffer(buf);\n        return '0x' + buf.toString('hex');\n    };\n}\n", "import { BaseBigNumber } from \"../abstracts\";\nimport { utils } from \"../utils\";\n\nexport class Converter {\n    static toHex(amount: BaseBigNumber | string | number) {\n        const dataType = typeof amount;\n        if (dataType === 'number') {\n            amount = new utils.BN(amount);\n        } else if (dataType === 'string') {\n            if ((amount as string).slice(0, 2) === '0x') {\n                return amount;\n            }\n            amount = new utils.BN(amount);\n        }\n        if (utils.BN.isBN(amount)) {\n            return '0x' + amount.toString(16);\n        }\n        else {\n            throw new Error(`Invalid value ${amount}, value is not a number.`);\n        }\n    }\n\n    static toBN(amount: BaseBigNumber | string | number): BaseBigNumber {\n        const dataType = typeof amount;\n        if (dataType === 'string') {\n            if ((amount as string).slice(0, 2) === '0x') {\n                amount = parseInt(amount as string, 16);\n            }\n        }\n        if (!utils.BN.isBN(amount)) {\n            amount = new utils.BN(amount);\n        }\n        return amount as BaseBigNumber;\n    }\n}\n", "import { ERROR_TYPE } from \"../enums\";\nimport { IError } from \"../interfaces\";\n\nexport class <PERSON>rrorHelper implements IError {\n    type: ERROR_TYPE;\n    message: string;\n\n    constructor(type: ERROR_TYPE, info?) {\n        this.type = type;\n        this.message = this.getMsg_(info);\n    }\n\n    throw() {\n        throw this.get();\n    }\n\n    get() {\n        return {\n            message: this.message,\n            type: this.type\n        } as IError;\n    }\n\n    private getMsg_(info) {\n        let errMsg: string;\n        switch (this.type) {\n            case ERROR_TYPE.AllowedOnChild:\n                errMsg = `The action ${info} is allowed only on child token.`;\n                break;\n            case ERROR_TYPE.AllowedOnRoot:\n                errMsg = `The action ${info} is allowed only on root token.`;\n                break;\n            case ERROR_TYPE.AllowedOnMainnet:\n                errMsg = `The action is allowed only on mainnet chains.`;\n                break;\n            case ERROR_TYPE.ProofAPINotSet:\n                errMsg = `Proof api is not set, please set it using \"setProofApi\"`;\n                break;\n            case ERROR_TYPE.BurnTxNotCheckPointed:\n                errMsg = `Burn transaction has not been checkpointed as yet`;\n                break;\n            case ERROR_TYPE.EIP1559NotSupported:\n                errMsg = `${info ? 'Root' : 'Child'} chain doesn't support eip-1559`;\n                break;\n            case ERROR_TYPE.NullSpenderAddress:\n                errMsg = `Please provide spender address.`;\n                break;\n            default:\n                if (!this.type) {\n                    this.type = ERROR_TYPE.Unknown;\n                }\n                errMsg = this.message;\n                break;\n        }\n        return errMsg;\n    }\n}", "export interface IEventBusPromise<T> extends Promise<T> {\n    on(event: string, cb: Function);\n    emit(event: string, ...args);\n    destroy();\n}\n\nexport const eventBusPromise = function <T>(executor: (resolve: (value?: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void) {\n    const promise: IEventBusPromise<T> = new Promise(executor) as any;\n    const eventBus = new EventBus();\n    promise.on = eventBus.on.bind(eventBus);\n    promise.emit = eventBus.emit.bind(eventBus);\n    return promise;\n};\n\nexport class EventBus {\n\n    constructor(ctx?) {\n        this._ctx = ctx;\n    }\n\n    private _ctx;\n\n    private _events: {\n        [key: string]: Function[]\n    } = {};\n\n    on(event: string, cb: Function) {\n        if (this._events[event] == null) {\n            this._events[event] = [];\n        }\n        this._events[event].push(cb);\n        return this;\n    }\n\n    off(event: string, cb: Function) {\n        if (this._events[event]) {\n            if (cb) {\n                const index = this._events[event].indexOf(cb);\n                this._events[event].splice(index, 1);\n            }\n            else {\n                this._events[event] = [];\n            }\n        }\n    }\n\n    emit(event: string, ...args) {\n        const events = this._events[event] || [];\n        return Promise.all(\n            events.map(cb => {\n                const result = cb.call(this._ctx, ...args);\n                return result && result.then ? result : Promise.resolve(result);\n            })\n        );\n    }\n\n    destroy() {\n        this._events = null;\n        this._ctx = null;\n    }\n}", "const fetch: (input: RequestInfo, init?: RequestInit) => Promise<Response> =\n    (() => {\n        if (process.env.BUILD_ENV === \"node\") {\n            return require('node-fetch').default;\n        }\n        return window.fetch;\n    })();\n\n\nexport class HttpRequest {\n    baseUrl = \"\";\n\n    constructor(option: { baseUrl: string } | string = {} as any) {\n        option = typeof option === \"string\" ? {\n            baseUrl: option\n        } : option;\n\n        if (option.baseUrl) {\n            this.baseUrl = option.baseUrl;\n        }\n    }\n\n    get<T>(url = \"\", query = {}): Promise<T> {\n        url = this.baseUrl + url + Object.keys(query).\n            map(key => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`).join('&');\n\n        return fetch(url, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            }\n        }).then(res => {\n            return res.json();\n        });\n    }\n\n    post(url = \"\", body) {\n        url = this.baseUrl + url;\n\n        return fetch(url, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            },\n            body: body ? JSON.stringify(body) : null\n        }).then(res => {\n            return res.json();\n        });\n    }\n}", "import { BaseWeb3Client } from \"../abstracts\";\nimport { EmptyBigNumber } from \"../implementation\";\nimport { Converter } from \"./converter\";\n\nexport * from \"./use\";\nexport * from \"./event_bus\";\nexport * from \"./logger\";\nexport * from \"./merge\";\nexport * from \"./map_promise\";\nexport * from \"./proof_util\";\nexport * from \"./http_request\";\nexport * from \"./converter\";\nexport * from \"./web3_side_chain_client\";\nexport * from \"./base_token\";\nexport * from \"./set_proof_api_url\";\nexport * from \"./resolve\";\nexport * from \"./promise_resolve\";\nexport * from \"./bridge_client\";\nexport * from \"./abi_manager\";\nexport * from \"./not_implemented\";\nexport * from \"./zkevm_bridge_client\";\nexport * from \"./buffer-utils\";\nexport * from \"./keccak\";\nexport * from \"./types\";\n\nexport const utils = {\n    converter: Converter,\n    Web3Client: BaseWeb3Client,\n    BN: EmptyBigNumber,\n    UnstoppableDomains: Object\n};\n", "import { keccak224, keccak384, keccak256 as k256, keccak512 } from 'ethereum-cryptography/keccak';\n\nexport class Keccak {\n    /**\n     * Throws if input is not a buffer\n     * @param {Buffer} input value to check\n     */\n    static assertIsBuffer = function (input: Buffer): void {\n        if (!Buffer.isBuffer(input)) {\n        const msg = `This method only supports <PERSON>uffer but input was: ${input}`;\n        throw new Error(msg);\n        }\n    };\n\n    /**\n     * Creates Keccak hash of a Buffer input\n     * @param a The input data (Buffer)\n     * @param bits (number = 256) The Keccak width\n     */\n    static keccak = function (a: Buffer, bits = 256): Buffer {\n        Keccak.assertIsBuffer(a);\n        switch (bits) {\n            case 224: {\n                return Buffer.from(keccak224(a));\n            }\n            case 256: {\n                return Buffer.from(k256(a));\n            }\n            case 384: {\n                return Buffer.from(keccak384(a));\n            }\n            case 512: {\n                return Buffer.from(keccak512(a));\n            }\n            default: {\n                throw new Error(`Invald algorithm: keccak${bits}`);\n            }\n        }\n    };\n\n    /**\n     * Creates Keccak-256 hash of the input, alias for keccak(a, 256).\n     * @param a The input data (Buffer)\n     */\n    static keccak256 = function (a: Buffer): Buffer {\n        return Keccak.keccak(a);\n    };\n}\n", "import { ERROR_TYPE } from \"../enums\";\nimport { <PERSON>rror<PERSON>elper } from \"./error_helper\";\n\nexport class Logger {\n\n    private isEnabled: boolean;\n\n    enableLog(value) {\n        this.isEnabled = value ? true : false;\n    }\n\n    log(...message) {\n        if (this.isEnabled) {\n            console.log(...message);\n        }\n    }\n\n    error(type: ERROR_TYPE, info?) {\n        return new ErrorHelper(type, info);\n    }\n}", "import { promiseResolve } from '..';\nimport { IMapPromiseOption } from '../interfaces';\n\nconst runPromises = (promises: Array<Promise<any>>, converter: Function) => {\n  const maps = promises.map((val, index) => {\n    return converter(val, index);\n  });\n  return Promise.all(maps);\n};\n\nexport function mapPromise(values: any[], converter: Function, option: IMapPromiseOption = {} as any) {\n  const valuesLength = values.length;\n  const concurrency = option.concurrency || valuesLength;\n\n  let result = [];\n  const limitPromiseRun: () => Promise<any> = () => {\n    const promises = values.splice(0, concurrency);\n    return runPromises(promises, converter).then(promiseResult => {\n      result = result.concat(promiseResult);\n\n      return valuesLength > result.length ?\n        limitPromiseRun() : promiseResolve(result);\n    });\n  };\n\n  return limitPromiseRun();\n}\n", "export const merge = (...obj) => {\n    return Object.assign({}, ...obj);\n};", "import { zeros } from '@ethereumjs/util';\nimport { Keccak } from './keccak';\nconst sha3 = Keccak.keccak256;\n\nimport { Buffer as SafeBuffer } from \"safe-buffer\";\n\nexport class MerkleTree {\n    leaves: any;\n    layers: any;\n\n    constructor(leaves = []) {\n        if (leaves.length < 1) {\n            throw new Error('Atleast 1 leaf needed');\n        }\n\n        const depth = Math.ceil(Math.log(leaves.length) / Math.log(2));\n        if (depth > 20) {\n            throw new Error('Depth must be 20 or less');\n        }\n\n        this.leaves = leaves.concat(\n            Array.from(\n                // tslint:disable-next-line\n                Array(Math.pow(2, depth) - leaves.length),\n                () => zeros(32)\n            )\n        );\n        this.layers = [this.leaves];\n        this.createHashes(this.leaves);\n    }\n\n    createHashes(nodes) {\n        if (nodes.length === 1) {\n            return false;\n        }\n\n\n        const treeLevel = [];\n        for (let i = 0; i < nodes.length; i += 2) {\n            const left = nodes[i];\n            const right = nodes[i + 1];\n\n            const data = SafeBuffer.concat([left, right]);\n            treeLevel.push(sha3(data as unknown as <PERSON>uffer));\n        }\n\n        // is odd number of nodes\n        if (nodes.length % 2 === 1) {\n            treeLevel.push(nodes[nodes.length - 1]);\n        }\n\n        this.layers.push(treeLevel);\n        this.createHashes(treeLevel);\n    }\n\n    getLeaves() {\n        return this.leaves;\n    }\n\n    getLayers() {\n        return this.layers;\n    }\n\n    getRoot() {\n        return this.layers[this.layers.length - 1][0];\n    }\n\n    getProof(leaf) {\n        let index = -1;\n        for (let i = 0; i < this.leaves.length; i++) {\n            if (SafeBuffer.compare(leaf, this.leaves[i]) === 0) {\n                index = i;\n            }\n        }\n\n        const proof = [];\n        if (index <= this.getLeaves().length) {\n            let siblingIndex;\n            for (let i = 0; i < this.layers.length - 1; i++) {\n                if (index % 2 === 0) {\n                    siblingIndex = index + 1;\n                } else {\n                    siblingIndex = index - 1;\n                }\n                index = Math.floor(index / 2);\n                proof.push(this.layers[i][siblingIndex]);\n            }\n        }\n        return proof;\n    }\n\n    verify(value, index, root, proof) {\n        if (!Array.isArray(proof) || !value || !root) {\n            return false;\n        }\n\n        let hash = value;\n        for (let i = 0; i < proof.length; i++) {\n            const node = proof[i];\n            if (index % 2 === 0) {\n                hash = sha3(SafeBuffer.concat([hash, node]) as unknown as Buffer);\n            } else {\n                hash = sha3(SafeBuffer.concat([node, hash]) as unknown as Buffer);\n            }\n\n            index = Math.floor(index / 2);\n        }\n\n        return SafeBuffer.compare(hash, root) === 0;\n    }\n}\n", "export const throwNotImplemented = <T>() => {\n    throw new Error(\"not implemented\");\n    return '' as any as T;\n};", "export const promiseResolve = <T>(value?) => {\n    return Promise.resolve<T>(value);\n};\n\nexport const promiseAny = (promisesArray) => {\n    const promiseErrors = new Array(promisesArray.length);\n    let counter = 0;\n\n    //return a new promise\n    return new Promise((resolve, reject) => {\n        promisesArray.forEach((promise) => {\n            Promise.resolve(promise)\n                .then(resolve) // resolve, when any of the input promise resolves\n                .catch((error) => {\n                    promiseErrors[counter] = error;\n                    counter = counter + 1;\n                    if (counter === promisesArray.length) {\n                        // all promises rejected, reject outer promise\n                        reject(promiseErrors);\n                    }\n                }); // reject, when any of the input promise rejects\n        });\n    });\n};\n", "import { BaseWeb3Client } from \"../abstracts\";\nimport { MerkleTree } from \"./merkle_tree\";\nimport { setLengthLeft } from \"@ethereumjs/util\";\nimport { Keccak } from \"./keccak\";\nimport { BufferUtil } from \"./buffer-utils\";\nimport rlp from \"rlp\";\nimport { ITransactionReceipt, IBlock, IBlockWithTransaction } from \"../interfaces\";\nimport { mapPromise } from \"./map_promise\";\nimport { Trie as TRIE } from '@ethereumjs/trie';\nimport { BlockHeader } from '@ethereumjs/block';\nimport { Converter, promiseResolve, utils } from \"..\";\nimport { Common, Chain, Hardfork } from '@ethereumjs/common';\n\n// Implementation adapted from <PERSON>'s `matic-proofs` library used under MIT License\n// https://github.com/TomAFrench/matic-proofs\n\nexport class ProofUtil {\n\n    static async getFastMerkleProof(\n        web3: BaseWeb3Client,\n        blockNumber: number,\n        startBlock: number,\n        endBlock: number\n    ): Promise<string[]> {\n        const merkleTreeDepth = Math.ceil(Math.log2(endBlock - startBlock + 1));\n\n        // We generate the proof root down, whereas we need from leaf up\n        const reversedProof: string[] = [];\n\n        const offset = startBlock;\n        const targetIndex = blockNumber - offset;\n        let leftBound = 0;\n        let rightBound = endBlock - offset;\n        //   console.log(\"Searching for\", targetIndex);\n        for (let depth = 0; depth < merkleTreeDepth; depth += 1) {\n            const nLeaves = 2 ** (merkleTreeDepth - depth);\n\n            // The pivot leaf is the last leaf which is included in the left subtree\n            const pivotLeaf = leftBound + nLeaves / 2 - 1;\n\n            if (targetIndex > pivotLeaf) {\n                // Get the root hash to the merkle subtree to the left\n                const newLeftBound = pivotLeaf + 1;\n                // eslint-disable-next-line no-await-in-loop\n                const subTreeMerkleRoot = await this.queryRootHash(web3, offset + leftBound, offset + pivotLeaf);\n                reversedProof.push(subTreeMerkleRoot);\n                leftBound = newLeftBound;\n            } else {\n                // Things are more complex when querying to the right.\n                // Root hash may come some layers down so we need to build a full tree by padding with zeros\n                // Some trees may be completely empty\n\n                const newRightBound = Math.min(rightBound, pivotLeaf);\n\n                // Expect the merkle tree to have a height one less than the current layer\n                const expectedHeight = merkleTreeDepth - (depth + 1);\n                if (rightBound <= pivotLeaf) {\n                    // Tree is empty so we repeatedly hash zero to correct height\n                    const subTreeMerkleRoot = this.recursiveZeroHash(expectedHeight, web3);\n                    reversedProof.push(subTreeMerkleRoot);\n                } else {\n                    // Height of tree given by RPC node\n                    const subTreeHeight = Math.ceil(Math.log2(rightBound - pivotLeaf));\n\n                    // Find the difference in height between this and the subtree we want\n                    const heightDifference = expectedHeight - subTreeHeight;\n\n                    // For every extra layer we need to fill 2*n leaves filled with the merkle root of a zero-filled Merkle tree\n                    // We need to build a tree which has heightDifference layers\n\n                    // The first leaf will hold the root hash as returned by the RPC\n                    // eslint-disable-next-line no-await-in-loop\n                    const remainingNodesHash = await this.queryRootHash(web3, offset + pivotLeaf + 1, offset + rightBound);\n\n                    // The remaining leaves will hold the merkle root of a zero-filled tree of height subTreeHeight\n                    const leafRoots = this.recursiveZeroHash(subTreeHeight, web3);\n\n                    // Build a merkle tree of correct size for the subtree using these merkle roots\n                    const leaves = Array.from({ length: 2 ** heightDifference }, () => BufferUtil.toBuffer(leafRoots));\n                    leaves[0] = remainingNodesHash;\n                    const subTreeMerkleRoot = new MerkleTree(leaves).getRoot();\n                    reversedProof.push(subTreeMerkleRoot);\n                }\n                rightBound = newRightBound;\n            }\n        }\n\n        return reversedProof.reverse();\n    }\n\n    static buildBlockProof(maticWeb3: BaseWeb3Client, startBlock: number, endBlock: number, blockNumber: number) {\n        return ProofUtil.getFastMerkleProof(\n            maticWeb3, blockNumber, startBlock, endBlock\n        ).then(proof => {\n            return BufferUtil.bufferToHex(\n                Buffer.concat(\n                    proof.map(p => {\n                        return BufferUtil.toBuffer(p);\n                    })\n                )\n            );\n        });\n    }\n\n    static queryRootHash(client: BaseWeb3Client, startBlock: number, endBlock: number) {\n        return client.getRootHash(startBlock, endBlock).then(rootHash => {\n            return BufferUtil.toBuffer(`0x${rootHash}`);\n        }).catch(_ => {\n            return null;\n        });\n    }\n\n    static recursiveZeroHash(n: number, client: BaseWeb3Client) {\n        if (n === 0) return '******************************************000000000000000000000000';\n        const subHash = this.recursiveZeroHash(n - 1, client);\n        return Keccak.keccak256(\n            BufferUtil.toBuffer(client.encodeParameters([subHash, subHash], ['bytes32', 'bytes32'],))\n        );\n    }\n\n    static getReceiptProof(receipt: ITransactionReceipt, block: IBlockWithTransaction, web3: BaseWeb3Client, requestConcurrency = Infinity, receiptsVal?: ITransactionReceipt[]) {\n        const stateSyncTxHash = BufferUtil.bufferToHex(ProofUtil.getStateSyncTxHash(block));\n        const receiptsTrie = new TRIE();\n        let receiptPromise: Promise<ITransactionReceipt[]>;\n        if (!receiptsVal) {\n            const receiptPromises = [];\n            block.transactions.forEach(tx => {\n                if (tx.transactionHash === stateSyncTxHash) {\n                    // ignore if tx hash is bor state-sync tx\n                    return;\n                }\n                receiptPromises.push(\n                    web3.getTransactionReceipt(tx.transactionHash)\n                );\n            });\n            receiptPromise = mapPromise(\n                receiptPromises,\n                val => {\n                    return val;\n                },\n                {\n                    concurrency: requestConcurrency,\n                }\n            );\n        }\n        else {\n            receiptPromise = promiseResolve(receiptsVal);\n        }\n\n        return receiptPromise.then(receipts => {\n            return Promise.all(\n                receipts.map(siblingReceipt => {\n                    const path = rlp.encode(siblingReceipt.transactionIndex);\n                    const rawReceipt = ProofUtil.getReceiptBytes(siblingReceipt);\n                    return receiptsTrie.put(path, rawReceipt);\n                })\n            );\n        }).then(_ => {\n            return receiptsTrie.findPath(rlp.encode(receipt.transactionIndex), true);\n        }).then(result => {\n            if (result.remaining.length > 0) {\n                throw new Error('Node does not contain the key');\n            }\n            // result.node.value\n            const prf = {\n                blockHash: BufferUtil.toBuffer(receipt.blockHash),\n                parentNodes: result.stack.map(s => s.raw()),\n                root: ProofUtil.getRawHeader(block).receiptTrie,\n                path: rlp.encode(receipt.transactionIndex),\n                value: ProofUtil.isTypedReceipt(receipt) ? result.node.value : rlp.decode(result.node.value.toString())\n            };\n            return prf;\n        });\n    }\n\n    static isTypedReceipt(receipt: ITransactionReceipt) {\n        const hexType = Converter.toHex(receipt.type);\n        return receipt.status != null && hexType !== \"0x0\" && hexType !== \"0x\";\n    }\n\n    // getStateSyncTxHash returns block's tx hash for state-sync receipt\n    // Bor blockchain includes extra receipt/tx for state-sync logs,\n    // but it is not included in transactionRoot or receiptRoot.\n    // So, while calculating proof, we have to exclude them.\n    //\n    // This is derived from block's hash and number\n    // state-sync tx hash = keccak256(\"matic-bor-receipt-\" + block.number + block.hash)\n    static getStateSyncTxHash(block): Buffer {\n        return Keccak.keccak256(\n            Buffer.concat([\n                // prefix for bor receipt\n                Buffer.from('matic-bor-receipt-', 'utf-8'),\n                setLengthLeft(BufferUtil.toBuffer(block.number), 8), // 8 bytes of block number (BigEndian)\n                BufferUtil.toBuffer(block.hash), // block hash\n            ])\n        );\n    }\n\n    static getReceiptBytes(receipt: ITransactionReceipt) {\n        let encodedData = rlp.encode([\n            BufferUtil.toBuffer(\n                receipt.status !== undefined && receipt.status != null ? (receipt.status ? '0x1' : '0x') : receipt.root\n            ),\n            BufferUtil.toBuffer(receipt.cumulativeGasUsed),\n            BufferUtil.toBuffer(receipt.logsBloom),\n            // encoded log array\n            receipt.logs.map(l => {\n                // [address, [topics array], data]\n                return [\n                    BufferUtil.toBuffer(l.address), // convert address to buffer\n                    l.topics.map(BufferUtil.toBuffer), // convert topics to buffer\n                    BufferUtil.toBuffer(l.data), // convert data to buffer\n                ];\n            }),\n        ]);\n        if (ProofUtil.isTypedReceipt(receipt)) {\n            encodedData = Buffer.concat([BufferUtil.toBuffer(receipt.type), encodedData]);\n        }\n        return encodedData;\n    }\n\n    static getRawHeader(_block) {\n        _block.difficulty = Converter.toHex(_block.difficulty) as any;\n        const common = new Common({\n            chain: Chain.Mainnet, hardfork: Hardfork.London\n        });\n        const rawHeader = BlockHeader.fromHeaderData(_block, {\n            common: common,\n            skipConsensusFormatValidation: true\n        });\n        return rawHeader;\n    }\n}\n", "export function resolve(obj, path) {\n    const properties = Array.isArray(path) ? path : path.split(\".\");\n    return properties.reduce((prev, curr) => prev && prev[curr], obj);\n}", "import { service, NetworkService } from \"../services\";\n\nexport const setProofApi = (url: string) => {\n    const urlLength = url.length;\n    if (url[urlLength - 1] !== '/') {\n        url += '/';\n    }\n    url += 'api/v1/';\n    service.network = new NetworkService(url);\n};\n\nexport const setZkEvmProofApi = (url: string) => {\n    const urlLength = url.length;\n    if (url[urlLength - 1] !== '/') {\n        url += '/';\n    }\n    url += 'api/zkevm/';\n    service.zkEvmNetwork = new NetworkService(url);\n};\n", "import BN from 'bn.js';\n\n/**\n * [`BN`](https://github.com/indutny/bn.js)\n */\nexport { BN };\n\n/*\n * A type that represents a `0x`-prefixed hex string.\n */\nexport type PrefixedHexString = string;\n\n/*\n * A type that represents an object that has a `toArray()` method.\n */\nexport interface ITransformableToArray {\n    toArray(): Uint8Array;\n    toBuffer?(): Buffer;\n}\n\n/*\n* A type that represents an object that has a `toBuffer()` method.\n*/\nexport interface ITransformableToBuffer {\n    toBuffer(): Buffer;\n    toArray?(): Uint8Array;\n}\n", "import { IPlugin } from \"../interfaces\";\nimport { defaultExport } from \"../default\";\n\nexport const use = (plugin, ...payload) => {\n    const pluginInstance: IPlugin = typeof plugin === \"function\" ? new plugin() : plugin;\n    return pluginInstance.setup(defaultExport, ...payload);\n};", "import { IBaseClientConfig } from \"../interfaces\";\nimport { BaseWeb3Client } from \"../abstracts\";\nimport { ABIManager } from \"../utils\";\nimport { Logger } from \"./logger\";\nimport { utils } from \"..\";\n\nconst chainIdToConfigPath = {\n    1: 'Main',\n    5: 'Main',\n    11155111: 'Main',\n    137: 'Matic',\n    80001: 'Matic',\n    80002: 'Matic',\n    1442: 'zkEVM',\n    2442: 'zkEVM',\n    1101: 'zkEVM'\n};\n\nexport class Web3SideChainClient<T_CONFIG> {\n    parent: BaseWeb3Client;\n    child: BaseWeb3Client;\n\n    config: T_CONFIG;\n\n    abiManager: ABIManager;\n\n    logger = new Logger();\n    resolution: {};\n\n    init(config: IBaseClientConfig) {\n        config = config || {} as any;\n        config.parent.defaultConfig = config.parent.defaultConfig || {} as any;\n        config.child.defaultConfig = config.child.defaultConfig || {} as any;\n        this.config = config as any;\n\n        // tslint:disable-next-line\n        const Web3Client = utils.Web3Client;\n\n        if (!Web3Client) {\n            throw new Error(\"Web3Client is not set\");\n        }\n\n        if (utils.UnstoppableDomains) {\n            this.resolution = utils.UnstoppableDomains;\n        }\n\n        this.parent = new (Web3Client as any)(config.parent.provider, this.logger);\n        this.child = new (Web3Client as any)(config.child.provider, this.logger);\n\n        this.logger.enableLog(config.log);\n\n        const network = config.network;\n        const version = config.version;\n        const abiManager = this.abiManager =\n            new ABIManager(network, version);\n        this.logger.log(\"init called\", abiManager);\n        return abiManager.init().catch(err => {\n            throw new Error(`network ${network} - ${version} is not supported`);\n        });\n    }\n\n    getABI(name: string, type?: string) {\n        return this.abiManager.getABI(name, type);\n    }\n\n    getConfig(path: string) {\n        return this.abiManager.getConfig(path);\n    }\n\n    get mainPlasmaContracts() {\n        return this.getConfig(\"Main.Contracts\");\n    }\n\n    get mainPOSContracts() {\n        return this.getConfig(\"Main.POSContracts\");\n    }\n\n    get mainZkEvmContracts() {\n        return this.getConfig(\"Main.Contracts\");\n    }\n\n    get zkEvmContracts() {\n        return this.getConfig(\"zkEVM.Contracts\");\n    }\n\n    isEIP1559Supported(chainId: number): boolean {\n        return this.getConfig(`${chainIdToConfigPath[chainId]}.SupportsEIP1559`);\n    }\n\n}\n", "import { Web3SideChainClient } from \".\";\nimport { BridgeUtil, ZkEvmBridge } from \"../zkevm\";\nimport { service } from \"../services\";\nimport { IBaseClientConfig } from \"..\";\n\nexport class ZkEvmBridgeClient {\n\n    client: Web3SideChainClient<IBaseClientConfig> = new Web3SideChainClient();\n    bridgeUtil: BridgeUtil;\n    rootChainBridge: ZkEvmBridge;\n    childChainBridge: ZkEvmBridge;\n\n    /**\n     * check whether a txHash is synced with child chain\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isDepositClaimable(txHash: string) {\n        return Promise.all([this.rootChainBridge.networkID(), this.bridgeUtil.getBridgeLogData(\n            txHash, true\n        )]).then(result => {\n            return service.zkEvmNetwork.getBridgeTransactionDetails(\n                this.client.config.version,\n                result[0],\n                result[1].depositCount\n            );\n        }).then(details => {\n            return details.ready_for_claim;\n        });\n    }\n\n    /**\n     * check whether proof is submitted on parent chain\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isWithdrawExitable(txHash: string) {\n        return Promise.all([this.childChainBridge.networkID(), this.bridgeUtil.getBridgeLogData(\n            txHash, false\n        )]).then(result => {\n            return service.zkEvmNetwork.getBridgeTransactionDetails(\n                this.client.config.version,\n                result[0],\n                result[1].depositCount\n            );\n        }).then(details => {\n            return details.ready_for_claim;\n        });\n    }\n\n    /**\n     * check whether deposit is completed\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isDeposited(txHash: string) {\n        return this.bridgeUtil.getBridgeLogData(\n            txHash, true\n        ).then(result => {\n            return this.childChainBridge.isClaimed(result.depositCount, 0);\n        });\n    }\n\n    /**\n     * check whether deposit is completed\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isExited(txHash: string) {\n        return this.bridgeUtil.getBridgeLogData(\n            txHash, false\n        ).then(result => {\n            return this.rootChainBridge.isClaimed(result.depositCount, 1);\n        });\n    }\n\n}\n", "import { Web3SideChainClient } from \"../utils\";\nimport { service } from \"../services\";\nimport { IBaseClientConfig, _GLOBAL_INDEX_MAINNET_FLAG } from \"..\";\nimport { TYPE_AMOUNT } from '../types';\n\ninterface IBridgeEventInfo {\n    originNetwork: number;\n    originTokenAddress: string;\n    destinationNetwork: number;\n    destinationAddress: string;\n    amount: TYPE_AMOUNT;\n    metadata: string;\n    depositCount: number;\n}\n\ninterface IMerkleProof {\n    merkle_proof: string[];\n    rollup_merkle_proof?: string[];\n    exit_root_num: string;\n    l2_exit_root_num: string;\n    main_exit_root: string;\n    rollup_exit_root: string;\n}\n\ninterface IClaimPayload {\n    smtProof: string[];\n    smtProofRollup: string[];\n    globalIndex: string;\n    mainnetExitRoot: string;\n    rollupExitRoot: string;\n    originNetwork: number;\n    originTokenAddress: string;\n    destinationNetwork: number;\n    destinationAddress: string;\n    amount: TYPE_AMOUNT;\n    metadata: string;\n}\n\nexport class BridgeUtil {\n    private client_: Web3SideChainClient<IBaseClientConfig>;\n    private BRIDGE_TOPIC = \"0x501781209a1f8899323b96b4ef08b168df93e0a90c673d1e4cce39366cb62f9b\";\n\n    constructor(client: Web3SideChainClient<IBaseClientConfig>) {\n        this.client_ = client;\n    }\n\n    private decodedBridgeData_(data: string, isParent: boolean) {\n        const client = isParent ? this.client_.parent : this.client_.child;\n        return this.client_.getABI(\"PolygonZkEVMBridge\", \"zkevm\").then(abi => {\n            const types = abi.filter(event => event.name === \"BridgeEvent\");\n            if (!types.length) {\n                throw new Error(\"Data not decoded\");\n            }\n            const decodedData = client.decodeParameters(data, types[0].inputs);\n            const [leafType, originNetwork, originTokenAddress, destinationNetwork, destinationAddress, amount, metadata, depositCount] = decodedData;\n            return {\n                leafType,\n                originNetwork,\n                originTokenAddress,\n                destinationNetwork,\n                destinationAddress,\n                amount,\n                metadata: metadata || '0x',\n                depositCount,\n            } as IBridgeEventInfo;\n        });\n    }\n\n    private getBridgeLogData_(transactionHash: string, isParent: boolean) {\n        const client = isParent ? this.client_.parent : this.client_.child;\n        return client.getTransactionReceipt(transactionHash)\n            .then(receipt => {\n                const logs = receipt.logs.filter(log => log.topics[0].toLowerCase() === this.BRIDGE_TOPIC);\n                if (!logs.length) {\n                    throw new Error(\"Log not found in receipt\");\n                }\n\n                const data = logs[0].data;\n                return this.decodedBridgeData_(data, isParent);\n            });\n    }\n\n    private getProof_(networkId: number, depositCount: number) {\n        return service.zkEvmNetwork.getMerkleProofForZkEvm(\n            this.client_.config.version,\n            networkId,\n            depositCount,\n        ).then(proof => {\n            return proof as IMerkleProof;\n        }).catch(_ => {\n            throw new Error(\"Error in creating proof\");\n        });\n    }\n\n    getBridgeLogData(transactionHash: string, isParent: boolean) {\n        return this.getBridgeLogData_(transactionHash, isParent);\n    }\n\n    computeGlobalIndex(indexLocal: number, indexRollup: number, sourceNetworkId: number) {\n        if (BigInt(sourceNetworkId) === BigInt(0)) {\n            return BigInt(indexLocal) + _GLOBAL_INDEX_MAINNET_FLAG;\n        } else {\n            return BigInt(indexLocal) + BigInt(indexRollup) * BigInt(2 ** 32);\n        }\n    }\n\n    buildPayloadForClaim(transactionHash: string, isParent: boolean, networkId: number) {\n        return this.getBridgeLogData_(transactionHash, isParent).then(data => {\n            const {\n                originNetwork,\n                originTokenAddress,\n                destinationNetwork,\n                destinationAddress,\n                amount,\n                metadata,\n                depositCount } = data;\n            return this.getProof_(networkId, depositCount).then(proof => {\n                const payload = {} as IClaimPayload;\n                payload.smtProof = proof.merkle_proof;\n                payload.smtProofRollup = proof.rollup_merkle_proof;\n                payload.globalIndex = this.computeGlobalIndex(depositCount, destinationNetwork, networkId).toString();\n                payload.mainnetExitRoot = proof.main_exit_root;\n                payload.rollupExitRoot = proof.rollup_exit_root;\n                payload.originNetwork = originNetwork;\n                payload.originTokenAddress = originTokenAddress;\n                payload.destinationNetwork = destinationNetwork;\n                payload.destinationAddress = destinationAddress;\n                payload.amount = amount;\n                payload.metadata = metadata;\n                return payload;\n            });\n        });\n    }\n}\n", "import { isHexString } from '@ethereumjs/util';\nimport { ITransactionOption } from \"../interfaces\";\nimport { Converter, Web3SideChainClient, promiseAny } from \"../utils\";\nimport { ZkEvmToken } from \"./zkevm_token\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { BaseContractMethod } from \"../abstracts\";\nimport { MAX_AMOUNT, ADDRESS_ZERO, DAI_PERMIT_TYPEHASH, EIP_2612_PERMIT_TYPEHASH, UNISWAP_DOMAIN_TYPEHASH, EIP_2612_DOMAIN_TYPEHASH, Permit, BaseContract, BaseWeb3Client, ERROR_TYPE } from '..';\nimport { IAllowanceTransactionOption, IApproveTransactionOption, IBridgeTransactionOption, IZkEvmClientConfig, IZkEvmContracts } from \"../interfaces\";\nimport { ZkEVMBridgeAdapter } from './zkevm_custom_bridge';\n\nexport class ERC20 extends ZkEvmToken {\n    private bridgeAdapter: ZkEVMBridgeAdapter;\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        bridgeAdapterAddress,\n        client: Web3SideChainClient<IZkEvmClientConfig>,\n        getContracts: () => IZkEvmContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            bridgeAdapterAddress,\n            name: 'ERC20',\n            bridgeType: 'zkevm'\n        }, client, getContracts);\n        if (bridgeAdapterAddress) {\n            this.bridgeAdapter = new ZkEVMBridgeAdapter(\n                this.client,\n                bridgeAdapterAddress,\n                isParent\n            );\n        }\n    }\n\n    /**\n     * get bridge for that token\n     *\n     * @returns\n     * @memberof ERC20\n     */\n    getBridgeAddress() {\n        const bridge = this.contractParam.isParent ? this.parentBridge : this.childBridge;\n        return bridge.contractAddress;\n    }\n\n    isEtherToken() {\n        return this.contractParam.address === ADDRESS_ZERO;\n    }\n\n    /**\n     * get token balance of user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    getBalance(userAddress: string, option?: ITransactionOption) {\n        if (this.isEtherToken()) {\n            const client = this.contractParam.isParent ? this.client.parent : this.client.child;\n            return client.getBalance(userAddress);\n        } else {\n            return this.getContract().then(contract => {\n                const method = contract.method(\n                    \"balanceOf\",\n                    userAddress\n                );\n                return this.processRead<string>(method, option);\n            });\n        }\n\n    }\n\n    /**\n     * is Approval needed to bridge tokens to other chains\n     *\n     * @returns\n     * @memberof ERC20\n     */\n    isApprovalNeeded() {\n        if (this.isEtherToken()) {\n            return false;\n        }\n\n        const bridge = this.contractParam.isParent ? this.parentBridge : this.childBridge;\n\n        return bridge.getOriginTokenInfo(this.contractParam.address)\n            .then(tokenInfo => {\n                return tokenInfo[1] === ADDRESS_ZERO;\n            });\n    }\n\n    /**\n     * get allowance of user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    getAllowance(userAddress: string, option: IAllowanceTransactionOption = {}) {\n        this.checkForNonNative(\"getAllowance\");\n        const spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"allowance\",\n                userAddress,\n                spenderAddress,\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    /**\n     * Approve given amount of tokens for user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {IApproveTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    approve(amount: TYPE_AMOUNT, option: IApproveTransactionOption = {}) {\n        this.checkForNonNative(\"approve\");\n        const spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"approve\",\n                spenderAddress,\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * Approve max amount of tokens for user\n     *\n     * @param {IApproveTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    approveMax(option: IApproveTransactionOption = {}) {\n        this.checkForNonNative(\"approveMax\");\n        return this.approve(\n            MAX_AMOUNT,\n            option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    deposit(amount: TYPE_AMOUNT, userAddress: string, option: IBridgeTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n        const permitData = option.permitData || '0x';\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        if (this.isEtherToken()) {\n            option.value = Converter.toHex(amount);\n        }\n\n        return this.childBridge.networkID().then(networkId => {\n            return this.parentBridge.bridgeAsset(\n                networkId,\n                userAddress,\n                amountInABI,\n                this.contractParam.address,\n                forceUpdateGlobalExitRoot,\n                permitData,\n                option\n            );\n        });\n    }\n\n    /**\n     * Deposit given amount of token for user along with ETH for gas token\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositWithGas(amount: TYPE_AMOUNT, userAddress: string, ethGasAmount: TYPE_AMOUNT, option: IBridgeTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        option.value = Converter.toHex(ethGasAmount);\n        if (option.v && option.r && option.s) {\n            return this.zkEVMWrapper.depositPermitWithGas(\n                this.contractParam.address,\n                amountInABI,\n                userAddress,\n                Math.floor((Date.now() + 3600000) / 1000).toString(),\n                option.v,\n                option.r,\n                option.s,\n                option\n            );\n        }\n        return this.zkEVMWrapper.depositWithGas(\n            this.contractParam.address,\n            amountInABI,\n            userAddress,\n            option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user along with ETH for gas token\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositPermitWithGas(amount: TYPE_AMOUNT, userAddress: string, ethGasAmount: TYPE_AMOUNT, option: IBridgeTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n        this.checkForNonNative(\"getPermitData\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        option.value = Converter.toHex(ethGasAmount);\n\n        return this.getPermitSignatureParams_(amount, this.zkEVMWrapper.contractAddress).then(\n            signatureParams => {\n                return this.zkEVMWrapper.depositPermitWithGas(\n                    this.contractParam.address,\n                    amountInABI,\n                    userAddress,\n                    Math.floor((Date.now() + 3600000) / 1000).toString(),\n                    signatureParams.v,\n                    signatureParams.r,\n                    signatureParams.s,\n                    option\n                );\n            }\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user with permit call\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositWithPermit(amount: TYPE_AMOUNT, userAddress: string, option: IApproveTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n        this.checkForNonNative(\"depositWithPermit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        return this.getPermitData(amountInABI, option).then(permitData => {\n            return this.childBridge.networkID().then(networkId => {\n                return this.parentBridge.bridgeAsset(\n                    networkId,\n                    userAddress,\n                    amountInABI,\n                    this.contractParam.address,\n                    forceUpdateGlobalExitRoot,\n                    permitData,\n                    option\n                );\n            });\n        });\n    }\n\n    /**\n     * Bridge asset to child chain using Custom ERC20 bridge Adapter\n     * @param amount\n     * @param userAddress\n     * @param forceUpdateGlobalExitRoot\n     * @returns\n     * @memberof ERC20\n     */\n    depositCustomERC20(amount: TYPE_AMOUNT, userAddress: string, forceUpdateGlobalExitRoot = true) {\n        // should be allowed to be used only in root chain\n        this.checkForRoot(\"depositCustomERC20\");\n        this.checkAdapterPresent(\"depositCustomERC20\");\n        // should not be allowed to use for native asset\n        this.checkForNonNative(\"depositCustomERC20\");\n        return this.bridgeAdapter.bridgeToken(userAddress, amount, forceUpdateGlobalExitRoot);\n    }\n\n    /**\n     * Claim asset on child chain bridged using custom bridge adapter on root chain\n     * @param transactionHash\n     * @param option\n     * @returns\n     * @memberof ERC20\n     */\n    customERC20DepositClaim(transactionHash: string, option?: ITransactionOption) {\n        this.checkForChild(\"customERC20DepositClaim\");\n        return this.parentBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                transactionHash, true, networkId\n            );\n        }).then(payload => {\n            return this.childBridge.claimMessage(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n\n    /**\n     * Complete deposit after GlobalExitRootManager is synced from Parent to root\n     *\n     * @param {string} transactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositClaim(transactionHash: string, option?: ITransactionOption) {\n        this.checkForChild(\"depositClaim\");\n        return this.parentBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                transactionHash, true, networkId\n            );\n        }).then(payload => {\n            return this.childBridge.claimAsset(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n    /**\n     * initiate withdraw by burning provided amount\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdraw(amount: TYPE_AMOUNT, userAddress: string, option: IBridgeTransactionOption = {}) {\n        this.checkForChild(\"withdraw\");\n        const permitData = option.permitData || '0x';\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        if (this.isEtherToken()) {\n            option.value = Converter.toHex(amount);\n        }\n\n        return this.parentBridge.networkID().then(networkId => {\n            return this.childBridge.bridgeAsset(\n                networkId,\n                userAddress,\n                amountInABI,\n                this.contractParam.address,\n                forceUpdateGlobalExitRoot,\n                permitData,\n                option\n            );\n        });\n    }\n\n    /**\n     * Bridge asset to root chain using Custom ERC20 bridge Adapter\n     * @param amount\n     * @param userAddress\n     * @param forceUpdateGlobalExitRoot\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawCustomERC20(amount: TYPE_AMOUNT, userAddress: string, forceUpdateGlobalExitRoot = true) {\n        // should be allowed to be used only in root chain\n        this.checkForChild(\"withdrawCustomERC20\");\n        this.checkAdapterPresent(\"depositCustomERC20\");\n        // should not be allowed to use for native asset\n        this.checkForNonNative(\"withdrawCustomERC20\");\n        return this.bridgeAdapter.bridgeToken(userAddress, amount, forceUpdateGlobalExitRoot);\n    }\n\n    /**\n     * Claim asset on root chain bridged using custom bridge adapter on child chain\n     * @param burnTransactionHash\n     * @param option\n     * @returns\n     * @memberof ERC20\n     */\n    customERC20WithdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"customERC20WithdrawExit\");\n        return this.childBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                burnTransactionHash, false, networkId\n            );\n        }).then(payload => {\n            return this.parentBridge.claimMessage(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n    /**\n     * initiate withdraw by transferring amount with PermitData for native tokens\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawWithPermit(amount: TYPE_AMOUNT, userAddress: string, option: IApproveTransactionOption = {}) {\n        this.checkForChild(\"withdraw\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        return this.getPermitData(amountInABI, option).then(permitData => {\n            return this.parentBridge.networkID().then(networkId => {\n                return this.childBridge.bridgeAsset(\n                    networkId,\n                    userAddress,\n                    amountInABI,\n                    this.contractParam.address,\n                    forceUpdateGlobalExitRoot,\n                    permitData,\n                    option\n                );\n            });\n        });\n    }\n\n    /**\n     * Complete deposit after GlobalExitRootManager is synced from Parent to root\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n        return this.childBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                burnTransactionHash, false, networkId\n            );\n        }).then(payload => {\n            return this.parentBridge.claimAsset(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n    /**\n     * transfer amount to another user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} to\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    transfer(amount: TYPE_AMOUNT, to: string, option: ITransactionOption = {}) {\n        if (this.contractParam.address === ADDRESS_ZERO) {\n            option.to = to;\n            option.value = Converter.toHex(amount);\n            return this.sendTransaction(option);\n        }\n        return this.transferERC20(to, amount, option);\n    }\n\n    /**\n     * get permitType of the token\n     *\n     * @returns\n     * @memberof ERC20\n     */\n    private getPermit() {\n        let contract: BaseContract;\n        return this.getContract().then(contractInstance => {\n            contract = contractInstance;\n            const method = contract.method(\n                \"PERMIT_TYPEHASH\",\n            );\n            return this.processRead<string>(method);\n        }).then(permitTypehash => {\n            switch (permitTypehash) {\n                case DAI_PERMIT_TYPEHASH: {\n                    return Permit.DAI;\n                }\n                case EIP_2612_PERMIT_TYPEHASH: {\n                    const DOMAIN_TYPEHASH = contract.method(\"DOMAIN_TYPEHASH\");\n                    const EIP712DOMAIN_HASH = contract.method(\"EIP712DOMAIN_HASH\");\n                    return promiseAny([this.processRead<string>(DOMAIN_TYPEHASH), this.processRead<string>(EIP712DOMAIN_HASH)]).then(\n                        (domainTypehash) => {\n                            switch (domainTypehash) {\n                                case EIP_2612_DOMAIN_TYPEHASH: {\n                                    return Permit.EIP_2612;\n                                }\n                                case UNISWAP_DOMAIN_TYPEHASH: {\n                                    return Permit.UNISWAP;\n                                }\n                                default: {\n                                    return Promise.reject(new Error(`Unsupported domain typehash: ${domainTypehash}`));\n                                }\n                            }\n                        }\n                    );\n                }\n                default: {\n                    return Promise.reject(new Error(`Unsupported permit typehash: ${permitTypehash}`));\n                }\n            }\n        });\n    }\n\n    /**\n     * get typedData for signing\n     * @param {string} permitType\n     * @param {string} account\n     * @param {number} chainId\n     * @param {string} name\n     * @param {string} nonce\n     * @param {string} spenderAddress\n     * @param {string} amount\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private getTypedData_(permitType: string, account: string, chainId: number, name: string, nonce: string, spenderAddress: string, amount: string) {\n        const typedData = {\n            types: {\n                EIP712Domain: [\n                    { name: 'name', type: 'string' },\n                    { name: 'version', type: 'string' },\n                    { name: 'chainId', type: 'uint256' },\n                    { name: 'verifyingContract', type: 'address' }\n                ],\n                Permit: []\n            },\n            primaryType: \"Permit\",\n            domain: {\n                name,\n                version: \"1\",\n                chainId,\n                verifyingContract: this.contractParam.address,\n            },\n            message: {}\n        };\n        switch (permitType) {\n            case Permit.DAI:\n                typedData.types.Permit = [\n                    { name: \"holder\", type: \"address\" },\n                    { name: \"spender\", type: \"address\" },\n                    { name: \"nonce\", type: \"uint256\" },\n                    { name: \"expiry\", type: \"uint256\" },\n                    { name: \"allowed\", type: \"bool\" },\n                ];\n                typedData.message = {\n                    holder: account,\n                    spender: spenderAddress,\n                    nonce,\n                    expiry: Math.floor((Date.now() + 3600000) / 1000),\n                    allowed: true,\n                };\n            case Permit.EIP_2612:\n            case Permit.UNISWAP:\n\n                if (permitType === Permit.UNISWAP) {\n                    typedData.types.EIP712Domain = [\n                        { name: 'name', type: 'string' },\n                        { name: 'chainId', type: 'uint256' },\n                        { name: 'verifyingContract', type: 'address' }\n                    ];\n                    delete typedData.domain.version;\n                }\n                typedData.types.Permit = [\n                    { name: 'owner', type: 'address' },\n                    { name: 'spender', type: 'address' },\n                    { name: 'value', type: 'uint256' },\n                    { name: 'nonce', type: 'uint256' },\n                    { name: 'deadline', type: 'uint256' }\n                ];\n                typedData.message = {\n                    owner: account,\n                    spender: spenderAddress,\n                    value: amount,\n                    nonce: nonce,\n                    deadline: Math.floor((Date.now() + 3600000) / 1000),\n                };\n        }\n        return typedData;\n    }\n\n    /**\n     * get {r, s, v} from signature\n     * @param {BaseWeb3Client} client\n     * @param {string} signature\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private getSignatureParameters_(client: BaseWeb3Client, signature: string) {\n        if (!isHexString(signature)) {\n            throw new Error(\n                'Given value \"'.concat(signature, '\" is not a valid hex string.'),\n            );\n        }\n\n        if (signature.slice(0, 2) !== '0x') {\n            signature = '0x'.concat(signature);\n        }\n\n        const r = signature.slice(0, 66);\n        const s = '0x'.concat(signature.slice(66, 130));\n        let v = client.hexToNumber('0x'.concat(signature.slice(130, 132)));\n        if (![27, 28].includes(v as any)) {\n            v += 27;\n        }\n        return {\n            r: r,\n            s: s,\n            v: v,\n        };\n    }\n\n    /**\n     * encode permit function data\n     * @param {BaseContract} contract\n     * @param {string} permitType\n     * @param {any} signatureParams\n     * @param {string} spenderAddress\n     * @param {string} account\n     * @param {string} nonce\n     * @param {string} amount\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private encodePermitFunctionData_(contract: BaseContract, permitType: string, signatureParams: any, spenderAddress: string, account: string, nonce: string, amount: string) {\n        const { r, s, v } = signatureParams;\n        let method: BaseContractMethod;\n        switch (permitType) {\n            case Permit.DAI:\n                method = contract.method(\n                    \"permit\",\n                    account,\n                    spenderAddress,\n                    nonce,\n                    Math.floor((Date.now() + 3600000) / 1000),\n                    true,\n                    v,\n                    r,\n                    s,\n                );\n                break;\n\n            case Permit.EIP_2612:\n            case Permit.UNISWAP:\n                method = contract.method(\n                    \"permit\",\n                    account,\n                    spenderAddress,\n                    amount,\n                    Math.floor((Date.now() + 3600000) / 1000),\n                    v,\n                    r,\n                    s,\n                );\n                break;\n        }\n        return method.encodeABI();\n    }\n\n    private getPermitSignatureParams_(amount: TYPE_AMOUNT, spenderAddress: string) {\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const client = this.contractParam.isParent ? this.client.parent : this.client.child;\n        let account: string;\n        let chainId: number;\n        let permitType: string;\n        let contract: BaseContract;\n        let nonce: string;\n\n        return Promise.all([client.name === 'WEB3' ? client.getAccountsUsingRPC_() : client.getAccounts(), this.getContract(), client.getChainId(), this.getPermit()]).then(result => {\n            account = result[0][0];\n            contract = result[1];\n            chainId = result[2];\n            permitType = result[3];\n            const nameMethod = contract.method(\"name\");\n            const nonceMethod = contract.method(\"nonces\", account);\n            return Promise.all([this.processRead<string>(nameMethod), this.processRead<string>(nonceMethod)]);\n        }).then(data => {\n            const name = data[0];\n            nonce = data[1];\n            return this.getTypedData_(permitType, account, chainId, name, nonce, spenderAddress, amountInABI);\n        }).then(typedData => {\n            return client.signTypedData(account, typedData);\n        }).then(signature => {\n            return this.getSignatureParameters_(client, signature);\n        });\n    }\n\n    /**\n     * Get permit data for given spender for given amount\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} spenderAddress\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private getPermitData_(amount: TYPE_AMOUNT, spenderAddress: string) {\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const client = this.contractParam.isParent ? this.client.parent : this.client.child;\n        let account: string;\n        let chainId: number;\n        let permitType: string;\n        let contract: BaseContract;\n        let nonce: string;\n\n        return Promise.all([client.name === 'WEB3' ? client.getAccountsUsingRPC_() : client.getAccounts(), this.getContract(), client.getChainId(), this.getPermit()]).then(result => {\n            account = result[0][0];\n            contract = result[1];\n            chainId = result[2];\n            permitType = result[3];\n            const nameMethod = contract.method(\"name\");\n            const nonceMethod = contract.method(\"nonces\", account);\n            return Promise.all([this.processRead<string>(nameMethod), this.processRead<string>(nonceMethod)]);\n        }).then(data => {\n            const name = data[0];\n            nonce = data[1];\n            return this.getTypedData_(permitType, account, chainId, name, nonce, spenderAddress, amountInABI);\n        }).then(typedData => {\n            return client.signTypedData(account, typedData);\n        }).then(signature => {\n            const signatureParameters = this.getSignatureParameters_(client, signature);\n            return this.encodePermitFunctionData_(\n                contract, permitType, signatureParameters, spenderAddress, account, nonce, amountInABI\n            );\n        });\n    }\n\n    /**\n     * Get permit data for given amount\n     * @param {TYPE_AMOUNT} amount\n     * @param {IApproveTransactionOption} option\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    getPermitData(amount: TYPE_AMOUNT, option: IApproveTransactionOption = {}) {\n        this.checkForNonNative(\"getPermitData\");\n\n        const spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();\n\n        return this.getPermitData_(amount, spenderAddress);\n    }\n}\n", "import { ERC20 } from \"./erc20\";\nimport { ZkEvmBridge } from \"./zkevm_bridge\";\nimport { BridgeUtil } from \"./bridge_util\";\nimport { ZkEvmBridgeClient } from \"../utils\";\nimport { IZkEvmClientConfig, IZkEvmContracts } from \"../interfaces\";\nimport { config as urlConfig } from \"../config\";\nimport { service, NetworkService } from \"../services\";\nimport { ZkEVMWrapper } from \"./zkevm_wrapper\";\n\nexport * from \"./zkevm_bridge\";\nexport * from \"./bridge_util\";\nexport * from \"./zkevm_wrapper\";\n\nexport class ZkEvmClient extends ZkEvmBridgeClient {\n\n    zkEVMWrapper: ZkEVMWrapper;\n\n    init(config: IZkEvmClientConfig) {\n        const client = this.client;\n\n        return client.init(config).then(_ => {\n            const mainZkEvmContracts = client.mainZkEvmContracts;\n            const zkEvmContracts = client.zkEvmContracts;\n            client.config = config = Object.assign(\n                {\n                    parentBridge: mainZkEvmContracts.PolygonZkEVMBridgeProxy,\n                    childBridge: zkEvmContracts.PolygonZkEVMBridge,\n                    zkEVMWrapper: mainZkEvmContracts.ZkEVMWrapper\n                } as IZkEvmClientConfig,\n                config\n            );\n\n            this.rootChainBridge = new ZkEvmBridge(\n                this.client,\n                config.parentBridge,\n                true\n            );\n\n            this.childChainBridge = new ZkEvmBridge(\n                this.client,\n                config.childBridge,\n                false\n            );\n\n            this.zkEVMWrapper = new ZkEVMWrapper(\n                this.client,\n                config.zkEVMWrapper\n            );\n\n            this.bridgeUtil = new BridgeUtil(\n                this.client\n            );\n\n            if (!service.zkEvmNetwork) {\n                if (urlConfig.zkEvmBridgeService[urlConfig.zkEvmBridgeService.length - 1] !== '/') {\n                    urlConfig.zkEvmBridgeService += '/';\n                }\n                urlConfig.zkEvmBridgeService += 'api/zkevm/';\n                service.zkEvmNetwork = new NetworkService(urlConfig.zkEvmBridgeService);\n            }\n\n            return this;\n        });\n    }\n\n    /**\n     * creates instance of ERC20 token\n     *\n     * @param {string} tokenAddress\n     * @param {boolean} isParent\n     *\n     * @param bridgeAdapterAddress Needed if a custom erc20 token is being bridged\n     * @returns\n     * @memberof ERC20\n     */\n    erc20(tokenAddress: string, isParent?: boolean, bridgeAdapterAddress?: string) {\n        return new ERC20(\n            tokenAddress,\n            isParent,\n            bridgeAdapterAddress,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    private getContracts_() {\n        return {\n            parentBridge: this.rootChainBridge,\n            childBridge: this.childChainBridge,\n            bridgeUtil: this.bridgeUtil,\n            zkEVMWrapper: this.zkEVMWrapper\n        } as IZkEvmContracts;\n    }\n}\n", "import { BaseToken, Web3SideChainClient, Converter, promiseResolve } from \"../utils\";\nimport { IZkEvmClientConfig, ITransactionOption } from \"../interfaces\";\nimport { TYPE_AMOUNT } from \"../types\";\n\nexport class ZkEvmBridge extends BaseToken<IZkEvmClientConfig> {\n\n    networkID_: number;\n\n    constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string, isParent: boolean) {\n        super({\n            address: address,\n            name: 'PolygonZkEVMBridge',\n            bridgeType: 'zkevm',\n            isParent: isParent\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    /**\n     * bridge function to be called on that network from where token is to be transferred to a different network\n     *\n     * @param {string} token Token address\n     * @param {number} destinationNetwork Network at which tokens will be bridged\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {TYPE_AMOUNT} amountamount amount of tokens\n     * @param {string} [permitData] Permit data to avoid approve call\n     * @param {ITransactionOption} [option] \n     * \n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    bridgeAsset(\n        destinationNetwork: number,\n        destinationAddress: string,\n        amount: TYPE_AMOUNT,\n        token: string,\n        forceUpdateGlobalExitRoot: boolean,\n        permitData = '0x',\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"bridgeAsset\",\n            destinationNetwork,\n            destinationAddress,\n            Converter.toHex(amount),\n            token,\n            forceUpdateGlobalExitRoot,\n            permitData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * Claim function to be called on the destination network\n     *\n     * @param {string[]} smtProof Merkle Proof\n     * @param {string[]} smtProofRollup Roll up Merkle Proof\n     * @param {string} globalIndex Global Index\n     * @param {string} mainnetExitRoot Mainnet Exit Root\n     * @param {string} rollupExitRoot RollUP Exit Root\n     * @param {number} originNetwork Network at which token was initially deployed\n     * @param {string} originTokenAddress Address of token at network where token was initially deployed\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {TYPE_AMOUNT} amount amount of tokens\n     * @param {string} [metadata] Metadata of token\n     * @param {ITransactionOption} [option]\n     * \n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    claimAsset(\n        smtProof: string[],\n        smtProofRollup: string[],\n        globalIndex: string,\n        mainnetExitRoot: string,\n        rollupExitRoot: string,\n        originNetwork: number,\n        originTokenAddress: string,\n        destinationNetwork: number,\n        destinationAddress: string,\n        amount: TYPE_AMOUNT,\n        metadata: string,\n        option: ITransactionOption\n    ) {\n        return this.method(\n            \"claimAsset\",\n            smtProof,\n            smtProofRollup,\n            globalIndex,\n            mainnetExitRoot,\n            rollupExitRoot,\n            originNetwork,\n            originTokenAddress,\n            destinationNetwork,\n            destinationAddress,\n            amount,\n            metadata\n        ).then(method => {\n            return this.processWrite(\n                method,\n                option\n            );\n        });\n    }\n\n    /**\n     * bridge function to be called on that network from where message is to be transferred to a different network\n     * @param {number} destinationNetwork Network at which tokens will be bridged\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {boolean} forceUpdateGlobalExitRoot Indicates if the new global exit root is updated or not\n     * @param {string} [permitData] Permit data to avoid approve call\n     * @param {ITransactionOption} [option]\n     *\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    bridgeMessage(\n        destinationNetwork: number,\n        destinationAddress: string,\n        forceUpdateGlobalExitRoot: boolean,\n        permitData = '0x',\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"bridgeMessage\",\n            destinationNetwork,\n            destinationAddress,\n            forceUpdateGlobalExitRoot,\n            permitData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * Claim Message new function to be called on the destination network\n     * If the receiving address is an EOA, the call will result as a success\n     * Which means that the amount of ether will be transferred correctly, but the message\n     * will not trigger any execution. this will work after Etrog changes\n     * @param {string[]} smtProof Merkle Proof\n     * @param {string[]} smtProofRollup Roll up Merkle Proof\n     * @param {string} globalIndex Global Index\n     * @param {string} mainnetExitRoot Mainnet Exit Root\n     * @param {string} rollupExitRoot RollUP Exit Root\n     * @param {number} originNetwork Network at which token was initially deployed\n     * @param {string} originTokenAddress Address of token at network where token was initially deployed\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {TYPE_AMOUNT} amount amount of tokens\n     * @param {string} [metadata] Metadata of token\n     * @param {ITransactionOption} [option]\n     *\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    claimMessage(\n        smtProof: string[],\n        smtProofRollup: string[],\n        globalIndex: string,\n        mainnetExitRoot: string,\n        rollupExitRoot: string,\n        originNetwork: number,\n        originTokenAddress: string,\n        destinationNetwork: number,\n        destinationAddress: string,\n        amount: TYPE_AMOUNT,\n        metadata: string,\n        option: ITransactionOption) {\n        return this.method(\n            \"claimMessage\",\n            smtProof,\n            smtProofRollup,\n            globalIndex,\n            mainnetExitRoot,\n            rollupExitRoot,\n            originNetwork,\n            originTokenAddress,\n            destinationNetwork,\n            destinationAddress,\n            amount,\n            metadata\n        ).then(method => {\n            return this.processWrite(\n                method,\n                option\n            );\n        });\n    }\n\n    /**\n     * get the address of token which is created by the bridge contract on the non origin chain\n     *\n     * @param {number} originNetwork Network at which the token was initially deployed\n     * @param {string} originTokenAddress Address at the network where token was initially deployed\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    getMappedTokenInfo(\n        originNetwork: number,\n        originTokenAddress: string\n    ) {\n        return this.method(\n            \"getTokenWrappedAddress\", originNetwork, originTokenAddress\n        ).then(method => {\n            return this.processRead<string>(method);\n        });\n    }\n\n    /**\n     * Tells if claim has already happed or not based on the deposit index\n     *\n     * @param {number} index\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    isClaimed(\n        index: number,\n        sourceBridgeNetwork: number\n    ) {\n        return this.method(\n            \"isClaimed\", index, sourceBridgeNetwork\n        ).then(method => {\n            return this.processRead<string>(method);\n        });\n    }\n\n    /**\n     * Even if the wrapped contract is not deployed on the destination chain, it will tell us the address which is going to be.\n     *\n     * @param {number} originNetwork Network at which the token was initially deployed\n     * @param {string} originTokenAddress Address at the network where token was initially deployed\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    precalculatedMappedTokenInfo(\n        originNetwork: number,\n        originTokenAddress: string\n    ) {\n        return this.method(\n            \"precalculatedWrapperAddress\", originNetwork, originTokenAddress\n        ).then(method => {\n            return this.processRead<string>(method);\n        });\n    }\n\n    /**\n     * get the address and network of the wrapped token where it was emerged initially\n     *\n     * @param {number} wrappedToken\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    getOriginTokenInfo(wrappedToken: string) {\n        return this.method(\n            \"wrappedTokenToTokenInfo\", wrappedToken\n        ).then(method => {\n            return this.processRead<[number, string]>(method);\n        });\n    }\n\n    /**\n     * get the network ID for chain in which the bridge contract is deployed\n     *\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    networkID() {\n        if (this.networkID_) {\n            return promiseResolve<number>(this.networkID_ as any);\n        }\n        return this.method(\n            \"networkID\"\n        ).then(method => {\n            return this.processRead<number>(method).then((networkId) => {\n                this.networkID_ = networkId;\n                return networkId;\n            });\n        });\n    }\n\n}\n", "import { ITransactionOption, IZkEvmClientConfig } from '../interfaces';\nimport { BaseToken, Converter, Web3SideChainClient } from '../utils';\nimport { TYPE_AMOUNT } from '../types';\n\n/**\n * ZkEVMBridgeAdapter used ZkEVMBridge to implement additional custom features\n * like bridging custom ERC20\n */\nexport class ZkEVMBridgeAdapter extends BaseToken<IZkEvmClientConfig> {\n\n  constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string, isParent: boolean) {\n    super(\n      {\n        address: address,\n        name: 'ZkEVMBridgeAdapter',\n        bridgeType: 'zkevm',\n        isParent: isParent, // decides if it's a child chain or a root chain adapter\n      },\n      client_,\n    );\n  }\n\n  method(methodName: string, ...args) {\n    return this.getContract().then(contract => {\n      return contract.method(methodName, ...args);\n    });\n  }\n\n  /**\n   * uses the bridge function present in the adapter contract\n   * @param recipient\n   * @param amount\n   * @param forceUpdateGlobalExitRoot\n   * @param option\n   *\n   * @returns\n   * @memberof ZkEvmCustomBridge\n   */\n  bridgeToken(\n    recipient: string,\n    amount: TYPE_AMOUNT,\n    forceUpdateGlobalExitRoot?: boolean,\n    option?: ITransactionOption,\n  ) {\n    return this.method('bridgeToken', recipient, Converter.toHex(amount), forceUpdateGlobalExitRoot).then(\n      method => {\n        return this.processWrite(method, option);\n      },\n    );\n  }\n}\n", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IContractInitParam, IZkEvmClientConfig } from \"../interfaces\";\nimport { IZkEvmContracts } from \"../interfaces\";\n\nexport class ZkEvmToken extends BaseToken<IZkEvmClientConfig> {\n\n    constructor(\n        contractParam: IContractInitParam,\n        client: Web3SideChainClient<IZkEvmClientConfig>,\n        protected getZkEvmContracts: () => IZkEvmContracts\n    ) {\n        super(contractParam, client);\n    }\n\n    protected get parentBridge() {\n        return this.getZkEvmContracts().parentBridge;\n    }\n\n    protected get zkEVMWrapper() {\n        return this.getZkEvmContracts().zkEVMWrapper;\n    }\n\n    protected get childBridge() {\n        return this.getZkEvmContracts().childBridge;\n    }\n\n    protected get bridgeUtil() {\n        return this.getZkEvmContracts().bridgeUtil;\n    }\n\n}\n", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IZkEvmClientConfig, ITransactionOption } from \"../interfaces\";\n\nexport class ZkEVMWrapper extends BaseToken<IZkEvmClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string) {\n        super({\n            address: address,\n            name: 'Zk<PERSON>VMWrapper',\n            bridgeType: 'zkevm',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    depositWithGas(\n        tokenAddress: string,\n        depositAmount: string,\n        userAddress: string,\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"deposit\",\n            tokenAddress,\n            depositAmount,\n            userAddress,\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    depositPermitWithGas(\n        tokenAddress: string,\n        depositAmount: string,\n        userAddress: string,\n        deadline: string,\n        v: number,\n        r: string,\n        s: string,\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"deposit\",\n            tokenAddress,\n            depositAmount,\n            userAddress,\n            deadline,\n            v,\n            r,\n            s\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_block__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_common__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_trie__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_util__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_bn_js__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_buffer__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_node_fetch__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_rlp__;", "function number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`positive integer expected, not ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`boolean expected, not ${b}`);\n}\n// copied from utils\nexport function isBytes(a) {\n    return (a instanceof Uint8Array ||\n        (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array'));\n}\nfunction bytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Uint8Array expected of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(h.outputLen);\n    number(h.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\nexport { number, bool, bytes, hash, exists, output };\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n//# sourceMappingURL=_assert.js.map", "const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for (let i = 0; i < lst.length; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\nexport { fromBig, split, toBig, shrSH, shrSL, rotrSH, rotrSL, rotrBH, rotrBL, rotr32H, rotr32L, rotlSH, rotlSL, rotlBH, rotlBL, add, add3L, add3H, add4L, add4H, add5H, add5L, };\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n//# sourceMappingURL=_u64.js.map", "export const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n//# sourceMappingURL=crypto.js.map", "import { bytes, exists, number, output } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport { Hash, u32, toBytes, wrapConstructor, wrapXOFConstructorWithOpts, isLE, byteSwap32, } from './utils.js';\n// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.\n// It's called a sponge function.\n// Various per round constants calculations\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ split(_SHA3_IOTA, true);\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n// Same as keccakf1600, but allows to skip some rounds\nexport function keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    B.fill(0);\n}\nexport class Keccak extends Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        // Can be passed from user as dkLen\n        number(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        if (0 >= this.blockLen || this.blockLen >= 200)\n            throw new Error('Sha3 supports only keccak-f1600 function');\n        this.state = new Uint8Array(200);\n        this.state32 = u32(this.state);\n    }\n    keccak() {\n        if (!isLE)\n            byteSwap32(this.state32);\n        keccakP(this.state32, this.rounds);\n        if (!isLE)\n            byteSwap32(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        exists(this);\n        const { blockLen, state } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        exists(this, false);\n        bytes(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        number(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        output(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        this.state.fill(0);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen) => wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\nexport const sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/**\n * SHA3-256 hash function\n * @param message - that would be hashed\n */\nexport const sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\nexport const sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\nexport const sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\nexport const keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/**\n * keccak-256 hash function. Different from SHA3-256.\n * @param message - that would be hashed\n */\nexport const keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\nexport const keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\nexport const keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\nconst genShake = (suffix, blockLen, outputLen) => wrapXOFConstructorWithOpts((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\nexport const shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\nexport const shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n//# sourceMappingURL=sha3.js.map", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nimport { bytes as abytes } from './_assert.js';\n// export { isBytes } from './_assert.js';\n// We can't reuse isBytes from _assert, because somehow this causes huge perf issues\nexport function isBytes(a) {\n    return (a instanceof Uint8Array ||\n        (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array'));\n}\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// The rotate left (circular left shift) operation for uint32\nexport const rotl = (word, shift) => (word << shift) | ((word >>> (32 - shift)) >>> 0);\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\n// The byte swap operation for uint32\nexport const byteSwap = (word) => ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff);\n// Conditionally byte swap if on a big-endian platform\nexport const byteSwapIfBE = isLE ? (n) => n : (n) => byteSwap(n);\n// In place byte swap for Uint32Array\nexport function byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, _A: 65, _F: 70, _a: 97, _f: 102 };\nfunction asciiToBase16(char) {\n    if (char >= asciis._0 && char <= asciis._9)\n        return char - asciis._0;\n    if (char >= asciis._A && char <= asciis._F)\n        return char - (asciis._A - 10);\n    if (char >= asciis._a && char <= asciis._f)\n        return char - (asciis._a - 10);\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// For runtime check if class implements interface\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "import { keccak_224, keccak_256, keccak_384, keccak_512 } from \"@noble/hashes/sha3\";\nimport { wrapHash } from \"./utils.js\";\nexport const keccak224 = wrapHash(keccak_224);\nexport const keccak256 = (() => {\n    const k = wrapHash(keccak_256);\n    k.create = keccak_256.create;\n    return k;\n})();\nexport const keccak384 = wrapHash(keccak_384);\nexport const keccak512 = wrapHash(keccak_512);\n", "import assert from \"@noble/hashes/_assert\";\nimport { hexToBytes as _hexToBytes } from \"@noble/hashes/utils\";\nconst assertBool = assert.bool;\nconst assertBytes = assert.bytes;\nexport { assertBool, assertBytes };\nexport { bytesToHex, bytesToHex as toHex, concatBytes, createView, utf8ToBytes } from \"@noble/hashes/utils\";\n// buf.toString('utf8') -> bytesToUtf8(buf)\nexport function bytesToUtf8(data) {\n    if (!(data instanceof Uint8Array)) {\n        throw new TypeError(`bytesToUtf8 expected Uint8Array, got ${typeof data}`);\n    }\n    return new TextDecoder().decode(data);\n}\nexport function hexToBytes(data) {\n    const sliced = data.startsWith(\"0x\") ? data.substring(2) : data;\n    return _hexToBytes(sliced);\n}\n// buf.equals(buf2) -> equalsBytes(buf, buf2)\nexport function equalsBytes(a, b) {\n    if (a.length !== b.length) {\n        return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n// Internal utils\nexport function wrapHash(hash) {\n    return (msg) => {\n        assert.bytes(msg);\n        return hash(msg);\n    };\n}\n// TODO(v3): switch away from node crypto, remove this unnecessary variable.\nexport const crypto = (() => {\n    const webCrypto = typeof globalThis === \"object\" && \"crypto\" in globalThis ? globalThis.crypto : undefined;\n    const nodeRequire = typeof module !== \"undefined\" &&\n        typeof module.require === \"function\" &&\n        module.require.bind(module);\n    return {\n        node: nodeRequire && !webCrypto ? nodeRequire(\"crypto\") : undefined,\n        web: webCrypto\n    };\n})();\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(\"./src/index.ts\");\n", ""], "names": [], "sourceRoot": ""}