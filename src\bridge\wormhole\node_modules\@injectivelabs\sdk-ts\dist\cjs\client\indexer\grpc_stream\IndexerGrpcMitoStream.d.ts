import { StreamStatusResponse } from '../types/index.js';
import { IndexerGrpcMitoStreamTransformer } from '../transformers/index.js';
import { Subscription } from 'rxjs';
import { MitoApi } from '@injectivelabs/mito-proto-ts';
export type TransfersStreamCallback = (response: ReturnType<typeof IndexerGrpcMitoStreamTransformer.transfersStreamCallback>) => void;
export type VaultStreamCallback = (response: ReturnType<typeof IndexerGrpcMitoStreamTransformer.vaultStreamCallback>) => void;
export type VaultHolderSubscriptionStreamCallback = (response: ReturnType<typeof IndexerGrpcMitoStreamTransformer.vaultHolderSubscriptionStreamCallback>) => void;
export type StakingRewardByAccountStreamCallback = (response: ReturnType<typeof IndexerGrpcMitoStreamTransformer.stakingRewardByAccountStreamCallback>) => void;
export type HistoricalStakingStreamCallback = (response: ReturnType<typeof IndexerGrpcMitoStreamTransformer.historicalStakingStreamCallback>) => void;
/**
 * @category Indexer Grpc Stream
 */
export declare class IndexerGrpcMitoStream {
    protected client: MitoApi.MitoAPIClientImpl;
    constructor(endpoint: string);
    streamTransfers({ vault, account, callback, onEndCallback, onStatusCallback, }: {
        vault?: string;
        account?: string;
        callback: TransfersStreamCallback;
        onEndCallback?: (status?: StreamStatusResponse) => void;
        onStatusCallback?: (status: StreamStatusResponse) => void;
    }): Subscription;
    streamVault({ vault, callback, onEndCallback, onStatusCallback, }: {
        vault?: string;
        callback: VaultStreamCallback;
        onEndCallback?: (status?: StreamStatusResponse) => void;
        onStatusCallback?: (status: StreamStatusResponse) => void;
    }): Subscription;
    streamVaultHolderSubscriptions({ holderAddress, vaultAddress, stakingContractAddress, callback, onEndCallback, onStatusCallback, }: {
        holderAddress: string;
        vaultAddress?: string;
        stakingContractAddress?: string;
        callback: VaultHolderSubscriptionStreamCallback;
        onEndCallback?: (status?: StreamStatusResponse) => void;
        onStatusCallback?: (status: StreamStatusResponse) => void;
    }): Subscription;
    streamStakingRewardsByAccount({ staker, callback, onEndCallback, onStatusCallback, stakingContractAddress, }: {
        staker: string;
        stakingContractAddress: string;
        callback: StakingRewardByAccountStreamCallback;
        onEndCallback?: (status?: StreamStatusResponse) => void;
        onStatusCallback?: (status: StreamStatusResponse) => void;
    }): Subscription;
    streamHistoricalStaking({ staker, stakingContractAddress, callback, onEndCallback, onStatusCallback, }: {
        staker: string;
        stakingContractAddress: string;
        callback: HistoricalStakingStreamCallback;
        onEndCallback?: (status?: StreamStatusResponse) => void;
        onStatusCallback?: (status: StreamStatusResponse) => void;
    }): Subscription;
}
