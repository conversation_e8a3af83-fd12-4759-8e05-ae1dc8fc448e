{"version": 3, "sources": ["../../src/client/get.ts"], "sourcesContent": ["import { AptosConfig } from \"../api/aptosConfig\";\nimport { aptosRequest } from \"./core\";\nimport { AptosResponse, AnyNumber, ClientConfig, MimeType } from \"../types\";\nimport { AptosApiType } from \"../utils/const\";\n\n/**\n * Options for making a GET request, including configuration for the API client.\n * @group Implementation\n * @category Client\n */\nexport type GetRequestOptions = {\n  /**\n   * The config for the API client\n   * @group Implementation\n   * @category Client\n   */\n  aptosConfig: AptosConfig;\n  /**\n   * The type of API endpoint to call e.g. fullnode, indexer, etc\n   * @group Implementation\n   * @category Client\n   */\n  type: AptosApiType;\n  /**\n   * The name of the API method\n   * @group Implementation\n   * @category Client\n   */\n  originMethod: string;\n  /**\n   * The URL path to the API method\n   * @group Implementation\n   * @category Client\n   */\n  path: string;\n  /**\n   * The content type of the request body\n   * @group Implementation\n   * @category Client\n   */\n  contentType?: MimeType;\n  /**\n   * The accepted content type of the response of the API\n   * @group Implementation\n   * @category Client\n   */\n  acceptType?: MimeType;\n  /**\n   * The query parameters for the request\n   * @group Implementation\n   * @category Client\n   */\n  params?: Record<string, string | AnyNumber | boolean | undefined>;\n  /**\n   * Specific client overrides for this request to override aptosConfig\n   * @group Implementation\n   * @category Client\n   */\n  overrides?: ClientConfig;\n};\n\n/**\n * Options for making a request to the Aptos API, excluding the \"type\" field.\n * @group Implementation\n * @category Client\n */\nexport type GetAptosRequestOptions = Omit<GetRequestOptions, \"type\">;\n\n/**\n * Executes a GET request to retrieve data based on the provided options.\n *\n * @param options - The options for the GET request.\n * @param options.aptosConfig - The configuration object for Aptos requests.\n * @param options.overrides - Optional overrides for the request configuration.\n * @param options.params - Query parameters to include in the request.\n * @param options.contentType - The content type of the request.\n * @param options.acceptType - The accepted response type.\n * @param options.path - The specific path for the request.\n * @param options.originMethod - The original method of the request.\n * @param options.type - The type of request being made.\n * @returns The response from the GET request.\n * @group Implementation\n * @category Client\n */\nexport async function get<Req extends {}, Res extends {}>(\n  options: GetRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig, overrides, params, contentType, acceptType, path, originMethod, type } = options;\n  const url = aptosConfig.getRequestUrl(type);\n\n  return aptosRequest<Req, Res>(\n    {\n      url,\n      method: \"GET\",\n      originMethod,\n      path,\n      contentType,\n      acceptType,\n      params,\n      overrides: {\n        ...aptosConfig.clientConfig,\n        ...overrides,\n      },\n    },\n    aptosConfig,\n    options.type,\n  );\n}\n\n/**\n * Retrieves data from the Aptos full node using the provided options.\n *\n * @param options - The options for the request to the Aptos full node.\n * @param options.aptosConfig - Configuration settings specific to the Aptos client and full node.\n * @param options.aptosConfig.clientConfig - The client configuration settings.\n * @param options.aptosConfig.fullnodeConfig - The full node configuration settings.\n * @param options.overrides - Additional overrides for the request.\n * @param options.type - The type of API request being made.\n *\n * @returns A promise that resolves with the response from the Aptos full node.\n * @group Implementation\n * @category Client\n */\nexport async function getAptosFullNode<Req extends {}, Res extends {}>(\n  options: GetAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  const { aptosConfig } = options;\n\n  return get<Req, Res>({\n    ...options,\n    type: AptosApiType.FULLNODE,\n    overrides: {\n      ...aptosConfig.clientConfig,\n      ...aptosConfig.fullnodeConfig,\n      ...options.overrides,\n      HEADERS: { ...aptosConfig.clientConfig?.HEADERS, ...aptosConfig.fullnodeConfig?.HEADERS },\n    },\n  });\n}\n\n/**\n * Makes a GET request to the Aptos Pepper service to retrieve data.\n *\n * @param options - The options for the request.\n * @param options.param1 - Description of param1.\n * @param options.param2 - Description of param2.\n * @returns AptosResponse - The response from the Aptos Pepper service.\n * @group Implementation\n * @category Client\n */\nexport async function getAptosPepperService<Req extends {}, Res extends {}>(\n  options: GetAptosRequestOptions,\n): Promise<AptosResponse<Req, Res>> {\n  return get<Req, Res>({ ...options, type: AptosApiType.PEPPER });\n}\n\n/**\n * This function is a helper for paginating using a function wrapping an API\n * @group Implementation\n * @category Client\n */\nexport async function paginateWithCursor<Req extends Record<string, any>, Res extends Array<{}>>(\n  options: GetAptosRequestOptions,\n): Promise<Res> {\n  const out: Res = new Array(0) as Res;\n  let cursor: string | undefined;\n  const requestParams = options.params as { start?: string; limit?: number };\n  do {\n    // eslint-disable-next-line no-await-in-loop\n    const response = await get<Req, Res>({\n      type: AptosApiType.FULLNODE,\n      aptosConfig: options.aptosConfig,\n      originMethod: options.originMethod,\n      path: options.path,\n      params: requestParams,\n      overrides: options.overrides,\n    });\n    /**\n     * the cursor is a \"state key\" from the API perspective. Client\n     * should not need to \"care\" what it represents but just use it\n     * to query the next chunk of data.\n     * @group Implementation\n     * @category Client\n     */\n    cursor = response.headers[\"x-aptos-cursor\"];\n    // Now that we have the cursor (if any), we remove the headers before\n    // adding these to the output of this function.\n    delete response.headers;\n    out.push(...response.data);\n    requestParams.start = cursor;\n  } while (cursor !== null && cursor !== undefined);\n  return out;\n}\n\n/// This function is a helper for paginating using a function wrapping an API using offset instead of start\nexport async function paginateWithObfuscatedCursor<Req extends Record<string, any>, Res extends Array<{}>>(\n  options: GetAptosRequestOptions,\n): Promise<Res> {\n  const out: Res = new Array(0) as Res;\n  let cursor: string | undefined;\n  const requestParams = options.params as { start?: string; limit?: number };\n  const totalLimit = requestParams.limit;\n  do {\n    // eslint-disable-next-line no-await-in-loop\n    const { response, cursor: newCursor } = await getPageWithObfuscatedCursor<Req, Res>({ ...options });\n\n    /**\n     * the cursor is a \"state key\" from the API perspective. Client\n     * should not need to \"care\" what it represents but just use it\n     * to query the next chunk of data.\n     */\n    cursor = newCursor;\n    out.push(...response.data);\n    if (options?.params) {\n      options.params.start = cursor;\n    }\n\n    // Re-evaluate length\n    if (totalLimit !== undefined) {\n      const newLimit = totalLimit - out.length;\n      if (newLimit <= 0) {\n        break;\n      }\n      requestParams.limit = newLimit;\n    }\n  } while (cursor !== null && cursor !== undefined);\n  return out;\n}\n\nexport async function getPageWithObfuscatedCursor<Req extends Record<string, any>, Res extends Array<{}>>(\n  options: GetAptosRequestOptions,\n): Promise<{ response: AptosResponse<Req, Res>; cursor: string | undefined }> {\n  let cursor: string | undefined;\n  let requestParams: { start?: string; limit?: number } = {};\n\n  // Drop any other values\n  // TODO: Throw error if cursor is not a string\n  if (typeof options.params?.cursor === \"string\") {\n    requestParams.start = options.params.cursor;\n  }\n  if (typeof options.params?.limit === \"number\") {\n    requestParams.limit = options.params.limit;\n  }\n\n  // eslint-disable-next-line no-await-in-loop\n  const response = await get<Req, Res>({\n    type: AptosApiType.FULLNODE,\n    aptosConfig: options.aptosConfig,\n    originMethod: options.originMethod,\n    path: options.path,\n    params: requestParams,\n    overrides: options.overrides,\n  });\n\n  /**\n   * the cursor is a \"state key\" from the API perspective. Client\n   * should not need to \"care\" what it represents but just use it\n   * to query the next chunk of data.\n   */\n  cursor = response.headers[\"x-aptos-cursor\"];\n  return { response, cursor };\n}\n"], "mappings": "yCAoFA,eAAsBA,EACpBC,EACkC,CAClC,GAAM,CAAE,YAAAC,EAAa,UAAAC,EAAW,OAAAC,EAAQ,YAAAC,EAAa,WAAAC,EAAY,KAAAC,EAAM,aAAAC,EAAc,KAAAC,CAAK,EAAIR,EACxFS,EAAMR,EAAY,cAAcO,CAAI,EAE1C,OAAOE,EACL,CACE,IAAAD,EACA,OAAQ,MACR,aAAAF,EACA,KAAAD,EACA,YAAAF,EACA,WAAAC,EACA,OAAAF,EACA,UAAW,CACT,GAAGF,EAAY,aACf,GAAGC,CACL,CACF,EACAD,EACAD,EAAQ,IACV,CACF,CAgBA,eAAsBW,EACpBX,EACkC,CAClC,GAAM,CAAE,YAAAC,CAAY,EAAID,EAExB,OAAOD,EAAc,CACnB,GAAGC,EACH,gBACA,UAAW,CACT,GAAGC,EAAY,aACf,GAAGA,EAAY,eACf,GAAGD,EAAQ,UACX,QAAS,CAAE,GAAGC,EAAY,cAAc,QAAS,GAAGA,EAAY,gBAAgB,OAAQ,CAC1F,CACF,CAAC,CACH,CAYA,eAAsBW,EACpBZ,EACkC,CAClC,OAAOD,EAAc,CAAE,GAAGC,EAAS,aAA0B,CAAC,CAChE,CAOA,eAAsBa,EACpBb,EACc,CACd,IAAMc,EAAW,IAAI,MAAM,CAAC,EACxBC,EACEC,EAAgBhB,EAAQ,OAC9B,EAAG,CAED,IAAMiB,EAAW,MAAMlB,EAAc,CACnC,gBACA,YAAaC,EAAQ,YACrB,aAAcA,EAAQ,aACtB,KAAMA,EAAQ,KACd,OAAQgB,EACR,UAAWhB,EAAQ,SACrB,CAAC,EAQDe,EAASE,EAAS,QAAQ,gBAAgB,EAG1C,OAAOA,EAAS,QAChBH,EAAI,KAAK,GAAGG,EAAS,IAAI,EACzBD,EAAc,MAAQD,CACxB,OAASA,GAAW,MACpB,OAAOD,CACT,CAGA,eAAsBI,EACpBlB,EACc,CACd,IAAMc,EAAW,IAAI,MAAM,CAAC,EACxBC,EACEC,EAAgBhB,EAAQ,OACxBmB,EAAaH,EAAc,MACjC,EAAG,CAED,GAAM,CAAE,SAAAC,EAAU,OAAQG,CAAU,EAAI,MAAMC,EAAsC,CAAE,GAAGrB,CAAQ,CAAC,EAclG,GAPAe,EAASK,EACTN,EAAI,KAAK,GAAGG,EAAS,IAAI,EACrBjB,GAAS,SACXA,EAAQ,OAAO,MAAQe,GAIrBI,IAAe,OAAW,CAC5B,IAAMG,EAAWH,EAAaL,EAAI,OAClC,GAAIQ,GAAY,EACd,MAEFN,EAAc,MAAQM,CACxB,CACF,OAASP,GAAW,MACpB,OAAOD,CACT,CAEA,eAAsBO,EACpBrB,EAC4E,CAC5E,IAAIe,EACAC,EAAoD,CAAC,EAIrD,OAAOhB,EAAQ,QAAQ,QAAW,WACpCgB,EAAc,MAAQhB,EAAQ,OAAO,QAEnC,OAAOA,EAAQ,QAAQ,OAAU,WACnCgB,EAAc,MAAQhB,EAAQ,OAAO,OAIvC,IAAMiB,EAAW,MAAMlB,EAAc,CACnC,gBACA,YAAaC,EAAQ,YACrB,aAAcA,EAAQ,aACtB,KAAMA,EAAQ,KACd,OAAQgB,EACR,UAAWhB,EAAQ,SACrB,CAAC,EAOD,OAAAe,EAASE,EAAS,QAAQ,gBAAgB,EACnC,CAAE,SAAAA,EAAU,OAAAF,CAAO,CAC5B", "names": ["get", "options", "aptosConfig", "overrides", "params", "contentType", "acceptType", "path", "originMethod", "type", "url", "aptosRequest", "getAptosFullNode", "getAptosPepperService", "paginateWithCursor", "out", "cursor", "requestParams", "response", "paginateWithObfuscatedCursor", "totalLimit", "newCursor", "getPageWithObfuscatedCursor", "newLimit"]}