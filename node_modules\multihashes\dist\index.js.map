{"version": 3, "sources": ["file:webpack/universalModuleDefinition", "file:webpack/bootstrap", "file:/Users/<USER>/code/pl/js-multihash/node_modules/node-libs-browser/node_modules/buffer/index.js", "file:/Users/<USER>/code/pl/js-multihash/src/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/webpack/buildin/global.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/base64-js/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/ieee754/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/isarray/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/constants.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/base-x/src/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/safe-buffer/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base16.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base32.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base64.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/encode.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/decode.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/length.js", "file:/Users/<USER>/code/pl/js-multihash/src/constants.js"], "names": ["<PERSON><PERSON><PERSON>", "require", "multibase", "varint", "cs", "exports", "names", "codes", "defaultLengths", "toHexString", "hash", "<PERSON><PERSON><PERSON><PERSON>", "Error", "toString", "fromHexString", "from", "toB58String", "encode", "slice", "fromB58String", "encoded", "decode", "buf", "length", "code", "isValidCode", "bytes", "len", "name", "digest", "undefined", "hashfn", "coerceCode", "concat", "isAppCode", "appCode", "validCode", "validate", "multihash", "prefix", "Object", "freeze"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;AClFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACa;;AAEb,aAAa,mBAAO,CAAC,CAAW;;AAEhC,cAAc,mBAAO,CAAC,CAAS;;AAE/B,cAAc,mBAAO,CAAC,CAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uBAAuB;AACvB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,mBAAmB,UAAU;AAC7B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;;AAEA;AACA;;AAEA;AACA,mBAAmB;;AAEnB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,iBAAiB;AAC9B;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,0BAA0B;;AAE1B;;AAEA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,2DAA2D;;AAE3D;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gDAAgD,EAAE;AAClD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;;;AAGA;AACA;AACA,qCAAqC;;AAErC;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA,2BAA2B;;AAE3B;AACA;AACA;AACA,GAAG;;;AAGH;;AAEA;AACA,uBAAuB;AACvB,GAAG;AACH,4BAA4B;AAC5B,GAAG;;;AAGH;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH,qBAAqB;;AAErB;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA,wBAAwB,QAAQ;AAChC;;AAEA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,GAAG;AACH;AACA;AACA,eAAe;AACf,GAAG;AACH;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;;AAEL,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,CAAC;AACD;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA,yDAAyD;AACzD,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,kBAAkB;AACnC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA,mBAAmB,cAAc;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;AACA,0CAA0C,iBAAiB;;AAE3D;AACA,yDAAyD;;AAEzD;AACA;AACA;;AAEA;AACA,+DAA+D;;AAE/D;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,qBAAqB,QAAQ;AAC7B;AACA;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA,EAAE;AACF;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;;;AAGH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,GAAG;AACH;AACA;;AAEA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA,EAAE;AACF;;;AAGA;;AAEA;AACA;AACA,uDAAuD;;AAEvD,gCAAgC;;AAEhC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B,qCAAqC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;;;AAGT;AACA;AACA,OAAO;;;AAGP;AACA;AACA;AACA;AACA,OAAO;;;AAGP;AACA,KAAK;AACL;AACA;AACA;;AAEA,yBAAyB;;AAEzB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,iBAAiB,gBAAgB;AACjC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;;AAEA;AACA;;AAEA;AACA,qBAAqB;AACrB,C;;;;;;;;;;;;;;;AC5zDA;;;;;AAKA;;AAEA,MAAM;AAAEA;AAAF,IAAaC,mBAAO,CAAC,CAAD,CAA1B;;AACA,MAAMC,SAAS,GAAGD,mBAAO,CAAC,CAAD,CAAzB;;AACA,MAAME,MAAM,GAAGF,mBAAO,CAAC,EAAD,CAAtB;;AACA,MAAMG,EAAE,GAAGH,mBAAO,CAAC,EAAD,CAAlB;;AAEAI,OAAO,CAACC,KAAR,GAAgBF,EAAE,CAACE,KAAnB;AACAD,OAAO,CAACE,KAAR,GAAgBH,EAAE,CAACG,KAAnB;AACAF,OAAO,CAACG,cAAR,GAAyBJ,EAAE,CAACI,cAA5B;AAEA;;;;;;;AAMAH,OAAO,CAACI,WAAR,GAAsB,SAASA,WAAT,CAAsBC,IAAtB,EAA4B;AAChD,MAAI,CAACV,MAAM,CAACW,QAAP,CAAgBD,IAAhB,CAAL,EAA4B;AAC1B,UAAM,IAAIE,KAAJ,CAAU,yBAAV,CAAN;AACD;;AAED,SAAOF,IAAI,CAACG,QAAL,CAAc,KAAd,CAAP;AACD,CAND;AAQA;;;;;;;;AAMAR,OAAO,CAACS,aAAR,GAAwB,SAASA,aAAT,CAAwBJ,IAAxB,EAA8B;AACpD,SAAOV,MAAM,CAACe,IAAP,CAAYL,IAAZ,EAAkB,KAAlB,CAAP;AACD,CAFD;AAIA;;;;;;;;AAMAL,OAAO,CAACW,WAAR,GAAsB,SAASA,WAAT,CAAsBN,IAAtB,EAA4B;AAChD,MAAI,CAACV,MAAM,CAACW,QAAP,CAAgBD,IAAhB,CAAL,EAA4B;AAC1B,UAAM,IAAIE,KAAJ,CAAU,yBAAV,CAAN;AACD;;AAED,SAAOV,SAAS,CAACe,MAAV,CAAiB,WAAjB,EAA8BP,IAA9B,EAAoCG,QAApC,GAA+CK,KAA/C,CAAqD,CAArD,CAAP;AACD,CAND;AAQA;;;;;;;;AAMAb,OAAO,CAACc,aAAR,GAAwB,SAASA,aAAT,CAAwBT,IAAxB,EAA8B;AACpD,MAAIU,OAAO,GAAGV,IAAd;;AACA,MAAIV,MAAM,CAACW,QAAP,CAAgBD,IAAhB,CAAJ,EAA2B;AACzBU,WAAO,GAAGV,IAAI,CAACG,QAAL,EAAV;AACD;;AAED,SAAOX,SAAS,CAACmB,MAAV,CAAiB,MAAMD,OAAvB,CAAP;AACD,CAPD;AASA;;;;;;;;AAMAf,OAAO,CAACgB,MAAR,GAAiB,SAASA,MAAT,CAAiBC,GAAjB,EAAsB;AACrC,MAAI,CAAEtB,MAAM,CAACW,QAAP,CAAgBW,GAAhB,CAAN,EAA6B;AAC3B,UAAM,IAAIV,KAAJ,CAAU,4BAAV,CAAN;AACD;;AAED,MAAIU,GAAG,CAACC,MAAJ,GAAa,CAAjB,EAAoB;AAClB,UAAM,IAAIX,KAAJ,CAAU,yCAAV,CAAN;AACD;;AAED,QAAMY,IAAI,GAAGrB,MAAM,CAACkB,MAAP,CAAcC,GAAd,CAAb;;AACA,MAAI,CAACjB,OAAO,CAACoB,WAAR,CAAoBD,IAApB,CAAL,EAAgC;AAC9B,UAAM,IAAIZ,KAAJ,8CAAgDY,IAAI,CAACX,QAAL,CAAc,EAAd,CAAhD,EAAN;AACD;;AACDS,KAAG,GAAGA,GAAG,CAACJ,KAAJ,CAAUf,MAAM,CAACkB,MAAP,CAAcK,KAAxB,CAAN;AAEA,QAAMC,GAAG,GAAGxB,MAAM,CAACkB,MAAP,CAAcC,GAAd,CAAZ;;AACA,MAAIK,GAAG,GAAG,CAAV,EAAa;AACX,UAAM,IAAIf,KAAJ,qCAAuCe,GAAvC,EAAN;AACD;;AACDL,KAAG,GAAGA,GAAG,CAACJ,KAAJ,CAAUf,MAAM,CAACkB,MAAP,CAAcK,KAAxB,CAAN;;AAEA,MAAIJ,GAAG,CAACC,MAAJ,KAAeI,GAAnB,EAAwB;AACtB,UAAM,IAAIf,KAAJ,4CAA8CU,GAAG,CAACT,QAAJ,CAAa,KAAb,CAA9C,EAAN;AACD;;AAED,SAAO;AACLW,QAAI,EAAEA,IADD;AAELI,QAAI,EAAExB,EAAE,CAACG,KAAH,CAASiB,IAAT,CAFD;AAGLD,UAAM,EAAEI,GAHH;AAILE,UAAM,EAAEP;AAJH,GAAP;AAMD,CA/BD;AAiCA;;;;;;;;;;;;AAUAjB,OAAO,CAACY,MAAR,GAAiB,SAASA,MAAT,CAAiBY,MAAjB,EAAyBL,IAAzB,EAA+BD,MAA/B,EAAuC;AACtD,MAAI,CAACM,MAAD,IAAWL,IAAI,KAAKM,SAAxB,EAAmC;AACjC,UAAM,IAAIlB,KAAJ,CAAU,2DAAV,CAAN;AACD,GAHqD,CAKtD;;;AACA,QAAMmB,MAAM,GAAG1B,OAAO,CAAC2B,UAAR,CAAmBR,IAAnB,CAAf;;AAEA,MAAI,CAAExB,MAAM,CAACW,QAAP,CAAgBkB,MAAhB,CAAN,EAAgC;AAC9B,UAAM,IAAIjB,KAAJ,CAAU,2BAAV,CAAN;AACD;;AAED,MAAIW,MAAM,IAAI,IAAd,EAAoB;AAClBA,UAAM,GAAGM,MAAM,CAACN,MAAhB;AACD;;AAED,MAAIA,MAAM,IAAIM,MAAM,CAACN,MAAP,KAAkBA,MAAhC,EAAwC;AACtC,UAAM,IAAIX,KAAJ,CAAU,oDAAV,CAAN;AACD;;AAED,SAAOZ,MAAM,CAACiC,MAAP,CAAc,CACnBjC,MAAM,CAACe,IAAP,CAAYZ,MAAM,CAACc,MAAP,CAAcc,MAAd,CAAZ,CADmB,EAEnB/B,MAAM,CAACe,IAAP,CAAYZ,MAAM,CAACc,MAAP,CAAcM,MAAd,CAAZ,CAFmB,EAGnBM,MAHmB,CAAd,CAAP;AAKD,CAzBD;AA2BA;;;;;;;;AAMAxB,OAAO,CAAC2B,UAAR,GAAqB,SAASA,UAAT,CAAqBJ,IAArB,EAA2B;AAC9C,MAAIJ,IAAI,GAAGI,IAAX;;AAEA,MAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;AAC5B,QAAIxB,EAAE,CAACE,KAAH,CAASsB,IAAT,MAAmBE,SAAvB,EAAkC;AAChC,YAAM,IAAIlB,KAAJ,6CAA+CgB,IAA/C,EAAN;AACD;;AACDJ,QAAI,GAAGpB,EAAE,CAACE,KAAH,CAASsB,IAAT,CAAP;AACD;;AAED,MAAI,OAAOJ,IAAP,KAAgB,QAApB,EAA8B;AAC5B,UAAM,IAAIZ,KAAJ,uDAAyDY,IAAzD,EAAN;AACD;;AAED,MAAIpB,EAAE,CAACG,KAAH,CAASiB,IAAT,MAAmBM,SAAnB,IAAgC,CAACzB,OAAO,CAAC6B,SAAR,CAAkBV,IAAlB,CAArC,EAA8D;AAC5D,UAAM,IAAIZ,KAAJ,uCAAyCY,IAAzC,EAAN;AACD;;AAED,SAAOA,IAAP;AACD,CAnBD;AAqBA;;;;;;;;AAMAnB,OAAO,CAAC6B,SAAR,GAAoB,SAASC,OAAT,CAAkBX,IAAlB,EAAwB;AAC1C,SAAOA,IAAI,GAAG,CAAP,IAAYA,IAAI,GAAG,IAA1B;AACD,CAFD;AAIA;;;;;;;;AAMAnB,OAAO,CAACoB,WAAR,GAAsB,SAASW,SAAT,CAAoBZ,IAApB,EAA0B;AAC9C,MAAInB,OAAO,CAAC6B,SAAR,CAAkBV,IAAlB,CAAJ,EAA6B;AAC3B,WAAO,IAAP;AACD;;AAED,MAAIpB,EAAE,CAACG,KAAH,CAASiB,IAAT,CAAJ,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,SAAO,KAAP;AACD,CAVD;AAYA;;;;;;;;;AAOA,SAASa,QAAT,CAAmBC,SAAnB,EAA8B;AAC5BjC,SAAO,CAACgB,MAAR,CAAeiB,SAAf,EAD4B,CACF;AAC3B;;AACDjC,OAAO,CAACgC,QAAR,GAAmBA,QAAnB;AAEA;;;;;;;;AAOAhC,OAAO,CAACkC,MAAR,GAAiB,SAASA,MAAT,CAAiBD,SAAjB,EAA4B;AAC3CD,UAAQ,CAACC,SAAD,CAAR;AAEA,SAAOA,SAAS,CAACpB,KAAV,CAAgB,CAAhB,EAAmB,CAAnB,CAAP;AACD,CAJD,C;;;;;;;AC3Na;;AAEb,MAAM;;AAEN;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,4CAA4C;;;AAG5C,mB;;;;;;;ACnBa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC,SAAS;AAC3C;AACA;AACA,CAAC;AACD;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;;AAGA;AACA;AACA;AACA;AACA,CAAC;;;AAGD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;;AAElB;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B;;AAE3B;AACA,6BAA6B;AAC7B;;AAEA,0CAA0C,UAAU;AACpD;AACA,GAAG;;;AAGH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA,C;;;;;;;ACtHa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;;AAEA,QAAQ,UAAU;;AAElB;AACA,E;;;;;;;ACzFa;;AAEb,iBAAiB;;AAEjB;AACA;AACA,E;;;;;;;ACNA;AACA;AACA;AACA;AACa;;AAEb;AACA;AACA,CAAC,GAAG,mBAAO,CAAC,CAAQ;;AAEpB,kBAAkB,mBAAO,CAAC,CAAa;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,OAAO;AAClB;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;AACA;;;AAGA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB;AACA,aAAa;AACb;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA,C;;;;;;;ACxIa;;AAEb,aAAa,mBAAO,CAAC,CAAW;;AAEhC,cAAc,mBAAO,CAAC,EAAQ;;AAE9B,eAAe,mBAAO,CAAC,EAAU;;AAEjC,eAAe,mBAAO,CAAC,EAAU;;AAEjC,eAAe,mBAAO,CAAC,EAAU,EAAE;;;AAGnC;AACA;AACA;AACA;AACA,CAAC,IAAI;AACL;AACA;AACA;AACA,CAAC,IAAI;AACL;AACA;AACA;AACA,E;;;;;;;ACzBa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,sB;;;;;;;AC3Ba;AACb;AACA;AACA;AACA;AACA;;AAEA,cAAc,mBAAO,CAAC,EAAa;;AAEnC;AACA;AACA;AACA;;AAEA;;AAEA,iBAAiB,qBAAqB;AACtC;AACA;;AAEA,iBAAiB,qBAAqB;AACtC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,8CAA8C;;AAE9C,+CAA+C;;AAE/C;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;AACA,mCAAmC;;AAEnC;AACA,iCAAiC;;AAEjC;;AAEA,8BAA8B,2CAA2C;AACzE;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA;AACA,KAAK;;;AAGL;;AAEA,UAAU,YAAY;AACtB;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,gBAAgB;;AAEhB;AACA;AACA,KAAK;;;AAGL;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL,wDAAwD;;AAExD,oCAAoC;;AAEpC;AACA;AACA,mDAAmD;;AAEnD;AACA;AACA;;AAEA;;AAEA,8BAA8B,2CAA2C;AACzE;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,sB;;;;;;;ACnMa;;AAEb;;AAEA;AACA,aAAa,mBAAO,CAAC,CAAQ;;AAE7B,2BAA2B;;AAE3B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uDAAuD;;AAEvD;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,E;;;;;;;ACzEa;;AAEb;AACA;AACA,CAAC,GAAG,mBAAO,CAAC,CAAQ;;AAEpB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,E;;;;;;;AC3Ba;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,E;;;;;;;AChFa;;AAEb;AACA;AACA,CAAC,GAAG,mBAAO,CAAC,CAAQ;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,E;;;;;;;AChDa;;AAEb;AACA,UAAU,mBAAO,CAAC,EAAa;AAC/B,UAAU,mBAAO,CAAC,EAAa;AAC/B,kBAAkB,mBAAO,CAAC,EAAa;AACvC,E;;;;;;;ACNa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,C;;;;;;;AC1Ba;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,C;;;;;;;AC3Ba;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,E;;;;;;;ACdA;;AACA;AACA;;AAEAb,OAAO,CAACC,KAAR,GAAgBkC,MAAM,CAACC,MAAP,CAAc;AAC5B,cAAc,GADc;AAE5B,UAAc,IAFc;AAG5B,cAAc,IAHc;AAI5B,cAAc,IAJc;AAK5B,kBAAgB,IALY;AAM5B,cAAc,IANc;AAO5B,cAAc,IAPc;AAQ5B,cAAc,IARc;AAS5B,cAAc,IATc;AAU5B,eAAc,IAVc;AAW5B,eAAc,IAXc;AAY5B,gBAAc,IAZc;AAa5B,gBAAc,IAbc;AAc5B,gBAAc,IAdc;AAe5B,gBAAc,IAfc;AAgB5B,iBAAe,IAhBa;AAiB5B,gBAAe,IAjBa;AAkB5B,SAAe,IAlBa;AAmB5B,SAAe,IAnBa;AAoB5B,eAAe,MApBa;AAqB5B,gBAAe,MArBa;AAsB5B,gBAAe,MAtBa;AAuB5B,gBAAe,MAvBa;AAwB5B,gBAAe,MAxBa;AAyB5B,gBAAe,MAzBa;AA0B5B,gBAAe,MA1Ba;AA2B5B,gBAAe,MA3Ba;AA4B5B,gBAAe,MA5Ba;AA6B5B,gBAAe,MA7Ba;AA8B5B,gBAAe,MA9Ba;AA+B5B,gBAAe,MA/Ba;AAgC5B,iBAAe,MAhCa;AAiC5B,iBAAe,MAjCa;AAkC5B,iBAAe,MAlCa;AAmC5B,iBAAe,MAnCa;AAoC5B,iBAAe,MApCa;AAqC5B,iBAAe,MArCa;AAsC5B,iBAAe,MAtCa;AAuC5B,iBAAe,MAvCa;AAwC5B,iBAAe,MAxCa;AAyC5B,iBAAe,MAzCa;AA0C5B,iBAAe,MA1Ca;AA2C5B,iBAAe,MA3Ca;AA4C5B,iBAAe,MA5Ca;AA6C5B,iBAAe,MA7Ca;AA8C5B,iBAAe,MA9Ca;AA+C5B,iBAAe,MA/Ca;AAgD5B,iBAAe,MAhDa;AAiD5B,iBAAe,MAjDa;AAkD5B,iBAAe,MAlDa;AAmD5B,iBAAe,MAnDa;AAoD5B,iBAAe,MApDa;AAqD5B,iBAAe,MArDa;AAsD5B,iBAAe,MAtDa;AAuD5B,iBAAe,MAvDa;AAwD5B,iBAAe,MAxDa;AAyD5B,iBAAe,MAzDa;AA0D5B,iBAAe,MA1Da;AA2D5B,iBAAe,MA3Da;AA4D5B,iBAAe,MA5Da;AA6D5B,iBAAe,MA7Da;AA8D5B,iBAAe,MA9Da;AA+D5B,iBAAe,MA/Da;AAgE5B,iBAAe,MAhEa;AAiE5B,iBAAe,MAjEa;AAkE5B,iBAAe,MAlEa;AAmE5B,iBAAe,MAnEa;AAoE5B,iBAAe,MApEa;AAqE5B,iBAAe,MArEa;AAsE5B,iBAAe,MAtEa;AAuE5B,iBAAe,MAvEa;AAwE5B,iBAAe,MAxEa;AAyE5B,iBAAe,MAzEa;AA0E5B,iBAAe,MA1Ea;AA2E5B,iBAAe,MA3Ea;AA4E5B,iBAAe,MA5Ea;AA6E5B,iBAAe,MA7Ea;AA8E5B,iBAAe,MA9Ea;AA+E5B,iBAAe,MA/Ea;AAgF5B,iBAAe,MAhFa;AAiF5B,iBAAe,MAjFa;AAkF5B,iBAAe,MAlFa;AAmF5B,iBAAe,MAnFa;AAoF5B,eAAe,MApFa;AAqF5B,gBAAe,MArFa;AAsF5B,gBAAe,MAtFa;AAuF5B,gBAAe,MAvFa;AAwF5B,gBAAe,MAxFa;AAyF5B,gBAAe,MAzFa;AA0F5B,gBAAe,MA1Fa;AA2F5B,gBAAe,MA3Fa;AA4F5B,gBAAe,MA5Fa;AA6F5B,gBAAe,MA7Fa;AA8F5B,gBAAe,MA9Fa;AA+F5B,gBAAe,MA/Fa;AAgG5B,iBAAe,MAhGa;AAiG5B,iBAAe,MAjGa;AAkG5B,iBAAe,MAlGa;AAmG5B,iBAAe,MAnGa;AAoG5B,iBAAe,MApGa;AAqG5B,iBAAe,MArGa;AAsG5B,iBAAe,MAtGa;AAuG5B,iBAAe,MAvGa;AAwG5B,iBAAe,MAxGa;AAyG5B,iBAAe,MAzGa;AA0G5B,iBAAe,MA1Ga;AA2G5B,iBAAe,MA3Ga;AA4G5B,iBAAe,MA5Ga;AA6G5B,iBAAe,MA7Ga;AA8G5B,iBAAe,MA9Ga;AA+G5B,iBAAe,MA/Ga;AAgH5B,iBAAe,MAhHa;AAiH5B,iBAAe,MAjHa;AAkH5B,iBAAe,MAlHa;AAmH5B,iBAAe,MAnHa;AAoH5B,gBAAc,MApHc;AAqH5B,iBAAe,MArHa;AAsH5B,iBAAe,MAtHa;AAuH5B,iBAAe,MAvHa;AAwH5B,iBAAe,MAxHa;AAyH5B,iBAAe,MAzHa;AA0H5B,iBAAe,MA1Ha;AA2H5B,iBAAe,MA3Ha;AA4H5B,iBAAe,MA5Ha;AA6H5B,iBAAe,MA7Ha;AA8H5B,iBAAe,MA9Ha;AA+H5B,iBAAe,MA/Ha;AAgI5B,kBAAgB,MAhIY;AAiI5B,kBAAgB,MAjIY;AAkI5B,kBAAgB,MAlIY;AAmI5B,kBAAgB,MAnIY;AAoI5B,kBAAgB,MApIY;AAqI5B,kBAAgB,MArIY;AAsI5B,kBAAgB,MAtIY;AAuI5B,kBAAgB,MAvIY;AAwI5B,kBAAgB,MAxIY;AAyI5B,kBAAgB,MAzIY;AA0I5B,kBAAgB,MA1IY;AA2I5B,kBAAgB,MA3IY;AA4I5B,kBAAgB,MA5IY;AA6I5B,kBAAgB,MA7IY;AA8I5B,kBAAgB,MA9IY;AA+I5B,kBAAgB,MA/IY;AAgJ5B,kBAAgB,MAhJY;AAiJ5B,kBAAgB,MAjJY;AAkJ5B,kBAAgB,MAlJY;AAmJ5B,kBAAgB,MAnJY;AAoJ5B,gBAAc,MApJc;AAqJ5B,iBAAe,MArJa;AAsJ5B,iBAAe,MAtJa;AAuJ5B,iBAAe,MAvJa;AAwJ5B,iBAAe,MAxJa;AAyJ5B,iBAAe,MAzJa;AA0J5B,iBAAe,MA1Ja;AA2J5B,iBAAe,MA3Ja;AA4J5B,iBAAe,MA5Ja;AA6J5B,iBAAe,MA7Ja;AA8J5B,iBAAe,MA9Ja;AA+J5B,iBAAe,MA/Ja;AAgK5B,kBAAgB,MAhKY;AAiK5B,kBAAgB,MAjKY;AAkK5B,kBAAgB,MAlKY;AAmK5B,kBAAgB,MAnKY;AAoK5B,kBAAgB,MApKY;AAqK5B,kBAAgB,MArKY;AAsK5B,kBAAgB,MAtKY;AAuK5B,kBAAgB,MAvKY;AAwK5B,kBAAgB,MAxKY;AAyK5B,kBAAgB,MAzKY;AA0K5B,kBAAgB,MA1KY;AA2K5B,kBAAgB,MA3KY;AA4K5B,kBAAgB,MA5KY;AA6K5B,kBAAgB,MA7KY;AA8K5B,kBAAgB,MA9KY;AA+K5B,kBAAgB,MA/KY;AAgL5B,kBAAgB,MAhLY;AAiL5B,kBAAgB,MAjLY;AAkL5B,kBAAgB,MAlLY;AAmL5B,kBAAgB,MAnLY;AAoL5B,kBAAgB,MApLY;AAqL5B,kBAAgB,MArLY;AAsL5B,kBAAgB,MAtLY;AAuL5B,kBAAgB,MAvLY;AAwL5B,kBAAgB,MAxLY;AAyL5B,kBAAgB,MAzLY;AA0L5B,kBAAgB,MA1LY;AA2L5B,kBAAgB,MA3LY;AA4L5B,kBAAgB,MA5LY;AA6L5B,kBAAgB,MA7LY;AA8L5B,kBAAgB,MA9LY;AA+L5B,kBAAgB,MA/LY;AAgM5B,kBAAgB,MAhMY;AAiM5B,kBAAgB,MAjMY;AAkM5B,kBAAgB,MAlMY;AAmM5B,kBAAgB,MAnMY;AAoM5B,kBAAgB,MApMY;AAqM5B,kBAAgB,MArMY;AAsM5B,kBAAgB,MAtMY;AAuM5B,kBAAgB,MAvMY;AAwM5B,kBAAgB,MAxMY;AAyM5B,kBAAgB,MAzMY;AA0M5B,kBAAgB,MA1MY;AA2M5B,kBAAgB,MA3MY;AA4M5B,kBAAgB,MA5MY;AA6M5B,kBAAgB,MA7MY;AA8M5B,kBAAgB,MA9MY;AA+M5B,kBAAgB,MA/MY;AAgN5B,kBAAgB,MAhNY;AAiN5B,kBAAgB,MAjNY;AAkN5B,kBAAgB,MAlNY;AAmN5B,kBAAgB,MAnNY;AAoN5B,iBAAe,MApNa;AAqN5B,kBAAgB,MArNY;AAsN5B,kBAAgB,MAtNY;AAuN5B,kBAAgB,MAvNY;AAwN5B,kBAAgB,MAxNY;AAyN5B,kBAAgB,MAzNY;AA0N5B,kBAAgB,MA1NY;AA2N5B,kBAAgB,MA3NY;AA4N5B,kBAAgB,MA5NY;AA6N5B,kBAAgB,MA7NY;AA8N5B,kBAAgB,MA9NY;AA+N5B,kBAAgB,MA/NY;AAgO5B,mBAAiB,MAhOW;AAiO5B,mBAAiB,MAjOW;AAkO5B,mBAAiB,MAlOW;AAmO5B,mBAAiB,MAnOW;AAoO5B,mBAAiB,MApOW;AAqO5B,mBAAiB,MArOW;AAsO5B,mBAAiB,MAtOW;AAuO5B,mBAAiB,MAvOW;AAwO5B,mBAAiB,MAxOW;AAyO5B,mBAAiB,MAzOW;AA0O5B,mBAAiB,MA1OW;AA2O5B,mBAAiB,MA3OW;AA4O5B,mBAAiB,MA5OW;AA6O5B,mBAAiB,MA7OW;AA8O5B,mBAAiB,MA9OW;AA+O5B,mBAAiB,MA/OW;AAgP5B,mBAAiB,MAhPW;AAiP5B,mBAAiB,MAjPW;AAkP5B,mBAAiB,MAlPW;AAmP5B,mBAAiB,MAnPW;AAoP5B,mBAAiB,MApPW;AAqP5B,mBAAiB,MArPW;AAsP5B,mBAAiB,MAtPW;AAuP5B,mBAAiB,MAvPW;AAwP5B,mBAAiB,MAxPW;AAyP5B,mBAAiB,MAzPW;AA0P5B,mBAAiB,MA1PW;AA2P5B,mBAAiB,MA3PW;AA4P5B,mBAAiB,MA5PW;AA6P5B,mBAAiB,MA7PW;AA8P5B,mBAAiB,MA9PW;AA+P5B,mBAAiB,MA/PW;AAgQ5B,mBAAiB,MAhQW;AAiQ5B,mBAAiB,MAjQW;AAkQ5B,mBAAiB,MAlQW;AAmQ5B,mBAAiB,MAnQW;AAoQ5B,mBAAiB,MApQW;AAqQ5B,mBAAiB,MArQW;AAsQ5B,mBAAiB,MAtQW;AAuQ5B,mBAAiB,MAvQW;AAwQ5B,mBAAiB,MAxQW;AAyQ5B,mBAAiB,MAzQW;AA0Q5B,mBAAiB,MA1QW;AA2Q5B,mBAAiB,MA3QW;AA4Q5B,mBAAiB,MA5QW;AA6Q5B,mBAAiB,MA7QW;AA8Q5B,mBAAiB,MA9QW;AA+Q5B,mBAAiB,MA/QW;AAgR5B,mBAAiB,MAhRW;AAiR5B,mBAAiB,MAjRW;AAkR5B,mBAAiB,MAlRW;AAmR5B,mBAAiB,MAnRW;AAoR5B,mBAAiB,MApRW;AAqR5B,mBAAiB,MArRW;AAsR5B,mBAAiB,MAtRW;AAuR5B,mBAAiB,MAvRW;AAwR5B,mBAAiB,MAxRW;AAyR5B,mBAAiB,MAzRW;AA0R5B,mBAAiB,MA1RW;AA2R5B,mBAAiB,MA3RW;AA4R5B,mBAAiB,MA5RW;AA6R5B,mBAAiB,MA7RW;AA8R5B,mBAAiB,MA9RW;AA+R5B,mBAAiB,MA/RW;AAgS5B,mBAAiB,MAhSW;AAiS5B,mBAAiB,MAjSW;AAkS5B,mBAAiB,MAlSW;AAmS5B,mBAAiB,MAnSW;AAoS5B,mBAAiB,MApSW;AAqS5B,mBAAiB,MArSW;AAsS5B,mBAAiB,MAtSW;AAuS5B,mBAAiB,MAvSW;AAwS5B,mBAAiB,MAxSW;AAyS5B,mBAAiB,MAzSW;AA0S5B,mBAAiB,MA1SW;AA2S5B,mBAAiB,MA3SW;AA4S5B,mBAAiB,MA5SW;AA6S5B,mBAAiB,MA7SW;AA8S5B,mBAAiB,MA9SW;AA+S5B,mBAAiB,MA/SW;AAgT5B,mBAAiB,MAhTW;AAiT5B,mBAAiB,MAjTW;AAkT5B,mBAAiB,MAlTW;AAmT5B,mBAAiB,MAnTW;AAoT5B,mBAAiB,MApTW;AAqT5B,mBAAiB,MArTW;AAsT5B,mBAAiB,MAtTW;AAuT5B,mBAAiB,MAvTW;AAwT5B,mBAAiB,MAxTW;AAyT5B,mBAAiB,MAzTW;AA0T5B,mBAAiB,MA1TW;AA2T5B,mBAAiB,MA3TW;AA4T5B,mBAAiB,MA5TW;AA6T5B,mBAAiB,MA7TW;AA8T5B,mBAAiB,MA9TW;AA+T5B,mBAAiB,MA/TW;AAgU5B,mBAAiB,MAhUW;AAiU5B,mBAAiB,MAjUW;AAkU5B,mBAAiB,MAlUW;AAmU5B,mBAAiB,MAnUW;AAoU5B,mBAAiB,MApUW;AAqU5B,mBAAiB,MArUW;AAsU5B,mBAAiB,MAtUW;AAuU5B,mBAAiB,MAvUW;AAwU5B,mBAAiB,MAxUW;AAyU5B,mBAAiB,MAzUW;AA0U5B,mBAAiB,MA1UW;AA2U5B,mBAAiB,MA3UW;AA4U5B,mBAAiB,MA5UW;AA6U5B,mBAAiB,MA7UW;AA8U5B,mBAAiB,MA9UW;AA+U5B,mBAAiB,MA/UW;AAgV5B,oBAAkB,MAhVU;AAiV5B,oBAAkB,MAjVU;AAkV5B,oBAAkB,MAlVU;AAmV5B,oBAAkB;AAnVU,CAAd,CAAhB;AAsVApC,OAAO,CAACE,KAAR,GAAgBiC,MAAM,CAACC,MAAP,CAAc;AAC5B,OAAK,UADuB;AAG5B;AACA,QAAM,MAJsB;AAK5B,QAAM,UALsB;AAM5B,QAAM,UANsB;AAO5B,QAAM,cAPsB;AAQ5B,QAAM,UARsB;AAS5B,QAAM,UATsB;AAU5B,QAAM,UAVsB;AAW5B,QAAM,UAXsB;AAY5B,QAAM,WAZsB;AAa5B,QAAM,WAbsB;AAc5B,QAAM,YAdsB;AAe5B,QAAM,YAfsB;AAgB5B,QAAM,YAhBsB;AAiB5B,QAAM,YAjBsB;AAmB5B,QAAM,aAnBsB;AAoB5B,QAAM,YApBsB;AAsB5B,QAAM,KAtBsB;AAuB5B,QAAM,KAvBsB;AAyB5B;AACA,UAAQ,WA1BoB;AA2B5B,UAAQ,YA3BoB;AA4B5B,UAAQ,YA5BoB;AA6B5B,UAAQ,YA7BoB;AA8B5B,UAAQ,YA9BoB;AA+B5B,UAAQ,YA/BoB;AAgC5B,UAAQ,YAhCoB;AAiC5B,UAAQ,YAjCoB;AAkC5B,UAAQ,YAlCoB;AAmC5B,UAAQ,YAnCoB;AAoC5B,UAAQ,YApCoB;AAqC5B,UAAQ,YArCoB;AAsC5B,UAAQ,aAtCoB;AAuC5B,UAAQ,aAvCoB;AAwC5B,UAAQ,aAxCoB;AAyC5B,UAAQ,aAzCoB;AA0C5B,UAAQ,aA1CoB;AA2C5B,UAAQ,aA3CoB;AA4C5B,UAAQ,aA5CoB;AA6C5B,UAAQ,aA7CoB;AA8C5B,UAAQ,aA9CoB;AA+C5B,UAAQ,aA/CoB;AAgD5B,UAAQ,aAhDoB;AAiD5B,UAAQ,aAjDoB;AAkD5B,UAAQ,aAlDoB;AAmD5B,UAAQ,aAnDoB;AAoD5B,UAAQ,aApDoB;AAqD5B,UAAQ,aArDoB;AAsD5B,UAAQ,aAtDoB;AAuD5B,UAAQ,aAvDoB;AAwD5B,UAAQ,aAxDoB;AAyD5B,UAAQ,aAzDoB;AA0D5B,UAAQ,aA1DoB;AA2D5B,UAAQ,aA3DoB;AA4D5B,UAAQ,aA5DoB;AA6D5B,UAAQ,aA7DoB;AA8D5B,UAAQ,aA9DoB;AA+D5B,UAAQ,aA/DoB;AAgE5B,UAAQ,aAhEoB;AAiE5B,UAAQ,aAjEoB;AAkE5B,UAAQ,aAlEoB;AAmE5B,UAAQ,aAnEoB;AAoE5B,UAAQ,aApEoB;AAqE5B,UAAQ,aArEoB;AAsE5B,UAAQ,aAtEoB;AAuE5B,UAAQ,aAvEoB;AAwE5B,UAAQ,aAxEoB;AAyE5B,UAAQ,aAzEoB;AA0E5B,UAAQ,aA1EoB;AA2E5B,UAAQ,aA3EoB;AA4E5B,UAAQ,aA5EoB;AA6E5B,UAAQ,aA7EoB;AA8E5B,UAAQ,aA9EoB;AA+E5B,UAAQ,aA/EoB;AAgF5B,UAAQ,aAhFoB;AAiF5B,UAAQ,aAjFoB;AAkF5B,UAAQ,aAlFoB;AAmF5B,UAAQ,aAnFoB;AAoF5B,UAAQ,aApFoB;AAqF5B,UAAQ,aArFoB;AAsF5B,UAAQ,aAtFoB;AAuF5B,UAAQ,aAvFoB;AAwF5B,UAAQ,aAxFoB;AAyF5B,UAAQ,aAzFoB;AA0F5B,UAAQ,WA1FoB;AA2F5B,UAAQ,YA3FoB;AA4F5B,UAAQ,YA5FoB;AA6F5B,UAAQ,YA7FoB;AA8F5B,UAAQ,YA9FoB;AA+F5B,UAAQ,YA/FoB;AAgG5B,UAAQ,YAhGoB;AAiG5B,UAAQ,YAjGoB;AAkG5B,UAAQ,YAlGoB;AAmG5B,UAAQ,YAnGoB;AAoG5B,UAAQ,YApGoB;AAqG5B,UAAQ,YArGoB;AAsG5B,UAAQ,aAtGoB;AAuG5B,UAAQ,aAvGoB;AAwG5B,UAAQ,aAxGoB;AAyG5B,UAAQ,aAzGoB;AA0G5B,UAAQ,aA1GoB;AA2G5B,UAAQ,aA3GoB;AA4G5B,UAAQ,aA5GoB;AA6G5B,UAAQ,aA7GoB;AA8G5B,UAAQ,aA9GoB;AA+G5B,UAAQ,aA/GoB;AAgH5B,UAAQ,aAhHoB;AAiH5B,UAAQ,aAjHoB;AAkH5B,UAAQ,aAlHoB;AAmH5B,UAAQ,aAnHoB;AAoH5B,UAAQ,aApHoB;AAqH5B,UAAQ,aArHoB;AAsH5B,UAAQ,aAtHoB;AAuH5B,UAAQ,aAvHoB;AAwH5B,UAAQ,aAxHoB;AAyH5B,UAAQ,aAzHoB;AA2H5B;AACA,UAAQ,YA5HoB;AA6H5B,UAAQ,aA7HoB;AA8H5B,UAAQ,aA9HoB;AA+H5B,UAAQ,aA/HoB;AAgI5B,UAAQ,aAhIoB;AAiI5B,UAAQ,aAjIoB;AAkI5B,UAAQ,aAlIoB;AAmI5B,UAAQ,aAnIoB;AAoI5B,UAAQ,aApIoB;AAqI5B,UAAQ,aArIoB;AAsI5B,UAAQ,aAtIoB;AAuI5B,UAAQ,aAvIoB;AAwI5B,UAAQ,cAxIoB;AAyI5B,UAAQ,cAzIoB;AA0I5B,UAAQ,cA1IoB;AA2I5B,UAAQ,cA3IoB;AA4I5B,UAAQ,cA5IoB;AA6I5B,UAAQ,cA7IoB;AA8I5B,UAAQ,cA9IoB;AA+I5B,UAAQ,cA/IoB;AAgJ5B,UAAQ,cAhJoB;AAiJ5B,UAAQ,cAjJoB;AAkJ5B,UAAQ,cAlJoB;AAmJ5B,UAAQ,cAnJoB;AAoJ5B,UAAQ,cApJoB;AAqJ5B,UAAQ,cArJoB;AAsJ5B,UAAQ,cAtJoB;AAuJ5B,UAAQ,cAvJoB;AAwJ5B,UAAQ,cAxJoB;AAyJ5B,UAAQ,cAzJoB;AA0J5B,UAAQ,cA1JoB;AA2J5B,UAAQ,cA3JoB;AA4J5B,UAAQ,YA5JoB;AA6J5B,UAAQ,aA7JoB;AA8J5B,UAAQ,aA9JoB;AA+J5B,UAAQ,aA/JoB;AAgK5B,UAAQ,aAhKoB;AAiK5B,UAAQ,aAjKoB;AAkK5B,UAAQ,aAlKoB;AAmK5B,UAAQ,aAnKoB;AAoK5B,UAAQ,aApKoB;AAqK5B,UAAQ,aArKoB;AAsK5B,UAAQ,aAtKoB;AAuK5B,UAAQ,aAvKoB;AAwK5B,UAAQ,cAxKoB;AAyK5B,UAAQ,cAzKoB;AA0K5B,UAAQ,cA1KoB;AA2K5B,UAAQ,cA3KoB;AA4K5B,UAAQ,cA5KoB;AA6K5B,UAAQ,cA7KoB;AA8K5B,UAAQ,cA9KoB;AA+K5B,UAAQ,cA/KoB;AAgL5B,UAAQ,cAhLoB;AAiL5B,UAAQ,cAjLoB;AAkL5B,UAAQ,cAlLoB;AAmL5B,UAAQ,cAnLoB;AAoL5B,UAAQ,cApLoB;AAqL5B,UAAQ,cArLoB;AAsL5B,UAAQ,cAtLoB;AAuL5B,UAAQ,cAvLoB;AAwL5B,UAAQ,cAxLoB;AAyL5B,UAAQ,cAzLoB;AA0L5B,UAAQ,cA1LoB;AA2L5B,UAAQ,cA3LoB;AA4L5B,UAAQ,cA5LoB;AA6L5B,UAAQ,cA7LoB;AA8L5B,UAAQ,cA9LoB;AA+L5B,UAAQ,cA/LoB;AAgM5B,UAAQ,cAhMoB;AAiM5B,UAAQ,cAjMoB;AAkM5B,UAAQ,cAlMoB;AAmM5B,UAAQ,cAnMoB;AAoM5B,UAAQ,cApMoB;AAqM5B,UAAQ,cArMoB;AAsM5B,UAAQ,cAtMoB;AAuM5B,UAAQ,cAvMoB;AAwM5B,UAAQ,cAxMoB;AAyM5B,UAAQ,cAzMoB;AA0M5B,UAAQ,cA1MoB;AA2M5B,UAAQ,cA3MoB;AA4M5B,UAAQ,cA5MoB;AA6M5B,UAAQ,cA7MoB;AA8M5B,UAAQ,cA9MoB;AA+M5B,UAAQ,cA/MoB;AAgN5B,UAAQ,cAhNoB;AAiN5B,UAAQ,cAjNoB;AAkN5B,UAAQ,cAlNoB;AAmN5B,UAAQ,cAnNoB;AAoN5B,UAAQ,cApNoB;AAqN5B,UAAQ,cArNoB;AAsN5B,UAAQ,cAtNoB;AAuN5B,UAAQ,cAvNoB;AAwN5B,UAAQ,cAxNoB;AAyN5B,UAAQ,cAzNoB;AA0N5B,UAAQ,cA1NoB;AA2N5B,UAAQ,cA3NoB;AA4N5B,UAAQ,aA5NoB;AA6N5B,UAAQ,cA7NoB;AA8N5B,UAAQ,cA9NoB;AA+N5B,UAAQ,cA/NoB;AAgO5B,UAAQ,cAhOoB;AAiO5B,UAAQ,cAjOoB;AAkO5B,UAAQ,cAlOoB;AAmO5B,UAAQ,cAnOoB;AAoO5B,UAAQ,cApOoB;AAqO5B,UAAQ,cArOoB;AAsO5B,UAAQ,cAtOoB;AAuO5B,UAAQ,cAvOoB;AAwO5B,UAAQ,eAxOoB;AAyO5B,UAAQ,eAzOoB;AA0O5B,UAAQ,eA1OoB;AA2O5B,UAAQ,eA3OoB;AA4O5B,UAAQ,eA5OoB;AA6O5B,UAAQ,eA7OoB;AA8O5B,UAAQ,eA9OoB;AA+O5B,UAAQ,eA/OoB;AAgP5B,UAAQ,eAhPoB;AAiP5B,UAAQ,eAjPoB;AAkP5B,UAAQ,eAlPoB;AAmP5B,UAAQ,eAnPoB;AAoP5B,UAAQ,eApPoB;AAqP5B,UAAQ,eArPoB;AAsP5B,UAAQ,eAtPoB;AAuP5B,UAAQ,eAvPoB;AAwP5B,UAAQ,eAxPoB;AAyP5B,UAAQ,eAzPoB;AA0P5B,UAAQ,eA1PoB;AA2P5B,UAAQ,eA3PoB;AA4P5B,UAAQ,eA5PoB;AA6P5B,UAAQ,eA7PoB;AA8P5B,UAAQ,eA9PoB;AA+P5B,UAAQ,eA/PoB;AAgQ5B,UAAQ,eAhQoB;AAiQ5B,UAAQ,eAjQoB;AAkQ5B,UAAQ,eAlQoB;AAmQ5B,UAAQ,eAnQoB;AAoQ5B,UAAQ,eApQoB;AAqQ5B,UAAQ,eArQoB;AAsQ5B,UAAQ,eAtQoB;AAuQ5B,UAAQ,eAvQoB;AAwQ5B,UAAQ,eAxQoB;AAyQ5B,UAAQ,eAzQoB;AA0Q5B,UAAQ,eA1QoB;AA2Q5B,UAAQ,eA3QoB;AA4Q5B,UAAQ,eA5QoB;AA6Q5B,UAAQ,eA7QoB;AA8Q5B,UAAQ,eA9QoB;AA+Q5B,UAAQ,eA/QoB;AAgR5B,UAAQ,eAhRoB;AAiR5B,UAAQ,eAjRoB;AAkR5B,UAAQ,eAlRoB;AAmR5B,UAAQ,eAnRoB;AAoR5B,UAAQ,eApRoB;AAqR5B,UAAQ,eArRoB;AAsR5B,UAAQ,eAtRoB;AAuR5B,UAAQ,eAvRoB;AAwR5B,UAAQ,eAxRoB;AAyR5B,UAAQ,eAzRoB;AA0R5B,UAAQ,eA1RoB;AA2R5B,UAAQ,eA3RoB;AA4R5B,UAAQ,eA5RoB;AA6R5B,UAAQ,eA7RoB;AA8R5B,UAAQ,eA9RoB;AA+R5B,UAAQ,eA/RoB;AAgS5B,UAAQ,eAhSoB;AAiS5B,UAAQ,eAjSoB;AAkS5B,UAAQ,eAlSoB;AAmS5B,UAAQ,eAnSoB;AAoS5B,UAAQ,eApSoB;AAqS5B,UAAQ,eArSoB;AAsS5B,UAAQ,eAtSoB;AAuS5B,UAAQ,eAvSoB;AAwS5B,UAAQ,eAxSoB;AAyS5B,UAAQ,eAzSoB;AA0S5B,UAAQ,eA1SoB;AA2S5B,UAAQ,eA3SoB;AA4S5B,UAAQ,eA5SoB;AA6S5B,UAAQ,eA7SoB;AA8S5B,UAAQ,eA9SoB;AA+S5B,UAAQ,eA/SoB;AAgT5B,UAAQ,eAhToB;AAiT5B,UAAQ,eAjToB;AAkT5B,UAAQ,eAlToB;AAmT5B,UAAQ,eAnToB;AAoT5B,UAAQ,eApToB;AAqT5B,UAAQ,eArToB;AAsT5B,UAAQ,eAtToB;AAuT5B,UAAQ,eAvToB;AAwT5B,UAAQ,eAxToB;AAyT5B,UAAQ,eAzToB;AA0T5B,UAAQ,eA1ToB;AA2T5B,UAAQ,eA3ToB;AA4T5B,UAAQ,eA5ToB;AA6T5B,UAAQ,eA7ToB;AA8T5B,UAAQ,eA9ToB;AA+T5B,UAAQ,eA/ToB;AAgU5B,UAAQ,eAhUoB;AAiU5B,UAAQ,eAjUoB;AAkU5B,UAAQ,eAlUoB;AAmU5B,UAAQ,eAnUoB;AAoU5B,UAAQ,eApUoB;AAqU5B,UAAQ,eArUoB;AAsU5B,UAAQ,eAtUoB;AAuU5B,UAAQ,eAvUoB;AAwU5B,UAAQ,eAxUoB;AAyU5B,UAAQ,eAzUoB;AA0U5B,UAAQ,eA1UoB;AA2U5B,UAAQ,eA3UoB;AA4U5B,UAAQ,eA5UoB;AA6U5B,UAAQ,eA7UoB;AA8U5B,UAAQ,eA9UoB;AA+U5B,UAAQ,eA/UoB;AAgV5B,UAAQ,eAhVoB;AAiV5B,UAAQ,eAjVoB;AAkV5B,UAAQ,eAlVoB;AAmV5B,UAAQ,eAnVoB;AAoV5B,UAAQ,eApVoB;AAqV5B,UAAQ,eArVoB;AAsV5B,UAAQ,eAtVoB;AAuV5B,UAAQ,eAvVoB;AAwV5B,UAAQ,gBAxVoB;AAyV5B,UAAQ,gBAzVoB;AA0V5B,UAAQ,gBA1VoB;AA2V5B,UAAQ;AA3VoB,CAAd,CAAhB;AA8VApC,OAAO,CAACG,cAAR,GAAyBgC,MAAM,CAACC,MAAP,CAAc;AACrC,QAAM,EAD+B;AAErC,QAAM,EAF+B;AAGrC,QAAM,EAH+B;AAIrC,QAAM,EAJ+B;AAKrC,QAAM,EAL+B;AAMrC,QAAM,EAN+B;AAOrC,QAAM,EAP+B;AAQrC,QAAM,EAR+B;AASrC,QAAM,EAT+B;AAUrC,QAAM,EAV+B;AAWrC,QAAM,EAX+B;AAYrC,QAAM,EAZ+B;AAarC,QAAM,EAb+B;AAcrC,QAAM,EAd+B;AAerC,QAAM,EAf+B;AAiBrC,UAAQ,IAjB6B;AAkBrC,UAAQ,IAlB6B;AAmBrC,UAAQ,IAnB6B;AAoBrC,UAAQ,IApB6B;AAqBrC,UAAQ,IArB6B;AAsBrC,UAAQ,IAtB6B;AAuBrC,UAAQ,IAvB6B;AAwBrC,UAAQ,IAxB6B;AAyBrC,UAAQ,IAzB6B;AA0BrC,UAAQ,IA1B6B;AA2BrC,UAAQ,IA3B6B;AA4BrC,UAAQ,IA5B6B;AA6BrC,UAAQ,IA7B6B;AA8BrC,UAAQ,IA9B6B;AA+BrC,UAAQ,IA/B6B;AAgCrC,UAAQ,IAhC6B;AAiCrC,UAAQ,IAjC6B;AAkCrC,UAAQ,IAlC6B;AAmCrC,UAAQ,IAnC6B;AAoCrC,UAAQ,IApC6B;AAqCrC,UAAQ,IArC6B;AAsCrC,UAAQ,IAtC6B;AAuCrC,UAAQ,IAvC6B;AAwCrC,UAAQ,IAxC6B;AAyCrC,UAAQ,IAzC6B;AA0CrC,UAAQ,IA1C6B;AA2CrC,UAAQ,IA3C6B;AA4CrC,UAAQ,IA5C6B;AA6CrC,UAAQ,IA7C6B;AA8CrC,UAAQ,IA9C6B;AA+CrC,UAAQ,IA/C6B;AAgDrC,UAAQ,IAhD6B;AAiDrC,UAAQ,IAjD6B;AAkDrC,UAAQ,IAlD6B;AAmDrC,UAAQ,IAnD6B;AAoDrC,UAAQ,IApD6B;AAqDrC,UAAQ,IArD6B;AAsDrC,UAAQ,IAtD6B;AAuDrC,UAAQ,IAvD6B;AAwDrC,UAAQ,IAxD6B;AAyDrC,UAAQ,IAzD6B;AA0DrC,UAAQ,IA1D6B;AA2DrC,UAAQ,IA3D6B;AA4DrC,UAAQ,IA5D6B;AA6DrC,UAAQ,IA7D6B;AA8DrC,UAAQ,IA9D6B;AA+DrC,UAAQ,IA/D6B;AAgErC,UAAQ,IAhE6B;AAiErC,UAAQ,IAjE6B;AAkErC,UAAQ,IAlE6B;AAmErC,UAAQ,IAnE6B;AAoErC,UAAQ,IApE6B;AAqErC,UAAQ,IArE6B;AAsErC,UAAQ,IAtE6B;AAuErC,UAAQ,IAvE6B;AAwErC,UAAQ,IAxE6B;AAyErC,UAAQ,IAzE6B;AA0ErC,UAAQ,IA1E6B;AA2ErC,UAAQ,IA3E6B;AA4ErC,UAAQ,IA5E6B;AA6ErC,UAAQ,IA7E6B;AA8ErC,UAAQ,IA9E6B;AA+ErC,UAAQ,IA/E6B;AAgFrC,UAAQ,IAhF6B;AAiFrC,UAAQ,IAjF6B;AAkFrC,UAAQ,IAlF6B;AAmFrC,UAAQ,IAnF6B;AAoFrC,UAAQ,IApF6B;AAqFrC,UAAQ,IArF6B;AAsFrC,UAAQ,IAtF6B;AAuFrC,UAAQ,IAvF6B;AAwFrC,UAAQ,IAxF6B;AAyFrC,UAAQ,IAzF6B;AA0FrC,UAAQ,IA1F6B;AA2FrC,UAAQ,IA3F6B;AA4FrC,UAAQ,IA5F6B;AA6FrC,UAAQ,IA7F6B;AA8FrC,UAAQ,IA9F6B;AA+FrC,UAAQ,IA/F6B;AAgGrC,UAAQ,IAhG6B;AAiGrC,UAAQ,IAjG6B;AAkGrC,UAAQ,IAlG6B;AAmGrC,UAAQ,IAnG6B;AAoGrC,UAAQ,IApG6B;AAqGrC,UAAQ,IArG6B;AAsGrC,UAAQ,IAtG6B;AAuGrC,UAAQ,IAvG6B;AAwGrC,UAAQ,IAxG6B;AAyGrC,UAAQ,IAzG6B;AA0GrC,UAAQ,IA1G6B;AA2GrC,UAAQ,IA3G6B;AA4GrC,UAAQ,IA5G6B;AA6GrC,UAAQ,IA7G6B;AA8GrC,UAAQ,IA9G6B;AA+GrC,UAAQ,IA/G6B;AAgHrC,UAAQ,IAhH6B;AAiHrC,UAAQ,IAjH6B;AAkHrC,UAAQ,IAlH6B;AAmHrC,UAAQ,IAnH6B;AAoHrC,UAAQ,IApH6B;AAqHrC,UAAQ,IArH6B;AAsHrC,UAAQ,IAtH6B;AAuHrC,UAAQ,IAvH6B;AAwHrC,UAAQ,IAxH6B;AAyHrC,UAAQ,IAzH6B;AA0HrC,UAAQ,IA1H6B;AA2HrC,UAAQ,IA3H6B;AA4HrC,UAAQ,IA5H6B;AA6HrC,UAAQ,IA7H6B;AA8HrC,UAAQ,IA9H6B;AA+HrC,UAAQ,IA/H6B;AAgIrC,UAAQ,IAhI6B;AAiIrC,UAAQ,IAjI6B;AAkIrC,UAAQ,IAlI6B;AAmIrC,UAAQ,IAnI6B;AAoIrC,UAAQ,IApI6B;AAqIrC,UAAQ,IArI6B;AAsIrC,UAAQ,IAtI6B;AAuIrC,UAAQ,IAvI6B;AAwIrC,UAAQ,IAxI6B;AAyIrC,UAAQ,IAzI6B;AA0IrC,UAAQ,IA1I6B;AA2IrC,UAAQ,IA3I6B;AA4IrC,UAAQ,IA5I6B;AA6IrC,UAAQ,IA7I6B;AA8IrC,UAAQ,IA9I6B;AA+IrC,UAAQ,IA/I6B;AAgJrC,UAAQ,IAhJ6B;AAiJrC,UAAQ,IAjJ6B;AAkJrC,UAAQ,IAlJ6B;AAmJrC,UAAQ,IAnJ6B;AAoJrC,UAAQ,IApJ6B;AAqJrC,UAAQ,IArJ6B;AAsJrC,UAAQ,IAtJ6B;AAuJrC,UAAQ,IAvJ6B;AAwJrC,UAAQ,IAxJ6B;AAyJrC,UAAQ,IAzJ6B;AA0JrC,UAAQ,IA1J6B;AA2JrC,UAAQ,IA3J6B;AA4JrC,UAAQ,IA5J6B;AA6JrC,UAAQ,IA7J6B;AA8JrC,UAAQ,IA9J6B;AA+JrC,UAAQ,IA/J6B;AAgKrC,UAAQ,IAhK6B;AAiKrC,UAAQ,IAjK6B;AAkKrC,UAAQ,IAlK6B;AAmKrC,UAAQ,IAnK6B;AAoKrC,UAAQ,IApK6B;AAqKrC,UAAQ,IArK6B;AAsKrC,UAAQ,IAtK6B;AAuKrC,UAAQ,IAvK6B;AAwKrC,UAAQ,IAxK6B;AAyKrC,UAAQ,IAzK6B;AA0KrC,UAAQ,IA1K6B;AA2KrC,UAAQ,IA3K6B;AA4KrC,UAAQ,IA5K6B;AA6KrC,UAAQ,IA7K6B;AA8KrC,UAAQ,IA9K6B;AA+KrC,UAAQ,IA/K6B;AAgLrC,UAAQ,IAhL6B;AAiLrC,UAAQ,IAjL6B;AAkLrC,UAAQ,IAlL6B;AAmLrC,UAAQ,IAnL6B;AAoLrC,UAAQ,IApL6B;AAqLrC,UAAQ,IArL6B;AAsLrC,UAAQ,IAtL6B;AAuLrC,UAAQ,IAvL6B;AAwLrC,UAAQ,IAxL6B;AAyLrC,UAAQ,IAzL6B;AA0LrC,UAAQ,IA1L6B;AA2LrC,UAAQ,IA3L6B;AA4LrC,UAAQ,IA5L6B;AA6LrC,UAAQ,IA7L6B;AA8LrC,UAAQ,IA9L6B;AA+LrC,UAAQ,IA/L6B;AAgMrC,UAAQ,IAhM6B;AAiMrC,UAAQ,IAjM6B;AAkMrC,UAAQ,IAlM6B;AAmMrC,UAAQ,IAnM6B;AAoMrC,UAAQ,IApM6B;AAqMrC,UAAQ,IArM6B;AAsMrC,UAAQ,IAtM6B;AAuMrC,UAAQ,IAvM6B;AAwMrC,UAAQ,IAxM6B;AAyMrC,UAAQ,IAzM6B;AA0MrC,UAAQ,IA1M6B;AA2MrC,UAAQ,IA3M6B;AA4MrC,UAAQ,IA5M6B;AA6MrC,UAAQ,IA7M6B;AA8MrC,UAAQ,IA9M6B;AA+MrC,UAAQ,IA/M6B;AAgNrC,UAAQ,IAhN6B;AAiNrC,UAAQ,IAjN6B;AAkNrC,UAAQ,IAlN6B;AAmNrC,UAAQ,IAnN6B;AAoNrC,UAAQ,IApN6B;AAqNrC,UAAQ,IArN6B;AAsNrC,UAAQ,IAtN6B;AAuNrC,UAAQ,IAvN6B;AAwNrC,UAAQ,IAxN6B;AAyNrC,UAAQ,IAzN6B;AA0NrC,UAAQ,IA1N6B;AA2NrC,UAAQ,IA3N6B;AA4NrC,UAAQ,IA5N6B;AA6NrC,UAAQ,IA7N6B;AA8NrC,UAAQ,IA9N6B;AA+NrC,UAAQ,IA/N6B;AAgOrC,UAAQ,IAhO6B;AAiOrC,UAAQ,IAjO6B;AAkOrC,UAAQ,IAlO6B;AAmOrC,UAAQ,IAnO6B;AAoOrC,UAAQ,IApO6B;AAqOrC,UAAQ,IArO6B;AAsOrC,UAAQ,IAtO6B;AAuOrC,UAAQ,IAvO6B;AAwOrC,UAAQ,IAxO6B;AAyOrC,UAAQ,IAzO6B;AA0OrC,UAAQ,IA1O6B;AA2OrC,UAAQ,IA3O6B;AA4OrC,UAAQ,IA5O6B;AA6OrC,UAAQ,IA7O6B;AA8OrC,UAAQ,IA9O6B;AA+OrC,UAAQ,IA/O6B;AAgPrC,UAAQ,IAhP6B;AAiPrC,UAAQ,IAjP6B;AAkPrC,UAAQ,IAlP6B;AAmPrC,UAAQ,IAnP6B;AAoPrC,UAAQ,IApP6B;AAqPrC,UAAQ,IArP6B;AAsPrC,UAAQ,IAtP6B;AAuPrC,UAAQ,IAvP6B;AAwPrC,UAAQ,IAxP6B;AAyPrC,UAAQ,IAzP6B;AA0PrC,UAAQ,IA1P6B;AA2PrC,UAAQ,IA3P6B;AA4PrC,UAAQ,IA5P6B;AA6PrC,UAAQ,IA7P6B;AA8PrC,UAAQ,IA9P6B;AA+PrC,UAAQ,IA/P6B;AAgQrC,UAAQ,IAhQ6B;AAiQrC,UAAQ,IAjQ6B;AAkQrC,UAAQ,IAlQ6B;AAmQrC,UAAQ,IAnQ6B;AAoQrC,UAAQ,IApQ6B;AAqQrC,UAAQ,IArQ6B;AAsQrC,UAAQ,IAtQ6B;AAuQrC,UAAQ,IAvQ6B;AAwQrC,UAAQ,IAxQ6B;AAyQrC,UAAQ,IAzQ6B;AA0QrC,UAAQ,IA1Q6B;AA2QrC,UAAQ,IA3Q6B;AA4QrC,UAAQ,IA5Q6B;AA6QrC,UAAQ,IA7Q6B;AA8QrC,UAAQ,IA9Q6B;AA+QrC,UAAQ,IA/Q6B;AAgRrC,UAAQ,IAhR6B;AAiRrC,UAAQ,IAjR6B;AAkRrC,UAAQ,IAlR6B;AAmRrC,UAAQ,IAnR6B;AAoRrC,UAAQ,IApR6B;AAqRrC,UAAQ,IArR6B;AAsRrC,UAAQ,IAtR6B;AAuRrC,UAAQ,IAvR6B;AAwRrC,UAAQ,IAxR6B;AAyRrC,UAAQ,IAzR6B;AA0RrC,UAAQ,IA1R6B;AA2RrC,UAAQ,IA3R6B;AA4RrC,UAAQ,IA5R6B;AA6RrC,UAAQ,IA7R6B;AA8RrC,UAAQ,IA9R6B;AA+RrC,UAAQ,IA/R6B;AAgSrC,UAAQ,IAhS6B;AAiSrC,UAAQ,IAjS6B;AAkSrC,UAAQ,IAlS6B;AAmSrC,UAAQ,IAnS6B;AAoSrC,UAAQ,IApS6B;AAqSrC,UAAQ,IArS6B;AAsSrC,UAAQ,IAtS6B;AAuSrC,UAAQ,IAvS6B;AAwSrC,UAAQ,IAxS6B;AAySrC,UAAQ,IAzS6B;AA0SrC,UAAQ,IA1S6B;AA2SrC,UAAQ,IA3S6B;AA4SrC,UAAQ,IA5S6B;AA6SrC,UAAQ,IA7S6B;AA8SrC,UAAQ,IA9S6B;AA+SrC,UAAQ,IA/S6B;AAgTrC,UAAQ,IAhT6B;AAiTrC,UAAQ,IAjT6B;AAkTrC,UAAQ,IAlT6B;AAmTrC,UAAQ,IAnT6B;AAoTrC,UAAQ,IApT6B;AAqTrC,UAAQ,IArT6B;AAsTrC,UAAQ,IAtT6B;AAuTrC,UAAQ,IAvT6B;AAwTrC,UAAQ,IAxT6B;AAyTrC,UAAQ,IAzT6B;AA0TrC,UAAQ,IA1T6B;AA2TrC,UAAQ,IA3T6B;AA4TrC,UAAQ,IA5T6B;AA6TrC,UAAQ,IA7T6B;AA8TrC,UAAQ,IA9T6B;AA+TrC,UAAQ,IA/T6B;AAgUrC,UAAQ,IAhU6B;AAiUrC,UAAQ,IAjU6B;AAkUrC,UAAQ,IAlU6B;AAmUrC,UAAQ,IAnU6B;AAoUrC,UAAQ,IApU6B;AAqUrC,UAAQ,IArU6B;AAsUrC,UAAQ,IAtU6B;AAuUrC,UAAQ,IAvU6B;AAwUrC,UAAQ,IAxU6B;AAyUrC,UAAQ,IAzU6B;AA0UrC,UAAQ,IA1U6B;AA2UrC,UAAQ,IA3U6B;AA4UrC,UAAQ,IA5U6B;AA6UrC,UAAQ,IA7U6B;AA8UrC,UAAQ,IA9U6B;AA+UrC,UAAQ,IA/U6B;AAgVrC,UAAQ;AAhV6B,CAAd,CAAzB,C", "file": "index.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Multihashes\"] = factory();\n\telse\n\t\troot[\"Multihashes\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 1);\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n\n/* eslint-disable no-proto */\n'use strict';\n\nvar base64 = require('base64-js');\n\nvar ieee754 = require('ieee754');\n\nvar isArray = require('isarray');\n\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\n\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined ? global.TYPED_ARRAY_SUPPORT : typedArraySupport();\n/*\n * Export kMaxLength after typed array support is determined.\n */\n\nexports.kMaxLength = kMaxLength();\n\nfunction typedArraySupport() {\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function foo() {\n        return 42;\n      }\n    };\n    return arr.foo() === 42 && // typed array instances can be augmented\n    typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n    arr.subarray(1, 1).byteLength === 0; // ie10 has broken `subarray`\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction kMaxLength() {\n  return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n}\n\nfunction createBuffer(that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length');\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n\n    that.length = length;\n  }\n\n  return that;\n}\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length);\n  } // Common case.\n\n\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error('If encoding is specified then the first argument must be a string');\n    }\n\n    return allocUnsafe(this, arg);\n  }\n\n  return from(this, arg, encodingOrOffset, length);\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n// TODO: Legacy, not needed anymore. Remove in next major version.\n\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr;\n};\n\nfunction from(that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length);\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset);\n  }\n\n  return fromObject(that, value);\n}\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\n\n\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length);\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    });\n  }\n}\n\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number');\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative');\n  }\n}\n\nfunction alloc(that, size, fill, encoding) {\n  assertSize(size);\n\n  if (size <= 0) {\n    return createBuffer(that, size);\n  }\n\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);\n  }\n\n  return createBuffer(that, size);\n}\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\n\n\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding);\n};\n\nfunction allocUnsafe(that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n\n  return that;\n}\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\n\n\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\n\n\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size);\n};\n\nfunction fromString(that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding');\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that;\n}\n\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n\n  return that;\n}\n\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n\n  return that;\n}\n\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that;\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that;\n  }\n\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n\n      return fromArrayLike(that, obj);\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\n\nfunction checked(length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');\n  }\n\n  return length | 0;\n}\n\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n\n  return Buffer.alloc(+length);\n}\n\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\n\nBuffer.compare = function compare(a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers');\n  }\n\n  if (a === b) return 0;\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n\n    default:\n      return false;\n  }\n};\n\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n\n  var i;\n\n  if (length === undefined) {\n    length = 0;\n\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n\n  return buffer;\n};\n\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0; // Use a for loop to avoid recursion\n\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length;\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n\n      case 'hex':\n        return len >>> 1;\n\n      case 'base64':\n        return base64ToBytes(string).length;\n\n      default:\n        if (loweredCase) return utf8ToBytes(string).length; // assume utf8\n\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\nBuffer.byteLength = byteLength;\n\nfunction slowToString(encoding, start, end) {\n  var loweredCase = false; // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n\n  if (start === undefined || start < 0) {\n    start = 0;\n  } // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n\n\n  if (start > this.length) {\n    return '';\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return '';\n  } // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n\n\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return '';\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n\n      case 'ascii':\n        return asciiSlice(this, start, end);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n\n      case 'base64':\n        return base64Slice(this, start, end);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n} // The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\n\n\nBuffer.prototype._isBuffer = true;\n\nfunction swap(b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16() {\n  var len = this.length;\n\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap32 = function swap32() {\n  var len = this.length;\n\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap64 = function swap64() {\n  var len = this.length;\n\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n\n  return this;\n};\n\nBuffer.prototype.toString = function toString() {\n  var length = this.length | 0;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\n\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\n\nBuffer.prototype.inspect = function inspect() {\n  var str = '';\n  var max = exports.INSPECT_MAX_BYTES;\n\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n\n  return '<Buffer ' + str + '>';\n};\n\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer');\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n\n  if (start >= end) {\n    return 1;\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n}; // Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\n\n\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1; // Normalize byteOffset\n\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n\n  byteOffset = +byteOffset; // Coerce to Number.\n\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  } // Normalize byteOffset: negative offsets start from the end of the buffer\n\n\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  } // Normalize val\n\n\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  } // Finally, search either indexOf (if dir is true) or lastIndexOf\n\n\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n\n    if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n\n  throw new TypeError('val must be string, number or Buffer');\n}\n\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n\n  var i;\n\n  if (dir) {\n    var foundIndex = -1;\n\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n\n      if (found) return i;\n    }\n  }\n\n  return -1;\n}\n\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\n\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\n\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n\n    if (length > remaining) {\n      length = remaining;\n    }\n  } // must be an even number of digits\n\n\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n\n  return i;\n}\n\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\n\nfunction latin1Write(buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length);\n}\n\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\n\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0; // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0; // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    } // legacy write(string, encoding, offset, length) - remove in v0.13\n\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n\n  if (!encoding) encoding = 'utf8';\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length);\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\n\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\n\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n  var i = start;\n\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n\n          break;\n\n        case 2:\n          secondByte = buf[i + 1];\n\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res);\n} // Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\n\n\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray(codePoints) {\n  var len = codePoints.length;\n\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  } // Decode in chunks to avoid \"call stack size exceeded\".\n\n\n  var res = '';\n  var i = 0;\n\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n\n  return res;\n}\n\nfunction asciiSlice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n\n  return ret;\n}\n\nfunction latin1Slice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n\n  return ret;\n}\n\nfunction hexSlice(buf, start, end) {\n  var len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  var out = '';\n\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n\n  return out;\n}\n\nfunction utf16leSlice(buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n\n  return res;\n}\n\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n  var newBuf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf;\n};\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\n\n\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\n\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\n\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nfunction objectWriteUInt16(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nfunction objectWriteUInt32(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = value >>> 24;\n    this[offset + 2] = value >>> 16;\n    this[offset + 1] = value >>> 8;\n    this[offset] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n    this[offset + 2] = value >>> 16;\n    this[offset + 3] = value >>> 24;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\n\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\n\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n}; // copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\n\n\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start; // Copy 0 bytes; we're done\n\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0; // Fatal error conditions\n\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds'); // Are we oob?\n\n  if (end > this.length) end = this.length;\n\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n\n  return len;\n}; // Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\n\n\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n\n      if (code < 256) {\n        val = code;\n      }\n    }\n\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } // Invalid ranges are not set to a default, so can range check early.\n\n\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n\n  if (end <= start) {\n    return this;\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this;\n}; // HELPER FUNCTIONS\n// ================\n\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean(str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, ''); // Node converts strings with length < 2 to ''\n\n  if (str.length < 2) return ''; // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n\n  return str;\n}\n\nfunction stringtrim(str) {\n  if (str.trim) return str.trim();\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n\nfunction toHex(n) {\n  if (n < 16) return '0' + n.toString(16);\n  return n.toString(16);\n}\n\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i); // is surrogate component\n\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } // valid lead\n\n\n        leadSurrogate = codePoint;\n        continue;\n      } // 2 leads in a row\n\n\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      } // valid surrogate pair\n\n\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null; // encode utf8\n\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n\n  return bytes;\n}\n\nfunction asciiToBytes(str) {\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n\n  return byteArray;\n}\n\nfunction utf16leToBytes(str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray;\n}\n\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\n\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n\n  return i;\n}\n\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}", "/**\n * Multihash implementation in JavaScript.\n *\n * @module multihash\n */\n'use strict'\n\nconst { <PERSON>uff<PERSON> } = require('buffer')\nconst multibase = require('multibase')\nconst varint = require('varint')\nconst cs = require('./constants')\n\nexports.names = cs.names\nexports.codes = cs.codes\nexports.defaultLengths = cs.defaultLengths\n\n/**\n * Convert the given multihash to a hex encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\nexports.toHexString = function toHexString (hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer')\n  }\n\n  return hash.toString('hex')\n}\n\n/**\n * Convert the given hex encoded string to a multihash.\n *\n * @param {string} hash\n * @returns {Buffer}\n */\nexports.fromHexString = function fromHexString (hash) {\n  return Buffer.from(hash, 'hex')\n}\n\n/**\n * Convert the given multihash to a base58 encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\nexports.toB58String = function toB58String (hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer')\n  }\n\n  return multibase.encode('base58btc', hash).toString().slice(1)\n}\n\n/**\n * Convert the given base58 encoded string to a multihash.\n *\n * @param {string|Buffer} hash\n * @returns {Buffer}\n */\nexports.fromB58String = function fromB58String (hash) {\n  let encoded = hash\n  if (Buffer.isBuffer(hash)) {\n    encoded = hash.toString()\n  }\n\n  return multibase.decode('z' + encoded)\n}\n\n/**\n * Decode a hash from the given multihash.\n *\n * @param {Buffer} buf\n * @returns {{code: number, name: string, length: number, digest: Buffer}} result\n */\nexports.decode = function decode (buf) {\n  if (!(Buffer.isBuffer(buf))) {\n    throw new Error('multihash must be a Buffer')\n  }\n\n  if (buf.length < 2) {\n    throw new Error('multihash too short. must be > 2 bytes.')\n  }\n\n  const code = varint.decode(buf)\n  if (!exports.isValidCode(code)) {\n    throw new Error(`multihash unknown function code: 0x${code.toString(16)}`)\n  }\n  buf = buf.slice(varint.decode.bytes)\n\n  const len = varint.decode(buf)\n  if (len < 0) {\n    throw new Error(`multihash invalid length: ${len}`)\n  }\n  buf = buf.slice(varint.decode.bytes)\n\n  if (buf.length !== len) {\n    throw new Error(`multihash length inconsistent: 0x${buf.toString('hex')}`)\n  }\n\n  return {\n    code: code,\n    name: cs.codes[code],\n    length: len,\n    digest: buf\n  }\n}\n\n/**\n *  Encode a hash digest along with the specified function code.\n *\n * > **Note:** the length is derived from the length of the digest itself.\n *\n * @param {Buffer} digest\n * @param {string|number} code\n * @param {number} [length]\n * @returns {Buffer}\n */\nexports.encode = function encode (digest, code, length) {\n  if (!digest || code === undefined) {\n    throw new Error('multihash encode requires at least two args: digest, code')\n  }\n\n  // ensure it's a hashfunction code.\n  const hashfn = exports.coerceCode(code)\n\n  if (!(Buffer.isBuffer(digest))) {\n    throw new Error('digest should be a Buffer')\n  }\n\n  if (length == null) {\n    length = digest.length\n  }\n\n  if (length && digest.length !== length) {\n    throw new Error('digest length should be equal to specified length.')\n  }\n\n  return Buffer.concat([\n    Buffer.from(varint.encode(hashfn)),\n    Buffer.from(varint.encode(length)),\n    digest\n  ])\n}\n\n/**\n * Converts a hash function name into the matching code.\n * If passed a number it will return the number if it's a valid code.\n * @param {string|number} name\n * @returns {number}\n */\nexports.coerceCode = function coerceCode (name) {\n  let code = name\n\n  if (typeof name === 'string') {\n    if (cs.names[name] === undefined) {\n      throw new Error(`Unrecognized hash function named: ${name}`)\n    }\n    code = cs.names[name]\n  }\n\n  if (typeof code !== 'number') {\n    throw new Error(`Hash function code should be a number. Got: ${code}`)\n  }\n\n  if (cs.codes[code] === undefined && !exports.isAppCode(code)) {\n    throw new Error(`Unrecognized function code: ${code}`)\n  }\n\n  return code\n}\n\n/**\n * Checks wether a code is part of the app range\n *\n * @param {number} code\n * @returns {boolean}\n */\nexports.isAppCode = function appCode (code) {\n  return code > 0 && code < 0x10\n}\n\n/**\n * Checks whether a multihash code is valid.\n *\n * @param {number} code\n * @returns {boolean}\n */\nexports.isValidCode = function validCode (code) {\n  if (exports.isAppCode(code)) {\n    return true\n  }\n\n  if (cs.codes[code]) {\n    return true\n  }\n\n  return false\n}\n\n/**\n * Check if the given buffer is a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\nfunction validate (multihash) {\n  exports.decode(multihash) // throws if bad.\n}\nexports.validate = validate\n\n/**\n * Returns a prefix from a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\nexports.prefix = function prefix (multihash) {\n  validate(multihash)\n\n  return multihash.slice(0, 2)\n}\n", "\"use strict\";\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;", "'use strict';\n\nexports.byteLength = byteLength;\nexports.toByteArray = toByteArray;\nexports.fromByteArray = fromByteArray;\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n} // Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\n\n\nrevLookup['-'.charCodeAt(0)] = 62;\nrevLookup['_'.charCodeAt(0)] = 63;\n\nfunction getLens(b64) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4');\n  } // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n\n\n  var validLen = b64.indexOf('=');\n  if (validLen === -1) validLen = len;\n  var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;\n  return [validLen, placeHoldersLen];\n} // base64 is 4/3 + up to two characters of the original data\n\n\nfunction byteLength(b64) {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(b64, validLen, placeHoldersLen) {\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction toByteArray(b64) {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n  var curByte = 0; // if there are placeholders, only get up to the last complete 4 chars\n\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n  var i;\n\n  for (i = 0; i < len; i += 4) {\n    tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = tmp >> 16 & 0xFF;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];\n}\n\nfunction encodeChunk(uint8, start, end) {\n  var tmp;\n  var output = [];\n\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16 & 0xFF0000) + (uint8[i + 1] << 8 & 0xFF00) + (uint8[i + 2] & 0xFF);\n    output.push(tripletToBase64(tmp));\n  }\n\n  return output.join('');\n}\n\nfunction fromByteArray(uint8) {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n  // go through the array every three bytes, we'll deal with trailing stuff later\n\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));\n  } // pad the end with zeros, but make sure to not forget the extra bytes\n\n\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 0x3F] + '==');\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 0x3F] + lookup[tmp << 2 & 0x3F] + '=');\n  }\n\n  return parts.join('');\n}", "\"use strict\";\n\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? nBytes - 1 : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n  i += d;\n  e = s & (1 << -nBits) - 1;\n  s >>= -nBits;\n  nBits += eLen;\n\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : (s ? -1 : 1) * Infinity;\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n  var i = isLE ? 0 : nBytes - 1;\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = e << mLen | m;\n  eLen += mLen;\n\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n};", "\"use strict\";\n\nvar toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};", "/**\n * Implementation of the [multibase](https://github.com/multiformats/multibase) specification.\n * @module Multibase\n */\n'use strict';\n\nconst {\n  Buffer\n} = require('buffer');\n\nconst constants = require('./constants');\n\nexports = module.exports = multibase;\nexports.encode = encode;\nexports.decode = decode;\nexports.isEncoded = isEncoded;\nexports.names = Object.freeze(Object.keys(constants.names));\nexports.codes = Object.freeze(Object.keys(constants.codes));\n/**\n * Create a new buffer with the multibase varint+code.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be prefixed with multibase.\n * @memberof Multibase\n * @returns {Buffer}\n */\n\nfunction multibase(nameOrCode, buf) {\n  if (!buf) {\n    throw new Error('requires an encoded buffer');\n  }\n\n  const base = getBase(nameOrCode);\n  const codeBuf = Buffer.from(base.code);\n  const name = base.name;\n  validEncode(name, buf);\n  return Buffer.concat([codeBuf, buf]);\n}\n/**\n * Encode data with the specified base and add the multibase prefix.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be encoded.\n * @returns {Buffer}\n * @memberof Multibase\n */\n\n\nfunction encode(nameOrCode, buf) {\n  const base = getBase(nameOrCode);\n  const name = base.name;\n  return multibase(name, Buffer.from(base.encode(buf)));\n}\n/**\n * Takes a buffer or string encoded with multibase header, decodes it and\n * returns the decoded buffer\n *\n * @param {Buffer|string} bufOrString\n * @returns {Buffer}\n * @memberof Multibase\n *\n */\n\n\nfunction decode(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  }\n\n  const code = bufOrString.substring(0, 1);\n  bufOrString = bufOrString.substring(1, bufOrString.length);\n\n  if (typeof bufOrString === 'string') {\n    bufOrString = Buffer.from(bufOrString);\n  }\n\n  const base = getBase(code);\n  return Buffer.from(base.decode(bufOrString.toString()));\n}\n/**\n * Is the given data multibase encoded?\n *\n * @param {Buffer|string} bufOrString\n * @returns {boolean}\n * @memberof Multibase\n */\n\n\nfunction isEncoded(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  } // Ensure bufOrString is a string\n\n\n  if (Object.prototype.toString.call(bufOrString) !== '[object String]') {\n    return false;\n  }\n\n  const code = bufOrString.substring(0, 1);\n\n  try {\n    const base = getBase(code);\n    return base.name;\n  } catch (err) {\n    return false;\n  }\n}\n/**\n * @param {string} name\n * @param {Buffer} buf\n * @private\n * @returns {undefined}\n */\n\n\nfunction validEncode(name, buf) {\n  const base = getBase(name);\n  base.decode(buf.toString());\n}\n\nfunction getBase(nameOrCode) {\n  let base;\n\n  if (constants.names[nameOrCode]) {\n    base = constants.names[nameOrCode];\n  } else if (constants.codes[nameOrCode]) {\n    base = constants.codes[nameOrCode];\n  } else {\n    throw new Error('Unsupported encoding');\n  }\n\n  if (!base.isImplemented()) {\n    throw new Error('Base ' + nameOrCode + ' is not implemented yet');\n  }\n\n  return base;\n}", "'use strict';\n\nconst Base = require('./base.js');\n\nconst baseX = require('base-x');\n\nconst base16 = require('./base16');\n\nconst base32 = require('./base32');\n\nconst base64 = require('./base64'); // name, code, implementation, alphabet\n\n\nconst constants = [['base1', '1', '', '1'], ['base2', '0', baseX, '01'], ['base8', '7', baseX, '01234567'], ['base10', '9', baseX, '0123456789'], ['base16', 'f', base16, '0123456789abcdef'], ['base32', 'b', base32, 'abcdefghijklmnopqrstuvwxyz234567'], ['base32pad', 'c', base32, 'abcdefghijklmnopqrstuvwxyz234567='], ['base32hex', 'v', base32, '0123456789abcdefghijklmnopqrstuv'], ['base32hexpad', 't', base32, '0123456789abcdefghijklmnopqrstuv='], ['base32z', 'h', base32, 'ybndrfg8ejkmcpqxot1uwisza345h769'], ['base58flickr', 'Z', baseX, '**********************************************************'], ['base58btc', 'z', baseX, '**********************************************************'], ['base64', 'm', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'], ['base64pad', 'M', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='], ['base64url', 'u', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'], ['base64urlpad', 'U', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=']];\nconst names = constants.reduce((prev, tupple) => {\n  prev[tupple[0]] = new Base(tupple[0], tupple[1], tupple[2], tupple[3]);\n  return prev;\n}, {});\nconst codes = constants.reduce((prev, tupple) => {\n  prev[tupple[1]] = names[tupple[0]];\n  return prev;\n}, {});\nmodule.exports = {\n  names: names,\n  codes: codes\n};", "'use strict';\n\nclass Base {\n  constructor(name, code, implementation, alphabet) {\n    this.name = name;\n    this.code = code;\n    this.alphabet = alphabet;\n\n    if (implementation && alphabet) {\n      this.engine = implementation(alphabet);\n    }\n  }\n\n  encode(stringOrBuffer) {\n    return this.engine.encode(stringOrBuffer);\n  }\n\n  decode(stringOrBuffer) {\n    return this.engine.decode(stringOrBuffer);\n  }\n\n  isImplemented() {\n    return this.engine;\n  }\n\n}\n\nmodule.exports = Base;", "'use strict'; // base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\n// @ts-ignore\n\nvar _Buffer = require('safe-buffer').Buffer;\n\nfunction base(ALPHABET) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n\n  var BASE_MAP = new Uint8Array(256);\n\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n\n    BASE_MAP[xc] = i;\n  }\n\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256); // log(BASE) / log(256), rounded up\n\n  var iFACTOR = Math.log(256) / Math.log(BASE); // log(256) / log(BASE), rounded up\n\n  function encode(source) {\n    if (Array.isArray(source) || source instanceof Uint8Array) {\n      source = _Buffer.from(source);\n    }\n\n    if (!_Buffer.isBuffer(source)) {\n      throw new TypeError('Expected Buffer');\n    }\n\n    if (source.length === 0) {\n      return '';\n    } // Skip & count leading zeroes.\n\n\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    } // Allocate enough space in big-endian base58 representation.\n\n\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size); // Process the bytes.\n\n    while (pbegin !== pend) {\n      var carry = source[pbegin]; // Apply \"b58 = b58 * 256 + ch\".\n\n      var i = 0;\n\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n\n      length = i;\n      pbegin++;\n    } // Skip leading zeroes in base58 result.\n\n\n    var it2 = size - length;\n\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    } // Translate the result into a string.\n\n\n    var str = LEADER.repeat(zeroes);\n\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n\n    return str;\n  }\n\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n\n    if (source.length === 0) {\n      return _Buffer.alloc(0);\n    }\n\n    var psz = 0; // Skip leading spaces.\n\n    if (source[psz] === ' ') {\n      return;\n    } // Skip and count leading '1's.\n\n\n    var zeroes = 0;\n    var length = 0;\n\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    } // Allocate enough space in big-endian base256 representation.\n\n\n    var size = (source.length - psz) * FACTOR + 1 >>> 0; // log(58) / log(256), rounded up.\n\n    var b256 = new Uint8Array(size); // Process the characters.\n\n    while (source[psz]) {\n      // Decode character\n      var carry = BASE_MAP[source.charCodeAt(psz)]; // Invalid character\n\n      if (carry === 255) {\n        return;\n      }\n\n      var i = 0;\n\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n\n      length = i;\n      psz++;\n    } // Skip trailing spaces.\n\n\n    if (source[psz] === ' ') {\n      return;\n    } // Skip leading zeroes in b256.\n\n\n    var it4 = size - length;\n\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n\n    var vch = _Buffer.allocUnsafe(zeroes + (size - it4));\n\n    vch.fill(0x00, 0, zeroes);\n    var j = zeroes;\n\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n\n    return vch;\n  }\n\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n\n    if (buffer) {\n      return buffer;\n    }\n\n    throw new Error('Non-base' + BASE + ' character');\n  }\n\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\n\nmodule.exports = base;", "\"use strict\";\n\n/*! safe-buffer. MIT License. Feross Aboukhadi<PERSON> <https://feross.org/opensource> */\n\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer');\n\nvar Buffer = buffer.Buffer; // alternative to using Object.keys for old browsers\n\nfunction copyProps(src, dst) {\n  for (var key in src) {\n    dst[key] = src[key];\n  }\n}\n\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer;\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports);\n  exports.Buffer = SafeBuffer;\n}\n\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length);\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype); // Copy static methods from Buffer\n\ncopyProps(Buffer, SafeBuffer);\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number');\n  }\n\n  return Buffer(arg, encodingOrOffset, length);\n};\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  var buf = Buffer(size);\n\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding);\n    } else {\n      buf.fill(fill);\n    }\n  } else {\n    buf.fill(0);\n  }\n\n  return buf;\n};\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return Buffer(size);\n};\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return buffer.SlowBuffer(size);\n};", "'use strict';\n\nconst {\n  <PERSON><PERSON><PERSON>\n} = require('buffer');\n\nmodule.exports = function base16(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return Buffer.from(input).toString('hex');\n      }\n\n      return input.toString('hex');\n    },\n\n    decode(input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base16 character');\n        }\n      }\n\n      return Buffer.from(input, 'hex');\n    }\n\n  };\n};", "'use strict';\n\nfunction decode(input, alphabet) {\n  input = input.replace(new RegExp('=', 'g'), '');\n  const length = input.length;\n  let bits = 0;\n  let value = 0;\n  let index = 0;\n  const output = new Uint8Array(length * 5 / 8 | 0);\n\n  for (let i = 0; i < length; i++) {\n    value = value << 5 | alphabet.indexOf(input[i]);\n    bits += 5;\n\n    if (bits >= 8) {\n      output[index++] = value >>> bits - 8 & 255;\n      bits -= 8;\n    }\n  }\n\n  return output.buffer;\n}\n\nfunction encode(buffer, alphabet) {\n  const length = buffer.byteLength;\n  const view = new Uint8Array(buffer);\n  const padding = alphabet.indexOf('=') === alphabet.length - 1;\n\n  if (padding) {\n    alphabet = alphabet.substring(0, alphabet.length - 1);\n  }\n\n  let bits = 0;\n  let value = 0;\n  let output = '';\n\n  for (let i = 0; i < length; i++) {\n    value = value << 8 | view[i];\n    bits += 8;\n\n    while (bits >= 5) {\n      output += alphabet[value >>> bits - 5 & 31];\n      bits -= 5;\n    }\n  }\n\n  if (bits > 0) {\n    output += alphabet[value << 5 - bits & 31];\n  }\n\n  if (padding) {\n    while (output.length % 8 !== 0) {\n      output += '=';\n    }\n  }\n\n  return output;\n}\n\nmodule.exports = function base32(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return encode(Uint8Array.from(input), alphabet);\n      }\n\n      return encode(input, alphabet);\n    },\n\n    decode(input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base32 character');\n        }\n      }\n\n      return decode(input, alphabet);\n    }\n\n  };\n};", "'use strict';\n\nconst {\n  <PERSON><PERSON><PERSON>\n} = require('buffer');\n\nmodule.exports = function base64(alphabet) {\n  // The alphabet is only used to know:\n  //   1. If padding is enabled (must contain '=')\n  //   2. If the output must be url-safe (must contain '-' and '_')\n  //   3. If the input of the output function is valid\n  // The alphabets from RFC 4648 are always used.\n  const padding = alphabet.indexOf('=') > -1;\n  const url = alphabet.indexOf('-') > -1 && alphabet.indexOf('_') > -1;\n  return {\n    encode(input) {\n      let output = '';\n\n      if (typeof input === 'string') {\n        output = Buffer.from(input).toString('base64');\n      } else {\n        output = input.toString('base64');\n      }\n\n      if (url) {\n        output = output.replace(/\\+/g, '-').replace(/\\//g, '_');\n      }\n\n      const pad = output.indexOf('=');\n\n      if (pad > 0 && !padding) {\n        output = output.substring(0, pad);\n      }\n\n      return output;\n    },\n\n    decode(input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base64 character');\n        }\n      }\n\n      return Buffer.from(input, 'base64');\n    }\n\n  };\n};", "\"use strict\";\n\nmodule.exports = {\n  encode: require('./encode.js'),\n  decode: require('./decode.js'),\n  encodingLength: require('./length.js')\n};", "\"use strict\";\n\nmodule.exports = encode;\nvar MSB = 0x80,\n    REST = 0x7F,\n    MSBALL = ~REST,\n    INT = Math.pow(2, 31);\n\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n\n  while (num >= INT) {\n    out[offset++] = num & 0xFF | MSB;\n    num /= 128;\n  }\n\n  while (num & MSBALL) {\n    out[offset++] = num & 0xFF | MSB;\n    num >>>= 7;\n  }\n\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}", "\"use strict\";\n\nmodule.exports = read;\nvar MSB = 0x80,\n    REST = 0x7F;\n\nfunction read(buf, offset) {\n  var res = 0,\n      offset = offset || 0,\n      shift = 0,\n      counter = offset,\n      b,\n      l = buf.length;\n\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST) << shift : (b & REST) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB);\n\n  read.bytes = counter - offset;\n  return res;\n}", "\"use strict\";\n\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\n\nmodule.exports = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};", "/* eslint quote-props: off */\n/* eslint key-spacing: off */\n'use strict'\n\nexports.names = Object.freeze({\n  'identity':   0x0,\n  'sha1':       0x11,\n  'sha2-256':   0x12,\n  'sha2-512':   0x13,\n  'dbl-sha2-256': 0x56,\n  'sha3-224':   0x17,\n  'sha3-256':   0x16,\n  'sha3-384':   0x15,\n  'sha3-512':   0x14,\n  'shake-128':  0x18,\n  'shake-256':  0x19,\n  'keccak-224': 0x1A,\n  'keccak-256': 0x1B,\n  'keccak-384': 0x1C,\n  'keccak-512': 0x1D,\n  'murmur3-128': 0x22,\n  'murmur3-32':  0x23,\n  'md4':         0xd4,\n  'md5':         0xd5,\n  'blake2b-8':   0xb201,\n  'blake2b-16':  0xb202,\n  'blake2b-24':  0xb203,\n  'blake2b-32':  0xb204,\n  'blake2b-40':  0xb205,\n  'blake2b-48':  0xb206,\n  'blake2b-56':  0xb207,\n  'blake2b-64':  0xb208,\n  'blake2b-72':  0xb209,\n  'blake2b-80':  0xb20a,\n  'blake2b-88':  0xb20b,\n  'blake2b-96':  0xb20c,\n  'blake2b-104': 0xb20d,\n  'blake2b-112': 0xb20e,\n  'blake2b-120': 0xb20f,\n  'blake2b-128': 0xb210,\n  'blake2b-136': 0xb211,\n  'blake2b-144': 0xb212,\n  'blake2b-152': 0xb213,\n  'blake2b-160': 0xb214,\n  'blake2b-168': 0xb215,\n  'blake2b-176': 0xb216,\n  'blake2b-184': 0xb217,\n  'blake2b-192': 0xb218,\n  'blake2b-200': 0xb219,\n  'blake2b-208': 0xb21a,\n  'blake2b-216': 0xb21b,\n  'blake2b-224': 0xb21c,\n  'blake2b-232': 0xb21d,\n  'blake2b-240': 0xb21e,\n  'blake2b-248': 0xb21f,\n  'blake2b-256': 0xb220,\n  'blake2b-264': 0xb221,\n  'blake2b-272': 0xb222,\n  'blake2b-280': 0xb223,\n  'blake2b-288': 0xb224,\n  'blake2b-296': 0xb225,\n  'blake2b-304': 0xb226,\n  'blake2b-312': 0xb227,\n  'blake2b-320': 0xb228,\n  'blake2b-328': 0xb229,\n  'blake2b-336': 0xb22a,\n  'blake2b-344': 0xb22b,\n  'blake2b-352': 0xb22c,\n  'blake2b-360': 0xb22d,\n  'blake2b-368': 0xb22e,\n  'blake2b-376': 0xb22f,\n  'blake2b-384': 0xb230,\n  'blake2b-392': 0xb231,\n  'blake2b-400': 0xb232,\n  'blake2b-408': 0xb233,\n  'blake2b-416': 0xb234,\n  'blake2b-424': 0xb235,\n  'blake2b-432': 0xb236,\n  'blake2b-440': 0xb237,\n  'blake2b-448': 0xb238,\n  'blake2b-456': 0xb239,\n  'blake2b-464': 0xb23a,\n  'blake2b-472': 0xb23b,\n  'blake2b-480': 0xb23c,\n  'blake2b-488': 0xb23d,\n  'blake2b-496': 0xb23e,\n  'blake2b-504': 0xb23f,\n  'blake2b-512': 0xb240,\n  'blake2s-8':   0xb241,\n  'blake2s-16':  0xb242,\n  'blake2s-24':  0xb243,\n  'blake2s-32':  0xb244,\n  'blake2s-40':  0xb245,\n  'blake2s-48':  0xb246,\n  'blake2s-56':  0xb247,\n  'blake2s-64':  0xb248,\n  'blake2s-72':  0xb249,\n  'blake2s-80':  0xb24a,\n  'blake2s-88':  0xb24b,\n  'blake2s-96':  0xb24c,\n  'blake2s-104': 0xb24d,\n  'blake2s-112': 0xb24e,\n  'blake2s-120': 0xb24f,\n  'blake2s-128': 0xb250,\n  'blake2s-136': 0xb251,\n  'blake2s-144': 0xb252,\n  'blake2s-152': 0xb253,\n  'blake2s-160': 0xb254,\n  'blake2s-168': 0xb255,\n  'blake2s-176': 0xb256,\n  'blake2s-184': 0xb257,\n  'blake2s-192': 0xb258,\n  'blake2s-200': 0xb259,\n  'blake2s-208': 0xb25a,\n  'blake2s-216': 0xb25b,\n  'blake2s-224': 0xb25c,\n  'blake2s-232': 0xb25d,\n  'blake2s-240': 0xb25e,\n  'blake2s-248': 0xb25f,\n  'blake2s-256': 0xb260,\n  'Skein256-8': 0xb301,\n  'Skein256-16': 0xb302,\n  'Skein256-24': 0xb303,\n  'Skein256-32': 0xb304,\n  'Skein256-40': 0xb305,\n  'Skein256-48': 0xb306,\n  'Skein256-56': 0xb307,\n  'Skein256-64': 0xb308,\n  'Skein256-72': 0xb309,\n  'Skein256-80': 0xb30a,\n  'Skein256-88': 0xb30b,\n  'Skein256-96': 0xb30c,\n  'Skein256-104': 0xb30d,\n  'Skein256-112': 0xb30e,\n  'Skein256-120': 0xb30f,\n  'Skein256-128': 0xb310,\n  'Skein256-136': 0xb311,\n  'Skein256-144': 0xb312,\n  'Skein256-152': 0xb313,\n  'Skein256-160': 0xb314,\n  'Skein256-168': 0xb315,\n  'Skein256-176': 0xb316,\n  'Skein256-184': 0xb317,\n  'Skein256-192': 0xb318,\n  'Skein256-200': 0xb319,\n  'Skein256-208': 0xb31a,\n  'Skein256-216': 0xb31b,\n  'Skein256-224': 0xb31c,\n  'Skein256-232': 0xb31d,\n  'Skein256-240': 0xb31e,\n  'Skein256-248': 0xb31f,\n  'Skein256-256': 0xb320,\n  'Skein512-8': 0xb321,\n  'Skein512-16': 0xb322,\n  'Skein512-24': 0xb323,\n  'Skein512-32': 0xb324,\n  'Skein512-40': 0xb325,\n  'Skein512-48': 0xb326,\n  'Skein512-56': 0xb327,\n  'Skein512-64': 0xb328,\n  'Skein512-72': 0xb329,\n  'Skein512-80': 0xb32a,\n  'Skein512-88': 0xb32b,\n  'Skein512-96': 0xb32c,\n  'Skein512-104': 0xb32d,\n  'Skein512-112': 0xb32e,\n  'Skein512-120': 0xb32f,\n  'Skein512-128': 0xb330,\n  'Skein512-136': 0xb331,\n  'Skein512-144': 0xb332,\n  'Skein512-152': 0xb333,\n  'Skein512-160': 0xb334,\n  'Skein512-168': 0xb335,\n  'Skein512-176': 0xb336,\n  'Skein512-184': 0xb337,\n  'Skein512-192': 0xb338,\n  'Skein512-200': 0xb339,\n  'Skein512-208': 0xb33a,\n  'Skein512-216': 0xb33b,\n  'Skein512-224': 0xb33c,\n  'Skein512-232': 0xb33d,\n  'Skein512-240': 0xb33e,\n  'Skein512-248': 0xb33f,\n  'Skein512-256': 0xb340,\n  'Skein512-264': 0xb341,\n  'Skein512-272': 0xb342,\n  'Skein512-280': 0xb343,\n  'Skein512-288': 0xb344,\n  'Skein512-296': 0xb345,\n  'Skein512-304': 0xb346,\n  'Skein512-312': 0xb347,\n  'Skein512-320': 0xb348,\n  'Skein512-328': 0xb349,\n  'Skein512-336': 0xb34a,\n  'Skein512-344': 0xb34b,\n  'Skein512-352': 0xb34c,\n  'Skein512-360': 0xb34d,\n  'Skein512-368': 0xb34e,\n  'Skein512-376': 0xb34f,\n  'Skein512-384': 0xb350,\n  'Skein512-392': 0xb351,\n  'Skein512-400': 0xb352,\n  'Skein512-408': 0xb353,\n  'Skein512-416': 0xb354,\n  'Skein512-424': 0xb355,\n  'Skein512-432': 0xb356,\n  'Skein512-440': 0xb357,\n  'Skein512-448': 0xb358,\n  'Skein512-456': 0xb359,\n  'Skein512-464': 0xb35a,\n  'Skein512-472': 0xb35b,\n  'Skein512-480': 0xb35c,\n  'Skein512-488': 0xb35d,\n  'Skein512-496': 0xb35e,\n  'Skein512-504': 0xb35f,\n  'Skein512-512': 0xb360,\n  'Skein1024-8': 0xb361,\n  'Skein1024-16': 0xb362,\n  'Skein1024-24': 0xb363,\n  'Skein1024-32': 0xb364,\n  'Skein1024-40': 0xb365,\n  'Skein1024-48': 0xb366,\n  'Skein1024-56': 0xb367,\n  'Skein1024-64': 0xb368,\n  'Skein1024-72': 0xb369,\n  'Skein1024-80': 0xb36a,\n  'Skein1024-88': 0xb36b,\n  'Skein1024-96': 0xb36c,\n  'Skein1024-104': 0xb36d,\n  'Skein1024-112': 0xb36e,\n  'Skein1024-120': 0xb36f,\n  'Skein1024-128': 0xb370,\n  'Skein1024-136': 0xb371,\n  'Skein1024-144': 0xb372,\n  'Skein1024-152': 0xb373,\n  'Skein1024-160': 0xb374,\n  'Skein1024-168': 0xb375,\n  'Skein1024-176': 0xb376,\n  'Skein1024-184': 0xb377,\n  'Skein1024-192': 0xb378,\n  'Skein1024-200': 0xb379,\n  'Skein1024-208': 0xb37a,\n  'Skein1024-216': 0xb37b,\n  'Skein1024-224': 0xb37c,\n  'Skein1024-232': 0xb37d,\n  'Skein1024-240': 0xb37e,\n  'Skein1024-248': 0xb37f,\n  'Skein1024-256': 0xb380,\n  'Skein1024-264': 0xb381,\n  'Skein1024-272': 0xb382,\n  'Skein1024-280': 0xb383,\n  'Skein1024-288': 0xb384,\n  'Skein1024-296': 0xb385,\n  'Skein1024-304': 0xb386,\n  'Skein1024-312': 0xb387,\n  'Skein1024-320': 0xb388,\n  'Skein1024-328': 0xb389,\n  'Skein1024-336': 0xb38a,\n  'Skein1024-344': 0xb38b,\n  'Skein1024-352': 0xb38c,\n  'Skein1024-360': 0xb38d,\n  'Skein1024-368': 0xb38e,\n  'Skein1024-376': 0xb38f,\n  'Skein1024-384': 0xb390,\n  'Skein1024-392': 0xb391,\n  'Skein1024-400': 0xb392,\n  'Skein1024-408': 0xb393,\n  'Skein1024-416': 0xb394,\n  'Skein1024-424': 0xb395,\n  'Skein1024-432': 0xb396,\n  'Skein1024-440': 0xb397,\n  'Skein1024-448': 0xb398,\n  'Skein1024-456': 0xb399,\n  'Skein1024-464': 0xb39a,\n  'Skein1024-472': 0xb39b,\n  'Skein1024-480': 0xb39c,\n  'Skein1024-488': 0xb39d,\n  'Skein1024-496': 0xb39e,\n  'Skein1024-504': 0xb39f,\n  'Skein1024-512': 0xb3a0,\n  'Skein1024-520': 0xb3a1,\n  'Skein1024-528': 0xb3a2,\n  'Skein1024-536': 0xb3a3,\n  'Skein1024-544': 0xb3a4,\n  'Skein1024-552': 0xb3a5,\n  'Skein1024-560': 0xb3a6,\n  'Skein1024-568': 0xb3a7,\n  'Skein1024-576': 0xb3a8,\n  'Skein1024-584': 0xb3a9,\n  'Skein1024-592': 0xb3aa,\n  'Skein1024-600': 0xb3ab,\n  'Skein1024-608': 0xb3ac,\n  'Skein1024-616': 0xb3ad,\n  'Skein1024-624': 0xb3ae,\n  'Skein1024-632': 0xb3af,\n  'Skein1024-640': 0xb3b0,\n  'Skein1024-648': 0xb3b1,\n  'Skein1024-656': 0xb3b2,\n  'Skein1024-664': 0xb3b3,\n  'Skein1024-672': 0xb3b4,\n  'Skein1024-680': 0xb3b5,\n  'Skein1024-688': 0xb3b6,\n  'Skein1024-696': 0xb3b7,\n  'Skein1024-704': 0xb3b8,\n  'Skein1024-712': 0xb3b9,\n  'Skein1024-720': 0xb3ba,\n  'Skein1024-728': 0xb3bb,\n  'Skein1024-736': 0xb3bc,\n  'Skein1024-744': 0xb3bd,\n  'Skein1024-752': 0xb3be,\n  'Skein1024-760': 0xb3bf,\n  'Skein1024-768': 0xb3c0,\n  'Skein1024-776': 0xb3c1,\n  'Skein1024-784': 0xb3c2,\n  'Skein1024-792': 0xb3c3,\n  'Skein1024-800': 0xb3c4,\n  'Skein1024-808': 0xb3c5,\n  'Skein1024-816': 0xb3c6,\n  'Skein1024-824': 0xb3c7,\n  'Skein1024-832': 0xb3c8,\n  'Skein1024-840': 0xb3c9,\n  'Skein1024-848': 0xb3ca,\n  'Skein1024-856': 0xb3cb,\n  'Skein1024-864': 0xb3cc,\n  'Skein1024-872': 0xb3cd,\n  'Skein1024-880': 0xb3ce,\n  'Skein1024-888': 0xb3cf,\n  'Skein1024-896': 0xb3d0,\n  'Skein1024-904': 0xb3d1,\n  'Skein1024-912': 0xb3d2,\n  'Skein1024-920': 0xb3d3,\n  'Skein1024-928': 0xb3d4,\n  'Skein1024-936': 0xb3d5,\n  'Skein1024-944': 0xb3d6,\n  'Skein1024-952': 0xb3d7,\n  'Skein1024-960': 0xb3d8,\n  'Skein1024-968': 0xb3d9,\n  'Skein1024-976': 0xb3da,\n  'Skein1024-984': 0xb3db,\n  'Skein1024-992': 0xb3dc,\n  'Skein1024-1000': 0xb3dd,\n  'Skein1024-1008': 0xb3de,\n  'Skein1024-1016': 0xb3df,\n  'Skein1024-1024': 0xb3e0\n})\n\nexports.codes = Object.freeze({\n  0x0: 'identity',\n\n  // sha family\n  0x11: 'sha1',\n  0x12: 'sha2-256',\n  0x13: 'sha2-512',\n  0x56: 'dbl-sha2-256',\n  0x17: 'sha3-224',\n  0x16: 'sha3-256',\n  0x15: 'sha3-384',\n  0x14: 'sha3-512',\n  0x18: 'shake-128',\n  0x19: 'shake-256',\n  0x1A: 'keccak-224',\n  0x1B: 'keccak-256',\n  0x1C: 'keccak-384',\n  0x1D: 'keccak-512',\n\n  0x22: 'murmur3-128',\n  0x23: 'murmur3-32',\n\n  0xd4: 'md4',\n  0xd5: 'md5',\n\n  // blake2\n  0xb201: 'blake2b-8',\n  0xb202: 'blake2b-16',\n  0xb203: 'blake2b-24',\n  0xb204: 'blake2b-32',\n  0xb205: 'blake2b-40',\n  0xb206: 'blake2b-48',\n  0xb207: 'blake2b-56',\n  0xb208: 'blake2b-64',\n  0xb209: 'blake2b-72',\n  0xb20a: 'blake2b-80',\n  0xb20b: 'blake2b-88',\n  0xb20c: 'blake2b-96',\n  0xb20d: 'blake2b-104',\n  0xb20e: 'blake2b-112',\n  0xb20f: 'blake2b-120',\n  0xb210: 'blake2b-128',\n  0xb211: 'blake2b-136',\n  0xb212: 'blake2b-144',\n  0xb213: 'blake2b-152',\n  0xb214: 'blake2b-160',\n  0xb215: 'blake2b-168',\n  0xb216: 'blake2b-176',\n  0xb217: 'blake2b-184',\n  0xb218: 'blake2b-192',\n  0xb219: 'blake2b-200',\n  0xb21a: 'blake2b-208',\n  0xb21b: 'blake2b-216',\n  0xb21c: 'blake2b-224',\n  0xb21d: 'blake2b-232',\n  0xb21e: 'blake2b-240',\n  0xb21f: 'blake2b-248',\n  0xb220: 'blake2b-256',\n  0xb221: 'blake2b-264',\n  0xb222: 'blake2b-272',\n  0xb223: 'blake2b-280',\n  0xb224: 'blake2b-288',\n  0xb225: 'blake2b-296',\n  0xb226: 'blake2b-304',\n  0xb227: 'blake2b-312',\n  0xb228: 'blake2b-320',\n  0xb229: 'blake2b-328',\n  0xb22a: 'blake2b-336',\n  0xb22b: 'blake2b-344',\n  0xb22c: 'blake2b-352',\n  0xb22d: 'blake2b-360',\n  0xb22e: 'blake2b-368',\n  0xb22f: 'blake2b-376',\n  0xb230: 'blake2b-384',\n  0xb231: 'blake2b-392',\n  0xb232: 'blake2b-400',\n  0xb233: 'blake2b-408',\n  0xb234: 'blake2b-416',\n  0xb235: 'blake2b-424',\n  0xb236: 'blake2b-432',\n  0xb237: 'blake2b-440',\n  0xb238: 'blake2b-448',\n  0xb239: 'blake2b-456',\n  0xb23a: 'blake2b-464',\n  0xb23b: 'blake2b-472',\n  0xb23c: 'blake2b-480',\n  0xb23d: 'blake2b-488',\n  0xb23e: 'blake2b-496',\n  0xb23f: 'blake2b-504',\n  0xb240: 'blake2b-512',\n  0xb241: 'blake2s-8',\n  0xb242: 'blake2s-16',\n  0xb243: 'blake2s-24',\n  0xb244: 'blake2s-32',\n  0xb245: 'blake2s-40',\n  0xb246: 'blake2s-48',\n  0xb247: 'blake2s-56',\n  0xb248: 'blake2s-64',\n  0xb249: 'blake2s-72',\n  0xb24a: 'blake2s-80',\n  0xb24b: 'blake2s-88',\n  0xb24c: 'blake2s-96',\n  0xb24d: 'blake2s-104',\n  0xb24e: 'blake2s-112',\n  0xb24f: 'blake2s-120',\n  0xb250: 'blake2s-128',\n  0xb251: 'blake2s-136',\n  0xb252: 'blake2s-144',\n  0xb253: 'blake2s-152',\n  0xb254: 'blake2s-160',\n  0xb255: 'blake2s-168',\n  0xb256: 'blake2s-176',\n  0xb257: 'blake2s-184',\n  0xb258: 'blake2s-192',\n  0xb259: 'blake2s-200',\n  0xb25a: 'blake2s-208',\n  0xb25b: 'blake2s-216',\n  0xb25c: 'blake2s-224',\n  0xb25d: 'blake2s-232',\n  0xb25e: 'blake2s-240',\n  0xb25f: 'blake2s-248',\n  0xb260: 'blake2s-256',\n\n  // skein\n  0xb301: 'Skein256-8',\n  0xb302: 'Skein256-16',\n  0xb303: 'Skein256-24',\n  0xb304: 'Skein256-32',\n  0xb305: 'Skein256-40',\n  0xb306: 'Skein256-48',\n  0xb307: 'Skein256-56',\n  0xb308: 'Skein256-64',\n  0xb309: 'Skein256-72',\n  0xb30a: 'Skein256-80',\n  0xb30b: 'Skein256-88',\n  0xb30c: 'Skein256-96',\n  0xb30d: 'Skein256-104',\n  0xb30e: 'Skein256-112',\n  0xb30f: 'Skein256-120',\n  0xb310: 'Skein256-128',\n  0xb311: 'Skein256-136',\n  0xb312: 'Skein256-144',\n  0xb313: 'Skein256-152',\n  0xb314: 'Skein256-160',\n  0xb315: 'Skein256-168',\n  0xb316: 'Skein256-176',\n  0xb317: 'Skein256-184',\n  0xb318: 'Skein256-192',\n  0xb319: 'Skein256-200',\n  0xb31a: 'Skein256-208',\n  0xb31b: 'Skein256-216',\n  0xb31c: 'Skein256-224',\n  0xb31d: 'Skein256-232',\n  0xb31e: 'Skein256-240',\n  0xb31f: 'Skein256-248',\n  0xb320: 'Skein256-256',\n  0xb321: 'Skein512-8',\n  0xb322: 'Skein512-16',\n  0xb323: 'Skein512-24',\n  0xb324: 'Skein512-32',\n  0xb325: 'Skein512-40',\n  0xb326: 'Skein512-48',\n  0xb327: 'Skein512-56',\n  0xb328: 'Skein512-64',\n  0xb329: 'Skein512-72',\n  0xb32a: 'Skein512-80',\n  0xb32b: 'Skein512-88',\n  0xb32c: 'Skein512-96',\n  0xb32d: 'Skein512-104',\n  0xb32e: 'Skein512-112',\n  0xb32f: 'Skein512-120',\n  0xb330: 'Skein512-128',\n  0xb331: 'Skein512-136',\n  0xb332: 'Skein512-144',\n  0xb333: 'Skein512-152',\n  0xb334: 'Skein512-160',\n  0xb335: 'Skein512-168',\n  0xb336: 'Skein512-176',\n  0xb337: 'Skein512-184',\n  0xb338: 'Skein512-192',\n  0xb339: 'Skein512-200',\n  0xb33a: 'Skein512-208',\n  0xb33b: 'Skein512-216',\n  0xb33c: 'Skein512-224',\n  0xb33d: 'Skein512-232',\n  0xb33e: 'Skein512-240',\n  0xb33f: 'Skein512-248',\n  0xb340: 'Skein512-256',\n  0xb341: 'Skein512-264',\n  0xb342: 'Skein512-272',\n  0xb343: 'Skein512-280',\n  0xb344: 'Skein512-288',\n  0xb345: 'Skein512-296',\n  0xb346: 'Skein512-304',\n  0xb347: 'Skein512-312',\n  0xb348: 'Skein512-320',\n  0xb349: 'Skein512-328',\n  0xb34a: 'Skein512-336',\n  0xb34b: 'Skein512-344',\n  0xb34c: 'Skein512-352',\n  0xb34d: 'Skein512-360',\n  0xb34e: 'Skein512-368',\n  0xb34f: 'Skein512-376',\n  0xb350: 'Skein512-384',\n  0xb351: 'Skein512-392',\n  0xb352: 'Skein512-400',\n  0xb353: 'Skein512-408',\n  0xb354: 'Skein512-416',\n  0xb355: 'Skein512-424',\n  0xb356: 'Skein512-432',\n  0xb357: 'Skein512-440',\n  0xb358: 'Skein512-448',\n  0xb359: 'Skein512-456',\n  0xb35a: 'Skein512-464',\n  0xb35b: 'Skein512-472',\n  0xb35c: 'Skein512-480',\n  0xb35d: 'Skein512-488',\n  0xb35e: 'Skein512-496',\n  0xb35f: 'Skein512-504',\n  0xb360: 'Skein512-512',\n  0xb361: 'Skein1024-8',\n  0xb362: 'Skein1024-16',\n  0xb363: 'Skein1024-24',\n  0xb364: 'Skein1024-32',\n  0xb365: 'Skein1024-40',\n  0xb366: 'Skein1024-48',\n  0xb367: 'Skein1024-56',\n  0xb368: 'Skein1024-64',\n  0xb369: 'Skein1024-72',\n  0xb36a: 'Skein1024-80',\n  0xb36b: 'Skein1024-88',\n  0xb36c: 'Skein1024-96',\n  0xb36d: 'Skein1024-104',\n  0xb36e: 'Skein1024-112',\n  0xb36f: 'Skein1024-120',\n  0xb370: 'Skein1024-128',\n  0xb371: 'Skein1024-136',\n  0xb372: 'Skein1024-144',\n  0xb373: 'Skein1024-152',\n  0xb374: 'Skein1024-160',\n  0xb375: 'Skein1024-168',\n  0xb376: 'Skein1024-176',\n  0xb377: 'Skein1024-184',\n  0xb378: 'Skein1024-192',\n  0xb379: 'Skein1024-200',\n  0xb37a: 'Skein1024-208',\n  0xb37b: 'Skein1024-216',\n  0xb37c: 'Skein1024-224',\n  0xb37d: 'Skein1024-232',\n  0xb37e: 'Skein1024-240',\n  0xb37f: 'Skein1024-248',\n  0xb380: 'Skein1024-256',\n  0xb381: 'Skein1024-264',\n  0xb382: 'Skein1024-272',\n  0xb383: 'Skein1024-280',\n  0xb384: 'Skein1024-288',\n  0xb385: 'Skein1024-296',\n  0xb386: 'Skein1024-304',\n  0xb387: 'Skein1024-312',\n  0xb388: 'Skein1024-320',\n  0xb389: 'Skein1024-328',\n  0xb38a: 'Skein1024-336',\n  0xb38b: 'Skein1024-344',\n  0xb38c: 'Skein1024-352',\n  0xb38d: 'Skein1024-360',\n  0xb38e: 'Skein1024-368',\n  0xb38f: 'Skein1024-376',\n  0xb390: 'Skein1024-384',\n  0xb391: 'Skein1024-392',\n  0xb392: 'Skein1024-400',\n  0xb393: 'Skein1024-408',\n  0xb394: 'Skein1024-416',\n  0xb395: 'Skein1024-424',\n  0xb396: 'Skein1024-432',\n  0xb397: 'Skein1024-440',\n  0xb398: 'Skein1024-448',\n  0xb399: 'Skein1024-456',\n  0xb39a: 'Skein1024-464',\n  0xb39b: 'Skein1024-472',\n  0xb39c: 'Skein1024-480',\n  0xb39d: 'Skein1024-488',\n  0xb39e: 'Skein1024-496',\n  0xb39f: 'Skein1024-504',\n  0xb3a0: 'Skein1024-512',\n  0xb3a1: 'Skein1024-520',\n  0xb3a2: 'Skein1024-528',\n  0xb3a3: 'Skein1024-536',\n  0xb3a4: 'Skein1024-544',\n  0xb3a5: 'Skein1024-552',\n  0xb3a6: 'Skein1024-560',\n  0xb3a7: 'Skein1024-568',\n  0xb3a8: 'Skein1024-576',\n  0xb3a9: 'Skein1024-584',\n  0xb3aa: 'Skein1024-592',\n  0xb3ab: 'Skein1024-600',\n  0xb3ac: 'Skein1024-608',\n  0xb3ad: 'Skein1024-616',\n  0xb3ae: 'Skein1024-624',\n  0xb3af: 'Skein1024-632',\n  0xb3b0: 'Skein1024-640',\n  0xb3b1: 'Skein1024-648',\n  0xb3b2: 'Skein1024-656',\n  0xb3b3: 'Skein1024-664',\n  0xb3b4: 'Skein1024-672',\n  0xb3b5: 'Skein1024-680',\n  0xb3b6: 'Skein1024-688',\n  0xb3b7: 'Skein1024-696',\n  0xb3b8: 'Skein1024-704',\n  0xb3b9: 'Skein1024-712',\n  0xb3ba: 'Skein1024-720',\n  0xb3bb: 'Skein1024-728',\n  0xb3bc: 'Skein1024-736',\n  0xb3bd: 'Skein1024-744',\n  0xb3be: 'Skein1024-752',\n  0xb3bf: 'Skein1024-760',\n  0xb3c0: 'Skein1024-768',\n  0xb3c1: 'Skein1024-776',\n  0xb3c2: 'Skein1024-784',\n  0xb3c3: 'Skein1024-792',\n  0xb3c4: 'Skein1024-800',\n  0xb3c5: 'Skein1024-808',\n  0xb3c6: 'Skein1024-816',\n  0xb3c7: 'Skein1024-824',\n  0xb3c8: 'Skein1024-832',\n  0xb3c9: 'Skein1024-840',\n  0xb3ca: 'Skein1024-848',\n  0xb3cb: 'Skein1024-856',\n  0xb3cc: 'Skein1024-864',\n  0xb3cd: 'Skein1024-872',\n  0xb3ce: 'Skein1024-880',\n  0xb3cf: 'Skein1024-888',\n  0xb3d0: 'Skein1024-896',\n  0xb3d1: 'Skein1024-904',\n  0xb3d2: 'Skein1024-912',\n  0xb3d3: 'Skein1024-920',\n  0xb3d4: 'Skein1024-928',\n  0xb3d5: 'Skein1024-936',\n  0xb3d6: 'Skein1024-944',\n  0xb3d7: 'Skein1024-952',\n  0xb3d8: 'Skein1024-960',\n  0xb3d9: 'Skein1024-968',\n  0xb3da: 'Skein1024-976',\n  0xb3db: 'Skein1024-984',\n  0xb3dc: 'Skein1024-992',\n  0xb3dd: 'Skein1024-1000',\n  0xb3de: 'Skein1024-1008',\n  0xb3df: 'Skein1024-1016',\n  0xb3e0: 'Skein1024-1024'\n})\n\nexports.defaultLengths = Object.freeze({\n  0x11: 20,\n  0x12: 32,\n  0x13: 64,\n  0x56: 32,\n  0x17: 28,\n  0x16: 32,\n  0x15: 48,\n  0x14: 64,\n  0x18: 32,\n  0x19: 64,\n  0x1A: 28,\n  0x1B: 32,\n  0x1C: 48,\n  0x1D: 64,\n  0x22: 32,\n\n  0xb201: 0x01,\n  0xb202: 0x02,\n  0xb203: 0x03,\n  0xb204: 0x04,\n  0xb205: 0x05,\n  0xb206: 0x06,\n  0xb207: 0x07,\n  0xb208: 0x08,\n  0xb209: 0x09,\n  0xb20a: 0x0a,\n  0xb20b: 0x0b,\n  0xb20c: 0x0c,\n  0xb20d: 0x0d,\n  0xb20e: 0x0e,\n  0xb20f: 0x0f,\n  0xb210: 0x10,\n  0xb211: 0x11,\n  0xb212: 0x12,\n  0xb213: 0x13,\n  0xb214: 0x14,\n  0xb215: 0x15,\n  0xb216: 0x16,\n  0xb217: 0x17,\n  0xb218: 0x18,\n  0xb219: 0x19,\n  0xb21a: 0x1a,\n  0xb21b: 0x1b,\n  0xb21c: 0x1c,\n  0xb21d: 0x1d,\n  0xb21e: 0x1e,\n  0xb21f: 0x1f,\n  0xb220: 0x20,\n  0xb221: 0x21,\n  0xb222: 0x22,\n  0xb223: 0x23,\n  0xb224: 0x24,\n  0xb225: 0x25,\n  0xb226: 0x26,\n  0xb227: 0x27,\n  0xb228: 0x28,\n  0xb229: 0x29,\n  0xb22a: 0x2a,\n  0xb22b: 0x2b,\n  0xb22c: 0x2c,\n  0xb22d: 0x2d,\n  0xb22e: 0x2e,\n  0xb22f: 0x2f,\n  0xb230: 0x30,\n  0xb231: 0x31,\n  0xb232: 0x32,\n  0xb233: 0x33,\n  0xb234: 0x34,\n  0xb235: 0x35,\n  0xb236: 0x36,\n  0xb237: 0x37,\n  0xb238: 0x38,\n  0xb239: 0x39,\n  0xb23a: 0x3a,\n  0xb23b: 0x3b,\n  0xb23c: 0x3c,\n  0xb23d: 0x3d,\n  0xb23e: 0x3e,\n  0xb23f: 0x3f,\n  0xb240: 0x40,\n  0xb241: 0x01,\n  0xb242: 0x02,\n  0xb243: 0x03,\n  0xb244: 0x04,\n  0xb245: 0x05,\n  0xb246: 0x06,\n  0xb247: 0x07,\n  0xb248: 0x08,\n  0xb249: 0x09,\n  0xb24a: 0x0a,\n  0xb24b: 0x0b,\n  0xb24c: 0x0c,\n  0xb24d: 0x0d,\n  0xb24e: 0x0e,\n  0xb24f: 0x0f,\n  0xb250: 0x10,\n  0xb251: 0x11,\n  0xb252: 0x12,\n  0xb253: 0x13,\n  0xb254: 0x14,\n  0xb255: 0x15,\n  0xb256: 0x16,\n  0xb257: 0x17,\n  0xb258: 0x18,\n  0xb259: 0x19,\n  0xb25a: 0x1a,\n  0xb25b: 0x1b,\n  0xb25c: 0x1c,\n  0xb25d: 0x1d,\n  0xb25e: 0x1e,\n  0xb25f: 0x1f,\n  0xb260: 0x20,\n  0xb301: 0x01,\n  0xb302: 0x02,\n  0xb303: 0x03,\n  0xb304: 0x04,\n  0xb305: 0x05,\n  0xb306: 0x06,\n  0xb307: 0x07,\n  0xb308: 0x08,\n  0xb309: 0x09,\n  0xb30a: 0x0a,\n  0xb30b: 0x0b,\n  0xb30c: 0x0c,\n  0xb30d: 0x0d,\n  0xb30e: 0x0e,\n  0xb30f: 0x0f,\n  0xb310: 0x10,\n  0xb311: 0x11,\n  0xb312: 0x12,\n  0xb313: 0x13,\n  0xb314: 0x14,\n  0xb315: 0x15,\n  0xb316: 0x16,\n  0xb317: 0x17,\n  0xb318: 0x18,\n  0xb319: 0x19,\n  0xb31a: 0x1a,\n  0xb31b: 0x1b,\n  0xb31c: 0x1c,\n  0xb31d: 0x1d,\n  0xb31e: 0x1e,\n  0xb31f: 0x1f,\n  0xb320: 0x20,\n  0xb321: 0x01,\n  0xb322: 0x02,\n  0xb323: 0x03,\n  0xb324: 0x04,\n  0xb325: 0x05,\n  0xb326: 0x06,\n  0xb327: 0x07,\n  0xb328: 0x08,\n  0xb329: 0x09,\n  0xb32a: 0x0a,\n  0xb32b: 0x0b,\n  0xb32c: 0x0c,\n  0xb32d: 0x0d,\n  0xb32e: 0x0e,\n  0xb32f: 0x0f,\n  0xb330: 0x10,\n  0xb331: 0x11,\n  0xb332: 0x12,\n  0xb333: 0x13,\n  0xb334: 0x14,\n  0xb335: 0x15,\n  0xb336: 0x16,\n  0xb337: 0x17,\n  0xb338: 0x18,\n  0xb339: 0x19,\n  0xb33a: 0x1a,\n  0xb33b: 0x1b,\n  0xb33c: 0x1c,\n  0xb33d: 0x1d,\n  0xb33e: 0x1e,\n  0xb33f: 0x1f,\n  0xb340: 0x20,\n  0xb341: 0x21,\n  0xb342: 0x22,\n  0xb343: 0x23,\n  0xb344: 0x24,\n  0xb345: 0x25,\n  0xb346: 0x26,\n  0xb347: 0x27,\n  0xb348: 0x28,\n  0xb349: 0x29,\n  0xb34a: 0x2a,\n  0xb34b: 0x2b,\n  0xb34c: 0x2c,\n  0xb34d: 0x2d,\n  0xb34e: 0x2e,\n  0xb34f: 0x2f,\n  0xb350: 0x30,\n  0xb351: 0x31,\n  0xb352: 0x32,\n  0xb353: 0x33,\n  0xb354: 0x34,\n  0xb355: 0x35,\n  0xb356: 0x36,\n  0xb357: 0x37,\n  0xb358: 0x38,\n  0xb359: 0x39,\n  0xb35a: 0x3a,\n  0xb35b: 0x3b,\n  0xb35c: 0x3c,\n  0xb35d: 0x3d,\n  0xb35e: 0x3e,\n  0xb35f: 0x3f,\n  0xb360: 0x40,\n  0xb361: 0x01,\n  0xb362: 0x02,\n  0xb363: 0x03,\n  0xb364: 0x04,\n  0xb365: 0x05,\n  0xb366: 0x06,\n  0xb367: 0x07,\n  0xb368: 0x08,\n  0xb369: 0x09,\n  0xb36a: 0x0a,\n  0xb36b: 0x0b,\n  0xb36c: 0x0c,\n  0xb36d: 0x0d,\n  0xb36e: 0x0e,\n  0xb36f: 0x0f,\n  0xb370: 0x10,\n  0xb371: 0x11,\n  0xb372: 0x12,\n  0xb373: 0x13,\n  0xb374: 0x14,\n  0xb375: 0x15,\n  0xb376: 0x16,\n  0xb377: 0x17,\n  0xb378: 0x18,\n  0xb379: 0x19,\n  0xb37a: 0x1a,\n  0xb37b: 0x1b,\n  0xb37c: 0x1c,\n  0xb37d: 0x1d,\n  0xb37e: 0x1e,\n  0xb37f: 0x1f,\n  0xb380: 0x20,\n  0xb381: 0x21,\n  0xb382: 0x22,\n  0xb383: 0x23,\n  0xb384: 0x24,\n  0xb385: 0x25,\n  0xb386: 0x26,\n  0xb387: 0x27,\n  0xb388: 0x28,\n  0xb389: 0x29,\n  0xb38a: 0x2a,\n  0xb38b: 0x2b,\n  0xb38c: 0x2c,\n  0xb38d: 0x2d,\n  0xb38e: 0x2e,\n  0xb38f: 0x2f,\n  0xb390: 0x30,\n  0xb391: 0x31,\n  0xb392: 0x32,\n  0xb393: 0x33,\n  0xb394: 0x34,\n  0xb395: 0x35,\n  0xb396: 0x36,\n  0xb397: 0x37,\n  0xb398: 0x38,\n  0xb399: 0x39,\n  0xb39a: 0x3a,\n  0xb39b: 0x3b,\n  0xb39c: 0x3c,\n  0xb39d: 0x3d,\n  0xb39e: 0x3e,\n  0xb39f: 0x3f,\n  0xb3a0: 0x40,\n  0xb3a1: 0x41,\n  0xb3a2: 0x42,\n  0xb3a3: 0x43,\n  0xb3a4: 0x44,\n  0xb3a5: 0x45,\n  0xb3a6: 0x46,\n  0xb3a7: 0x47,\n  0xb3a8: 0x48,\n  0xb3a9: 0x49,\n  0xb3aa: 0x4a,\n  0xb3ab: 0x4b,\n  0xb3ac: 0x4c,\n  0xb3ad: 0x4d,\n  0xb3ae: 0x4e,\n  0xb3af: 0x4f,\n  0xb3b0: 0x50,\n  0xb3b1: 0x51,\n  0xb3b2: 0x52,\n  0xb3b3: 0x53,\n  0xb3b4: 0x54,\n  0xb3b5: 0x55,\n  0xb3b6: 0x56,\n  0xb3b7: 0x57,\n  0xb3b8: 0x58,\n  0xb3b9: 0x59,\n  0xb3ba: 0x5a,\n  0xb3bb: 0x5b,\n  0xb3bc: 0x5c,\n  0xb3bd: 0x5d,\n  0xb3be: 0x5e,\n  0xb3bf: 0x5f,\n  0xb3c0: 0x60,\n  0xb3c1: 0x61,\n  0xb3c2: 0x62,\n  0xb3c3: 0x63,\n  0xb3c4: 0x64,\n  0xb3c5: 0x65,\n  0xb3c6: 0x66,\n  0xb3c7: 0x67,\n  0xb3c8: 0x68,\n  0xb3c9: 0x69,\n  0xb3ca: 0x6a,\n  0xb3cb: 0x6b,\n  0xb3cc: 0x6c,\n  0xb3cd: 0x6d,\n  0xb3ce: 0x6e,\n  0xb3cf: 0x6f,\n  0xb3d0: 0x70,\n  0xb3d1: 0x71,\n  0xb3d2: 0x72,\n  0xb3d3: 0x73,\n  0xb3d4: 0x74,\n  0xb3d5: 0x75,\n  0xb3d6: 0x76,\n  0xb3d7: 0x77,\n  0xb3d8: 0x78,\n  0xb3d9: 0x79,\n  0xb3da: 0x7a,\n  0xb3db: 0x7b,\n  0xb3dc: 0x7c,\n  0xb3dd: 0x7d,\n  0xb3de: 0x7e,\n  0xb3df: 0x7f,\n  0xb3e0: 0x80\n})\n"], "sourceRoot": ""}