import { GoogleProtobufAny, CosmosGovV1Beta1Tx, CosmosBaseV1Beta1Coin, InjectiveOracleV1Beta1Oracle } from '@injectivelabs/core-proto-ts';
import { SnakeCaseKeys } from 'snakecase-keys';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgSubmitProposalPerpetualMarketLaunch {
    interface Params {
        market: {
            title: string;
            description: string;
            ticker: string;
            quoteDenom: string;
            oracleBase: string;
            oracleQuote: string;
            oracleScaleFactor: number;
            oracleType: InjectiveOracleV1Beta1Oracle.OracleType;
            initialMarginRatio: string;
            maintenanceMarginRatio: string;
            makerFeeRate: string;
            takerFeeRate: string;
            minPriceTickSize: string;
            minQuantityTickSize: string;
            minNotional: string;
            adminInfo?: {
                admin: string;
                adminPermissions: number;
            };
        };
        proposer: string;
        deposit: {
            amount: string;
            denom: string;
        };
    }
    type Proto = CosmosGovV1Beta1Tx.MsgSubmitProposal;
    type Object = Omit<CosmosGovV1Beta1Tx.MsgSubmitProposal, 'content'> & {
        content: {
            type_url: string;
            value: any;
        };
    };
}
/**
 * @category Messages
 */
export default class MsgSubmitProposalPerpetualMarketLaunch extends MsgBase<MsgSubmitProposalPerpetualMarketLaunch.Params, MsgSubmitProposalPerpetualMarketLaunch.Proto, MsgSubmitProposalPerpetualMarketLaunch.Object> {
    static fromJSON(params: MsgSubmitProposalPerpetualMarketLaunch.Params): MsgSubmitProposalPerpetualMarketLaunch;
    toProto(): CosmosGovV1Beta1Tx.MsgSubmitProposal;
    toData(): {
        content: GoogleProtobufAny.Any | undefined;
        initialDeposit: CosmosBaseV1Beta1Coin.Coin[];
        proposer: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<MsgSubmitProposalPerpetualMarketLaunch.Object>;
    };
    toWeb3Gw(): {
        initial_deposit: {
            denom: string;
            amount: string;
        }[];
        proposer: string;
        content: {
            type_url: string;
            value: any;
        };
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: SnakeCaseKeys<MsgSubmitProposalPerpetualMarketLaunch.Object>;
    };
    toEip712V2(): {
        content: any;
        initial_deposit: {
            denom: string;
            amount: string;
        }[];
        proposer: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: CosmosGovV1Beta1Tx.MsgSubmitProposal;
    };
    toBinary(): Uint8Array;
}
