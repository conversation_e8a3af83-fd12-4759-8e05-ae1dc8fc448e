"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SigningCosmWasmClient = exports.findAttribute = void 0;
/* eslint-disable @typescript-eslint/naming-convention */
const amino_1 = require("@cosmjs/amino");
const crypto_1 = require("@cosmjs/crypto");
const encoding_1 = require("@cosmjs/encoding");
const math_1 = require("@cosmjs/math");
const proto_signing_1 = require("@cosmjs/proto-signing");
const stargate_1 = require("@cosmjs/stargate");
const tendermint_rpc_1 = require("@cosmjs/tendermint-rpc");
const utils_1 = require("@cosmjs/utils");
const tx_1 = require("cosmjs-types/cosmos/distribution/v1beta1/tx");
const tx_2 = require("cosmjs-types/cosmos/staking/v1beta1/tx");
const signing_1 = require("cosmjs-types/cosmos/tx/signing/v1beta1/signing");
const tx_3 = require("cosmjs-types/cosmos/tx/v1beta1/tx");
const tx_4 = require("cosmjs-types/cosmwasm/wasm/v1/tx");
const pako_1 = __importDefault(require("pako"));
const cosmwasmclient_1 = require("./cosmwasmclient");
const modules_1 = require("./modules");
/**
 * Searches in events for an event of the given event type which contains an
 * attribute for with the given key.
 *
 * Throws if the attribute was not found.
 */
function findAttribute(events, eventType, attrKey) {
    // all attributes from events with the right event type
    const attributes = events.filter((event) => event.type === eventType).flatMap((e) => e.attributes);
    const out = attributes.find((attr) => attr.key === attrKey);
    if (!out) {
        throw new Error(`Could not find attribute '${attrKey}' in first event of type '${eventType}' in first log.`);
    }
    return out;
}
exports.findAttribute = findAttribute;
function createDeliverTxResponseErrorMessage(result) {
    return `Error when broadcasting tx ${result.transactionHash} at height ${result.height}. Code: ${result.code}; Raw log: ${result.rawLog}`;
}
class SigningCosmWasmClient extends cosmwasmclient_1.CosmWasmClient {
    /**
     * Creates an instance by connecting to the given CometBFT RPC endpoint.
     *
     * This uses auto-detection to decide between a CometBFT 0.38, Tendermint 0.37 and 0.34 client.
     * To set the Comet client explicitly, use `createWithSigner`.
     */
    static async connectWithSigner(endpoint, signer, options = {}) {
        const cometClient = await (0, tendermint_rpc_1.connectComet)(endpoint);
        return SigningCosmWasmClient.createWithSigner(cometClient, signer, options);
    }
    /**
     * Creates an instance from a manually created Comet client.
     * Use this to use `Comet38Client` or `Tendermint37Client` instead of `Tendermint34Client`.
     */
    static async createWithSigner(cometClient, signer, options = {}) {
        return new SigningCosmWasmClient(cometClient, signer, options);
    }
    /**
     * Creates a client in offline mode.
     *
     * This should only be used in niche cases where you know exactly what you're doing,
     * e.g. when building an offline signing application.
     *
     * When you try to use online functionality with such a signer, an
     * exception will be raised.
     */
    static async offline(signer, options = {}) {
        return new SigningCosmWasmClient(undefined, signer, options);
    }
    constructor(cometClient, signer, options) {
        super(cometClient);
        // Starting with Cosmos SDK 0.47, we see many cases in which 1.3 is not enough anymore
        // E.g. https://github.com/cosmos/cosmos-sdk/issues/16020
        this.defaultGasMultiplier = 1.4;
        const { registry = new proto_signing_1.Registry([...stargate_1.defaultRegistryTypes, ...modules_1.wasmTypes]), aminoTypes = new stargate_1.AminoTypes({
            ...(0, stargate_1.createDefaultAminoConverters)(),
            ...(0, modules_1.createWasmAminoConverters)(),
        }), } = options;
        this.registry = registry;
        this.aminoTypes = aminoTypes;
        this.signer = signer;
        this.broadcastTimeoutMs = options.broadcastTimeoutMs;
        this.broadcastPollIntervalMs = options.broadcastPollIntervalMs;
        this.gasPrice = options.gasPrice;
    }
    async simulate(signerAddress, messages, memo) {
        const anyMsgs = messages.map((m) => this.registry.encodeAsAny(m));
        const accountFromSigner = (await this.signer.getAccounts()).find((account) => account.address === signerAddress);
        if (!accountFromSigner) {
            throw new Error("Failed to retrieve account from signer");
        }
        const pubkey = (0, amino_1.encodeSecp256k1Pubkey)(accountFromSigner.pubkey);
        const { sequence } = await this.getSequence(signerAddress);
        const { gasInfo } = await this.forceGetQueryClient().tx.simulate(anyMsgs, memo, pubkey, sequence);
        (0, utils_1.assertDefined)(gasInfo);
        return math_1.Uint53.fromString(gasInfo.gasUsed.toString()).toNumber();
    }
    /** Uploads code and returns a receipt, including the code ID */
    async upload(senderAddress, wasmCode, fee, memo = "", instantiatePermission) {
        const compressed = pako_1.default.gzip(wasmCode, { level: 9 });
        const storeCodeMsg = {
            typeUrl: "/cosmwasm.wasm.v1.MsgStoreCode",
            value: tx_4.MsgStoreCode.fromPartial({
                sender: senderAddress,
                wasmByteCode: compressed,
                instantiatePermission,
            }),
        };
        // When uploading a contract, the simulation is only 1-2% away from the actual gas usage.
        // So we have a smaller default gas multiplier than signAndBroadcast.
        const usedFee = fee == "auto" ? 1.1 : fee;
        const result = await this.signAndBroadcast(senderAddress, [storeCodeMsg], usedFee, memo);
        if ((0, stargate_1.isDeliverTxFailure)(result)) {
            throw new Error(createDeliverTxResponseErrorMessage(result));
        }
        const codeIdAttr = findAttribute(result.events, "store_code", "code_id");
        return {
            checksum: (0, encoding_1.toHex)((0, crypto_1.sha256)(wasmCode)),
            originalSize: wasmCode.length,
            compressedSize: compressed.length,
            codeId: Number.parseInt(codeIdAttr.value, 10),
            logs: stargate_1.logs.parseRawLog(result.rawLog),
            height: result.height,
            transactionHash: result.transactionHash,
            events: result.events,
            gasWanted: result.gasWanted,
            gasUsed: result.gasUsed,
        };
    }
    async instantiate(senderAddress, codeId, msg, label, fee, options = {}) {
        const instantiateContractMsg = {
            typeUrl: "/cosmwasm.wasm.v1.MsgInstantiateContract",
            value: tx_4.MsgInstantiateContract.fromPartial({
                sender: senderAddress,
                codeId: BigInt(new math_1.Uint53(codeId).toString()),
                label: label,
                msg: (0, encoding_1.toUtf8)(JSON.stringify(msg)),
                funds: [...(options.funds || [])],
                admin: options.admin,
            }),
        };
        const result = await this.signAndBroadcast(senderAddress, [instantiateContractMsg], fee, options.memo);
        if ((0, stargate_1.isDeliverTxFailure)(result)) {
            throw new Error(createDeliverTxResponseErrorMessage(result));
        }
        const contractAddressAttr = findAttribute(result.events, "instantiate", "_contract_address");
        return {
            contractAddress: contractAddressAttr.value,
            logs: stargate_1.logs.parseRawLog(result.rawLog),
            height: result.height,
            transactionHash: result.transactionHash,
            events: result.events,
            gasWanted: result.gasWanted,
            gasUsed: result.gasUsed,
        };
    }
    async instantiate2(senderAddress, codeId, salt, msg, label, fee, options = {}) {
        const instantiateContract2Msg = {
            typeUrl: "/cosmwasm.wasm.v1.MsgInstantiateContract2",
            value: tx_4.MsgInstantiateContract2.fromPartial({
                sender: senderAddress,
                codeId: BigInt(new math_1.Uint53(codeId).toString()),
                label: label,
                msg: (0, encoding_1.toUtf8)(JSON.stringify(msg)),
                funds: [...(options.funds || [])],
                admin: options.admin,
                salt: salt,
                fixMsg: false,
            }),
        };
        const result = await this.signAndBroadcast(senderAddress, [instantiateContract2Msg], fee, options.memo);
        if ((0, stargate_1.isDeliverTxFailure)(result)) {
            throw new Error(createDeliverTxResponseErrorMessage(result));
        }
        const contractAddressAttr = findAttribute(result.events, "instantiate", "_contract_address");
        return {
            contractAddress: contractAddressAttr.value,
            logs: stargate_1.logs.parseRawLog(result.rawLog),
            height: result.height,
            transactionHash: result.transactionHash,
            events: result.events,
            gasWanted: result.gasWanted,
            gasUsed: result.gasUsed,
        };
    }
    async updateAdmin(senderAddress, contractAddress, newAdmin, fee, memo = "") {
        const updateAdminMsg = {
            typeUrl: "/cosmwasm.wasm.v1.MsgUpdateAdmin",
            value: tx_4.MsgUpdateAdmin.fromPartial({
                sender: senderAddress,
                contract: contractAddress,
                newAdmin: newAdmin,
            }),
        };
        const result = await this.signAndBroadcast(senderAddress, [updateAdminMsg], fee, memo);
        if ((0, stargate_1.isDeliverTxFailure)(result)) {
            throw new Error(createDeliverTxResponseErrorMessage(result));
        }
        return {
            logs: stargate_1.logs.parseRawLog(result.rawLog),
            height: result.height,
            transactionHash: result.transactionHash,
            events: result.events,
            gasWanted: result.gasWanted,
            gasUsed: result.gasUsed,
        };
    }
    async clearAdmin(senderAddress, contractAddress, fee, memo = "") {
        const clearAdminMsg = {
            typeUrl: "/cosmwasm.wasm.v1.MsgClearAdmin",
            value: tx_4.MsgClearAdmin.fromPartial({
                sender: senderAddress,
                contract: contractAddress,
            }),
        };
        const result = await this.signAndBroadcast(senderAddress, [clearAdminMsg], fee, memo);
        if ((0, stargate_1.isDeliverTxFailure)(result)) {
            throw new Error(createDeliverTxResponseErrorMessage(result));
        }
        return {
            logs: stargate_1.logs.parseRawLog(result.rawLog),
            height: result.height,
            transactionHash: result.transactionHash,
            events: result.events,
            gasWanted: result.gasWanted,
            gasUsed: result.gasUsed,
        };
    }
    async migrate(senderAddress, contractAddress, codeId, migrateMsg, fee, memo = "") {
        const migrateContractMsg = {
            typeUrl: "/cosmwasm.wasm.v1.MsgMigrateContract",
            value: tx_4.MsgMigrateContract.fromPartial({
                sender: senderAddress,
                contract: contractAddress,
                codeId: BigInt(new math_1.Uint53(codeId).toString()),
                msg: (0, encoding_1.toUtf8)(JSON.stringify(migrateMsg)),
            }),
        };
        const result = await this.signAndBroadcast(senderAddress, [migrateContractMsg], fee, memo);
        if ((0, stargate_1.isDeliverTxFailure)(result)) {
            throw new Error(createDeliverTxResponseErrorMessage(result));
        }
        return {
            logs: stargate_1.logs.parseRawLog(result.rawLog),
            height: result.height,
            transactionHash: result.transactionHash,
            events: result.events,
            gasWanted: result.gasWanted,
            gasUsed: result.gasUsed,
        };
    }
    async execute(senderAddress, contractAddress, msg, fee, memo = "", funds) {
        const instruction = {
            contractAddress: contractAddress,
            msg: msg,
            funds: funds,
        };
        return this.executeMultiple(senderAddress, [instruction], fee, memo);
    }
    /**
     * Like `execute` but allows executing multiple messages in one transaction.
     */
    async executeMultiple(senderAddress, instructions, fee, memo = "") {
        const msgs = instructions.map((i) => ({
            typeUrl: "/cosmwasm.wasm.v1.MsgExecuteContract",
            value: tx_4.MsgExecuteContract.fromPartial({
                sender: senderAddress,
                contract: i.contractAddress,
                msg: (0, encoding_1.toUtf8)(JSON.stringify(i.msg)),
                funds: [...(i.funds || [])],
            }),
        }));
        const result = await this.signAndBroadcast(senderAddress, msgs, fee, memo);
        if ((0, stargate_1.isDeliverTxFailure)(result)) {
            throw new Error(createDeliverTxResponseErrorMessage(result));
        }
        return {
            logs: stargate_1.logs.parseRawLog(result.rawLog),
            height: result.height,
            transactionHash: result.transactionHash,
            events: result.events,
            gasWanted: result.gasWanted,
            gasUsed: result.gasUsed,
        };
    }
    async sendTokens(senderAddress, recipientAddress, amount, fee, memo = "") {
        const sendMsg = {
            typeUrl: "/cosmos.bank.v1beta1.MsgSend",
            value: {
                fromAddress: senderAddress,
                toAddress: recipientAddress,
                amount: [...amount],
            },
        };
        return this.signAndBroadcast(senderAddress, [sendMsg], fee, memo);
    }
    async delegateTokens(delegatorAddress, validatorAddress, amount, fee, memo = "") {
        const delegateMsg = {
            typeUrl: "/cosmos.staking.v1beta1.MsgDelegate",
            value: tx_2.MsgDelegate.fromPartial({ delegatorAddress: delegatorAddress, validatorAddress, amount }),
        };
        return this.signAndBroadcast(delegatorAddress, [delegateMsg], fee, memo);
    }
    async undelegateTokens(delegatorAddress, validatorAddress, amount, fee, memo = "") {
        const undelegateMsg = {
            typeUrl: "/cosmos.staking.v1beta1.MsgUndelegate",
            value: tx_2.MsgUndelegate.fromPartial({ delegatorAddress: delegatorAddress, validatorAddress, amount }),
        };
        return this.signAndBroadcast(delegatorAddress, [undelegateMsg], fee, memo);
    }
    async withdrawRewards(delegatorAddress, validatorAddress, fee, memo = "") {
        const withdrawDelegatorRewardMsg = {
            typeUrl: "/cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward",
            value: tx_1.MsgWithdrawDelegatorReward.fromPartial({ delegatorAddress: delegatorAddress, validatorAddress }),
        };
        return this.signAndBroadcast(delegatorAddress, [withdrawDelegatorRewardMsg], fee, memo);
    }
    /**
     * Creates a transaction with the given messages, fee, memo and timeout height. Then signs and broadcasts the transaction.
     *
     * @param signerAddress The address that will sign transactions using this instance. The signer must be able to sign with this address.
     * @param messages
     * @param fee
     * @param memo
     * @param timeoutHeight (optional) timeout height to prevent the tx from being committed past a certain height
     */
    async signAndBroadcast(signerAddress, messages, fee, memo = "", timeoutHeight) {
        let usedFee;
        if (fee == "auto" || typeof fee === "number") {
            (0, utils_1.assertDefined)(this.gasPrice, "Gas price must be set in the client options when auto gas is used.");
            const gasEstimation = await this.simulate(signerAddress, messages, memo);
            const multiplier = typeof fee === "number" ? fee : this.defaultGasMultiplier;
            usedFee = (0, stargate_1.calculateFee)(Math.round(gasEstimation * multiplier), this.gasPrice);
        }
        else {
            usedFee = fee;
        }
        const txRaw = await this.sign(signerAddress, messages, usedFee, memo, undefined, timeoutHeight);
        const txBytes = tx_3.TxRaw.encode(txRaw).finish();
        return this.broadcastTx(txBytes, this.broadcastTimeoutMs, this.broadcastPollIntervalMs);
    }
    /**
     * Creates a transaction with the given messages, fee, memo and timeout height. Then signs and broadcasts the transaction.
     *
     * This method is useful if you want to send a transaction in broadcast,
     * without waiting for it to be placed inside a block, because for example
     * I would like to receive the hash to later track the transaction with another tool.
     *
     * @param signerAddress The address that will sign transactions using this instance. The signer must be able to sign with this address.
     * @param messages
     * @param fee
     * @param memo
     * @param timeoutHeight (optional) timeout height to prevent the tx from being committed past a certain height
     *
     * @returns Returns the hash of the transaction
     */
    async signAndBroadcastSync(signerAddress, messages, fee, memo = "", timeoutHeight) {
        let usedFee;
        if (fee == "auto" || typeof fee === "number") {
            (0, utils_1.assertDefined)(this.gasPrice, "Gas price must be set in the client options when auto gas is used.");
            const gasEstimation = await this.simulate(signerAddress, messages, memo);
            const multiplier = typeof fee === "number" ? fee : this.defaultGasMultiplier;
            usedFee = (0, stargate_1.calculateFee)(Math.round(gasEstimation * multiplier), this.gasPrice);
        }
        else {
            usedFee = fee;
        }
        const txRaw = await this.sign(signerAddress, messages, usedFee, memo, undefined, timeoutHeight);
        const txBytes = tx_3.TxRaw.encode(txRaw).finish();
        return this.broadcastTxSync(txBytes);
    }
    async sign(signerAddress, messages, fee, memo, explicitSignerData, timeoutHeight) {
        let signerData;
        if (explicitSignerData) {
            signerData = explicitSignerData;
        }
        else {
            const { accountNumber, sequence } = await this.getSequence(signerAddress);
            const chainId = await this.getChainId();
            signerData = {
                accountNumber: accountNumber,
                sequence: sequence,
                chainId: chainId,
            };
        }
        return (0, proto_signing_1.isOfflineDirectSigner)(this.signer)
            ? this.signDirect(signerAddress, messages, fee, memo, signerData, timeoutHeight)
            : this.signAmino(signerAddress, messages, fee, memo, signerData, timeoutHeight);
    }
    async signAmino(signerAddress, messages, fee, memo, { accountNumber, sequence, chainId }, timeoutHeight) {
        (0, utils_1.assert)(!(0, proto_signing_1.isOfflineDirectSigner)(this.signer));
        const accountFromSigner = (await this.signer.getAccounts()).find((account) => account.address === signerAddress);
        if (!accountFromSigner) {
            throw new Error("Failed to retrieve account from signer");
        }
        const pubkey = (0, proto_signing_1.encodePubkey)((0, amino_1.encodeSecp256k1Pubkey)(accountFromSigner.pubkey));
        const signMode = signing_1.SignMode.SIGN_MODE_LEGACY_AMINO_JSON;
        const msgs = messages.map((msg) => this.aminoTypes.toAmino(msg));
        const signDoc = (0, amino_1.makeSignDoc)(msgs, fee, chainId, memo, accountNumber, sequence, timeoutHeight);
        const { signature, signed } = await this.signer.signAmino(signerAddress, signDoc);
        const signedTxBody = {
            typeUrl: "/cosmos.tx.v1beta1.TxBody",
            value: {
                messages: signed.msgs.map((msg) => this.aminoTypes.fromAmino(msg)),
                memo: signed.memo,
                timeoutHeight: timeoutHeight,
            },
        };
        const signedTxBodyBytes = this.registry.encode(signedTxBody);
        const signedGasLimit = math_1.Int53.fromString(signed.fee.gas).toNumber();
        const signedSequence = math_1.Int53.fromString(signed.sequence).toNumber();
        const signedAuthInfoBytes = (0, proto_signing_1.makeAuthInfoBytes)([{ pubkey, sequence: signedSequence }], signed.fee.amount, signedGasLimit, signed.fee.granter, signed.fee.payer, signMode);
        return tx_3.TxRaw.fromPartial({
            bodyBytes: signedTxBodyBytes,
            authInfoBytes: signedAuthInfoBytes,
            signatures: [(0, encoding_1.fromBase64)(signature.signature)],
        });
    }
    async signDirect(signerAddress, messages, fee, memo, { accountNumber, sequence, chainId }, timeoutHeight) {
        (0, utils_1.assert)((0, proto_signing_1.isOfflineDirectSigner)(this.signer));
        const accountFromSigner = (await this.signer.getAccounts()).find((account) => account.address === signerAddress);
        if (!accountFromSigner) {
            throw new Error("Failed to retrieve account from signer");
        }
        const pubkey = (0, proto_signing_1.encodePubkey)((0, amino_1.encodeSecp256k1Pubkey)(accountFromSigner.pubkey));
        const txBody = {
            typeUrl: "/cosmos.tx.v1beta1.TxBody",
            value: {
                messages: messages,
                memo: memo,
                timeoutHeight: timeoutHeight,
            },
        };
        const txBodyBytes = this.registry.encode(txBody);
        const gasLimit = math_1.Int53.fromString(fee.gas).toNumber();
        const authInfoBytes = (0, proto_signing_1.makeAuthInfoBytes)([{ pubkey, sequence }], fee.amount, gasLimit, fee.granter, fee.payer);
        const signDoc = (0, proto_signing_1.makeSignDoc)(txBodyBytes, authInfoBytes, chainId, accountNumber);
        const { signature, signed } = await this.signer.signDirect(signerAddress, signDoc);
        return tx_3.TxRaw.fromPartial({
            bodyBytes: signed.bodyBytes,
            authInfoBytes: signed.authInfoBytes,
            signatures: [(0, encoding_1.fromBase64)(signature.signature)],
        });
    }
}
exports.SigningCosmWasmClient = SigningCosmWasmClient;
//# sourceMappingURL=signingcosmwasmclient.js.map