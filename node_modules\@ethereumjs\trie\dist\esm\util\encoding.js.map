{"version": 3, "file": "encoding.js", "sourceRoot": "", "sources": ["../../../src/util/encoding.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAA;AAI5E,wGAAwG;AACxG,EAAE;AACF,wDAAwD;AACxD,EAAE;AACF,mFAAmF;AACnF,+BAA+B;AAC/B,EAAE;AACF,qFAAqF;AACrF,qFAAqF;AACrF,qFAAqF;AACrF,wBAAwB;AACxB,EAAE;AACF,oFAAoF;AACpF,wFAAwF;AACxF,sFAAsF;AACtF,yFAAyF;AACzF,0FAA0F;AAC1F,wFAAwF;AACxF,+EAA+E;AAE/E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,OAAmB,EAAE,EAAE;IACnD,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAA;AACjE,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,OAAmB,EAAE,KAAiB,EAAE,EAAE;IACvE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QAC9D,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;KACjD;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,GAAe,EAAE,EAAE;IAC/C,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;QACtB,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;KACtC;IACD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QACxB,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAA;KACnD;IACD,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAC1C,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAExB,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,iBAAiB;AACjB,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,OAAmB,EAAE,EAAE;IAC3D,IAAI,UAAU,GAAG,CAAC,CAAA;IAClB,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;QAC1B,UAAU,GAAG,CAAC,CAAA;QACd,0CAA0C;QAC1C,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;KAClD;IACD,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;IAClD,4DAA4D;IAC5D,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAA;IACxB,oFAAoF;IACpF,qEAAqE;IACrE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC9B,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAChB,GAAG,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAA;QACpB,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;KAC9B;IACD,4CAA4C;IAC5C,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IACxC,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,GAAe,EAAE,EAAE;IAChD,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAA;IAC5B,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAA;IACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;QAChB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;QACvB,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;KAC5B;IACD,kEAAkE;IAClE,2CAA2C;IAC3C,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAA;IACnB,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,OAAmB,EAAE,EAAE;IAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO,OAAO,CAAA;KACf;IACD,IAAI,IAAI,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;IAClC,oEAAoE;IACpE,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;QACf,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;KACzC;IACD,oEAAoE;IACpE,sDAAsD;IACtD,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;AAC5B,CAAC,CAAA;AAED;;;;;GAKG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,GAAY,EAAc,EAAE;IAClE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACb,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;KAClC;IAED,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED;;;;;GAKG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,GAAY,EAAc,EAAE;IAC/D,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAA;IACpB,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAA;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED;;;;;GAKG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,GAAe,EAAW,EAAE;IAC/D,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;IACzB,MAAM,OAAO,GAAG,EAAa,CAAA;IAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,CAAC,GAAG,CAAC,CAAA;QACX,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;KAC1B;IAED,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAED;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,SAAkB,EAAE,OAAe,EAAc,EAAE;IAC5F,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;IACjC,MAAM,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAA;IACjC,IAAI,OAAO,KAAK,KAAK,EAAE;QACrB,OAAO,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;KACjD;SAAM,IAAI,OAAO,KAAK,SAAS,EAAE;QAChC,OAAO,uBAAuB,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;KACpD;IACD,MAAM,KAAK,CAAC,2CAA2C,CAAC,CAAA;AAC1D,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,WAAqB,EAAE,EAAE;IAC9D,MAAM,GAAG,GAAe,EAAE,CAAA;IAC1B,IAAI,KAAK,GAAa,EAAE,CAAA;IACxB,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,OAAO,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE;QAC7B,MAAM,gBAAgB,GAAG,WAAW,CAAC,CAAC,CAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACnD,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAA;QAC7C,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAA;QAE7C,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAC7B,IAAI,iBAAiB,KAAK,SAAS,EAAE;YACnC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;SAC9B;QAED,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;QACX,OAAO,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE;YAC7B,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,CAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAClD,MAAM,gBAAgB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAA;YAC3C,MAAM,gBAAgB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAA;YAE3C,IAAI,gBAAgB,KAAK,iBAAiB,EAAE;gBAC1C,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;aAC7B;iBAAM;gBACL,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACf,KAAK,GAAG,EAAE,CAAA;gBACV,CAAC,GAAG,CAAC,CAAA;gBACL,MAAK;aACN;YACD,CAAC,EAAE,CAAA;SACJ;QACD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACf,KAAK,GAAG,EAAE,CAAA;SACX;KACF;IACD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;QAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAErC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAC7B,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACpB,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,EAAE;YACjB,kCAAkC;YAClC,OAAO,qBAAqB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAA;SACtD;aAAM;YACL,+BAA+B;YAC/B,OAAO,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAA;SAC9C;IACH,CAAC,CAAC,CACH,CAAA;AACH,CAAC,CAAA"}