/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.auth.module.v1";
function createBaseModule() {
    return { bech32Prefix: "", moduleAccountPermissions: [], authority: "" };
}
export const Module = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.bech32Prefix !== "") {
            writer.uint32(10).string(message.bech32Prefix);
        }
        for (const v of message.moduleAccountPermissions) {
            ModuleAccountPermission.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.authority !== "") {
            writer.uint32(26).string(message.authority);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModule();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bech32Prefix = reader.string();
                    break;
                case 2:
                    message.moduleAccountPermissions.push(ModuleAccountPermission.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.authority = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            bech32Prefix: isSet(object.bech32Prefix) ? String(object.bech32Prefix) : "",
            moduleAccountPermissions: Array.isArray(object?.moduleAccountPermissions)
                ? object.moduleAccountPermissions.map((e) => ModuleAccountPermission.fromJSON(e))
                : [],
            authority: isSet(object.authority) ? String(object.authority) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.bech32Prefix !== undefined && (obj.bech32Prefix = message.bech32Prefix);
        if (message.moduleAccountPermissions) {
            obj.moduleAccountPermissions = message.moduleAccountPermissions.map((e) => e ? ModuleAccountPermission.toJSON(e) : undefined);
        }
        else {
            obj.moduleAccountPermissions = [];
        }
        message.authority !== undefined && (obj.authority = message.authority);
        return obj;
    },
    create(base) {
        return Module.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModule();
        message.bech32Prefix = object.bech32Prefix ?? "";
        message.moduleAccountPermissions =
            object.moduleAccountPermissions?.map((e) => ModuleAccountPermission.fromPartial(e)) || [];
        message.authority = object.authority ?? "";
        return message;
    },
};
function createBaseModuleAccountPermission() {
    return { account: "", permissions: [] };
}
export const ModuleAccountPermission = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        for (const v of message.permissions) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModuleAccountPermission();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.permissions.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            permissions: Array.isArray(object?.permissions) ? object.permissions.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        if (message.permissions) {
            obj.permissions = message.permissions.map((e) => e);
        }
        else {
            obj.permissions = [];
        }
        return obj;
    },
    create(base) {
        return ModuleAccountPermission.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModuleAccountPermission();
        message.account = object.account ?? "";
        message.permissions = object.permissions?.map((e) => e) || [];
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
