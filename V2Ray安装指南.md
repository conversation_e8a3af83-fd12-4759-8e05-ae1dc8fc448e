# V2Ray 安装指南

## 🔧 手动安装V2Ray

由于网络限制，自动下载可能失败，请按以下步骤手动安装：

### 1. 下载V2Ray
访问：https://github.com/v2fly/v2ray-core/releases/latest

下载文件：`v2ray-windows-64.zip`

### 2. 解压文件
1. 在项目根目录创建文件夹：`tools\v2ray\`
2. 将下载的zip文件解压到该目录
3. 确保目录结构如下：
```
tools\
└── v2ray\
    ├── v2ray.exe
    ├── v2ctl.exe
    └── 其他文件...
```

### 3. 验证安装
运行以下命令验证安装：
```bash
tools\v2ray\v2ray.exe version
```

## 🚀 使用IP轮换工具

安装完成后，您可以使用以下功能：

### 启动自动轮换（每60秒）
```bash
python src\utils\ip\v2ray_rotator.py start 60
```

### 手动切换到香港节点
```bash
python src\utils\ip\v2ray_rotator.py switch 香港
```

### 查看所有可用节点
```bash
python src\utils\ip\v2ray_rotator.py list
```

### 查看当前状态
```bash
python src\utils\ip\v2ray_rotator.py status
```

### 停止所有代理
```bash
python src\utils\ip\v2ray_rotator.py stop
```

## 📊 功能特点

✅ **真正的网络代理** - 所有网络流量都通过代理
✅ **自动IP轮换** - 顺序轮换所有182个代理节点
✅ **双协议支持** - HTTP (1081) 和 SOCKS5 (1080) 代理
✅ **地区选择** - 支持香港、台湾、日本、新加坡、美国、韩国等

## 🎯 一键管理工具

运行 `ip_tools.bat` 获得图形化菜单界面，包含所有功能的快捷操作。

## ⚠️ 注意事项

1. 确保V2Ray正确安装在 `tools\v2ray\v2ray.exe`
2. 首次使用需要更新IP配置：`python src\utils\ip\config_creator.py`
3. 代理启动后，系统代理会自动设置为 `127.0.0.1:1081`
4. 停止代理时会自动禁用系统代理设置
