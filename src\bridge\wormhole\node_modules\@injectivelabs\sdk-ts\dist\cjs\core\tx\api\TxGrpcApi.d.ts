import { TxConcreteApi, TxClientBroadcastOptions, TxClientBroadcastResponse } from '../types/tx.js';
import { TxResponse } from '../types/tx.js';
import { CosmosTxV1Beta1Service, CosmosTxV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { grpcPkg } from '../../../utils/grpc.js';
export declare class TxGrpcApi implements TxConcreteApi {
    txService: CosmosTxV1Beta1Service.ServiceClientImpl;
    protected metadata?: grpcPkg.grpc.Metadata;
    endpoint: string;
    constructor(endpoint: string);
    setMetadata(map: Record<string, string>): this;
    clearMetadata(): void;
    fetchTx(hash: string): Promise<TxResponse>;
    fetchTxPoll(txHash: string, timeout?: number): Promise<TxResponse>;
    simulate(txRaw: CosmosTxV1Beta1Tx.TxRaw): Promise<{
        result: {
            data: string | Uint8Array<ArrayBufferLike>;
            log: string;
            eventsList: import("@injectivelabs/core-proto-ts/cjs/tendermint/abci/types.js").Event[];
            events?: import("@injectivelabs/core-proto-ts/cjs/tendermint/abci/types.js").Event[] | undefined;
            msgResponses?: import("@injectivelabs/core-proto-ts/cjs/google/protobuf/any.js").Any[] | undefined;
        };
        gasInfo: {
            gasWanted: number;
            gasUsed: number;
        };
    }>;
    broadcast(txRaw: CosmosTxV1Beta1Tx.TxRaw, options?: TxClientBroadcastOptions): Promise<TxResponse>;
    /** @deprecated - the BLOCk mode broadcasting is deprecated now, use either sync or async */
    broadcastBlock(txRaw: CosmosTxV1Beta1Tx.TxRaw): Promise<TxClientBroadcastResponse>;
}
