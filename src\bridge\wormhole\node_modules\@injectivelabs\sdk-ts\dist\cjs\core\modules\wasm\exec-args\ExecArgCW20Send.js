"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const utf8_js_1 = require("../../../../utils/utf8.js");
const ExecArgBase_js_1 = require("../ExecArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgCW20Send extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgCW20Send(params);
    }
    toData() {
        const { params } = this;
        return {
            contract: params.contractAddress,
            amount: params.amount,
            msg: Buffer.from((0, utf8_js_1.fromUtf8)(JSON.stringify(params.msg || {}))).toString('base64'),
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('send', this.toData());
    }
}
exports.default = ExecArgCW20Send;
