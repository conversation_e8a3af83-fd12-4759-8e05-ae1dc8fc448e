{"version": 3, "sources": ["../../src/api/aptosConfig.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport aptosClient from \"@aptos-labs/aptos-client\";\nimport { AptosSettings, ClientConfig, Client, FullNodeConfig, IndexerConfig, FaucetConfig } from \"../types\";\nimport {\n  NetworkToNodeAPI,\n  NetworkToFaucetAPI,\n  NetworkToIndexerAPI,\n  Network,\n  NetworkToPepperAPI,\n  NetworkToProverAPI,\n} from \"../utils/apiEndpoints\";\nimport { AptosApiType } from \"../utils/const\";\n\n/**\n * Represents the configuration settings for an Aptos SDK client instance.\n * This class allows customization of various endpoints and client settings.\n *\n * @example\n * ```typescript\n * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n *\n * async function runExample() {\n *     // Create a configuration for connecting to the Aptos testnet\n *     const config = new AptosConfig({ network: Network.TESTNET });\n *\n *     // Initialize the Aptos client with the configuration\n *     const aptos = new Aptos(config);\n *\n *     console.log(\"Aptos client initialized:\", aptos);\n * }\n * runExample().catch(console.error);\n * ```\n * @group Client\n */\nexport class AptosConfig {\n  /**\n   * The Network that this SDK is associated with. Defaults to DEVNET\n   * @group Client\n   */\n  readonly network: Network;\n\n  /**\n   * The client instance the SDK uses. Defaults to `@aptos-labs/aptos-client\n   * @group Client\n   */\n  readonly client: Client;\n\n  /**\n   * The optional hardcoded fullnode URL to send requests to instead of using the network\n   * @group Client\n   */\n  readonly fullnode?: string;\n\n  /**\n   * The optional hardcoded faucet URL to send requests to instead of using the network\n   * @group Client\n   */\n  readonly faucet?: string;\n\n  /**\n   * The optional hardcoded pepper service URL to send requests to instead of using the network\n   * @group Client\n   */\n  readonly pepper?: string;\n\n  /**\n   * The optional hardcoded prover service URL to send requests to instead of using the network\n   * @group Client\n   */\n  readonly prover?: string;\n\n  /**\n   * The optional hardcoded indexer URL to send requests to instead of using the network\n   * @group Client\n   */\n  readonly indexer?: string;\n\n  /**\n   * Optional client configurations\n   * @group Client\n   */\n  readonly clientConfig?: ClientConfig;\n\n  /**\n   * Optional specific Fullnode configurations\n   * @group Client\n   */\n  readonly fullnodeConfig?: FullNodeConfig;\n\n  /**\n   * Optional specific Indexer configurations\n   * @group Client\n   */\n  readonly indexerConfig?: IndexerConfig;\n\n  /**\n   * Optional specific Faucet configurations\n   * @group Client\n   */\n  readonly faucetConfig?: FaucetConfig;\n\n  /**\n   * Initializes an instance of the Aptos client with the specified settings.\n   * This allows users to configure various aspects of the client, such as network and endpoints.\n   *\n   * @param settings - Optional configuration settings for the Aptos client.\n   * @param settings.network - The network to connect to, defaults to `Network.DEVNET`.\n   * @param settings.fullnode - The fullnode endpoint to use for requests.\n   * @param settings.faucet - The faucet endpoint for obtaining test tokens.\n   * @param settings.pepper - The pepper used for transaction signing.\n   * @param settings.prover - The prover endpoint for transaction verification.\n   * @param settings.indexer - The indexer endpoint for querying blockchain data.\n   * @param settings.client - Custom client settings, defaults to a standard Aptos client.\n   * @param settings.clientConfig - Additional configuration for the client.\n   * @param settings.fullnodeConfig - Additional configuration for the fullnode.\n   * @param settings.indexerConfig - Additional configuration for the indexer.\n   * @param settings.faucetConfig - Additional configuration for the faucet.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * async function runExample() {\n   *     // Create a new Aptos client with default settings\n   *     const config = new AptosConfig({ network: Network.TESTNET }); // Specify the network\n   *     const aptos = new Aptos(config);\n   *\n   *     console.log(\"Aptos client initialized:\", aptos);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Client\n   */\n  constructor(settings?: AptosSettings) {\n    // If there are any endpoint overrides, they are custom networks, keep that in mind\n    if (settings?.fullnode || settings?.indexer || settings?.faucet || settings?.pepper || settings?.prover) {\n      if (settings?.network === Network.CUSTOM) {\n        console.info(\"Note: using CUSTOM network will require queries to lookup ChainId\");\n      } else if (!settings?.network) {\n        throw new Error(\"Custom endpoints require a network to be specified\");\n      }\n    }\n\n    this.network = settings?.network ?? Network.DEVNET;\n    this.fullnode = settings?.fullnode;\n    this.faucet = settings?.faucet;\n    this.pepper = settings?.pepper;\n    this.prover = settings?.prover;\n    this.indexer = settings?.indexer;\n    this.client = settings?.client ?? { provider: aptosClient };\n    this.clientConfig = settings?.clientConfig ?? {};\n    this.fullnodeConfig = settings?.fullnodeConfig ?? {};\n    this.indexerConfig = settings?.indexerConfig ?? {};\n    this.faucetConfig = settings?.faucetConfig ?? {};\n  }\n\n  /**\n   * Returns the URL endpoint to send the request to based on the specified API type.\n   * If a custom URL was provided in the configuration, that URL is returned. Otherwise, the URL endpoint is derived from the network.\n   *\n   * @param apiType - The type of Aptos API to get the URL for. This can be one of the following: FULLNODE, FAUCET, INDEXER, PEPPER, PROVER.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network, AptosApiType } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * async function runExample() {\n   *   // Getting the request URL for the FULLNODE API\n   *   const url = config.getRequestUrl(AptosApiType.FULLNODE);\n   *   console.log(\"Request URL for FULLNODE:\", url);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Client\n   */\n  getRequestUrl(apiType: AptosApiType): string {\n    switch (apiType) {\n      case AptosApiType.FULLNODE:\n        if (this.fullnode !== undefined) return this.fullnode;\n        if (this.network === Network.CUSTOM) throw new Error(\"Please provide a custom full node url\");\n        return NetworkToNodeAPI[this.network];\n      case AptosApiType.FAUCET:\n        if (this.faucet !== undefined) return this.faucet;\n        if (this.network === Network.TESTNET) {\n          throw new Error(\n            \"There is no way to programmatically mint testnet APT, you must use the minting site at https://aptos.dev/network/faucet\",\n          );\n        }\n        if (this.network === Network.MAINNET) {\n          throw new Error(\"There is no mainnet faucet\");\n        }\n        if (this.network === Network.CUSTOM) throw new Error(\"Please provide a custom faucet url\");\n        return NetworkToFaucetAPI[this.network];\n      case AptosApiType.INDEXER:\n        if (this.indexer !== undefined) return this.indexer;\n        if (this.network === Network.CUSTOM) throw new Error(\"Please provide a custom indexer url\");\n        return NetworkToIndexerAPI[this.network];\n      case AptosApiType.PEPPER:\n        if (this.pepper !== undefined) return this.pepper;\n        if (this.network === Network.CUSTOM) throw new Error(\"Please provide a custom pepper service url\");\n        return NetworkToPepperAPI[this.network];\n      case AptosApiType.PROVER:\n        if (this.prover !== undefined) return this.prover;\n        if (this.network === Network.CUSTOM) throw new Error(\"Please provide a custom prover service url\");\n        return NetworkToProverAPI[this.network];\n      default:\n        throw Error(`apiType ${apiType} is not supported`);\n    }\n  }\n\n  /**\n   * Checks if the provided URL is a known pepper service endpoint.\n   *\n   * @param url - The URL to check against the known pepper service endpoints.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * async function runExample() {\n   *     const url = \"https://example.pepper.service\"; // replace with a real pepper service URL\n   *\n   *     // Check if the URL is a known pepper service endpoint\n   *     const isPepperService = config.isPepperServiceRequest(url);\n   *\n   *     console.log(`Is the URL a known pepper service? ${isPepperService}`);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Client\n   */\n  isPepperServiceRequest(url: string): boolean {\n    return NetworkToPepperAPI[this.network] === url;\n  }\n\n  /**\n   * Checks if the provided URL is a known prover service endpoint.\n   *\n   * @param url - The URL to check against known prover service endpoints.\n   * @returns A boolean indicating whether the URL is a known prover service endpoint.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * // Check if the URL is a known prover service endpoint\n   * const url = \"https://prover.testnet.aptos.dev\"; // replace with a real URL if needed\n   * const isProver = config.isProverServiceRequest(url);\n   *\n   * console.log(`Is the URL a known prover service? ${isProver}`);\n   * ```\n   * @group Client\n   */\n  isProverServiceRequest(url: string): boolean {\n    return NetworkToProverAPI[this.network] === url;\n  }\n}\n"], "mappings": "qEAGA,OAAOA,MAAiB,2BAiCjB,IAAMC,EAAN,KAAkB,CAmGvB,YAAYC,EAA0B,CAEpC,GAAIA,GAAU,UAAYA,GAAU,SAAWA,GAAU,QAAUA,GAAU,QAAUA,GAAU,QAC/F,GAAIA,GAAU,UAAY,SACxB,QAAQ,KAAK,mEAAmE,UACvE,CAACA,GAAU,QACpB,MAAM,IAAI,MAAM,oDAAoD,EAIxE,KAAK,QAAUA,GAAU,SAAW,SACpC,KAAK,SAAWA,GAAU,SAC1B,KAAK,OAASA,GAAU,OACxB,KAAK,OAASA,GAAU,OACxB,KAAK,OAASA,GAAU,OACxB,KAAK,QAAUA,GAAU,QACzB,KAAK,OAASA,GAAU,QAAU,CAAE,SAAUC,CAAY,EAC1D,KAAK,aAAeD,GAAU,cAAgB,CAAC,EAC/C,KAAK,eAAiBA,GAAU,gBAAkB,CAAC,EACnD,KAAK,cAAgBA,GAAU,eAAiB,CAAC,EACjD,KAAK,aAAeA,GAAU,cAAgB,CAAC,CACjD,CAwBA,cAAcE,EAA+B,CAC3C,OAAQA,EAAS,CACf,eACE,GAAI,KAAK,WAAa,OAAW,OAAO,KAAK,SAC7C,GAAI,KAAK,UAAY,SAAgB,MAAM,IAAI,MAAM,uCAAuC,EAC5F,OAAOC,EAAiB,KAAK,OAAO,EACtC,aACE,GAAI,KAAK,SAAW,OAAW,OAAO,KAAK,OAC3C,GAAI,KAAK,UAAY,UACnB,MAAM,IAAI,MACR,yHACF,EAEF,GAAI,KAAK,UAAY,UACnB,MAAM,IAAI,MAAM,4BAA4B,EAE9C,GAAI,KAAK,UAAY,SAAgB,MAAM,IAAI,MAAM,oCAAoC,EACzF,OAAOC,EAAmB,KAAK,OAAO,EACxC,cACE,GAAI,KAAK,UAAY,OAAW,OAAO,KAAK,QAC5C,GAAI,KAAK,UAAY,SAAgB,MAAM,IAAI,MAAM,qCAAqC,EAC1F,OAAOC,EAAoB,KAAK,OAAO,EACzC,aACE,GAAI,KAAK,SAAW,OAAW,OAAO,KAAK,OAC3C,GAAI,KAAK,UAAY,SAAgB,MAAM,IAAI,MAAM,4CAA4C,EACjG,OAAOC,EAAmB,KAAK,OAAO,EACxC,aACE,GAAI,KAAK,SAAW,OAAW,OAAO,KAAK,OAC3C,GAAI,KAAK,UAAY,SAAgB,MAAM,IAAI,MAAM,4CAA4C,EACjG,OAAOC,EAAmB,KAAK,OAAO,EACxC,QACE,MAAM,MAAM,WAAWL,CAAO,mBAAmB,CACrD,CACF,CA0BA,uBAAuBM,EAAsB,CAC3C,OAAOF,EAAmB,KAAK,OAAO,IAAME,CAC9C,CAuBA,uBAAuBA,EAAsB,CAC3C,OAAOD,EAAmB,KAAK,OAAO,IAAMC,CAC9C,CACF", "names": ["aptosClient", "AptosConfig", "settings", "aptosClient", "apiType", "NetworkToNodeAPI", "NetworkToFaucetAPI", "NetworkToIndexerAPI", "NetworkToPepperAPI", "NetworkToProverAPI", "url"]}