{"version": 3, "file": "queryclient.js", "sourceRoot": "", "sources": ["../../src/queryclient/queryclient.ts"], "names": [], "mappings": ";;;AAAA,8GAA8G;AAC9G,yCAAqG;AACrG,+CAAkD;AAClD,2CAA4C;AAE5C,yCAAkG;AAMlG,SAAS,eAAe,CAAC,EAAwB,EAAE,IAAY,EAAE,GAAe;IAC9E,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE;QACpB,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;KAC/D;IACD,IAAI,CAAC,IAAA,0BAAkB,EAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE;QACpC,MAAM,IAAI,KAAK,CAAC,kDAAkD,IAAA,gBAAK,EAAC,GAAG,CAAC,aAAa,IAAA,gBAAK,EAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;KAC3G;IACD,OAAO,aAAK,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAC/C,CAAC;AA0BD,MAAa,WAAW;IAwbf,MAAM,CAAC,cAAc,CAC1B,WAAwB,EACxB,GAAG,eAAmD;QAEtD,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;QACnF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,IAAA,cAAM,EAAC,IAAA,uBAAe,EAAC,SAAS,CAAC,EAAE,qCAAqC,CAAC,CAAC;YAC1E,KAAK,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAChE,IAAA,cAAM,EACJ,IAAA,uBAAe,EAAC,WAAW,CAAC,EAC5B,gDAAgD,OAAO,WAAW,gBAAgB,SAAS,IAAI,CAChG,CAAC;gBACF,MAAM,OAAO,GAAI,MAAc,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;gBAChD,MAAc,CAAC,SAAS,CAAC,GAAG;oBAC3B,GAAG,OAAO;oBACV,GAAG,WAAW;iBACf,CAAC;aACH;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAID,YAAmB,WAAwB;QACzC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,kBAAkB,CAC7B,KAAa,EACb,QAAoB,EACpB,aAAsB;QAEtB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QAE/F,MAAM,QAAQ,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;QACvE,MAAM,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,IAAA,kBAAO,EAAC,KAAK,CAAC,CAAC,CAAC;QAEjF,4DAA4D;QAC5D,IAAA,cAAM,EAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACzB,IAAA,cAAM,EAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAE/B,sDAAsD;QACtD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,sBAAsB;YACtB,IAAA,cAAM,EAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC1B,yEAAyE;YACzE,IAAA,0BAAkB,EAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACnF;aAAM;YACL,kBAAkB;YAClB,IAAA,cAAM,EAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACvB,IAAA,cAAM,EAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7B,yEAAyE;YACzE,IAAA,uBAAe,EAAC,QAAQ,CAAC,KAAK,EAAE,gBAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SACpF;QAED,kGAAkG;QAClG,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAChD,IAAA,uBAAe,EAAC,UAAU,CAAC,KAAK,EAAE,sBAAc,EAAE,MAAM,CAAC,OAAO,EAAE,IAAA,kBAAO,EAAC,KAAK,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAE1G,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IAChC,CAAC;IAEM,KAAK,CAAC,aAAa,CACxB,KAAa,EACb,QAAoB,EACpB,aAAsB;QAEtB,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;YAChF,2DAA2D;YAC3D,8GAA8G;YAC9G,IAAI,EAAE,UAAU,KAAK,MAAM;YAC3B,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,aAAa;SACtB,CAAC,CAAC;QAEH,IAAI,IAAI,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;SACxD;QAED,IAAI,CAAC,IAAA,0BAAkB,EAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,gBAAgB,IAAA,gBAAK,EAAC,GAAG,CAAC,4BAA4B,IAAA,gBAAK,EAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SAC1F;QAED,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC7C;QACD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC,2BAA2B,CAAC,CAAC;SACjG;QAED,6EAA6E;QAC7E,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;QACjD,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,IAAA,kBAAO,EAAC,KAAK,CAAC,CAAC,CAAC;QAE9D,OAAO;YACL,GAAG,EAAE,GAAG;YACR,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM;YACd,wDAAwD;YACxD,KAAK,EAAE;gBACL,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;aACpB;SACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,SAAS,CACpB,IAAY,EACZ,OAAmB,EACnB,aAAsB;QAEtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;YAChD,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,aAAa;SACtB,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,IAAI,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;SAC1E;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC7C;QAED,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAED,2CAA2C;IAC3C,8CAA8C;IACtC,KAAK,CAAC,aAAa,CAAC,MAAe;QACzC,IAAA,qBAAa,EAAC,MAAM,CAAC,CAAC;QACtB,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;QAED,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,CAAC;QAChC,IAAI,UAA2C,CAAC;QAChD,IAAI,mBAAyE,CAAC;QAC9E,IAAI;YACF,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE,CAAC;SAClE;QAAC,MAAM;YACN,8DAA8D;SAC/D;QAED,IAAI,mBAAmB,EAAE;YACvB,MAAM,WAAW,GAAG,MAAM,IAAA,mBAAU,EAAC,mBAAmB,CAAC,CAAC;YAC1D,0GAA0G;YAC1G,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY,EAAE;gBACvC,UAAU,GAAG,WAAW,CAAC;aAC1B;SACF;QAED,OAAO,CAAC,UAAU,EAAE;YAClB,+EAA+E;YAC/E,MAAM,aAAa,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,UAAU;iBACvF,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;iBAC1B,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC;YAC1C,IAAI,aAAa,EAAE;gBACjB,UAAU,GAAG,aAAa,CAAC;aAC5B;iBAAM;gBACL,MAAM,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;aACnB;SACF;QAED,IAAA,cAAM,EAAC,UAAU,CAAC,MAAM,KAAK,YAAY,EAAE,qDAAqD,CAAC,CAAC;QAClG,OAAO,UAAU,CAAC;IACpB,CAAC;CACF;AAlnBD,kCAknBC"}