import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cometbft.crypto.v1";
/** PublicKey is a ED25519 or a secp256k1 public key. */
export interface PublicKey {
    ed25519?: Uint8Array | undefined;
    secp256k1?: Uint8Array | undefined;
    bls12381?: Uint8Array | undefined;
    secp256k1eth?: Uint8Array | undefined;
}
export declare const PublicKey: {
    encode(message: <PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PublicKey;
    fromJSON(object: any): PublicKey;
    toJSON(message: PublicKey): unknown;
    create(base?: DeepPartial<PublicKey>): PublicKey;
    fromPartial(object: DeepPartial<PublicKey>): PublicKey;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
