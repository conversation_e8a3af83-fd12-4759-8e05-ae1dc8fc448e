import BaseRestConsumer from '../../base/BaseRestConsumer.js';
import { HttpRequestException, UnspecifiedErrorCode, } from '@injectivelabs/exceptions';
import { IndexerModule } from '../types/index.js';
/**
 * @category Indexer Chronos API
 */
export class IndexerRestDerivativesChronosApi extends BaseRestConsumer {
    async fetchMarketSummary(marketId) {
        const path = `market_summary`;
        try {
            const { data } = await this.retry(() => this.get(path, {
                marketId,
                resolution: '24h',
            }));
            return data;
        }
        catch (e) {
            if (e instanceof HttpRequestException) {
                throw e;
            }
            throw new HttpRequestException(new Error(e), {
                code: UnspecifiedErrorCode,
                context: `${this.endpoint}/${path}?marketId=${marketId}`,
                contextModule: IndexerModule.ChronosDerivative,
            });
        }
    }
    async fetchMarketsSummary() {
        const path = `market_summary_all`;
        try {
            const { data } = await this.retry(() => this.get(path, {
                resolution: '24h',
            }));
            return data;
        }
        catch (e) {
            if (e instanceof HttpRequestException) {
                throw e;
            }
            throw new HttpRequestException(new Error(e), {
                code: UnspecifiedErrorCode,
                context: `${this.endpoint}/${path}`,
                contextModule: IndexerModule.ChronosDerivative,
            });
        }
    }
}
