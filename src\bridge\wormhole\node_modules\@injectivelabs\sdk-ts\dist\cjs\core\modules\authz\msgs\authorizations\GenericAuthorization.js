"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const Base_js_1 = require("./Base.js");
const exceptions_1 = require("@injectivelabs/exceptions");
const utils_js_1 = require("../../utils.js");
/**
 * @category Contract Exec Arguments
 */
class GenericAuthorization extends Base_js_1.BaseAuthorization {
    static fromJSON(params) {
        return new GenericAuthorization(params);
    }
    toProto() {
        const genericAuthorization = core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(this.toAny().value);
        return genericAuthorization;
    }
    toAmino() {
        const genericAuthorization = core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(this.toAny().value);
        return {
            type: 'cosmos-sdk/GenericAuthorization',
            value: { msg: genericAuthorization.msg },
        };
    }
    toWeb3() {
        const genericAuthorization = core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(this.toAny().value);
        return {
            '@type': '/cosmos.authz.v1beta1.GenericAuthorization',
            msg: genericAuthorization.msg,
        };
    }
    toAny() {
        const { params } = this;
        if (!params.authorization && !params.messageTypeUrl) {
            throw new exceptions_1.GeneralException(new Error('Either authorization or messageType must be provided'));
        }
        return (params.authorization ||
            (0, utils_js_1.getGenericAuthorizationFromMessageType)(params.messageTypeUrl));
    }
}
exports.default = GenericAuthorization;
