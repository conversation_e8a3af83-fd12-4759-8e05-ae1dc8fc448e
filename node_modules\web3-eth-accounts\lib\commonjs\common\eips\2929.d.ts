declare const _default: {
    name: string;
    comment: string;
    url: string;
    status: string;
    minimumHardfork: string;
    gasConfig: {};
    gasPrices: {
        coldsload: {
            v: number;
            d: string;
        };
        coldaccountaccess: {
            v: number;
            d: string;
        };
        warmstorageread: {
            v: number;
            d: string;
        };
        sstoreCleanGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreNoopGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreDirtyGasEIP2200: {
            v: number;
            d: string;
        };
        sstoreInitRefundEIP2200: {
            v: number;
            d: string;
        };
        sstoreCleanRefundEIP2200: {
            v: number;
            d: string;
        };
        call: {
            v: number;
            d: string;
        };
        callcode: {
            v: number;
            d: string;
        };
        delegatecall: {
            v: number;
            d: string;
        };
        staticcall: {
            v: number;
            d: string;
        };
        balance: {
            v: number;
            d: string;
        };
        extcodesize: {
            v: number;
            d: string;
        };
        extcodecopy: {
            v: number;
            d: string;
        };
        extcodehash: {
            v: number;
            d: string;
        };
        sload: {
            v: number;
            d: string;
        };
        sstore: {
            v: number;
            d: string;
        };
    };
    vm: {};
    pow: {};
};
export default _default;
