import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cometbft.mempool.v2";
/** Txs contains a list of transaction from the mempool. */
export interface Txs {
    txs: Uint8Array[];
}
/**
 * HaveTx is sent by the DOG protocol to signal a peer that the sender already
 * has a transaction.
 */
export interface HaveTx {
    txKey: Uint8Array;
}
/**
 * ResetRoute is sent by the DOG protocol to signal a peer to reset a (random)
 * route to the sender.
 */
export interface ResetRoute {
}
/** Message is an abstract mempool message. */
export interface Message {
    txs?: Txs | undefined;
    haveTx?: HaveTx | undefined;
    resetRoute?: ResetRoute | undefined;
}
export declare const Txs: {
    encode(message: Txs, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Txs;
    fromJSON(object: any): Txs;
    toJSON(message: Txs): unknown;
    create(base?: DeepPartial<Txs>): Txs;
    fromPartial(object: DeepPartial<Txs>): Txs;
};
export declare const HaveTx: {
    encode(message: HaveTx, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HaveTx;
    fromJSON(object: any): HaveTx;
    toJSON(message: HaveTx): unknown;
    create(base?: DeepPartial<HaveTx>): HaveTx;
    fromPartial(object: DeepPartial<HaveTx>): HaveTx;
};
export declare const ResetRoute: {
    encode(_: ResetRoute, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResetRoute;
    fromJSON(_: any): ResetRoute;
    toJSON(_: ResetRoute): unknown;
    create(base?: DeepPartial<ResetRoute>): ResetRoute;
    fromPartial(_: DeepPartial<ResetRoute>): ResetRoute;
};
export declare const Message: {
    encode(message: Message, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Message;
    fromJSON(object: any): Message;
    toJSON(message: Message): unknown;
    create(base?: DeepPartial<Message>): Message;
    fromPartial(object: DeepPartial<Message>): Message;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
