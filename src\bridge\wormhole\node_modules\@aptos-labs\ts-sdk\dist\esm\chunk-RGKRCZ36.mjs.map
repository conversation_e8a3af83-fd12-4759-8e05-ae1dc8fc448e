{"version": 3, "sources": ["../../src/core/accountAddress.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bytesToHex, hexToBytes } from \"@noble/hashes/utils\";\nimport { Serializable, Serializer } from \"../bcs/serializer\";\nimport { Deserializer } from \"../bcs/deserializer\";\nimport { Parsing<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"./common\";\nimport { TransactionArgument } from \"../transactions/instances/transactionArgument\";\nimport { HexInput, ScriptTransactionArgumentVariants } from \"../types\";\n\n/**\n * Provides reasons for an address was invalid.\n * @group Implementation\n * @category Serialization\n */\nexport enum AddressInvalidReason {\n  INCORRECT_NUMBER_OF_BYTES = \"incorrect_number_of_bytes\",\n  INVALID_HEX_CHARS = \"invalid_hex_chars\",\n  TOO_SHORT = \"too_short\",\n  TOO_LONG = \"too_long\",\n  LEADING_ZERO_X_REQUIRED = \"leading_zero_x_required\",\n  LONG_FORM_REQUIRED_UNLESS_SPECIAL = \"long_form_required_unless_special\",\n  INVALID_PADDING_ZEROES = \"INVALID_PADDING_ZEROES\",\n  INVALID_PADDING_STRICTNESS = \"INVALID_PADDING_STRICTNESS\",\n}\n\n/**\n * The input for an account address, which can be either a hexadecimal string or a standard account address.\n * @group Implementation\n * @category Serialization\n */\nexport type AccountAddressInput = HexInput | AccountAddress;\n\n/**\n * NOTE: Only use this class for account addresses. For other hex data, e.g. transaction\n * hashes, use the Hex class.\n *\n * AccountAddress is used for working with account addresses. Account addresses, when\n * represented as a string, generally look like these examples:\n * - 0x1\n * - 0xaa86fe99004361f747f91342ca13c426ca0cccb0c1217677180c9493bad6ef0c\n *\n * Proper formatting and parsing of account addresses is defined by AIP-40.\n * To learn more about the standard, read the AIP here:\n * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n *\n * The comments in this class make frequent reference to the LONG and SHORT formats,\n * as well as \"special\" addresses. To learn what these refer to see AIP-40.\n * @group Implementation\n * @category Serialization\n */\nexport class AccountAddress extends Serializable implements TransactionArgument {\n  /**\n   * This is the internal representation of an account address.\n   * @group Implementation\n   * @category Serialization\n   */\n  readonly data: Uint8Array;\n\n  /**\n   * The number of bytes that make up an account address.\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The length of an address string in LONG form without a leading 0x.\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LONG_STRING_LENGTH: number = 64;\n\n  static ZERO: AccountAddress = AccountAddress.from(\"0x0\");\n\n  static ONE: AccountAddress = AccountAddress.from(\"0x1\");\n\n  static TWO: AccountAddress = AccountAddress.from(\"0x2\");\n\n  static THREE: AccountAddress = AccountAddress.from(\"0x3\");\n\n  static FOUR: AccountAddress = AccountAddress.from(\"0x4\");\n\n  static A: AccountAddress = AccountAddress.from(\"0xA\");\n\n  /**\n   * Creates an instance of AccountAddress from a Uint8Array.\n   *\n   * This function ensures that the input data is exactly 32 bytes long, which is required for a valid account address.\n   *\n   * @param input A Uint8Array representing an account address.\n   * @throws ParsingError if the input length is not equal to 32 bytes.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(input: Uint8Array) {\n    super();\n    if (input.length !== AccountAddress.LENGTH) {\n      throw new ParsingError(\n        \"AccountAddress data should be exactly 32 bytes long\",\n        AddressInvalidReason.INCORRECT_NUMBER_OF_BYTES,\n      );\n    }\n    this.data = input;\n  }\n\n  /**\n   * Determines if the address is classified as special, which is defined as 0x0 to 0xf inclusive.\n   * In other words, the last byte of the address must be < 0b10000 (16)\n   * and every other byte must be zero.\n   *\n   * For more information on how special addresses are defined, see AIP-40:\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   *\n   * @returns true if the address is special, false otherwise.\n   * @group Implementation\n   * @category Serialization\n   */\n  isSpecial(): boolean {\n    return (\n      this.data.slice(0, this.data.length - 1).every((byte) => byte === 0) && this.data[this.data.length - 1] < 0b10000\n    );\n  }\n  // ===\n  // Methods for representing an instance of AccountAddress as other types.\n  // ===\n\n  /**\n   * Return the AccountAddress as a string as per AIP-40.\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   * This representation returns special addresses in SHORT form (0xf)\n   * and other addresses in LONG form (0x + 64 characters).\n   *\n   * @returns AccountAddress as a string conforming to AIP-40.\n   * @group Implementation\n   * @category Serialization\n   */\n  toString(): `0x${string}` {\n    return `0x${this.toStringWithoutPrefix()}`;\n  }\n\n  /**\n   * Return the AccountAddress as a string conforming to AIP-40 but without the leading 0x.\n   *\n   * NOTE: Prefer to use `toString` where possible.\n   *\n   * @returns AccountAddress as a string without the leading 0x.\n   * @group Implementation\n   * @category Serialization\n   */\n  toStringWithoutPrefix(): string {\n    let hex = bytesToHex(this.data);\n    if (this.isSpecial()) {\n      hex = hex[hex.length - 1];\n    }\n    return hex;\n  }\n\n  /**\n   * Convert the account address to a string in LONG format, which is always 0x followed by 64 hex characters.\n   *\n   * NOTE: Prefer to use `toString` where possible, as it formats special addresses using the SHORT form (no leading 0s).\n   *\n   * @returns AccountAddress as a string in LONG form.\n   * @group Implementation\n   * @category Serialization\n   */\n  toStringLong(): `0x${string}` {\n    return `0x${this.toStringLongWithoutPrefix()}`;\n  }\n\n  /**\n   * Returns the account address as a string in LONG form without a leading 0x.\n   * This function will include leading zeroes and will produce a string of 64 hex characters.\n   *\n   * NOTE: Prefer to use `toString` where possible, as it formats special addresses using the SHORT form (no leading 0s).\n   *\n   * @returns {string} The account address in LONG form.\n   * @group Implementation\n   * @category Serialization\n   */\n  toStringLongWithoutPrefix(): string {\n    return bytesToHex(this.data);\n  }\n\n  /**\n   * Convert the account address to a string in SHORT format, which is 0x followed by the shortest\n   * possible representation (no leading zeros).\n   *\n   * @returns AccountAddress as a string in SHORT form.\n   * @group Implementation\n   * @category Serialization\n   */\n  toStringShort(): `0x${string}` {\n    return `0x${this.toStringShortWithoutPrefix()}`;\n  }\n\n  /**\n   * Returns a lossless short string representation of the address by trimming leading zeros.\n   * If the address consists of all zeros, returns \"0\".\n   *\n   * @returns A string representation of the address without leading zeros\n   * @group Implementation\n   * @category Serialization\n   */\n  toStringShortWithoutPrefix(): string {\n    const hex = bytesToHex(this.data).replace(/^0+/, \"\");\n    return hex === \"\" ? \"0\" : hex;\n  }\n\n  /**\n   * Get the inner data as a Uint8Array.\n   * The inner data is already a Uint8Array, so no conversion takes place.\n   *\n   * @returns Hex data as Uint8Array\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.data;\n  }\n\n  /**\n   * Serialize the AccountAddress to a Serializer instance's data buffer.\n   * @param serializer The serializer to serialize the AccountAddress to.\n   * @returns void\n   * @example\n   * const serializer = new Serializer();\n   * const address = AccountAddress.fromString(\"0x1\");\n   * address.serialize(serializer);\n   * const bytes = serializer.toUint8Array();\n   * // `bytes` is now the BCS-serialized address.\n   * @group Implementation\n   * @category Serialization\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.data);\n  }\n\n  /**\n   * Serializes the current instance into a byte sequence suitable for entry functions.\n   * This allows for the proper encoding of data when interacting with entry functions in the blockchain.\n   *\n   * @param serializer - The serializer instance used to convert the data into bytes.\n   * @group Implementation\n   * @category Serialization\n   */\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  /**\n   * Serializes the current instance for use in a script function by encoding it into a byte sequence.\n   * This process involves serializing the variant index and the instance data, making it suitable for transmission.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category Serialization\n   */\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.Address);\n    serializer.serialize(this);\n  }\n\n  /**\n   * Deserialize an AccountAddress from the byte buffer in a Deserializer instance.\n   * This function allows you to convert a byte representation of an AccountAddress into an instance of AccountAddress.\n   * @param deserializer The deserializer to deserialize the AccountAddress from.\n   * @returns An instance of AccountAddress.\n   * @example\n   * const bytes = hexToBytes(\"0x0102030405060708091011121314151617181920212223242526272829303132\");\n   * const deserializer = new Deserializer(bytes);\n   * const address = AccountAddress.deserialize(deserializer);\n   * // `address` is now an instance of AccountAddress.\n   * @group Implementation\n   * @category Serialization\n   */\n  static deserialize(deserializer: Deserializer): AccountAddress {\n    const bytes = deserializer.deserializeFixedBytes(AccountAddress.LENGTH);\n    return new AccountAddress(bytes);\n  }\n\n  // ===\n  // Methods for creating an instance of AccountAddress from other types.\n  // ===\n\n  /**\n   * NOTE: This function has strict parsing behavior. For relaxed behavior, please use\n   * the `fromString` function.\n   *\n   * Creates an instance of AccountAddress from a hex string.\n   *\n   * This function allows only the strictest formats defined by AIP-40. In short this\n   * means only the following formats are accepted:\n   *\n   * - LONG\n   * - SHORT for special addresses\n   *\n   * Where:\n   * - LONG is defined as 0x + 64 hex characters.\n   * - SHORT for special addresses is 0x0 to 0xf inclusive without padding zeroes.\n   *\n   * This means the following are not accepted:\n   * - SHORT for non-special addresses.\n   * - Any address without a leading 0x.\n   *\n   * @param input - A hex string representing an account address.\n   *\n   * @throws {ParsingError} If the hex string does not start with 0x or is not in a valid format.\n   *\n   * @remarks\n   *\n   * This function has strict parsing behavior. For relaxed behavior, please use the `fromString` function.\n   *\n   * @see AIP-40 documentation for more details on address formats:\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   *\n   * @returns An instance of AccountAddress.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromStringStrict(input: string): AccountAddress {\n    // Assert the string starts with 0x.\n    if (!input.startsWith(\"0x\")) {\n      throw new ParsingError(\"Hex string must start with a leading 0x.\", AddressInvalidReason.LEADING_ZERO_X_REQUIRED);\n    }\n\n    const address = AccountAddress.fromString(input);\n\n    // Check if the address is in LONG form. If it is not, this is only allowed for\n    // special addresses, in which case we check it is in proper SHORT form.\n    if (input.length !== AccountAddress.LONG_STRING_LENGTH + 2) {\n      if (!address.isSpecial()) {\n        throw new ParsingError(\n          `The given hex string ${input} is not a special address, it must be represented as 0x + 64 chars.`,\n          AddressInvalidReason.LONG_FORM_REQUIRED_UNLESS_SPECIAL,\n        );\n      } else if (input.length !== 3) {\n        // 0x + one hex char is the only valid SHORT form for special addresses.\n        throw new ParsingError(\n          // eslint-disable-next-line max-len\n          `The given hex string ${input} is a special address not in LONG form, it must be 0x0 to 0xf without padding zeroes.`,\n          AddressInvalidReason.INVALID_PADDING_ZEROES,\n        );\n      }\n    }\n\n    return address;\n  }\n\n  /**\n   * NOTE: This function has relaxed parsing behavior. For strict behavior, please use\n   * the `fromStringStrict` function. Where possible use `fromStringStrict` rather than this\n   * function, `fromString`.\n   *\n   * Creates an instance of AccountAddress from a hex string.\n   *\n   * This function allows all formats defined by AIP-40. In short this means the\n   * following formats are accepted:\n   *\n   * - LONG, with or without leading 0x\n   * - SHORT*, with or without leading 0x\n   *\n   * Where:\n   * - LONG is 64 hex characters.\n   * - SHORT* is 1 to 63 hex characters inclusive. The address can have missing values up to `maxMissingChars` before it is padded.\n   * - Padding zeroes are allowed, e.g. 0x0123 is valid.\n   *\n   * Learn more about the different address formats by reading AIP-40:\n   * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.\n   *\n   * @param input A hex string representing an account address.\n   * @param args.maxMissingChars The number of characters that can be missing in a padded address before it is invalid.\n   *\n   * @returns An instance of AccountAddress.\n   *\n   * @throws ParsingError if the hex string is too short, too long, or contains invalid characters.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromString(input: string, { maxMissingChars = 4 }: { maxMissingChars?: number } = {}): AccountAddress {\n    let parsedInput = input;\n    // Remove leading 0x for parsing.\n    if (input.startsWith(\"0x\")) {\n      parsedInput = input.slice(2);\n    }\n\n    // Ensure the address string is at least 1 character long.\n    if (parsedInput.length === 0) {\n      throw new ParsingError(\n        \"Hex string is too short, must be 1 to 64 chars long, excluding the leading 0x.\",\n        AddressInvalidReason.TOO_SHORT,\n      );\n    }\n\n    // Ensure the address string is not longer than 64 characters.\n    if (parsedInput.length > 64) {\n      throw new ParsingError(\n        \"Hex string is too long, must be 1 to 64 chars long, excluding the leading 0x.\",\n        AddressInvalidReason.TOO_LONG,\n      );\n    }\n\n    // Ensure that the maxMissingChars is between or equal to 0 and 63.\n    if (maxMissingChars > 63 || maxMissingChars < 0) {\n      throw new ParsingError(\n        `maxMissingChars must be between or equal to 0 and 63. Received ${maxMissingChars}`,\n        AddressInvalidReason.INVALID_PADDING_STRICTNESS,\n      );\n    }\n\n    let addressBytes: Uint8Array;\n    try {\n      // Pad the address with leading zeroes, so it is 64 chars long and then convert\n      // the hex string to bytes. Every two characters in a hex string constitutes a\n      // single byte. So a 64 length hex string becomes a 32 byte array.\n      addressBytes = hexToBytes(parsedInput.padStart(64, \"0\"));\n    } catch (error: any) {\n      // At this point the only way this can fail is if the hex string contains\n      // invalid characters.\n      throw new ParsingError(`Hex characters are invalid: ${error?.message}`, AddressInvalidReason.INVALID_HEX_CHARS);\n    }\n\n    const address = new AccountAddress(addressBytes);\n\n    // Cannot pad the address if it has more than maxMissingChars missing.\n    if (parsedInput.length < 64 - maxMissingChars) {\n      if (!address.isSpecial()) {\n        throw new ParsingError(\n          `Hex string is too short, must be ${64 - maxMissingChars} to 64 chars long, excluding the leading 0x. You may need to fix \nthe addresss by padding it with 0s before passing it to \\`fromString\\` (e.g. <addressString>.padStart(64, '0')). \nReceived ${input}`,\n          AddressInvalidReason.TOO_SHORT,\n        );\n      }\n    }\n\n    return address;\n  }\n\n  /**\n   * Convenience method for creating an AccountAddress from various input types.\n   * This function accepts a string, Uint8Array, or an existing AccountAddress instance and returns the corresponding\n   * AccountAddress.\n   *\n   * @param input - The input to convert into an AccountAddress. This can be a string representation of an address, a Uint8Array,\n   * or an existing AccountAddress.\n   * @param args.maxMissingChars The number of characters that can be missing in a padded address before it is invalid.\n   * @group Implementation\n   * @category Serialization\n   */\n  static from(input: AccountAddressInput, { maxMissingChars = 4 }: { maxMissingChars?: number } = {}): AccountAddress {\n    if (typeof input === \"string\") {\n      return AccountAddress.fromString(input, { maxMissingChars });\n    }\n    if (input instanceof Uint8Array) {\n      return new AccountAddress(input);\n    }\n    return input;\n  }\n\n  /**\n   * Create an AccountAddress from various input types, including strings, Uint8Array, and AccountAddress instances.\n   *\n   * @param input - The input to convert into an AccountAddress, which can be a string, a Uint8Array, or an AccountAddress.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromStrict(input: AccountAddressInput): AccountAddress {\n    if (typeof input === \"string\") {\n      return AccountAddress.fromStringStrict(input);\n    }\n    if (input instanceof Uint8Array) {\n      return new AccountAddress(input);\n    }\n    return input;\n  }\n  // ===\n  // Methods for checking validity.\n  // ===\n\n  /**\n   * Check if the provided input is a valid AccountAddress.\n   *\n   * @param args - The arguments for validation.\n   * @param args.input - A hex string representing an account address.\n   * @param args.strict - If true, use strict parsing behavior; if false, use relaxed parsing behavior.\n   *\n   * @returns An object indicating whether the address is valid. If valid, valid = true; if not, valid = false with additional details.\n   * If the address is invalid, invalidReason will explain why it is invalid, and invalidReasonMessage will provide the error message.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isValid(args: { input: AccountAddressInput; strict?: boolean }): ParsingResult<AddressInvalidReason> {\n    try {\n      if (args.strict) {\n        AccountAddress.fromStrict(args.input);\n      } else {\n        AccountAddress.from(args.input);\n      }\n      return { valid: true };\n    } catch (error: any) {\n      return {\n        valid: false,\n        invalidReason: error?.invalidReason,\n        invalidReasonMessage: error?.message,\n      };\n    }\n  }\n\n  /**\n   * Determine if two AccountAddresses are equal based on their underlying byte data.\n   *\n   * @param other - The AccountAddress to compare to.\n   * @returns true if the AccountAddresses are equal, false if not.\n   * @group Implementation\n   * @category Serialization\n   */\n  equals(other: AccountAddress): boolean {\n    if (this.data.length !== other.data.length) return false;\n    return this.data.every((value, index) => value === other.data[index]);\n  }\n}\n"], "mappings": "kFAGA,OAAS,cAAAA,EAAY,cAAAC,MAAkB,sBAYhC,IAAKC,OACVA,EAAA,0BAA4B,4BAC5BA,EAAA,kBAAoB,oBACpBA,EAAA,UAAY,YACZA,EAAA,SAAW,WACXA,EAAA,wBAA0B,0BAC1BA,EAAA,kCAAoC,oCACpCA,EAAA,uBAAyB,yBACzBA,EAAA,2BAA6B,6BARnBA,OAAA,IAoCCC,EAAN,MAAMA,UAAuBC,CAA4C,CA4C9E,YAAYC,EAAmB,CAE7B,GADA,MAAM,EACFA,EAAM,SAAWF,EAAe,OAClC,MAAM,IAAIG,EACR,sDACA,2BACF,EAEF,KAAK,KAAOD,CACd,CAcA,WAAqB,CACnB,OACE,KAAK,KAAK,MAAM,EAAG,KAAK,KAAK,OAAS,CAAC,EAAE,MAAOE,GAASA,IAAS,CAAC,GAAK,KAAK,KAAK,KAAK,KAAK,OAAS,CAAC,EAAI,EAE9G,CAeA,UAA0B,CACxB,MAAO,KAAK,KAAK,sBAAsB,CAAC,EAC1C,CAWA,uBAAgC,CAC9B,IAAIC,EAAMC,EAAW,KAAK,IAAI,EAC9B,OAAI,KAAK,UAAU,IACjBD,EAAMA,EAAIA,EAAI,OAAS,CAAC,GAEnBA,CACT,CAWA,cAA8B,CAC5B,MAAO,KAAK,KAAK,0BAA0B,CAAC,EAC9C,CAYA,2BAAoC,CAClC,OAAOC,EAAW,KAAK,IAAI,CAC7B,CAUA,eAA+B,CAC7B,MAAO,KAAK,KAAK,2BAA2B,CAAC,EAC/C,CAUA,4BAAqC,CACnC,IAAMD,EAAMC,EAAW,KAAK,IAAI,EAAE,QAAQ,MAAO,EAAE,EACnD,OAAOD,IAAQ,GAAK,IAAMA,CAC5B,CAUA,cAA2B,CACzB,OAAO,KAAK,IACd,CAeA,UAAUE,EAA8B,CACtCA,EAAW,oBAAoB,KAAK,IAAI,CAC1C,CAUA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAUA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA+D,EAC1EA,EAAW,UAAU,IAAI,CAC3B,CAeA,OAAO,YAAYE,EAA4C,CAC7D,IAAMC,EAAQD,EAAa,sBAAsBT,EAAe,MAAM,EACtE,OAAO,IAAIA,EAAeU,CAAK,CACjC,CAyCA,OAAO,iBAAiBR,EAA+B,CAErD,GAAI,CAACA,EAAM,WAAW,IAAI,EACxB,MAAM,IAAIC,EAAa,2CAA4C,yBAA4C,EAGjH,IAAMQ,EAAUX,EAAe,WAAWE,CAAK,EAI/C,GAAIA,EAAM,SAAWF,EAAe,mBAAqB,EACvD,GAAKW,EAAQ,UAAU,GAKhB,GAAIT,EAAM,SAAW,EAE1B,MAAM,IAAIC,EAER,wBAAwBD,CAAK,wFAC7B,wBACF,MAVA,OAAM,IAAIC,EACR,wBAAwBD,CAAK,sEAC7B,mCACF,EAWJ,OAAOS,CACT,CAgCA,OAAO,WAAWT,EAAe,CAAE,gBAAAU,EAAkB,CAAE,EAAkC,CAAC,EAAmB,CAC3G,IAAIC,EAAcX,EAOlB,GALIA,EAAM,WAAW,IAAI,IACvBW,EAAcX,EAAM,MAAM,CAAC,GAIzBW,EAAY,SAAW,EACzB,MAAM,IAAIV,EACR,iFACA,WACF,EAIF,GAAIU,EAAY,OAAS,GACvB,MAAM,IAAIV,EACR,gFACA,UACF,EAIF,GAAIS,EAAkB,IAAMA,EAAkB,EAC5C,MAAM,IAAIT,EACR,kEAAkES,CAAe,GACjF,4BACF,EAGF,IAAIE,EACJ,GAAI,CAIFA,EAAeC,EAAWF,EAAY,SAAS,GAAI,GAAG,CAAC,CACzD,OAASG,EAAY,CAGnB,MAAM,IAAIb,EAAa,+BAA+Ba,GAAO,OAAO,GAAI,mBAAsC,CAChH,CAEA,IAAML,EAAU,IAAIX,EAAec,CAAY,EAG/C,GAAID,EAAY,OAAS,GAAKD,GACxB,CAACD,EAAQ,UAAU,EACrB,MAAM,IAAIR,EACR,oCAAoC,GAAKS,CAAe;AAAA;AAAA,WAEvDV,CAAK,GACN,WACF,EAIJ,OAAOS,CACT,CAaA,OAAO,KAAKT,EAA4B,CAAE,gBAAAU,EAAkB,CAAE,EAAkC,CAAC,EAAmB,CAClH,OAAI,OAAOV,GAAU,SACZF,EAAe,WAAWE,EAAO,CAAE,gBAAAU,CAAgB,CAAC,EAEzDV,aAAiB,WACZ,IAAIF,EAAeE,CAAK,EAE1BA,CACT,CASA,OAAO,WAAWA,EAA4C,CAC5D,OAAI,OAAOA,GAAU,SACZF,EAAe,iBAAiBE,CAAK,EAE1CA,aAAiB,WACZ,IAAIF,EAAeE,CAAK,EAE1BA,CACT,CAiBA,OAAO,QAAQe,EAA6F,CAC1G,GAAI,CACF,OAAIA,EAAK,OACPjB,EAAe,WAAWiB,EAAK,KAAK,EAEpCjB,EAAe,KAAKiB,EAAK,KAAK,EAEzB,CAAE,MAAO,EAAK,CACvB,OAASD,EAAY,CACnB,MAAO,CACL,MAAO,GACP,cAAeA,GAAO,cACtB,qBAAsBA,GAAO,OAC/B,CACF,CACF,CAUA,OAAOE,EAAgC,CACrC,OAAI,KAAK,KAAK,SAAWA,EAAM,KAAK,OAAe,GAC5C,KAAK,KAAK,MAAM,CAACC,EAAOC,IAAUD,IAAUD,EAAM,KAAKE,CAAK,CAAC,CACtE,CACF,EAxdapB,EAaK,OAAiB,GAbtBA,EAoBK,mBAA6B,GApBlCA,EAsBJ,KAAuBA,EAAe,KAAK,KAAK,EAtB5CA,EAwBJ,IAAsBA,EAAe,KAAK,KAAK,EAxB3CA,EA0BJ,IAAsBA,EAAe,KAAK,KAAK,EA1B3CA,EA4BJ,MAAwBA,EAAe,KAAK,KAAK,EA5B7CA,EA8BJ,KAAuBA,EAAe,KAAK,KAAK,EA9B5CA,EAgCJ,EAAoBA,EAAe,KAAK,KAAK,EAhC/C,IAAMqB,EAANrB", "names": ["bytesToHex", "hexToBytes", "AddressInvalidReason", "_Account<PERSON>ddress", "Serializable", "input", "ParsingE<PERSON>r", "byte", "hex", "bytesToHex", "serializer", "bcsBytes", "deserializer", "bytes", "address", "maxMissingChars", "parsedInput", "addressBytes", "hexToBytes", "error", "args", "other", "value", "index", "Account<PERSON><PERSON><PERSON>"]}