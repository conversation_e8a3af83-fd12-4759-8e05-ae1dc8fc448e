"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GolangBinding = exports.ModuleConfig = exports.Config = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var any_1 = require("../../../google/protobuf/any.js");
exports.protobufPackage = "cosmos.app.v1alpha1";
function createBaseConfig() {
    return { modules: [], golangBindings: [] };
}
exports.Config = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.modules), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exports.ModuleConfig.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(message.golangBindings), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.GolangBinding.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseConfig();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.modules.push(exports.ModuleConfig.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.golangBindings.push(exports.GolangBinding.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            modules: Array.isArray(object === null || object === void 0 ? void 0 : object.modules) ? object.modules.map(function (e) { return exports.ModuleConfig.fromJSON(e); }) : [],
            golangBindings: Array.isArray(object === null || object === void 0 ? void 0 : object.golangBindings)
                ? object.golangBindings.map(function (e) { return exports.GolangBinding.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.modules) {
            obj.modules = message.modules.map(function (e) { return e ? exports.ModuleConfig.toJSON(e) : undefined; });
        }
        else {
            obj.modules = [];
        }
        if (message.golangBindings) {
            obj.golangBindings = message.golangBindings.map(function (e) { return e ? exports.GolangBinding.toJSON(e) : undefined; });
        }
        else {
            obj.golangBindings = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Config.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseConfig();
        message.modules = ((_a = object.modules) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.ModuleConfig.fromPartial(e); })) || [];
        message.golangBindings = ((_b = object.golangBindings) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.GolangBinding.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseModuleConfig() {
    return { name: "", config: undefined, golangBindings: [] };
}
exports.ModuleConfig = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.config !== undefined) {
            any_1.Any.encode(message.config, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.golangBindings), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.GolangBinding.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModuleConfig();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.name = reader.string();
                    break;
                case 2:
                    message.config = any_1.Any.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.golangBindings.push(exports.GolangBinding.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            name: isSet(object.name) ? String(object.name) : "",
            config: isSet(object.config) ? any_1.Any.fromJSON(object.config) : undefined,
            golangBindings: Array.isArray(object === null || object === void 0 ? void 0 : object.golangBindings)
                ? object.golangBindings.map(function (e) { return exports.GolangBinding.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.name !== undefined && (obj.name = message.name);
        message.config !== undefined && (obj.config = message.config ? any_1.Any.toJSON(message.config) : undefined);
        if (message.golangBindings) {
            obj.golangBindings = message.golangBindings.map(function (e) { return e ? exports.GolangBinding.toJSON(e) : undefined; });
        }
        else {
            obj.golangBindings = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ModuleConfig.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseModuleConfig();
        message.name = (_a = object.name) !== null && _a !== void 0 ? _a : "";
        message.config = (object.config !== undefined && object.config !== null)
            ? any_1.Any.fromPartial(object.config)
            : undefined;
        message.golangBindings = ((_b = object.golangBindings) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.GolangBinding.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseGolangBinding() {
    return { interfaceType: "", implementation: "" };
}
exports.GolangBinding = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.interfaceType !== "") {
            writer.uint32(10).string(message.interfaceType);
        }
        if (message.implementation !== "") {
            writer.uint32(18).string(message.implementation);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGolangBinding();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.interfaceType = reader.string();
                    break;
                case 2:
                    message.implementation = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            interfaceType: isSet(object.interfaceType) ? String(object.interfaceType) : "",
            implementation: isSet(object.implementation) ? String(object.implementation) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.interfaceType !== undefined && (obj.interfaceType = message.interfaceType);
        message.implementation !== undefined && (obj.implementation = message.implementation);
        return obj;
    },
    create: function (base) {
        return exports.GolangBinding.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseGolangBinding();
        message.interfaceType = (_a = object.interfaceType) !== null && _a !== void 0 ? _a : "";
        message.implementation = (_b = object.implementation) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
