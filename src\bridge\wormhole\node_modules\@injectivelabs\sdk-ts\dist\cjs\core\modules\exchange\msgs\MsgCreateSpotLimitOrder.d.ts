import { MsgBase } from '../../MsgBase.js';
import { SnakeCaseKeys } from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Exchange, InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgCreateSpotLimitOrder {
    interface Params {
        marketId: string;
        subaccountId: string;
        injectiveAddress: string;
        orderType: InjectiveExchangeV1Beta1Exchange.OrderType;
        triggerPrice?: string;
        feeRecipient: string;
        price: string;
        quantity: string;
        cid?: string;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgCreateSpotLimitOrder;
}
/**
 * @category Messages
 */
export default class MsgCreateSpotLimitOrder extends MsgBase<MsgCreateSpotLimitOrder.Params, MsgCreateSpotLimitOrder.Proto> {
    static fromJSON(params: MsgCreateSpotLimitOrder.Params): MsgCreateSpotLimitOrder;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgCreateSpotLimitOrder;
    toData(): {
        sender: string;
        order: InjectiveExchangeV1Beta1Exchange.SpotOrder | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<InjectiveExchangeV1Beta1Tx.MsgCreateSpotLimitOrder>;
    };
    toWeb3Gw(): {
        sender: string;
        order: {
            market_id: string;
            order_info: {
                subaccount_id: string;
                fee_recipient: string;
                price: string;
                quantity: string;
                cid: string;
            } | undefined;
            order_type: InjectiveExchangeV1Beta1Exchange.OrderType;
            trigger_price: string;
        } | undefined;
        '@type': string;
    };
    toEip712V2(): {
        order: any;
        sender: string;
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: {
            order: {
                order_info: {
                    price: string;
                    quantity: string;
                    subaccount_id?: string | undefined;
                    fee_recipient?: string | undefined;
                    cid?: string | undefined;
                };
                trigger_price: string;
                market_id?: string | undefined;
                order_type?: InjectiveExchangeV1Beta1Exchange.OrderType | undefined;
            };
            sender: string;
        };
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgCreateSpotLimitOrder;
    };
    toBinary(): Uint8Array;
}
