#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
持续运行的套利分析主脚本
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from scripts.arbitrage.portal_polygon_bridge.run_arbitrage_finder import main as run_finder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("arb_finder_main")

def main():
    """
    主函数
    """
    logger.info("启动持续运行的套利分析...")
    
    while True:
        try:
            # 运行套利查找器
            run_finder()
            
            # 等待一段时间后再次运行
            wait_time = 30  # 30秒
            logger.info(f"等待 {wait_time} 秒后开始下一轮分析...")
            time.sleep(wait_time)
            
        except KeyboardInterrupt:
            logger.info("用户中断操作")
            break
        except Exception as e:
            logger.error(f"主循环出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # 出错后等待一段时间再继续
            time.sleep(60)

if __name__ == "__main__":
    main() 