/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.distribution.module.v1";
function createBaseModule() {
    return { feeCollectorName: "", authority: "" };
}
export const Module = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.feeCollectorName !== "") {
            writer.uint32(10).string(message.feeCollectorName);
        }
        if (message.authority !== "") {
            writer.uint32(18).string(message.authority);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModule();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feeCollectorName = reader.string();
                    break;
                case 2:
                    message.authority = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            feeCollectorName: isSet(object.feeCollectorName) ? String(object.feeCollectorName) : "",
            authority: isSet(object.authority) ? String(object.authority) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.feeCollectorName !== undefined && (obj.feeCollectorName = message.feeCollectorName);
        message.authority !== undefined && (obj.authority = message.authority);
        return obj;
    },
    create(base) {
        return Module.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModule();
        message.feeCollectorName = object.feeCollectorName ?? "";
        message.authority = object.authority ?? "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
