{"version": 3, "sources": ["../../src/transactions/instances/rotationProofChallenge.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../../bcs/serializer\";\nimport { AccountAddress } from \"../../core/accountAddress\";\nimport { AnyNumber } from \"../../types\";\nimport { PublicKey } from \"../../core/crypto\";\nimport { MoveString, MoveVector, U64, U8 } from \"../../bcs\";\n\n/**\n * Represents a challenge required for the account owner to sign in order to rotate the authentication key.\n * @group Implementation\n * @category Transactions\n */\nexport class RotationProofChallenge extends Serializable {\n  // Resource account address\n  public readonly accountAddress: AccountAddress = AccountAddress.ONE;\n\n  // Module name, i.e: 0x1::account\n  public readonly moduleName: MoveString = new MoveString(\"account\");\n\n  // The rotation proof challenge struct name that live under the module\n  public readonly structName: MoveString = new MoveString(\"RotationProofChallenge\");\n\n  // Signer's address\n  public readonly originator: AccountAddress;\n\n  // Signer's current authentication key\n  public readonly currentAuthKey: AccountAddress;\n\n  // New public key to rotate to\n  public readonly newPublicKey: MoveVector<U8>;\n\n  // Sequence number of the account\n  public readonly sequenceNumber: U64;\n\n  /**\n   * Initializes a new instance of the class with the specified parameters.\n   * This constructor sets up the necessary attributes for managing account keys.\n   *\n   * @param args - The parameters required to create the instance.\n   * @param args.sequenceNumber - The sequence number associated with the transaction.\n   * @param args.originator - The account address of the originator.\n   * @param args.currentAuthKey - The current authentication key of the account.\n   * @param args.newPublicKey - The new public key to be set for the account.\n   * @group Implementation\n   * @category Transactions\n   */\n  constructor(args: {\n    sequenceNumber: AnyNumber;\n    originator: AccountAddress;\n    currentAuthKey: AccountAddress;\n    newPublicKey: PublicKey;\n  }) {\n    super();\n    this.sequenceNumber = new U64(args.sequenceNumber);\n    this.originator = args.originator;\n    this.currentAuthKey = args.currentAuthKey;\n    this.newPublicKey = MoveVector.U8(args.newPublicKey.toUint8Array());\n  }\n\n  /**\n   * Serializes the properties of the current instance for transmission or storage.\n   * This function helps in converting the instance data into a format suitable for serialization.\n   *\n   * @param serializer - The serializer used to serialize the instance properties.\n   * @param serializer.accountAddress - The account address to serialize.\n   * @param serializer.moduleName - The module name to serialize.\n   * @param serializer.structName - The struct name to serialize.\n   * @param serializer.sequenceNumber - The sequence number to serialize.\n   * @param serializer.originator - The originator to serialize.\n   * @param serializer.currentAuthKey - The current authentication key to serialize.\n   * @param serializer.newPublicKey - The new public key to serialize.\n   * @group Implementation\n   * @category Transactions\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serialize(this.accountAddress);\n    serializer.serialize(this.moduleName);\n    serializer.serialize(this.structName);\n    serializer.serialize(this.sequenceNumber);\n    serializer.serialize(this.originator);\n    serializer.serialize(this.currentAuthKey);\n    serializer.serialize(this.newPublicKey);\n  }\n}\n"], "mappings": "2KAcO,IAAMA,EAAN,cAAqCC,CAAa,CAkCvD,YAAYC,EAKT,CACD,MAAM,EAtCR,KAAgB,eAAiCC,EAAe,IAGhE,KAAgB,WAAyB,IAAIC,EAAW,SAAS,EAGjE,KAAgB,WAAyB,IAAIA,EAAW,wBAAwB,EAiC9E,KAAK,eAAiB,IAAIC,EAAIH,EAAK,cAAc,EACjD,KAAK,WAAaA,EAAK,WACvB,KAAK,eAAiBA,EAAK,eAC3B,KAAK,aAAeI,EAAW,GAAGJ,EAAK,aAAa,aAAa,CAAC,CACpE,CAiBA,UAAUK,EAA8B,CACtCA,EAAW,UAAU,KAAK,cAAc,EACxCA,EAAW,UAAU,KAAK,UAAU,EACpCA,EAAW,UAAU,KAAK,UAAU,EACpCA,EAAW,UAAU,KAAK,cAAc,EACxCA,EAAW,UAAU,KAAK,UAAU,EACpCA,EAAW,UAAU,KAAK,cAAc,EACxCA,EAAW,UAAU,KAAK,YAAY,CACxC,CACF", "names": ["RotationProofChallenge", "Serializable", "args", "Account<PERSON><PERSON><PERSON>", "MoveString", "U64", "MoveVector", "serializer"]}