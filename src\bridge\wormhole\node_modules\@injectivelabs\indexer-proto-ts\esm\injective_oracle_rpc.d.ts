import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Observable } from "rxjs";
export declare const protobufPackage = "injective_oracle_rpc";
export interface OracleListRequest {
}
export interface OracleListResponse {
    oracles: Oracle[];
}
export interface Oracle {
    /** The symbol of the oracle asset. */
    symbol: string;
    /** Oracle base currency */
    baseSymbol: string;
    /** Oracle quote currency */
    quoteSymbol: string;
    /** Oracle Type */
    oracleType: string;
    /** The price of the oracle asset */
    price: string;
}
export interface PriceRequest {
    /** Oracle base currency */
    baseSymbol: string;
    /** Oracle quote currency */
    quoteSymbol: string;
    /** Oracle Type */
    oracleType: string;
    /** OracleScaleFactor */
    oracleScaleFactor: number;
}
export interface PriceResponse {
    /** The price of the oracle asset */
    price: string;
}
export interface StreamPricesRequest {
    /** Oracle base currency */
    baseSymbol: string;
    /** Oracle quote currency */
    quoteSymbol: string;
    /** Oracle Type */
    oracleType: string;
}
export interface StreamPricesResponse {
    /** The price of the oracle asset */
    price: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface StreamPricesByMarketsRequest {
    /** marketIDs to stream price for, empty to listen for all prices */
    marketIds: string[];
}
export interface StreamPricesByMarketsResponse {
    /** The price of the oracle asset */
    price: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
    /** marketID that the price has been updated */
    marketId: string;
}
export declare const OracleListRequest: {
    encode(_: OracleListRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OracleListRequest;
    fromJSON(_: any): OracleListRequest;
    toJSON(_: OracleListRequest): unknown;
    create(base?: DeepPartial<OracleListRequest>): OracleListRequest;
    fromPartial(_: DeepPartial<OracleListRequest>): OracleListRequest;
};
export declare const OracleListResponse: {
    encode(message: OracleListResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OracleListResponse;
    fromJSON(object: any): OracleListResponse;
    toJSON(message: OracleListResponse): unknown;
    create(base?: DeepPartial<OracleListResponse>): OracleListResponse;
    fromPartial(object: DeepPartial<OracleListResponse>): OracleListResponse;
};
export declare const Oracle: {
    encode(message: Oracle, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Oracle;
    fromJSON(object: any): Oracle;
    toJSON(message: Oracle): unknown;
    create(base?: DeepPartial<Oracle>): Oracle;
    fromPartial(object: DeepPartial<Oracle>): Oracle;
};
export declare const PriceRequest: {
    encode(message: PriceRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceRequest;
    fromJSON(object: any): PriceRequest;
    toJSON(message: PriceRequest): unknown;
    create(base?: DeepPartial<PriceRequest>): PriceRequest;
    fromPartial(object: DeepPartial<PriceRequest>): PriceRequest;
};
export declare const PriceResponse: {
    encode(message: PriceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceResponse;
    fromJSON(object: any): PriceResponse;
    toJSON(message: PriceResponse): unknown;
    create(base?: DeepPartial<PriceResponse>): PriceResponse;
    fromPartial(object: DeepPartial<PriceResponse>): PriceResponse;
};
export declare const StreamPricesRequest: {
    encode(message: StreamPricesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamPricesRequest;
    fromJSON(object: any): StreamPricesRequest;
    toJSON(message: StreamPricesRequest): unknown;
    create(base?: DeepPartial<StreamPricesRequest>): StreamPricesRequest;
    fromPartial(object: DeepPartial<StreamPricesRequest>): StreamPricesRequest;
};
export declare const StreamPricesResponse: {
    encode(message: StreamPricesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamPricesResponse;
    fromJSON(object: any): StreamPricesResponse;
    toJSON(message: StreamPricesResponse): unknown;
    create(base?: DeepPartial<StreamPricesResponse>): StreamPricesResponse;
    fromPartial(object: DeepPartial<StreamPricesResponse>): StreamPricesResponse;
};
export declare const StreamPricesByMarketsRequest: {
    encode(message: StreamPricesByMarketsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamPricesByMarketsRequest;
    fromJSON(object: any): StreamPricesByMarketsRequest;
    toJSON(message: StreamPricesByMarketsRequest): unknown;
    create(base?: DeepPartial<StreamPricesByMarketsRequest>): StreamPricesByMarketsRequest;
    fromPartial(object: DeepPartial<StreamPricesByMarketsRequest>): StreamPricesByMarketsRequest;
};
export declare const StreamPricesByMarketsResponse: {
    encode(message: StreamPricesByMarketsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamPricesByMarketsResponse;
    fromJSON(object: any): StreamPricesByMarketsResponse;
    toJSON(message: StreamPricesByMarketsResponse): unknown;
    create(base?: DeepPartial<StreamPricesByMarketsResponse>): StreamPricesByMarketsResponse;
    fromPartial(object: DeepPartial<StreamPricesByMarketsResponse>): StreamPricesByMarketsResponse;
};
/** InjectiveOracleRPC defines gRPC API of Exchange Oracle provider. */
export interface InjectiveOracleRPC {
    /** List all oracles */
    OracleList(request: DeepPartial<OracleListRequest>, metadata?: grpc.Metadata): Promise<OracleListResponse>;
    /** Gets the price of the oracle */
    Price(request: DeepPartial<PriceRequest>, metadata?: grpc.Metadata): Promise<PriceResponse>;
    /**
     * StreamPrices streams new price changes for a specified oracle. If no oracles
     * are provided, all price changes are streamed.
     */
    StreamPrices(request: DeepPartial<StreamPricesRequest>, metadata?: grpc.Metadata): Observable<StreamPricesResponse>;
    /** StreamPrices streams new price changes markets */
    StreamPricesByMarkets(request: DeepPartial<StreamPricesByMarketsRequest>, metadata?: grpc.Metadata): Observable<StreamPricesByMarketsResponse>;
}
export declare class InjectiveOracleRPCClientImpl implements InjectiveOracleRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    OracleList(request: DeepPartial<OracleListRequest>, metadata?: grpc.Metadata): Promise<OracleListResponse>;
    Price(request: DeepPartial<PriceRequest>, metadata?: grpc.Metadata): Promise<PriceResponse>;
    StreamPrices(request: DeepPartial<StreamPricesRequest>, metadata?: grpc.Metadata): Observable<StreamPricesResponse>;
    StreamPricesByMarkets(request: DeepPartial<StreamPricesByMarketsRequest>, metadata?: grpc.Metadata): Observable<StreamPricesByMarketsResponse>;
}
export declare const InjectiveOracleRPCDesc: {
    serviceName: string;
};
export declare const InjectiveOracleRPCOracleListDesc: UnaryMethodDefinitionish;
export declare const InjectiveOracleRPCPriceDesc: UnaryMethodDefinitionish;
export declare const InjectiveOracleRPCStreamPricesDesc: UnaryMethodDefinitionish;
export declare const InjectiveOracleRPCStreamPricesByMarketsDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        streamingTransport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
