import { toUtf8 } from '../../../utils/index.js';
export class SwapQueryTransformer {
    static contractRouteResponseToContractRoute(response) {
        const data = JSON.parse(toUtf8(response.data));
        return {
            steps: data.steps,
            sourceDenom: data.source_denom,
            targetDenom: data.target_denom,
        };
    }
    static contractAllRoutesResponseToContractAllRoutes(response) {
        const data = JSON.parse(toUtf8(response.data));
        return data.map((route) => ({
            steps: route.steps,
            sourceDenom: route.source_denom,
            targetDenom: route.target_denom,
        }));
    }
    static contractQuantityResponseToContractQuantity(response) {
        const data = JSON.parse(toUtf8(response.data));
        return {
            expectedFees: data.expected_fees,
            resultQuantity: data.result_quantity,
        };
    }
}
