import { GoogleProtobufAny, CosmosAuthzV1Beta1Authz } from '@injectivelabs/core-proto-ts';
import { BaseAuthorization } from './Base.js';
export declare namespace GenericAuthorization {
    interface Params {
        messageTypeUrl?: string;
        authorization?: GoogleProtobufAny.Any;
    }
    type Any = GoogleProtobufAny.Any;
    type Proto = CosmosAuthzV1Beta1Authz.GenericAuthorization;
    type Amino = Object;
}
/**
 * @category Contract Exec Arguments
 */
export default class GenericAuthorization extends BaseAuthorization<GenericAuthorization.Params, GenericAuthorization.Proto, GenericAuthorization.Amino> {
    static fromJSON(params: GenericAuthorization.Params): GenericAuthorization;
    toProto(): GenericAuthorization.Proto;
    toAmino(): GenericAuthorization.Amino;
    toWeb3(): GenericAuthorization.Amino;
    toAny(): GoogleProtobufAny.Any;
}
