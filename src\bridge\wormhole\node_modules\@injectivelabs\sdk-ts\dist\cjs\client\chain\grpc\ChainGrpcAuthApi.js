"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcAuthApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const pagination_js_1 = require("../../../utils/pagination.js");
const ChainGrpcAuthTransformer_js_1 = require("../transformers/ChainGrpcAuthTransformer.js");
/**
 * @category Chain Grpc API
 */
class ChainGrpc<PERSON>uth<PERSON><PERSON> extends BaseGrpcConsumer_js_1.default {
    module = index_js_1.ChainModule.Auth;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new core_proto_ts_1.CosmosAuthV1Beta1Query.QueryClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchModuleParams() {
        const request = core_proto_ts_1.CosmosAuthV1Beta1Query.QueryParamsRequest.create();
        try {
            const response = await this.retry(() => this.client.Params(request, this.metadata));
            return ChainGrpcAuthTransformer_js_1.ChainGrpcAuthTransformer.moduleParamsResponseToModuleParams(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosAuthV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Params',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Params',
                contextModule: this.module,
            });
        }
    }
    async fetchAccount(address) {
        const request = core_proto_ts_1.CosmosAuthV1Beta1Query.QueryAccountRequest.create();
        request.address = address;
        try {
            const response = await this.retry(() => this.client.Account(request, this.metadata));
            return ChainGrpcAuthTransformer_js_1.ChainGrpcAuthTransformer.accountResponseToAccount(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosAuthV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Account',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Account',
                contextModule: this.module,
            });
        }
    }
    async fetchAccounts(pagination) {
        const request = core_proto_ts_1.CosmosAuthV1Beta1Query.QueryAccountsRequest.create();
        const paginationForRequest = (0, pagination_js_1.paginationRequestFromPagination)(pagination);
        if (paginationForRequest) {
            request.pagination = paginationForRequest;
        }
        try {
            const response = await this.retry(() => this.client.Accounts(request, this.metadata));
            return ChainGrpcAuthTransformer_js_1.ChainGrpcAuthTransformer.accountsResponseToAccounts(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosAuthV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Accounts',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Accounts',
                contextModule: this.module,
            });
        }
    }
}
exports.ChainGrpcAuthApi = ChainGrpcAuthApi;
