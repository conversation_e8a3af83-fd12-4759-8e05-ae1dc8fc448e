"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const numbers_js_1 = require("../../../../utils/numbers.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const createLimitOrder = (params) => {
    const orderInfo = core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
    orderInfo.subaccountId = params.subaccountId;
    orderInfo.feeRecipient = params.feeRecipient;
    orderInfo.price = params.price;
    orderInfo.quantity = params.quantity;
    if (params.cid) {
        orderInfo.cid = params.cid;
    }
    const derivativeOrder = core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.DerivativeOrder.create();
    derivativeOrder.marketId = params.marketId;
    derivativeOrder.orderInfo = orderInfo;
    derivativeOrder.orderType = params.orderType;
    derivativeOrder.margin = params.margin;
    derivativeOrder.triggerPrice = params.triggerPrice || '0';
    const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgCreateDerivativeLimitOrder.create();
    message.sender = params.injectiveAddress;
    message.order = derivativeOrder;
    return message;
};
/**
 * @category Messages
 */
class MsgCreateDerivativeLimitOrder extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgCreateDerivativeLimitOrder(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            price: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.price).toFixed(),
            margin: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.margin).toFixed(),
            triggerPrice: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.triggerPrice || 0).toFixed(),
            quantity: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.quantity).toFixed(),
        };
        return createLimitOrder(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgCreateDerivativeLimitOrder',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const order = createLimitOrder(params);
        const message = {
            ...(0, snakecase_keys_1.default)(order),
        };
        return {
            type: 'exchange/MsgCreateDerivativeLimitOrder',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgCreateDerivativeLimitOrder',
            ...value,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const order = web3gw.order;
        const messageAdjusted = {
            ...web3gw,
            order: {
                ...order,
                order_info: {
                    ...order.order_info,
                    price: (0, numbers_js_1.numberToCosmosSdkDecString)(params.price),
                    quantity: (0, numbers_js_1.numberToCosmosSdkDecString)(params.quantity),
                },
                margin: (0, numbers_js_1.numberToCosmosSdkDecString)(params.margin),
                trigger_price: (0, numbers_js_1.numberToCosmosSdkDecString)(params.triggerPrice || '0'),
                order_type: core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.orderTypeToJSON(params.orderType),
            },
        };
        return messageAdjusted;
    }
    toEip712() {
        const { params } = this;
        const amino = this.toAmino();
        const { value, type } = amino;
        const messageAdjusted = {
            ...value,
            order: {
                ...value.order,
                order_info: {
                    ...value.order?.order_info,
                    price: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.price).toFixed(),
                    quantity: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.quantity).toFixed(),
                },
                margin: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.margin).toFixed(),
                trigger_price: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.triggerPrice || '0').toFixed(),
            },
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgCreateDerivativeLimitOrder',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgCreateDerivativeLimitOrder.encode(this.toProto()).finish();
    }
}
exports.default = MsgCreateDerivativeLimitOrder;
