"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcPermissionsTransformer = void 0;
const ChainGrpcCommonTransformer_js_1 = require("./ChainGrpcCommonTransformer.js");
/**
 * @category Chain Grpc Transformer
 */
class ChainGrpcPermissionsTransformer {
    static grpcRoleToRole(grpcRole) {
        return {
            name: grpcRole.name,
            roleId: grpcRole.roleId,
            permissions: grpcRole.permissions,
        };
    }
    static grpcActorRolesToActorRoles(grpcActorRoles) {
        return {
            actor: grpcActorRoles.actor,
            roles: grpcActorRoles.roles,
        };
    }
    static grpcRoleManagerToRoleManager(grpcRoleManager) {
        return {
            roles: grpcRoleManager.roles,
            manager: grpcRoleManager.manager,
        };
    }
    static grpcPolicyStatusToPolicyStatus(grpcPolicyStatus) {
        return {
            action: grpcPolicyStatus.action,
            isSealed: grpcPolicyStatus.isSealed,
            isDisabled: grpcPolicyStatus.isDisabled,
        };
    }
    static grpcPolicyManagerCapabilityToPolicyManagerCapability(grpcPolicyManagerCapability) {
        return {
            action: grpcPolicyManagerCapability.action,
            canSeal: grpcPolicyManagerCapability.canSeal,
            manager: grpcPolicyManagerCapability.manager,
            canDisable: grpcPolicyManagerCapability.canDisable,
        };
    }
    static grpcAddressVoucherToAddressVoucher(grpcAddressVoucher) {
        return {
            address: grpcAddressVoucher.address,
            voucher: grpcAddressVoucher.voucher
                ? ChainGrpcCommonTransformer_js_1.ChainGrpcCommonTransformer.grpcCoinToCoin(grpcAddressVoucher.voucher)
                : undefined,
        };
    }
    static grpcNamespaceToNamespace(grpcNamespace) {
        return {
            denom: grpcNamespace.denom,
            contractHook: grpcNamespace.contractHook,
            rolePermissions: grpcNamespace.rolePermissions.map(ChainGrpcPermissionsTransformer.grpcRoleToRole),
            actorRoles: grpcNamespace.actorRoles.map(ChainGrpcPermissionsTransformer.grpcActorRolesToActorRoles),
            roleManagers: grpcNamespace.roleManagers.map(ChainGrpcPermissionsTransformer.grpcRoleManagerToRoleManager),
            policyStatuses: grpcNamespace.policyStatuses.map(ChainGrpcPermissionsTransformer.grpcPolicyStatusToPolicyStatus),
            policyManagerCapabilities: grpcNamespace.policyManagerCapabilities.map(ChainGrpcPermissionsTransformer.grpcPolicyManagerCapabilityToPolicyManagerCapability),
        };
    }
    static moduleParamsResponseToModuleParams(response) {
        const params = response.params;
        return {
            wasmHookQueryMaxGas: params.wasmHookQueryMaxGas,
        };
    }
    static nameSpaceDenomsResponseToNameSpaceDenoms(response) {
        return response.denoms.map((denom) => denom);
    }
    static namespaceResponseToNamespaces(response) {
        if (!response.namespace) {
            return;
        }
        return ChainGrpcPermissionsTransformer.grpcNamespaceToNamespace(response.namespace);
    }
    static namespacesResponseToNamespaces(response) {
        return response.namespaces.map((namespace) => {
            ChainGrpcPermissionsTransformer.grpcNamespaceToNamespace(namespace);
        });
    }
    static actorsByRoleResponseToActorsByRole(response) {
        return response.actors.map((role) => {
            return {
                roles: role,
            };
        });
    }
    static rolesByActorResponseToRolesByActor(response) {
        return response.roles.map((role) => {
            return {
                roles: role,
            };
        });
    }
    static roleManagerResponseToRoleManager(response) {
        if (!response.roleManager) {
            return;
        }
        return ChainGrpcPermissionsTransformer.grpcRoleManagerToRoleManager(response.roleManager);
    }
    static roleManagersResponseToRoleManagers(response) {
        return response.roleManagers.map(ChainGrpcPermissionsTransformer.grpcRoleManagerToRoleManager);
    }
    static policyStatusesResponseToPolicyStatuses(response) {
        return response.policyStatuses.map(ChainGrpcPermissionsTransformer.grpcPolicyStatusToPolicyStatus);
    }
    static policyManagerCapabilitiesResponseToPolicyManagerCapabilities(response) {
        return response.policyManagerCapabilities.map(ChainGrpcPermissionsTransformer.grpcPolicyManagerCapabilityToPolicyManagerCapability);
    }
    static voucherResponseToVoucher(response) {
        if (!response.voucher) {
            return;
        }
        return ChainGrpcCommonTransformer_js_1.ChainGrpcCommonTransformer.grpcCoinToCoin(response.voucher);
    }
    static vouchersResponseToVouchers(response) {
        return response.vouchers.map(ChainGrpcPermissionsTransformer.grpcAddressVoucherToAddressVoucher);
    }
    static moduleStateResponseToModuleState(response) {
        if (!response.state) {
            return;
        }
        return {
            params: ChainGrpcPermissionsTransformer.moduleParamsResponseToModuleParams(response.state),
            namespaces: response.state.namespaces.map(ChainGrpcPermissionsTransformer.grpcNamespaceToNamespace),
            vouchers: response.state.vouchers.map(ChainGrpcPermissionsTransformer.grpcAddressVoucherToAddressVoucher),
        };
    }
}
exports.ChainGrpcPermissionsTransformer = ChainGrpcPermissionsTransformer;
