import{b as a}from"./chunk-7DQDJ2SA.mjs";var l=(t=>(t[t.API_ERROR=0]="API_ERROR",t[t.EXTERNAL_API_ERROR=1]="EXTERNAL_API_ERROR",t[t.SESSION_EXPIRED=2]="SESSION_EXPIRED",t[t.INVALID_STATE=3]="INVALID_STATE",t[t.INVALID_SIGNATURE=4]="INVALID_SIGNATURE",t[t.UNKNOWN=5]="UNKNOWN",t))(l||{}),O=(s=>(s.REAUTHENTICATE="Re-authentiate to continue using your keyless account",s.REAUTHENTICATE_UNSURE="Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support",s.UPDATE_REQUEST_PARAMS="Update the invalid request parameters and reauthenticate.",s.RATE_LIMIT_EXCEEDED="Cache the keyless account and reuse it to avoid making too many requests.  Keyless accounts are valid until either the EphemeralKeyPair expires, when the JWK is rotated, or when the proof verifying key is changed, whichever comes soonest.",s.SERVER_ERROR="Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx",s.CALL_PRECHECK="Call `await account.checkKeylessAccountValidity()` to wait for asyncronous changes and check for account validity before signing or serializing.",s.REINSTANTIATE="Try instantiating the account again.  Avoid manipulating the account object directly",s.JOIN_SUPPORT_GROUP="For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx",s.UNKNOWN="Error unknown. For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx",s))(O||{}),u=(e=>(e[e.EPHEMERAL_KEY_PAIR_EXPIRED=0]="EPHEMERAL_KEY_PAIR_EXPIRED",e[e.PROOF_NOT_FOUND=1]="PROOF_NOT_FOUND",e[e.ASYNC_PROOF_FETCH_FAILED=2]="ASYNC_PROOF_FETCH_FAILED",e[e.INVALID_PROOF_VERIFICATION_FAILED=3]="INVALID_PROOF_VERIFICATION_FAILED",e[e.INVALID_PROOF_VERIFICATION_KEY_NOT_FOUND=4]="INVALID_PROOF_VERIFICATION_KEY_NOT_FOUND",e[e.INVALID_JWT_SIG=5]="INVALID_JWT_SIG",e[e.INVALID_JWT_JWK_NOT_FOUND=6]="INVALID_JWT_JWK_NOT_FOUND",e[e.INVALID_JWT_ISS_NOT_RECOGNIZED=7]="INVALID_JWT_ISS_NOT_RECOGNIZED",e[e.INVALID_JWT_FEDERATED_ISS_NOT_SUPPORTED=8]="INVALID_JWT_FEDERATED_ISS_NOT_SUPPORTED",e[e.INVALID_TW_SIG_VERIFICATION_FAILED=9]="INVALID_TW_SIG_VERIFICATION_FAILED",e[e.INVALID_TW_SIG_PUBLIC_KEY_NOT_FOUND=10]="INVALID_TW_SIG_PUBLIC_KEY_NOT_FOUND",e[e.INVALID_EXPIRY_HORIZON=11]="INVALID_EXPIRY_HORIZON",e[e.JWT_PARSING_ERROR=12]="JWT_PARSING_ERROR",e[e.JWK_FETCH_FAILED=13]="JWK_FETCH_FAILED",e[e.JWK_FETCH_FAILED_FEDERATED=14]="JWK_FETCH_FAILED_FEDERATED",e[e.RATE_LIMIT_EXCEEDED=15]="RATE_LIMIT_EXCEEDED",e[e.PEPPER_SERVICE_INTERNAL_ERROR=16]="PEPPER_SERVICE_INTERNAL_ERROR",e[e.PEPPER_SERVICE_BAD_REQUEST=17]="PEPPER_SERVICE_BAD_REQUEST",e[e.PEPPER_SERVICE_OTHER=18]="PEPPER_SERVICE_OTHER",e[e.PROVER_SERVICE_INTERNAL_ERROR=19]="PROVER_SERVICE_INTERNAL_ERROR",e[e.PROVER_SERVICE_BAD_REQUEST=20]="PROVER_SERVICE_BAD_REQUEST",e[e.PROVER_SERVICE_OTHER=21]="PROVER_SERVICE_OTHER",e[e.FULL_NODE_CONFIG_LOOKUP_ERROR=22]="FULL_NODE_CONFIG_LOOKUP_ERROR",e[e.FULL_NODE_VERIFICATION_KEY_LOOKUP_ERROR=23]="FULL_NODE_VERIFICATION_KEY_LOOKUP_ERROR",e[e.FULL_NODE_JWKS_LOOKUP_ERROR=24]="FULL_NODE_JWKS_LOOKUP_ERROR",e[e.FULL_NODE_OTHER=25]="FULL_NODE_OTHER",e[e.SIGNATURE_TYPE_INVALID=26]="SIGNATURE_TYPE_INVALID",e[e.SIGNATURE_EXPIRED=27]="SIGNATURE_EXPIRED",e[e.MAX_EXPIRY_HORIZON_EXCEEDED=28]="MAX_EXPIRY_HORIZON_EXCEEDED",e[e.EPHEMERAL_SIGNATURE_VERIFICATION_FAILED=29]="EPHEMERAL_SIGNATURE_VERIFICATION_FAILED",e[e.TRAINING_WHEELS_SIGNATURE_MISSING=30]="TRAINING_WHEELS_SIGNATURE_MISSING",e[e.TRAINING_WHEELS_SIGNATURE_VERIFICATION_FAILED=31]="TRAINING_WHEELS_SIGNATURE_VERIFICATION_FAILED",e[e.PROOF_VERIFICATION_FAILED=32]="PROOF_VERIFICATION_FAILED",e[e.UNKNOWN=33]="UNKNOWN",e))(u||{}),A={0:["The ephemeral keypair has expired.",2,"Re-authentiate to continue using your keyless account"],1:["The required proof could not be found.",3,"Call `await account.checkKeylessAccountValidity()` to wait for asyncronous changes and check for account validity before signing or serializing."],2:["The required proof failed to fetch.",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],3:["The provided proof is invalid.",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],4:["The verification key used to authenticate was updated.",2,"Re-authentiate to continue using your keyless account"],5:["The JWK was found, but JWT failed verification",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],6:["The JWK required to verify the JWT could not be found. The JWK may have been rotated out.",2,"Re-authentiate to continue using your keyless account"],7:["The JWT issuer is not recognized.",3,"Update the invalid request parameters and reauthenticate."],8:["The JWT issuer is not supported by the Federated Keyless ",0,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],9:["The training wheels signature is invalid.",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],10:["The public key used to verify the training wheels signature was not found.",2,"Re-authentiate to continue using your keyless account"],11:["The expiry horizon is invalid.",2,"Re-authentiate to continue using your keyless account"],13:["Failed to fetch JWKS.",1,"For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],14:["Failed to fetch JWKS for Federated Keyless provider.",1,"For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],15:["Rate limit exceeded. Too many requests in a short period.",0,"Cache the keyless account and reuse it to avoid making too many requests.  Keyless accounts are valid until either the EphemeralKeyPair expires, when the JWK is rotated, or when the proof verifying key is changed, whichever comes soonest."],16:["Internal error from Pepper service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],17:["Bad request sent to Pepper service.",0,"Update the invalid request parameters and reauthenticate."],18:["Unknown error from Pepper service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],19:["Internal error from Prover service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],20:["Bad request sent to Prover service.",0,"Update the invalid request parameters and reauthenticate."],21:["Unknown error from Prover service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],12:["Error when parsing JWT. This should never happen. Join https://t.me/+h5CN-W35yUFiYzkx for support",3,"Try instantiating the account again.  Avoid manipulating the account object directly"],22:["Error when looking up on-chain keyless configuration.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],23:["Error when looking up on-chain verification key.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],24:["Error when looking up on-chain JWKS.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],25:["Unknown error from full node.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],26:["The signature is not a valid Keyless signature.",4,"For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],27:["The ephemeral key pair used to sign the message has expired.",4,"Re-authentiate to continue using your keyless account"],28:["The expiry horizon on the signature exceeds the maximum allowed value.",4,"Re-authentiate to continue using your keyless account"],29:["Failed to verify the ephemeral signature with the ephemeral public key.",4,"Re-authentiate to continue using your keyless account"],30:["The training wheels signature is missing but is required by the Keyless configuration.",4,"Re-authentiate to continue using your keyless account"],31:["Failed to verify the training wheels signature with the training wheels public key.",4,"Re-authentiate to continue using your keyless account"],32:["The proof verification failed.",4,"Re-authentiate to continue using your keyless account"],33:["An unknown error has occurred.",5,"Error unknown. For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"]},N=class _ extends Error{constructor(o){let{innerError:r,category:E,resolutionTip:i,type:R,message:t=A[R][0],details:I}=o;super(t),this.name="KeylessError",this.innerError=r,this.category=E,this.resolutionTip=i,this.type=R,this.details=I,this.message=_.constructMessage(t,i,r,I)}static constructMessage(o,r,E,i){let R=`
Message: ${o}`;return i&&(R+=`
Details: ${i}`),E instanceof T?R+=`
AptosApiError: ${E.message}`:E!==void 0&&(R+=`
Error: ${a(E)}`),R+=`
KeylessErrorResolutionTip: ${r}`,R}static fromErrorType(o){let{error:r,type:E,details:i}=o,[R,t,I]=A[E];return new _({message:R,details:i,innerError:r,category:t,resolutionTip:I,type:E})}},T=class extends Error{constructor({apiType:o,aptosRequest:r,aptosResponse:E}){super(S({apiType:o,aptosRequest:r,aptosResponse:E})),this.name="AptosApiError",this.url=E.url,this.status=E.status,this.statusText=E.statusText,this.data=E.data,this.request=r}};function S({apiType:_,aptosRequest:o,aptosResponse:r}){let E=r.headers?.traceparent?.split("-")[1],i=E?`(trace_id:${E}) `:"",R=`Request to [${_}]: ${o.method} ${r.url??o.url} ${i}failed with`;return _==="Indexer"&&r.data?.errors?.[0]?.message!=null?`${R}: ${r.data.errors[0].message}`:r.data?.message!=null&&r.data?.error_code!=null?`${R}: ${JSON.stringify(r.data)}`:`${R} status: ${r.statusText}(code:${r.status}) and response body: ${P(r.data)}`}var n=400;function P(_){let o=JSON.stringify(_);return o.length<=n?o:`truncated(original_size:${o.length}): ${o.slice(0,n/2)}...${o.slice(-n/2)}`}export{l as a,O as b,u as c,N as d,T as e};
//# sourceMappingURL=chunk-6WDVDEQZ.mjs.map