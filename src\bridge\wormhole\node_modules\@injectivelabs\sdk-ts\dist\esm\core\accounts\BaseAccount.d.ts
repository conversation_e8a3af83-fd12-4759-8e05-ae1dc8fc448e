import { Address } from './Address.js';
import { AccountResponse, BaseAccountRestResponse } from '../../client/chain/index.js';
import { AccountDetails } from '../../types/auth.js';
/**
 * @category Utility Classes
 */
export declare class BaseAccount extends Address {
    accountNumber: number;
    sequence: number;
    pubKey: {
        type: string;
        key: string;
    };
    constructor({ address, accountNumber, sequence, pubKey, }: {
        address: string;
        accountNumber: number;
        sequence: number;
        pubKey: {
            type: string;
            key: string;
        };
    });
    static fromRestApi(accountResponse: AccountResponse): BaseAccount;
    static fromRestCosmosApi(accountResponse: BaseAccountRestResponse): BaseAccount;
    incrementSequence(): this;
    toAccountDetails(): AccountDetails;
}
