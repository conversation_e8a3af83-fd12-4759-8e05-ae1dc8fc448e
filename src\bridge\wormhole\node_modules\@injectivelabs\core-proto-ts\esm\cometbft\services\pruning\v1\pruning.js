/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cometbft.services.pruning.v1";
function createBaseSetBlockRetainHeightRequest() {
    return { height: "0" };
}
export const SetBlockRetainHeightRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetBlockRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create(base) {
        return SetBlockRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSetBlockRetainHeightRequest();
        message.height = object.height ?? "0";
        return message;
    },
};
function createBaseSetBlockRetainHeightResponse() {
    return {};
}
export const SetBlockRetainHeightResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetBlockRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return SetBlockRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseSetBlockRetainHeightResponse();
        return message;
    },
};
function createBaseGetBlockRetainHeightRequest() {
    return {};
}
export const GetBlockRetainHeightRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return GetBlockRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseGetBlockRetainHeightRequest();
        return message;
    },
};
function createBaseGetBlockRetainHeightResponse() {
    return { appRetainHeight: "0", pruningServiceRetainHeight: "0" };
}
export const GetBlockRetainHeightResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.appRetainHeight !== "0") {
            writer.uint32(8).uint64(message.appRetainHeight);
        }
        if (message.pruningServiceRetainHeight !== "0") {
            writer.uint32(16).uint64(message.pruningServiceRetainHeight);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.appRetainHeight = longToString(reader.uint64());
                    break;
                case 2:
                    message.pruningServiceRetainHeight = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            appRetainHeight: isSet(object.appRetainHeight) ? String(object.appRetainHeight) : "0",
            pruningServiceRetainHeight: isSet(object.pruningServiceRetainHeight)
                ? String(object.pruningServiceRetainHeight)
                : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.appRetainHeight !== undefined && (obj.appRetainHeight = message.appRetainHeight);
        message.pruningServiceRetainHeight !== undefined &&
            (obj.pruningServiceRetainHeight = message.pruningServiceRetainHeight);
        return obj;
    },
    create(base) {
        return GetBlockRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetBlockRetainHeightResponse();
        message.appRetainHeight = object.appRetainHeight ?? "0";
        message.pruningServiceRetainHeight = object.pruningServiceRetainHeight ?? "0";
        return message;
    },
};
function createBaseSetBlockResultsRetainHeightRequest() {
    return { height: "0" };
}
export const SetBlockResultsRetainHeightRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetBlockResultsRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create(base) {
        return SetBlockResultsRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSetBlockResultsRetainHeightRequest();
        message.height = object.height ?? "0";
        return message;
    },
};
function createBaseSetBlockResultsRetainHeightResponse() {
    return {};
}
export const SetBlockResultsRetainHeightResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetBlockResultsRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return SetBlockResultsRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseSetBlockResultsRetainHeightResponse();
        return message;
    },
};
function createBaseGetBlockResultsRetainHeightRequest() {
    return {};
}
export const GetBlockResultsRetainHeightRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockResultsRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return GetBlockResultsRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseGetBlockResultsRetainHeightRequest();
        return message;
    },
};
function createBaseGetBlockResultsRetainHeightResponse() {
    return { pruningServiceRetainHeight: "0" };
}
export const GetBlockResultsRetainHeightResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.pruningServiceRetainHeight !== "0") {
            writer.uint32(8).uint64(message.pruningServiceRetainHeight);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockResultsRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pruningServiceRetainHeight = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            pruningServiceRetainHeight: isSet(object.pruningServiceRetainHeight)
                ? String(object.pruningServiceRetainHeight)
                : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.pruningServiceRetainHeight !== undefined &&
            (obj.pruningServiceRetainHeight = message.pruningServiceRetainHeight);
        return obj;
    },
    create(base) {
        return GetBlockResultsRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetBlockResultsRetainHeightResponse();
        message.pruningServiceRetainHeight = object.pruningServiceRetainHeight ?? "0";
        return message;
    },
};
function createBaseSetTxIndexerRetainHeightRequest() {
    return { height: "0" };
}
export const SetTxIndexerRetainHeightRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetTxIndexerRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create(base) {
        return SetTxIndexerRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSetTxIndexerRetainHeightRequest();
        message.height = object.height ?? "0";
        return message;
    },
};
function createBaseSetTxIndexerRetainHeightResponse() {
    return {};
}
export const SetTxIndexerRetainHeightResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetTxIndexerRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return SetTxIndexerRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseSetTxIndexerRetainHeightResponse();
        return message;
    },
};
function createBaseGetTxIndexerRetainHeightRequest() {
    return {};
}
export const GetTxIndexerRetainHeightRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetTxIndexerRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return GetTxIndexerRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseGetTxIndexerRetainHeightRequest();
        return message;
    },
};
function createBaseGetTxIndexerRetainHeightResponse() {
    return { height: "0" };
}
export const GetTxIndexerRetainHeightResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetTxIndexerRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create(base) {
        return GetTxIndexerRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetTxIndexerRetainHeightResponse();
        message.height = object.height ?? "0";
        return message;
    },
};
function createBaseSetBlockIndexerRetainHeightRequest() {
    return { height: "0" };
}
export const SetBlockIndexerRetainHeightRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetBlockIndexerRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create(base) {
        return SetBlockIndexerRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSetBlockIndexerRetainHeightRequest();
        message.height = object.height ?? "0";
        return message;
    },
};
function createBaseSetBlockIndexerRetainHeightResponse() {
    return {};
}
export const SetBlockIndexerRetainHeightResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSetBlockIndexerRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return SetBlockIndexerRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseSetBlockIndexerRetainHeightResponse();
        return message;
    },
};
function createBaseGetBlockIndexerRetainHeightRequest() {
    return {};
}
export const GetBlockIndexerRetainHeightRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockIndexerRetainHeightRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return GetBlockIndexerRetainHeightRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseGetBlockIndexerRetainHeightRequest();
        return message;
    },
};
function createBaseGetBlockIndexerRetainHeightResponse() {
    return { height: "0" };
}
export const GetBlockIndexerRetainHeightResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockIndexerRetainHeightResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create(base) {
        return GetBlockIndexerRetainHeightResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetBlockIndexerRetainHeightResponse();
        message.height = object.height ?? "0";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
