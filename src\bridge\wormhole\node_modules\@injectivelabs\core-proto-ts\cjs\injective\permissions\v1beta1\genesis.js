"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var params_1 = require("./params.js");
var permissions_1 = require("./permissions.js");
exports.protobufPackage = "injective.permissions.v1beta1";
function createBaseGenesisState() {
    return { params: undefined, namespaces: [], vouchers: [] };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            params_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _c = __values(message.namespaces), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                permissions_1.Namespace.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(message.vouchers), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                permissions_1.AddressVoucher.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = params_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.namespaces.push(permissions_1.Namespace.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.vouchers.push(permissions_1.AddressVoucher.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? params_1.Params.fromJSON(object.params) : undefined,
            namespaces: Array.isArray(object === null || object === void 0 ? void 0 : object.namespaces) ? object.namespaces.map(function (e) { return permissions_1.Namespace.fromJSON(e); }) : [],
            vouchers: Array.isArray(object === null || object === void 0 ? void 0 : object.vouchers) ? object.vouchers.map(function (e) { return permissions_1.AddressVoucher.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? params_1.Params.toJSON(message.params) : undefined);
        if (message.namespaces) {
            obj.namespaces = message.namespaces.map(function (e) { return e ? permissions_1.Namespace.toJSON(e) : undefined; });
        }
        else {
            obj.namespaces = [];
        }
        if (message.vouchers) {
            obj.vouchers = message.vouchers.map(function (e) { return e ? permissions_1.AddressVoucher.toJSON(e) : undefined; });
        }
        else {
            obj.vouchers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? params_1.Params.fromPartial(object.params)
            : undefined;
        message.namespaces = ((_a = object.namespaces) === null || _a === void 0 ? void 0 : _a.map(function (e) { return permissions_1.Namespace.fromPartial(e); })) || [];
        message.vouchers = ((_b = object.vouchers) === null || _b === void 0 ? void 0 : _b.map(function (e) { return permissions_1.AddressVoucher.fromPartial(e); })) || [];
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
