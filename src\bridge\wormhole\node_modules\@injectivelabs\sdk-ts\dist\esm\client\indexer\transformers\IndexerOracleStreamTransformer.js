import { StreamOperation } from '../../../types/index.js';
/**
 * @category Indexer Stream Transformer
 */
export class IndexerOracleStreamTransformer {
    static pricesStreamCallback = (response) => ({
        price: response.price,
        operation: StreamOperation.Update,
        timestamp: parseInt(response.timestamp, 10),
    });
    static pricesByMarketsCallback = (response) => ({
        ...response,
    });
}
