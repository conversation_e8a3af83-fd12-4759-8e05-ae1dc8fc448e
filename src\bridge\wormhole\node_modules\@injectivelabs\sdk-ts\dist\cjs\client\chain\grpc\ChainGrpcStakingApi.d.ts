import { CosmosStakingV1Beta1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcStakingApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: CosmosStakingV1Beta1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchModuleParams(): Promise<import("../types/staking.js").StakingModuleParams>;
    fetchPool(): Promise<import("../types/staking.js").Pool>;
    fetchValidators(pagination?: PaginationOption): Promise<{
        validators: import("../types/staking.js").Validator[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchValidator(address: string): Promise<import("../types/staking.js").Validator>;
    fetchValidatorDelegations({ validatorAddress, pagination, }: {
        validatorAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        delegations: import("../types/staking.js").Delegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchValidatorDelegationsNoThrow({ validatorAddress, pagination, }: {
        validatorAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        delegations: import("../types/staking.js").Delegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchValidatorUnbondingDelegations({ validatorAddress, pagination, }: {
        validatorAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        unbondingDelegations: import("../types/staking.js").UnBondingDelegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchValidatorUnbondingDelegationsNoThrow({ validatorAddress, pagination, }: {
        validatorAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        unbondingDelegations: import("../types/staking.js").UnBondingDelegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchDelegation({ injectiveAddress, validatorAddress, }: {
        injectiveAddress: string;
        validatorAddress: string;
    }): Promise<import("../types/staking.js").Delegation>;
    fetchDelegations({ injectiveAddress, pagination, }: {
        injectiveAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        delegations: import("../types/staking.js").Delegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchDelegationsNoThrow({ injectiveAddress, pagination, }: {
        injectiveAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        delegations: import("../types/staking.js").Delegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchDelegators({ validatorAddress, pagination, }: {
        validatorAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        delegations: import("../types/staking.js").Delegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchDelegatorsNoThrow({ validatorAddress, pagination, }: {
        validatorAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        delegations: import("../types/staking.js").Delegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchUnbondingDelegations({ injectiveAddress, pagination, }: {
        injectiveAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        unbondingDelegations: import("../types/staking.js").UnBondingDelegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchUnbondingDelegationsNoThrow({ injectiveAddress, pagination, }: {
        injectiveAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        unbondingDelegations: import("../types/staking.js").UnBondingDelegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchReDelegations({ injectiveAddress, pagination, }: {
        injectiveAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        redelegations: import("../types/staking.js").ReDelegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchReDelegationsNoThrow({ injectiveAddress, pagination, }: {
        injectiveAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        redelegations: import("../types/staking.js").ReDelegation[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
}
