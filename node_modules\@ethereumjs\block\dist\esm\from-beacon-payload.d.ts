import type { ExecutionPayload } from './types.js';
import type { PrefixedHexString, VerkleExecutionWitness } from '@ethereumjs/util';
declare type BeaconWithdrawal = {
    index: PrefixedHexString;
    validator_index: PrefixedHexString;
    address: PrefixedHexString;
    amount: PrefixedHexString;
};
declare type BeaconDepositRequest = {
    pubkey: PrefixedHexString;
    withdrawal_credentials: PrefixedHexString;
    amount: PrefixedHexString;
    signature: PrefixedHexString;
    index: PrefixedHexString;
};
declare type BeaconWithdrawalRequest = {
    source_address: PrefixedHexString;
    validator_pubkey: PrefixedHexString;
    amount: PrefixedHexString;
};
declare type BeaconConsolidationRequest = {
    source_address: PrefixedHexString;
    source_pubkey: PrefixedHexString;
    target_pubkey: PrefixedHexString;
};
export declare type BeaconPayloadJson = {
    parent_hash: PrefixedHexString;
    fee_recipient: PrefixedHexString;
    state_root: PrefixedHexString;
    receipts_root: PrefixedHexString;
    logs_bloom: PrefixedHexString;
    prev_randao: PrefixedHexString;
    block_number: PrefixedHexString;
    gas_limit: PrefixedHexString;
    gas_used: PrefixedHexString;
    timestamp: PrefixedHexString;
    extra_data: PrefixedHexString;
    base_fee_per_gas: PrefixedHexString;
    block_hash: PrefixedHexString;
    transactions: PrefixedHexString[];
    withdrawals?: BeaconWithdrawal[];
    blob_gas_used?: PrefixedHexString;
    excess_blob_gas?: PrefixedHexString;
    parent_beacon_block_root?: PrefixedHexString;
    deposit_requests?: BeaconDepositRequest[];
    withdrawal_requests?: BeaconWithdrawalRequest[];
    consolidation_requests?: BeaconConsolidationRequest[];
    execution_witness?: VerkleExecutionWitness;
};
/**
 * Converts a beacon block execution payload JSON object {@link BeaconPayloadJson} to the {@link ExecutionPayload} data needed to construct a {@link Block}.
 * The JSON data can be retrieved from a consensus layer (CL) client on this Beacon API `/eth/v2/beacon/blocks/[block number]`
 */
export declare function executionPayloadFromBeaconPayload(payload: BeaconPayloadJson): ExecutionPayload;
export {};
//# sourceMappingURL=from-beacon-payload.d.ts.map