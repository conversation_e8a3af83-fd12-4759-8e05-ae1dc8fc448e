"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatNotificationDescription = exports.isThrownException = exports.THROWN_EXCEPTIONS = void 0;
exports.THROWN_EXCEPTIONS = [
    'Web3Exception',
    'LedgerException',
    'TrezorException',
    'WalletException',
    'BitGetException',
    'GeneralException',
    'MetamaskException',
    'OkxWalletException',
    'HttpRequestException',
    'TransactionException',
    'TrustWalletException',
    'LedgerCosmosException',
    'CosmosWalletException',
    'GrpcUnaryRequestException',
    'TurnkeyWalletSessionException',
];
const isThrownException = (exception) => {
    if (exports.THROWN_EXCEPTIONS.includes(exception.constructor.name)) {
        return true;
    }
    if (exports.THROWN_EXCEPTIONS.includes(exception.name)) {
        return true;
    }
    if ('errorClass' in exception &&
        exports.THROWN_EXCEPTIONS.includes(exception.errorClass)) {
        return true;
    }
    return false;
};
exports.isThrownException = isThrownException;
const formatNotificationDescription = (description) => {
    const DESCRIPTION_CHARACTER_LIMIT = 50;
    if (description.length <= DESCRIPTION_CHARACTER_LIMIT) {
        return {
            description,
            tooltip: '',
        };
    }
    return {
        description: description.slice(0, DESCRIPTION_CHARACTER_LIMIT) + ' ...',
        tooltip: description,
    };
};
exports.formatNotificationDescription = formatNotificationDescription;
