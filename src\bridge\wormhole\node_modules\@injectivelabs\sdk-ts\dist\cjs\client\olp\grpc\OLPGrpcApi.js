"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OLPGrpcApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const olp_proto_ts_1 = require("@injectivelabs/olp-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("./transformers/index.js");
class OLPGrpcApi extends BaseGrpcConsumer_js_1.default {
    module = exceptions_1.IndexerErrorModule.OLP;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new olp_proto_ts_1.InjectiveDmmRpc.InjectiveDmmV2RPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchEpochs(status) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetEpochsRequest.create();
        if (status) {
            request.status = status;
        }
        try {
            const response = await this.retry(() => this.client.GetEpochs(request));
            return index_js_1.DmmGrpcTransformer.epochsResponseToEpochs(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetEpochs',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetEpochs',
                contextModule: this.module,
            });
        }
    }
    async fetchMarketRewards(epochId) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetMarketRewardsRequest.create();
        request.epochId = epochId.toString();
        try {
            const response = await this.retry(() => this.client.GetMarketRewards(request));
            return index_js_1.DmmGrpcTransformer.marketRewardsResponseToMarketRewards(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetMarketRewards',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetMarketRewards',
                contextModule: this.module,
            });
        }
    }
    async fetchEligibleAddresses({ epochId, page, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetEligibleAddressesRequest.create();
        olp_proto_ts_1.InjectiveDmmRpc.GetRewardsDistributionRequest;
        request.epochId = epochId;
        if (page) {
            request.page = page;
        }
        try {
            const response = await this.retry(() => this.client.GetEligibleAddresses(request));
            return index_js_1.DmmGrpcTransformer.eligibleAddressesResponseToEligibleAddresses(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetEligibleAddresses',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetEligibleAddresses',
                contextModule: this.module,
            });
        }
    }
    async fetchEpochScores({ epochId, page, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetEpochScoresRequest.create();
        request.epochId = epochId;
        if (page) {
            request.page = page;
        }
        try {
            const response = await this.retry(() => this.client.GetEpochScores(request));
            return index_js_1.DmmGrpcTransformer.epochScoresResponseToEpochScores(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetEpochScores',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetEpochScores',
                contextModule: this.module,
            });
        }
    }
    async fetchEpochScoresHistory({ epochId, accountAddress, page, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetEpochScoresHistoryRequest.create();
        request.epochId = epochId;
        request.accountAddress = accountAddress;
        if (page) {
            request.page = page;
        }
        try {
            const response = await this.retry(() => this.client.GetEpochScoresHistory(request));
            return index_js_1.DmmGrpcTransformer.epochScoresHistoryResponseToEpochScoresHistory(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetEpochScoresHistory',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetEpochScoresHistory',
                contextModule: this.module,
            });
        }
    }
    async fetchTotalScores({ epochId, marketId, page, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetTotalScoresRequest.create();
        request.epochId = epochId;
        request.marketId = marketId;
        if (page) {
            request.page = page;
        }
        try {
            const response = await this.retry(() => this.client.GetTotalScores(request));
            return index_js_1.DmmGrpcTransformer.totalScoresResponseToTotalScores(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetTotalScores',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetTotalScores',
                contextModule: this.module,
            });
        }
    }
    async fetchTotalScoresHistory({ epochId, marketId, accountAddress, page, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetTotalScoresHistoryRequest.create();
        request.epochId = epochId;
        request.marketId = marketId;
        request.accountAddress = accountAddress;
        if (page) {
            request.page = page;
        }
        try {
            const response = await this.retry(() => this.client.GetTotalScoresHistory(request));
            return index_js_1.DmmGrpcTransformer.totalScoresHistoryResponseToTotalScoresHistory(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetTotalScoresHistory',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetTotalScoresHistory',
                contextModule: this.module,
            });
        }
    }
    async fetchRewardsDistribution({ epochId, height, page, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetRewardsDistributionRequest.create();
        request.epochId = epochId;
        if (height) {
            request.height = height;
        }
        if (page) {
            request.page = page;
        }
        try {
            const response = await this.retry(() => this.client.GetRewardsDistribution(request));
            return index_js_1.DmmGrpcTransformer.rewardsDistributionResponseToRewardsDistribution(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetRewardsDistribution',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetRewardsDistribution',
                contextModule: this.module,
            });
        }
    }
    async fetchAccountVolumes({ epochId, accountAddress, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetAccountVolumesRequest.create();
        request.epochId = epochId;
        request.accountAddress = accountAddress;
        try {
            const response = await this.retry(() => this.client.GetAccountVolumes(request));
            return index_js_1.DmmGrpcTransformer.accountVolumesResponseToAccountVolumes(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetAccountVolumes',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetAccountVolumes',
                contextModule: this.module,
            });
        }
    }
    async fetchRewardsEligibility({ epochId, accountAddress, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetRewardsEligibilityRequest.create();
        if (epochId) {
            request.epochId = epochId;
        }
        if (accountAddress) {
            request.accountAddress = accountAddress;
        }
        try {
            const response = await this.retry(() => this.client.GetRewardsEligibility(request));
            return index_js_1.DmmGrpcTransformer.rewardsEligibilityResponseToRewardsEligibility(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetRewardsEligibility',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetRewardsEligibility',
                contextModule: this.module,
            });
        }
    }
    async fetchMarketRewardsRange({ epochId, marketId, }) {
        const request = olp_proto_ts_1.InjectiveDmmRpc.GetMarketRewardsRangeRequest.create();
        request.epochId = epochId;
        request.marketId = marketId;
        try {
            const response = await this.retry(() => this.client.GetMarketRewardRanges(request));
            return index_js_1.DmmGrpcTransformer.marketRewardsRangeResponseToMarketRewardsRange(response);
        }
        catch (e) {
            if (e instanceof olp_proto_ts_1.InjectiveDmmRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetMarketRewardRanges',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetMarketRewardRanges',
                contextModule: this.module,
            });
        }
    }
}
exports.OLPGrpcApi = OLPGrpcApi;
