"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcAuthTransformer = void 0;
const pagination_js_1 = require("../../../utils/pagination.js");
const index_js_1 = require("../../../utils/index.js");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Chain Grpc Transformer
 */
class ChainGrpcAuthTransformer {
    static moduleParamsResponseToModuleParams(response) {
        const params = response.params;
        return {
            maxMemoCharacters: parseInt(params.maxMemoCharacters, 10),
            txSigLimit: parseInt(params.txSigLimit, 10),
            txSizeCostPerByte: parseInt(params.txSizeCostPerByte, 10),
            sigVerifyCostEd25519: parseInt(params.sigVerifyCostEd25519, 10),
            sigVerifyCostSecp256k1: parseInt(params.sigVerifyCostSecp256k1, 10),
        };
    }
    static grpcAccountToAccount(ethAccount) {
        const account = core_proto_ts_1.InjectiveTypesV1Beta1Account.EthAccount.decode(ethAccount.value);
        const baseAccount = account.baseAccount;
        const pubKey = baseAccount.pubKey;
        return {
            codeHash: (0, index_js_1.uint8ArrayToString)(account.codeHash),
            baseAccount: {
                address: baseAccount.address,
                pubKey: pubKey
                    ? {
                        key: Buffer.from(core_proto_ts_1.InjectiveCryptoV1Beta1Ethsecp256k1Keys.PubKey.decode(pubKey.value).key).toString('base64'),
                        typeUrl: pubKey.typeUrl,
                    }
                    : undefined,
                accountNumber: parseInt(baseAccount.accountNumber, 10),
                sequence: parseInt(baseAccount.sequence, 10),
            },
        };
    }
    static accountResponseToAccount(response) {
        return ChainGrpcAuthTransformer.grpcAccountToAccount(response.account);
    }
    static accountsResponseToAccounts(response) {
        return {
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(response.pagination),
            accounts: response.accounts.map(ChainGrpcAuthTransformer.grpcAccountToAccount),
        };
    }
}
exports.ChainGrpcAuthTransformer = ChainGrpcAuthTransformer;
