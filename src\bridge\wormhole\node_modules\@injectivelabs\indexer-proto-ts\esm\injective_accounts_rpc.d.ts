import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Observable } from "rxjs";
export declare const protobufPackage = "injective_accounts_rpc";
export interface PortfolioRequest {
    /** Account address */
    accountAddress: string;
}
export interface PortfolioResponse {
    /** The portfolio of this account */
    portfolio: AccountPortfolio | undefined;
}
export interface AccountPortfolio {
    /** The account's portfolio value in USD. */
    portfolioValue: string;
    /** The account's available balance value in USD. */
    availableBalance: string;
    /** The account's locked balance value in USD. */
    lockedBalance: string;
    /** The account's total unrealized PnL value in USD. */
    unrealizedPnl: string;
    /** List of all subaccounts' portfolio */
    subaccounts: SubaccountPortfolio[];
}
export interface SubaccountPortfolio {
    /** The ID of this subaccount */
    subaccountId: string;
    /** The subaccount's available balance value in USD. */
    availableBalance: string;
    /** The subaccount's locked balance value in USD. */
    lockedBalance: string;
    /** The subaccount's total unrealized PnL value in USD. */
    unrealizedPnl: string;
}
export interface OrderStatesRequest {
    spotOrderHashes: string[];
    derivativeOrderHashes: string[];
}
export interface OrderStatesResponse {
    /** List of the spot order state records */
    spotOrderStates: OrderStateRecord[];
    /** List of the derivative order state records */
    derivativeOrderStates: OrderStateRecord[];
}
export interface OrderStateRecord {
    /** Hash of the order */
    orderHash: string;
    /** The subaccountId that this order belongs to */
    subaccountId: string;
    /** The Market ID of the order */
    marketId: string;
    /** The type of the order */
    orderType: string;
    /** The side of the order */
    orderSide: string;
    /** The state (status) of the order */
    state: string;
    /** The filled quantity of the order */
    quantityFilled: string;
    /** The filled quantity of the order */
    quantityRemaining: string;
    /** Order committed timestamp in UNIX millis. */
    createdAt: string;
    /** Order updated timestamp in UNIX millis. */
    updatedAt: string;
    /** Order prices */
    price: string;
    /** Margin for derivative order */
    margin: string;
}
export interface SubaccountsListRequest {
    /** Account address, the subaccounts owner */
    accountAddress: string;
}
export interface SubaccountsListResponse {
    subaccounts: string[];
}
export interface SubaccountBalancesListRequest {
    /** SubaccountId of the trader we want to get the trades from */
    subaccountId: string;
    /**
     * Filter balances by denoms. If not set, the balances of all the denoms for
     * the subaccount are provided.
     */
    denoms: string[];
}
export interface SubaccountBalancesListResponse {
    /** List of subaccount balances */
    balances: SubaccountBalance[];
}
export interface SubaccountBalance {
    /** Related subaccount ID */
    subaccountId: string;
    /** Account address, owner of this subaccount */
    accountAddress: string;
    /** Coin denom on the chain. */
    denom: string;
    deposit: SubaccountDeposit | undefined;
}
export interface SubaccountDeposit {
    totalBalance: string;
    availableBalance: string;
    totalBalanceUsd: string;
    availableBalanceUsd: string;
}
export interface SubaccountBalanceEndpointRequest {
    /** SubaccountId of the trader we want to get the trades from */
    subaccountId: string;
    /** Specify denom to get balance */
    denom: string;
}
export interface SubaccountBalanceEndpointResponse {
    /** Subaccount balance */
    balance: SubaccountBalance | undefined;
}
export interface StreamSubaccountBalanceRequest {
    /** SubaccountId of the trader we want to get the trades from */
    subaccountId: string;
    /**
     * Filter balances by denoms. If not set, the balances of all the denoms for
     * the subaccount are provided.
     */
    denoms: string[];
}
export interface StreamSubaccountBalanceResponse {
    /** Subaccount balance */
    balance: SubaccountBalance | undefined;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface SubaccountHistoryRequest {
    /** SubaccountId of the trader we want to get the history from */
    subaccountId: string;
    /** Filter history by denom */
    denom: string;
    /** Filter history by transfer type */
    transferTypes: string[];
    /** Skip will skip the first n item from the result */
    skip: string;
    /** Limit is used to specify the maximum number of items to be returned */
    limit: number;
    /** Upper bound of account transfer history's executedAt */
    endTime: string;
}
export interface SubaccountHistoryResponse {
    /** List of subaccount transfers */
    transfers: SubaccountBalanceTransfer[];
    paging: Paging | undefined;
}
export interface SubaccountBalanceTransfer {
    /** Type of the subaccount balance transfer */
    transferType: string;
    /** Subaccount ID of the sending side */
    srcSubaccountId: string;
    /** Account address of the sending side */
    srcAccountAddress: string;
    /** Subaccount ID of the receiving side */
    dstSubaccountId: string;
    /** Account address of the receiving side */
    dstAccountAddress: string;
    /** Coin amount of the transfer */
    amount: CosmosCoin | undefined;
    /** Timestamp of the transfer in UNIX millis */
    executedAt: string;
}
export interface CosmosCoin {
    /** Coin denominator */
    denom: string;
    /** Coin amount (big int) */
    amount: string;
}
/** Paging defines the structure for required params for handling pagination */
export interface Paging {
    /** total number of txs saved in database */
    total: string;
    /** can be either block height or index num */
    from: number;
    /** can be either block height or index num */
    to: number;
    /** count entries by subaccount, serving some places on helix */
    countBySubaccount: string;
    /** array of tokens to navigate to the next pages */
    next: string[];
}
export interface SubaccountOrderSummaryRequest {
    /** SubaccountId of the trader we want to get the summary from */
    subaccountId: string;
    /** MarketId is limiting order summary to specific market only */
    marketId: string;
    /** Filter by direction of the orders */
    orderDirection: string;
}
export interface SubaccountOrderSummaryResponse {
    /** Total count of subaccount's spot orders in given market and direction */
    spotOrdersTotal: string;
    /** Total count of subaccount's derivative orders in given market and direction */
    derivativeOrdersTotal: string;
}
export interface RewardsRequest {
    /** The distribution epoch sequence number. -1 for latest. */
    epoch: string;
    /** Account address for the rewards distribution */
    accountAddress: string;
}
export interface RewardsResponse {
    /** The trading rewards distributed */
    rewards: Reward[];
}
export interface Reward {
    /** Account address */
    accountAddress: string;
    /** Reward coins distributed */
    rewards: Coin[];
    /** Rewards distribution timestamp in UNIX millis. */
    distributedAt: string;
}
export interface Coin {
    /** Denom of the coin */
    denom: string;
    amount: string;
    usdValue: string;
}
export interface StreamAccountDataRequest {
    /** account address */
    accountAddress: string;
}
export interface StreamAccountDataResponse {
    subaccountBalance: SubaccountBalanceResult | undefined;
    position: PositionsResult | undefined;
    trade: TradeResult | undefined;
    order: OrderResult | undefined;
    orderHistory: OrderHistoryResult | undefined;
    fundingPayment: FundingPaymentResult | undefined;
}
export interface SubaccountBalanceResult {
    /** Subaccount balance */
    balance: SubaccountBalance | undefined;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface PositionsResult {
    /** Updated derivative Position */
    position: Position | undefined;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface Position {
    /** Ticker of the derivative market */
    ticker: string;
    /** Derivative Market ID */
    marketId: string;
    /** The subaccountId that the position belongs to */
    subaccountId: string;
    /** Direction of the position */
    direction: string;
    /** Quantity of the position */
    quantity: string;
    /** Price of the position */
    entryPrice: string;
    /** Margin of the position */
    margin: string;
    /** LiquidationPrice of the position */
    liquidationPrice: string;
    /** MarkPrice of the position */
    markPrice: string;
    /** Position updated timestamp in UNIX millis. */
    updatedAt: string;
    /** Position created timestamp in UNIX millis. */
    createdAt: string;
}
export interface TradeResult {
    /** New spot market trade */
    spotTrade?: SpotTrade | undefined;
    /** New derivative market trade */
    derivativeTrade?: DerivativeTrade | undefined;
    /** Executed trades update type */
    operationType: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface SpotTrade {
    /** Maker order hash. */
    orderHash: string;
    /** The subaccountId that executed the trade */
    subaccountId: string;
    /** The ID of the market that this trade is in */
    marketId: string;
    /** The execution type of the trade */
    tradeExecutionType: string;
    /** The direction the trade */
    tradeDirection: string;
    /** Price level at which trade has been executed */
    price: PriceLevel | undefined;
    /** The fee associated with the trade (quote asset denom) */
    fee: string;
    /** Timestamp of trade execution in UNIX millis */
    executedAt: string;
    /** Fee recipient address */
    feeRecipient: string;
    /** A unique string that helps differentiate between trades */
    tradeId: string;
    /** Trade's execution side, marker/taker */
    executionSide: string;
    /** Custom client order ID */
    cid: string;
}
export interface PriceLevel {
    /** Price number of the price level. */
    price: string;
    /** Quantity of the price level. */
    quantity: string;
    /** Price level last updated timestamp in UNIX millis. */
    timestamp: string;
}
export interface DerivativeTrade {
    /** Order hash. */
    orderHash: string;
    /** The subaccountId that executed the trade */
    subaccountId: string;
    /** The ID of the market that this trade is in */
    marketId: string;
    /** The execution type of the trade */
    tradeExecutionType: string;
    /** True if the trade is a liquidation */
    isLiquidation: boolean;
    /** Position Delta from the trade */
    positionDelta: PositionDelta | undefined;
    /** The payout associated with the trade */
    payout: string;
    /** The fee associated with the trade */
    fee: string;
    /** Timestamp of trade execution in UNIX millis */
    executedAt: string;
    /** Fee recipient address */
    feeRecipient: string;
    /** A unique string that helps differentiate between trades */
    tradeId: string;
    /** Trade's execution side, marker/taker */
    executionSide: string;
    /** Custom client order ID */
    cid: string;
}
export interface PositionDelta {
    /** The direction the trade */
    tradeDirection: string;
    /** Execution Price of the trade. */
    executionPrice: string;
    /** Execution Quantity of the trade. */
    executionQuantity: string;
    /** Execution Margin of the trade. */
    executionMargin: string;
}
export interface OrderResult {
    /** Updated spot market order */
    spotOrder?: SpotLimitOrder | undefined;
    /** Updated derivative market order */
    derivativeOrder?: DerivativeLimitOrder | undefined;
    /** Executed orders update type */
    operationType: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface SpotLimitOrder {
    /** Hash of the order */
    orderHash: string;
    /** The side of the order */
    orderSide: string;
    /** Spot Market ID is keccak265(baseDenom + quoteDenom) */
    marketId: string;
    /** The subaccountId that this order belongs to */
    subaccountId: string;
    /** Price of the order */
    price: string;
    /** Quantity of the order */
    quantity: string;
    /** The amount of the quantity remaining unfilled */
    unfilledQuantity: string;
    /**
     * Trigger price is the trigger price used by stop/take orders. 0 if the
     * trigger price is not set.
     */
    triggerPrice: string;
    /** Fee recipient address */
    feeRecipient: string;
    /** Order state */
    state: string;
    /** Order committed timestamp in UNIX millis. */
    createdAt: string;
    /** Order updated timestamp in UNIX millis. */
    updatedAt: string;
    /** Transaction Hash where order is created. Not all orders have this field */
    txHash: string;
    /** Custom client order ID */
    cid: string;
}
export interface DerivativeLimitOrder {
    /** Hash of the order */
    orderHash: string;
    /** The side of the order */
    orderSide: string;
    /** Derivative Market ID */
    marketId: string;
    /** The subaccountId that this order belongs to */
    subaccountId: string;
    /** True if the order is a reduce-only order */
    isReduceOnly: boolean;
    /** Margin of the order */
    margin: string;
    /** Price of the order */
    price: string;
    /** Quantity of the order */
    quantity: string;
    /** The amount of the quantity remaining unfilled */
    unfilledQuantity: string;
    /** Trigger price is the trigger price used by stop/take orders */
    triggerPrice: string;
    /** Fee recipient address */
    feeRecipient: string;
    /** Order state */
    state: string;
    /** Order committed timestamp in UNIX millis. */
    createdAt: string;
    /** Order updated timestamp in UNIX millis. */
    updatedAt: string;
    /** Order number of subaccount */
    orderNumber: string;
    /** Order type */
    orderType: string;
    /** Order type */
    isConditional: boolean;
    /** Trigger timestamp, only exists for conditional orders */
    triggerAt: string;
    /** OrderHash of order that is triggered by this conditional order */
    placedOrderHash: string;
    /** Execution type of conditional order */
    executionType: string;
    /** Transaction Hash where order is created. Not all orders have this field */
    txHash: string;
    /** Custom client order ID */
    cid: string;
}
export interface OrderHistoryResult {
    /** Spot order history */
    spotOrderHistory?: SpotOrderHistory | undefined;
    /** Derivative order history */
    derivativeOrderHistory?: DerivativeOrderHistory | undefined;
    /** Order update type */
    operationType: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface SpotOrderHistory {
    /** Hash of the order */
    orderHash: string;
    /** Spot Market ID is keccak265(baseDenom + quoteDenom) */
    marketId: string;
    /** active state of the order */
    isActive: boolean;
    /** The subaccountId that this order belongs to */
    subaccountId: string;
    /** The execution type */
    executionType: string;
    /** The side of the order */
    orderType: string;
    /** Price of the order */
    price: string;
    /** Trigger price */
    triggerPrice: string;
    /** Quantity of the order */
    quantity: string;
    /** Filled amount */
    filledQuantity: string;
    /** Order state */
    state: string;
    /** Order committed timestamp in UNIX millis. */
    createdAt: string;
    /** Order updated timestamp in UNIX millis. */
    updatedAt: string;
    /** Order direction (order side) */
    direction: string;
    /** Transaction Hash where order is created. Not all orders have this field */
    txHash: string;
    /** Custom client order ID */
    cid: string;
}
export interface DerivativeOrderHistory {
    /** Hash of the order */
    orderHash: string;
    /** Spot Market ID is keccak265(baseDenom + quoteDenom) */
    marketId: string;
    /** active state of the order */
    isActive: boolean;
    /** The subaccountId that this order belongs to */
    subaccountId: string;
    /** The execution type */
    executionType: string;
    /** The side of the order */
    orderType: string;
    /** Price of the order */
    price: string;
    /** Trigger price */
    triggerPrice: string;
    /** Quantity of the order */
    quantity: string;
    /** Filled amount */
    filledQuantity: string;
    /** Order state */
    state: string;
    /** Order committed timestamp in UNIX millis. */
    createdAt: string;
    /** Order updated timestamp in UNIX millis. */
    updatedAt: string;
    /** True if an order is reduce only */
    isReduceOnly: boolean;
    /** Order direction (order side) */
    direction: string;
    /** True if this is conditional order, otherwise false */
    isConditional: boolean;
    /** Trigger timestamp in unix milli */
    triggerAt: string;
    /** Order hash placed when this triggers */
    placedOrderHash: string;
    /** Order's margin */
    margin: string;
    /** Transaction Hash where order is created. Not all orders have this field */
    txHash: string;
    /** Custom client order ID */
    cid: string;
}
export interface FundingPaymentResult {
    /** Funding payments of the account */
    fundingPayments: FundingPayment | undefined;
    /** Funding payments type */
    operationType: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface FundingPayment {
    /** Derivative Market ID */
    marketId: string;
    /** The subaccountId that the position belongs to */
    subaccountId: string;
    /** Amount of the funding payment */
    amount: string;
    /** Timestamp of funding payment in UNIX millis */
    timestamp: string;
}
export declare const PortfolioRequest: {
    encode(message: PortfolioRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PortfolioRequest;
    fromJSON(object: any): PortfolioRequest;
    toJSON(message: PortfolioRequest): unknown;
    create(base?: DeepPartial<PortfolioRequest>): PortfolioRequest;
    fromPartial(object: DeepPartial<PortfolioRequest>): PortfolioRequest;
};
export declare const PortfolioResponse: {
    encode(message: PortfolioResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PortfolioResponse;
    fromJSON(object: any): PortfolioResponse;
    toJSON(message: PortfolioResponse): unknown;
    create(base?: DeepPartial<PortfolioResponse>): PortfolioResponse;
    fromPartial(object: DeepPartial<PortfolioResponse>): PortfolioResponse;
};
export declare const AccountPortfolio: {
    encode(message: AccountPortfolio, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountPortfolio;
    fromJSON(object: any): AccountPortfolio;
    toJSON(message: AccountPortfolio): unknown;
    create(base?: DeepPartial<AccountPortfolio>): AccountPortfolio;
    fromPartial(object: DeepPartial<AccountPortfolio>): AccountPortfolio;
};
export declare const SubaccountPortfolio: {
    encode(message: SubaccountPortfolio, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountPortfolio;
    fromJSON(object: any): SubaccountPortfolio;
    toJSON(message: SubaccountPortfolio): unknown;
    create(base?: DeepPartial<SubaccountPortfolio>): SubaccountPortfolio;
    fromPartial(object: DeepPartial<SubaccountPortfolio>): SubaccountPortfolio;
};
export declare const OrderStatesRequest: {
    encode(message: OrderStatesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderStatesRequest;
    fromJSON(object: any): OrderStatesRequest;
    toJSON(message: OrderStatesRequest): unknown;
    create(base?: DeepPartial<OrderStatesRequest>): OrderStatesRequest;
    fromPartial(object: DeepPartial<OrderStatesRequest>): OrderStatesRequest;
};
export declare const OrderStatesResponse: {
    encode(message: OrderStatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderStatesResponse;
    fromJSON(object: any): OrderStatesResponse;
    toJSON(message: OrderStatesResponse): unknown;
    create(base?: DeepPartial<OrderStatesResponse>): OrderStatesResponse;
    fromPartial(object: DeepPartial<OrderStatesResponse>): OrderStatesResponse;
};
export declare const OrderStateRecord: {
    encode(message: OrderStateRecord, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderStateRecord;
    fromJSON(object: any): OrderStateRecord;
    toJSON(message: OrderStateRecord): unknown;
    create(base?: DeepPartial<OrderStateRecord>): OrderStateRecord;
    fromPartial(object: DeepPartial<OrderStateRecord>): OrderStateRecord;
};
export declare const SubaccountsListRequest: {
    encode(message: SubaccountsListRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountsListRequest;
    fromJSON(object: any): SubaccountsListRequest;
    toJSON(message: SubaccountsListRequest): unknown;
    create(base?: DeepPartial<SubaccountsListRequest>): SubaccountsListRequest;
    fromPartial(object: DeepPartial<SubaccountsListRequest>): SubaccountsListRequest;
};
export declare const SubaccountsListResponse: {
    encode(message: SubaccountsListResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountsListResponse;
    fromJSON(object: any): SubaccountsListResponse;
    toJSON(message: SubaccountsListResponse): unknown;
    create(base?: DeepPartial<SubaccountsListResponse>): SubaccountsListResponse;
    fromPartial(object: DeepPartial<SubaccountsListResponse>): SubaccountsListResponse;
};
export declare const SubaccountBalancesListRequest: {
    encode(message: SubaccountBalancesListRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalancesListRequest;
    fromJSON(object: any): SubaccountBalancesListRequest;
    toJSON(message: SubaccountBalancesListRequest): unknown;
    create(base?: DeepPartial<SubaccountBalancesListRequest>): SubaccountBalancesListRequest;
    fromPartial(object: DeepPartial<SubaccountBalancesListRequest>): SubaccountBalancesListRequest;
};
export declare const SubaccountBalancesListResponse: {
    encode(message: SubaccountBalancesListResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalancesListResponse;
    fromJSON(object: any): SubaccountBalancesListResponse;
    toJSON(message: SubaccountBalancesListResponse): unknown;
    create(base?: DeepPartial<SubaccountBalancesListResponse>): SubaccountBalancesListResponse;
    fromPartial(object: DeepPartial<SubaccountBalancesListResponse>): SubaccountBalancesListResponse;
};
export declare const SubaccountBalance: {
    encode(message: SubaccountBalance, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalance;
    fromJSON(object: any): SubaccountBalance;
    toJSON(message: SubaccountBalance): unknown;
    create(base?: DeepPartial<SubaccountBalance>): SubaccountBalance;
    fromPartial(object: DeepPartial<SubaccountBalance>): SubaccountBalance;
};
export declare const SubaccountDeposit: {
    encode(message: SubaccountDeposit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountDeposit;
    fromJSON(object: any): SubaccountDeposit;
    toJSON(message: SubaccountDeposit): unknown;
    create(base?: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
    fromPartial(object: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
};
export declare const SubaccountBalanceEndpointRequest: {
    encode(message: SubaccountBalanceEndpointRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalanceEndpointRequest;
    fromJSON(object: any): SubaccountBalanceEndpointRequest;
    toJSON(message: SubaccountBalanceEndpointRequest): unknown;
    create(base?: DeepPartial<SubaccountBalanceEndpointRequest>): SubaccountBalanceEndpointRequest;
    fromPartial(object: DeepPartial<SubaccountBalanceEndpointRequest>): SubaccountBalanceEndpointRequest;
};
export declare const SubaccountBalanceEndpointResponse: {
    encode(message: SubaccountBalanceEndpointResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalanceEndpointResponse;
    fromJSON(object: any): SubaccountBalanceEndpointResponse;
    toJSON(message: SubaccountBalanceEndpointResponse): unknown;
    create(base?: DeepPartial<SubaccountBalanceEndpointResponse>): SubaccountBalanceEndpointResponse;
    fromPartial(object: DeepPartial<SubaccountBalanceEndpointResponse>): SubaccountBalanceEndpointResponse;
};
export declare const StreamSubaccountBalanceRequest: {
    encode(message: StreamSubaccountBalanceRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamSubaccountBalanceRequest;
    fromJSON(object: any): StreamSubaccountBalanceRequest;
    toJSON(message: StreamSubaccountBalanceRequest): unknown;
    create(base?: DeepPartial<StreamSubaccountBalanceRequest>): StreamSubaccountBalanceRequest;
    fromPartial(object: DeepPartial<StreamSubaccountBalanceRequest>): StreamSubaccountBalanceRequest;
};
export declare const StreamSubaccountBalanceResponse: {
    encode(message: StreamSubaccountBalanceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamSubaccountBalanceResponse;
    fromJSON(object: any): StreamSubaccountBalanceResponse;
    toJSON(message: StreamSubaccountBalanceResponse): unknown;
    create(base?: DeepPartial<StreamSubaccountBalanceResponse>): StreamSubaccountBalanceResponse;
    fromPartial(object: DeepPartial<StreamSubaccountBalanceResponse>): StreamSubaccountBalanceResponse;
};
export declare const SubaccountHistoryRequest: {
    encode(message: SubaccountHistoryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountHistoryRequest;
    fromJSON(object: any): SubaccountHistoryRequest;
    toJSON(message: SubaccountHistoryRequest): unknown;
    create(base?: DeepPartial<SubaccountHistoryRequest>): SubaccountHistoryRequest;
    fromPartial(object: DeepPartial<SubaccountHistoryRequest>): SubaccountHistoryRequest;
};
export declare const SubaccountHistoryResponse: {
    encode(message: SubaccountHistoryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountHistoryResponse;
    fromJSON(object: any): SubaccountHistoryResponse;
    toJSON(message: SubaccountHistoryResponse): unknown;
    create(base?: DeepPartial<SubaccountHistoryResponse>): SubaccountHistoryResponse;
    fromPartial(object: DeepPartial<SubaccountHistoryResponse>): SubaccountHistoryResponse;
};
export declare const SubaccountBalanceTransfer: {
    encode(message: SubaccountBalanceTransfer, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalanceTransfer;
    fromJSON(object: any): SubaccountBalanceTransfer;
    toJSON(message: SubaccountBalanceTransfer): unknown;
    create(base?: DeepPartial<SubaccountBalanceTransfer>): SubaccountBalanceTransfer;
    fromPartial(object: DeepPartial<SubaccountBalanceTransfer>): SubaccountBalanceTransfer;
};
export declare const CosmosCoin: {
    encode(message: CosmosCoin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CosmosCoin;
    fromJSON(object: any): CosmosCoin;
    toJSON(message: CosmosCoin): unknown;
    create(base?: DeepPartial<CosmosCoin>): CosmosCoin;
    fromPartial(object: DeepPartial<CosmosCoin>): CosmosCoin;
};
export declare const Paging: {
    encode(message: Paging, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Paging;
    fromJSON(object: any): Paging;
    toJSON(message: Paging): unknown;
    create(base?: DeepPartial<Paging>): Paging;
    fromPartial(object: DeepPartial<Paging>): Paging;
};
export declare const SubaccountOrderSummaryRequest: {
    encode(message: SubaccountOrderSummaryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountOrderSummaryRequest;
    fromJSON(object: any): SubaccountOrderSummaryRequest;
    toJSON(message: SubaccountOrderSummaryRequest): unknown;
    create(base?: DeepPartial<SubaccountOrderSummaryRequest>): SubaccountOrderSummaryRequest;
    fromPartial(object: DeepPartial<SubaccountOrderSummaryRequest>): SubaccountOrderSummaryRequest;
};
export declare const SubaccountOrderSummaryResponse: {
    encode(message: SubaccountOrderSummaryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountOrderSummaryResponse;
    fromJSON(object: any): SubaccountOrderSummaryResponse;
    toJSON(message: SubaccountOrderSummaryResponse): unknown;
    create(base?: DeepPartial<SubaccountOrderSummaryResponse>): SubaccountOrderSummaryResponse;
    fromPartial(object: DeepPartial<SubaccountOrderSummaryResponse>): SubaccountOrderSummaryResponse;
};
export declare const RewardsRequest: {
    encode(message: RewardsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RewardsRequest;
    fromJSON(object: any): RewardsRequest;
    toJSON(message: RewardsRequest): unknown;
    create(base?: DeepPartial<RewardsRequest>): RewardsRequest;
    fromPartial(object: DeepPartial<RewardsRequest>): RewardsRequest;
};
export declare const RewardsResponse: {
    encode(message: RewardsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RewardsResponse;
    fromJSON(object: any): RewardsResponse;
    toJSON(message: RewardsResponse): unknown;
    create(base?: DeepPartial<RewardsResponse>): RewardsResponse;
    fromPartial(object: DeepPartial<RewardsResponse>): RewardsResponse;
};
export declare const Reward: {
    encode(message: Reward, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Reward;
    fromJSON(object: any): Reward;
    toJSON(message: Reward): unknown;
    create(base?: DeepPartial<Reward>): Reward;
    fromPartial(object: DeepPartial<Reward>): Reward;
};
export declare const Coin: {
    encode(message: Coin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Coin;
    fromJSON(object: any): Coin;
    toJSON(message: Coin): unknown;
    create(base?: DeepPartial<Coin>): Coin;
    fromPartial(object: DeepPartial<Coin>): Coin;
};
export declare const StreamAccountDataRequest: {
    encode(message: StreamAccountDataRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamAccountDataRequest;
    fromJSON(object: any): StreamAccountDataRequest;
    toJSON(message: StreamAccountDataRequest): unknown;
    create(base?: DeepPartial<StreamAccountDataRequest>): StreamAccountDataRequest;
    fromPartial(object: DeepPartial<StreamAccountDataRequest>): StreamAccountDataRequest;
};
export declare const StreamAccountDataResponse: {
    encode(message: StreamAccountDataResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamAccountDataResponse;
    fromJSON(object: any): StreamAccountDataResponse;
    toJSON(message: StreamAccountDataResponse): unknown;
    create(base?: DeepPartial<StreamAccountDataResponse>): StreamAccountDataResponse;
    fromPartial(object: DeepPartial<StreamAccountDataResponse>): StreamAccountDataResponse;
};
export declare const SubaccountBalanceResult: {
    encode(message: SubaccountBalanceResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalanceResult;
    fromJSON(object: any): SubaccountBalanceResult;
    toJSON(message: SubaccountBalanceResult): unknown;
    create(base?: DeepPartial<SubaccountBalanceResult>): SubaccountBalanceResult;
    fromPartial(object: DeepPartial<SubaccountBalanceResult>): SubaccountBalanceResult;
};
export declare const PositionsResult: {
    encode(message: PositionsResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PositionsResult;
    fromJSON(object: any): PositionsResult;
    toJSON(message: PositionsResult): unknown;
    create(base?: DeepPartial<PositionsResult>): PositionsResult;
    fromPartial(object: DeepPartial<PositionsResult>): PositionsResult;
};
export declare const Position: {
    encode(message: Position, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Position;
    fromJSON(object: any): Position;
    toJSON(message: Position): unknown;
    create(base?: DeepPartial<Position>): Position;
    fromPartial(object: DeepPartial<Position>): Position;
};
export declare const TradeResult: {
    encode(message: TradeResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradeResult;
    fromJSON(object: any): TradeResult;
    toJSON(message: TradeResult): unknown;
    create(base?: DeepPartial<TradeResult>): TradeResult;
    fromPartial(object: DeepPartial<TradeResult>): TradeResult;
};
export declare const SpotTrade: {
    encode(message: SpotTrade, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotTrade;
    fromJSON(object: any): SpotTrade;
    toJSON(message: SpotTrade): unknown;
    create(base?: DeepPartial<SpotTrade>): SpotTrade;
    fromPartial(object: DeepPartial<SpotTrade>): SpotTrade;
};
export declare const PriceLevel: {
    encode(message: PriceLevel, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceLevel;
    fromJSON(object: any): PriceLevel;
    toJSON(message: PriceLevel): unknown;
    create(base?: DeepPartial<PriceLevel>): PriceLevel;
    fromPartial(object: DeepPartial<PriceLevel>): PriceLevel;
};
export declare const DerivativeTrade: {
    encode(message: DerivativeTrade, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeTrade;
    fromJSON(object: any): DerivativeTrade;
    toJSON(message: DerivativeTrade): unknown;
    create(base?: DeepPartial<DerivativeTrade>): DerivativeTrade;
    fromPartial(object: DeepPartial<DerivativeTrade>): DerivativeTrade;
};
export declare const PositionDelta: {
    encode(message: PositionDelta, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PositionDelta;
    fromJSON(object: any): PositionDelta;
    toJSON(message: PositionDelta): unknown;
    create(base?: DeepPartial<PositionDelta>): PositionDelta;
    fromPartial(object: DeepPartial<PositionDelta>): PositionDelta;
};
export declare const OrderResult: {
    encode(message: OrderResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderResult;
    fromJSON(object: any): OrderResult;
    toJSON(message: OrderResult): unknown;
    create(base?: DeepPartial<OrderResult>): OrderResult;
    fromPartial(object: DeepPartial<OrderResult>): OrderResult;
};
export declare const SpotLimitOrder: {
    encode(message: SpotLimitOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotLimitOrder;
    fromJSON(object: any): SpotLimitOrder;
    toJSON(message: SpotLimitOrder): unknown;
    create(base?: DeepPartial<SpotLimitOrder>): SpotLimitOrder;
    fromPartial(object: DeepPartial<SpotLimitOrder>): SpotLimitOrder;
};
export declare const DerivativeLimitOrder: {
    encode(message: DerivativeLimitOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeLimitOrder;
    fromJSON(object: any): DerivativeLimitOrder;
    toJSON(message: DerivativeLimitOrder): unknown;
    create(base?: DeepPartial<DerivativeLimitOrder>): DerivativeLimitOrder;
    fromPartial(object: DeepPartial<DerivativeLimitOrder>): DerivativeLimitOrder;
};
export declare const OrderHistoryResult: {
    encode(message: OrderHistoryResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderHistoryResult;
    fromJSON(object: any): OrderHistoryResult;
    toJSON(message: OrderHistoryResult): unknown;
    create(base?: DeepPartial<OrderHistoryResult>): OrderHistoryResult;
    fromPartial(object: DeepPartial<OrderHistoryResult>): OrderHistoryResult;
};
export declare const SpotOrderHistory: {
    encode(message: SpotOrderHistory, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotOrderHistory;
    fromJSON(object: any): SpotOrderHistory;
    toJSON(message: SpotOrderHistory): unknown;
    create(base?: DeepPartial<SpotOrderHistory>): SpotOrderHistory;
    fromPartial(object: DeepPartial<SpotOrderHistory>): SpotOrderHistory;
};
export declare const DerivativeOrderHistory: {
    encode(message: DerivativeOrderHistory, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeOrderHistory;
    fromJSON(object: any): DerivativeOrderHistory;
    toJSON(message: DerivativeOrderHistory): unknown;
    create(base?: DeepPartial<DerivativeOrderHistory>): DerivativeOrderHistory;
    fromPartial(object: DeepPartial<DerivativeOrderHistory>): DerivativeOrderHistory;
};
export declare const FundingPaymentResult: {
    encode(message: FundingPaymentResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FundingPaymentResult;
    fromJSON(object: any): FundingPaymentResult;
    toJSON(message: FundingPaymentResult): unknown;
    create(base?: DeepPartial<FundingPaymentResult>): FundingPaymentResult;
    fromPartial(object: DeepPartial<FundingPaymentResult>): FundingPaymentResult;
};
export declare const FundingPayment: {
    encode(message: FundingPayment, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FundingPayment;
    fromJSON(object: any): FundingPayment;
    toJSON(message: FundingPayment): unknown;
    create(base?: DeepPartial<FundingPayment>): FundingPayment;
    fromPartial(object: DeepPartial<FundingPayment>): FundingPayment;
};
/** InjectiveAccountsRPC defines API of Exchange Accounts provider. */
export interface InjectiveAccountsRPC {
    /** Provide the account's portfolio value in USD. */
    Portfolio(request: DeepPartial<PortfolioRequest>, metadata?: grpc.Metadata): Promise<PortfolioResponse>;
    /** List order states by order hashes */
    OrderStates(request: DeepPartial<OrderStatesRequest>, metadata?: grpc.Metadata): Promise<OrderStatesResponse>;
    /** List all subaccounts IDs of an account address */
    SubaccountsList(request: DeepPartial<SubaccountsListRequest>, metadata?: grpc.Metadata): Promise<SubaccountsListResponse>;
    /** List subaccount balances for the provided denoms. */
    SubaccountBalancesList(request: DeepPartial<SubaccountBalancesListRequest>, metadata?: grpc.Metadata): Promise<SubaccountBalancesListResponse>;
    /** Gets a balance for specific coin denom */
    SubaccountBalanceEndpoint(request: DeepPartial<SubaccountBalanceEndpointRequest>, metadata?: grpc.Metadata): Promise<SubaccountBalanceEndpointResponse>;
    /**
     * StreamSubaccountBalance streams new balance changes for a specified
     * subaccount and denoms. If no denoms are provided, all denom changes are
     * streamed.
     */
    StreamSubaccountBalance(request: DeepPartial<StreamSubaccountBalanceRequest>, metadata?: grpc.Metadata): Observable<StreamSubaccountBalanceResponse>;
    /** Get subaccount's deposits and withdrawals history */
    SubaccountHistory(request: DeepPartial<SubaccountHistoryRequest>, metadata?: grpc.Metadata): Promise<SubaccountHistoryResponse>;
    /** Get subaccount's orders summary */
    SubaccountOrderSummary(request: DeepPartial<SubaccountOrderSummaryRequest>, metadata?: grpc.Metadata): Promise<SubaccountOrderSummaryResponse>;
    /** Provide historical trading rewards */
    Rewards(request: DeepPartial<RewardsRequest>, metadata?: grpc.Metadata): Promise<RewardsResponse>;
    /** Stream live data for an account and respective data */
    StreamAccountData(request: DeepPartial<StreamAccountDataRequest>, metadata?: grpc.Metadata): Observable<StreamAccountDataResponse>;
}
export declare class InjectiveAccountsRPCClientImpl implements InjectiveAccountsRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    Portfolio(request: DeepPartial<PortfolioRequest>, metadata?: grpc.Metadata): Promise<PortfolioResponse>;
    OrderStates(request: DeepPartial<OrderStatesRequest>, metadata?: grpc.Metadata): Promise<OrderStatesResponse>;
    SubaccountsList(request: DeepPartial<SubaccountsListRequest>, metadata?: grpc.Metadata): Promise<SubaccountsListResponse>;
    SubaccountBalancesList(request: DeepPartial<SubaccountBalancesListRequest>, metadata?: grpc.Metadata): Promise<SubaccountBalancesListResponse>;
    SubaccountBalanceEndpoint(request: DeepPartial<SubaccountBalanceEndpointRequest>, metadata?: grpc.Metadata): Promise<SubaccountBalanceEndpointResponse>;
    StreamSubaccountBalance(request: DeepPartial<StreamSubaccountBalanceRequest>, metadata?: grpc.Metadata): Observable<StreamSubaccountBalanceResponse>;
    SubaccountHistory(request: DeepPartial<SubaccountHistoryRequest>, metadata?: grpc.Metadata): Promise<SubaccountHistoryResponse>;
    SubaccountOrderSummary(request: DeepPartial<SubaccountOrderSummaryRequest>, metadata?: grpc.Metadata): Promise<SubaccountOrderSummaryResponse>;
    Rewards(request: DeepPartial<RewardsRequest>, metadata?: grpc.Metadata): Promise<RewardsResponse>;
    StreamAccountData(request: DeepPartial<StreamAccountDataRequest>, metadata?: grpc.Metadata): Observable<StreamAccountDataResponse>;
}
export declare const InjectiveAccountsRPCDesc: {
    serviceName: string;
};
export declare const InjectiveAccountsRPCPortfolioDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCOrderStatesDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCSubaccountsListDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCSubaccountBalancesListDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCSubaccountBalanceEndpointDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCStreamSubaccountBalanceDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCSubaccountHistoryDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCSubaccountOrderSummaryDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCRewardsDesc: UnaryMethodDefinitionish;
export declare const InjectiveAccountsRPCStreamAccountDataDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        streamingTransport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
