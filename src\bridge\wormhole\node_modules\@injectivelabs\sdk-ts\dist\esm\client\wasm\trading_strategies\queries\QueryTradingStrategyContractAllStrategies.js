import { BaseWasmQuery } from '../../BaseWasmQuery.js';
import { toBase64 } from '../../../../utils/index.js';
export class QueryTradingStrategyContractAllStrategies extends BaseWasmQuery {
    toPayload() {
        const payload = toBase64({
            all_strategies: {
                start_after: this.params.startAfter,
                limit: this.params.limit,
            },
        });
        return payload;
    }
}
