{"name": "@chainlink/ccip-tools-ts", "version": "0.2.7", "description": "CLI and library to interact with CCIP", "author": "Chainlink devs", "license": "MIT", "type": "module", "main": "dist/lib/index.js", "module": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "bin": "dist/ccip-tools-ts", "scripts": {"test": "jest", "test:rpc": "tsx scripts/test-rpc-integration.ts", "test:networks": "tsx scripts/test-network-names.ts", "example:rpc": "tsx examples/rpc-integration-example.ts", "lint": "prettier --check ./src && eslint ./src", "lint:fix": "prettier --write ./src && eslint --fix ./src", "typecheck": "tsc --noEmit", "generate": "node ./generate.cjs ./src/abi/* ./src/lib/selectors.ts ./src/index.ts", "build": "npm run clean && npm run generate && tsc -p ./tsconfig.build.json && npm run make-script", "make-script": "sed -e '1s|#!/.*|#!/usr/bin/env node|' ./dist/index.js > ./dist/ccip-tools-ts && chmod +x ./dist/ccip-tools-ts && rm -v ./dist/index.js", "start": "tsx src", "clean": "rm -rfv ./dist ./coverage", "prepare": "npm run build"}, "files": ["./dist/**"], "devDependencies": {"@types/bn.js": "^5.1.6", "@eslint/js": "9.27.0", "@types/jest": "29.5.14", "@types/node": "22.15.26", "@types/yargs": "17.0.33", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-import-resolver-typescript": "4.4.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.6", "ethers-abitype": "1.0.3", "glob": "11.0.2", "jest": "29.7.0", "prettier": "3.5.3", "ts-jest": "29.3.4", "tsx": "4.19.4", "typescript": "5.8.3", "typescript-eslint": "8.33.0"}, "dependencies": {"@coral-xyz/anchor": "0.29.0", "@inquirer/prompts": "7.5.3", "@solana/spl-token": "0.4.13", "@xlabs-xyz/ledger-signer-ethers-v6": "^0.0.1", "abitype": "1.0.8", "bn.js": "^5.2.2", "borsh": "^2.0.0", "bs58": "^6.0.0", "buffer-layout": "^1.2.2", "ethers": "6.14.3", "tslib": "2.8.1", "yaml": "2.8.0", "yargs": "18.0.0"}, "overrides": {"@xlabs-xyz/ledger-signer-ethers-v6": {"ethers": "$ethers"}}, "prettier": {"trailingComma": "all", "tabWidth": 2, "semi": false, "singleQuote": true, "printWidth": 100}, "jest": {"preset": "ts-jest/presets/js-with-ts-esm", "modulePathIgnorePatterns": ["<rootDir>/dist/"], "moduleNameMapper": {"(.+)\\.[jt]s$": "$1"}, "transform": {"^.+\\.ts$": ["ts-jest", {"useESM": true, "tsconfig": "tsconfig.test.json"}]}, "collectCoverage": true}}