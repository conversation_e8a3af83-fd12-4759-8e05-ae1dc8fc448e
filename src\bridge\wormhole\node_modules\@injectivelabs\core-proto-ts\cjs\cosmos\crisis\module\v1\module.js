"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Module = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cosmos.crisis.module.v1";
function createBaseModule() {
    return { feeCollectorName: "", authority: "" };
}
exports.Module = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feeCollectorName !== "") {
            writer.uint32(10).string(message.feeCollectorName);
        }
        if (message.authority !== "") {
            writer.uint32(18).string(message.authority);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feeCollectorName = reader.string();
                    break;
                case 2:
                    message.authority = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feeCollectorName: isSet(object.feeCollectorName) ? String(object.feeCollectorName) : "",
            authority: isSet(object.authority) ? String(object.authority) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feeCollectorName !== undefined && (obj.feeCollectorName = message.feeCollectorName);
        message.authority !== undefined && (obj.authority = message.authority);
        return obj;
    },
    create: function (base) {
        return exports.Module.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseModule();
        message.feeCollectorName = (_a = object.feeCollectorName) !== null && _a !== void 0 ? _a : "";
        message.authority = (_b = object.authority) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
