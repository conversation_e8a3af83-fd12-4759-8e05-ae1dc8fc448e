"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgUpdateAdmin extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgUpdateAdmin(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.CosmwasmWasmV1Tx.MsgUpdateAdmin.create();
        message.sender = params.sender;
        message.newAdmin = params.newAdmin;
        message.contract = params.contract;
        return core_proto_ts_1.CosmwasmWasmV1Tx.MsgUpdateAdmin.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmwasm.wasm.v1.MsgUpdateAdmin',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'wasm/MsgUpdateAdmin',
            value: { ...message },
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmwasm.wasm.v1.MsgUpdateAdmin',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmwasm.wasm.v1.MsgUpdateAdmin',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmwasmWasmV1Tx.MsgUpdateAdmin.encode(this.toProto()).finish();
    }
}
exports.default = MsgUpdateAdmin;
