import { AptosConfig } from '../api/aptosConfig.mjs';
import { AccountAddressInput } from '../core/accountAddress.mjs';
import { HexInput } from '../types/types.mjs';
import { A as Account } from '../Ed25519Account-D9XrCLfE.mjs';
import { InputGenerateTransactionOptions } from '../transactions/types.mjs';
import { EphemeralKeyPair } from '../account/EphemeralKeyPair.mjs';
import { KeylessAccount } from '../account/KeylessAccount.mjs';
import { ProofFetchCallback } from '../account/AbstractKeylessAccount.mjs';
import { FederatedKeylessAccount } from '../account/FederatedKeylessAccount.mjs';
import { SimpleTransaction } from '../transactions/instances/simpleTransaction.mjs';
import { l as ZeroKnowledgeSig, r as MoveJWK } from '../federatedKeyless-DAYXjY2Y.mjs';
import '../utils/apiEndpoints.mjs';
import '../utils/const.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';
import '../core/crypto/ephemeral.mjs';
import '../core/crypto/proof.mjs';
import '../types/keyless.mjs';
import '@noble/curves/abstract/weierstrass';
import '@noble/curves/abstract/tower';

/**
 * Retrieves a pepper value based on the provided configuration and authentication details.
 *
 * @param args - The arguments required to fetch the pepper.
 * @param args.aptosConfig - The configuration object for Aptos.
 * @param args.jwt - The JSON Web Token used for authentication.
 * @param args.ephemeralKeyPair - The ephemeral key pair used for the operation.
 * @param args.uidKey - An optional unique identifier key (defaults to "sub").
 * @param args.derivationPath - An optional derivation path for the key.
 * @returns A Uint8Array containing the fetched pepper value.
 * @group Implementation
 */
declare function getPepper(args: {
    aptosConfig: AptosConfig;
    jwt: string;
    ephemeralKeyPair: EphemeralKeyPair;
    uidKey?: string;
    derivationPath?: string;
}): Promise<Uint8Array>;
/**
 * Generates a zero-knowledge proof based on the provided parameters.
 * This function is essential for creating a signed proof that can be used in various cryptographic operations.
 *
 * @param args - The parameters required to generate the proof.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.jwt - The JSON Web Token used for authentication.
 * @param args.ephemeralKeyPair - The ephemeral key pair used for generating the proof.
 * @param args.pepper - An optional hex input used to enhance security (default is generated if not provided).
 * @param args.uidKey - An optional string that specifies the unique identifier key (defaults to "sub").
 * @throws Error if the pepper length is not valid or if the ephemeral key pair's lifespan exceeds the maximum allowed.
 * @group Implementation
 */
declare function getProof(args: {
    aptosConfig: AptosConfig;
    jwt: string;
    ephemeralKeyPair: EphemeralKeyPair;
    pepper?: HexInput;
    uidKey?: string;
    maxExpHorizonSecs?: number;
}): Promise<ZeroKnowledgeSig>;
/**
 * Derives a keyless account by fetching the necessary proof and looking up the original account address.
 * This function helps in creating a keyless account that can be used without managing private keys directly.
 *
 * @param args - The arguments required to derive the keyless account.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.jwt - The JSON Web Token used for authentication.
 * @param args.ephemeralKeyPair - The ephemeral key pair used for cryptographic operations.
 * @param args.uidKey - An optional unique identifier key for the user.
 * @param args.pepper - An optional hexadecimal input used for additional security.
 * @param args.proofFetchCallback - An optional callback function to handle the proof fetch outcome.
 * @returns A keyless account object.
 * @group Implementation
 */
declare function deriveKeylessAccount(args: {
    aptosConfig: AptosConfig;
    jwt: string;
    ephemeralKeyPair: EphemeralKeyPair;
    uidKey?: string;
    pepper?: HexInput;
    proofFetchCallback?: ProofFetchCallback;
}): Promise<KeylessAccount>;
declare function deriveKeylessAccount(args: {
    aptosConfig: AptosConfig;
    jwt: string;
    ephemeralKeyPair: EphemeralKeyPair;
    jwkAddress: AccountAddressInput;
    uidKey?: string;
    pepper?: HexInput;
    proofFetchCallback?: ProofFetchCallback;
}): Promise<FederatedKeylessAccount>;
interface JWKS {
    keys: MoveJWK[];
}
declare function updateFederatedKeylessJwkSetTransaction(args: {
    aptosConfig: AptosConfig;
    sender: Account;
    iss: string;
    jwksUrl?: string;
    options?: InputGenerateTransactionOptions;
}): Promise<SimpleTransaction>;

export { type JWKS, deriveKeylessAccount, getPepper, getProof, updateFederatedKeylessJwkSetTransaction };
