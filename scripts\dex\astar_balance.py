#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询Astar网络钱包余额脚本
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

import argparse
from tabulate import tabulate
from src.dex.astar.arthswap.client import ArthSwapClient
from config.config import config  # 直接导入config对象


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='查询Astar钱包余额')
    parser.add_argument('tokens', nargs='*', help='要查询的代币符号列表，例如ASTR WASTR USDT，默认为ASTR和WASTR')
    parser.add_argument('-a', '--address', help='要查询的钱包地址，默认为配置中的钱包')
    parser.add_argument('-f', '--format', choices=['table', 'simple', 'plain'], default='table', 
                       help='输出格式: table=带格式表格，simple=简单表格，plain=纯文本')
    
    args = parser.parse_args()
    
    # 不需要初始化配置，直接使用已加载的config对象
    
    try:
        # 创建ArthSwap客户端
        client = ArthSwapClient()
        
        # 如果指定了地址，使用该地址，否则使用钱包地址
        address = args.address if args.address else client.address
        
        # 如果没有指定代币，默认查询ASTR和WASTR
        tokens = [t.upper() for t in args.tokens] if args.tokens else ['ASTR', 'WASTR']
        
        # 获取代币余额
        balances = []
        for token in tokens:
            try:
                # 获取余额
                if token == 'ASTR':
                    balance = client.wallet.get_native_balance()
                else:
                    balance = client.wallet.get_token_balance(token)
                
                # 获取代币详情
                decimals = client.get_token_decimals(token)
                token_address = client.get_token_address(token) if token != 'ASTR' else 'Native Token'
                
                # 添加到结果列表
                balances.append({
                    'token': token,
                    'balance': balance,
                    'decimals': decimals,
                    'address': token_address
                })
            except Exception as e:
                print(f"获取{token}余额失败: {str(e)}")
        
        # 显示结果
        print(f"\n钱包地址: {address}")
        
        if balances:
            # 准备表格数据
            table_data = []
            for b in balances:
                table_data.append([
                    b['token'],
                    f"{b['balance']:.6f}",
                    b['decimals'],
                    b['address']
                ])
            
            # 表格头
            headers = ['代币', '余额', '精度', '合约地址']
            
            # 根据指定格式输出
            if args.format == 'table':
                print(tabulate(table_data, headers=headers, tablefmt='pretty'))
            elif args.format == 'simple':
                print(tabulate(table_data, headers=headers, tablefmt='simple'))
            elif args.format == 'plain':
                for row in table_data:
                    print(f"{row[0]}: {row[1]}")
        else:
            print("未找到余额信息")
    
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 