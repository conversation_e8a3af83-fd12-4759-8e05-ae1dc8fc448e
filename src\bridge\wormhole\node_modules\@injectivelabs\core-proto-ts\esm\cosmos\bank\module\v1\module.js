/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.bank.module.v1";
function createBaseModule() {
    return { blockedModuleAccountsOverride: [], authority: "", restrictionsOrder: [] };
}
export const Module = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.blockedModuleAccountsOverride) {
            writer.uint32(10).string(v);
        }
        if (message.authority !== "") {
            writer.uint32(18).string(message.authority);
        }
        for (const v of message.restrictionsOrder) {
            writer.uint32(26).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModule();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.blockedModuleAccountsOverride.push(reader.string());
                    break;
                case 2:
                    message.authority = reader.string();
                    break;
                case 3:
                    message.restrictionsOrder.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            blockedModuleAccountsOverride: Array.isArray(object?.blockedModuleAccountsOverride)
                ? object.blockedModuleAccountsOverride.map((e) => String(e))
                : [],
            authority: isSet(object.authority) ? String(object.authority) : "",
            restrictionsOrder: Array.isArray(object?.restrictionsOrder)
                ? object.restrictionsOrder.map((e) => String(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.blockedModuleAccountsOverride) {
            obj.blockedModuleAccountsOverride = message.blockedModuleAccountsOverride.map((e) => e);
        }
        else {
            obj.blockedModuleAccountsOverride = [];
        }
        message.authority !== undefined && (obj.authority = message.authority);
        if (message.restrictionsOrder) {
            obj.restrictionsOrder = message.restrictionsOrder.map((e) => e);
        }
        else {
            obj.restrictionsOrder = [];
        }
        return obj;
    },
    create(base) {
        return Module.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModule();
        message.blockedModuleAccountsOverride = object.blockedModuleAccountsOverride?.map((e) => e) || [];
        message.authority = object.authority ?? "";
        message.restrictionsOrder = object.restrictionsOrder?.map((e) => e) || [];
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
