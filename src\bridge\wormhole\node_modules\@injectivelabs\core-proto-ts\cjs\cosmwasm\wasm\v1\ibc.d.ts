import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cosmwasm.wasm.v1";
/** MsgIBCSend */
export interface MsgIBCSend {
    /** the channel by which the packet will be sent */
    channel: string;
    /**
     * Timeout height relative to the current block height.
     * The timeout is disabled when set to 0.
     */
    timeoutHeight: string;
    /**
     * Timeout timestamp (in nanoseconds) relative to the current block timestamp.
     * The timeout is disabled when set to 0.
     */
    timeoutTimestamp: string;
    /**
     * Data is the payload to transfer. We must not make assumption what format or
     * content is in here.
     */
    data: Uint8Array;
}
/** MsgIBCSendResponse */
export interface MsgIBCSendResponse {
    /** Sequence number of the IBC packet sent */
    sequence: string;
}
/** MsgIBCWriteAcknowledgementResponse */
export interface MsgIBCWriteAcknowledgementResponse {
}
/** MsgIBCCloseChannel port and channel need to be owned by the contract */
export interface MsgIBCCloseChannel {
    channel: string;
}
export declare const MsgIBCSend: {
    encode(message: <PERSON><PERSON><PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgIBCSend;
    fromJSON(object: any): MsgIBCSend;
    toJSON(message: MsgIBCSend): unknown;
    create(base?: DeepPartial<MsgIBCSend>): MsgIBCSend;
    fromPartial(object: DeepPartial<MsgIBCSend>): MsgIBCSend;
};
export declare const MsgIBCSendResponse: {
    encode(message: MsgIBCSendResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgIBCSendResponse;
    fromJSON(object: any): MsgIBCSendResponse;
    toJSON(message: MsgIBCSendResponse): unknown;
    create(base?: DeepPartial<MsgIBCSendResponse>): MsgIBCSendResponse;
    fromPartial(object: DeepPartial<MsgIBCSendResponse>): MsgIBCSendResponse;
};
export declare const MsgIBCWriteAcknowledgementResponse: {
    encode(_: MsgIBCWriteAcknowledgementResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgIBCWriteAcknowledgementResponse;
    fromJSON(_: any): MsgIBCWriteAcknowledgementResponse;
    toJSON(_: MsgIBCWriteAcknowledgementResponse): unknown;
    create(base?: DeepPartial<MsgIBCWriteAcknowledgementResponse>): MsgIBCWriteAcknowledgementResponse;
    fromPartial(_: DeepPartial<MsgIBCWriteAcknowledgementResponse>): MsgIBCWriteAcknowledgementResponse;
};
export declare const MsgIBCCloseChannel: {
    encode(message: MsgIBCCloseChannel, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgIBCCloseChannel;
    fromJSON(object: any): MsgIBCCloseChannel;
    toJSON(message: MsgIBCCloseChannel): unknown;
    create(base?: DeepPartial<MsgIBCCloseChannel>): MsgIBCCloseChannel;
    fromPartial(object: DeepPartial<MsgIBCCloseChannel>): MsgIBCCloseChannel;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
