import { MsgBase } from '../../MsgBase.js';
import { CosmosBaseV1Beta1Coin, InjectiveAuctionV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgBid {
    interface Params {
        round: number;
        injectiveAddress: string;
        amount: {
            denom: string;
            amount: string;
        };
    }
    type Proto = InjectiveAuctionV1Beta1Tx.MsgBid;
}
/**
 * @category Messages
 */
export default class MsgBid extends MsgBase<MsgBid.Params, MsgBid.Proto> {
    static fromJSON(params: MsgBid.Params): MsgBid;
    toProto(): InjectiveAuctionV1Beta1Tx.MsgBid;
    toData(): {
        sender: string;
        bidAmount: CosmosBaseV1Beta1Coin.Coin | undefined;
        round: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            bid_amount: CosmosBaseV1Beta1Coin.Coin | undefined;
            round: string;
        };
    };
    toDirectSign(): {
        type: string;
        message: InjectiveAuctionV1Beta1Tx.MsgBid;
    };
    toWeb3Gw(): {
        sender: string;
        bid_amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        round: string;
        '@type': string;
    };
    toBinary(): Uint8Array;
}
