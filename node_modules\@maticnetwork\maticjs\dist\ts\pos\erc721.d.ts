import { IPOSClientConfig, IPOSContracts, ITransactionOption } from "../interfaces";
import { Web3SideChainClient } from "../utils";
import { POSToken } from "./pos_token";
import { TYPE_AMOUNT } from "../types";
export declare class ERC721 extends POSToken {
    constructor(tokenAddress: string, isParent: boolean, client: Web3SideChainClient<IPOSClientConfig>, getContracts: () => IPOSContracts);
    private validateMany_;
    /**
     * get tokens count for the user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [options]
     * @returns
     * @memberof ERC721
     */
    getTokensCount(userAddress: string, options?: ITransactionOption): Promise<number>;
    /**
     * returns token id on supplied index for user
     *
     * @param {number} index
     * @param {string} userAddress
     * @param {ITransactionOption} [options]
     * @returns
     * @memberof ERC721
     */
    getTokenIdAtIndexForUser(index: number, userAddress: string, options?: ITransactionOption): Promise<string>;
    /**
     * get all tokens for user
     *
     * @param {string} userAddress
     * @param {*} [limit=Infinity]
     * @returns
     * @memberof ERC721
     */
    getAllTokens(userAddress: string, limit?: number): Promise<any[]>;
    isApproved(tokenId: string, option?: ITransactionOption): Promise<boolean>;
    isApprovedAll(userAddress: string, option?: ITransactionOption): Promise<boolean>;
    approve(tokenId: TYPE_AMOUNT, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    approveAll(option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    deposit(tokenId: TYPE_AMOUNT, userAddress: string, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    depositMany(tokenIds: TYPE_AMOUNT[], userAddress: string, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    withdrawStart(tokenId: TYPE_AMOUNT, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    withdrawStartWithMetaData(tokenId: TYPE_AMOUNT, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    withdrawStartMany(tokenIds: TYPE_AMOUNT[], option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    withdrawExit(burnTransactionHash: string, option?: ITransactionOption): any;
    withdrawExitOnIndex(burnTransactionHash: string, index: number, option?: ITransactionOption): any;
    withdrawExitFaster(burnTransactionHash: string, option?: ITransactionOption): any;
    isWithdrawExited(txHash: string): Promise<boolean>;
    isWithdrawExitedMany(txHash: string): Promise<boolean>;
    isWithdrawExitedOnIndex(txHash: string, index: number): Promise<boolean>;
    /**
     * transfer to another user
     *
     * @param {string} tokenId
     * @param {string} from
     * @param {string} to
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC721
     */
    transfer(tokenId: string, from: string, to: string, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
}
