import { AuthorityMetadata, TokenFactoryModuleParams, TokenFactoryModuleState } from '../types/tokenfactory.js';
import { InjectiveTokenFactoryV1Beta1Query } from '@injectivelabs/core-proto-ts';
/**
 * @category Chain Grpc Transformer
 */
export declare class ChainGrpcTokenFactoryTransformer {
    static moduleParamsResponseToModuleParams(response: InjectiveTokenFactoryV1Beta1Query.QueryParamsResponse): TokenFactoryModuleParams;
    static moduleStateResponseToModuleState(response: InjectiveTokenFactoryV1Beta1Query.QueryModuleStateResponse): TokenFactoryModuleState;
    static denomsCreatorResponseToDenomsString(response: InjectiveTokenFactoryV1Beta1Query.QueryDenomsFromCreatorResponse): string[];
    static authorityMetadataResponseToAuthorityMetadata(response: InjectiveTokenFactoryV1Beta1Query.QueryDenomAuthorityMetadataResponse): AuthorityMetadata;
}
