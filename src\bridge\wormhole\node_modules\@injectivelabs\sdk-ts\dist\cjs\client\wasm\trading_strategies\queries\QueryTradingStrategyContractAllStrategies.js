"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryTradingStrategyContractAllStrategies = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryTradingStrategyContractAllStrategies extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        const payload = (0, index_js_1.toBase64)({
            all_strategies: {
                start_after: this.params.startAfter,
                limit: this.params.limit,
            },
        });
        return payload;
    }
}
exports.QueryTradingStrategyContractAllStrategies = QueryTradingStrategyContractAllStrategies;
