{"name": "secp256k1", "version": "4.0.4", "description": "This module provides native bindings to ecdsa secp256k1 functions", "keywords": ["ec", "ecdh", "ecdsa", "secp256k1"], "bugs": {"url": "https://github.com/cryptocoinjs/secp256k1-node/issues"}, "repository": {"type": "git", "url": "https://github.com/cryptocoinjs/secp256k1-node.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> (https://github.com/kumavis)", "<PERSON> (https://github.com/phpb-com)", "<PERSON><PERSON> <<EMAIL>> (http://kagami.genshiken.org/)", "ethers (https://github.com/ethers)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/fanatid)"], "main": "./index.js", "browser": {"./index.js": "./elliptic.js"}, "scripts": {"install": "node-gyp-build || exit 0"}, "dependencies": {"elliptic": "^6.5.7", "node-addon-api": "^5.0.0", "node-gyp-build": "^4.2.0"}, "engines": {"node": ">=18.0.0"}, "gypfile": true}