import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
export declare const protobufPackage = "injective.ocr.v1beta1";
export interface Params {
    /** Native denom for LINK coin in the bank keeper */
    linkDenom: string;
    /** The block number interval at which payouts are made */
    payoutBlockInterval: string;
    /** The admin for the OCR module */
    moduleAdmin: string;
}
export interface FeedConfig {
    /** signers ith element is address ith oracle uses to sign a report */
    signers: string[];
    /**
     * transmitters ith element is address ith oracle uses to transmit a report
     * via the transmit method
     */
    transmitters: string[];
    /**
     * f maximum number of faulty/dishonest oracles the protocol can tolerate
     * while still working correctly
     */
    f: number;
    /** onchain_config serialized data with reporting plugin params on chain. */
    onchainConfig: Uint8Array;
    /**
     * offchain_config_version version of the serialization format used for
     * "offchain_config" parameter
     */
    offchainConfigVersion: string;
    /**
     * offchain_config serialized data used by oracles to configure their offchain
     * operation
     */
    offchainConfig: Uint8Array;
    /** feed-specific params for the Cosmos module. */
    moduleParams: ModuleParams | undefined;
}
export interface FeedConfigInfo {
    latestConfigDigest: Uint8Array;
    f: number;
    n: number;
    /**
     * config_count ordinal number of this config setting among all config
     * settings
     */
    configCount: string;
    latestConfigBlockNumber: string;
}
export interface ModuleParams {
    /** feed_id is an unique ID for the target of this config */
    feedId: string;
    /** lowest answer the median of a report is allowed to be */
    minAnswer: string;
    /** highest answer the median of a report is allowed to be */
    maxAnswer: string;
    /** Fixed LINK reward for each observer */
    linkPerObservation: string;
    /** Fixed LINK reward for transmitter */
    linkPerTransmission: string;
    /** Native denom for LINK coin in the bank keeper */
    linkDenom: string;
    /** Enables unique reports */
    uniqueReports: boolean;
    /**
     * short human-readable description of observable this feed's answers pertain
     * to
     */
    description: string;
    /** feed administrator */
    feedAdmin: string;
    /** feed billing administrator */
    billingAdmin: string;
}
export interface ContractConfig {
    /**
     * config_count ordinal number of this config setting among all config
     * settings
     */
    configCount: string;
    /** signers ith element is address ith oracle uses to sign a report */
    signers: string[];
    /**
     * transmitters ith element is address ith oracle uses to transmit a report
     * via the transmit method
     */
    transmitters: string[];
    /**
     * f maximum number of faulty/dishonest oracles the protocol can tolerate
     * while still working correctly
     */
    f: number;
    /** onchain_config serialized data with reporting plugin params on chain. */
    onchainConfig: Uint8Array;
    /**
     * offchain_config_version version of the serialization format used for
     * "offchain_config" parameter
     */
    offchainConfigVersion: string;
    /**
     * offchain_config serialized data used by oracles to configure their offchain
     * operation
     */
    offchainConfig: Uint8Array;
}
export interface SetConfigProposal {
    title: string;
    description: string;
    config: FeedConfig | undefined;
}
export interface FeedProperties {
    /** feed_id is an unique ID for the target of this config */
    feedId: string;
    /**
     * f maximum number of faulty/dishonest oracles the protocol can tolerate
     * while still working correctly
     */
    f: number;
    /** onchain_config serialized data with reporting plugin params on chain. */
    onchainConfig: Uint8Array;
    /**
     * offchain_config_version version of the serialization format used for
     * "offchain_config" parameter
     */
    offchainConfigVersion: string;
    /**
     * offchain_config serialized data used by oracles to configure their offchain
     * operation
     */
    offchainConfig: Uint8Array;
    /** lowest answer the median of a report is allowed to be */
    minAnswer: string;
    /** highest answer the median of a report is allowed to be */
    maxAnswer: string;
    /** Fixed LINK reward for each observer */
    linkPerObservation: string;
    /** Fixed LINK reward for transmitter */
    linkPerTransmission: string;
    /** Enables unique reports */
    uniqueReports: boolean;
    /**
     * short human-readable description of observable this feed's answers pertain
     * to
     */
    description: string;
}
export interface SetBatchConfigProposal {
    title: string;
    description: string;
    /** signers ith element is address ith oracle uses to sign a report */
    signers: string[];
    /**
     * transmitters ith element is address ith oracle uses to transmit a report
     * via the transmit method
     */
    transmitters: string[];
    /** Native denom for LINK coin in the bank keeper */
    linkDenom: string;
    /** feed properties */
    feedProperties: FeedProperties[];
}
export interface OracleObservationsCounts {
    counts: number[];
}
/** LINK-INJ-denominated reimbursements for gas used by transmitters. */
export interface GasReimbursements {
    reimbursements: Coin[];
}
export interface Payee {
    transmitterAddr: string;
    paymentAddr: string;
}
/**
 * Transmission records the median answer from the transmit transaction at
 * time timestamp
 */
export interface Transmission {
    answer: string;
    /** when were observations made offchain */
    observationsTimestamp: string;
    /** when was report received onchain */
    transmissionTimestamp: string;
}
export interface EpochAndRound {
    epoch: string;
    round: string;
}
export interface Report {
    observationsTimestamp: string;
    /** ith element is the index of the ith observer */
    observers: Uint8Array;
    /** ith element is the ith observation */
    observations: string[];
}
export interface ReportToSign {
    configDigest: Uint8Array;
    epoch: string;
    round: string;
    extraHash: Uint8Array;
    /** Opaque report */
    report: Uint8Array;
}
export interface EventOraclePaid {
    transmitterAddr: string;
    payeeAddr: string;
    amount: Coin | undefined;
}
export interface EventAnswerUpdated {
    current: string;
    roundId: string;
    updatedAt: Date | undefined;
}
export interface EventNewRound {
    roundId: string;
    /** address of starter */
    startedBy: string;
    startedAt: Date | undefined;
}
export interface EventTransmitted {
    configDigest: Uint8Array;
    epoch: string;
}
export interface EventNewTransmission {
    feedId: string;
    aggregatorRoundId: number;
    answer: string;
    transmitter: string;
    observationsTimestamp: string;
    observations: string[];
    observers: Uint8Array;
    configDigest: Uint8Array;
    epochAndRound: EpochAndRound | undefined;
}
export interface EventConfigSet {
    /** hash of the config */
    configDigest: Uint8Array;
    /**
     * previous_config_block_number block in which the previous config was set, to
     * simplify historic analysis
     */
    previousConfigBlockNumber: string;
    config: FeedConfig | undefined;
    configInfo: FeedConfigInfo | undefined;
}
export declare const Params: {
    encode(message: Params, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Params;
    fromJSON(object: any): Params;
    toJSON(message: Params): unknown;
    create(base?: DeepPartial<Params>): Params;
    fromPartial(object: DeepPartial<Params>): Params;
};
export declare const FeedConfig: {
    encode(message: FeedConfig, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeedConfig;
    fromJSON(object: any): FeedConfig;
    toJSON(message: FeedConfig): unknown;
    create(base?: DeepPartial<FeedConfig>): FeedConfig;
    fromPartial(object: DeepPartial<FeedConfig>): FeedConfig;
};
export declare const FeedConfigInfo: {
    encode(message: FeedConfigInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeedConfigInfo;
    fromJSON(object: any): FeedConfigInfo;
    toJSON(message: FeedConfigInfo): unknown;
    create(base?: DeepPartial<FeedConfigInfo>): FeedConfigInfo;
    fromPartial(object: DeepPartial<FeedConfigInfo>): FeedConfigInfo;
};
export declare const ModuleParams: {
    encode(message: ModuleParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ModuleParams;
    fromJSON(object: any): ModuleParams;
    toJSON(message: ModuleParams): unknown;
    create(base?: DeepPartial<ModuleParams>): ModuleParams;
    fromPartial(object: DeepPartial<ModuleParams>): ModuleParams;
};
export declare const ContractConfig: {
    encode(message: ContractConfig, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ContractConfig;
    fromJSON(object: any): ContractConfig;
    toJSON(message: ContractConfig): unknown;
    create(base?: DeepPartial<ContractConfig>): ContractConfig;
    fromPartial(object: DeepPartial<ContractConfig>): ContractConfig;
};
export declare const SetConfigProposal: {
    encode(message: SetConfigProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetConfigProposal;
    fromJSON(object: any): SetConfigProposal;
    toJSON(message: SetConfigProposal): unknown;
    create(base?: DeepPartial<SetConfigProposal>): SetConfigProposal;
    fromPartial(object: DeepPartial<SetConfigProposal>): SetConfigProposal;
};
export declare const FeedProperties: {
    encode(message: FeedProperties, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeedProperties;
    fromJSON(object: any): FeedProperties;
    toJSON(message: FeedProperties): unknown;
    create(base?: DeepPartial<FeedProperties>): FeedProperties;
    fromPartial(object: DeepPartial<FeedProperties>): FeedProperties;
};
export declare const SetBatchConfigProposal: {
    encode(message: SetBatchConfigProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBatchConfigProposal;
    fromJSON(object: any): SetBatchConfigProposal;
    toJSON(message: SetBatchConfigProposal): unknown;
    create(base?: DeepPartial<SetBatchConfigProposal>): SetBatchConfigProposal;
    fromPartial(object: DeepPartial<SetBatchConfigProposal>): SetBatchConfigProposal;
};
export declare const OracleObservationsCounts: {
    encode(message: OracleObservationsCounts, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OracleObservationsCounts;
    fromJSON(object: any): OracleObservationsCounts;
    toJSON(message: OracleObservationsCounts): unknown;
    create(base?: DeepPartial<OracleObservationsCounts>): OracleObservationsCounts;
    fromPartial(object: DeepPartial<OracleObservationsCounts>): OracleObservationsCounts;
};
export declare const GasReimbursements: {
    encode(message: GasReimbursements, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GasReimbursements;
    fromJSON(object: any): GasReimbursements;
    toJSON(message: GasReimbursements): unknown;
    create(base?: DeepPartial<GasReimbursements>): GasReimbursements;
    fromPartial(object: DeepPartial<GasReimbursements>): GasReimbursements;
};
export declare const Payee: {
    encode(message: Payee, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Payee;
    fromJSON(object: any): Payee;
    toJSON(message: Payee): unknown;
    create(base?: DeepPartial<Payee>): Payee;
    fromPartial(object: DeepPartial<Payee>): Payee;
};
export declare const Transmission: {
    encode(message: Transmission, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Transmission;
    fromJSON(object: any): Transmission;
    toJSON(message: Transmission): unknown;
    create(base?: DeepPartial<Transmission>): Transmission;
    fromPartial(object: DeepPartial<Transmission>): Transmission;
};
export declare const EpochAndRound: {
    encode(message: EpochAndRound, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EpochAndRound;
    fromJSON(object: any): EpochAndRound;
    toJSON(message: EpochAndRound): unknown;
    create(base?: DeepPartial<EpochAndRound>): EpochAndRound;
    fromPartial(object: DeepPartial<EpochAndRound>): EpochAndRound;
};
export declare const Report: {
    encode(message: Report, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Report;
    fromJSON(object: any): Report;
    toJSON(message: Report): unknown;
    create(base?: DeepPartial<Report>): Report;
    fromPartial(object: DeepPartial<Report>): Report;
};
export declare const ReportToSign: {
    encode(message: ReportToSign, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ReportToSign;
    fromJSON(object: any): ReportToSign;
    toJSON(message: ReportToSign): unknown;
    create(base?: DeepPartial<ReportToSign>): ReportToSign;
    fromPartial(object: DeepPartial<ReportToSign>): ReportToSign;
};
export declare const EventOraclePaid: {
    encode(message: EventOraclePaid, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventOraclePaid;
    fromJSON(object: any): EventOraclePaid;
    toJSON(message: EventOraclePaid): unknown;
    create(base?: DeepPartial<EventOraclePaid>): EventOraclePaid;
    fromPartial(object: DeepPartial<EventOraclePaid>): EventOraclePaid;
};
export declare const EventAnswerUpdated: {
    encode(message: EventAnswerUpdated, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAnswerUpdated;
    fromJSON(object: any): EventAnswerUpdated;
    toJSON(message: EventAnswerUpdated): unknown;
    create(base?: DeepPartial<EventAnswerUpdated>): EventAnswerUpdated;
    fromPartial(object: DeepPartial<EventAnswerUpdated>): EventAnswerUpdated;
};
export declare const EventNewRound: {
    encode(message: EventNewRound, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventNewRound;
    fromJSON(object: any): EventNewRound;
    toJSON(message: EventNewRound): unknown;
    create(base?: DeepPartial<EventNewRound>): EventNewRound;
    fromPartial(object: DeepPartial<EventNewRound>): EventNewRound;
};
export declare const EventTransmitted: {
    encode(message: EventTransmitted, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventTransmitted;
    fromJSON(object: any): EventTransmitted;
    toJSON(message: EventTransmitted): unknown;
    create(base?: DeepPartial<EventTransmitted>): EventTransmitted;
    fromPartial(object: DeepPartial<EventTransmitted>): EventTransmitted;
};
export declare const EventNewTransmission: {
    encode(message: EventNewTransmission, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventNewTransmission;
    fromJSON(object: any): EventNewTransmission;
    toJSON(message: EventNewTransmission): unknown;
    create(base?: DeepPartial<EventNewTransmission>): EventNewTransmission;
    fromPartial(object: DeepPartial<EventNewTransmission>): EventNewTransmission;
};
export declare const EventConfigSet: {
    encode(message: EventConfigSet, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventConfigSet;
    fromJSON(object: any): EventConfigSet;
    toJSON(message: EventConfigSet): unknown;
    create(base?: DeepPartial<EventConfigSet>): EventConfigSet;
    fromPartial(object: DeepPartial<EventConfigSet>): EventConfigSet;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
