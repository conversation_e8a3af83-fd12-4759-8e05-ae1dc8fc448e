var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,d,g){a!=Array.prototype&&a!=Object.prototype&&(a[d]=g.value)};$jscomp.getGlobal=function(a){return"undefined"!=typeof window&&window===a?a:"undefined"!=typeof global&&null!=global?global:a};$jscomp.global=$jscomp.getGlobal(this);$jscomp.SYMBOL_PREFIX="jscomp_symbol_";
$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){};$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)};$jscomp.symbolCounter_=0;$jscomp.Symbol=function(a){return $jscomp.SYMBOL_PREFIX+(a||"")+$jscomp.symbolCounter_++};
$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var a=$jscomp.global.Symbol.iterator;a||(a=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("iterator"));"function"!=typeof Array.prototype[a]&&$jscomp.defineProperty(Array.prototype,a,{configurable:!0,writable:!0,value:function(){return $jscomp.arrayIterator(this)}});$jscomp.initSymbolIterator=function(){}};$jscomp.arrayIterator=function(a){var d=0;return $jscomp.iteratorPrototype(function(){return d<a.length?{done:!1,value:a[d++]}:{done:!0}})};
$jscomp.iteratorPrototype=function(a){$jscomp.initSymbolIterator();a={next:a};a[$jscomp.global.Symbol.iterator]=function(){return this};return a};$jscomp.makeIterator=function(a){$jscomp.initSymbolIterator();var d=a[Symbol.iterator];return d?d.call(a):$jscomp.arrayIterator(a)};
$jscomp.polyfill=function(a,d,g,e){if(d){g=$jscomp.global;a=a.split(".");for(e=0;e<a.length-1;e++){var b=a[e];b in g||(g[b]={});g=g[b]}a=a[a.length-1];e=g[a];d=d(e);d!=e&&null!=d&&$jscomp.defineProperty(g,a,{configurable:!0,writable:!0,value:d})}};$jscomp.FORCE_POLYFILL_PROMISE=!1;
$jscomp.polyfill("Promise",function(a){function d(){this.batch_=null}function g(c){return c instanceof b?c:new b(function(a,b){a(c)})}if(a&&!$jscomp.FORCE_POLYFILL_PROMISE)return a;d.prototype.asyncExecute=function(c){null==this.batch_&&(this.batch_=[],this.asyncExecuteBatch_());this.batch_.push(c);return this};d.prototype.asyncExecuteBatch_=function(){var c=this;this.asyncExecuteFunction(function(){c.executeBatch_()})};var e=$jscomp.global.setTimeout;d.prototype.asyncExecuteFunction=function(c){e(c,
0)};d.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var c=this.batch_;this.batch_=[];for(var a=0;a<c.length;++a){var b=c[a];delete c[a];try{b()}catch(l){this.asyncThrow_(l)}}}this.batch_=null};d.prototype.asyncThrow_=function(a){this.asyncExecuteFunction(function(){throw a;})};var b=function(a){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];var c=this.createResolveAndReject_();try{a(c.resolve,c.reject)}catch(h){c.reject(h)}};b.prototype.createResolveAndReject_=
function(){function a(a){return function(c){b||(b=!0,a.call(t,c))}}var t=this,b=!1;return{resolve:a(this.resolveTo_),reject:a(this.reject_)}};b.prototype.resolveTo_=function(a){if(a===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(a instanceof b)this.settleSameAsPromise_(a);else{a:switch(typeof a){case "object":var c=null!=a;break a;case "function":c=!0;break a;default:c=!1}c?this.resolveToNonPromiseObj_(a):this.fulfill_(a)}};b.prototype.resolveToNonPromiseObj_=function(a){var c=
void 0;try{c=a.then}catch(h){this.reject_(h);return}"function"==typeof c?this.settleSameAsThenable_(c,a):this.fulfill_(a)};b.prototype.reject_=function(a){this.settle_(2,a)};b.prototype.fulfill_=function(a){this.settle_(1,a)};b.prototype.settle_=function(a,b){if(0!=this.state_)throw Error("Cannot settle("+a+", "+b|"): Promise already settled in state"+this.state_);this.state_=a;this.result_=b;this.executeOnSettledCallbacks_()};b.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var a=
this.onSettledCallbacks_,b=0;b<a.length;++b)a[b].call(),a[b]=null;this.onSettledCallbacks_=null}};var n=new d;b.prototype.settleSameAsPromise_=function(a){var b=this.createResolveAndReject_();a.callWhenSettled_(b.resolve,b.reject)};b.prototype.settleSameAsThenable_=function(a,b){var c=this.createResolveAndReject_();try{a.call(b,c.resolve,c.reject)}catch(l){c.reject(l)}};b.prototype.then=function(a,d){function c(a,b){return"function"==typeof a?function(b){try{l(a(b))}catch(m){f(m)}}:b}var l,f,t=new b(function(a,
b){l=a;f=b});this.callWhenSettled_(c(a,l),c(d,f));return t};b.prototype.catch=function(a){return this.then(void 0,a)};b.prototype.callWhenSettled_=function(a,b){function c(){switch(d.state_){case 1:a(d.result_);break;case 2:b(d.result_);break;default:throw Error("Unexpected state: "+d.state_);}}var d=this;null==this.onSettledCallbacks_?n.asyncExecute(c):this.onSettledCallbacks_.push(function(){n.asyncExecute(c)})};b.resolve=g;b.reject=function(a){return new b(function(b,c){c(a)})};b.race=function(a){return new b(function(b,
c){for(var d=$jscomp.makeIterator(a),f=d.next();!f.done;f=d.next())g(f.value).callWhenSettled_(b,c)})};b.all=function(a){var c=$jscomp.makeIterator(a),d=c.next();return d.done?g([]):new b(function(a,b){function f(b){return function(c){e[b]=c;k--;0==k&&a(e)}}var e=[],k=0;do e.push(void 0),k++,g(d.value).callWhenSettled_(f(e.length-1),b),d=c.next();while(!d.done)})};return b},"es6","es3");
(function e$jscomp$0(d,g,e){function b(c,h){if(!g[c]){if(!d[c]){var l="function"==typeof require&&require;if(!h&&l)return l(c,!0);if(n)return n(c,!0);h=Error("Cannot find module '"+c+"'");throw h.code="MODULE_NOT_FOUND",h;}h=g[c]={exports:{}};d[c][0].call(h.exports,function(f){var e=d[c][1][f];return b(e?e:f)},h,h.exports,e$jscomp$0,d,g,e)}return g[c].exports}for(var n="function"==typeof require&&require,c=0;c<e.length;c++)b(e[c]);return b})({1:[function(a,d,g){function e(){throw Error("setTimeout has not been defined");
}function b(){throw Error("clearTimeout has not been defined");}function n(a){if(q===setTimeout)return setTimeout(a,0);if((q===e||!q)&&setTimeout)return q=setTimeout,setTimeout(a,0);try{return q(a,0)}catch(w){try{return q.call(null,a,0)}catch(x){return q.call(this,a,0)}}}function c(a){if(p===clearTimeout)return clearTimeout(a);if((p===b||!p)&&clearTimeout)return p=clearTimeout,clearTimeout(a);try{return p(a)}catch(w){try{return p.call(null,a)}catch(x){return p.call(this,a)}}}function t(){r&&m&&(r=
!1,m.length?k=m.concat(k):u=-1,k.length&&h())}function h(){if(!r){var a=n(t);r=!0;for(var b=k.length;b;){m=k;for(k=[];++u<b;)m&&m[u].run();u=-1;b=k.length}m=null;r=!1;c(a)}}function l(a,b){this.fun=a;this.array=b}function f(){}a=d.exports={};try{var q="function"===typeof setTimeout?setTimeout:e}catch(v){q=e}try{var p="function"===typeof clearTimeout?clearTimeout:b}catch(v){p=b}var k=[],r=!1,m,u=-1;a.nextTick=function(a){var b=Array(arguments.length-1);if(1<arguments.length)for(var c=1;c<arguments.length;c++)b[c-
1]=arguments[c];k.push(new l(a,b));1!==k.length||r||n(h)};l.prototype.run=function(){this.fun.apply(null,this.array)};a.title="browser";a.browser=!0;a.env={};a.argv=[];a.version="";a.versions={};a.on=f;a.addListener=f;a.once=f;a.off=f;a.removeListener=f;a.removeAllListeners=f;a.emit=f;a.prependListener=f;a.prependOnceListener=f;a.listeners=function(a){return[]};a.binding=function(a){throw Error("process.binding is not supported");};a.cwd=function(){return"/"};a.chdir=function(a){throw Error("process.chdir is not supported");
};a.umask=function(){return 0}},{}],2:[function(a,d,g){(function(a){d.exports=function(b){if(a.browser)var d=b.xhr;else{var c=b.express;var e=b.cors;d=b.request;var g=b["body-parser"]}return{api:function(a,b){return new Promise(function(d,f){function k(a,c){try{var d=a.url,e=d.indexOf("(");if(-1!==e){var f=d.slice(1,e);var g=JSON.parse("["+decodeURIComponent(d.slice(e+1,-1))+"]")}else f=d.slice(1),g=[a.body];var h=b[f].apply(null,g);h.then?h.then(function(a){return c.json(a)}):c.json(h)}catch(y){c.send("null")}}
var h=c();h.use(g.json());h.use(e());h.use(function(a,b,c,d){"invalid json"===a.message?c.send("null"):d()});h.get("*",k);h.post("*",k);h.listen(a,d).on("error",f)})},at:function(a){return new Proxy({},{get:function(b,c){return function(){var b=[].slice.call(arguments);return new Promise(function(e,f){d.post({uri:a+"/"+c+"("+JSON.stringify(b).slice(1,-1)+")"},function(a,b,c){a?f(a):e(JSON.parse(c))})})}}})}}}}).call(this,a("_process"))},{_process:1}]},{},[2]);
