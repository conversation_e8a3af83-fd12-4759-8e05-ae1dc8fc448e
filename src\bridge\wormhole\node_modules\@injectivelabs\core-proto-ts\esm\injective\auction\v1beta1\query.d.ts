import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { LastAuctionResult, Params } from "./auction";
import { GenesisState } from "./genesis";
export declare const protobufPackage = "injective.auction.v1beta1";
/**
 * QueryAuctionParamsRequest is the request type for the Query/AuctionParams RPC
 * method.
 */
export interface QueryAuctionParamsRequest {
}
/**
 * QueryAuctionParamsRequest is the response type for the Query/AuctionParams
 * RPC method.
 */
export interface QueryAuctionParamsResponse {
    params: Params | undefined;
}
/**
 * QueryCurrentAuctionBasketRequest is the request type for the
 * Query/CurrentAuctionBasket RPC method.
 */
export interface QueryCurrentAuctionBasketRequest {
}
/**
 * QueryCurrentAuctionBasketResponse is the response type for the
 * Query/CurrentAuctionBasket RPC method.
 */
export interface QueryCurrentAuctionBasketResponse {
    /** amount describes the amount put on auction */
    amount: Coin[];
    /** auctionRound describes current auction round */
    auctionRound: string;
    /** auctionClosingTime describes auction close time for the round */
    auctionClosingTime: string;
    /** highestBidder describes highest bidder on current round */
    highestBidder: string;
    /** highestBidAmount describes highest bid amount on current round */
    highestBidAmount: string;
}
/**
 * QueryModuleStateRequest is the request type for the Query/AuctionModuleState
 * RPC method.
 */
export interface QueryModuleStateRequest {
}
/**
 * QueryModuleStateResponse is the response type for the
 * Query/AuctionModuleState RPC method.
 */
export interface QueryModuleStateResponse {
    state: GenesisState | undefined;
}
export interface QueryLastAuctionResultRequest {
}
export interface QueryLastAuctionResultResponse {
    lastAuctionResult: LastAuctionResult | undefined;
}
export declare const QueryAuctionParamsRequest: {
    encode(_: QueryAuctionParamsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryAuctionParamsRequest;
    fromJSON(_: any): QueryAuctionParamsRequest;
    toJSON(_: QueryAuctionParamsRequest): unknown;
    create(base?: DeepPartial<QueryAuctionParamsRequest>): QueryAuctionParamsRequest;
    fromPartial(_: DeepPartial<QueryAuctionParamsRequest>): QueryAuctionParamsRequest;
};
export declare const QueryAuctionParamsResponse: {
    encode(message: QueryAuctionParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryAuctionParamsResponse;
    fromJSON(object: any): QueryAuctionParamsResponse;
    toJSON(message: QueryAuctionParamsResponse): unknown;
    create(base?: DeepPartial<QueryAuctionParamsResponse>): QueryAuctionParamsResponse;
    fromPartial(object: DeepPartial<QueryAuctionParamsResponse>): QueryAuctionParamsResponse;
};
export declare const QueryCurrentAuctionBasketRequest: {
    encode(_: QueryCurrentAuctionBasketRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryCurrentAuctionBasketRequest;
    fromJSON(_: any): QueryCurrentAuctionBasketRequest;
    toJSON(_: QueryCurrentAuctionBasketRequest): unknown;
    create(base?: DeepPartial<QueryCurrentAuctionBasketRequest>): QueryCurrentAuctionBasketRequest;
    fromPartial(_: DeepPartial<QueryCurrentAuctionBasketRequest>): QueryCurrentAuctionBasketRequest;
};
export declare const QueryCurrentAuctionBasketResponse: {
    encode(message: QueryCurrentAuctionBasketResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryCurrentAuctionBasketResponse;
    fromJSON(object: any): QueryCurrentAuctionBasketResponse;
    toJSON(message: QueryCurrentAuctionBasketResponse): unknown;
    create(base?: DeepPartial<QueryCurrentAuctionBasketResponse>): QueryCurrentAuctionBasketResponse;
    fromPartial(object: DeepPartial<QueryCurrentAuctionBasketResponse>): QueryCurrentAuctionBasketResponse;
};
export declare const QueryModuleStateRequest: {
    encode(_: QueryModuleStateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateRequest;
    fromJSON(_: any): QueryModuleStateRequest;
    toJSON(_: QueryModuleStateRequest): unknown;
    create(base?: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
    fromPartial(_: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
};
export declare const QueryModuleStateResponse: {
    encode(message: QueryModuleStateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateResponse;
    fromJSON(object: any): QueryModuleStateResponse;
    toJSON(message: QueryModuleStateResponse): unknown;
    create(base?: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
    fromPartial(object: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
};
export declare const QueryLastAuctionResultRequest: {
    encode(_: QueryLastAuctionResultRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryLastAuctionResultRequest;
    fromJSON(_: any): QueryLastAuctionResultRequest;
    toJSON(_: QueryLastAuctionResultRequest): unknown;
    create(base?: DeepPartial<QueryLastAuctionResultRequest>): QueryLastAuctionResultRequest;
    fromPartial(_: DeepPartial<QueryLastAuctionResultRequest>): QueryLastAuctionResultRequest;
};
export declare const QueryLastAuctionResultResponse: {
    encode(message: QueryLastAuctionResultResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryLastAuctionResultResponse;
    fromJSON(object: any): QueryLastAuctionResultResponse;
    toJSON(message: QueryLastAuctionResultResponse): unknown;
    create(base?: DeepPartial<QueryLastAuctionResultResponse>): QueryLastAuctionResultResponse;
    fromPartial(object: DeepPartial<QueryLastAuctionResultResponse>): QueryLastAuctionResultResponse;
};
/** Query defines the gRPC querier service. */
export interface Query {
    /** Retrieves auction params */
    AuctionParams(request: DeepPartial<QueryAuctionParamsRequest>, metadata?: grpc.Metadata): Promise<QueryAuctionParamsResponse>;
    /** Retrieves current auction basket with current highest bid and bidder */
    CurrentAuctionBasket(request: DeepPartial<QueryCurrentAuctionBasketRequest>, metadata?: grpc.Metadata): Promise<QueryCurrentAuctionBasketResponse>;
    /** Retrieves the entire auction module's state */
    AuctionModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
    LastAuctionResult(request: DeepPartial<QueryLastAuctionResultRequest>, metadata?: grpc.Metadata): Promise<QueryLastAuctionResultResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    AuctionParams(request: DeepPartial<QueryAuctionParamsRequest>, metadata?: grpc.Metadata): Promise<QueryAuctionParamsResponse>;
    CurrentAuctionBasket(request: DeepPartial<QueryCurrentAuctionBasketRequest>, metadata?: grpc.Metadata): Promise<QueryCurrentAuctionBasketResponse>;
    AuctionModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
    LastAuctionResult(request: DeepPartial<QueryLastAuctionResultRequest>, metadata?: grpc.Metadata): Promise<QueryLastAuctionResultResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryAuctionParamsDesc: UnaryMethodDefinitionish;
export declare const QueryCurrentAuctionBasketDesc: UnaryMethodDefinitionish;
export declare const QueryAuctionModuleStateDesc: UnaryMethodDefinitionish;
export declare const QueryLastAuctionResultDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
