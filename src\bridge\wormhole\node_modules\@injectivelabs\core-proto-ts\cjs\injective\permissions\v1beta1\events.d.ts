import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
export declare const protobufPackage = "injective.permissions.v1beta1";
export interface EventSetVoucher {
    addr: string;
    voucher: Coin | undefined;
}
export declare const EventSetVoucher: {
    encode(message: EventSetVoucher, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSetVoucher;
    fromJSON(object: any): EventSetVoucher;
    toJSON(message: EventSetVoucher): unknown;
    create(base?: DeepPartial<EventSetVoucher>): EventSetVoucher;
    fromPartial(object: DeepPartial<EventSetVoucher>): EventSetVoucher;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
