{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/node/util.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AAErC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAC7C,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAA;AAEnD,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAA;AACxC,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAA;AAC9C,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAA;AAIpC,MAAM,UAAU,aAAa,CAAC,GAAiB;IAC7C,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE;QACrB,OAAO,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;KACjC;SAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACtC,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;SACzD;QACD,OAAO,IAAI,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;KACnE;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;KAChC;AACH,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,CAAgC;IACxD,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,UAAU,CAAC,CAAA;AACvD,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,IAAgB;IACzC,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACrD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;KAChC;IACD,OAAO,aAAa,CAAC,WAAW,CAAC,CAAA;AACnC,CAAC"}