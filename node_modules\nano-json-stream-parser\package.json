{"name": "nano-json-stream-parser", "version": "0.1.2", "description": "Lightweight, streamed JSON parser in 748 bytes", "main": "dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/maiavictor/nano-json-stream-parser.git"}, "keywords": ["json", "parse"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/maiavictor/nano-json-stream-parser/issues"}, "homepage": "https://github.com/maiavictor/nano-json-stream-parser#readme"}