"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
/**
 * @category Messages
 */
class MsgUpdateActorRoles extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgUpdateActorRoles(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateActorRoles.create();
        message.sender = params.sender;
        message.denom = params.denom;
        const roleActorsToAdd = params.roleActorsToAdd.map((roleActors) => {
            const actor = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.RoleActors.create();
            actor.role = roleActors.role;
            actor.actors = roleActors.actors;
            return actor;
        }) || [];
        message.roleActorsToAdd = roleActorsToAdd;
        const roleActorsToRevoke = params.roleActorsToRevoke.map((roleActors) => {
            const actor = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.RoleActors.create();
            actor.role = roleActors.role;
            actor.actors = roleActors.actors;
            return actor;
        }) || [];
        message.roleActorsToRevoke = roleActorsToRevoke;
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateActorRoles.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.permissions.v1beta1.MsgUpdateActorRoles',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'permissions/MsgUpdateActorRoles',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.permissions.v1beta1.MsgUpdateActorRoles',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.permissions.v1beta1.MsgUpdateActorRoles',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateActorRoles.encode(this.toProto()).finish();
    }
}
exports.default = MsgUpdateActorRoles;
