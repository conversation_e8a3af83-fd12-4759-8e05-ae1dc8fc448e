@echo off
title IP Tools Manager

:menu
cls
echo.
echo ========================================
echo           IP Tools Manager
echo ========================================
echo.
echo 1. Install V2Ray
echo 2. Update IP Config
echo 3. Start Auto IP Rotation (60s)
echo 4. Start Auto IP Rotation (Custom)
echo 5. Switch to Hong Kong
echo 6. Switch to Japan
echo 7. List All Proxies
echo 8. Show Status
echo 9. Stop All Proxies
echo 0. Exit
echo.
set /p choice=Please select an option (0-9): 

if "%choice%"=="1" goto install_v2ray
if "%choice%"=="2" goto update_config
if "%choice%"=="3" goto start_rotation_60
if "%choice%"=="4" goto start_rotation_custom
if "%choice%"=="5" goto switch_hk
if "%choice%"=="6" goto switch_jp
if "%choice%"=="7" goto list_proxies
if "%choice%"=="8" goto show_status
if "%choice%"=="9" goto stop_proxies
if "%choice%"=="0" goto exit
goto menu

:install_v2ray
echo.
echo Installing V2Ray...
call install_v2ray.bat
pause
goto menu

:update_config
echo.
echo Updating IP configuration...
python src\utils\ip\config_creator.py
pause
goto menu

:start_rotation_60
echo.
echo Starting auto IP rotation (60 seconds interval)...
python src\utils\ip\v2ray_rotator.py start 60
pause
goto menu

:start_rotation_custom
echo.
set /p interval=Enter rotation interval in seconds: 
echo Starting auto IP rotation (%interval% seconds interval)...
python src\utils\ip\v2ray_rotator.py start %interval%
pause
goto menu

:switch_hk
echo.
echo Switching to Hong Kong proxy...
python src\utils\ip\v2ray_rotator.py switch 香港
pause
goto menu

:switch_jp
echo.
echo Switching to Japan proxy...
python src\utils\ip\v2ray_rotator.py switch 日本
pause
goto menu

:list_proxies
echo.
echo Listing all proxies...
python src\utils\ip\v2ray_rotator.py list
pause
goto menu

:show_status
echo.
echo Showing proxy status...
python src\utils\ip\v2ray_rotator.py status
pause
goto menu

:stop_proxies
echo.
echo Stopping all proxies...
python src\utils\ip\v2ray_rotator.py stop
python src\utils\ip\auto_ip_rotator.py stop
echo All proxies stopped.
pause
goto menu

:exit
echo.
echo Goodbye!
exit
