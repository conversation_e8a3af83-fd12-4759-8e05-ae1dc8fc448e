#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理检查工具

该脚本用于检查代理列表的有效性，并输出结果。
"""

import os
import sys
import argparse
import logging
from tabulate import tabulate

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.utils.proxy import ProxyManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('proxy_check')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='代理检查工具')
    
    parser.add_argument('-f', '--file', required=True,
                        help='代理列表文件路径，每行一个代理URL')
    
    parser.add_argument('-u', '--url', default='https://www.google.com',
                        help='用于测试的URL，默认为 https://www.google.com')
    
    parser.add_argument('-t', '--timeout', type=int, default=5,
                        help='请求超时时间，默认为5秒')
    
    parser.add_argument('-a', '--all', action='store_true',
                        help='检查所有代理并输出详细结果')
    
    parser.add_argument('-o', '--output',
                        help='将有效代理保存到输出文件')
    
    return parser.parse_args()

def check_single_proxy(proxy_manager, proxy, test_url, timeout):
    """检查单个代理并返回结果"""
    is_valid = proxy_manager.check_proxy(proxy, test_url, timeout)
    return {
        'proxy': proxy,
        'status': '有效' if is_valid else '无效',
        'valid': is_valid
    }

def check_all_proxies(proxy_manager, test_url, timeout):
    """检查所有代理并返回结果列表"""
    results = []
    for proxy in proxy_manager.proxies:
        result = check_single_proxy(proxy_manager, proxy, test_url, timeout)
        results.append(result)
    
    return results

def save_valid_proxies(results, output_file):
    """保存有效代理到文件"""
    valid_proxies = [r['proxy'] for r in results if r['valid']]
    try:
        with open(output_file, 'w') as f:
            for proxy in valid_proxies:
                f.write(f"{proxy}\n")
        logger.info(f"已将 {len(valid_proxies)} 个有效代理保存到 {output_file}")
    except Exception as e:
        logger.error(f"保存到文件失败: {str(e)}")

def main():
    """主函数"""
    args = parse_args()
    
    if not os.path.exists(args.file):
        logger.error(f"代理文件不存在: {args.file}")
        return 1
    
    # 创建代理管理器
    proxy_manager = ProxyManager(args.file)
    
    if not proxy_manager.proxies:
        logger.error("没有找到可用的代理")
        return 1
    
    logger.info(f"加载了 {len(proxy_manager.proxies)} 个代理，开始检查...")
    
    # 检查所有代理
    if args.all:
        results = check_all_proxies(proxy_manager, args.url, args.timeout)
        
        # 创建表格数据
        table_data = []
        for i, result in enumerate(results, 1):
            table_data.append([
                i,
                result['proxy'],
                result['status']
            ])
        
        # 输出表格
        headers = ["序号", "代理地址", "状态"]
        print("\n" + tabulate(table_data, headers=headers, tablefmt="grid"))
        
        # 统计结果
        valid_count = sum(1 for r in results if r['valid'])
        invalid_count = len(results) - valid_count
        
        print(f"\n检查结果: 总共 {len(results)} 个代理，{valid_count} 个有效，{invalid_count} 个无效")
        
        # 保存有效代理到输出文件
        if args.output:
            save_valid_proxies(results, args.output)
    
    else:
        # 只检查代理是否可用
        valid_count = proxy_manager.check_all_proxies(args.url, args.timeout)
        print(f"\n检查结果: 总共 {len(proxy_manager.proxies)} 个代理，{valid_count} 个有效")
        
        # 保存有效代理到输出文件
        if args.output:
            try:
                with open(args.output, 'w') as f:
                    for proxy in proxy_manager.proxies:
                        f.write(f"{proxy}\n")
                logger.info(f"已将 {len(proxy_manager.proxies)} 个有效代理保存到 {args.output}")
            except Exception as e:
                logger.error(f"保存到文件失败: {str(e)}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 