"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpRequestException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
class HttpRequestException extends base_js_1.ConcreteException {
    static errorClass = 'HttpRequestException';
    method = index_js_1.HttpRequestMethod.Get;
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.HttpRequest;
        this.method = context
            ? context.method || index_js_1.HttpRequestMethod.Get
            : index_js_1.HttpRequestMethod.Get;
        this.context = context?.context || 'Unknown';
    }
    parse() {
        this.setName(HttpRequestException.errorClass);
    }
}
exports.HttpRequestException = HttpRequestException;
