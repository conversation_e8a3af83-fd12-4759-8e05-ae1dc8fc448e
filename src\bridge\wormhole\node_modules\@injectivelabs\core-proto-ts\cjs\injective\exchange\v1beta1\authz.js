"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchUpdateOrdersAuthz = exports.BatchCancelDerivativeOrdersAuthz = exports.CancelDerivativeOrderAuthz = exports.BatchCreateDerivativeLimitOrdersAuthz = exports.CreateDerivativeMarketOrderAuthz = exports.CreateDerivativeLimitOrderAuthz = exports.BatchCancelSpotOrdersAuthz = exports.CancelSpotOrderAuthz = exports.BatchCreateSpotLimitOrdersAuthz = exports.CreateSpotMarketOrderAuthz = exports.CreateSpotLimitOrderAuthz = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "injective.exchange.v1beta1";
function createBaseCreateSpotLimitOrderAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.CreateSpotLimitOrderAuthz = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCreateSpotLimitOrderAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CreateSpotLimitOrderAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCreateSpotLimitOrderAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseCreateSpotMarketOrderAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.CreateSpotMarketOrderAuthz = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCreateSpotMarketOrderAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CreateSpotMarketOrderAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCreateSpotMarketOrderAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseBatchCreateSpotLimitOrdersAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.BatchCreateSpotLimitOrdersAuthz = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchCreateSpotLimitOrdersAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchCreateSpotLimitOrdersAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseBatchCreateSpotLimitOrdersAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseCancelSpotOrderAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.CancelSpotOrderAuthz = {
    encode: function (message, writer) {
        var e_4, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCancelSpotOrderAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CancelSpotOrderAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCancelSpotOrderAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseBatchCancelSpotOrdersAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.BatchCancelSpotOrdersAuthz = {
    encode: function (message, writer) {
        var e_5, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchCancelSpotOrdersAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchCancelSpotOrdersAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseBatchCancelSpotOrdersAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseCreateDerivativeLimitOrderAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.CreateDerivativeLimitOrderAuthz = {
    encode: function (message, writer) {
        var e_6, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCreateDerivativeLimitOrderAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CreateDerivativeLimitOrderAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCreateDerivativeLimitOrderAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseCreateDerivativeMarketOrderAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.CreateDerivativeMarketOrderAuthz = {
    encode: function (message, writer) {
        var e_7, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCreateDerivativeMarketOrderAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CreateDerivativeMarketOrderAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCreateDerivativeMarketOrderAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseBatchCreateDerivativeLimitOrdersAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.BatchCreateDerivativeLimitOrdersAuthz = {
    encode: function (message, writer) {
        var e_8, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchCreateDerivativeLimitOrdersAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchCreateDerivativeLimitOrdersAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseBatchCreateDerivativeLimitOrdersAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseCancelDerivativeOrderAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.CancelDerivativeOrderAuthz = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCancelDerivativeOrderAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CancelDerivativeOrderAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCancelDerivativeOrderAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseBatchCancelDerivativeOrdersAuthz() {
    return { subaccountId: "", marketIds: [] };
}
exports.BatchCancelDerivativeOrdersAuthz = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchCancelDerivativeOrdersAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchCancelDerivativeOrdersAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseBatchCancelDerivativeOrdersAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseBatchUpdateOrdersAuthz() {
    return { subaccountId: "", spotMarkets: [], derivativeMarkets: [] };
}
exports.BatchUpdateOrdersAuthz = {
    encode: function (message, writer) {
        var e_11, _a, e_12, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _c = __values(message.spotMarkets), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_11) throw e_11.error; }
        }
        try {
            for (var _e = __values(message.derivativeMarkets), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchUpdateOrdersAuthz();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.spotMarkets.push(reader.string());
                    break;
                case 3:
                    message.derivativeMarkets.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            spotMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.spotMarkets) ? object.spotMarkets.map(function (e) { return String(e); }) : [],
            derivativeMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeMarkets)
                ? object.derivativeMarkets.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.spotMarkets) {
            obj.spotMarkets = message.spotMarkets.map(function (e) { return e; });
        }
        else {
            obj.spotMarkets = [];
        }
        if (message.derivativeMarkets) {
            obj.derivativeMarkets = message.derivativeMarkets.map(function (e) { return e; });
        }
        else {
            obj.derivativeMarkets = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchUpdateOrdersAuthz.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseBatchUpdateOrdersAuthz();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.spotMarkets = ((_b = object.spotMarkets) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.derivativeMarkets = ((_c = object.derivativeMarkets) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
