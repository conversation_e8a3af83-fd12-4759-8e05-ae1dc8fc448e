import { AptosConfig } from '../../api/aptosConfig.mjs';
import { AccountAddressInput } from '../../core/accountAddress.mjs';
import { P as PublicKey } from '../../publicKey-CJOcUwJK.mjs';
import { AccountAuthenticatorNoAccountAuthenticator, AccountAuthenticatorEd25519, AccountAuthenticatorSingleKey, AccountAuthenticatorMultiKey } from '../authenticator/account.mjs';
import { InputScriptData, InputEntryFunctionDataWithRemoteABI, InputMultiSigDataWithRemoteABI, InputEntryFunctionDataWithABI, InputMultiSigDataWithABI, InputViewFunctionDataWithRemoteABI, InputViewFunctionDataWithABI, AnyTransactionPayloadInstance, InputGenerateTransactionOptions, InputGenerateSingleSignerRawTransactionArgs, InputGenerateMultiAgentRawTransactionArgs, InputSimulateTransactionData, InputSubmitTransactionData } from '../types.mjs';
import { RawTransaction } from '../instances/rawTransaction.mjs';
import { TransactionPayloadScript, TransactionPayloadEntryFunction, TransactionPayloadMultiSig, EntryFunction } from '../instances/transactionPayload.mjs';
import { SimpleTransaction } from '../instances/simpleTransaction.mjs';
import { MultiAgentTransaction } from '../instances/multiAgentTransaction.mjs';
import '../../types/types.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../utils/const.mjs';
import '../../bcs/serializer.mjs';
import '../../core/hex.mjs';
import '../../core/common.mjs';
import '../../bcs/deserializer.mjs';
import '../instances/transactionArgument.mjs';
import '../../core/crypto/signature.mjs';
import '../../core/crypto/ed25519.mjs';
import '../../core/crypto/privateKey.mjs';
import '../../core/crypto/multiEd25519.mjs';
import '../../core/crypto/multiKey.mjs';
import '../../core/crypto/singleKey.mjs';
import '../../core/crypto/secp256k1.mjs';
import '../../bcs/serializable/moveStructs.mjs';
import '../../bcs/serializable/movePrimitives.mjs';
import '../../bcs/serializable/fixedBytes.mjs';
import '../typeTag/index.mjs';
import '../instances/identifier.mjs';
import '../instances/chainId.mjs';
import '../instances/moduleId.mjs';

/**
 * Builds a transaction payload based on the provided arguments and returns a transaction payload.
 * This function uses the RemoteABI by default, but can also utilize a specified ABI.
 * When we call our `generateTransactionPayload` function with the relevant type properties,
 * Typescript can infer the return type based on the appropriate function overload.
 * @param args - The input data for generating the transaction payload.
 * @param args.function - The function to be called, specified in the format "moduleAddress::moduleName::functionName".
 * @param args.functionArguments - The arguments to pass to the function.
 * @param args.typeArguments - The type arguments for the function.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.abi - The ABI to use for the transaction, if not using the RemoteABI.
 *
 * @returns TransactionPayload - The generated transaction payload, which can be of type TransactionPayloadScript,
 * TransactionPayloadMultiSig, or TransactionPayloadEntryFunction.
 * @group Implementation
 * @category Transactions
 */
declare function generateTransactionPayload(args: InputScriptData): Promise<TransactionPayloadScript>;
/**
 * @group Implementation
 * @category Transactions
 */
declare function generateTransactionPayload(args: InputEntryFunctionDataWithRemoteABI): Promise<TransactionPayloadEntryFunction>;
/**
 * @group Implementation
 * @category Transactions
 */
declare function generateTransactionPayload(args: InputMultiSigDataWithRemoteABI): Promise<TransactionPayloadMultiSig>;
/**
 * Generates a transaction payload using the provided ABI and function details.
 * This function helps create a properly structured transaction payload for executing a specific function on a module.
 *
 * @param args - The input data required to generate the transaction payload.
 * @param args.abi - The ABI of the function to be executed.
 * @param args.function - The fully qualified name of the function in the format `moduleAddress::moduleName::functionName`.
 * @param args.typeArguments - An array of type arguments that correspond to the function's type parameters.
 * @param args.functionArguments - An array of arguments to be passed to the function.
 * @param args.multisigAddress - (Optional) The address for a multisig transaction if applicable.
 *
 * @throws Error if the type argument count does not match the ABI or if the number of function arguments is incorrect.
 * @group Implementation
 * @category Transactions
 */
declare function generateTransactionPayloadWithABI(args: InputEntryFunctionDataWithABI): TransactionPayloadEntryFunction;
/**
 * @group Implementation
 * @category Transactions
 */
declare function generateTransactionPayloadWithABI(args: InputMultiSigDataWithABI): TransactionPayloadMultiSig;
/**
 * Generates the payload for a view function call using the provided arguments.
 * This function helps in preparing the necessary data to interact with a specific view function on the blockchain.
 *
 * @param args - The input data required to generate the view function payload.
 * @param args.function - The function identifier in the format "moduleAddress::moduleName::functionName".
 * @param args.aptosConfig - Configuration settings for the Aptos client.
 * @param args.abi - The ABI (Application Binary Interface) of the module.
 *
 * @returns The generated payload for the view function call.
 * @group Implementation
 * @category Transactions
 */
declare function generateViewFunctionPayload(args: InputViewFunctionDataWithRemoteABI): Promise<EntryFunction>;
/**
 * Generates a payload for a view function call using the provided ABI and arguments.
 * This function ensures that the type arguments and function arguments are correctly formatted
 * and match the expected counts as defined in the ABI.
 *
 * @param args - The input data for generating the view function payload.
 * @param args.abi - The ABI of the function to be called.
 * @param args.function - The full name of the function in the format "moduleAddress::moduleName::functionName".
 * @param args.typeArguments - An array of type arguments to be used in the function call.
 * @param args.functionArguments - An array of arguments to be passed to the function.
 *
 * @throws Error if the type argument count does not match the ABI or if the function arguments
 * do not match the expected parameters defined in the ABI.
 * @group Implementation
 * @category Transactions
 */
declare function generateViewFunctionPayloadWithABI(args: InputViewFunctionDataWithABI): EntryFunction;
/**
 * Generates a raw transaction that can be sent to the Aptos network.
 *
 * @param args - The arguments for generating the raw transaction.
 * @param args.aptosConfig - The configuration for the Aptos network.
 * @param args.sender - The transaction's sender account address as a hex input.
 * @param args.payload - The transaction payload, which can be created using generateTransactionPayload().
 * @param args.options - Optional parameters for transaction generation.
 * @param args.feePayerAddress - The address of the fee payer for sponsored transactions.
 *
 * @returns RawTransaction - The generated raw transaction.
 * @group Implementation
 * @category Transactions
 */
declare function generateRawTransaction(args: {
    aptosConfig: AptosConfig;
    sender: AccountAddressInput;
    payload: AnyTransactionPayloadInstance;
    options?: InputGenerateTransactionOptions;
    feePayerAddress?: AccountAddressInput;
}): Promise<RawTransaction>;
/**
 * Generates a transaction based on the provided arguments.
 * This function can create both simple and multi-agent transactions, allowing for flexible transaction handling.
 *
 * @param args - The input arguments for generating the transaction.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.sender - The transaction's sender account address as a hex input.
 * @param args.payload - The transaction payload, which can be created using `generateTransactionPayload()`.
 * @param args.options - Optional. Transaction options object.
 * @param args.secondarySignerAddresses - Optional. An array of addresses for additional signers in a multi-signature transaction.
 * @param args.feePayerAddress - Optional. The address of the fee payer for sponsored transactions.
 * @returns An instance of a transaction, which may include secondary signer addresses and a fee payer address.
 * @group Implementation
 * @category Transactions
 */
declare function buildTransaction(args: InputGenerateSingleSignerRawTransactionArgs): Promise<SimpleTransaction>;
/**
 * @group Implementation
 * @category Transactions
 */
declare function buildTransaction(args: InputGenerateMultiAgentRawTransactionArgs): Promise<MultiAgentTransaction>;
/**
 * Generate a signed transaction for simulation before submitting it to the chain.
 * This function helps in preparing a transaction that can be simulated, allowing users to verify its validity and expected behavior.
 *
 * @param args - The input data required to generate the signed transaction for simulation.
 * @param args.transaction - An Aptos transaction type to sign.
 * @param args.signerPublicKey - The public key of the signer.
 * @param args.secondarySignersPublicKeys - Optional. The public keys of secondary signers if it is a multi-signer transaction.
 * @param args.feePayerPublicKey - Optional. The public key of the fee payer in a sponsored transaction.
 * @param args.options - Optional. Additional options for simulating the transaction.
 *
 * @returns A signed serialized transaction that can be simulated.
 * @group Implementation
 * @category Transactions
 */
declare function generateSignedTransactionForSimulation(args: InputSimulateTransactionData): Uint8Array;
/**
 * @group Implementation
 * @category Transactions
 */
declare function getAuthenticatorForSimulation(publicKey?: PublicKey): AccountAuthenticatorNoAccountAuthenticator | AccountAuthenticatorEd25519 | AccountAuthenticatorSingleKey | AccountAuthenticatorMultiKey;
/**
 * Generate a signed transaction ready for submission to the blockchain.
 * This function prepares the transaction by authenticating the sender and any additional signers based on the provided arguments.
 *
 * @param args - The input data required to generate the signed transaction.
 * @param args.transaction - An Aptos transaction type containing the details of the transaction.
 * @param args.senderAuthenticator - The account authenticator of the transaction sender.
 * @param args.feePayerAuthenticator - The authenticator for the fee payer, required if the transaction has a fee payer address.
 * @param args.additionalSignersAuthenticators - Optional authenticators for additional signers in a multi-signer transaction.
 *
 * @returns A Uint8Array representing the signed transaction in bytes.
 *
 * @throws Error if the feePayerAuthenticator is not provided for a fee payer transaction.
 * @throws Error if additionalSignersAuthenticators are not provided for a multi-signer transaction.
 * @group Implementation
 * @category Transactions
 */
declare function generateSignedTransaction(args: InputSubmitTransactionData): Uint8Array;
/**
 * Hashes the set of values using a SHA-3 256 hash algorithm.
 * @param input - An array of UTF-8 strings or Uint8Array byte arrays to be hashed.
 * @group Implementation
 * @category Transactions
 */
declare function hashValues(input: (Uint8Array | string)[]): Uint8Array;
/**
 * Generates a user transaction hash for the provided transaction payload, which must already have an authenticator.
 * This function helps ensure the integrity and uniqueness of the transaction by producing a hash based on the signed transaction data.
 *
 * @param args - The input data required to submit the transaction.
 * @param args.authenticator - The authenticator for the transaction.
 * @param args.payload - The payload containing the transaction details.
 * @param args.sender - The address of the sender initiating the transaction.
 * @param args.sequenceNumber - The sequence number of the transaction for the sender.
 * @group Implementation
 * @category Transactions
 */
declare function generateUserTransactionHash(args: InputSubmitTransactionData): string;

export { buildTransaction, generateRawTransaction, generateSignedTransaction, generateSignedTransactionForSimulation, generateTransactionPayload, generateTransactionPayloadWithABI, generateUserTransactionHash, generateViewFunctionPayload, generateViewFunctionPayloadWithABI, getAuthenticatorForSimulation, hashValues };
