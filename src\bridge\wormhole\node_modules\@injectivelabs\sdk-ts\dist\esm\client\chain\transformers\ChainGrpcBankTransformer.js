import { ChainGrpcCommonTransformer } from './ChainGrpcCommonTransformer.js';
import { grpcPaginationToPagination } from '../../../utils/pagination.js';
/**
 * @category Chain Grpc Transformer
 */
export class ChainGrpcBankTransformer {
    static metadataToMetadata(metadata) {
        return metadata;
    }
    static moduleParamsResponseToModuleParams(response) {
        const params = response.params;
        return {
            sendEnabledList: params.sendEnabled,
            defaultSendEnabled: params.defaultSendEnabled,
        };
    }
    static denomOwnersResponseToDenomOwners(response) {
        const denomOwners = response.denomOwners;
        const pagination = response.pagination;
        return {
            denomOwners,
            pagination: grpcPaginationToPagination(pagination),
        };
    }
    static totalSupplyResponseToTotalSupply(response) {
        const balances = response.supply;
        const pagination = response.pagination;
        return {
            supply: balances.map(ChainGrpcCommonTransformer.grpcCoinToCoin),
            pagination: grpcPaginationToPagination(pagination),
        };
    }
    static denomsMetadataResponseToDenomsMetadata(response) {
        const metadatas = response.metadatas;
        const pagination = response.pagination;
        return {
            metadatas: metadatas.map(ChainGrpcBankTransformer.metadataToMetadata),
            pagination: grpcPaginationToPagination(pagination),
        };
    }
    static balanceResponseToBalance(response) {
        return ChainGrpcCommonTransformer.grpcCoinToCoin(response.balance);
    }
    static balancesResponseToBalances(response) {
        const balances = response.balances;
        const pagination = response.pagination;
        return {
            balances: balances.map(ChainGrpcCommonTransformer.grpcCoinToCoin),
            pagination: grpcPaginationToPagination(pagination),
        };
    }
}
