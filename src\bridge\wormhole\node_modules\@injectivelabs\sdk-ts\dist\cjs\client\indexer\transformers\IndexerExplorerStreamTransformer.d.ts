import { StreamOperation } from '../../../types/index.js';
import { InjectiveExplorerRpc } from '@injectivelabs/indexer-proto-ts';
/**
 * @category Indexer Stream Transformer
 */
export declare class ExplorerStreamTransformer {
    static blocksStreamCallback: (response: InjectiveExplorerRpc.StreamBlocksResponse) => {
        block: import("../index.js").Block;
        operation: StreamOperation;
    };
    static blocksWithTxsStreamCallback: (response: InjectiveExplorerRpc.StreamBlocksResponse) => {
        block: import("../index.js").BlockWithTxs;
        operation: StreamOperation;
    };
    static transactionsStreamCallback: (response: InjectiveExplorerRpc.StreamTxsResponse) => {
        block: import("../index.js").IndexerStreamTransaction;
        operation: StreamOperation;
    };
}
