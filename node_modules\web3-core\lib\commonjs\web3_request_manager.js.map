{"version": 3, "file": "web3_request_manager.js", "sourceRoot": "", "sources": ["../../src/web3_request_manager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;;;;;;AAEF,6CAOqB;AACrB,8EAA+C;AAC/C,0EAA2C;AAmB3C,2CAA+E;AAC/E,yCAMoB;AACpB,mEAA2D;AAG3D,IAAY,uBAGX;AAHD,WAAY,uBAAuB;IAClC,gEAAqC,CAAA;IACrC,4EAAiD,CAAA;AAClD,CAAC,EAHW,uBAAuB,uCAAvB,uBAAuB,QAGlC;AAED,MAAM,kBAAkB,GAGpB;IACH,YAAY,EAAE,6BAA2C;IACzD,iBAAiB,EAAE,2BAAyC;CAC5D,CAAC;AAEF,MAAa,kBAEX,SAAQ,wCAER;IAKD,YACC,QAA2C,EAC3C,uBAAiC,EACjC,wBAAwD;QAExD,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,IAAA,sBAAS,EAAC,QAAQ,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;QAEvD,IAAI,CAAC,IAAA,sBAAS,EAAC,wBAAwB,CAAC;YAAE,IAAI,CAAC,UAAU,GAAG,wBAAwB,CAAC;IACtF,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,SAAS;QAC1B,OAAO,kBAAkB,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,IAAW,QAAQ;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,kDAAkD;IAClD,IAAW,SAAS;QACnB,OAAO,kBAAkB,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAA2C;QAC7D,IAAI,WAAgD,CAAC;QAErD,sBAAsB;QACtB,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAChE,OAAO;YACP,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtC,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAM,QAAQ,CAAC,CAAC;gBAE7D,KAAK;YACN,CAAC;iBAAM,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3C,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAM,QAAQ,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACP,MAAM,IAAI,2BAAa,CAAC,kCAAkC,QAAQ,GAAG,CAAC,CAAC;YACxE,CAAC;QACF,CAAC;aAAM,IAAI,IAAA,sBAAS,EAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,qCAAqC;YACrC,WAAW,GAAG,SAAS,CAAC;QACzB,CAAC;aAAM,CAAC;YACP,WAAW,GAAG,QAAmC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1E,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,aAAa,CAAC,wBAAuD;QAC3E,IAAI,CAAC,UAAU,GAAG,wBAAwB,CAAC;IAC5C,CAAC;IAED;;;;;;;;OAQG;IACU,IAAI,CAGf,OAAoC;;YACrC,MAAM,UAAU,qBAAQ,OAAO,CAAE,CAAC;YAElC,IAAI,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAuB,UAAU,CAAC,CAAC;YAEzE,IAAI,CAAC,IAAA,sBAAS,EAAC,IAAI,CAAC,UAAU,CAAC;gBAAE,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAE5F,IAAI,oBAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5C,OAAO,QAAQ,CAAC,MAAM,CAAC;YACxB,CAAC;YAED,MAAM,IAAI,2BAAa,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;KAAA;IAED;;;;OAIG;IACU,SAAS,CAAC,OAA4B;;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAe,OAAO,CAAC,CAAC;YAEhE,OAAO,QAAyC,CAAC;QAClD,CAAC;KAAA;IAEa,YAAY,CAIzB,OAA0D;;YAE1D,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;YAE1B,IAAI,IAAA,sBAAS,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,2BAAa,CACtB,wFAAwF,CACxF,CAAC;YACH,CAAC;YAED,IAAI,OAAO,GAAG,CACb,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC9B,CAAC,CAAC,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gBACjC,CAAC,CAAC,oBAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CACX,CAAC;YAEpB,IAAI,CAAC,IAAA,sBAAS,EAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,IAAA,yBAAc,EAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,IAAI,QAAQ,CAAC;gBAEb,IAAI,CAAC;oBACJ,QAAQ,GAAG,MAAM,QAAQ,CAAC,OAAO,CAChC,OAAsC,CACtC,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBAChB,oEAAoE;oBACpE,QAAQ,GAAG,KAAsC,CAAC;gBACnD,CAAC;gBACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,IAAA,4BAAiB,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACjC,OAAQ,QAAkC;qBACxC,OAAO,CAAuB,OAAsC,CAAC;qBACrE,IAAI,CACJ,GAAG,CAAC,EAAE,CACL,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,GAAG,EAAE;oBAC1C,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,KAAK;iBACZ,CAA4C,CAC9C;qBACA,KAAK,CAAC,KAAK,CAAC,EAAE,CACd,IAAI,CAAC,uBAAuB,CAC3B,OAAO,EACP,KAA+C,EAC/C,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAC7B,CACD,CAAC;YACJ,CAAC;YAED,8CAA8C;YAC9C,IAAI,IAAA,kCAAuB,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,OAAO,CAAgC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrE,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,EAAE;wBACxC,MAAM,CACL,IAAI,CAAC,uBAAuB,CAC3B,OAAO,EACP,GAAoC,EACpC;4BACC,MAAM,EAAE,IAAI;4BACZ,KAAK,EAAE,IAAI;yBACX,CACD,CACD,CAAC;oBACH,CAAC,CAAC;oBAEF,MAAM,mBAAmB,GAAG,CAAC,QAAuC,EAAE,EAAE,CACvE,OAAO,CACN,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE;wBAC/C,MAAM,EAAE,IAAI;wBACZ,KAAK,EAAE,KAAK;qBACZ,CAAC,CACF,CAAC;oBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAC9B,OAAO;oBACP,uEAAuE;oBACvE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;wBACjB,IAAI,GAAG,EAAE,CAAC;4BACT,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;wBAC7B,CAAC;wBAED,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;oBACtC,CAAC,CACD,CAAC;oBACF,8FAA8F;oBAC9F,4DAA4D;oBAC5D,gFAAgF;oBAChF,qHAAqH;oBACrH,gMAAgM;oBAChM,iFAAiF;oBACjF,+EAA+E;oBAC/E,IAAI,IAAA,sBAAS,EAAC,MAAM,CAAC,EAAE,CAAC;wBACvB,MAAM,eAAe,GAAG,MAEvB,CAAC;wBACF,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;4BACvD,IAAI,CAAC;gCACJ,wCAAwC;gCACxC,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAClD,OAAO,EACP,KAA+C,EAC/C,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAC7B,CAAC;gCACF,MAAM,CAAC,cAAc,CAAC,CAAC;4BACxB,CAAC;4BAAC,OAAO,eAAe,EAAE,CAAC;gCAC1B,0DAA0D;gCAC1D,MAAM,CAAC,eAAe,CAAC,CAAC;4BACzB,CAAC;wBACF,CAAC,CAAC,CAAC;oBACJ,CAAC;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC;YAED,8CAA8C;YAC9C,IAAI,IAAA,+BAAoB,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,OAAO,IAAI,OAAO,CAAgC,CAAC,OAAO,EAAE,MAAM,EAAQ,EAAE;oBAC3E,QAAQ,CAAC,IAAI,CAAe,OAAO,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;wBACtD,IAAI,GAAG,EAAE,CAAC;4BACT,OAAO,MAAM,CACZ,IAAI,CAAC,uBAAuB,CAC3B,OAAO,EACP,GAA+C,EAC/C;gCACC,MAAM,EAAE,IAAI;gCACZ,KAAK,EAAE,IAAI;6BACX,CACD,CACD,CAAC;wBACH,CAAC;wBAED,IAAI,IAAA,sBAAS,EAAC,QAAQ,CAAC,EAAE,CAAC;4BACzB,MAAM,IAAI,2BAAa,CACtB,EAAW,EACX,yCAAyC,CACzC,CAAC;wBACH,CAAC;wBAED,OAAO,OAAO,CACb,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE;4BAC/C,MAAM,EAAE,IAAI;4BACZ,KAAK,EAAE,KAAK;yBACZ,CAAC,CACF,CAAC;oBACH,CAAC,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC;YACJ,CAAC;YAED,8CAA8C;YAC9C,IAAI,IAAA,oCAAyB,EAAC,QAAQ,CAAC,EAAE,CAAC;gBACzC,OAAO,QAAQ;qBACb,SAAS,CAAe,OAAO,CAAC;qBAChC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAChB,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAC/E;qBACA,KAAK,CAAC,KAAK,CAAC,EAAE,CACd,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAsC,EAAE;oBAC7E,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,IAAI;iBACX,CAAC,CACF,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,2BAAa,CAAC,yDAAyD,CAAC,CAAC;QACpF,CAAC;KAAA;IAED,kDAAkD;IAC1C,uBAAuB,CAC9B,OAAoC,EACpC,QAAgD,EAChD,EAAE,MAAM,EAAE,KAAK,EAAuC;QAEtD,IAAI,IAAA,sBAAS,EAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,cAAc,CACzB,OAAO;YACP,qDAAqD;YACrD,2CAA2C;YAC3C,IAAyD,EACzD,KAAK,CACL,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,8CAA8C;QAC9C,IAAI,oBAAO,CAAC,mBAAmB,CAAY,QAAQ,CAAC,EAAE,CAAC;YACtD,4BAA4B;YAC5B,IACC,IAAI,CAAC,uBAAuB;gBAC5B,IAAA,+BAAkB,EAAC,QAAoC,CAAC,EACvD,CAAC;gBACF,MAAM,gBAAgB,GAAG,QAAoC,CAAC;gBAC9D,uGAAuG;gBACvG,IAAI,0BAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnD,oEAAoE;oBACpE,MAAM,GAAG,GAAG,0BAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAE,CAAC,KAAK,CAAC;oBACjE,MAAM,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACP,MAAM,IAAI,sBAAQ,CAAC,gBAAgB,CAAC,CAAC;gBACtC,CAAC;YACF,CAAC;iBAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,kCAAoB,CAAyB,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC3E,CAAC;QACF,CAAC;QAED,yDAAyD;QACzD,+CAA+C;QAC/C,IAAI,oBAAO,CAAC,oBAAoB,CAAa,QAAQ,CAAC,EAAE,CAAC;YACxD,OAAO,QAAQ,CAAC;QACjB,CAAC;QAED,IAAK,QAAoB,YAAY,KAAK,EAAE,CAAC;YAC5C,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,QAAQ,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,MAAM,IAAI,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,oBAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrF,OAAO,QAA4C,CAAC;QACrD,CAAC;QAED,IAAI,MAAM,IAAI,CAAC,KAAK,IAAI,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACzD,OAAO,QAA4C,CAAC;QACrD,CAAC;QAED,IAAI,MAAM,IAAI,KAAK,IAAI,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACxD,0EAA0E;YAC1E,MAAM,QAAQ,CAAC;QAChB,CAAC;QAED,IACC,MAAM;YACN,CAAC,oBAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YACtC,CAAC,oBAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EACtC,CAAC;YACF,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,2BAAa,CAAC,QAAQ,EAAE,0CAA0C,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,2BAAa,CAAC,QAAQ,EAAE,0CAA0C,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,IAAI,2BAAa,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;IACvD,CAAC;IAEO,MAAM,CAAC,WAAW,CACzB,QAAgD;QAEhD,IAAI,KAA+B,CAAC;QAEpC,IAAI,oBAAO,CAAC,mBAAmB,CAAY,QAAQ,CAAC,EAAE,CAAC;YACtD,KAAK,GAAI,QAAqC,CAAC,KAAK,CAAC;QACtD,CAAC;aAAM,IAAK,QAAoB,YAAY,KAAK,EAAE,CAAC;YACnD,KAAK,GAAG,QAAmC,CAAC;QAC7C,CAAC;QAED,4FAA4F;QAC5F,mFAAmF;QACnF,iEAAiE;QACjE,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,MAAM,IAAI,oCAAsB,CAAC,KAAK,CAAC,CAAC;QAE/E,OAAO,KAAK,CAAC;IACd,CAAC;IACD,0FAA0F;IAC1F,kDAAkD;IAC1C,cAAc,CACrB,OAAoC,EACpC,QAAgD,EAChD,KAAc;QAEd,MAAM,GAAG,GAAG;YACX,OAAO,EAAE,KAAK;YACd,6CAA6C;YAC7C,EAAE,EAAE,oBAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gBAClC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;gBACf,CAAC,CAAC,IAAI,IAAI,OAAO;oBACjB,CAAC,CAAC,OAAO,CAAC,EAAE;oBACZ,CAAC,CAAC,uCAAuC;wBACvC,2CAA2C;wBAC3C,IAAI;SACP,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACX,OAAO,gCACH,GAAG,KACN,KAAK,EAAE,QAAmB,GACK,CAAC;QAClC,CAAC;QAED,OAAO,gCACH,GAAG,KACN,MAAM,EAAE,QAAmB,GACI,CAAC;IAClC,CAAC;CACD;AAtaD,gDAsaC"}