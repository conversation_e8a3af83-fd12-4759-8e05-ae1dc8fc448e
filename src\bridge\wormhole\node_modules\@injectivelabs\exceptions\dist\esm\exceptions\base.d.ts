import { Exception, ErrorType, ErrorContext, ErrorCode, ErrorContextCode } from './types/index.js';
export declare abstract class ConcreteException extends Error implements Exception {
    /**
     * The type of the Error
     */
    type: ErrorType;
    /**
     * Error specific code (HttpStatus, GrpcStatus, etc)
     */
    code: ErrorCode;
    /**
     * The name of the error (the name of the instance of the Exception)
     */
    name: string;
    /**
     * The name of the error (the name of the instance of the Exception)
     * Needed for reporting reasons, ex: bugsnag
     */
    errorClass: string;
    /**
     * Providing more context
     * (ex: endpoint on http request)
     */
    context?: string;
    /**
     * Providing more context as to where the exception was thrown
     * (ex: on-chain module, etc)
     */
    contextModule?: string;
    /**
     * Providing more context as to why the exception was thrown
     * (ex: on-chain error code, etc)
     */
    contextCode?: ErrorContextCode;
    /**
     * Parsed message of the exception
     */
    message: string;
    /**
     * The original stack of the error
     */
    stack?: string;
    /**
     * The original message of the error
     */
    originalMessage: string;
    constructor(error: Error, context?: ErrorContext);
    parse(): void;
    parseError(error: Error): void;
    parseContext(errorContext?: ErrorContext): void;
    setType(type: ErrorType): void;
    setCode(code: ErrorCode): void;
    setContext(context: string): void;
    setOriginalMessage(message: string): void;
    setStack(stack: string): void;
    setName(name: string): void;
    setMessage(message: string): void;
    setContextModule(contextModule: string): void;
    setContextCode(code: ErrorContextCode): void;
    toOriginalError(): Error;
    toError(): Error;
    toCompactError(): Error;
    toJson(): string;
    toObject(): {
        code: ErrorCode;
        type: ErrorType;
        errorClass: string;
        message: string;
        context: string | undefined;
        contextCode: number | undefined;
        contextModule: string | undefined;
        originalMessage: string;
        stack: string[];
    };
    toString(): string;
}
