"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chainErrorMessagesMap = exports.chainModuleCodeErrorMessagesMap = void 0;
const index_js_1 = require("./types/index.js");
const auctionErrorMap = {
    [index_js_1.ChainAuctionErrorCodes.ErrBidInvalid]: 'The gas limit provided in the transaction is not valid',
    [index_js_1.ChainAuctionErrorCodes.ErrBidRound]: 'The gas limit provided in the transaction is not valid',
};
const authZErrorMap = {
    // ErrNoAuthorizationFound error if there is no authorization found given a grant key
    [index_js_1.ChainAuthZErrorCodes.ErrNoAuthorizationFound]: 'Authorization not found',
    // ErrInvalidExpirationTime error if the set expiration time is in the past
    [index_js_1.ChainAuthZErrorCodes.ErrInvalidExpirationTime]: 'Expiration time of authorization should be more than current time',
    // ErrUnknownAuthorizationType error for unknown authorization type
    [index_js_1.ChainAuthZErrorCodes.ErrUnknownAuthorizationType]: 'Unknown authorization type',
    // ErrNoGrantKeyFound error if the requested grant key does not exist
    [index_js_1.ChainAuthZErrorCodes.ErrNoGrantKeyFound]: 'Grant key not found',
    // ErrAuthorizationExpired error if the authorization has expired
    [index_js_1.ChainAuthZErrorCodes.ErrAuthorizationExpired]: 'Authorization expired',
    // ErrGranteeIsGranter error if the grantee and the granter are the same
    [index_js_1.ChainAuthZErrorCodes.ErrGranteeIsGranter]: 'Grantee and granter should be different',
    // ErrAuthorizationNumOfSigners error if an authorization message does not have only one signer
    [index_js_1.ChainAuthZErrorCodes.ErrAuthorizationNumOfSigners]: 'Authorization can be given to msg with only one signer',
    // ErrNegativeMaxTokens error if the max tokens is negative
    [index_js_1.ChainAuthZErrorCodes.ErrNegativeMaxTokens]: 'Max tokens should be positive',
};
const cosmosErrorMap = {
    [index_js_1.ChainCosmosErrorCode.ErrInsufficientFee]: 'You do not have enough funds to cover the transaction fees.',
    [index_js_1.ChainCosmosErrorCode.ErrInsufficientFunds]: 'You do not have enough funds.',
    [index_js_1.ChainCosmosErrorCode.ErrTxTimeoutHeight]: 'The transaction failed to be included within a block on time.',
    [index_js_1.ChainCosmosErrorCode.ErrTxDecode]: 'There is an issue while parsing the transaction',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidSequence]: 'The sequence number is not valid',
    [index_js_1.ChainCosmosErrorCode.ErrUnauthorized]: 'Unauthorized',
    [index_js_1.ChainCosmosErrorCode.ErrUnknownRequest]: 'The request is not known',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidAddress]: 'The address is not valid',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidPubKey]: 'The public key is not valid',
    [index_js_1.ChainCosmosErrorCode.ErrUnknownAddress]: 'The address is unknown',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidCoins]: 'The coins are not valid',
    [index_js_1.ChainCosmosErrorCode.ErrOutOfGas]: 'The transaction run out of gas',
    [index_js_1.ChainCosmosErrorCode.ErrMemoTooLarge]: 'The memo field in the transaction is too large',
    [index_js_1.ChainCosmosErrorCode.ErrTooManySignatures]: 'The transaction exceeded the maximum number of signatures',
    [index_js_1.ChainCosmosErrorCode.ErrNoSignatures]: 'There are no signatures appended on the transaction',
    [index_js_1.ChainCosmosErrorCode.ErrJSONMarshal]: 'There is an issue while parsing the transaction',
    [index_js_1.ChainCosmosErrorCode.ErrJSONUnmarshal]: 'There is an issue while parsing the transaction',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidRequest]: 'invalid request',
    [index_js_1.ChainCosmosErrorCode.ErrTxInMempoolCache]: 'The transaction is already in the mempool',
    [index_js_1.ChainCosmosErrorCode.ErrMempoolIsFull]: 'The mempool is full',
    [index_js_1.ChainCosmosErrorCode.ErrTxTooLarge]: 'The transaction is too large',
    [index_js_1.ChainCosmosErrorCode.ErrKeyNotFound]: 'Address is not yet active on the chain as it has a $0 balance. To activate it, please send a small amount of funds. Once funded, the network will recognize the address, allowing you to proceed.',
    [index_js_1.ChainCosmosErrorCode.ErrWrongPassword]: 'invalid account password',
    [index_js_1.ChainCosmosErrorCode.ErrorInvalidSigner]: 'tx intended signer does not match the given signer',
    [index_js_1.ChainCosmosErrorCode.ErrorInvalidGasAdjustment]: 'invalid gas adjustment',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidHeight]: 'The height provided in the transaction is not valid',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidVersion]: 'The version provided in the transaction is not valid',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidChainID]: 'The chainId provided in the transaction is not valid',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidType]: 'The type provided in the transaction is not valid',
    [index_js_1.ChainCosmosErrorCode.ErrUnknownExtensionOptions]: 'The extension options provided in the transaction is unknown',
    [index_js_1.ChainCosmosErrorCode.ErrWrongSequence]: 'The sequence number provided in the transaction is incorrect',
    [index_js_1.ChainCosmosErrorCode.ErrPackAny]: 'failed packing protobuf message to Any',
    [index_js_1.ChainCosmosErrorCode.ErrUnpackAny]: 'failed unpacking protobuf message from Any',
    [index_js_1.ChainCosmosErrorCode.ErrLogic]: 'Internal logic error',
    [index_js_1.ChainCosmosErrorCode.ErrConflict]: 'conflict',
    [index_js_1.ChainCosmosErrorCode.ErrNotSupported]: 'The feature is not supported',
    [index_js_1.ChainCosmosErrorCode.ErrNotFound]: 'not found',
    [index_js_1.ChainCosmosErrorCode.ErrIO]: 'Internal IO error',
    [index_js_1.ChainCosmosErrorCode.ErrAppConfig]: 'error in app.toml',
    [index_js_1.ChainCosmosErrorCode.ErrInvalidGasLimit]: 'The gas limit provided in the transaction is not valid',
};
const exchangeErrorMap = {
    [index_js_1.ChainExchangeModuleErrorCode.ErrOrderInvalid]: 'Your order failed to validate',
    [index_js_1.ChainExchangeModuleErrorCode.ErrSpotMarketNotFound]: 'The spot market has not been found',
    [index_js_1.ChainExchangeModuleErrorCode.ErrSpotMarketExists]: 'The spot market already exists',
    [index_js_1.ChainExchangeModuleErrorCode.ErrBadField]: 'There is an issue with your order',
    [index_js_1.ChainExchangeModuleErrorCode.ErrMarketInvalid]: 'The market failed to validate',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInsufficientDeposit]: 'Your trading account has insufficient funds',
    [index_js_1.ChainExchangeModuleErrorCode.ErrUnrecognizedOrderType]: 'The order type is not recognized',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInsufficientPositionQuantity]: 'The position quantity is insufficient for the order',
    [index_js_1.ChainExchangeModuleErrorCode.ErrOrderHashInvalid]: 'The order hash is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrBadSubaccountID]: 'The subaccount id is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidBaseDenom]: '',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidExpiry]: 'The expiry date is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidPrice]: 'The price is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidQuantity]: 'The quantity is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrUnsupportedOracleType]: 'The oracle type is not supported',
    [index_js_1.ChainExchangeModuleErrorCode.ErrOrderDoesntExist]: 'The order does not exist',
    [index_js_1.ChainExchangeModuleErrorCode.ErrOrderbookFillInvalid]: '',
    [index_js_1.ChainExchangeModuleErrorCode.ErrPerpetualMarketExists]: 'The perpetual market already exists',
    [index_js_1.ChainExchangeModuleErrorCode.ErrExpiryFuturesMarketExists]: 'The expiry futures market market already exists',
    [index_js_1.ChainExchangeModuleErrorCode.ErrExpiryFuturesMarketExpired]: 'The expiry futures market has expired',
    [index_js_1.ChainExchangeModuleErrorCode.ErrNoLiquidity]: 'There is not enough liquidity',
    [index_js_1.ChainExchangeModuleErrorCode.ErrSlippageExceedsWorstPrice]: 'There is not enough liquidity',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInsufficientOrderMargin]: 'The order has insufficient margin',
    [index_js_1.ChainExchangeModuleErrorCode.ErrDerivativeMarketNotFound]: 'The derivative market cannot be found',
    [index_js_1.ChainExchangeModuleErrorCode.ErrPositionNotFound]: 'The position cannot be found',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidReduceOnlyPositionDirection]: 'Position direction does not oppose the reduce-only order',
    [index_js_1.ChainExchangeModuleErrorCode.ErrPriceSurpassesBankruptcyPrice]: 'Your order price surpasses bankruptcy price',
    [index_js_1.ChainExchangeModuleErrorCode.ErrPositionNotLiquidable]: 'The position is not liquidable',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTriggerPrice]: 'Your order trigger price is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidOracleType]: 'The oracle type is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidPriceTickSize]: 'The minimum price tick size is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidQuantityTickSize]: 'The minimum quantity tick size is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMargin]: "Your order's minimum margin is not valid ",
    [index_js_1.ChainExchangeModuleErrorCode.ErrExceedsOrderSideCount]: 'You cannot have more orders for this market for this direction',
    [index_js_1.ChainExchangeModuleErrorCode.ErrMarketOrderAlreadyExists]: 'You cannot place another market order within this block',
    [index_js_1.ChainExchangeModuleErrorCode.ErrConditionalMarketOrderAlreadyExists]: 'You cannot place another conditional market order',
    [index_js_1.ChainExchangeModuleErrorCode.ErrMarketLaunchProposalAlreadyExists]: 'There is an existing equivalent market launch proposal.',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMarketStatus]: 'The market status is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrSameDenoms]: 'The base denom and quote denom cannot be same',
    [index_js_1.ChainExchangeModuleErrorCode.ErrSameOracles]: 'The oracle base and the oracle quote cannot be the same',
    [index_js_1.ChainExchangeModuleErrorCode.ErrFeeRatesRelation]: 'The MakerFeeRate does not match TakerFeeRate requirements',
    [index_js_1.ChainExchangeModuleErrorCode.ErrMarginsRelation]: 'The MaintenanceMarginRatio cannot be greater than InitialMarginRatio',
    [index_js_1.ChainExchangeModuleErrorCode.ErrExceedsMaxOracleScaleFactor]: 'The OracleScaleFactor cannot be greater than MaxOracleScaleFactor',
    [index_js_1.ChainExchangeModuleErrorCode.ErrSpotExchangeNotEnabled]: 'Spot exchange is not enabled yet',
    [index_js_1.ChainExchangeModuleErrorCode.ErrDerivativesExchangeNotEnabled]: 'Derivatives exchange is not enabled yet',
    [index_js_1.ChainExchangeModuleErrorCode.ErrOraclePriceDeltaExceedsThreshold]: 'The oracle price delta exceeds threshold',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidHourlyInterestRate]: 'The hourly interest rate is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidHourlyFundingRateCap]: 'The hourly funding rate cap is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMarketFundingParamUpdate]: 'You can only update funding parameters on perpetual markets.',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTradingRewardCampaign]: 'The trading reward campaign is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidFeeDiscountSchedule]: 'The fee discount schedule is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidLiquidationOrder]: 'The liquidation order is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrTradingRewardCampaignDistributionError]: 'Unknown error happened for campaign distributions',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTradingRewardsPendingPointsUpdate]: 'The updated trading reward points is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidBatchMsgUpdate]: 'The MsgBatchUpdate is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrExceedsTopOfBookPrice]: 'The post-only order price exceeds top of the orderbook price',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidOrderTypeForMessage]: 'The order type is not supported for this message',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidDMMSender]: 'The sender must match the DMM address',
    [index_js_1.ChainExchangeModuleErrorCode.ErrAlreadyOptedOutOfRewards]: 'The DMM address already opted out of rewards',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMarginRatio]: 'The margin ratio is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrBelowMinimalContribution]: 'The provided funds are below minimum',
    [index_js_1.ChainExchangeModuleErrorCode.ErrLowPositionMargin]: 'The position is below initial margin requirement',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTotalSupply]: 'The pool has non-positive total LP token supply',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidLpTokenBurnAmount]: 'The passed LP token burn amount is greater than total LP token supply',
    [index_js_1.ChainExchangeModuleErrorCode.ErrUnsupportedAction]: 'This action is not supported',
    [index_js_1.ChainExchangeModuleErrorCode.ErrNegativePositionQuantity]: 'The position quantity cannot be negative',
    [index_js_1.ChainExchangeModuleErrorCode.ErrBinaryOptionsMarketExists]: 'The BinaryOptions market already exists',
    [index_js_1.ChainExchangeModuleErrorCode.ErrBinaryOptionsMarketNotFound]: 'The BinaryOptions market cannot be found',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidSettlement]: 'The settlement price is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrAccountDoesntExist]: 'The trading account does not exist',
    [index_js_1.ChainExchangeModuleErrorCode.ErrSenderIsNotAnAdmin]: 'The sender should be the admin of the market',
    [index_js_1.ChainExchangeModuleErrorCode.ErrMarketAlreadyScheduledToSettle]: 'The market is already scheduled to settle ',
    [index_js_1.ChainExchangeModuleErrorCode.ErrGenericMarketNotFound]: 'The market cannot be found',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidDenomDecimal]: 'The denom decimal cannot be below 1 or above max scale factor',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidState]: 'The state is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrTransientOrdersUpToCancelNotSupported]: 'The transient orders up to cancellation not supported',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTrade]: 'The trade is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrNoMarginLocked]: 'There is no margin locked in the trading account',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidAccessLevel]: 'There is no access to perform action',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidAddress]: 'The address is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidArgument]: 'The argument is not valid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidFundsDirection]: 'Invalid funds direction',
    [index_js_1.ChainExchangeModuleErrorCode.ErrNoFundsProvided]: 'No funds provided',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidSignature]: 'Invalid signature',
    [index_js_1.ChainExchangeModuleErrorCode.ErrNoFundsToUnlock]: 'No funds to unlock',
    [index_js_1.ChainExchangeModuleErrorCode.ErrNoMsgsProvided]: 'No msgs provided',
    [index_js_1.ChainExchangeModuleErrorCode.ErrNoMsgProvided]: 'No msg provided',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidAmount]: 'Invalid amount',
    [index_js_1.ChainExchangeModuleErrorCode.ErrFeatureDisabled]: 'The current feature has been disabled',
    [index_js_1.ChainExchangeModuleErrorCode.ErrTooMuchOrderMargin]: 'Order has too much margin',
    [index_js_1.ChainExchangeModuleErrorCode.ErrBadSubaccountNonce]: 'Subaccount nonce is invalid',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInsufficientFunds]: 'Insufficient funds',
    [index_js_1.ChainExchangeModuleErrorCode.ErrPostOnlyMode]: 'Only post-only actions available for approximately 30 minutes after a chain upgrade.',
    [index_js_1.ChainExchangeModuleErrorCode.ErrClientOrderIdAlreadyExists]: 'Client order id already exists',
    [index_js_1.ChainExchangeModuleErrorCode.ErrInvalidCid]: 'Client order id is invalid. Max length is 36 chars',
};
const insuranceErrorMap = {
    [index_js_1.ChainInsuranceErrorCodes.ErrInsuranceFundAlreadyExists]: 'The insurance fund already exists',
    [index_js_1.ChainInsuranceErrorCodes.ErrInsuranceFundNotFound]: 'The insurance fund is not found',
    [index_js_1.ChainInsuranceErrorCodes.ErrRedemptionAlreadyExists]: 'The redemption already exists',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidDepositAmount]: 'The deposit amount is not valid',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidDepositDenom]: 'The deposit denom is not valid',
    [index_js_1.ChainInsuranceErrorCodes.ErrPayoutTooLarge]: 'The insurance fund payout exceeds the deposits',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidTicker]: 'The ticker is not valid',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidQuoteDenom]: 'The quote denom is not valid',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidOracle]: 'The oracle is not valid',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidExpirationTime]: 'The expiration time is not valid',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidMarketID]: 'The marketId is not valid',
    [index_js_1.ChainInsuranceErrorCodes.ErrInvalidShareDenom]: 'The share denom is not valid',
};
const ocrErrorMap = {
    [index_js_1.ChainOcrErrorCodes.ErrStaleReport]: 'stale report',
    [index_js_1.ChainOcrErrorCodes.ErrIncompleteProposal]: 'incomplete proposal',
    [index_js_1.ChainOcrErrorCodes.ErrRepeatedAddress]: 'repeated oracle address',
    [index_js_1.ChainOcrErrorCodes.ErrTooManySigners]: 'too many signers',
    [index_js_1.ChainOcrErrorCodes.ErrIncorrectConfig]: 'incorrect config',
    [index_js_1.ChainOcrErrorCodes.ErrConfigDigestNotMatch]: "config digest doesn't match",
    [index_js_1.ChainOcrErrorCodes.ErrWrongNumberOfSignatures]: 'wrong number of signatures',
    [index_js_1.ChainOcrErrorCodes.ErrIncorrectSignature]: 'incorrect signature',
    [index_js_1.ChainOcrErrorCodes.ErrNoTransmitter]: 'no transmitter specified',
    [index_js_1.ChainOcrErrorCodes.ErrIncorrectTransmissionData]: 'incorrect transmission data',
    [index_js_1.ChainOcrErrorCodes.ErrNoTransmissionsFound]: 'no transmissions found',
    [index_js_1.ChainOcrErrorCodes.ErrMedianValueOutOfBounds]: 'median value is out of bounds',
    [index_js_1.ChainOcrErrorCodes.ErrIncorrectRewardPoolDenom]: "LINK denom doesn't match",
    [index_js_1.ChainOcrErrorCodes.ErrNoRewardPool]: "Reward Pool doesn't exist",
    [index_js_1.ChainOcrErrorCodes.ErrInvalidPayees]: 'wrong number of payees and transmitters',
    [index_js_1.ChainOcrErrorCodes.ErrModuleAdminRestricted]: 'action is restricted to the module admin',
    [index_js_1.ChainOcrErrorCodes.ErrFeedAlreadyExists]: 'feed already exists',
    [index_js_1.ChainOcrErrorCodes.ErrFeedDoesntExists]: 'feed doesnt exists',
    [index_js_1.ChainOcrErrorCodes.ErrAdminRestricted]: 'action is admin-restricted',
    [index_js_1.ChainOcrErrorCodes.ErrInsufficientRewardPool]: 'insufficient reward pool',
    [index_js_1.ChainOcrErrorCodes.ErrPayeeAlreadySet]: 'payee already set',
    [index_js_1.ChainOcrErrorCodes.ErrPayeeRestricted]: 'action is payee-restricted',
    [index_js_1.ChainOcrErrorCodes.ErrFeedConfigNotFound]: 'feed config not found',
};
const oracleErrorMap = {
    [index_js_1.ChainOracleErrorCodes.ErrEmptyRelayerAddr]: 'relayer address is empty',
    [index_js_1.ChainOracleErrorCodes.ErrBadRatesCount]: 'bad rates count',
    [index_js_1.ChainOracleErrorCodes.ErrBadResolveTimesCount]: 'bad resolve times',
    [index_js_1.ChainOracleErrorCodes.ErrBadRequestIDsCount]: 'bad request ID',
    [index_js_1.ChainOracleErrorCodes.ErrRelayerNotAuthorized]: 'relayer not authorized',
    [index_js_1.ChainOracleErrorCodes.ErrBadPriceFeedBaseCount]: 'bad price feed base count',
    [index_js_1.ChainOracleErrorCodes.ErrBadPriceFeedQuoteCount]: 'bad price feed quote count',
    [index_js_1.ChainOracleErrorCodes.ErrUnsupportedOracleType]: 'unsupported oracle type',
    [index_js_1.ChainOracleErrorCodes.ErrBadMessagesCount]: 'bad messages count',
    [index_js_1.ChainOracleErrorCodes.ErrBadCoinbaseMessage]: 'bad Coinbase message',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidEthereumSignature]: 'bad Ethereum signature',
    [index_js_1.ChainOracleErrorCodes.ErrBadCoinbaseMessageTimestamp]: 'bad Coinbase message timestamp',
    [index_js_1.ChainOracleErrorCodes.ErrCoinbasePriceNotFound]: 'Coinbase price not found',
    [index_js_1.ChainOracleErrorCodes.ErrBadPrice]: 'Prices must be positive',
    [index_js_1.ChainOracleErrorCodes.ErrPriceTooLarge]: 'Prices must be less than 10 million.',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidBandIBCRequest]: 'Invalid Band IBC Request',
    [index_js_1.ChainOracleErrorCodes.ErrSample]: 'sample error',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidPacketTimeout]: 'invalid packet timeout',
    [index_js_1.ChainOracleErrorCodes.ErrBadSymbolsCount]: 'invalid symbols count',
    [index_js_1.ChainOracleErrorCodes.ErrBadIBCPortBind]: 'could not claim port capability',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidPortID]: 'invalid IBC Port ID',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidChannelID]: 'invalid IBC Channel ID',
    [index_js_1.ChainOracleErrorCodes.ErrBadRequestInterval]: 'invalid Band IBC request interval',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidBandIBCUpdateRequest]: 'Invalid Band IBC Update Request Proposal',
    [index_js_1.ChainOracleErrorCodes.ErrBandIBCRequestNotFound]: 'Band IBC Oracle Request not found',
    [index_js_1.ChainOracleErrorCodes.ErrEmptyBaseInfo]: 'Base Info is empty',
    [index_js_1.ChainOracleErrorCodes.ErrEmptyProvider]: 'provider is empty',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidProvider]: 'invalid provider name',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidSymbol]: 'invalid symbol',
    [index_js_1.ChainOracleErrorCodes.ErrRelayerAlreadyExists]: 'relayer already exists',
    [index_js_1.ChainOracleErrorCodes.ErrProviderPriceNotFound]: 'provider price not found',
    [index_js_1.ChainOracleErrorCodes.ErrInvalidOracleRequest]: 'invalid oracle request',
    [index_js_1.ChainOracleErrorCodes.ErrOraclePriceNotFound]: 'no price for oracle was found',
};
const peggyErrorMap = {
    [index_js_1.ChainPeggyErrorCodes.ErrInternal]: 'internal',
    [index_js_1.ChainPeggyErrorCodes.ErrDuplicate]: 'duplicate',
    [index_js_1.ChainPeggyErrorCodes.ErrInvalid]: 'invalid',
    [index_js_1.ChainPeggyErrorCodes.ErrTimeout]: 'timeout',
    [index_js_1.ChainPeggyErrorCodes.ErrUnknown]: 'unknown',
    [index_js_1.ChainPeggyErrorCodes.ErrEmpty]: 'empty',
    [index_js_1.ChainPeggyErrorCodes.ErrOutdated]: 'outdated',
    [index_js_1.ChainPeggyErrorCodes.ErrUnsupported]: 'unsupported',
    [index_js_1.ChainPeggyErrorCodes.ErrNonContiguousEventNonce]: 'non contiguous event nonce',
    [index_js_1.ChainPeggyErrorCodes.ErrNoUnbatchedTxsFound]: 'no unbatched txs found',
    [index_js_1.ChainPeggyErrorCodes.ErrResetDelegateKeys]: 'can not set orchestrator addresses more than once',
    [index_js_1.ChainPeggyErrorCodes.ErrSupplyOverflow]: 'supply cannot exceed max ERC20 value',
    [index_js_1.ChainPeggyErrorCodes.ErrInvalidEthSender]: 'invalid ethereum sender on claim',
    [index_js_1.ChainPeggyErrorCodes.ErrInvalidEthDestination]: 'invalid ethereum destination',
};
const tokenFactoryErrorMap = {
    [index_js_1.ChainTokenFactoryErrorCodes.ErrDenomExists]: 'attempting to create a denom that already exists',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrUnauthorized]: 'unauthorized account',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrInvalidDenom]: 'invalid denom',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrInvalidCreator]: 'invalid creator',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrInvalidAuthorityMetadata]: 'invalid authority metadata',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrInvalidGenesis]: 'invalid genesis',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrSubdenomTooLong]: 'subdenom too long',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrSubdenomTooShort]: 'subdenom too short',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrSubdenomNestedTooShort]: 'nested subdenom too short, each one should have at least',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrCreatorTooLong]: 'creator too long',
    [index_js_1.ChainTokenFactoryErrorCodes.ErrDenomDoesNotExist]: 'denom does not exist',
};
const wasmxErrorMap = {
    [index_js_1.ChainWasmXErrorCodes.ErrInvalidGasLimit]: 'invalid gas limit',
    [index_js_1.ChainWasmXErrorCodes.ErrInvalidGasPrice]: 'invalid gas price',
    [index_js_1.ChainWasmXErrorCodes.ErrInvalidContractAddress]: 'invalid contract address',
    [index_js_1.ChainWasmXErrorCodes.ErrAlreadyRegistered]: 'contract already registered',
    [index_js_1.ChainWasmXErrorCodes.ErrDuplicateContract]: 'duplicate contract',
    [index_js_1.ChainWasmXErrorCodes.ErrNoContractAddresses]: 'no contract addresses found',
    [index_js_1.ChainWasmXErrorCodes.ErrInvalidCodeId]: 'invalid code id',
};
const stakingErrorMap = {
    [index_js_1.ChainStakingErrorCodes.ErrEmptyValidatorAddr]: 'empty validator address',
    [index_js_1.ChainStakingErrorCodes.ErrNoValidatorFound]: 'validator does not exist',
    [index_js_1.ChainStakingErrorCodes.ErrValidatorOwnerExists]: 'validator already exist for this operator address; must use new validator operator address',
    [index_js_1.ChainStakingErrorCodes.ErrValidatorPubKeyExists]: 'validator already exist for this pubkey; must use new validator pubkey',
    [index_js_1.ChainStakingErrorCodes.ErrValidatorPubKeyTypeNotSupported]: 'validator pubkey type is not supported',
    [index_js_1.ChainStakingErrorCodes.ErrValidatorJailed]: 'validator for this address is currently jailed',
    [index_js_1.ChainStakingErrorCodes.ErrBadRemoveValidator]: 'failed to remove validator',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionNegative]: 'commission must be positive',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionHuge]: 'commission cannot be more than 100%',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionGTMaxRate]: 'commission cannot be more than the max rate',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionUpdateTime]: 'commission cannot be changed more than once in 24h',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionChangeRateNegative]: 'commission change rate must be positive',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionChangeRateGTMaxRate]: 'commission change rate cannot be more than the max rate',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionGTMaxChangeRate]: 'commission cannot be changed more than max change rate',
    [index_js_1.ChainStakingErrorCodes.ErrSelfDelegationBelowMinimum]: "validator's self delegation must be greater than their minimum self delegation",
    [index_js_1.ChainStakingErrorCodes.ErrMinSelfDelegationDecreased]: 'minimum self delegation cannot be decrease',
    [index_js_1.ChainStakingErrorCodes.ErrEmptyDelegatorAddr]: 'empty delegator address',
    [index_js_1.ChainStakingErrorCodes.ErrNoDelegation]: 'no delegation for (address, validator) tuple',
    [index_js_1.ChainStakingErrorCodes.ErrBadDelegatorAddr]: 'delegator does not exist with address',
    [index_js_1.ChainStakingErrorCodes.ErrNoDelegatorForAddress]: 'delegator does not contain delegation',
    [index_js_1.ChainStakingErrorCodes.ErrInsufficientShares]: 'insufficient delegation shares',
    [index_js_1.ChainStakingErrorCodes.ErrDelegationValidatorEmpty]: 'cannot delegate to an empty validator',
    [index_js_1.ChainStakingErrorCodes.ErrNotEnoughDelegationShares]: 'not enough delegation shares',
    [index_js_1.ChainStakingErrorCodes.ErrNotMature]: 'entry not mature',
    [index_js_1.ChainStakingErrorCodes.ErrNoUnbondingDelegation]: 'no unbonding delegation found',
    [index_js_1.ChainStakingErrorCodes.ErrMaxUnbondingDelegationEntries]: 'too many unbonding delegation entries for (delegator, validator) tuple',
    [index_js_1.ChainStakingErrorCodes.ErrNoRedelegation]: 'no redelegation found',
    [index_js_1.ChainStakingErrorCodes.ErrSelfRedelegation]: 'cannot redelegate to the same validator',
    [index_js_1.ChainStakingErrorCodes.ErrTinyRedelegationAmount]: 'too few tokens to redelegate (truncates to zero tokens)',
    [index_js_1.ChainStakingErrorCodes.ErrBadRedelegationDst]: 'redelegation destination validator not found',
    [index_js_1.ChainStakingErrorCodes.ErrTransitiveRedelegation]: 'redelegation to this validator already in progress; first redelegation to this validator must complete before next redelegation',
    [index_js_1.ChainStakingErrorCodes.ErrMaxRedelegationEntries]: 'too many redelegation entries for (delegator, src-validator, dst-validator) tuple',
    [index_js_1.ChainStakingErrorCodes.ErrDelegatorShareExRateInvalid]: 'cannot delegate to validators with invalid (zero) ex-rate',
    [index_js_1.ChainStakingErrorCodes.ErrBothShareMsgsGiven]: 'both shares amount and shares percent provided',
    [index_js_1.ChainStakingErrorCodes.ErrNeitherShareMsgsGiven]: 'neither shares amount nor shares percent provided',
    [index_js_1.ChainStakingErrorCodes.ErrInvalidHistoricalInfo]: 'invalid historical info',
    [index_js_1.ChainStakingErrorCodes.ErrNoHistoricalInfo]: 'no historical info found',
    [index_js_1.ChainStakingErrorCodes.ErrEmptyValidatorPubKey]: 'empty validator public key',
    [index_js_1.ChainStakingErrorCodes.ErrCommissionLTMinRate]: 'commission cannot be less than min rate',
    [index_js_1.ChainStakingErrorCodes.ErrUnbondingNotFound]: 'unbonding operation not found',
    [index_js_1.ChainStakingErrorCodes.ErrUnbondingOnHoldRefCountNegative]: 'cannot un-hold unbonding operation that is not on hold',
};
const govErrorMap = {
    [index_js_1.ChainGovErrorCodes.ErrUnknownProposal]: 'unknown proposal',
    [index_js_1.ChainGovErrorCodes.ErrInactiveProposal]: 'inactive proposal',
    [index_js_1.ChainGovErrorCodes.ErrAlreadyActiveProposal]: 'proposal already active',
    [index_js_1.ChainGovErrorCodes.ErrInvalidProposalContent]: 'invalid proposal content',
    [index_js_1.ChainGovErrorCodes.ErrInvalidProposalType]: 'invalid proposal type',
    [index_js_1.ChainGovErrorCodes.ErrInvalidVote]: 'invalid vote option',
    [index_js_1.ChainGovErrorCodes.ErrInvalidGenesis]: 'invalid genesis state',
    [index_js_1.ChainGovErrorCodes.ErrNoProposalHandlerExists]: 'no handler exists for proposal type',
    [index_js_1.ChainGovErrorCodes.ErrUnroutableProposalMsg]: 'proposal message not recognized by router',
    [index_js_1.ChainGovErrorCodes.ErrNoProposalMsgs]: 'no messages proposed',
    [index_js_1.ChainGovErrorCodes.ErrInvalidProposalMsg]: 'invalid proposal message',
    [index_js_1.ChainGovErrorCodes.ErrInvalidSigner]: 'expected gov account as only signer for proposal message',
    [index_js_1.ChainGovErrorCodes.ErrInvalidSignalMsg]: 'signal message is invalid',
    [index_js_1.ChainGovErrorCodes.ErrMetadataTooLong]: 'metadata too long',
    [index_js_1.ChainGovErrorCodes.ErrMinDepositTooSmall]: 'minimum deposit is too small',
    [index_js_1.ChainGovErrorCodes.ErrProposalNotFound]: 'proposal is not found',
    [index_js_1.ChainGovErrorCodes.ErrInvalidProposer]: 'invalid proposer',
    [index_js_1.ChainGovErrorCodes.ErrNoDeposits]: 'no deposits found',
    [index_js_1.ChainGovErrorCodes.ErrVotingPeriodEnded]: 'voting period already ended',
    [index_js_1.ChainGovErrorCodes.ErrInvalidProposal]: 'invalid proposal',
};
const bankErrorMap = {
    [index_js_1.ChainBankErrorCodes.ErrNoInputs]: 'no inputs to send transaction',
    [index_js_1.ChainBankErrorCodes.ErrNoOutputs]: 'no outputs to send transaction',
    [index_js_1.ChainBankErrorCodes.ErrInputOutputMismatch]: 'sum inputs != sum outputs',
    [index_js_1.ChainBankErrorCodes.ErrSendDisabled]: 'send transactions are disabled',
    [index_js_1.ChainBankErrorCodes.ErrDenomMetadataNotFound]: 'client denom metadata not found',
    [index_js_1.ChainBankErrorCodes.ErrInvalidKey]: 'invalid key',
    [index_js_1.ChainBankErrorCodes.ErrDuplicateEntry]: 'duplicate entry',
    [index_js_1.ChainBankErrorCodes.ErrMultipleSenders]: 'multiple senders not allowed',
};
const distributionErrorMap = {
    [index_js_1.ChainDistributionErrorCodes.ErrEmptyDelegatorAddr]: 'delegator address is empty',
    [index_js_1.ChainDistributionErrorCodes.ErrEmptyWithdrawAddr]: 'withdraw address is empty',
    [index_js_1.ChainDistributionErrorCodes.ErrEmptyValidatorAddr]: 'validator address is empty',
    [index_js_1.ChainDistributionErrorCodes.ErrEmptyDelegationDistInfo]: 'no delegation distribution info',
    [index_js_1.ChainDistributionErrorCodes.ErrNoValidatorDistInfo]: 'no validator distribution info',
    [index_js_1.ChainDistributionErrorCodes.ErrNoValidatorCommission]: 'no validator commission to withdraw',
    [index_js_1.ChainDistributionErrorCodes.ErrSetWithdrawAddrDisabled]: 'set withdraw address disabled',
    [index_js_1.ChainDistributionErrorCodes.ErrBadDistribution]: 'community pool does not have sufficient coins to distribute',
    [index_js_1.ChainDistributionErrorCodes.ErrInvalidProposalAmount]: 'invalid community pool spend proposal amount',
    [index_js_1.ChainDistributionErrorCodes.ErrEmptyProposalRecipient]: 'invalid community pool spend proposal recipient',
    [index_js_1.ChainDistributionErrorCodes.ErrNoValidatorExists]: 'validator does not exist',
    [index_js_1.ChainDistributionErrorCodes.ErrNoDelegationExists]: 'delegation does not exist',
};
const wasmErrorMap = {
    [index_js_1.ChainWasmErrorCodes.ErrCreateFailed]: 'create wasm contract failed',
    [index_js_1.ChainWasmErrorCodes.ErrAccountExists]: 'contract account already exists',
    [index_js_1.ChainWasmErrorCodes.ErrInstantiateFailed]: 'instantiate wasm contract failed',
    [index_js_1.ChainWasmErrorCodes.ErrExecuteFailed]: 'Contract execution failed',
    [index_js_1.ChainWasmErrorCodes.ErrGasLimit]: 'insufficient gas',
    [index_js_1.ChainWasmErrorCodes.ErrInvalidGenesis]: 'invalid genesis',
    [index_js_1.ChainWasmErrorCodes.ErrNotFound]: 'not found',
    [index_js_1.ChainWasmErrorCodes.ErrQueryFailed]: 'query wasm contract failed',
    [index_js_1.ChainWasmErrorCodes.ErrInvalidMsg]: 'invalid CosmosMsg from the contract',
    [index_js_1.ChainWasmErrorCodes.ErrMigrationFailed]: 'migrate wasm contract failed',
    [index_js_1.ChainWasmErrorCodes.ErrEmpty]: 'empty',
    [index_js_1.ChainWasmErrorCodes.ErrLimit]: 'exceeds limit',
    [index_js_1.ChainWasmErrorCodes.ErrInvalid]: 'invalid',
    [index_js_1.ChainWasmErrorCodes.ErrDuplicate]: 'duplicate',
    [index_js_1.ChainWasmErrorCodes.ErrMaxIBCChannels]: 'max transfer channels',
    [index_js_1.ChainWasmErrorCodes.ErrUnsupportedForContract]: 'unsupported for this contract',
    [index_js_1.ChainWasmErrorCodes.ErrPinContractFailed]: 'pinning contract failed',
    [index_js_1.ChainWasmErrorCodes.ErrUnpinContractFailed]: 'unpinning contract failed',
    [index_js_1.ChainWasmErrorCodes.ErrUnknownMsg]: 'unknown message from the contract',
    [index_js_1.ChainWasmErrorCodes.ErrInvalidEvent]: 'invalid event',
};
exports.chainModuleCodeErrorMessagesMap = {
    [index_js_1.TransactionChainErrorModule.AuthZ]: authZErrorMap,
    [index_js_1.TransactionChainErrorModule.Auction]: auctionErrorMap,
    [index_js_1.TransactionChainErrorModule.CosmosSdk]: cosmosErrorMap,
    [index_js_1.TransactionChainErrorModule.Exchange]: exchangeErrorMap,
    [index_js_1.TransactionChainErrorModule.Insurance]: insuranceErrorMap,
    [index_js_1.TransactionChainErrorModule.Ocr]: ocrErrorMap,
    [index_js_1.TransactionChainErrorModule.Oracle]: oracleErrorMap,
    [index_js_1.TransactionChainErrorModule.Peggy]: peggyErrorMap,
    [index_js_1.TransactionChainErrorModule.TokenFactory]: tokenFactoryErrorMap,
    [index_js_1.TransactionChainErrorModule.Wasmx]: wasmxErrorMap,
    [index_js_1.TransactionChainErrorModule.Wasm]: wasmErrorMap,
    [index_js_1.TransactionChainErrorModule.Staking]: stakingErrorMap,
    [index_js_1.TransactionChainErrorModule.Bank]: bankErrorMap,
    [index_js_1.TransactionChainErrorModule.Gov]: govErrorMap,
    [index_js_1.TransactionChainErrorModule.Distribution]: distributionErrorMap,
};
/**
 * **Legacy** but needed for error messages from broadcasting transactions
 * where we don't control the response and only have the message
 * i.e Keplr, Leap, etc
 */
exports.chainErrorMessagesMap = {
    'insufficient fee': {
        message: 'You do not have enough funds to cover the transaction fees.',
        code: index_js_1.ChainCosmosErrorCode.ErrInsufficientFee,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'insufficient funds': {
        message: 'You do not have enough funds.',
        code: index_js_1.ChainCosmosErrorCode.ErrInsufficientFunds,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'tx timeout height': {
        message: 'The transaction failed to be included within a block on time.',
        code: index_js_1.ChainCosmosErrorCode.ErrTxTimeoutHeight,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'tx parse error': {
        message: 'There is an issue while parsing the transaction',
        code: index_js_1.ChainCosmosErrorCode.ErrTxDecode,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid sequence': {
        message: 'The sequence number is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidSequence,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    unauthorized: {
        message: 'Unauthorized',
        code: index_js_1.ChainCosmosErrorCode.ErrUnauthorized,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid address': {
        message: 'The address is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidAddress,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'cosmos account not exists': {
        message: 'You need to create your address on Injective by transferring funds',
        code: index_js_1.ChainCosmosErrorCode.ErrInsufficientFee,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid pubkey': {
        message: 'The public key is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidPubKey,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'unknown address': {
        message: 'The address is unknown',
        code: index_js_1.ChainCosmosErrorCode.ErrUnknownAddress,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid coins': {
        message: 'The coins are not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidCoins,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'out of gas': {
        message: 'The transaction run out of gas',
        code: index_js_1.ChainCosmosErrorCode.ErrOutOfGas,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'memo too large': {
        message: 'The memo field in the transaction is too large',
        code: index_js_1.ChainCosmosErrorCode.ErrMemoTooLarge,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'maximum number of signatures exceeded': {
        message: 'The transaction exceeded the maximum number of signatures',
        code: index_js_1.ChainCosmosErrorCode.ErrTooManySignatures,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'no signatures supplied': {
        message: 'There are no signatures appended on the transaction',
        code: index_js_1.ChainCosmosErrorCode.ErrNoSignatures,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'failed to marshal JSON bytes': {
        message: 'There is an issue while parsing the transaction',
        code: index_js_1.ChainCosmosErrorCode.ErrJSONMarshal,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'failed to unmarshal JSON bytes': {
        message: 'There is an issue while parsing the transaction',
        code: index_js_1.ChainCosmosErrorCode.ErrJSONUnmarshal,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid request': {
        message: 'invalid request',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidRequest,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'tx already in mempool': {
        message: 'The transaction is already in the mempool',
        code: index_js_1.ChainCosmosErrorCode.ErrTxInMempoolCache,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'mempool is full': {
        message: 'The transaction failed to be included within a block on time.',
        code: index_js_1.ChainCosmosErrorCode.ErrMempoolIsFull,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'tx too large': {
        message: 'The transaction is too large',
        code: index_js_1.ChainCosmosErrorCode.ErrTxTooLarge,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'key not found': {
        message: 'Address is not yet active on the chain as it has a $0 balance. To activate it, please send a small amount of funds. Once funded, the network will recognize the address, allowing you to proceed.',
        code: index_js_1.ChainCosmosErrorCode.ErrKeyNotFound,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid account password': {
        message: 'invalid account password',
        code: index_js_1.ChainCosmosErrorCode.ErrWrongPassword,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'tx intended signer does not match the given signer': {
        message: 'tx intended signer does not match the given signer',
        code: index_js_1.ChainCosmosErrorCode.ErrorInvalidSigner,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid gas adjustment': {
        message: 'invalid gas adjustment',
        code: index_js_1.ChainCosmosErrorCode.ErrorInvalidGasAdjustment,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid height': {
        message: 'The height provided in the transaction is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidHeight,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid version': {
        message: 'The version provided in the transaction is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidVersion,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid chain-id': {
        message: 'The chainId provided in the transaction is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidChainID,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid type': {
        message: 'The type provided in the transaction is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidType,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'unknown extension options': {
        message: 'The extension options provided in the transaction is unknown',
        code: index_js_1.ChainCosmosErrorCode.ErrUnknownExtensionOptions,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'incorrect account sequence': {
        message: 'The sequence number provided in the transaction is incorrect',
        code: index_js_1.ChainCosmosErrorCode.ErrWrongSequence,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'failed packing protobuf message to Any': {
        message: 'failed packing protobuf message to Any',
        code: index_js_1.ChainCosmosErrorCode.ErrPackAny,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'failed unpacking protobuf message from Any': {
        message: 'failed unpacking protobuf message from Any',
        code: index_js_1.ChainCosmosErrorCode.ErrUnpackAny,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'internal logic error': {
        message: 'Internal logic error',
        code: index_js_1.ChainCosmosErrorCode.ErrLogic,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    conflict: {
        message: 'conflict',
        code: index_js_1.ChainCosmosErrorCode.ErrConflict,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'feature not supported': {
        message: 'The feature is not supported',
        code: index_js_1.ChainCosmosErrorCode.ErrNotSupported,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'Internal IO error': {
        message: 'Internal IO error',
        code: index_js_1.ChainCosmosErrorCode.ErrIO,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'error in app.toml': {
        message: 'error in app.toml',
        code: index_js_1.ChainCosmosErrorCode.ErrAppConfig,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'invalid gas limit': {
        message: 'The gas limit provided in the transaction is not valid',
        code: index_js_1.ChainCosmosErrorCode.ErrInvalidGasLimit,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    // Auction Module Messages
    'invalid bid denom': {
        message: 'The gas limit provided in the transaction is not valid',
        code: index_js_1.ChainAuctionErrorCodes.ErrBidInvalid,
        module: index_js_1.TransactionChainErrorModule.Auction,
    },
    'invalid bid round': {
        message: 'The gas limit provided in the transaction is not valid',
        code: index_js_1.ChainAuctionErrorCodes.ErrBidRound,
        module: index_js_1.TransactionChainErrorModule.Auction,
    },
    // Insurance Module Messages
    'insurance fund already exists': {
        message: 'The insurance fund already exists',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInsuranceFundAlreadyExists,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'insurance fund not found': {
        message: 'The insurance fund is not found',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInsuranceFundNotFound,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'redemption already exists': {
        message: 'The redemption already exists',
        code: index_js_1.ChainInsuranceErrorCodes.ErrRedemptionAlreadyExists,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid deposit amount': {
        message: 'The deposit amount is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidDepositAmount,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid deposit denom': {
        message: 'The deposit denom is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidDepositDenom,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'insurance payout exceeds deposits': {
        message: 'The insurance fund payout exceeds the deposits',
        code: index_js_1.ChainInsuranceErrorCodes.ErrPayoutTooLarge,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid ticker': {
        message: 'The ticker is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidTicker,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid quote denom': {
        message: 'The quote denom is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidQuoteDenom,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid oracle': {
        message: 'The oracle is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidOracle,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid expiration time': {
        message: 'The expiration time is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidExpirationTime,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid marketID': {
        message: 'The marketId is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidMarketID,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    'invalid share denom': {
        message: 'The share denom is not valid',
        code: index_js_1.ChainInsuranceErrorCodes.ErrInvalidShareDenom,
        module: index_js_1.TransactionChainErrorModule.Insurance,
    },
    // Exchange Module Messages
    'failed to validate order': {
        message: 'Your order failed to validate',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrOrderInvalid,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'spot market not found': {
        message: 'The spot market has not been found',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrSpotMarketNotFound,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'spot market exists': {
        message: 'The spot market already exists',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrSpotMarketExists,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'struct field error': {
        message: 'There is an issue with your order',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrBadField,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'failed to validate market': {
        message: 'The market failed to validate',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrMarketInvalid,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'subaccount has insufficient deposits': {
        message: 'Your trading account has insufficient funds',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInsufficientDeposit,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'unrecognized order type': {
        message: 'The order type is not recognized',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrUnrecognizedOrderType,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'position quantity insufficient for order': {
        message: 'The position quantity is insufficient for the order',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInsufficientPositionQuantity,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'order hash is not valid': {
        message: 'The order hash is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrOrderHashInvalid,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'subaccount id is not valid': {
        message: 'The subaccount id is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrBadSubaccountID,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid base denom': {
        message: '',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidBaseDenom,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid expiry': {
        message: 'The expiry date is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidExpiry,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid price': {
        message: 'The price is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidPrice,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid quantity': {
        message: 'The quantity is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidQuantity,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'unsupported oracle type': {
        message: 'The oracle type is not supported',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrUnsupportedOracleType,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'order doesnt exist': {
        message: 'The order does not exist',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrOrderDoesntExist,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'spot limit orderbook fill invalid': {
        message: '',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrOrderbookFillInvalid,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'perpetual market exists': {
        message: 'The perpetual market already exists',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrPerpetualMarketExists,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'expiry futures market exists': {
        message: 'The expiry futures market market already exists',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrExpiryFuturesMarketExists,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'expiry futures market expired': {
        message: 'The expiry futures market has expired',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrExpiryFuturesMarketExpired,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'no liquidity on the orderbook': {
        message: 'There is not enough liquidity',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrNoLiquidity,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'orderbook liquidity cannot satisfy current worst price': {
        message: 'There is not enough liquidity',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrSlippageExceedsWorstPrice,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'order has insufficient margin': {
        message: 'The order has insufficient margin',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInsufficientOrderMargin,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'derivative market not found': {
        message: 'The derivative market cannot be found',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrDerivativeMarketNotFound,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'position not found': {
        message: 'The position cannot be found',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrPositionNotFound,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'position direction does not oppose the reduce-only order': {
        message: 'Position direction does not oppose the reduce-only order',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidReduceOnlyPositionDirection,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'price surpasses bankruptcy price': {
        message: 'Your order price surpasses bankruptcy price',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrPriceSurpassesBankruptcyPrice,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'position not liquidable': {
        message: 'The position is not liquidable',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrPositionNotLiquidable,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid trigger price': {
        message: 'Your order trigger price is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTriggerPrice,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid oracle type': {
        message: 'The oracle type is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidOracleType,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid minimum price tick size': {
        message: 'The minimum price tick size is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidPriceTickSize,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid minimum quantity tick size': {
        message: 'The minimum quantity tick size is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidQuantityTickSize,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid minimum order margin': {
        message: "Your order's minimum margin is not valid ",
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMargin,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'exceeds order side count': {
        message: 'You cannot have more orders for this market for this direction',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrExceedsOrderSideCount,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'subaccount cannot place a market order when a market order in the same market was already placed in same block': {
        message: 'You cannot place another market order within this block',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrMarketOrderAlreadyExists,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'cannot place a conditional market order when a conditional market order in same relative direction already exists': {
        message: 'You cannot place another conditional market order',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrConditionalMarketOrderAlreadyExists,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'an equivalent market launch proposal already exists.': {
        message: 'There is an existing equivalent market launch proposal.',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrMarketLaunchProposalAlreadyExists,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid market status': {
        message: 'The market status is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMarketStatus,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'base denom cannot be same with quote denom': {
        message: 'The base denom and quote denom cannot be same',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrSameDenoms,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'oracle base cannot be same with oracle quote': {
        message: 'The oracle base and the oracle quote cannot be the same',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrSameOracles,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'makerfeerate does not match takerfeeeate requirements': {
        message: 'The MakerFeeRate does not match TakerFeeRate requirements',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrFeeRatesRelation,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'maintenancemarginratio cannot be greater than initialmarginratio': {
        message: 'The MaintenanceMarginRatio cannot be greater than InitialMarginRatio',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrMarginsRelation,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'oraclescalefactor cannot be greater than maxoraclescalefactor': {
        message: 'The OracleScaleFactor cannot be greater than MaxOracleScaleFactor',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrExceedsMaxOracleScaleFactor,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'spot exchange is not enabled yet': {
        message: 'Spot exchange is not enabled yet',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrSpotExchangeNotEnabled,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'derivatives exchange is not enabled yet': {
        message: 'Derivatives exchange is not enabled yet',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrDerivativesExchangeNotEnabled,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'oracle price delta exceeds threshold': {
        message: 'The oracle price delta exceeds threshold',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrOraclePriceDeltaExceedsThreshold,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid hourly interest rate': {
        message: 'The hourly interest rate is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidHourlyInterestRate,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid hourly funding rate cap': {
        message: 'The hourly funding rate cap is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidHourlyFundingRateCap,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'only perpetual markets can update funding parameters': {
        message: 'You can only update funding parameters on perpetual markets.',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMarketFundingParamUpdate,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid trading reward campaign': {
        message: 'The trading reward campaign is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTradingRewardCampaign,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid fee discount schedule': {
        message: 'The fee discount schedule is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidFeeDiscountSchedule,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid liquidation order': {
        message: 'The liquidation order is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidLiquidationOrder,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'unknown error happened for campaign distributions': {
        message: 'Unknown error happened for campaign distributions',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrTradingRewardCampaignDistributionError,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid trading reward points update': {
        message: 'The updated trading reward points is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTradingRewardsPendingPointsUpdate,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid batch msg update': {
        message: 'The MsgBatchUpdate is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidBatchMsgUpdate,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'post-only order exceeds top of book price': {
        message: 'The post-only order price exceeds top of the orderbook price',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrExceedsTopOfBookPrice,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'order type not supported for given message': {
        message: 'The order type is not supported for this message',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidOrderTypeForMessage,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'sender must match dmm account': {
        message: 'The sender must match the DMM address',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidDMMSender,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'already opted out of rewards': {
        message: 'The DMM address already opted out of rewards',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrAlreadyOptedOutOfRewards,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid margin ratio': {
        message: 'The margin ratio is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidMarginRatio,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'provided funds are below minimum': {
        message: 'The provided funds are below minimum',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrBelowMinimalContribution,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'position is below initial margin requirement': {
        message: 'The position is below initial margin requirement',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrLowPositionMargin,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'pool has non-positive total lp token supply': {
        message: 'The pool has non-positive total LP token supply',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTotalSupply,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'passed lp token burn amount is greater than total lp token supply': {
        message: 'The passed LP token burn amount is greater than total LP token supply',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidLpTokenBurnAmount,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'unsupported action': {
        message: 'This action is not supported',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrUnsupportedAction,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'position quantity cannot be negative': {
        message: 'The position quantity cannot be negative',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrNegativePositionQuantity,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'binary options market exists': {
        message: 'The BinaryOptions market already exists',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrBinaryOptionsMarketExists,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'binary options market not found': {
        message: 'The BinaryOptions market cannot be found',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrBinaryOptionsMarketNotFound,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid settlement': {
        message: 'The settlement price is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidSettlement,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'account doesnt exist': {
        message: 'The trading account does not exist',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrAccountDoesntExist,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'sender should be a market admin': {
        message: 'The sender should be the admin of the market',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrSenderIsNotAnAdmin,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'market is already scheduled to settle': {
        message: 'The market is already scheduled to settle ',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrMarketAlreadyScheduledToSettle,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'market not found': {
        message: 'The market cannot be found',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrGenericMarketNotFound,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'denom decimal cannot be below 1 or above max scale factor': {
        message: 'The denom decimal cannot be below 1 or above max scale factor',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidDenomDecimal,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'state is invalid': {
        message: 'The state is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidState,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'transient orders up to cancellation not supported': {
        message: 'The transient orders up to cancellation not supported',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrTransientOrdersUpToCancelNotSupported,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'invalid trade': {
        message: 'The trade is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidTrade,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'no margin locked in subaccount': {
        message: 'There is no margin locked in the trading account',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrNoMarginLocked,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'Invalid access level to perform action': {
        message: 'There is no access to perform action',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidAccessLevel,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'Invalid address': {
        message: 'The address is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidAddress,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'Invalid argument': {
        message: 'The argument is not valid',
        code: index_js_1.ChainExchangeModuleErrorCode.ErrInvalidArgument,
        module: index_js_1.TransactionChainErrorModule.Exchange,
    },
    'empty validator address': {
        message: 'empty validator address',
        code: index_js_1.ChainStakingErrorCodes.ErrEmptyValidatorAddr,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'validator does not exist': {
        message: 'validator does not exist',
        code: index_js_1.ChainStakingErrorCodes.ErrNoValidatorFound,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'validator already exist for this operator address; must use new validator operator address': {
        message: 'validator already exist for this operator address; must use new validator operator address',
        code: index_js_1.ChainStakingErrorCodes.ErrValidatorOwnerExists,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'validator already exist for this pubkey; must use new validator pubkey': {
        message: 'validator already exist for this pubkey; must use new validator pubkey',
        code: index_js_1.ChainStakingErrorCodes.ErrValidatorPubKeyExists,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'validator pubkey type is not supported': {
        message: 'validator pubkey type is not supported',
        code: index_js_1.ChainStakingErrorCodes.ErrValidatorPubKeyTypeNotSupported,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'validator for this address is currently jailed': {
        message: 'validator for this address is currently jailed',
        code: index_js_1.ChainStakingErrorCodes.ErrValidatorJailed,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'failed to remove validator': {
        message: 'failed to remove validator',
        code: index_js_1.ChainStakingErrorCodes.ErrBadRemoveValidator,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission must be positive': {
        message: 'commission must be positive',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionNegative,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission cannot be more than 100%': {
        message: 'commission cannot be more than 100%',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionHuge,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission cannot be more than the max rate': {
        message: 'commission cannot be more than the max rate',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionGTMaxRate,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission cannot be changed more than once in 24h': {
        message: 'commission cannot be changed more than once in 24h',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionUpdateTime,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission change rate must be positive': {
        message: 'commission change rate must be positive',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionChangeRateNegative,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission change rate cannot be more than the max rate': {
        message: 'commission change rate cannot be more than the max rate',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionChangeRateGTMaxRate,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission cannot be changed more than max change rate': {
        message: 'commission cannot be changed more than max change rate',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionGTMaxChangeRate,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    "validator's self delegation must be greater than their minimum self delegation": {
        message: "validator's self delegation must be greater than their minimum self delegation",
        code: index_js_1.ChainStakingErrorCodes.ErrSelfDelegationBelowMinimum,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'minimum self delegation cannot be decrease': {
        message: 'minimum self delegation cannot be decrease',
        code: index_js_1.ChainStakingErrorCodes.ErrMinSelfDelegationDecreased,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'empty delegator address': {
        message: 'empty delegator address',
        code: index_js_1.ChainStakingErrorCodes.ErrEmptyDelegatorAddr,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'no delegation for (address, validator) tuple': {
        message: 'no delegation for (address, validator) tuple',
        code: index_js_1.ChainStakingErrorCodes.ErrNoDelegation,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'delegator does not exist with address': {
        message: 'delegator does not exist with address',
        code: index_js_1.ChainStakingErrorCodes.ErrBadDelegatorAddr,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'delegator does not contain delegation': {
        message: 'delegator does not contain delegation',
        code: index_js_1.ChainStakingErrorCodes.ErrNoDelegatorForAddress,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'insufficient delegation shares': {
        message: 'insufficient delegation shares',
        code: index_js_1.ChainStakingErrorCodes.ErrInsufficientShares,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'cannot delegate to an empty validator': {
        message: 'cannot delegate to an empty validator',
        code: index_js_1.ChainStakingErrorCodes.ErrDelegationValidatorEmpty,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'not enough delegation shares': {
        message: 'not enough delegation shares',
        code: index_js_1.ChainStakingErrorCodes.ErrNotEnoughDelegationShares,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'entry not mature': {
        message: 'entry not mature',
        code: index_js_1.ChainStakingErrorCodes.ErrNotMature,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'no unbonding delegation found': {
        message: 'no unbonding delegation found',
        code: index_js_1.ChainStakingErrorCodes.ErrNoUnbondingDelegation,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'too many unbonding delegation entries for (delegator, validator) tuple': {
        message: 'too many unbonding delegation entries for (delegator, validator) tuple',
        code: index_js_1.ChainStakingErrorCodes.ErrMaxUnbondingDelegationEntries,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'no redelegation found': {
        message: 'no redelegation found',
        code: index_js_1.ChainStakingErrorCodes.ErrNoRedelegation,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'cannot redelegate to the same validator': {
        message: 'cannot redelegate to the same validator',
        code: index_js_1.ChainStakingErrorCodes.ErrSelfRedelegation,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'too few tokens to redelegate (truncates to zero tokens)': {
        message: 'too few tokens to redelegate (truncates to zero tokens)',
        code: index_js_1.ChainStakingErrorCodes.ErrTinyRedelegationAmount,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'redelegation destination validator not found': {
        message: 'redelegation destination validator not found',
        code: index_js_1.ChainStakingErrorCodes.ErrBadRedelegationDst,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'redelegation to this validator already in progress; first redelegation to this validator must complete before next redelegation': {
        message: 'redelegation to this validator already in progress; first redelegation to this validator must complete before next redelegation',
        code: index_js_1.ChainStakingErrorCodes.ErrTransitiveRedelegation,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'too many redelegation entries for (delegator, src-validator, dst-validator) tuple': {
        message: 'too many redelegation entries for (delegator, src-validator, dst-validator) tuple',
        code: index_js_1.ChainStakingErrorCodes.ErrMaxRedelegationEntries,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'cannot delegate to validators with invalid (zero) ex-rate': {
        message: 'cannot delegate to validators with invalid (zero) ex-rate',
        code: index_js_1.ChainStakingErrorCodes.ErrDelegatorShareExRateInvalid,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'both shares amount and shares percent provided': {
        message: 'both shares amount and shares percent provided',
        code: index_js_1.ChainStakingErrorCodes.ErrBothShareMsgsGiven,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'neither shares amount nor shares percent provided': {
        message: 'neither shares amount nor shares percent provided',
        code: index_js_1.ChainStakingErrorCodes.ErrNeitherShareMsgsGiven,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'invalid historical info': {
        message: 'invalid historical info',
        code: index_js_1.ChainStakingErrorCodes.ErrInvalidHistoricalInfo,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'no historical info found': {
        message: 'no historical info found',
        code: index_js_1.ChainStakingErrorCodes.ErrNoHistoricalInfo,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'empty validator public key': {
        message: 'empty validator public key',
        code: index_js_1.ChainStakingErrorCodes.ErrEmptyValidatorPubKey,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'commission cannot be less than min rate': {
        message: 'commission cannot be less than min rate',
        code: index_js_1.ChainStakingErrorCodes.ErrCommissionLTMinRate,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'unbonding operation not found': {
        message: 'unbonding operation not found',
        code: index_js_1.ChainStakingErrorCodes.ErrUnbondingNotFound,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'cannot un-hold unbonding operation that is not on hold': {
        message: 'cannot un-hold unbonding operation that is not on hold',
        code: index_js_1.ChainStakingErrorCodes.ErrUnbondingOnHoldRefCountNegative,
        module: index_js_1.TransactionChainErrorModule.Staking,
    },
    'delegator address is empty': {
        message: 'delegator address is empty',
        code: index_js_1.ChainDistributionErrorCodes.ErrEmptyDelegatorAddr,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'withdraw address is empty': {
        message: 'withdraw address is empty',
        code: index_js_1.ChainDistributionErrorCodes.ErrEmptyWithdrawAddr,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'validator address is empty': {
        message: 'validator address is empty',
        code: index_js_1.ChainDistributionErrorCodes.ErrEmptyValidatorAddr,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'no delegation distribution info': {
        message: 'no delegation distribution info',
        code: index_js_1.ChainDistributionErrorCodes.ErrEmptyDelegationDistInfo,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'no validator distribution info': {
        message: 'no validator distribution info',
        code: index_js_1.ChainDistributionErrorCodes.ErrNoValidatorDistInfo,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'no validator commission to withdraw': {
        message: 'no validator commission to withdraw',
        code: index_js_1.ChainDistributionErrorCodes.ErrNoValidatorCommission,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'set withdraw address disabled': {
        message: 'set withdraw address disabled',
        code: index_js_1.ChainDistributionErrorCodes.ErrSetWithdrawAddrDisabled,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'community pool does not have sufficient coins to distribute': {
        message: 'community pool does not have sufficient coins distribute',
        code: index_js_1.ChainDistributionErrorCodes.ErrBadDistribution,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'invalid community pool spend proposal amount': {
        message: 'invalid community pool spend proposal amount',
        code: index_js_1.ChainDistributionErrorCodes.ErrInvalidProposalAmount,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'invalid community pool spend proposal recipient': {
        message: 'invalid community pool spend proposal recipient',
        code: index_js_1.ChainDistributionErrorCodes.ErrEmptyProposalRecipient,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'delegation does not exist': {
        message: 'delegation does not exist',
        code: index_js_1.ChainDistributionErrorCodes.ErrNoDelegationExists,
        module: index_js_1.TransactionChainErrorModule.Distribution,
    },
    'unknown proposal': {
        message: 'unknown proposal',
        code: index_js_1.ChainGovErrorCodes.ErrUnknownProposal,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'inactive proposal': {
        message: 'inactive proposal',
        code: index_js_1.ChainGovErrorCodes.ErrInactiveProposal,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'proposal already active': {
        message: 'proposal already active',
        code: index_js_1.ChainGovErrorCodes.ErrAlreadyActiveProposal,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'invalid proposal content': {
        message: 'invalid proposal content',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidProposalContent,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'invalid proposal type': {
        message: 'invalid proposal type',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidProposalType,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'invalid vote option': {
        message: 'invalid vote option',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidVote,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'invalid genesis state': {
        message: 'invalid genesis state',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidGenesis,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'no handler exists for proposal type': {
        message: 'no handler exists for proposal type',
        code: index_js_1.ChainGovErrorCodes.ErrNoProposalHandlerExists,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'proposal message not recognized by router': {
        message: 'proposal message not recognized by router',
        code: index_js_1.ChainGovErrorCodes.ErrUnroutableProposalMsg,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'no messages proposed': {
        message: 'no messages proposed',
        code: index_js_1.ChainGovErrorCodes.ErrNoProposalMsgs,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'invalid proposal message': {
        message: 'invalid proposal message',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidProposalMsg,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'expected gov account as only signer for proposal message': {
        message: 'expected gov account as only signer for proposal message',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidSigner,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'signal message is invalid': {
        message: 'signal message is invalid',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidSignalMsg,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'metadata too long': {
        message: 'metadata too long',
        code: index_js_1.ChainGovErrorCodes.ErrMetadataTooLong,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'minimum deposit is too small': {
        message: 'minimum deposit is too small',
        code: index_js_1.ChainGovErrorCodes.ErrMinDepositTooSmall,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'proposal is not found': {
        message: 'proposal is not found',
        code: index_js_1.ChainGovErrorCodes.ErrProposalNotFound,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'invalid proposer': {
        message: 'invalid proposer',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidProposer,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'no deposits found': {
        message: 'no deposits found',
        code: index_js_1.ChainGovErrorCodes.ErrNoDeposits,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'voting period already ended': {
        message: 'voting period already ended',
        code: index_js_1.ChainGovErrorCodes.ErrVotingPeriodEnded,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'invalid proposal': {
        message: 'invalid proposal',
        code: index_js_1.ChainGovErrorCodes.ErrInvalidProposal,
        module: index_js_1.TransactionChainErrorModule.Gov,
    },
    'no inputs to send transaction': {
        message: 'no inputs to send transaction',
        code: index_js_1.ChainBankErrorCodes.ErrNoInputs,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'no outputs to send transaction': {
        message: 'no outputs to send transaction',
        code: index_js_1.ChainBankErrorCodes.ErrNoOutputs,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'sum inputs != sum outputs': {
        message: 'sum inputs != sum outputs',
        code: index_js_1.ChainBankErrorCodes.ErrInputOutputMismatch,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'send transactions are disabled': {
        message: 'send transactions are disabled',
        code: index_js_1.ChainBankErrorCodes.ErrSendDisabled,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'client denom metadata not found': {
        message: 'client denom metadata not found',
        code: index_js_1.ChainBankErrorCodes.ErrDenomMetadataNotFound,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'invalid key': {
        message: 'invalid key',
        code: index_js_1.ChainBankErrorCodes.ErrInvalidKey,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'duplicate entry': {
        message: 'duplicate entry',
        code: index_js_1.ChainBankErrorCodes.ErrDuplicateEntry,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'multiple senders not allowed': {
        message: 'multiple senders not allowed',
        code: index_js_1.ChainBankErrorCodes.ErrMultipleSenders,
        module: index_js_1.TransactionChainErrorModule.Bank,
    },
    'create wasm contract failed': {
        code: index_js_1.ChainWasmErrorCodes.ErrCreateFailed,
        message: 'create wasm contract failed',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'contract account already exists': {
        code: index_js_1.ChainWasmErrorCodes.ErrAccountExists,
        message: 'contract account already exists',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'instantiate wasm contract failed': {
        code: index_js_1.ChainWasmErrorCodes.ErrInstantiateFailed,
        message: 'instantiate wasm contract failed',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'execute wasm contract failed': {
        code: index_js_1.ChainWasmErrorCodes.ErrExecuteFailed,
        message: 'Contract execution failed',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'insufficient gas': {
        code: index_js_1.ChainWasmErrorCodes.ErrGasLimit,
        message: 'insufficient gas',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'invalid genesis': {
        code: index_js_1.ChainWasmErrorCodes.ErrInvalidGenesis,
        message: 'invalid genesis',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'query wasm contract failed': {
        code: index_js_1.ChainWasmErrorCodes.ErrQueryFailed,
        message: 'query wasm contract failed',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'invalid CosmosMsg from the contract': {
        code: index_js_1.ChainWasmErrorCodes.ErrInvalidMsg,
        message: 'invalid CosmosMsg from the contract',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'migrate wasm contract failed': {
        code: index_js_1.ChainWasmErrorCodes.ErrMigrationFailed,
        message: 'migrate wasm contract failed',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    empty: {
        code: index_js_1.ChainWasmErrorCodes.ErrEmpty,
        message: 'empty',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'exceeds limit': {
        code: index_js_1.ChainWasmErrorCodes.ErrLimit,
        message: 'exceeds limit',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    invalid: {
        code: index_js_1.ChainWasmErrorCodes.ErrInvalid,
        message: 'invalid',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    duplicate: {
        code: index_js_1.ChainWasmErrorCodes.ErrDuplicate,
        message: 'duplicate',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'max transfer channels': {
        code: index_js_1.ChainWasmErrorCodes.ErrMaxIBCChannels,
        message: 'max transfer channels',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'unsupported for this contract': {
        code: index_js_1.ChainWasmErrorCodes.ErrUnsupportedForContract,
        message: 'unsupported for this contract',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'pinning contract failed': {
        code: index_js_1.ChainWasmErrorCodes.ErrPinContractFailed,
        message: 'pinning contract failed',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'unpinning contract failed': {
        code: index_js_1.ChainWasmErrorCodes.ErrUnpinContractFailed,
        message: 'unpinning contract failed',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'unknown message from the contract': {
        code: index_js_1.ChainWasmErrorCodes.ErrUnknownMsg,
        message: 'unknown message from the contract',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'invalid event': {
        code: index_js_1.ChainWasmErrorCodes.ErrInvalidEvent,
        message: 'invalid event',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'authorization not found': {
        code: index_js_1.ChainAuthZErrorCodes.ErrNoAuthorizationFound,
        message: 'Authorization not found',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'expiration time of authorization': {
        code: index_js_1.ChainAuthZErrorCodes.ErrAuthorizationExpired,
        message: 'Authorization expired',
        module: index_js_1.TransactionChainErrorModule.Wasm,
    },
    'not found': {
        message: 'not found',
        code: index_js_1.ChainCosmosErrorCode.ErrNotFound,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
    'failed to fetch account num/seq': {
        message: 'Address is not yet active on the chain as it has a $0 balance. To activate it, please send a small amount of funds. Once funded, the network will recognize the address, allowing you to proceed.',
        code: index_js_1.ChainCosmosErrorCode.ErrKeyNotFound,
        module: index_js_1.TransactionChainErrorModule.CosmosSdk,
    },
};
