import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Permissions } from "./types";
export declare const protobufPackage = "cosmos.circuit.v1";
/** MsgAuthorizeCircuitBreaker defines the Msg/AuthorizeCircuitBreaker request type. */
export interface MsgAuthorizeCircuitBreaker {
    /**
     * granter is the granter of the circuit breaker permissions and must have
     * LEVEL_SUPER_ADMIN.
     */
    granter: string;
    /** grantee is the account authorized with the provided permissions. */
    grantee: string;
    /**
     * permissions are the circuit breaker permissions that the grantee receives.
     * These will overwrite any existing permissions. LEVEL_NONE_UNSPECIFIED can
     * be specified to revoke all permissions.
     */
    permissions: Permissions | undefined;
}
/** MsgAuthorizeCircuitBreakerResponse defines the Msg/AuthorizeCircuitBreaker response type. */
export interface MsgAuthorizeCircuitBreakerResponse {
    success: boolean;
}
/** MsgTripCircuitBreaker defines the Msg/TripCircuitBreaker request type. */
export interface MsgTripCircuitBreaker {
    /** authority is the account authorized to trip the circuit breaker. */
    authority: string;
    /**
     * msg_type_urls specifies a list of type URLs to immediately stop processing.
     * IF IT IS LEFT EMPTY, ALL MSG PROCESSING WILL STOP IMMEDIATELY.
     * This value is validated against the authority's permissions and if the
     * authority does not have permissions to trip the specified msg type URLs
     * (or all URLs), the operation will fail.
     */
    msgTypeUrls: string[];
}
/** MsgTripCircuitBreakerResponse defines the Msg/TripCircuitBreaker response type. */
export interface MsgTripCircuitBreakerResponse {
    success: boolean;
}
/** MsgResetCircuitBreaker defines the Msg/ResetCircuitBreaker request type. */
export interface MsgResetCircuitBreaker {
    /** authority is the account authorized to trip or reset the circuit breaker. */
    authority: string;
    /**
     * msg_type_urls specifies a list of Msg type URLs to resume processing. If
     * it is left empty all Msg processing for type URLs that the account is
     * authorized to trip will resume.
     */
    msgTypeUrls: string[];
}
/** MsgResetCircuitBreakerResponse defines the Msg/ResetCircuitBreaker response type. */
export interface MsgResetCircuitBreakerResponse {
    success: boolean;
}
export declare const MsgAuthorizeCircuitBreaker: {
    encode(message: MsgAuthorizeCircuitBreaker, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAuthorizeCircuitBreaker;
    fromJSON(object: any): MsgAuthorizeCircuitBreaker;
    toJSON(message: MsgAuthorizeCircuitBreaker): unknown;
    create(base?: DeepPartial<MsgAuthorizeCircuitBreaker>): MsgAuthorizeCircuitBreaker;
    fromPartial(object: DeepPartial<MsgAuthorizeCircuitBreaker>): MsgAuthorizeCircuitBreaker;
};
export declare const MsgAuthorizeCircuitBreakerResponse: {
    encode(message: MsgAuthorizeCircuitBreakerResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAuthorizeCircuitBreakerResponse;
    fromJSON(object: any): MsgAuthorizeCircuitBreakerResponse;
    toJSON(message: MsgAuthorizeCircuitBreakerResponse): unknown;
    create(base?: DeepPartial<MsgAuthorizeCircuitBreakerResponse>): MsgAuthorizeCircuitBreakerResponse;
    fromPartial(object: DeepPartial<MsgAuthorizeCircuitBreakerResponse>): MsgAuthorizeCircuitBreakerResponse;
};
export declare const MsgTripCircuitBreaker: {
    encode(message: MsgTripCircuitBreaker, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgTripCircuitBreaker;
    fromJSON(object: any): MsgTripCircuitBreaker;
    toJSON(message: MsgTripCircuitBreaker): unknown;
    create(base?: DeepPartial<MsgTripCircuitBreaker>): MsgTripCircuitBreaker;
    fromPartial(object: DeepPartial<MsgTripCircuitBreaker>): MsgTripCircuitBreaker;
};
export declare const MsgTripCircuitBreakerResponse: {
    encode(message: MsgTripCircuitBreakerResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgTripCircuitBreakerResponse;
    fromJSON(object: any): MsgTripCircuitBreakerResponse;
    toJSON(message: MsgTripCircuitBreakerResponse): unknown;
    create(base?: DeepPartial<MsgTripCircuitBreakerResponse>): MsgTripCircuitBreakerResponse;
    fromPartial(object: DeepPartial<MsgTripCircuitBreakerResponse>): MsgTripCircuitBreakerResponse;
};
export declare const MsgResetCircuitBreaker: {
    encode(message: MsgResetCircuitBreaker, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgResetCircuitBreaker;
    fromJSON(object: any): MsgResetCircuitBreaker;
    toJSON(message: MsgResetCircuitBreaker): unknown;
    create(base?: DeepPartial<MsgResetCircuitBreaker>): MsgResetCircuitBreaker;
    fromPartial(object: DeepPartial<MsgResetCircuitBreaker>): MsgResetCircuitBreaker;
};
export declare const MsgResetCircuitBreakerResponse: {
    encode(message: MsgResetCircuitBreakerResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgResetCircuitBreakerResponse;
    fromJSON(object: any): MsgResetCircuitBreakerResponse;
    toJSON(message: MsgResetCircuitBreakerResponse): unknown;
    create(base?: DeepPartial<MsgResetCircuitBreakerResponse>): MsgResetCircuitBreakerResponse;
    fromPartial(object: DeepPartial<MsgResetCircuitBreakerResponse>): MsgResetCircuitBreakerResponse;
};
/** Msg defines the circuit Msg service. */
export interface Msg {
    /**
     * AuthorizeCircuitBreaker allows a super-admin to grant (or revoke) another
     * account's circuit breaker permissions.
     */
    AuthorizeCircuitBreaker(request: DeepPartial<MsgAuthorizeCircuitBreaker>, metadata?: grpc.Metadata): Promise<MsgAuthorizeCircuitBreakerResponse>;
    /** TripCircuitBreaker pauses processing of Msg's in the state machine. */
    TripCircuitBreaker(request: DeepPartial<MsgTripCircuitBreaker>, metadata?: grpc.Metadata): Promise<MsgTripCircuitBreakerResponse>;
    /**
     * ResetCircuitBreaker resumes processing of Msg's in the state machine that
     * have been been paused using TripCircuitBreaker.
     */
    ResetCircuitBreaker(request: DeepPartial<MsgResetCircuitBreaker>, metadata?: grpc.Metadata): Promise<MsgResetCircuitBreakerResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    AuthorizeCircuitBreaker(request: DeepPartial<MsgAuthorizeCircuitBreaker>, metadata?: grpc.Metadata): Promise<MsgAuthorizeCircuitBreakerResponse>;
    TripCircuitBreaker(request: DeepPartial<MsgTripCircuitBreaker>, metadata?: grpc.Metadata): Promise<MsgTripCircuitBreakerResponse>;
    ResetCircuitBreaker(request: DeepPartial<MsgResetCircuitBreaker>, metadata?: grpc.Metadata): Promise<MsgResetCircuitBreakerResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgAuthorizeCircuitBreakerDesc: UnaryMethodDefinitionish;
export declare const MsgTripCircuitBreakerDesc: UnaryMethodDefinitionish;
export declare const MsgResetCircuitBreakerDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
