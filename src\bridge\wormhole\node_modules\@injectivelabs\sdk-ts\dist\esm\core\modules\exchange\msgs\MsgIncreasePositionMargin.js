import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { amountToCosmosSdkDecAmount, numberToCosmosSdkDecString, } from '../../../../utils/numbers.js';
import { MsgBase } from '../../MsgBase.js';
import snakecaseKeys from 'snakecase-keys';
const createMessage = (params) => {
    const message = InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin.create();
    message.sender = params.injectiveAddress;
    message.sourceSubaccountId = params.srcSubaccountId;
    message.destinationSubaccountId = params.dstSubaccountId;
    message.marketId = params.marketId;
    message.amount = params.amount;
    return InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin.fromPartial(message);
};
/**
 * @category Messages
 */
export default class MsgIncreasePositionMargin extends MsgBase {
    static fromJSON(params) {
        return new MsgIncreasePositionMargin(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            amount: amountToCosmosSdkDecAmount(initialParams.amount).toFixed(),
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgIncreasePositionMargin',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const msg = createMessage(params);
        const message = {
            ...snakecaseKeys(msg),
        };
        return {
            type: 'exchange/MsgIncreasePositionMargin',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgIncreasePositionMargin',
            ...value,
        };
    }
    toEip712() {
        const amino = this.toAmino();
        const { type, value } = amino;
        const messageAdjusted = {
            ...value,
            amount: amountToCosmosSdkDecAmount(value.amount).toFixed(),
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const messageAdjusted = {
            ...web3gw,
            amount: numberToCosmosSdkDecString(params.amount),
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgIncreasePositionMargin',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin.encode(this.toProto()).finish();
    }
}
