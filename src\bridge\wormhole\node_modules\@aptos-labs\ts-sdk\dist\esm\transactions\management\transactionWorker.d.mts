import { AccountSequenceNumber } from './accountSequenceNumber.mjs';
import EventEmitter from 'eventemitter3';
import { AptosConfig } from '../../api/aptosConfig.mjs';
import { PendingTransactionResponse } from '../../types/types.mjs';
import { SimpleTransaction } from '../instances/simpleTransaction.mjs';
import { InputGenerateTransactionPayloadData, InputGenerateTransactionOptions } from '../types.mjs';
import { A as Account } from '../../Ed25519Account-D9XrCLfE.mjs';
import { AsyncQueue } from './asyncQueue.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../utils/const.mjs';
import '../authenticator/account.mjs';
import '../../bcs/deserializer.mjs';
import '../../bcs/serializer.mjs';
import '../../core/hex.mjs';
import '../../core/common.mjs';
import '../../core/crypto/ed25519.mjs';
import '../../publicKey-CJOcUwJK.mjs';
import '../../core/accountAddress.mjs';
import '../instances/transactionArgument.mjs';
import '../../core/crypto/signature.mjs';
import '../../core/crypto/privateKey.mjs';
import '../../core/crypto/multiEd25519.mjs';
import '../../core/crypto/multiKey.mjs';
import '../../core/crypto/singleKey.mjs';
import '../../core/crypto/secp256k1.mjs';
import '../../bcs/serializable/moveStructs.mjs';
import '../../bcs/serializable/movePrimitives.mjs';
import '../../bcs/serializable/fixedBytes.mjs';
import '../instances/rawTransaction.mjs';
import '../instances/chainId.mjs';
import '../instances/transactionPayload.mjs';
import '../instances/identifier.mjs';
import '../instances/moduleId.mjs';
import '../typeTag/index.mjs';
import '../instances/multiAgentTransaction.mjs';

/**
 * @group Implementation
 * @category Transactions
 */
declare const promiseFulfilledStatus = "fulfilled";
/**
 * Events emitted by the transaction worker during its operation, allowing the dapp to respond to various transaction states.
 * @group Implementation
 * @category Transactions
 */
declare enum TransactionWorkerEventsEnum {
    TransactionSent = "transactionSent",
    TransactionSendFailed = "transactionSendFailed",
    TransactionExecuted = "transactionExecuted",
    TransactionExecutionFailed = "transactionExecutionFailed",
    ExecutionFinish = "executionFinish"
}
/**
 * Defines the events emitted by the transaction worker during various stages of transaction processing. *
 * @group Implementation
 * @category Transactions
 */
interface TransactionWorkerEvents {
    transactionSent: (data: SuccessEventData) => void;
    transactionSendFailed: (data: FailureEventData) => void;
    transactionExecuted: (data: SuccessEventData) => void;
    transactionExecutionFailed: (data: FailureEventData) => void;
    executionFinish: (data: ExecutionFinishEventData) => void;
}
/**
 * The payload for when the worker has finished its job.
 * @group Implementation
 * @category Transactions
 */
type ExecutionFinishEventData = {
    message: string;
};
/**
 * The payload for a success event.
 * @group Implementation
 * @category Transactions
 */
type SuccessEventData = {
    message: string;
    transactionHash: string;
};
/**
 * The payload for a failure event.
 * @group Implementation
 * @category Transactions
 */
type FailureEventData = {
    message: string;
    error: string;
};
/**
 * TransactionWorker provides a simple framework for receiving payloads to be processed.
 *
 * Once one `start()` the process and pushes a new transaction, the worker acquires
 * the current account's next sequence number (by using the AccountSequenceNumber class),
 * generates a signed transaction and pushes an async submission process into the `outstandingTransactions` queue.
 * At the same time, the worker processes transactions by reading the `outstandingTransactions` queue
 * and submits the next transaction to chain, it
 * 1) waits for resolution of the submission process or get pre-execution validation error
 * and 2) waits for the resolution of the execution process or get an execution error.
 * The worker fires events for any submission and/or execution success and/or failure.
 * @group Implementation
 * @category Transactions
 */
declare class TransactionWorker extends EventEmitter<TransactionWorkerEvents> {
    readonly aptosConfig: AptosConfig;
    readonly account: Account;
    readonly accountSequnceNumber: AccountSequenceNumber;
    readonly taskQueue: AsyncQueue<() => Promise<void>>;
    started: boolean;
    /**
     * transactions payloads waiting to be generated and signed
     *
     * TODO support entry function payload from ABI builder
     * @group Implementation
     * @category Transactions
     */
    transactionsQueue: AsyncQueue<[InputGenerateTransactionPayloadData, InputGenerateTransactionOptions | undefined]>;
    /**
     * signed transactions waiting to be submitted
     * @group Implementation
     * @category Transactions
     */
    outstandingTransactions: AsyncQueue<[Promise<PendingTransactionResponse>, bigint]>;
    /**
     * transactions that have been submitted to chain
     * @group Implementation
     * @category Transactions
     */
    sentTransactions: Array<[string, bigint, any]>;
    /**
     * transactions that have been committed to chain
     * @group Implementation
     * @category Transactions
     */
    executedTransactions: Array<[string, bigint, any]>;
    /**
     * Initializes a new instance of the class, providing a framework for receiving payloads to be processed.
     *
     * @param aptosConfig - A configuration object for Aptos.
     * @param account - The account that will be used for sending transactions.
     * @param maxWaitTime - The maximum wait time to wait before re-syncing the sequence number to the current on-chain state,
     * default is 30 seconds.
     * @param maximumInFlight - The maximum number of transactions that can be submitted per account, default is 100.
     * @param sleepTime - The time to wait in seconds before re-evaluating if the maximum number of transactions are in flight,
     * default is 10 seconds.
     * @group Implementation
     * @category Transactions
     */
    constructor(aptosConfig: AptosConfig, account: Account, maxWaitTime?: number, maximumInFlight?: number, sleepTime?: number);
    /**
     * Submits the next transaction for the account by generating it with the current sequence number
     * and adding it to the outstanding transaction queue for processing.
     * This function continues to submit transactions until there are no more to process.
     *
     * @throws {Error} Throws an error if the transaction submission fails.
     * @group Implementation
     * @category Transactions
     */
    submitNextTransaction(): Promise<void>;
    /**
     * Reads the outstanding transaction queue and submits the transactions to the chain.
     * This function processes each transaction, checking their status and emitting events based on whether they were successfully
     * sent or failed.
     *
     * @throws {Error} Throws an error if the process execution fails.
     * @event TransactionWorkerEventsEnum.TransactionSent - Emitted when a transaction has been successfully committed to the chain.
     * @event TransactionWorkerEventsEnum.TransactionSendFailed - Emitted when a transaction fails to commit, along with the error
     * reason.
     * @event TransactionWorkerEventsEnum.ExecutionFinish - Emitted when the execution of transactions is complete.
     * @group Implementation
     * @category Transactions
     */
    processTransactions(): Promise<void>;
    /**
     * Once a transaction has been sent to the chain, this function checks for its execution status.
     * @param sentTransaction - The transaction that was sent to the chain and is now waiting to be executed.
     * @param sequenceNumber - The account's sequence number that was sent with the transaction.
     * @group Implementation
     * @category Transactions
     */
    checkTransaction(sentTransaction: PromiseFulfilledResult<PendingTransactionResponse>, sequenceNumber: bigint): Promise<void>;
    /**
     * Pushes a transaction to the transactions queue for processing.
     *
     * @param transactionData - The transaction payload containing necessary details.
     * @param transactionData.abi - For all entry function payloads, the ABI to skip remote ABI lookups.
     * @param options - Optional parameters for transaction configuration.
     * @param options.maxGasAmount - Maximum gas amount for the transaction.
     * @param options.gasUnitPrice - Gas unit price for the transaction.
     * @param options.expireTimestamp - Expiration timestamp on the transaction.
     * @param options.accountSequenceNumber - The sequence number for the transaction.
     * @group Implementation
     * @category Transactions
     */
    push(transactionData: InputGenerateTransactionPayloadData, options?: InputGenerateTransactionOptions): Promise<void>;
    /**
     * Generates a signed transaction that can be submitted to the chain.
     *
     * @param account - An Aptos account used as the sender of the transaction.
     * @param sequenceNumber - A sequence number the transaction will be generated with.
     * @returns A signed transaction object or undefined if the transaction queue is empty.
     * @group Implementation
     * @category Transactions
     */
    generateNextTransaction(account: Account, sequenceNumber: bigint): Promise<SimpleTransaction | undefined>;
    /**
     * Starts transaction submission and processing by executing tasks from the queue until it is cancelled.
     *
     * @throws {Error} Throws an error if unable to start transaction batching.
     * @group Implementation
     * @category Transactions
     */
    run(): Promise<void>;
    /**
     * Starts the transaction management process.
     *
     * @throws {Error} Throws an error if the worker has already started.
     * @group Implementation
     * @category Transactions
     */
    start(): void;
    /**
     * Stops the transaction management process.
     *
     * @throws {Error} Throws an error if the worker has already stopped.
     * @group Implementation
     * @category Transactions
     */
    stop(): void;
}

export { type ExecutionFinishEventData, type FailureEventData, type SuccessEventData, TransactionWorker, type TransactionWorkerEvents, TransactionWorkerEventsEnum, promiseFulfilledStatus };
