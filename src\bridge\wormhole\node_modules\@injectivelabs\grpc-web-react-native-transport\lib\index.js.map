{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,oDAA+C;AAE/C,SAAgB,oBAAoB,CAClC,IAA2B;IAE3B,OAAO,UAAC,IAA2B;QACjC,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC,CAAC;AACJ,CAAC;AAND,oDAMC;AAED,IAAI,iBAAiB,GAA6B,IAAI,CAAC;AAEvD,SAAS,YAAY;IACnB,IAAI,iBAAiB,EAAE;QACrB,IAAM,eAAe,GAAG,iBAAiB,CAAC;QAC1C,iBAAiB,GAAG,IAAI,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI;gBACF,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;aACtB;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,iBAAiB,KAAK,IAAI,EAAE;oBAC9B,iBAAiB,GAAG,EAAE,CAAC;oBACvB,UAAU,CAAC;wBACT,YAAY,EAAE,CAAC;oBACjB,CAAC,EAAE,CAAC,CAAC,CAAC;iBACP;gBACD,KAAK,IAAI,CAAC,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBACnD,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC/C;gBACD,MAAM,CAAC,CAAC;aACT;SACF;KACF;AACH,CAAC;AAED,SAAS,KAAK;IAAC,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,yBAAc;;IAC3B,IAAI,OAAO,CAAC,KAAK,EAAE;QACjB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACjC;SAAM;QACL,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAC/B;AACH,CAAC;AAED,SAAS,MAAM,CAAC,EAAc;IAC5B,IAAI,iBAAiB,KAAK,IAAI,EAAE;QAC9B,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3B,OAAO;KACR;IACD,iBAAiB,GAAG,CAAC,EAAE,CAAC,CAAC;IACzB,UAAU,CAAC;QACT,YAAY,EAAE,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAW;IACtC,IAAM,OAAO,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3C,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAM,SAAS,GAAI,MAAM,CAAC,SAAiB,CAAC,WAAW;YACrD,CAAC,CAAE,GAAW,CAAC,WAAW,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAChC,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;KAC1C;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAW,EAAE,KAAa;IACrD,IAAI,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,EAAE;QACpC,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACvC,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,EAAE;YACpC,IAAI,GAAG,OAAO,GAAG,CAAC,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;SAC5D;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;IAOE,aACE,gBAAuC,EACvC,IAA2B;QAE3B,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAES,6BAAe,GAAzB;QAAA,iBASC;QARC,IAAI,CAAC,OAAO,CAAC,KAAK;YAChB,KAAK,CAAC,8BAA8B,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAClE,IAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;QACtC,IAAM,aAAa,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,CAAC;YACL,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,yBAAW,GAAX;QAAA,iBAIC;QAHC,MAAM,CAAC;YACL,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,2BAAa,GAAb;QAAA,iBASC;QARC,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,gBAAgB,EAAE;YAC3D,MAAM,CAAC;gBACL,KAAI,CAAC,OAAO,CAAC,SAAS,CACpB,IAAI,eAAI,CAAC,QAAQ,CAAC,KAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,EACnD,KAAI,CAAC,GAAG,CAAC,MAAM,CAChB,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAED,yBAAW,GAAX,UAAY,QAAoB;QAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAED,wBAAU,GAAV,cAAc,CAAC;IAEf,mBAAK,GAAL,UAAM,QAAuB;QAA7B,iBAuBC;QAtBC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEnC,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,MAAM;YAChC,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEzD,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,GAAG,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,GAAe;YAC5C,MAAM,CAAC;gBACL,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAES,0BAAY,GAAtB;QACE,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,MAAM,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,oCAAoC,CAAC,CAAC;IAClE,CAAC;IAED,oBAAM,GAAN;QACE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;IACH,UAAC;AAAD,CAAC,AAlFD,IAkFC;AAED;IAA6B,kCAAG;IAAhC;;IAsBA,CAAC;IArBC,qCAAY,GAAZ;QACE,IAAI,CAAC,OAAO,CAAC,KAAK;YAChB,KAAK,CACH,oEAAoE,CACrE,CAAC;QACH,IAAI,CAAC,GAAW,CAAC,YAAY,GAAG,aAAa,CAAC;IACjD,CAAC;IAED,wCAAe,GAAf,cAAmB,CAAC;IAEpB,oCAAW,GAAX;QAAA,iBAUC;QATC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,KAAK;YAChB,KAAK,CAAC,8BAA8B,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9D,MAAM,CAAC;YACL,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAc,IAAI,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QACH,MAAM,CAAC;YACL,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IACH,qBAAC;AAAD,CAAC,AAtBD,CAA6B,GAAG,GAsB/B"}