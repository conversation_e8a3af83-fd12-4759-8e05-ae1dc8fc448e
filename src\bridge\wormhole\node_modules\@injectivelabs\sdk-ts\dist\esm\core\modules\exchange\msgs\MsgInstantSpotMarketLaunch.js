import snakecaseKeys from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import { amountToCosmosSdkDecAmount, numberToCosmosSdkDecString, } from '../../../../utils/numbers.js';
const createMessage = (params) => {
    const message = InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch.create();
    message.sender = params.proposer;
    message.ticker = params.market.ticker;
    message.baseDenom = params.market.baseDenom;
    message.quoteDenom = params.market.quoteDenom;
    message.minPriceTickSize = params.market.minPriceTickSize;
    message.minQuantityTickSize = params.market.minQuantityTickSize;
    message.minNotional = params.market.minNotional;
    message.baseDecimals = Number(params.market.baseDecimals);
    message.quoteDecimals = Number(params.market.quoteDecimals);
    return InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch.fromPartial(message);
};
/**
 * @category Messages
 */
export default class MsgInstantSpotMarketLaunch extends MsgBase {
    static fromJSON(params) {
        return new MsgInstantSpotMarketLaunch(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            market: {
                ...initialParams.market,
                minPriceTickSize: amountToCosmosSdkDecAmount(initialParams.market.minPriceTickSize).toFixed(),
                minQuantityTickSize: amountToCosmosSdkDecAmount(initialParams.market.minQuantityTickSize).toFixed(),
                minNotional: amountToCosmosSdkDecAmount(initialParams.market.minNotional).toFixed(),
            },
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgInstantSpotMarketLaunch',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const message = {
            ...snakecaseKeys(createMessage(params)),
        };
        return {
            type: 'exchange/MsgInstantSpotMarketLaunch',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgInstantSpotMarketLaunch',
            ...value,
        };
    }
    toEip712() {
        const amino = this.toAmino();
        const { type, value } = amino;
        const messageAdjusted = {
            ...value,
            min_price_tick_size: amountToCosmosSdkDecAmount(value.min_price_tick_size).toFixed(),
            min_quantity_tick_size: amountToCosmosSdkDecAmount(value.min_quantity_tick_size).toFixed(),
            min_notional: amountToCosmosSdkDecAmount(value.min_notional).toFixed(),
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const messageAdjusted = {
            ...web3gw,
            min_price_tick_size: numberToCosmosSdkDecString(params.market.minPriceTickSize),
            min_quantity_tick_size: numberToCosmosSdkDecString(params.market.minQuantityTickSize),
            min_notional: numberToCosmosSdkDecString(params.market.minNotional),
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgInstantSpotMarketLaunch',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch.encode(this.toProto()).finish();
    }
}
