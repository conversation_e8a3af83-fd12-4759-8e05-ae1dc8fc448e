{"version": 3, "file": "from-beacon-payload.js", "sourceRoot": "", "sources": ["../../src/from-beacon-payload.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAwF9C,SAAS,kCAAkC,CAAC,EAC1C,UAAU,EACV,YAAY,GACoB;IAChC,OAAO;QACL,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;YACrD,IAAI;YACJ,WAAW,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;gBACvE,YAAY,EAAE,aAAa;gBAC3B,QAAQ,EAAE,SAAS;gBACnB,MAAM;aACP,CAAC,CAAC;SACJ,CAAC,CAAC;QACH,WAAW,EAAE;YACX,iBAAiB,EAAE,YAAY,CAAC,mBAAmB;YACnD,CAAC,EAAE,YAAY,CAAC,CAAC;YACjB,qBAAqB,EAAE,YAAY,CAAC,uBAAuB;YAC3D,QAAQ,EAAE;gBACR,EAAE,EAAE,YAAY,CAAC,SAAS,CAAC,EAAE;gBAC7B,EAAE,EAAE,YAAY,CAAC,SAAS,CAAC,EAAE;gBAC7B,eAAe,EAAE,YAAY,CAAC,SAAS,CAAC,gBAAgB;aACzD;YACD,UAAU,EAAE,YAAY,CAAC,WAAW;SACrC;KACF,CAAA;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,iCAAiC,CAAC,OAA0B;IAC1E,MAAM,gBAAgB,GAAqB;QACzC,UAAU,EAAE,OAAO,CAAC,WAAW;QAC/B,YAAY,EAAE,OAAO,CAAC,aAAa;QACnC,SAAS,EAAE,OAAO,CAAC,UAAU;QAC7B,YAAY,EAAE,OAAO,CAAC,aAAa;QACnC,SAAS,EAAE,OAAO,CAAC,UAAU;QAC7B,UAAU,EAAE,OAAO,CAAC,WAAW;QAC/B,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACtD,QAAQ,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAChD,OAAO,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC9C,SAAS,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACjD,SAAS,EAAE,OAAO,CAAC,UAAU;QAC7B,aAAa,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC5D,SAAS,EAAE,OAAO,CAAC,UAAU;QAC7B,YAAY,EAAE,OAAO,CAAC,YAAY;KACnC,CAAA;IAED,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;QACrE,gBAAgB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9D,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YACpC,cAAc,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;YACvD,OAAO,EAAE,EAAE,CAAC,OAAO;YACnB,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;SACvC,CAAC,CAAC,CAAA;KACJ;IAED,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,KAAK,IAAI,EAAE;QACzE,gBAAgB,CAAC,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAA;KAC1E;IACD,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,IAAI,OAAO,CAAC,eAAe,KAAK,IAAI,EAAE;QAC7E,gBAAgB,CAAC,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAA;KAC9E;IACD,IAAI,OAAO,CAAC,wBAAwB,KAAK,SAAS,IAAI,OAAO,CAAC,wBAAwB,KAAK,IAAI,EAAE;QAC/F,gBAAgB,CAAC,qBAAqB,GAAG,OAAO,CAAC,wBAAwB,CAAA;KAC1E;IAED,WAAW;IACX,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,IAAI,OAAO,CAAC,gBAAgB,KAAK,IAAI,EAAE;QAC/E,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACzE,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;YAClD,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC,CAAA;KACJ;IACD,IAAI,OAAO,CAAC,mBAAmB,KAAK,SAAS,IAAI,OAAO,CAAC,mBAAmB,KAAK,IAAI,EAAE;QACrF,gBAAgB,CAAC,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC/E,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC,CAAA;KACJ;IACD,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,IAAI,OAAO,CAAC,sBAAsB,KAAK,IAAI,EAAE;QAC3F,gBAAgB,CAAC,qBAAqB,GAAG,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACrF,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,aAAa;SACjC,CAAC,CAAC,CAAA;KACJ;IAED,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,IAAI,OAAO,CAAC,iBAAiB,KAAK,IAAI,EAAE;QACjF,qFAAqF;QACrF,gBAAgB,CAAC,gBAAgB;YAC/B,OAAO,CAAC,iBAAiB,CAAC,WAAW,KAAK,SAAS;gBACjD,CAAC,CAAC,OAAO,CAAC,iBAAiB;gBAC3B,CAAC,CAAC,kCAAkC,CAChC,OAAO,CAAC,iBAA+D,CACxE,CAAA;KACR;IAED,OAAO,gBAAgB,CAAA;AACzB,CAAC"}