# web3-core-helpers

[![NPM Package][npm-image]][npm-url]

This is a sub-package of [web3.js][repo] with useful helper functions.

Please read the [documentation][docs] for more.

## Installation

You can install the package either using [NPM](https://www.npmjs.com/package/web3-core-helpers) or using [Yarn](https://yarnpkg.com/package/web3-core-helpers)

### Using NPM

```bash
npm install web3-core-helpers
```

### Using Yarn

```bash
yarn add web3-core-helpers
```

## Usage

```js
const helpers = require('web3-core-helpers');

helpers.formatters;
helpers.errors;
...
```

## Types

All the TypeScript typings are placed in the `types` folder.

[docs]: http://web3js.readthedocs.io/en/1.0/
[repo]: https://github.com/ethereum/web3.js
[npm-image]: https://img.shields.io/npm/v/web3-core-helpers.svg
[npm-url]: https://npmjs.org/package/web3-core-helpers
