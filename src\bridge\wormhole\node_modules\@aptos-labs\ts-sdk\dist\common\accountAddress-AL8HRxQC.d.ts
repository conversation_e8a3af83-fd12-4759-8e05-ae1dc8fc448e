type GetAccountCoinsDataQuery = {
    current_fungible_asset_balances: Array<{
        amount?: any | null;
        asset_type?: string | null;
        is_frozen: boolean;
        is_primary?: boolean | null;
        last_transaction_timestamp?: any | null;
        last_transaction_version?: any | null;
        owner_address: string;
        storage_id: string;
        token_standard?: string | null;
        metadata?: {
            token_standard: string;
            symbol: string;
            supply_aggregator_table_key_v1?: string | null;
            supply_aggregator_table_handle_v1?: string | null;
            project_uri?: string | null;
            name: string;
            last_transaction_version: any;
            last_transaction_timestamp: any;
            icon_uri?: string | null;
            decimals: number;
            creator_address: string;
            asset_type: string;
        } | null;
    }>;
};
type GetAccountCollectionsWithOwnedTokensQuery = {
    current_collection_ownership_v2_view: Array<{
        collection_id?: string | null;
        collection_name?: string | null;
        collection_uri?: string | null;
        creator_address?: string | null;
        distinct_tokens?: any | null;
        last_transaction_version?: any | null;
        owner_address?: string | null;
        single_token_uri?: string | null;
        current_collection?: {
            collection_id: string;
            collection_name: string;
            creator_address: string;
            current_supply: any;
            description: string;
            last_transaction_timestamp: any;
            last_transaction_version: any;
            mutable_description?: boolean | null;
            max_supply?: any | null;
            mutable_uri?: boolean | null;
            table_handle_v1?: string | null;
            token_standard: string;
            total_minted_v2?: any | null;
            uri: string;
        } | null;
    }>;
};
type GetAccountOwnedTokensQuery = {
    current_token_ownerships_v2: Array<{
        token_standard: string;
        token_properties_mutated_v1?: any | null;
        token_data_id: string;
        table_type_v1?: string | null;
        storage_id: string;
        property_version_v1: any;
        owner_address: string;
        last_transaction_version: any;
        last_transaction_timestamp: any;
        is_soulbound_v2?: boolean | null;
        is_fungible_v2?: boolean | null;
        amount: any;
        current_token_data?: {
            collection_id: string;
            description: string;
            is_fungible_v2?: boolean | null;
            largest_property_version_v1?: any | null;
            last_transaction_timestamp: any;
            last_transaction_version: any;
            maximum?: any | null;
            supply?: any | null;
            token_data_id: string;
            token_name: string;
            token_properties: any;
            token_standard: string;
            token_uri: string;
            decimals?: any | null;
            current_collection?: {
                collection_id: string;
                collection_name: string;
                creator_address: string;
                current_supply: any;
                description: string;
                last_transaction_timestamp: any;
                last_transaction_version: any;
                max_supply?: any | null;
                mutable_description?: boolean | null;
                mutable_uri?: boolean | null;
                table_handle_v1?: string | null;
                token_standard: string;
                total_minted_v2?: any | null;
                uri: string;
            } | null;
        } | null;
    }>;
};
type GetAccountOwnedTokensFromCollectionQuery = {
    current_token_ownerships_v2: Array<{
        token_standard: string;
        token_properties_mutated_v1?: any | null;
        token_data_id: string;
        table_type_v1?: string | null;
        storage_id: string;
        property_version_v1: any;
        owner_address: string;
        last_transaction_version: any;
        last_transaction_timestamp: any;
        is_soulbound_v2?: boolean | null;
        is_fungible_v2?: boolean | null;
        amount: any;
        current_token_data?: {
            collection_id: string;
            description: string;
            is_fungible_v2?: boolean | null;
            largest_property_version_v1?: any | null;
            last_transaction_timestamp: any;
            last_transaction_version: any;
            maximum?: any | null;
            supply?: any | null;
            token_data_id: string;
            token_name: string;
            token_properties: any;
            token_standard: string;
            token_uri: string;
            decimals?: any | null;
            current_collection?: {
                collection_id: string;
                collection_name: string;
                creator_address: string;
                current_supply: any;
                description: string;
                last_transaction_timestamp: any;
                last_transaction_version: any;
                max_supply?: any | null;
                mutable_description?: boolean | null;
                mutable_uri?: boolean | null;
                table_handle_v1?: string | null;
                token_standard: string;
                total_minted_v2?: any | null;
                uri: string;
            } | null;
        } | null;
    }>;
};
type GetChainTopUserTransactionsQuery = {
    user_transactions: Array<{
        version: any;
    }>;
};
type GetCollectionDataQuery = {
    current_collections_v2: Array<{
        uri: string;
        total_minted_v2?: any | null;
        token_standard: string;
        table_handle_v1?: string | null;
        mutable_uri?: boolean | null;
        mutable_description?: boolean | null;
        max_supply?: any | null;
        collection_id: string;
        collection_name: string;
        creator_address: string;
        current_supply: any;
        description: string;
        last_transaction_timestamp: any;
        last_transaction_version: any;
        cdn_asset_uris?: {
            cdn_image_uri?: string | null;
            asset_uri: string;
            animation_optimizer_retry_count: number;
            cdn_animation_uri?: string | null;
            cdn_json_uri?: string | null;
            image_optimizer_retry_count: number;
            json_parser_retry_count: number;
            raw_animation_uri?: string | null;
            raw_image_uri?: string | null;
        } | null;
    }>;
};
type GetCurrentFungibleAssetBalancesQuery = {
    current_fungible_asset_balances: Array<{
        amount?: any | null;
        asset_type?: string | null;
        is_frozen: boolean;
        is_primary?: boolean | null;
        last_transaction_timestamp?: any | null;
        last_transaction_version?: any | null;
        owner_address: string;
        storage_id: string;
        token_standard?: string | null;
    }>;
};
type GetDelegatedStakingActivitiesQuery = {
    delegated_staking_activities: Array<{
        amount: any;
        delegator_address: string;
        event_index: any;
        event_type: string;
        pool_address: string;
        transaction_version: any;
    }>;
};
type GetEventsQuery = {
    events: Array<{
        account_address: string;
        creation_number: any;
        data: any;
        event_index: any;
        sequence_number: any;
        transaction_block_height: any;
        transaction_version: any;
        type: string;
        indexed_type: string;
    }>;
};
type GetFungibleAssetActivitiesQuery = {
    fungible_asset_activities: Array<{
        amount?: any | null;
        asset_type?: string | null;
        block_height: any;
        entry_function_id_str?: string | null;
        event_index: any;
        gas_fee_payer_address?: string | null;
        is_frozen?: boolean | null;
        is_gas_fee: boolean;
        is_transaction_success: boolean;
        owner_address?: string | null;
        storage_id: string;
        storage_refund_amount: any;
        token_standard: string;
        transaction_timestamp: any;
        transaction_version: any;
        type: string;
    }>;
};
type GetFungibleAssetMetadataQuery = {
    fungible_asset_metadata: Array<{
        icon_uri?: string | null;
        project_uri?: string | null;
        supply_aggregator_table_handle_v1?: string | null;
        supply_aggregator_table_key_v1?: string | null;
        creator_address: string;
        asset_type: string;
        decimals: number;
        last_transaction_timestamp: any;
        last_transaction_version: any;
        name: string;
        symbol: string;
        token_standard: string;
        supply_v2?: any | null;
        maximum_v2?: any | null;
    }>;
};
type GetNamesQuery = {
    current_aptos_names: Array<{
        domain?: string | null;
        expiration_timestamp?: any | null;
        registered_address?: string | null;
        subdomain?: string | null;
        token_standard?: string | null;
        is_primary?: boolean | null;
        owner_address?: string | null;
        subdomain_expiration_policy?: any | null;
        domain_expiration_timestamp?: any | null;
    }>;
};
type GetNumberOfDelegatorsQuery = {
    num_active_delegator_per_pool: Array<{
        num_active_delegator?: any | null;
        pool_address?: string | null;
    }>;
};
type GetObjectDataQuery = {
    current_objects: Array<{
        allow_ungated_transfer: boolean;
        state_key_hash: string;
        owner_address: string;
        object_address: string;
        last_transaction_version: any;
        last_guid_creation_num: any;
        is_deleted: boolean;
    }>;
};
type GetProcessorStatusQuery = {
    processor_status: Array<{
        last_success_version: any;
        processor: string;
        last_updated: any;
    }>;
};
type GetTableItemsDataQuery = {
    table_items: Array<{
        decoded_key: any;
        decoded_value?: any | null;
        key: string;
        table_handle: string;
        transaction_version: any;
        write_set_change_index: any;
    }>;
};
type GetTableItemsMetadataQuery = {
    table_metadatas: Array<{
        handle: string;
        key_type: string;
        value_type: string;
    }>;
};
type GetTokenActivityQuery = {
    token_activities_v2: Array<{
        after_value?: string | null;
        before_value?: string | null;
        entry_function_id_str?: string | null;
        event_account_address: string;
        event_index: any;
        from_address?: string | null;
        is_fungible_v2?: boolean | null;
        property_version_v1: any;
        to_address?: string | null;
        token_amount: any;
        token_data_id: string;
        token_standard: string;
        transaction_timestamp: any;
        transaction_version: any;
        type: string;
    }>;
};
type GetCurrentTokenOwnershipQuery = {
    current_token_ownerships_v2: Array<{
        token_standard: string;
        token_properties_mutated_v1?: any | null;
        token_data_id: string;
        table_type_v1?: string | null;
        storage_id: string;
        property_version_v1: any;
        owner_address: string;
        last_transaction_version: any;
        last_transaction_timestamp: any;
        is_soulbound_v2?: boolean | null;
        is_fungible_v2?: boolean | null;
        amount: any;
        current_token_data?: {
            collection_id: string;
            description: string;
            is_fungible_v2?: boolean | null;
            largest_property_version_v1?: any | null;
            last_transaction_timestamp: any;
            last_transaction_version: any;
            maximum?: any | null;
            supply?: any | null;
            token_data_id: string;
            token_name: string;
            token_properties: any;
            token_standard: string;
            token_uri: string;
            decimals?: any | null;
            current_collection?: {
                collection_id: string;
                collection_name: string;
                creator_address: string;
                current_supply: any;
                description: string;
                last_transaction_timestamp: any;
                last_transaction_version: any;
                max_supply?: any | null;
                mutable_description?: boolean | null;
                mutable_uri?: boolean | null;
                table_handle_v1?: string | null;
                token_standard: string;
                total_minted_v2?: any | null;
                uri: string;
            } | null;
        } | null;
    }>;
};
type GetTokenDataQuery = {
    current_token_datas_v2: Array<{
        collection_id: string;
        description: string;
        is_fungible_v2?: boolean | null;
        largest_property_version_v1?: any | null;
        last_transaction_timestamp: any;
        last_transaction_version: any;
        maximum?: any | null;
        supply?: any | null;
        token_data_id: string;
        token_name: string;
        token_properties: any;
        token_standard: string;
        token_uri: string;
        decimals?: any | null;
        current_collection?: {
            collection_id: string;
            collection_name: string;
            creator_address: string;
            current_supply: any;
            description: string;
            last_transaction_timestamp: any;
            last_transaction_version: any;
            max_supply?: any | null;
            mutable_description?: boolean | null;
            mutable_uri?: boolean | null;
            table_handle_v1?: string | null;
            token_standard: string;
            total_minted_v2?: any | null;
            uri: string;
        } | null;
    }>;
};

/**
 * GENERATED QUERY TYPES FROM GRAPHQL SCHEMA
 *
 * generated types we generate from graphql schema that match the structure of the
 * response type when querying from Hasura schema.
 *
 * These types are used as the return type when making the actual request (usually
 * under the /internal/ folder)
 * @group Implementation
 * @category Types
 */

/**
 * CUSTOM RESPONSE TYPES FOR THE END USER
 *
 * To provide a good dev exp, we build custom types derived from the
 * query types to be the response type the end developer/user will
 * work with.
 *
 * These types are used as the return type when calling a sdk api function
 * that calls the function that queries the server (usually under the /api/ folder)
 * @group Implementation
 * @category Types
 */
type GetObjectDataQueryResponse = GetObjectDataQuery["current_objects"];
/**
 * The response structure for querying tokens owned by an account.
 * @group Implementation
 * @category Types
 */
type GetAccountOwnedTokensQueryResponse = GetAccountOwnedTokensQuery["current_token_ownerships_v2"];
/**
 * The response containing the current token ownerships for an account from a specific collection.
 * @group Implementation
 * @category Types
 */
type GetAccountOwnedTokensFromCollectionResponse = GetAccountOwnedTokensFromCollectionQuery["current_token_ownerships_v2"];
/**
 * The response structure for retrieving account collections associated with owned tokens.
 * @group Implementation
 * @category Types
 */
type GetAccountCollectionsWithOwnedTokenResponse = GetAccountCollectionsWithOwnedTokensQuery["current_collection_ownership_v2_view"];
/**
 * The current balances of fungible assets for an account.
 * @group Implementation
 * @category Types
 */
type GetAccountCoinsDataResponse = GetAccountCoinsDataQuery["current_fungible_asset_balances"];
/**
 * The response structure for retrieving user transactions from the top of the blockchain.
 * @group Implementation
 * @category Types
 */
type GetChainTopUserTransactionsResponse = GetChainTopUserTransactionsQuery["user_transactions"];
/**
 * The response containing the events from the GetEventsQuery.
 * @group Implementation
 * @category Types
 */
type GetEventsResponse = GetEventsQuery["events"];
/**
 * The number of active delegators per pool in response to a query.
 * @group Implementation
 * @category Types
 */
type GetNumberOfDelegatorsResponse = GetNumberOfDelegatorsQuery["num_active_delegator_per_pool"];
/**
 * The response containing the delegated staking activities from the query.
 * @group Implementation
 * @category Types
 */
type GetDelegatedStakingActivitiesResponse = GetDelegatedStakingActivitiesQuery["delegated_staking_activities"];
/**
 * The response structure for retrieving data from the current collections.
 * @group Implementation
 * @category Types
 */
type GetCollectionDataResponse = GetCollectionDataQuery["current_collections_v2"][0];
/**
 * The response structure for retrieving token data, containing the current token information.
 * @group Implementation
 * @category Types
 */
type GetTokenDataResponse = GetTokenDataQuery["current_token_datas_v2"][0];
/**
 * The status of the processor as returned by the GetProcessorStatusQuery.
 * @group Implementation
 * @category Types
 */
type GetProcessorStatusResponse = GetProcessorStatusQuery["processor_status"];
/**
 * The response containing metadata for a fungible asset.
 * @group Implementation
 * @category Types
 */
type GetFungibleAssetMetadataResponse = GetFungibleAssetMetadataQuery["fungible_asset_metadata"];
/**
 * The response containing the activities related to fungible assets.
 * @group Implementation
 * @category Types
 */
type GetFungibleAssetActivitiesResponse = GetFungibleAssetActivitiesQuery["fungible_asset_activities"];
/**
 * The current balances of fungible assets for a specific query.
 * @group Implementation
 * @category Types
 */
type GetCurrentFungibleAssetBalancesResponse = GetCurrentFungibleAssetBalancesQuery["current_fungible_asset_balances"];
/**
 * The response structure for retrieving token activity data.
 * @group Implementation
 * @category Types
 */
type GetTokenActivityResponse = GetTokenActivityQuery["token_activities_v2"];
/**
 * The response structure for retrieving the current token ownership details.
 * @group Implementation
 * @category Types
 */
type GetCurrentTokenOwnershipResponse = GetCurrentTokenOwnershipQuery["current_token_ownerships_v2"][0];
/**
 * The response containing the current token ownerships for a user.
 * @group Implementation
 * @category Types
 */
type GetOwnedTokensResponse = GetCurrentTokenOwnershipQuery["current_token_ownerships_v2"];
/**
 * The response structure for retrieving items from a table.
 * @group Implementation
 * @category Types
 */
type GetTableItemsDataResponse = GetTableItemsDataQuery["table_items"];
/**
 * The metadata for table items retrieved from a query.
 * @group Implementation
 * @category Types
 */
type GetTableItemsMetadataResponse = GetTableItemsMetadataQuery["table_metadatas"];
/**
 * The response containing the current Aptos names from the GetNamesQuery.
 * @group Implementation
 * @category Types
 */
type GetANSNameResponse = GetNamesQuery["current_aptos_names"];
/**
 * A generic type that being passed by each function and holds an
 * array of properties we can sort the query by
 * @group Implementation
 * @category Types
 */
type OrderBy<T> = Array<{
    [K in keyof T]?: OrderByValue;
}>;
/**
 * Specifies the order direction for sorting, including options for handling null values.
 * @group Implementation
 * @category Types
 */
type OrderByValue = "asc" | "asc_nulls_first" | "asc_nulls_last" | "desc" | "desc_nulls_first" | "desc_nulls_last";
/**
 * The token standard to query for, which can be either version "v1" or "v2".
 * @group Implementation
 * @category Types
 */
type TokenStandard = "v1" | "v2";
/**
 * The GraphQL query to pass into the `queryIndexer` function.
 * @group Implementation
 * @category Types
 */
type GraphqlQuery = {
    query: string;
    variables?: {};
};

/**
 * @group Implementation
 * @category Network
 */
declare const NetworkToIndexerAPI: Record<string, string>;
/**
 * @group Implementation
 * @category Network
 */
declare const NetworkToNodeAPI: Record<string, string>;
/**
 * @group Implementation
 * @category Network
 */
declare const NetworkToFaucetAPI: Record<string, string>;
/**
 * @group Implementation
 * @category Network
 */
declare const NetworkToPepperAPI: Record<string, string>;
/**
 * @group Implementation
 * @category Network
 */
declare const NetworkToProverAPI: Record<string, string>;
/**
 * Different network environments for connecting to services, ranging from production to development setups.
 * @group Implementation
 * @category Network
 */
declare enum Network {
    MAINNET = "mainnet",
    TESTNET = "testnet",
    DEVNET = "devnet",
    LOCAL = "local",
    CUSTOM = "custom"
}
/**
 * @group Implementation
 * @category Network
 */
declare const NetworkToChainId: Record<string, number>;
/**
 * @group Implementation
 * @category Network
 */
declare const NetworkToNetworkName: Record<string, Network>;

/**
 * Different MIME types used for data interchange in transactions and responses.
 */
declare enum MimeType {
    /**
     * JSON representation, used for transaction submission and accept type JSON output
     */
    JSON = "application/json",
    /**
     * BCS representation, used for accept type BCS output
     */
    BCS = "application/x-bcs",
    /**
     * BCS representation, used for transaction submission in BCS input
     */
    BCS_SIGNED_TRANSACTION = "application/x.aptos.signed_transaction+bcs",
    BCS_VIEW_FUNCTION = "application/x.aptos.view_function+bcs"
}
/**
 * Hexadecimal data input for functions, supporting both string and Uint8Array formats.
 */
type HexInput = string | Uint8Array;
/**
 * Variants of type tags used in the system, encompassing various data types and structures.
 * {@link https://github.com/aptos-labs/aptos-core/blob/main/third_party/move/move-core/types/src/language_storage.rs#L27}
 */
declare enum TypeTagVariants {
    Bool = 0,
    U8 = 1,
    U64 = 2,
    U128 = 3,
    Address = 4,
    Signer = 5,
    Vector = 6,
    Struct = 7,
    U16 = 8,
    U32 = 9,
    U256 = 10,
    Reference = 254,// This is specifically a placeholder and does not represent a real type
    Generic = 255
}
/**
 * Variants of script transaction arguments used in Rust, encompassing various data types for transaction processing.
 * {@link https://github.com/aptos-labs/aptos-core/blob/main/third_party/move/move-core/types/src/transaction_argument.rs#L11}
 */
declare enum ScriptTransactionArgumentVariants {
    U8 = 0,
    U64 = 1,
    U128 = 2,
    Address = 3,
    U8Vector = 4,
    Bool = 5,
    U16 = 6,
    U32 = 7,
    U256 = 8,
    Serialized = 9
}
/**
 * The payload for various transaction types in the system.
 * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/mod.rs#L478}
 */
declare enum TransactionPayloadVariants {
    Script = 0,
    EntryFunction = 2,
    Multisig = 3
}
/**
 * Variants of transactions used in the system.
 * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/mod.rs#L440}
 */
declare enum TransactionVariants {
    MultiAgentTransaction = 0,
    FeePayerTransaction = 1
}
/**
 * Variants of transaction authenticators used in the system.
 * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/authenticator.rs#L44}
 */
declare enum TransactionAuthenticatorVariant {
    Ed25519 = 0,
    MultiEd25519 = 1,
    MultiAgent = 2,
    FeePayer = 3,
    SingleSender = 4
}
/**
 * Variants of account authenticators used in transactions.
 * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/authenticator.rs#L414}
 */
declare enum AccountAuthenticatorVariant {
    Ed25519 = 0,
    MultiEd25519 = 1,
    SingleKey = 2,
    MultiKey = 3,
    NoAccountAuthenticator = 4,
    Abstraction = 5
}
/**
 * Variants of private keys that can comply with the AIP-80 standard.
 * {@link https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md}
 */
declare enum PrivateKeyVariants {
    Ed25519 = "ed25519",
    Secp256k1 = "secp256k1"
}
/**
 * Variants of public keys used in cryptographic operations.
 */
declare enum AnyPublicKeyVariant {
    Ed25519 = 0,
    Secp256k1 = 1,
    Keyless = 3,
    FederatedKeyless = 4
}
/**
 * Variants of signature types used for cryptographic operations.
 */
declare enum AnySignatureVariant {
    Ed25519 = 0,
    Secp256k1 = 1,
    Keyless = 3
}
/**
 * Variants of ephemeral public keys used in cryptographic operations.
 */
declare enum EphemeralPublicKeyVariant {
    Ed25519 = 0
}
/**
 * Variants of ephemeral signatures used for secure communication.
 */
declare enum EphemeralSignatureVariant {
    Ed25519 = 0
}
/**
 * Variants of ephemeral certificates used in secure transactions.
 */
declare enum EphemeralCertificateVariant {
    ZkProof = 0
}
/**
 * Variants of zero-knowledge proofs used in cryptographic operations.
 */
declare enum ZkpVariant {
    Groth16 = 0
}
/**
 * BCS types
 */
type Uint8 = number;
/**
 * A 16-bit unsigned integer.
 */
type Uint16 = number;
/**
 * A 32-bit unsigned integer.
 */
type Uint32 = number;
/**
 * A 64-bit unsigned integer value.
 */
type Uint64 = bigint;
/**
 * A 128-bit unsigned integer used for precise arithmetic operations.
 */
type Uint128 = bigint;
/**
 * A 256-bit unsigned integer used for precise numerical calculations.
 */
type Uint256 = bigint;
/**
 * A number or a bigint value.
 */
type AnyNumber = number | bigint;
/**
 * Configuration options for initializing the SDK, allowing customization of its behavior and interaction with the Aptos network.
 */
type AptosSettings = {
    readonly network?: Network;
    readonly fullnode?: string;
    readonly faucet?: string;
    readonly indexer?: string;
    readonly pepper?: string;
    readonly prover?: string;
    readonly clientConfig?: ClientConfig;
    readonly client?: Client;
    readonly fullnodeConfig?: FullNodeConfig;
    readonly indexerConfig?: IndexerConfig;
    readonly faucetConfig?: FaucetConfig;
};
/**
 * Defines the parameters for paginating query results, including the starting position and maximum number of items to return.
 * @param offset Specifies the starting position of the query result. Default is 0.
 * @param limit Specifies the maximum number of items to return. Default is 25.
 */
interface PaginationArgs {
    offset?: AnyNumber;
    limit?: number;
}
/**
 * Defines the parameters for paginating query results, including the starting position and maximum number of items to return.
 * @param cursor Specifies the starting position of the query result. Default is at the beginning if undefined.  This is not a number and must come from the API.
 * @param limit Specifies the maximum number of items to return. Default is 25.
 */
interface CursorPaginationArgs {
    cursor?: string;
    limit?: number;
}
/**
 * Represents the arguments for specifying a token standard.
 *
 * @param tokenStandard - Optional standard of the token.
 */
interface TokenStandardArg {
    tokenStandard?: TokenStandard;
}
interface OrderByArg<T extends {}> {
    orderBy?: OrderBy<T>;
}
interface WhereArg<T extends {}> {
    where?: T;
}
/**
 * QUERY TYPES
 */
/**
 * A configuration object for requests to the server, including API key, extra headers, and cookie handling options.
 */
type ClientConfig = ClientHeadersType & {
    WITH_CREDENTIALS?: boolean;
    API_KEY?: string;
};
/**
 * A configuration object for a Fullnode, allowing for the inclusion of extra headers in requests.
 */
type FullNodeConfig = ClientHeadersType;
/**
 * An Indexer configuration object for sending requests with additional headers.
 */
type IndexerConfig = ClientHeadersType;
/**
 * A configuration object for a faucet, including optional authentication and headers for requests.
 */
type FaucetConfig = ClientHeadersType & {
    AUTH_TOKEN?: string;
};
/**
 * General type definition for client headers.
 */
type ClientHeadersType = {
    HEADERS?: Record<string, string | number | boolean>;
};
/**
 * Represents a client for making requests to a service provider.
 *
 * @param Req - The type of the request payload.
 * @param Res - The type of the response payload.
 */
interface ClientRequest<Req> {
    url: string;
    method: "GET" | "POST";
    originMethod?: string;
    body?: Req;
    contentType?: string;
    params?: any;
    overrides?: ClientConfig & FullNodeConfig & IndexerConfig & FaucetConfig;
    headers?: Record<string, any>;
}
interface ClientResponse<Res> {
    status: number;
    statusText: string;
    data: Res;
    config?: any;
    request?: any;
    response?: any;
    headers?: any;
}
interface Client {
    /**
     * Sends a request to the specified URL with the given options.
     *
     * @param requestOptions - The options for the request.
     * @param requestOptions.url - The URL to send the request to.
     * @param requestOptions.method - The HTTP method to use, either "GET" or "POST".
     * @param requestOptions.path - An optional path to append to the URL.
     * @param requestOptions.body - The body of the request, applicable for POST requests.
     * @param requestOptions.contentType - The content type of the request body.
     * @param requestOptions.acceptType - The expected content type of the response.
     * @param requestOptions.params - Optional parameters to include in the request.
     * @param requestOptions.originMethod - An optional method to specify the origin of the request.
     * @param requestOptions.overrides - Optional configuration overrides for the request.
     */
    provider<Req, Res>(requestOptions: ClientRequest<Req>): Promise<ClientResponse<Res>>;
}
/**
 * The API request type
 *
 * @param url - the url to make the request to, i.e. https://fullnode.devnet.aptoslabs.com/v1
 * @param method - the request method "GET" | "POST"
 * @param endpoint (optional) - the endpoint to make the request to, i.e. transactions
 * @param body (optional) - the body of the request
 * @param contentType (optional) - the content type to set the `content-type` header to,
 * by default is set to `application/json`
 * @param params (optional) - query params to add to the request
 * @param originMethod (optional) - the local method the request came from
 * @param overrides (optional) - a `ClientConfig` object type to override request data
 */
type AptosRequest = {
    url: string;
    method: "GET" | "POST";
    path?: string;
    body?: any;
    contentType?: string;
    acceptType?: string;
    params?: Record<string, string | AnyNumber | boolean | undefined>;
    originMethod?: string;
    overrides?: ClientConfig & FullNodeConfig & IndexerConfig & FaucetConfig;
};
/**
 * The API response type
 *
 * @param status - the response status. i.e. 200
 * @param statusText - the response message
 * @param data the response data
 * @param url the url the request was made to
 * @param headers the response headers
 * @param config (optional) - the request object
 * @param request (optional) - the request object
 */
interface AptosResponse<Req, Res> {
    status: number;
    statusText: string;
    data: Res;
    url: string;
    headers: any;
    config?: any;
    request?: Req;
}
/**
 * The ledger version of transactions, defaulting to the latest version if not specified.
 */
type LedgerVersionArg = {
    ledgerVersion?: AnyNumber;
};
/**
 * RESPONSE TYPES
 */
/**
 * The output of the estimate gas API, including the deprioritized estimate for the gas unit price.
 */
type GasEstimation = {
    /**
     * The deprioritized estimate for the gas unit price
     */
    deprioritized_gas_estimate?: number;
    /**
     * The current estimate for the gas unit price
     */
    gas_estimate: number;
    /**
     * The prioritized estimate for the gas unit price
     */
    prioritized_gas_estimate?: number;
};
type MoveResource<T = {}> = {
    type: MoveStructId;
    data: T;
};
/**
 * The data associated with an account, including its sequence number.
 */
type AccountData = {
    sequence_number: string;
    authentication_key: string;
};
/**
 * A Move module containing an address.
 */
type MoveModuleBytecode = {
    bytecode: string;
    abi?: MoveModule;
};
/**
 * TRANSACTION TYPES
 */
/**
 * Different types of transaction responses that can occur in the system.
 */
declare enum TransactionResponseType {
    Pending = "pending_transaction",
    User = "user_transaction",
    Genesis = "genesis_transaction",
    BlockMetadata = "block_metadata_transaction",
    StateCheckpoint = "state_checkpoint_transaction",
    Validator = "validator_transaction",
    BlockEpilogue = "block_epilogue_transaction"
}
/**
 * The response for a transaction, which can be either pending or committed.
 */
type TransactionResponse = PendingTransactionResponse | CommittedTransactionResponse;
/**
 * The response for a committed transaction, which can be one of several transaction types.
 */
type CommittedTransactionResponse = UserTransactionResponse | GenesisTransactionResponse | BlockMetadataTransactionResponse | StateCheckpointTransactionResponse | ValidatorTransactionResponse | BlockEpilogueTransactionResponse;
/**
 * Determine if the given transaction response is currently pending.
 *
 * @param response - The transaction response to evaluate.
 * @returns A boolean indicating whether the transaction is pending.
 */
declare function isPendingTransactionResponse(response: TransactionResponse): response is PendingTransactionResponse;
/**
 * Determines if the given transaction response is a user transaction.
 *
 * @param response - The transaction response to evaluate.
 * @returns A boolean indicating whether the transaction is of type User.
 */
declare function isUserTransactionResponse(response: TransactionResponse): response is UserTransactionResponse;
/**
 * Determines if the given transaction response is a Genesis transaction.
 *
 * @param response - The transaction response to evaluate.
 * @returns A boolean indicating whether the transaction is a Genesis transaction.
 */
declare function isGenesisTransactionResponse(response: TransactionResponse): response is GenesisTransactionResponse;
/**
 * Determine if the given transaction response is of type BlockMetadata.
 *
 * @param response - The transaction response to evaluate.
 * @returns A boolean indicating whether the response is a BlockMetadata transaction.
 */
declare function isBlockMetadataTransactionResponse(response: TransactionResponse): response is BlockMetadataTransactionResponse;
/**
 * Determines if the provided transaction response is a state checkpoint transaction.
 *
 * @param response - The transaction response to evaluate.
 * @returns A boolean indicating whether the transaction response is of type StateCheckpoint.
 */
declare function isStateCheckpointTransactionResponse(response: TransactionResponse): response is StateCheckpointTransactionResponse;
/**
 * Determine if the given transaction response is of type Validator.
 *
 * @param response - The transaction response to evaluate.
 * @returns A boolean indicating whether the transaction response is a Validator type.
 */
declare function isValidatorTransactionResponse(response: TransactionResponse): response is ValidatorTransactionResponse;
/**
 * Determines if the given transaction response is of the type Block Epilogue.
 *
 * @param response - The transaction response to evaluate.
 * @returns A boolean indicating whether the response is a Block Epilogue transaction.
 */
declare function isBlockEpilogueTransactionResponse(response: TransactionResponse): response is BlockEpilogueTransactionResponse;
/**
 * The response for a pending transaction, indicating that the transaction is still being processed.
 */
type PendingTransactionResponse = {
    type: TransactionResponseType.Pending;
    hash: string;
    sender: string;
    sequence_number: string;
    max_gas_amount: string;
    gas_unit_price: string;
    expiration_timestamp_secs: string;
    payload: TransactionPayloadResponse;
    signature?: TransactionSignature;
};
/**
 * The response structure for a user transaction.
 */
type UserTransactionResponse = {
    type: TransactionResponseType.User;
    version: string;
    hash: string;
    state_change_hash: string;
    event_root_hash: string;
    state_checkpoint_hash: string | null;
    gas_used: string;
    /**
     * Whether the transaction was successful
     */
    success: boolean;
    /**
     * The VM status of the transaction, can tell useful information in a failure
     */
    vm_status: string;
    accumulator_root_hash: string;
    /**
     * Final state of resources changed by the transaction
     */
    changes: Array<WriteSetChange>;
    sender: string;
    sequence_number: string;
    max_gas_amount: string;
    gas_unit_price: string;
    expiration_timestamp_secs: string;
    payload: TransactionPayloadResponse;
    signature?: TransactionSignature;
    /**
     * Events generated by the transaction
     */
    events: Array<Event>;
    timestamp: string;
};
/**
 * The response for a genesis transaction, indicating the type of transaction.
 */
type GenesisTransactionResponse = {
    type: TransactionResponseType.Genesis;
    version: string;
    hash: string;
    state_change_hash: string;
    event_root_hash: string;
    state_checkpoint_hash?: string;
    gas_used: string;
    /**
     * Whether the transaction was successful
     */
    success: boolean;
    /**
     * The VM status of the transaction, can tell useful information in a failure
     */
    vm_status: string;
    accumulator_root_hash: string;
    /**
     * Final state of resources changed by the transaction
     */
    changes: Array<WriteSetChange>;
    payload: GenesisPayload;
    /**
     * Events emitted during genesis
     */
    events: Array<Event>;
};
/**
 * The structure representing a blockchain block with its height.
 */
type BlockMetadataTransactionResponse = {
    type: TransactionResponseType.BlockMetadata;
    version: string;
    hash: string;
    state_change_hash: string;
    event_root_hash: string;
    state_checkpoint_hash: string | null;
    gas_used: string;
    /**
     * Whether the transaction was successful
     */
    success: boolean;
    /**
     * The VM status of the transaction, can tell useful information in a failure
     */
    vm_status: string;
    accumulator_root_hash: string;
    /**
     * Final state of resources changed by the transaction
     */
    changes: Array<WriteSetChange>;
    id: string;
    epoch: string;
    round: string;
    /**
     * The events emitted at the block creation
     */
    events: Array<Event>;
    /**
     * Previous block votes
     */
    previous_block_votes_bitvec: Array<number>;
    proposer: string;
    /**
     * The indices of the proposers who failed to propose
     */
    failed_proposer_indices: Array<number>;
    timestamp: string;
};
/**
 * The response for a state checkpoint transaction, indicating the type of transaction.
 */
type StateCheckpointTransactionResponse = {
    type: TransactionResponseType.StateCheckpoint;
    version: string;
    hash: string;
    state_change_hash: string;
    event_root_hash: string;
    state_checkpoint_hash: string | null;
    gas_used: string;
    /**
     * Whether the transaction was successful
     */
    success: boolean;
    /**
     * The VM status of the transaction, can tell useful information in a failure
     */
    vm_status: string;
    accumulator_root_hash: string;
    /**
     * Final state of resources changed by the transaction
     */
    changes: Array<WriteSetChange>;
    timestamp: string;
};
/**
 * The response for a validator transaction, indicating the type of transaction.
 */
type ValidatorTransactionResponse = {
    type: TransactionResponseType.Validator;
    version: string;
    hash: string;
    state_change_hash: string;
    event_root_hash: string;
    state_checkpoint_hash: string | null;
    gas_used: string;
    /**
     * Whether the transaction was successful
     */
    success: boolean;
    /**
     * The VM status of the transaction, can tell useful information in a failure
     */
    vm_status: string;
    accumulator_root_hash: string;
    /**
     * Final state of resources changed by the transaction
     */
    changes: Array<WriteSetChange>;
    /**
     * The events emitted by the validator transaction
     */
    events: Array<Event>;
    timestamp: string;
};
/**
 * Describes the gas state of the block, indicating whether the block gas limit has been reached.
 */
type BlockEndInfo = {
    block_gas_limit_reached: boolean;
    block_output_limit_reached: boolean;
    block_effective_block_gas_units: number;
    block_approx_output_size: number;
};
/**
 * A transaction executed at the end of a block that tracks data from the entire block.
 */
type BlockEpilogueTransactionResponse = {
    type: TransactionResponseType.BlockEpilogue;
    version: string;
    hash: string;
    state_change_hash: string;
    event_root_hash: string;
    state_checkpoint_hash: string | null;
    gas_used: string;
    /**
     * Whether the transaction was successful
     */
    success: boolean;
    /**
     * The VM status of the transaction, can tell useful information in a failure
     */
    vm_status: string;
    accumulator_root_hash: string;
    /**
     * Final state of resources changed by the transaction
     */
    changes: Array<WriteSetChange>;
    timestamp: string;
    block_end_info: BlockEndInfo | null;
};
/**
 * WRITESET CHANGE TYPES
 */
/**
 * A union type that encompasses both script and direct write sets for data operations.
 */
type WriteSetChange = WriteSetChangeDeleteModule | WriteSetChangeDeleteResource | WriteSetChangeDeleteTableItem | WriteSetChangeWriteModule | WriteSetChangeWriteResource | WriteSetChangeWriteTableItem;
/**
 * The structure for a module deletion change in a write set.
 */
type WriteSetChangeDeleteModule = {
    type: string;
    address: string;
    /**
     * State key hash
     */
    state_key_hash: string;
    module: MoveModuleId;
};
/**
 * The payload for a resource deletion in a write set change.
 */
type WriteSetChangeDeleteResource = {
    type: string;
    address: string;
    state_key_hash: string;
    resource: string;
};
/**
 * The payload for a write set change that deletes a table item.
 */
type WriteSetChangeDeleteTableItem = {
    type: string;
    state_key_hash: string;
    handle: string;
    key: string;
    data?: DeletedTableData;
};
/**
 * The structure for a write module change in a write set.
 */
type WriteSetChangeWriteModule = {
    type: string;
    address: string;
    state_key_hash: string;
    data: MoveModuleBytecode;
};
/**
 * The resource associated with a write set change, identified by its type.
 */
type WriteSetChangeWriteResource = {
    type: string;
    address: string;
    state_key_hash: string;
    data: MoveResource;
};
/**
 * The structure for a write operation on a table in a write set change.
 */
type WriteSetChangeWriteTableItem = {
    type: string;
    state_key_hash: string;
    handle: string;
    key: string;
    value: string;
    data?: DecodedTableData;
};
/**
 * The decoded data for a table, including its key in JSON format.
 */
type DecodedTableData = {
    /**
     * Key of table in JSON
     */
    key: any;
    /**
     * Type of key
     */
    key_type: string;
    /**
     * Value of table in JSON
     */
    value: any;
    /**
     * Type of value
     */
    value_type: string;
};
/**
 * Data for a deleted table entry.
 */
type DeletedTableData = {
    /**
     * Deleted key
     */
    key: any;
    /**
     * Deleted key type
     */
    key_type: string;
};
/**
 * The payload for a transaction response, which can be an entry function, script, or multisig payload.
 */
type TransactionPayloadResponse = EntryFunctionPayloadResponse | ScriptPayloadResponse | MultisigPayloadResponse;
/**
 * The response payload for an entry function, containing the type of the entry.
 */
type EntryFunctionPayloadResponse = {
    type: string;
    function: MoveFunctionId;
    /**
     * Type arguments of the function
     */
    type_arguments: Array<string>;
    /**
     * Arguments of the function
     */
    arguments: Array<any>;
};
/**
 * The payload for a script response, containing the type of the script.
 */
type ScriptPayloadResponse = {
    type: string;
    code: MoveScriptBytecode;
    /**
     * Type arguments of the function
     */
    type_arguments: Array<string>;
    /**
     * Arguments of the function
     */
    arguments: Array<any>;
};
/**
 * The response payload for a multisig transaction, containing the type of the transaction.
 */
type MultisigPayloadResponse = {
    type: string;
    multisig_address: string;
    transaction_payload?: EntryFunctionPayloadResponse;
};
/**
 * The payload for the genesis block containing the type of the payload.
 */
type GenesisPayload = {
    type: string;
    write_set: WriteSet;
};
/**
 * The bytecode for a Move script.
 */
type MoveScriptBytecode = {
    bytecode: string;
    abi?: MoveFunction;
};
/**
 * JSON representations of transaction signatures returned from the node API.
 */
type TransactionSignature = TransactionEd25519Signature | TransactionSecp256k1Signature | TransactionMultiEd25519Signature | TransactionMultiAgentSignature | TransactionFeePayerSignature;
/**
 * Determine if the provided signature is an Ed25519 signature.
 * This function checks for the presence of the "signature" property
 * and verifies that its value is "ed25519_signature".
 *
 * @param signature - The transaction signature to be checked.
 * @returns A boolean indicating whether the signature is an Ed25519 signature.
 */
declare function isEd25519Signature(signature: TransactionSignature): signature is TransactionFeePayerSignature;
/**
 * Determine if the provided signature is a valid secp256k1 ECDSA signature.
 *
 * @param signature - The transaction signature to validate.
 * @returns A boolean indicating whether the signature is a secp256k1 ECDSA signature.
 */
declare function isSecp256k1Signature(signature: TransactionSignature): signature is TransactionFeePayerSignature;
/**
 * Determine if the provided transaction signature is a multi-agent signature.
 *
 * @param signature - The transaction signature to evaluate.
 * @returns A boolean indicating whether the signature is a multi-agent signature.
 */
declare function isMultiAgentSignature(signature: TransactionSignature): signature is TransactionMultiAgentSignature;
/**
 * Determine if the provided signature is a fee payer signature.
 *
 * @param signature - The transaction signature to evaluate.
 * @returns A boolean indicating whether the signature is a fee payer signature.
 */
declare function isFeePayerSignature(signature: TransactionSignature): signature is TransactionFeePayerSignature;
/**
 * Determine if the provided signature is of type "multi_ed25519_signature".
 *
 * @param signature - The transaction signature to check.
 * @returns A boolean indicating whether the signature is a multi-ed25519 signature.
 */
declare function isMultiEd25519Signature(signature: TransactionSignature): signature is TransactionMultiEd25519Signature;
/**
 * The signature for a transaction using the Ed25519 algorithm.
 */
type TransactionEd25519Signature = {
    type: string;
    public_key: string;
    signature: "ed25519_signature";
};
/**
 * The structure for a Secp256k1 signature in a transaction.
 */
type TransactionSecp256k1Signature = {
    type: string;
    public_key: string;
    signature: "secp256k1_ecdsa_signature";
};
/**
 * The structure for a multi-signature transaction using Ed25519.
 */
type TransactionMultiEd25519Signature = {
    type: "multi_ed25519_signature";
    /**
     * The public keys for the Ed25519 signature
     */
    public_keys: Array<string>;
    /**
     * Signature associated with the public keys in the same order
     */
    signatures: Array<string>;
    /**
     * The number of signatures required for a successful transaction
     */
    threshold: number;
    bitmap: string;
};
/**
 * The structure for a multi-agent signature in a transaction.
 */
type TransactionMultiAgentSignature = {
    type: "multi_agent_signature";
    sender: AccountSignature;
    /**
     * The other involved parties' addresses
     */
    secondary_signer_addresses: Array<string>;
    /**
     * The associated signatures, in the same order as the secondary addresses
     */
    secondary_signers: Array<AccountSignature>;
};
/**
 * The signature of the fee payer in a transaction.
 */
type TransactionFeePayerSignature = {
    type: "fee_payer_signature";
    sender: AccountSignature;
    /**
     * The other involved parties' addresses
     */
    secondary_signer_addresses: Array<string>;
    /**
     * The associated signatures, in the same order as the secondary addresses
     */
    secondary_signers: Array<AccountSignature>;
    fee_payer_address: string;
    fee_payer_signer: AccountSignature;
};
/**
 * The union of all single account signatures, including Ed25519, Secp256k1, and MultiEd25519 signatures.
 */
type AccountSignature = TransactionEd25519Signature | TransactionSecp256k1Signature | TransactionMultiEd25519Signature;
type WriteSet = ScriptWriteSet | DirectWriteSet;
/**
 * The set of properties for writing scripts, including the type of script.
 */
type ScriptWriteSet = {
    type: string;
    execute_as: string;
    script: ScriptPayloadResponse;
};
/**
 * The set of direct write operations, identified by a type string.
 */
type DirectWriteSet = {
    type: string;
    changes: Array<WriteSetChange>;
    events: Array<Event>;
};
/**
 * The structure for an event's unique identifier, including its creation number.
 */
/**
 * The structure for an event, identified by a unique GUID.
 */
type EventGuid = {
    creation_number: string;
    account_address: string;
};
type Event = {
    guid: EventGuid;
    sequence_number: string;
    type: string;
    /**
     * The JSON representation of the event
     */
    data: any;
};
/**
 * A number representing a Move uint8 type.
 */
type MoveUint8Type = number;
/**
 * A 16-bit unsigned integer used in the Move programming language.
 */
type MoveUint16Type = number;
/**
 * A 32-bit unsigned integer type used in Move programming.
 */
type MoveUint32Type = number;
/**
 * A string representation of a 64-bit unsigned integer used in Move programming.
 */
type MoveUint64Type = string;
/**
 * A string representing a 128-bit unsigned integer in the Move programming language.
 */
type MoveUint128Type = string;
/**
 * A string representation of a 256-bit unsigned integer used in Move programming.
 */
type MoveUint256Type = string;
/**
 * A string representing a Move address.
 */
type MoveAddressType = string;
/**
 * The type for identifying objects to be moved within the system.
 */
type MoveObjectType = string;
/**
 * The type for move options, which can be a MoveType, null, or undefined.
 */
type MoveOptionType = MoveType | null | undefined;
/**
 * A structure representing a move with a name.
 */
type MoveStructId = `${string}::${string}::${string}`;
/**
 * The move function containing its name. Same as MoveStructId since it reads weird to take a StructId for a Function.
 */
type MoveFunctionId = MoveStructId;
type MoveStructType = {};
/**
 * A union type that encompasses various data types used in Move, including primitive types, address types, object types, and
 * arrays of MoveType.
 */
type MoveType = boolean | string | MoveUint8Type | MoveUint16Type | MoveUint32Type | MoveUint64Type | MoveUint128Type | MoveUint256Type | MoveAddressType | MoveObjectType | MoveStructType | Array<MoveType>;
/**
 * Possible Move values acceptable by move functions (entry, view)
 *
 * Map of a Move value to the corresponding TypeScript value
 *
 * `Bool -> boolean`
 *
 * `u8, u16, u32 -> number`
 *
 * `u64, u128, u256 -> string`
 *
 * `String -> string`
 *
 * `Address -> 0x${string}`
 *
 * `Struct - 0x${string}::${string}::${string}`
 *
 * `Object -> 0x${string}`
 *
 * `Vector -> Array<MoveValue>`
 *
 * `Option -> MoveValue | null | undefined`
 */
type MoveValue = boolean | string | MoveUint8Type | MoveUint16Type | MoveUint32Type | MoveUint64Type | MoveUint128Type | MoveUint256Type | MoveAddressType | MoveObjectType | MoveStructId | MoveOptionType | Array<MoveValue>;
/**
 * A string representation of a Move module, formatted as `module_name::function_name`.
 * Module names are case-sensitive.
 */
type MoveModuleId = `${string}::${string}`;
/**
 * Specifies the visibility levels for move functions, controlling access permissions.
 */
declare enum MoveFunctionVisibility {
    PRIVATE = "private",
    PUBLIC = "public",
    FRIEND = "friend"
}
/**
 * Abilities related to moving items within the system.
 */
declare enum MoveAbility {
    STORE = "store",
    DROP = "drop",
    KEY = "key",
    COPY = "copy"
}
/**
 * Move abilities associated with the generic type parameter of a function.
 */
type MoveFunctionGenericTypeParam = {
    constraints: Array<MoveAbility>;
};
/**
 * A field in a Move struct, identified by its name.
 */
type MoveStructField = {
    name: string;
    type: string;
};
/**
 * A Move module
 */
type MoveModule = {
    address: string;
    name: string;
    /**
     * Friends of the module
     */
    friends: Array<MoveModuleId>;
    /**
     * Public functions of the module
     */
    exposed_functions: Array<MoveFunction>;
    /**
     * Structs of the module
     */
    structs: Array<MoveStruct>;
};
/**
 * A move struct
 */
type MoveStruct = {
    name: string;
    /**
     * Whether the struct is a native struct of Move
     */
    is_native: boolean;
    /**
     * Whether the struct is a module event (aka v2 event). This will be false for v1
     * events because the value is derived from the #[event] attribute on the struct in
     * the Move source code. This attribute is only relevant for v2 events.
     */
    is_event: boolean;
    /**
     * Abilities associated with the struct
     */
    abilities: Array<MoveAbility>;
    /**
     * Generic types associated with the struct
     */
    generic_type_params: Array<MoveFunctionGenericTypeParam>;
    /**
     * Fields associated with the struct
     */
    fields: Array<MoveStructField>;
};
/**
 * Move function
 */
type MoveFunction = {
    name: string;
    visibility: MoveFunctionVisibility;
    /**
     * Whether the function can be called as an entry function directly in a transaction
     */
    is_entry: boolean;
    /**
     * Whether the function is a view function or not
     */
    is_view: boolean;
    /**
     * Generic type params associated with the Move function
     */
    generic_type_params: Array<MoveFunctionGenericTypeParam>;
    /**
     * Parameters associated with the move function
     */
    params: Array<string>;
    /**
     * Return type of the function
     */
    return: Array<string>;
};
/**
 * Roles that can be assigned within the system, indicating different levels of access and functionality.
 */
declare enum RoleType {
    VALIDATOR = "validator",
    FULL_NODE = "full_node"
}
/**
 * Information about the current blockchain ledger, including its chain ID.
 */
type LedgerInfo = {
    /**
     * Chain ID of the current chain
     */
    chain_id: number;
    epoch: string;
    ledger_version: string;
    oldest_ledger_version: string;
    ledger_timestamp: string;
    node_role: RoleType;
    oldest_block_height: string;
    block_height: string;
    /**
     * Git hash of the build of the API endpoint.  Can be used to determine the exact
     * software version used by the API endpoint.
     */
    git_hash?: string;
};
/**
 * A Block type
 */
type Block = {
    block_height: string;
    block_hash: string;
    block_timestamp: string;
    first_version: string;
    last_version: string;
    /**
     * The transactions in the block in sequential order
     */
    transactions?: Array<TransactionResponse>;
};
/**
 * The request payload for the GetTableItem API.
 */
type TableItemRequest = {
    key_type: MoveValue;
    value_type: MoveValue;
    /**
     * The value of the table item's key
     */
    key: any;
};
/**
 * A list of supported Authentication Key schemes in Aptos, consisting of combinations of signing schemes and derive schemes.
 */
type AuthenticationKeyScheme = SigningScheme | DeriveScheme;
/**
 * Different schemes for signing keys used in cryptographic operations.
 */
declare enum SigningScheme {
    /**
     * For Ed25519PublicKey
     */
    Ed25519 = 0,
    /**
     * For MultiEd25519PublicKey
     */
    MultiEd25519 = 1,
    /**
     * For SingleKey ecdsa
     */
    SingleKey = 2,
    MultiKey = 3
}
/**
 * Specifies the signing schemes available for cryptographic operations.
 */
declare enum SigningSchemeInput {
    /**
     * For Ed25519PublicKey
     */
    Ed25519 = 0,
    /**
     * For Secp256k1Ecdsa
     */
    Secp256k1Ecdsa = 2
}
/**
 * Specifies the schemes for deriving account addresses from various data sources.
 */
declare enum DeriveScheme {
    /**
     * Derives an address using an AUID, used for objects
     */
    DeriveAuid = 251,
    /**
     * Derives an address from another object address
     */
    DeriveObjectAddressFromObject = 252,
    /**
     * Derives an address from a GUID, used for objects
     */
    DeriveObjectAddressFromGuid = 253,
    /**
     * Derives an address from seed bytes, used for named objects
     */
    DeriveObjectAddressFromSeed = 254,
    /**
     * Derives an address from seed bytes, used for resource accounts
     */
    DeriveResourceAccountAddress = 255
}
/**
 * Options for configuring the behavior of the waitForTransaction() function.
 */
type WaitForTransactionOptions = {
    timeoutSecs?: number;
    checkSuccess?: boolean;
    waitForIndexer?: boolean;
};
/**
 * Input type to generate an account using the Ed25519 signing scheme.
 */
type GenerateAccountWithEd25519 = {
    scheme: SigningSchemeInput.Ed25519;
    legacy: boolean;
};
/**
 * Input type to generate an account with a Single Signer using Secp256k1.
 */
type GenerateAccountWithSingleSignerSecp256k1Key = {
    scheme: SigningSchemeInput.Secp256k1Ecdsa;
    legacy?: false;
};
type GenerateAccount = GenerateAccountWithEd25519 | GenerateAccountWithSingleSignerSecp256k1Key;

/**
 * This interface exists to define Deserializable<T> inputs for functions that
 * deserialize a byte buffer into a type T.
 * It is not intended to be implemented or extended, because Typescript has no support
 * for static methods in interfaces.
 *
 * @template T - The type that this will deserialize into.
 * @group Implementation
 * @category BCS
 */
interface Deserializable<T> {
    /**
     * Deserializes the buffered bytes into an instance of the specified class type.
     * This function provides an alternative syntax for deserialization, allowing users to call
     * `deserializer.deserialize(MyClass)` instead of `MyClass.deserialize(deserializer)`.
     *
     * @param deserializer - The deserializer instance with the buffered bytes.
     * @returns The deserialized value of class type T.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));
     * const value = deserializer.deserialize(MyClass); // where MyClass has a `deserialize` function
     * // value is now an instance of MyClass
     * // equivalent to `const value = MyClass.deserialize(deserializer)`
     * ```
     * @group Implementation
     * @category BCS
     */
    deserialize(deserializer: Deserializer): T;
}
/**
 * A class that provides methods for deserializing various data types from a byte buffer.
 * It supports deserialization of primitive types, strings, and complex objects using a BCS (Binary Common Serialization) layout.
 * @group Implementation
 * @category BCS
 */
declare class Deserializer {
    private buffer;
    private offset;
    /**
     * Creates a new instance of the class with a copy of the provided data buffer.
     * This prevents outside mutation of the buffer.
     *
     * @param data - The data to be copied into the internal buffer as a Uint8Array.
     * @group Implementation
     * @category BCS
     */
    constructor(data: Uint8Array);
    static fromHex(hex: HexInput): Deserializer;
    /**
     * Reads a specified number of bytes from the buffer and advances the offset.
     *
     * @param length - The number of bytes to read from the buffer.
     * @throws Throws an error if the read operation exceeds the buffer's length.
     * @group Implementation
     * @category BCS
     */
    private read;
    /**
     * Returns the number of bytes remaining in the buffer.
     *
     * This information is useful to determine if there's more data to be read.
     *
     * @returns The number of bytes remaining in the buffer.
     * @group Implementation
     * @category BCS
     */
    remaining(): number;
    /**
     * Asserts that the buffer has no remaining bytes.
     *
     * @throws {Error} Throws an error if there are remaining bytes in the buffer.
     * @group Implementation
     * @category BCS
     */
    assertFinished(): void;
    /**
     * Deserializes a UTF-8 encoded string from a byte array. It first reads the length of the string in bytes,
     * followed by the actual byte content, and decodes it into a string.
     *
     * BCS layout for "string": string_length | string_content
     * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.
     *
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([8, 49, 50, 51, 52, 97, 98, 99, 100]));
     * assert(deserializer.deserializeStr() === "1234abcd");
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeStr(): string;
    /**
     * @deprecated use `deserializeOption("string")` instead.
     *
     * The BCS layout for Optional<String> is 0 if none, else 1 followed by the string length and string content.
     * @returns The deserialized string if it exists, otherwise undefined.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x00]));
     * assert(deserializer.deserializeOptionStr() === undefined);
     * const deserializer = new Deserializer(new Uint8Array([1, 8, 49, 50, 51, 52, 97, 98, 99, 100]));
     * assert(deserializer.deserializeOptionStr() === "1234abcd");
     * ```
     */
    deserializeOptionStr(): string | undefined;
    /**
     * Deserializes an optional value from the buffer.
     *
     * The BCS layout for Optional<T> starts with a boolean byte (0 if none, 1 if some),
     * followed by the value if present.
     *
     * @template T - The type of the value to deserialize
     * @param type - Either a Deserializable class or one of the string literals: "string", "bytes", or "fixedBytes"
     * @param len - Required length when type is "fixedBytes", ignored otherwise
     * @returns The deserialized value if present, undefined otherwise
     *
     * @throws {Error} When "fixedBytes" is specified without a length
     *
     * @example
     * ```typescript
     * // Deserialize an optional string
     * const deserializer = new Deserializer(new Uint8Array([1, 3, 97, 98, 99]));
     * const optStr = deserializer.deserializeOption("string");
     * // optStr === "abc"
     *
     * // Deserialize an optional custom type
     * const deserializer = new Deserializer(new Uint8Array([0]));
     * const optValue = deserializer.deserializeOption(MyClass);
     * // optValue === undefined
     *
     * // Deserialize optional bytes
     * const deserializer = new Deserializer(new Uint8Array([1, 3, 1, 2, 3]));
     * const optBytes = deserializer.deserializeOption("bytes");
     * // optBytes === Uint8Array[1, 2, 3]
     *
     * // Deserialize optional fixed bytes
     * const deserializer = new Deserializer(new Uint8Array([1, 1, 2, 3, 4]));
     * const optBytes = deserializer.deserializeOption("fixedBytes", 4);
     * // optBytes === Uint8Array[1, 2, 3, 4]
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeOption(type: "string"): string | undefined;
    deserializeOption(type: "bytes"): Uint8Array | undefined;
    deserializeOption(type: "fixedBytes", len: number): Uint8Array | undefined;
    deserializeOption<T>(type: Deserializable<T>): T | undefined;
    /**
     * Deserializes an array of bytes.
     *
     * The BCS layout for "bytes" consists of a bytes_length followed by the bytes themselves, where bytes_length is a u32 integer
     * encoded as a uleb128 integer, indicating the length of the bytes array.
     *
     * @returns {Uint8Array} The deserialized array of bytes.
     * @group Implementation
     * @category BCS
     */
    deserializeBytes(): Uint8Array;
    /**
     * Deserializes an array of bytes of a specified length.
     *
     * @param len - The number of bytes to read from the source.
     * @group Implementation
     * @category BCS
     */
    deserializeFixedBytes(len: number): Uint8Array;
    /**
     * Deserializes a boolean value from a byte stream.
     *
     * The BCS layout for a boolean uses one byte, where "0x01" represents true and "0x00" represents false.
     * An error is thrown if the byte value is not valid.
     *
     * @returns The deserialized boolean value.
     * @throws Throws an error if the boolean value is invalid.
     * @group Implementation
     * @category BCS
     */
    deserializeBool(): boolean;
    /**
     * Deserializes a uint8 number from the binary data.
     *
     * BCS layout for "uint8": One byte. Binary format in little-endian representation.
     *
     * @returns {number} The deserialized uint8 number.
     * @group Implementation
     * @category BCS
     */
    deserializeU8(): Uint8;
    /**
     * Deserializes a uint16 number from a binary format in little-endian representation.
     *
     * BCS layout for "uint16": Two bytes.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x34, 0x12]));
     * assert(deserializer.deserializeU16() === 4660);
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeU16(): Uint16;
    /**
     * Deserializes a uint32 number from a binary format in little-endian representation.
     *
     * BCS layout for "uint32": Four bytes.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x78, 0x56, 0x34, 0x12]));
     * assert(deserializer.deserializeU32() === 305419896);
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeU32(): Uint32;
    /**
     * Deserializes a uint64 number.
     *
     * This function combines two 32-bit values to return a 64-bit unsigned integer in little-endian representation.
     * @example
     * ```typescript
     * const deserializer = new Deserializer(new Uint8Array([0x00, 0xEF, 0xCD, 0xAB, 0x78, 0x56, 0x34, 0x12]));
     * assert(deserializer.deserializeU64() === 1311768467750121216);
     * ```
     * @group Implementation
     * @category BCS
     */
    deserializeU64(): Uint64;
    /**
     * Deserializes a uint128 number from its binary representation.
     * This function combines two 64-bit values to return a single uint128 value in little-endian format.
     *
     * @returns {BigInt} The deserialized uint128 number.
     * @group Implementation
     * @category BCS
     */
    deserializeU128(): Uint128;
    /**
     * Deserializes a uint256 number from its binary representation.
     *
     * The BCS layout for "uint256" consists of thirty-two bytes in little-endian format.
     *
     * @returns {BigInt} The deserialized uint256 number.
     * @group Implementation
     * @category BCS
     */
    deserializeU256(): Uint256;
    /**
     * Deserializes a uleb128 encoded uint32 number.
     *
     * This function is used for interpreting lengths of variable-length sequences and tags of enum values in BCS encoding.
     *
     * @throws {Error} Throws an error if the parsed value exceeds the maximum uint32 number.
     * @returns {number} The deserialized uint32 value.
     * @group Implementation
     * @category BCS
     */
    deserializeUleb128AsU32(): Uint32;
    /**
     * Helper function that primarily exists to support alternative syntax for deserialization.
     * That is, if we have a `const deserializer: new Deserializer(...)`, instead of having to use
     * `MyClass.deserialize(deserializer)`, we can call `deserializer.deserialize(MyClass)`.
     *
     * @example const deserializer = new Deserializer(new Uint8Array([1, 2, 3]));
     * const value = deserializer.deserialize(MyClass); // where MyClass has a `deserialize` function
     * // value is now an instance of MyClass
     * // equivalent to `const value = MyClass.deserialize(deserializer)`
     * @param cls The BCS-deserializable class to deserialize the buffered bytes into.
     *
     * @returns the deserialized value of class type T
     * @group Implementation
     * @category BCS
     */
    deserialize<T>(cls: Deserializable<T>): T;
    /**
     * Deserializes an array of BCS Deserializable values given an existing Deserializer instance with a loaded byte buffer.
     *
     * @param cls The BCS-deserializable class to deserialize the buffered bytes into.
     * @returns An array of deserialized values of type T.
     * @example
     * // serialize a vector of addresses
     * const addresses = new Array<AccountAddress>(
     *   AccountAddress.from("0x1"),
     *   AccountAddress.from("0x2"),
     *   AccountAddress.from("0xa"),
     *   AccountAddress.from("0xb"),
     * );
     * const serializer = new Serializer();
     * serializer.serializeVector(addresses);
     * const serializedBytes = serializer.toUint8Array();
     *
     * // deserialize the bytes into an array of addresses
     * const deserializer = new Deserializer(serializedBytes);
     * const deserializedAddresses = deserializer.deserializeVector(AccountAddress);
     * // deserializedAddresses is now an array of AccountAddress instances
     * @group Implementation
     * @category BCS
     */
    deserializeVector<T>(cls: Deserializable<T>): Array<T>;
}

/**
 * This error is used to explain why parsing failed.
 * @group Implementation
 * @category Serialization
 */
declare class ParsingError<T> extends Error {
    /**
     * This provides a programmatic way to access why parsing failed. Downstream devs
     * might want to use this to build their own error messages if the default error
     * messages are not suitable for their use case. This should be an enum.
     * @group Implementation
     * @category Serialization
     */
    invalidReason: T;
    /**
     * Creates an instance of the error with a specified message and invalid reason.
     *
     * @param message The error message that describes the issue.
     * @param invalidReason The reason why the input is considered invalid.
     * @group Implementation
     * @category Serialization
     */
    constructor(message: string, invalidReason: T);
}
/**
 * Whereas ParsingError is thrown when parsing fails, e.g. in a fromString function,
 * this type is returned from "defensive" functions like isValid.
 * @group Implementation
 * @category Serialization
 */
type ParsingResult<T> = {
    /**
     * True if valid, false otherwise.
     * @group Implementation
     * @category Serialization
     */
    valid: boolean;
    /**
     * If valid is false, this will be a code explaining why parsing failed.
     * @group Implementation
     * @category Serialization
     */
    invalidReason?: T;
    /**
     * If valid is false, this will be a string explaining why parsing failed.
     * @group Implementation
     * @category Serialization
     */
    invalidReasonMessage?: string;
};

/**
 * Provides reasons for parsing failures related to hexadecimal values.
 * @group Implementation
 * @category Serialization
 */
declare enum HexInvalidReason {
    TOO_SHORT = "too_short",
    INVALID_LENGTH = "invalid_length",
    INVALID_HEX_CHARS = "invalid_hex_chars"
}
/**
 * NOTE: Do not use this class when working with account addresses; use AccountAddress instead.
 * When accepting hex data as input to a function, prefer to accept HexInput and
 *
 * A helper class for working with hex data. Hex data, when represented as a string,
 * generally looks like this, for example: 0xaabbcc, 45cd32, etc.
 *
 * then use the static helper methods of this class to convert it into the desired
 * format. This enables the greatest flexibility for the developer.
 *
 * Example usage:
 * ```typescript
 * getTransactionByHash(txnHash: HexInput): Promise<Transaction> {
 *   const txnHashString = Hex.fromHexInput(txnHash).toString();
 *   return await getTransactionByHashInner(txnHashString);
 * }
 * ```
 * This call to `Hex.fromHexInput().toString()` converts the HexInput to a hex string
 * with a leading 0x prefix, regardless of what the input format was.
 *
 * Other ways to chain the functions together:
 * - `Hex.fromHexString({ hexInput: "0x1f" }).toUint8Array()`
 * - `new Hex([1, 3]).toStringWithoutPrefix()`
 * @group Implementation
 * @category Serialization
 */
declare class Hex {
    private readonly data;
    /**
     * Create a new Hex instance from a Uint8Array.
     *
     * @param data - The Uint8Array containing the data to initialize the Hex instance.
     * @group Implementation
     * @category Serialization
     */
    constructor(data: Uint8Array);
    /**
     * Get the inner hex data as a Uint8Array. The inner data is already a Uint8Array, so no conversion takes place.
     *
     * @returns Hex data as Uint8Array
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    /**
     * Get the hex data as a string without the 0x prefix.
     *
     * @returns Hex string without 0x prefix
     * @group Implementation
     * @category Serialization
     */
    toStringWithoutPrefix(): string;
    /**
     * Get the hex data as a string with the 0x prefix.
     *
     * @returns Hex string with 0x prefix
     * @group Implementation
     * @category Serialization
     */
    toString(): string;
    /**
     * Converts a hex string into a Hex instance, allowing for both prefixed and non-prefixed formats.
     *
     * @param str - A hex string, with or without the 0x prefix.
     *
     * @throws ParsingError - If the hex string is too short, has an odd number of characters, or contains invalid hex characters.
     *
     * @returns Hex - The resulting Hex instance created from the provided string.
     * @group Implementation
     * @category Serialization
     */
    static fromHexString(str: string): Hex;
    /**
     * Converts an instance of HexInput, which can be a string or a Uint8Array, into a Hex instance.
     * This function is useful for transforming hexadecimal representations into a structured Hex object for further manipulation.
     *
     * @param hexInput - A HexInput which can be a string or Uint8Array.
     * @returns A Hex instance created from the provided hexInput.
     * @group Implementation
     * @category Serialization
     */
    static fromHexInput(hexInput: HexInput): Hex;
    /**
     * Converts an instance of HexInput, which can be a string or a Uint8Array, into a Uint8Array.
     *
     * @param hexInput - A HexInput which can be a string or Uint8Array.
     * @returns A Uint8Array created from the provided hexInput.
     */
    static hexInputToUint8Array(hexInput: HexInput): Uint8Array;
    /**
     * Converts a HexInput (string or Uint8Array) to a hex string with '0x' prefix.
     *
     * @param hexInput - The input to convert, either a hex string (with/without '0x' prefix) or Uint8Array
     * @returns A hex string with '0x' prefix (e.g., "0x1234")
     *
     * @example
     * ```typescript
     * Hex.hexInputToString("1234")        // returns "0x1234"
     * Hex.hexInputToString("0x1234")      // returns "0x1234"
     * Hex.hexInputToString(new Uint8Array([0x12, 0x34])) // returns "0x1234"
     * ```
     */
    static hexInputToString(hexInput: HexInput): string;
    /**
     * Converts a HexInput (string or Uint8Array) to a hex string without '0x' prefix.
     *
     * @param hexInput - The input to convert, either a hex string (with/without '0x' prefix) or Uint8Array
     * @returns A hex string without '0x' prefix (e.g., "1234")
     *
     * @example
     * ```typescript
     * Hex.hexInputToStringWithoutPrefix("1234")        // returns "1234"
     * Hex.hexInputToStringWithoutPrefix("0x1234")      // returns "1234"
     * Hex.hexInputToStringWithoutPrefix(new Uint8Array([0x12, 0x34])) // returns "1234"
     * ```
     */
    static hexInputToStringWithoutPrefix(hexInput: HexInput): string;
    /**
     * Check if the provided string is a valid hexadecimal representation.
     *
     * @param str - A hex string representing byte data.
     *
     * @returns An object containing:
     *  - valid: A boolean indicating whether the string is valid.
     *  - invalidReason: The reason for invalidity if the string is not valid.
     *  - invalidReasonMessage: A message explaining why the string is invalid.
     * @group Implementation
     * @category Serialization
     */
    static isValid(str: string): ParsingResult<HexInvalidReason>;
    /**
     * Determine if two Hex instances are equal by comparing their underlying byte data.
     *
     * @param other The Hex instance to compare to.
     * @returns true if the Hex instances are equal, false if not.
     * @group Implementation
     * @category Serialization
     */
    equals(other: Hex): boolean;
}
declare const hexToAsciiString: (hex: string) => string;

/**
 * This class serves as a base class for all serializable types. It facilitates
 * composable serialization of complex types and enables the serialization of
 * instances to their BCS (Binary Canonical Serialization) representation.
 * @group Implementation
 * @category BCS
 */
declare abstract class Serializable {
    abstract serialize(serializer: Serializer): void;
    /**
     * Serializes a `Serializable` value to its BCS representation.
     * This function is the TypeScript SDK equivalent of `bcs::to_bytes` in Move.
     * @returns the BCS representation of the Serializable instance as a byte buffer.
     * @group Implementation
     * @category BCS
     */
    bcsToBytes(): Uint8Array;
    /**
     * Converts the BCS-serialized bytes of a value into a Hex instance.
     * This function provides a Hex representation of the BCS-serialized data for easier handling and manipulation.
     * @returns A Hex instance with the BCS-serialized bytes loaded into its underlying Uint8Array.
     * @group Implementation
     * @category BCS
     */
    bcsToHex(): Hex;
    /**
     * Returns the hex string representation of the `Serializable` value without the 0x prefix.
     * @returns the hex format as a string without `0x` prefix.
     */
    toStringWithoutPrefix(): string;
    /**
     * Returns the hex string representation of the `Serializable` value with the 0x prefix.
     * @returns the hex formatas a string prefixed by `0x`.
     */
    toString(): string;
}
/**
 * A class for serializing various data types into a binary format.
 * It provides methods to serialize strings, bytes, numbers, and other serializable objects
 * using the Binary Coded Serialization (BCS) layout. The serialized data can be retrieved as a
 * Uint8Array.
 * @group Implementation
 * @category BCS
 */
declare class Serializer {
    private buffer;
    private offset;
    /**
     * Constructs a serializer with a buffer of size `length` bytes, 64 bytes by default.
     * The `length` must be greater than 0.
     *
     * @param length - The size of the buffer in bytes.
     * @group Implementation
     * @category BCS
     */
    constructor(length?: number);
    /**
     * Ensures that the internal buffer can accommodate the specified number of bytes.
     * This function dynamically resizes the buffer if the current size is insufficient.
     *
     * @param bytes - The number of bytes to ensure the buffer can handle.
     * @group Implementation
     * @category BCS
     */
    private ensureBufferWillHandleSize;
    /**
     * Appends the specified values to the buffer, ensuring that the buffer can accommodate the new data.
     *
     * @param {Uint8Array} values - The values to be appended to the buffer.
     * @group Implementation
     * @category BCS
     */
    protected appendToBuffer(values: Uint8Array): void;
    /**
     * Serializes a value into the buffer using the provided function, ensuring the buffer can accommodate the size.
     *
     * @param fn - The function to serialize the value, which takes a byte offset, the value to serialize, and an optional little-endian flag.
     * @param fn.byteOffset - The byte offset at which to write the value.
     * @param fn.value - The numeric value to serialize into the buffer.
     * @param fn.littleEndian - Optional flag indicating whether to use little-endian byte order (defaults to true).
     * @group Implementation
     * @category BCS
     */
    private serializeWithFunction;
    /**
     * Serializes a string. UTF8 string is supported.
     * The number of bytes in the string content is serialized first, as a uleb128-encoded u32 integer.
     * Then the string content is serialized as UTF8 encoded bytes.
     *
     * BCS layout for "string": string_length | string_content
     * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.
     *
     * @param value - The string to serialize.
     *
     * @example
     * ```typescript
     * const serializer = new Serializer();
     * serializer.serializeStr("1234abcd");
     * assert(serializer.toUint8Array() === new Uint8Array([8, 49, 50, 51, 52, 97, 98, 99, 100]));
     * ```
     * @group Implementation
     * @category BCS
     */
    serializeStr(value: string): void;
    /**
     * Serializes an array of bytes.
     *
     * This function encodes the length of the byte array as a u32 integer in uleb128 format, followed by the byte array itself.
     * BCS layout for "bytes": bytes_length | bytes
     * where bytes_length is a u32 integer encoded as a uleb128 integer, equal to the length of the bytes array.
     * @param value - The byte array to serialize.
     * @group Implementation
     * @category BCS
     */
    serializeBytes(value: Uint8Array): void;
    /**
     * Serializes an array of bytes with a known length, allowing for efficient deserialization without needing to serialize the
     * length itself.
     * When deserializing, the number of bytes to deserialize needs to be passed in.
  
     * @param value - The Uint8Array to be serialized.
     * @group Implementation
     * @category BCS
     */
    serializeFixedBytes(value: Uint8Array): void;
    /**
     * Serializes a boolean value into a byte representation.
     *
     * The BCS layout for a boolean uses one byte, where "0x01" represents true and "0x00" represents false.
     *
     * @param value - The boolean value to serialize.
     * @group Implementation
     * @category BCS
     */
    serializeBool(value: boolean): void;
    /**
     * Serializes a Uint8 value and appends it to the buffer.
     * BCS layout for "uint8": One byte. Binary format in little-endian representation.
     *
     * @param value - The Uint8 value to serialize.
     * @group Implementation
     * @category BCS
     */
    serializeU8(value: Uint8): void;
    /**
     * Serializes a uint16 number.
     *
     * @group Implementation
     * @category BCS
  
     */
    /**
     * Serializes a 16-bit unsigned integer value into a binary format.
     * BCS layout for "uint16": Two bytes. Binary format in little-endian representation.
     *
     * @param value - The 16-bit unsigned integer value to serialize.
     * @example
     * ```typescript
     * const serializer = new Serializer();
     * serializer.serializeU16(4660);
     * assert(serializer.toUint8Array() === new Uint8Array([0x34, 0x12]));
     * ```
     * @group Implementation
     * @category BCS
     */
    serializeU16(value: Uint16): void;
    /**
     * Serializes a 32-bit unsigned integer value into a binary format.
     * This function is useful for encoding data that needs to be stored or transmitted in a compact form.
     * @example
     * ```typescript
     * const serializer = new Serializer();
     * serializer.serializeU32(305419896);
     * assert(serializer.toUint8Array() === new Uint8Array([0x78, 0x56, 0x34, 0x12]));
     * ```
     * @param value - The 32-bit unsigned integer value to serialize.
     * @group Implementation
     * @category BCS
     */
    serializeU32(value: Uint32): void;
    /**
     * Serializes a 64-bit unsigned integer into a format suitable for storage or transmission.
     * This function breaks down the value into two 32-bit components and writes them in little-endian order.
     *
     * @param value - The 64-bit unsigned integer to serialize, represented as a number.
     * @example
     * ```ts
     * const serializer = new Serializer();
     * serializer.serializeU64(1311768467750121216);
     * assert(serializer.toUint8Array() === new Uint8Array([0x00, 0xEF, 0xCD, 0xAB, 0x78, 0x56, 0x34, 0x12]));
     * ```
     * @group Implementation
     * @category BCS
     */
    serializeU64(value: AnyNumber): void;
    /**
     * Serializes a U128 value into a format suitable for storage or transmission.
     *
     * @param value - The U128 value to serialize, represented as a number.
     * @group Implementation
     * @category BCS
     */
    serializeU128(value: AnyNumber): void;
    /**
     * Serializes a U256 value into a byte representation.
     * This function is essential for encoding large numbers in a compact format suitable for transmission or storage.
     *
     * @param value - The U256 value to serialize, represented as an AnyNumber.
     * @group Implementation
     * @category BCS
     */
    serializeU256(value: AnyNumber): void;
    /**
     * Serializes a 32-bit unsigned integer as a variable-length ULEB128 encoded byte array.
     * BCS uses uleb128 encoding in two cases: (1) lengths of variable-length sequences and (2) tags of enum values
     *
     * @param val - The 32-bit unsigned integer value to be serialized.
     * @group Implementation
     * @category BCS
     */
    serializeU32AsUleb128(val: Uint32): void;
    /**
     * Returns the buffered bytes as a Uint8Array.
     *
     * This function allows you to retrieve the byte representation of the buffer up to the current offset.
     *
     * @returns Uint8Array - The byte array representation of the buffer.
     * @group Implementation
     * @category BCS
     */
    toUint8Array(): Uint8Array;
    /**
     * Serializes a `Serializable` value, facilitating composable serialization.
     *
     * @param value The Serializable value to serialize.
     *
     * @returns the serializer instance
     * @group Implementation
     * @category BCS
     */
    serialize<T extends Serializable>(value: T): void;
    /**
     * Serializes an array of BCS Serializable values to a serializer instance.
     * The bytes are added to the serializer instance's byte buffer.
     *
     * @param values The array of BCS Serializable values
     * @example
     * const addresses = new Array<AccountAddress>(
     *   AccountAddress.from("0x1"),
     *   AccountAddress.from("0x2"),
     *   AccountAddress.from("0xa"),
     *   AccountAddress.from("0xb"),
     * );
     * const serializer = new Serializer();
     * serializer.serializeVector(addresses);
     * const serializedBytes = serializer.toUint8Array();
     * // serializedBytes is now the BCS-serialized bytes
     * // The equivalent value in Move would be:
     * // `bcs::to_bytes(&vector<address> [@0x1, @0x2, @0xa, @0xb])`;
     * @group Implementation
     * @category BCS
     */
    serializeVector<T extends Serializable>(values: Array<T>): void;
    /**
     * Serializes an optional value which can be a Serializable, string, or Uint8Array.
     * For strings and Uint8Arrays, it uses the appropriate serialization method.
     *
     * @param value The value to serialize (Serializable, string, Uint8Array, or undefined)
     * @param len Optional fixed length for Uint8Array serialization. If provided, uses serializeFixedBytes instead of serializeBytes
     *
     * @example
     * ```typescript
     * const serializer = new Serializer();
     * serializer.serializeOption("hello");  // Serializes optional string
     * serializer.serializeOption(new Uint8Array([1, 2, 3]));  // Serializes optional bytes
     * serializer.serializeOption(new Uint8Array([1, 2, 3]), 3);  // Serializes optional fixed-length bytes
     * serializer.serializeOption(new AccountAddress(...));  // Serializes optional Serializable
     * serializer.serializeOption(undefined);  // Serializes none case
     * ```
     * @group Implementation
     * @category BCS
     */
    serializeOption<T extends Serializable | string | Uint8Array>(value?: T, len?: number): void;
    /**
     * @deprecated use `serializeOption` instead.
     * Serializes an optional string, supporting UTF8 encoding.
     * The function encodes the existence of the string first, followed by the length and content if it exists.
     *
     * BCS layout for optional "string": 1 | string_length | string_content
     * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.
     * BCS layout for undefined: 0
     *
     * @param value - The optional string to serialize. If undefined, it will serialize as 0.
     * @group Implementation
     * @category BCS
     */
    serializeOptionStr(value?: string): void;
}
/**
 * @group Implementation
 * @category BCS
 */
declare function ensureBoolean(value: unknown): asserts value is boolean;
/**
 * @group Implementation
 * @category BCS
 */
declare const outOfRangeErrorMessage: (value: AnyNumber, min: AnyNumber, max: AnyNumber) => string;
/**
 * Validates that a given number is within a specified range.
 * This function throws an error if the value is outside the defined minimum and maximum bounds.
 *
 * @param value - The number to validate.
 * @param minValue - The minimum allowable value (inclusive).
 * @param maxValue - The maximum allowable value (inclusive).
 * @group Implementation
 * @category BCS
 */
declare function validateNumberInRange<T extends AnyNumber>(value: T, minValue: T, maxValue: T): void;

interface TransactionArgument extends EntryFunctionArgument, ScriptFunctionArgument {
}
/**
 * Represents an argument for entry functions, providing methods to serialize the argument
 * to BCS-serialized bytes and convert it to different formats.
 * @group Implementation
 * @category Transactions
 */
interface EntryFunctionArgument {
    /**
     * Serialize an argument to BCS-serialized bytes.
     *
     * @param serializer - The serializer instance used for serialization.
     * @group Implementation
     * @category Transactions
     */
    serialize(serializer: Serializer): void;
    /**
     * Serialize an argument to BCS-serialized bytes.
     * Serialize an argument as a type-agnostic, fixed byte sequence. The byte sequence contains
     * the number of the following bytes followed by the BCS-serialized bytes for a typed argument.
     *
     * @param serializer - The serializer used to convert the argument.
     * @group Implementation
     * @category Transactions
     */
    serializeForEntryFunction(serializer: Serializer): void;
    /**
     * Convert the argument to BCS-serialized bytes.
     *
     * @returns Uint8Array representing the BCS-serialized bytes of the argument.
     * @group Implementation
     * @category Transactions
     */
    bcsToBytes(): Uint8Array;
    /**
     * Converts the BCS-serialized bytes of an argument into a hexadecimal representation.
     * This function is useful for obtaining a Hex instance that encapsulates the BCS-serialized bytes,
     * allowing for easier manipulation and representation of the data.
     * @returns A Hex instance containing the BCS-serialized bytes.
     * @group Implementation
     * @category Transactions
     */
    bcsToHex(): Hex;
}
/**
 * Represents an argument for script functions, providing methods to serialize and convert to bytes.
 * @group Implementation
 * @category Transactions
 */
interface ScriptFunctionArgument {
    /**
     * Serialize an argument to BCS-serialized bytes.
     * @group Implementation
     * @category Transactions
     */
    serialize(serializer: Serializer): void;
    /**
     * Serialize an argument to BCS-serialized bytes as a type aware byte sequence.
     * The byte sequence contains an enum variant index followed by the BCS-serialized
     * bytes for a typed argument.
     * @group Implementation
     * @category Transactions
     */
    serializeForScriptFunction(serializer: Serializer): void;
    bcsToBytes(): Uint8Array;
    bcsToHex(): Hex;
}

/**
 * Provides reasons for an address was invalid.
 * @group Implementation
 * @category Serialization
 */
declare enum AddressInvalidReason {
    INCORRECT_NUMBER_OF_BYTES = "incorrect_number_of_bytes",
    INVALID_HEX_CHARS = "invalid_hex_chars",
    TOO_SHORT = "too_short",
    TOO_LONG = "too_long",
    LEADING_ZERO_X_REQUIRED = "leading_zero_x_required",
    LONG_FORM_REQUIRED_UNLESS_SPECIAL = "long_form_required_unless_special",
    INVALID_PADDING_ZEROES = "INVALID_PADDING_ZEROES",
    INVALID_PADDING_STRICTNESS = "INVALID_PADDING_STRICTNESS"
}
/**
 * The input for an account address, which can be either a hexadecimal string or a standard account address.
 * @group Implementation
 * @category Serialization
 */
type AccountAddressInput = HexInput | AccountAddress;
/**
 * NOTE: Only use this class for account addresses. For other hex data, e.g. transaction
 * hashes, use the Hex class.
 *
 * AccountAddress is used for working with account addresses. Account addresses, when
 * represented as a string, generally look like these examples:
 * - 0x1
 * - 0xaa86fe99004361f747f91342ca13c426ca0cccb0c1217677180c9493bad6ef0c
 *
 * Proper formatting and parsing of account addresses is defined by AIP-40.
 * To learn more about the standard, read the AIP here:
 * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.
 *
 * The comments in this class make frequent reference to the LONG and SHORT formats,
 * as well as "special" addresses. To learn what these refer to see AIP-40.
 * @group Implementation
 * @category Serialization
 */
declare class AccountAddress extends Serializable implements TransactionArgument {
    /**
     * This is the internal representation of an account address.
     * @group Implementation
     * @category Serialization
     */
    readonly data: Uint8Array;
    /**
     * The number of bytes that make up an account address.
     * @group Implementation
     * @category Serialization
     */
    static readonly LENGTH: number;
    /**
     * The length of an address string in LONG form without a leading 0x.
     * @group Implementation
     * @category Serialization
     */
    static readonly LONG_STRING_LENGTH: number;
    static ZERO: AccountAddress;
    static ONE: AccountAddress;
    static TWO: AccountAddress;
    static THREE: AccountAddress;
    static FOUR: AccountAddress;
    static A: AccountAddress;
    /**
     * Creates an instance of AccountAddress from a Uint8Array.
     *
     * This function ensures that the input data is exactly 32 bytes long, which is required for a valid account address.
     *
     * @param input A Uint8Array representing an account address.
     * @throws ParsingError if the input length is not equal to 32 bytes.
     * @group Implementation
     * @category Serialization
     */
    constructor(input: Uint8Array);
    /**
     * Determines if the address is classified as special, which is defined as 0x0 to 0xf inclusive.
     * In other words, the last byte of the address must be < 0b10000 (16)
     * and every other byte must be zero.
     *
     * For more information on how special addresses are defined, see AIP-40:
     * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.
     *
     * @returns true if the address is special, false otherwise.
     * @group Implementation
     * @category Serialization
     */
    isSpecial(): boolean;
    /**
     * Return the AccountAddress as a string as per AIP-40.
     * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.
     * This representation returns special addresses in SHORT form (0xf)
     * and other addresses in LONG form (0x + 64 characters).
     *
     * @returns AccountAddress as a string conforming to AIP-40.
     * @group Implementation
     * @category Serialization
     */
    toString(): `0x${string}`;
    /**
     * Return the AccountAddress as a string conforming to AIP-40 but without the leading 0x.
     *
     * NOTE: Prefer to use `toString` where possible.
     *
     * @returns AccountAddress as a string without the leading 0x.
     * @group Implementation
     * @category Serialization
     */
    toStringWithoutPrefix(): string;
    /**
     * Convert the account address to a string in LONG format, which is always 0x followed by 64 hex characters.
     *
     * NOTE: Prefer to use `toString` where possible, as it formats special addresses using the SHORT form (no leading 0s).
     *
     * @returns AccountAddress as a string in LONG form.
     * @group Implementation
     * @category Serialization
     */
    toStringLong(): `0x${string}`;
    /**
     * Returns the account address as a string in LONG form without a leading 0x.
     * This function will include leading zeroes and will produce a string of 64 hex characters.
     *
     * NOTE: Prefer to use `toString` where possible, as it formats special addresses using the SHORT form (no leading 0s).
     *
     * @returns {string} The account address in LONG form.
     * @group Implementation
     * @category Serialization
     */
    toStringLongWithoutPrefix(): string;
    /**
     * Convert the account address to a string in SHORT format, which is 0x followed by the shortest
     * possible representation (no leading zeros).
     *
     * @returns AccountAddress as a string in SHORT form.
     * @group Implementation
     * @category Serialization
     */
    toStringShort(): `0x${string}`;
    /**
     * Returns a lossless short string representation of the address by trimming leading zeros.
     * If the address consists of all zeros, returns "0".
     *
     * @returns A string representation of the address without leading zeros
     * @group Implementation
     * @category Serialization
     */
    toStringShortWithoutPrefix(): string;
    /**
     * Get the inner data as a Uint8Array.
     * The inner data is already a Uint8Array, so no conversion takes place.
     *
     * @returns Hex data as Uint8Array
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    /**
     * Serialize the AccountAddress to a Serializer instance's data buffer.
     * @param serializer The serializer to serialize the AccountAddress to.
     * @returns void
     * @example
     * const serializer = new Serializer();
     * const address = AccountAddress.fromString("0x1");
     * address.serialize(serializer);
     * const bytes = serializer.toUint8Array();
     * // `bytes` is now the BCS-serialized address.
     * @group Implementation
     * @category Serialization
     */
    serialize(serializer: Serializer): void;
    /**
     * Serializes the current instance into a byte sequence suitable for entry functions.
     * This allows for the proper encoding of data when interacting with entry functions in the blockchain.
     *
     * @param serializer - The serializer instance used to convert the data into bytes.
     * @group Implementation
     * @category Serialization
     */
    serializeForEntryFunction(serializer: Serializer): void;
    /**
     * Serializes the current instance for use in a script function by encoding it into a byte sequence.
     * This process involves serializing the variant index and the instance data, making it suitable for transmission.
     *
     * @param serializer - The serializer instance used to perform the serialization.
     * @group Implementation
     * @category Serialization
     */
    serializeForScriptFunction(serializer: Serializer): void;
    /**
     * Deserialize an AccountAddress from the byte buffer in a Deserializer instance.
     * This function allows you to convert a byte representation of an AccountAddress into an instance of AccountAddress.
     * @param deserializer The deserializer to deserialize the AccountAddress from.
     * @returns An instance of AccountAddress.
     * @example
     * const bytes = hexToBytes("0x0102030405060708091011121314151617181920212223242526272829303132");
     * const deserializer = new Deserializer(bytes);
     * const address = AccountAddress.deserialize(deserializer);
     * // `address` is now an instance of AccountAddress.
     * @group Implementation
     * @category Serialization
     */
    static deserialize(deserializer: Deserializer): AccountAddress;
    /**
     * NOTE: This function has strict parsing behavior. For relaxed behavior, please use
     * the `fromString` function.
     *
     * Creates an instance of AccountAddress from a hex string.
     *
     * This function allows only the strictest formats defined by AIP-40. In short this
     * means only the following formats are accepted:
     *
     * - LONG
     * - SHORT for special addresses
     *
     * Where:
     * - LONG is defined as 0x + 64 hex characters.
     * - SHORT for special addresses is 0x0 to 0xf inclusive without padding zeroes.
     *
     * This means the following are not accepted:
     * - SHORT for non-special addresses.
     * - Any address without a leading 0x.
     *
     * @param input - A hex string representing an account address.
     *
     * @throws {ParsingError} If the hex string does not start with 0x or is not in a valid format.
     *
     * @remarks
     *
     * This function has strict parsing behavior. For relaxed behavior, please use the `fromString` function.
     *
     * @see AIP-40 documentation for more details on address formats:
     * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.
     *
     * @returns An instance of AccountAddress.
     * @group Implementation
     * @category Serialization
     */
    static fromStringStrict(input: string): AccountAddress;
    /**
     * NOTE: This function has relaxed parsing behavior. For strict behavior, please use
     * the `fromStringStrict` function. Where possible use `fromStringStrict` rather than this
     * function, `fromString`.
     *
     * Creates an instance of AccountAddress from a hex string.
     *
     * This function allows all formats defined by AIP-40. In short this means the
     * following formats are accepted:
     *
     * - LONG, with or without leading 0x
     * - SHORT*, with or without leading 0x
     *
     * Where:
     * - LONG is 64 hex characters.
     * - SHORT* is 1 to 63 hex characters inclusive. The address can have missing values up to `maxMissingChars` before it is padded.
     * - Padding zeroes are allowed, e.g. 0x0123 is valid.
     *
     * Learn more about the different address formats by reading AIP-40:
     * https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-40.md.
     *
     * @param input A hex string representing an account address.
     * @param args.maxMissingChars The number of characters that can be missing in a padded address before it is invalid.
     *
     * @returns An instance of AccountAddress.
     *
     * @throws ParsingError if the hex string is too short, too long, or contains invalid characters.
     * @group Implementation
     * @category Serialization
     */
    static fromString(input: string, { maxMissingChars }?: {
        maxMissingChars?: number;
    }): AccountAddress;
    /**
     * Convenience method for creating an AccountAddress from various input types.
     * This function accepts a string, Uint8Array, or an existing AccountAddress instance and returns the corresponding
     * AccountAddress.
     *
     * @param input - The input to convert into an AccountAddress. This can be a string representation of an address, a Uint8Array,
     * or an existing AccountAddress.
     * @param args.maxMissingChars The number of characters that can be missing in a padded address before it is invalid.
     * @group Implementation
     * @category Serialization
     */
    static from(input: AccountAddressInput, { maxMissingChars }?: {
        maxMissingChars?: number;
    }): AccountAddress;
    /**
     * Create an AccountAddress from various input types, including strings, Uint8Array, and AccountAddress instances.
     *
     * @param input - The input to convert into an AccountAddress, which can be a string, a Uint8Array, or an AccountAddress.
     * @group Implementation
     * @category Serialization
     */
    static fromStrict(input: AccountAddressInput): AccountAddress;
    /**
     * Check if the provided input is a valid AccountAddress.
     *
     * @param args - The arguments for validation.
     * @param args.input - A hex string representing an account address.
     * @param args.strict - If true, use strict parsing behavior; if false, use relaxed parsing behavior.
     *
     * @returns An object indicating whether the address is valid. If valid, valid = true; if not, valid = false with additional details.
     * If the address is invalid, invalidReason will explain why it is invalid, and invalidReasonMessage will provide the error message.
     * @group Implementation
     * @category Serialization
     */
    static isValid(args: {
        input: AccountAddressInput;
        strict?: boolean;
    }): ParsingResult<AddressInvalidReason>;
    /**
     * Determine if two AccountAddresses are equal based on their underlying byte data.
     *
     * @param other - The AccountAddress to compare to.
     * @returns true if the AccountAddresses are equal, false if not.
     * @group Implementation
     * @category Serialization
     */
    equals(other: AccountAddress): boolean;
}

export { type WaitForTransactionOptions as $, type AnyNumber as A, type GetAccountOwnedTokensFromCollectionResponse as B, type Client as C, Deserializer as D, type EntryFunctionArgument as E, type FullNodeConfig as F, type GetAccountOwnedTokensQueryResponse as G, type HexInput as H, type IndexerConfig as I, type GetAccountCollectionsWithOwnedTokenResponse as J, type GetAccountCoinsDataResponse as K, type LedgerVersionArg as L, type MoveModuleId as M, Network as N, type OrderByArg as O, PrivateKeyVariants as P, type GetObjectDataQueryResponse as Q, type GetCollectionDataResponse as R, Serializable as S, type TransactionArgument as T, type Uint8 as U, type GetTokenDataResponse as V, type WhereArg as W, type GetCurrentTokenOwnershipResponse as X, type GetOwnedTokensResponse as Y, type GetTokenActivityResponse as Z, type GetEventsResponse as _, Serializer as a, type BlockEndInfo as a$, type UserTransactionResponse as a0, type GetFungibleAssetMetadataResponse as a1, type GetFungibleAssetActivitiesResponse as a2, type GetCurrentFungibleAssetBalancesResponse as a3, type LedgerInfo as a4, type Block as a5, type GetChainTopUserTransactionsResponse as a6, type GraphqlQuery as a7, type GetProcessorStatusResponse as a8, type GetANSNameResponse as a9, type OrderBy as aA, type OrderByValue as aB, type TokenStandard as aC, TypeTagVariants as aD, ScriptTransactionArgumentVariants as aE, TransactionPayloadVariants as aF, TransactionVariants as aG, TransactionAuthenticatorVariant as aH, AccountAuthenticatorVariant as aI, AnySignatureVariant as aJ, EphemeralSignatureVariant as aK, type Uint64 as aL, type Uint128 as aM, type Uint256 as aN, type ClientHeadersType as aO, TransactionResponseType as aP, isPendingTransactionResponse as aQ, isUserTransactionResponse as aR, isGenesisTransactionResponse as aS, isBlockMetadataTransactionResponse as aT, isStateCheckpointTransactionResponse as aU, isValidatorTransactionResponse as aV, isBlockEpilogueTransactionResponse as aW, type GenesisTransactionResponse as aX, type BlockMetadataTransactionResponse as aY, type StateCheckpointTransactionResponse as aZ, type ValidatorTransactionResponse as a_, type GetNumberOfDelegatorsResponse as aa, type GetDelegatedStakingActivitiesResponse as ab, type PendingTransactionResponse as ac, type CommittedTransactionResponse as ad, type GasEstimation as ae, type TableItemRequest as af, type GetTableItemsDataResponse as ag, type GetTableItemsMetadataResponse as ah, EphemeralPublicKeyVariant as ai, EphemeralCertificateVariant as aj, ZkpVariant as ak, SigningScheme as al, SigningSchemeInput as am, type ClientRequest as an, type ClientResponse as ao, type AptosRequest as ap, type AptosResponse as aq, MimeType as ar, ensureBoolean as as, outOfRangeErrorMessage as at, validateNumberInRange as au, AddressInvalidReason as av, ParsingError as aw, type ParsingResult as ax, HexInvalidReason as ay, hexToAsciiString as az, type Uint16 as b, type BlockEpilogueTransactionResponse as b0, type WriteSetChange as b1, type WriteSetChangeDeleteModule as b2, type WriteSetChangeDeleteResource as b3, type WriteSetChangeDeleteTableItem as b4, type WriteSetChangeWriteModule as b5, type WriteSetChangeWriteResource as b6, type WriteSetChangeWriteTableItem as b7, type DecodedTableData as b8, type DeletedTableData as b9, type MoveUint64Type as bA, type MoveUint128Type as bB, type MoveUint256Type as bC, type MoveAddressType as bD, type MoveObjectType as bE, type MoveOptionType as bF, type MoveStructType as bG, type MoveType as bH, MoveFunctionVisibility as bI, MoveAbility as bJ, type MoveStructField as bK, type MoveStruct as bL, RoleType as bM, DeriveScheme as bN, type GenerateAccountWithEd25519 as bO, type GenerateAccountWithSingleSignerSecp256k1Key as bP, type GenerateAccount as bQ, NetworkToIndexerAPI as bR, NetworkToNodeAPI as bS, NetworkToFaucetAPI as bT, NetworkToPepperAPI as bU, NetworkToProverAPI as bV, NetworkToChainId as bW, NetworkToNetworkName as bX, type TransactionPayloadResponse as ba, type EntryFunctionPayloadResponse as bb, type ScriptPayloadResponse as bc, type MultisigPayloadResponse as bd, type GenesisPayload as be, type MoveScriptBytecode as bf, type TransactionSignature as bg, isEd25519Signature as bh, isSecp256k1Signature as bi, isMultiAgentSignature as bj, isFeePayerSignature as bk, isMultiEd25519Signature as bl, type TransactionEd25519Signature as bm, type TransactionSecp256k1Signature as bn, type TransactionMultiEd25519Signature as bo, type TransactionMultiAgentSignature as bp, type TransactionFeePayerSignature as bq, type AccountSignature as br, type WriteSet as bs, type ScriptWriteSet as bt, type DirectWriteSet as bu, type EventGuid as bv, type Event as bw, type MoveUint8Type as bx, type MoveUint16Type as by, type MoveUint32Type as bz, type Uint32 as c, type Deserializable as d, Hex as e, type AuthenticationKeyScheme as f, AccountAddress as g, type ClientConfig as h, type FaucetConfig as i, type AptosSettings as j, AnyPublicKeyVariant as k, type ScriptFunctionArgument as l, type MoveFunctionId as m, type MoveFunctionGenericTypeParam as n, type MoveStructId as o, type MoveValue as p, type AccountAddressInput as q, type MoveFunction as r, type MoveModule as s, type AccountData as t, type MoveModuleBytecode as u, type CursorPaginationArgs as v, type PaginationArgs as w, type TransactionResponse as x, type MoveResource as y, type TokenStandardArg as z };
