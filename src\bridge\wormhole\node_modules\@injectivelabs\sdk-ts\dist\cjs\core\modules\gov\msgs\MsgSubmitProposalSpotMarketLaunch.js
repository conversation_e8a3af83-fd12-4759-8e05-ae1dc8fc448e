"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const numbers_js_1 = require("../../../../utils/numbers.js");
const createSpotMarketLaunchContent = (params) => {
    const content = core_proto_ts_1.InjectiveExchangeV1Beta1Proposal.SpotMarketLaunchProposal.create();
    content.title = params.market.title;
    content.description = params.market.description;
    content.ticker = params.market.ticker;
    content.baseDenom = params.market.baseDenom;
    content.quoteDenom = params.market.quoteDenom;
    content.minPriceTickSize = params.market.minPriceTickSize;
    content.minQuantityTickSize = params.market.minQuantityTickSize;
    content.makerFeeRate = params.market.makerFeeRate;
    content.takerFeeRate = params.market.takerFeeRate;
    content.minNotional = params.market.minNotional;
    content.baseDecimals = Number(params.market.baseDecimals);
    content.quoteDecimals = Number(params.market.quoteDecimals);
    if (params.market.adminInfo) {
        const adminInfo = core_proto_ts_1.InjectiveExchangeV1Beta1Proposal.AdminInfo.create();
        adminInfo.admin = params.market.adminInfo.admin;
        adminInfo.adminPermissions = params.market.adminInfo.adminPermissions;
        content.adminInfo = adminInfo;
    }
    return core_proto_ts_1.InjectiveExchangeV1Beta1Proposal.SpotMarketLaunchProposal.fromPartial(content);
};
/**
 * @category Messages
 */
class MsgSubmitProposalSpotMarketLaunch extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgSubmitProposalSpotMarketLaunch(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            market: {
                ...initialParams.market,
                minPriceTickSize: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minPriceTickSize).toFixed(),
                minQuantityTickSize: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minQuantityTickSize).toFixed(),
                makerFeeRate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.makerFeeRate).toFixed(),
                takerFeeRate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.takerFeeRate).toFixed(),
                minNotional: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minNotional).toFixed(),
            },
        };
        const depositParams = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        depositParams.denom = params.deposit.denom;
        depositParams.amount = params.deposit.amount;
        const contentAny = core_proto_ts_1.GoogleProtobufAny.Any.create();
        contentAny.typeUrl = '/injective.exchange.v1beta1.SpotMarketLaunchProposal';
        contentAny.value =
            core_proto_ts_1.InjectiveExchangeV1Beta1Proposal.SpotMarketLaunchProposal.encode(createSpotMarketLaunchContent(params)).finish();
        const message = core_proto_ts_1.CosmosGovV1Beta1Tx.MsgSubmitProposal.create();
        message.content = contentAny;
        message.initialDeposit = [depositParams];
        message.proposer = params.proposer;
        return core_proto_ts_1.CosmosGovV1Beta1Tx.MsgSubmitProposal.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.gov.v1beta1.MsgSubmitProposal',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const content = createSpotMarketLaunchContent(params);
        const messageWithProposalType = (0, snakecase_keys_1.default)({
            content: {
                type: 'exchange/SpotMarketLaunchProposal',
                value: content,
            },
            initial_deposit: [
                {
                    denom: params.deposit.denom,
                    amount: params.deposit.amount,
                },
            ],
            proposer: params.proposer,
        });
        return {
            type: 'cosmos-sdk/MsgSubmitProposal',
            value: messageWithProposalType,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        const messageWithProposalType = {
            ...value,
            content: {
                '@type': '/injective.exchange.v1beta1.SpotMarketLaunchProposal',
                ...value.content.value,
            },
        };
        return {
            '@type': '/cosmos.gov.v1beta1.MsgSubmitProposal',
            ...messageWithProposalType,
        };
    }
    toEip712() {
        const { params } = this;
        const amino = this.toAmino();
        const { value, type } = amino;
        const messageAdjusted = {
            ...value,
            content: {
                type: 'exchange/SpotMarketLaunchProposal',
                value: {
                    ...value.content.value,
                    maker_fee_rate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.market.makerFeeRate).toFixed(),
                    taker_fee_rate: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.market.takerFeeRate).toFixed(),
                    min_price_tick_size: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.market.minPriceTickSize).toFixed(),
                    min_notional: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.market.minNotional).toFixed(),
                    min_quantity_tick_size: (0, numbers_js_1.amountToCosmosSdkDecAmount)(params.market.minQuantityTickSize).toFixed(),
                },
            },
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const content = web3gw.content;
        const messageAdjusted = {
            ...web3gw,
            content: {
                ...content,
                maker_fee_rate: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.makerFeeRate),
                taker_fee_rate: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.takerFeeRate),
                min_price_tick_size: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minPriceTickSize),
                min_notional: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minNotional),
                min_quantity_tick_size: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minQuantityTickSize),
                admin_info: content.admin_info || null,
            },
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.gov.v1beta1.MsgSubmitProposal',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosGovV1Beta1Tx.MsgSubmitProposal.encode(this.toProto()).finish();
    }
}
exports.default = MsgSubmitProposalSpotMarketLaunch;
