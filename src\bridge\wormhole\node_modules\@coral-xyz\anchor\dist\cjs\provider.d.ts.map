{"version": 3, "file": "provider.d.ts", "sourceRoot": "", "sources": ["../../src/provider.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,UAAU,EACV,MAAM,EACN,SAAS,EACT,WAAW,EACX,oBAAoB,EACpB,cAAc,EAEd,UAAU,EAEV,WAAW,EACX,oBAAoB,EAErB,MAAM,iBAAiB,CAAC;AAGzB,OAAO,EAEL,8BAA8B,EAC/B,MAAM,gBAAgB,CAAC;AAExB,MAAM,CAAC,OAAO,WAAW,QAAQ;IAC/B,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC;IAChC,QAAQ,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;IAE/B,IAAI,CAAC,CACH,EAAE,EAAE,WAAW,GAAG,oBAAoB,EACtC,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,IAAI,CAAC,EAAE,WAAW,GACjB,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACjC,cAAc,CAAC,CACb,EAAE,EAAE,WAAW,GAAG,oBAAoB,EACtC,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACjC,OAAO,CAAC,CAAC,CAAC,SAAS,WAAW,GAAG,oBAAoB,EACnD,aAAa,EAAE;QACb,EAAE,EAAE,CAAC,CAAC;QACN,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;KACpB,EAAE,EACH,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACxC,QAAQ,CAAC,CACP,EAAE,EAAE,WAAW,GAAG,oBAAoB,EACtC,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,UAAU,CAAC,EAAE,UAAU,EACvB,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,EAAE,GACtC,OAAO,CAAC,8BAA8B,CAAC,CAAC;CAC5C;AAED;;;GAGG;AACH,qBAAa,cAAe,YAAW,QAAQ;IAS3C,QAAQ,CAAC,UAAU,EAAE,UAAU;IAC/B,QAAQ,CAAC,MAAM,EAAE,MAAM;IACvB,QAAQ,CAAC,IAAI,EAAE,cAAc;IAV/B,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;IAE9B;;;;OAIG;gBAEQ,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,cAAc;IAK/B,MAAM,CAAC,cAAc,IAAI,cAAc;IAOvC;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,cAAc,GAAG,cAAc;IAcjE;;;;;OAKG;IACH,MAAM,CAAC,GAAG,IAAI,cAAc;IAkB5B;;;;;;OAMG;IACG,cAAc,CAClB,EAAE,EAAE,WAAW,GAAG,oBAAoB,EACtC,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,oBAAoB,CAAC;IAsDhC;;;;;;OAMG;IACG,OAAO,CAAC,CAAC,SAAS,WAAW,GAAG,oBAAoB,EACxD,aAAa,EAAE;QACb,EAAE,EAAE,CAAC,CAAC;QACN,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;KACpB,EAAE,EACH,IAAI,CAAC,EAAE,cAAc,GACpB,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAwEvC;;;;;;;;;OASG;IACG,QAAQ,CACZ,EAAE,EAAE,WAAW,GAAG,oBAAoB,EACtC,OAAO,CAAC,EAAE,MAAM,EAAE,EAClB,UAAU,CAAC,EAAE,UAAU,EACvB,eAAe,CAAC,EAAE,OAAO,GAAG,SAAS,EAAE,GACtC,OAAO,CAAC,8BAA8B,CAAC;CAuC3C;AAWD,MAAM,MAAM,aAAa,GAAG;IAC1B,EAAE,EAAE,WAAW,CAAC;IAChB,OAAO,EAAE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;CACpC,CAAC;AAEF;;;GAGG;AACH,MAAM,WAAW,MAAM;IACrB,eAAe,CAAC,CAAC,SAAS,WAAW,GAAG,oBAAoB,EAC1D,EAAE,EAAE,CAAC,GACJ,OAAO,CAAC,CAAC,CAAC,CAAC;IACd,mBAAmB,CAAC,CAAC,SAAS,WAAW,GAAG,oBAAoB,EAC9D,GAAG,EAAE,CAAC,EAAE,GACP,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,SAAS,EAAE,SAAS,CAAC;CACtB;AAyCD;;GAEG;AACH,wBAAgB,WAAW,CAAC,QAAQ,EAAE,QAAQ,QAE7C;AAED;;GAEG;AACH,wBAAgB,WAAW,IAAI,QAAQ,CAKtC"}