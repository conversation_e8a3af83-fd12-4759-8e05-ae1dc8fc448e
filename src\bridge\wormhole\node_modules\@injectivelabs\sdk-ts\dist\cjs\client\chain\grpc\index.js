"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcTendermintApi = exports.ChainGrpcWasmXApi = exports.ChainGrpcWasmApi = exports.ChainGrpcTokenFactoryApi = exports.ChainGrpcStakingApi = exports.ChainGrpcPermissionsApi = exports.ChainGrpcPeggyApi = exports.ChainGrpcOracleApi = exports.ChainGrpcMintApi = exports.ChainGrpcInsuranceFundApi = exports.ChainGrpcIbcApi = exports.ChainGrpcGovApi = exports.ChainGrpcExchangeApi = exports.ChainGrpcDistributionApi = exports.ChainGrpcBankApi = exports.ChainGrpcAuthZApi = exports.ChainGrpcAuthApi = exports.ChainGrpcAuctionApi = void 0;
var ChainGrpcAuctionApi_js_1 = require("./ChainGrpcAuctionApi.js");
Object.defineProperty(exports, "ChainGrpcAuctionApi", { enumerable: true, get: function () { return ChainGrpcAuctionApi_js_1.ChainGrpcAuctionApi; } });
var ChainGrpcAuthApi_js_1 = require("./ChainGrpcAuthApi.js");
Object.defineProperty(exports, "ChainGrpcAuthApi", { enumerable: true, get: function () { return ChainGrpcAuthApi_js_1.ChainGrpcAuthApi; } });
var ChainGrpcAuthZApi_js_1 = require("./ChainGrpcAuthZApi.js");
Object.defineProperty(exports, "ChainGrpcAuthZApi", { enumerable: true, get: function () { return ChainGrpcAuthZApi_js_1.ChainGrpcAuthZApi; } });
var ChainGrpcBankApi_js_1 = require("./ChainGrpcBankApi.js");
Object.defineProperty(exports, "ChainGrpcBankApi", { enumerable: true, get: function () { return ChainGrpcBankApi_js_1.ChainGrpcBankApi; } });
var ChainGrpcDistributionApi_js_1 = require("./ChainGrpcDistributionApi.js");
Object.defineProperty(exports, "ChainGrpcDistributionApi", { enumerable: true, get: function () { return ChainGrpcDistributionApi_js_1.ChainGrpcDistributionApi; } });
var ChainGrpcExchangeApi_js_1 = require("./ChainGrpcExchangeApi.js");
Object.defineProperty(exports, "ChainGrpcExchangeApi", { enumerable: true, get: function () { return ChainGrpcExchangeApi_js_1.ChainGrpcExchangeApi; } });
var ChainGrpcGovApi_js_1 = require("./ChainGrpcGovApi.js");
Object.defineProperty(exports, "ChainGrpcGovApi", { enumerable: true, get: function () { return ChainGrpcGovApi_js_1.ChainGrpcGovApi; } });
var ChainGrpcIbcApi_js_1 = require("./ChainGrpcIbcApi.js");
Object.defineProperty(exports, "ChainGrpcIbcApi", { enumerable: true, get: function () { return ChainGrpcIbcApi_js_1.ChainGrpcIbcApi; } });
var ChainGrpcInsuranceFundApi_js_1 = require("./ChainGrpcInsuranceFundApi.js");
Object.defineProperty(exports, "ChainGrpcInsuranceFundApi", { enumerable: true, get: function () { return ChainGrpcInsuranceFundApi_js_1.ChainGrpcInsuranceFundApi; } });
var ChainGrpcMintApi_js_1 = require("./ChainGrpcMintApi.js");
Object.defineProperty(exports, "ChainGrpcMintApi", { enumerable: true, get: function () { return ChainGrpcMintApi_js_1.ChainGrpcMintApi; } });
var ChainGrpcOracleApi_js_1 = require("./ChainGrpcOracleApi.js");
Object.defineProperty(exports, "ChainGrpcOracleApi", { enumerable: true, get: function () { return ChainGrpcOracleApi_js_1.ChainGrpcOracleApi; } });
var ChainGrpcPeggyApi_js_1 = require("./ChainGrpcPeggyApi.js");
Object.defineProperty(exports, "ChainGrpcPeggyApi", { enumerable: true, get: function () { return ChainGrpcPeggyApi_js_1.ChainGrpcPeggyApi; } });
var ChainGrpcPermissionsApi_js_1 = require("./ChainGrpcPermissionsApi.js");
Object.defineProperty(exports, "ChainGrpcPermissionsApi", { enumerable: true, get: function () { return ChainGrpcPermissionsApi_js_1.ChainGrpcPermissionsApi; } });
var ChainGrpcStakingApi_js_1 = require("./ChainGrpcStakingApi.js");
Object.defineProperty(exports, "ChainGrpcStakingApi", { enumerable: true, get: function () { return ChainGrpcStakingApi_js_1.ChainGrpcStakingApi; } });
var ChainGrpcTokenFactoryApi_js_1 = require("./ChainGrpcTokenFactoryApi.js");
Object.defineProperty(exports, "ChainGrpcTokenFactoryApi", { enumerable: true, get: function () { return ChainGrpcTokenFactoryApi_js_1.ChainGrpcTokenFactoryApi; } });
var ChainGrpcWasmApi_js_1 = require("./ChainGrpcWasmApi.js");
Object.defineProperty(exports, "ChainGrpcWasmApi", { enumerable: true, get: function () { return ChainGrpcWasmApi_js_1.ChainGrpcWasmApi; } });
var ChainGrpcWasmXApi_js_1 = require("./ChainGrpcWasmXApi.js");
Object.defineProperty(exports, "ChainGrpcWasmXApi", { enumerable: true, get: function () { return ChainGrpcWasmXApi_js_1.ChainGrpcWasmXApi; } });
var ChainGrpcTendermintApi_js_1 = require("./ChainGrpcTendermintApi.js");
Object.defineProperty(exports, "ChainGrpcTendermintApi", { enumerable: true, get: function () { return ChainGrpcTendermintApi_js_1.ChainGrpcTendermintApi; } });
