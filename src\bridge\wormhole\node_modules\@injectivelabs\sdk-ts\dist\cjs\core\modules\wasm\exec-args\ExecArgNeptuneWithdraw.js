"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
const utf8_js_1 = require("../../../../utils/utf8.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgNeptuneWithdraw extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgNeptuneWithdraw(params);
    }
    toData() {
        const { params } = this;
        const innerMsg = { redeem: {} };
        const encodedMsg = (0, utf8_js_1.toBase64)(innerMsg);
        return {
            amount: params.amount,
            contract: params.contract,
            msg: encodedMsg,
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('send', this.toData());
    }
}
exports.default = ExecArgNeptuneWithdraw;
