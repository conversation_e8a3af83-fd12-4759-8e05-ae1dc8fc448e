import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "tendermint.libs.bits";
export interface BitArray {
    bits: string;
    elems: string[];
}
export declare const BitArray: {
    encode(message: <PERSON><PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BitArray;
    fromJSON(object: any): BitArray;
    toJSON(message: BitArray): unknown;
    create(base?: DeepPartial<BitArray>): BitArray;
    fromPartial(object: DeepPartial<BitArray>): BitArray;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
