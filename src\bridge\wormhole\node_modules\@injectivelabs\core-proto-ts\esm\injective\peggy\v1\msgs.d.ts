import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { Any } from "../../../google/protobuf/any";
import { Params } from "./params";
import { BridgeValidator } from "./types";
export declare const protobufPackage = "injective.peggy.v1";
/**
 * MsgSetOrchestratorAddresses
 * this message allows validators to delegate their voting responsibilities
 * to a given key. This key is then used as an optional authentication method
 * for sigining oracle claims
 * VALIDATOR
 * The validator field is a cosmosvaloper1... string (i.e. sdk.ValAddress)
 * that references a validator in the active set
 * ORCHESTRATOR
 * The orchestrator field is a cosmos1... string  (i.e. sdk.AccAddress) that
 * references the key that is being delegated to
 * ETH_ADDRESS
 * This is a hex encoded 0x Ethereum public key that will be used by this
 * validator on Ethereum
 */
export interface MsgSetOrchestratorAddresses {
    sender: string;
    orchestrator: string;
    ethAddress: string;
}
export interface MsgSetOrchestratorAddressesResponse {
}
/**
 * MsgValsetConfirm
 * this is the message sent by the validators when they wish to submit their
 * signatures over the validator set at a given block height. A validator must
 * first call MsgSetEthAddress to set their Ethereum address to be used for
 * signing. Then someone (anyone) must make a ValsetRequest the request is
 * essentially a messaging mechanism to determine which block all validators
 * should submit signatures over. Finally validators sign the validator set,
 * powers, and Ethereum addresses of the entire validator set at the height of a
 * ValsetRequest and submit that signature with this message.
 *
 * If a sufficient number of validators (66% of voting power) (A) have set
 * Ethereum addresses and (B) submit ValsetConfirm messages with their
 * signatures it is then possible for anyone to view these signatures in the
 * chain store and submit them to Ethereum to update the validator set
 * -------------
 */
export interface MsgValsetConfirm {
    nonce: string;
    orchestrator: string;
    ethAddress: string;
    signature: string;
}
export interface MsgValsetConfirmResponse {
}
/**
 * MsgSendToEth
 * This is the message that a user calls when they want to bridge an asset
 * it will later be removed when it is included in a batch and successfully
 * submitted tokens are removed from the users balance immediately
 * -------------
 * AMOUNT:
 * the coin to send across the bridge, note the restriction that this is a
 * single coin not a set of coins that is normal in other Cosmos messages
 * FEE:
 * the fee paid for the bridge, distinct from the fee paid to the chain to
 * actually send this message in the first place. So a successful send has
 * two layers of fees for the user
 */
export interface MsgSendToEth {
    sender: string;
    ethDest: string;
    amount: Coin | undefined;
    bridgeFee: Coin | undefined;
}
export interface MsgSendToEthResponse {
}
/**
 * MsgRequestBatch
 * this is a message anyone can send that requests a batch of transactions to
 * send across the bridge be created for whatever block height this message is
 * included in. This acts as a coordination point, the handler for this message
 * looks at the AddToOutgoingPool tx's in the store and generates a batch, also
 * available in the store tied to this message. The validators then grab this
 * batch, sign it, submit the signatures with a MsgConfirmBatch before a relayer
 * can finally submit the batch
 * -------------
 */
export interface MsgRequestBatch {
    orchestrator: string;
    denom: string;
}
export interface MsgRequestBatchResponse {
}
/**
 * MsgConfirmBatch
 * When validators observe a MsgRequestBatch they form a batch by ordering
 * transactions currently in the txqueue in order of highest to lowest fee,
 * cutting off when the batch either reaches a hardcoded maximum size (to be
 * decided, probably around 100) or when transactions stop being profitable
 * (TODO determine this without nondeterminism) This message includes the batch
 * as well as an Ethereum signature over this batch by the validator
 * -------------
 */
export interface MsgConfirmBatch {
    nonce: string;
    tokenContract: string;
    ethSigner: string;
    orchestrator: string;
    signature: string;
}
export interface MsgConfirmBatchResponse {
}
/**
 * EthereumBridgeDepositClaim
 * When more than 66% of the active validator set has
 * claimed to have seen the deposit enter the ethereum blockchain coins are
 * issued to the Cosmos address in question
 * -------------
 */
export interface MsgDepositClaim {
    eventNonce: string;
    blockHeight: string;
    tokenContract: string;
    amount: string;
    ethereumSender: string;
    cosmosReceiver: string;
    orchestrator: string;
    data: string;
}
export interface MsgDepositClaimResponse {
}
/**
 * WithdrawClaim claims that a batch of withdrawal
 * operations on the bridge contract was executed.
 */
export interface MsgWithdrawClaim {
    eventNonce: string;
    blockHeight: string;
    batchNonce: string;
    tokenContract: string;
    orchestrator: string;
}
export interface MsgWithdrawClaimResponse {
}
/**
 * ERC20DeployedClaim allows the Cosmos module
 * to learn about an ERC20 that someone deployed
 * to represent a Cosmos asset
 */
export interface MsgERC20DeployedClaim {
    eventNonce: string;
    blockHeight: string;
    cosmosDenom: string;
    tokenContract: string;
    name: string;
    symbol: string;
    decimals: string;
    orchestrator: string;
}
export interface MsgERC20DeployedClaimResponse {
}
/**
 * This call allows the sender (and only the sender)
 * to cancel a given MsgSendToEth and recieve a refund
 * of the tokens
 */
export interface MsgCancelSendToEth {
    transactionId: string;
    sender: string;
}
export interface MsgCancelSendToEthResponse {
}
/**
 * This call allows anyone to submit evidence that a
 * validator has signed a valset, batch, or logic call that never
 * existed. Subject contains the batch, valset, or logic call.
 */
export interface MsgSubmitBadSignatureEvidence {
    subject: Any | undefined;
    signature: string;
    sender: string;
}
export interface MsgSubmitBadSignatureEvidenceResponse {
}
/**
 * This informs the Cosmos module that a validator
 * set has been updated.
 */
export interface MsgValsetUpdatedClaim {
    eventNonce: string;
    valsetNonce: string;
    blockHeight: string;
    members: BridgeValidator[];
    rewardAmount: string;
    rewardToken: string;
    orchestrator: string;
}
export interface MsgValsetUpdatedClaimResponse {
}
export interface MsgUpdateParams {
    /** authority is the address of the governance account. */
    authority: string;
    /**
     * params defines the peggy parameters to update.
     *
     * NOTE: All parameters must be supplied.
     */
    params: Params | undefined;
}
export interface MsgUpdateParamsResponse {
}
/**
 * MsgBlacklistEthereumAddresses defines the message used to add Ethereum
 * addresses to peggy blacklist.
 */
export interface MsgBlacklistEthereumAddresses {
    /** signer address */
    signer: string;
    /** Ethereum addresses to include in the blacklist */
    blacklistAddresses: string[];
}
/**
 * MsgBlacklistEthereumAddressesResponse defines the
 * MsgBlacklistEthereumAddresses response type.
 */
export interface MsgBlacklistEthereumAddressesResponse {
}
/**
 * MsgRevokeEthereumBlacklist defines the message used to remove Ethereum
 * addresses from peggy blacklist.
 */
export interface MsgRevokeEthereumBlacklist {
    /** signer address */
    signer: string;
    /** Ethereum addresses to include in the blacklist */
    blacklistAddresses: string[];
}
/**
 * MsgRevokeEthereumBlacklistResponse defines the MsgRevokeEthereumBlacklist
 * response type.
 */
export interface MsgRevokeEthereumBlacklistResponse {
}
export declare const MsgSetOrchestratorAddresses: {
    encode(message: MsgSetOrchestratorAddresses, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSetOrchestratorAddresses;
    fromJSON(object: any): MsgSetOrchestratorAddresses;
    toJSON(message: MsgSetOrchestratorAddresses): unknown;
    create(base?: DeepPartial<MsgSetOrchestratorAddresses>): MsgSetOrchestratorAddresses;
    fromPartial(object: DeepPartial<MsgSetOrchestratorAddresses>): MsgSetOrchestratorAddresses;
};
export declare const MsgSetOrchestratorAddressesResponse: {
    encode(_: MsgSetOrchestratorAddressesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSetOrchestratorAddressesResponse;
    fromJSON(_: any): MsgSetOrchestratorAddressesResponse;
    toJSON(_: MsgSetOrchestratorAddressesResponse): unknown;
    create(base?: DeepPartial<MsgSetOrchestratorAddressesResponse>): MsgSetOrchestratorAddressesResponse;
    fromPartial(_: DeepPartial<MsgSetOrchestratorAddressesResponse>): MsgSetOrchestratorAddressesResponse;
};
export declare const MsgValsetConfirm: {
    encode(message: MsgValsetConfirm, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgValsetConfirm;
    fromJSON(object: any): MsgValsetConfirm;
    toJSON(message: MsgValsetConfirm): unknown;
    create(base?: DeepPartial<MsgValsetConfirm>): MsgValsetConfirm;
    fromPartial(object: DeepPartial<MsgValsetConfirm>): MsgValsetConfirm;
};
export declare const MsgValsetConfirmResponse: {
    encode(_: MsgValsetConfirmResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgValsetConfirmResponse;
    fromJSON(_: any): MsgValsetConfirmResponse;
    toJSON(_: MsgValsetConfirmResponse): unknown;
    create(base?: DeepPartial<MsgValsetConfirmResponse>): MsgValsetConfirmResponse;
    fromPartial(_: DeepPartial<MsgValsetConfirmResponse>): MsgValsetConfirmResponse;
};
export declare const MsgSendToEth: {
    encode(message: MsgSendToEth, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSendToEth;
    fromJSON(object: any): MsgSendToEth;
    toJSON(message: MsgSendToEth): unknown;
    create(base?: DeepPartial<MsgSendToEth>): MsgSendToEth;
    fromPartial(object: DeepPartial<MsgSendToEth>): MsgSendToEth;
};
export declare const MsgSendToEthResponse: {
    encode(_: MsgSendToEthResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSendToEthResponse;
    fromJSON(_: any): MsgSendToEthResponse;
    toJSON(_: MsgSendToEthResponse): unknown;
    create(base?: DeepPartial<MsgSendToEthResponse>): MsgSendToEthResponse;
    fromPartial(_: DeepPartial<MsgSendToEthResponse>): MsgSendToEthResponse;
};
export declare const MsgRequestBatch: {
    encode(message: MsgRequestBatch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRequestBatch;
    fromJSON(object: any): MsgRequestBatch;
    toJSON(message: MsgRequestBatch): unknown;
    create(base?: DeepPartial<MsgRequestBatch>): MsgRequestBatch;
    fromPartial(object: DeepPartial<MsgRequestBatch>): MsgRequestBatch;
};
export declare const MsgRequestBatchResponse: {
    encode(_: MsgRequestBatchResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRequestBatchResponse;
    fromJSON(_: any): MsgRequestBatchResponse;
    toJSON(_: MsgRequestBatchResponse): unknown;
    create(base?: DeepPartial<MsgRequestBatchResponse>): MsgRequestBatchResponse;
    fromPartial(_: DeepPartial<MsgRequestBatchResponse>): MsgRequestBatchResponse;
};
export declare const MsgConfirmBatch: {
    encode(message: MsgConfirmBatch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgConfirmBatch;
    fromJSON(object: any): MsgConfirmBatch;
    toJSON(message: MsgConfirmBatch): unknown;
    create(base?: DeepPartial<MsgConfirmBatch>): MsgConfirmBatch;
    fromPartial(object: DeepPartial<MsgConfirmBatch>): MsgConfirmBatch;
};
export declare const MsgConfirmBatchResponse: {
    encode(_: MsgConfirmBatchResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgConfirmBatchResponse;
    fromJSON(_: any): MsgConfirmBatchResponse;
    toJSON(_: MsgConfirmBatchResponse): unknown;
    create(base?: DeepPartial<MsgConfirmBatchResponse>): MsgConfirmBatchResponse;
    fromPartial(_: DeepPartial<MsgConfirmBatchResponse>): MsgConfirmBatchResponse;
};
export declare const MsgDepositClaim: {
    encode(message: MsgDepositClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgDepositClaim;
    fromJSON(object: any): MsgDepositClaim;
    toJSON(message: MsgDepositClaim): unknown;
    create(base?: DeepPartial<MsgDepositClaim>): MsgDepositClaim;
    fromPartial(object: DeepPartial<MsgDepositClaim>): MsgDepositClaim;
};
export declare const MsgDepositClaimResponse: {
    encode(_: MsgDepositClaimResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgDepositClaimResponse;
    fromJSON(_: any): MsgDepositClaimResponse;
    toJSON(_: MsgDepositClaimResponse): unknown;
    create(base?: DeepPartial<MsgDepositClaimResponse>): MsgDepositClaimResponse;
    fromPartial(_: DeepPartial<MsgDepositClaimResponse>): MsgDepositClaimResponse;
};
export declare const MsgWithdrawClaim: {
    encode(message: MsgWithdrawClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgWithdrawClaim;
    fromJSON(object: any): MsgWithdrawClaim;
    toJSON(message: MsgWithdrawClaim): unknown;
    create(base?: DeepPartial<MsgWithdrawClaim>): MsgWithdrawClaim;
    fromPartial(object: DeepPartial<MsgWithdrawClaim>): MsgWithdrawClaim;
};
export declare const MsgWithdrawClaimResponse: {
    encode(_: MsgWithdrawClaimResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgWithdrawClaimResponse;
    fromJSON(_: any): MsgWithdrawClaimResponse;
    toJSON(_: MsgWithdrawClaimResponse): unknown;
    create(base?: DeepPartial<MsgWithdrawClaimResponse>): MsgWithdrawClaimResponse;
    fromPartial(_: DeepPartial<MsgWithdrawClaimResponse>): MsgWithdrawClaimResponse;
};
export declare const MsgERC20DeployedClaim: {
    encode(message: MsgERC20DeployedClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgERC20DeployedClaim;
    fromJSON(object: any): MsgERC20DeployedClaim;
    toJSON(message: MsgERC20DeployedClaim): unknown;
    create(base?: DeepPartial<MsgERC20DeployedClaim>): MsgERC20DeployedClaim;
    fromPartial(object: DeepPartial<MsgERC20DeployedClaim>): MsgERC20DeployedClaim;
};
export declare const MsgERC20DeployedClaimResponse: {
    encode(_: MsgERC20DeployedClaimResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgERC20DeployedClaimResponse;
    fromJSON(_: any): MsgERC20DeployedClaimResponse;
    toJSON(_: MsgERC20DeployedClaimResponse): unknown;
    create(base?: DeepPartial<MsgERC20DeployedClaimResponse>): MsgERC20DeployedClaimResponse;
    fromPartial(_: DeepPartial<MsgERC20DeployedClaimResponse>): MsgERC20DeployedClaimResponse;
};
export declare const MsgCancelSendToEth: {
    encode(message: MsgCancelSendToEth, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelSendToEth;
    fromJSON(object: any): MsgCancelSendToEth;
    toJSON(message: MsgCancelSendToEth): unknown;
    create(base?: DeepPartial<MsgCancelSendToEth>): MsgCancelSendToEth;
    fromPartial(object: DeepPartial<MsgCancelSendToEth>): MsgCancelSendToEth;
};
export declare const MsgCancelSendToEthResponse: {
    encode(_: MsgCancelSendToEthResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelSendToEthResponse;
    fromJSON(_: any): MsgCancelSendToEthResponse;
    toJSON(_: MsgCancelSendToEthResponse): unknown;
    create(base?: DeepPartial<MsgCancelSendToEthResponse>): MsgCancelSendToEthResponse;
    fromPartial(_: DeepPartial<MsgCancelSendToEthResponse>): MsgCancelSendToEthResponse;
};
export declare const MsgSubmitBadSignatureEvidence: {
    encode(message: MsgSubmitBadSignatureEvidence, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSubmitBadSignatureEvidence;
    fromJSON(object: any): MsgSubmitBadSignatureEvidence;
    toJSON(message: MsgSubmitBadSignatureEvidence): unknown;
    create(base?: DeepPartial<MsgSubmitBadSignatureEvidence>): MsgSubmitBadSignatureEvidence;
    fromPartial(object: DeepPartial<MsgSubmitBadSignatureEvidence>): MsgSubmitBadSignatureEvidence;
};
export declare const MsgSubmitBadSignatureEvidenceResponse: {
    encode(_: MsgSubmitBadSignatureEvidenceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSubmitBadSignatureEvidenceResponse;
    fromJSON(_: any): MsgSubmitBadSignatureEvidenceResponse;
    toJSON(_: MsgSubmitBadSignatureEvidenceResponse): unknown;
    create(base?: DeepPartial<MsgSubmitBadSignatureEvidenceResponse>): MsgSubmitBadSignatureEvidenceResponse;
    fromPartial(_: DeepPartial<MsgSubmitBadSignatureEvidenceResponse>): MsgSubmitBadSignatureEvidenceResponse;
};
export declare const MsgValsetUpdatedClaim: {
    encode(message: MsgValsetUpdatedClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgValsetUpdatedClaim;
    fromJSON(object: any): MsgValsetUpdatedClaim;
    toJSON(message: MsgValsetUpdatedClaim): unknown;
    create(base?: DeepPartial<MsgValsetUpdatedClaim>): MsgValsetUpdatedClaim;
    fromPartial(object: DeepPartial<MsgValsetUpdatedClaim>): MsgValsetUpdatedClaim;
};
export declare const MsgValsetUpdatedClaimResponse: {
    encode(_: MsgValsetUpdatedClaimResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgValsetUpdatedClaimResponse;
    fromJSON(_: any): MsgValsetUpdatedClaimResponse;
    toJSON(_: MsgValsetUpdatedClaimResponse): unknown;
    create(base?: DeepPartial<MsgValsetUpdatedClaimResponse>): MsgValsetUpdatedClaimResponse;
    fromPartial(_: DeepPartial<MsgValsetUpdatedClaimResponse>): MsgValsetUpdatedClaimResponse;
};
export declare const MsgUpdateParams: {
    encode(message: MsgUpdateParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParams;
    fromJSON(object: any): MsgUpdateParams;
    toJSON(message: MsgUpdateParams): unknown;
    create(base?: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
    fromPartial(object: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
};
export declare const MsgUpdateParamsResponse: {
    encode(_: MsgUpdateParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParamsResponse;
    fromJSON(_: any): MsgUpdateParamsResponse;
    toJSON(_: MsgUpdateParamsResponse): unknown;
    create(base?: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
    fromPartial(_: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
};
export declare const MsgBlacklistEthereumAddresses: {
    encode(message: MsgBlacklistEthereumAddresses, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBlacklistEthereumAddresses;
    fromJSON(object: any): MsgBlacklistEthereumAddresses;
    toJSON(message: MsgBlacklistEthereumAddresses): unknown;
    create(base?: DeepPartial<MsgBlacklistEthereumAddresses>): MsgBlacklistEthereumAddresses;
    fromPartial(object: DeepPartial<MsgBlacklistEthereumAddresses>): MsgBlacklistEthereumAddresses;
};
export declare const MsgBlacklistEthereumAddressesResponse: {
    encode(_: MsgBlacklistEthereumAddressesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBlacklistEthereumAddressesResponse;
    fromJSON(_: any): MsgBlacklistEthereumAddressesResponse;
    toJSON(_: MsgBlacklistEthereumAddressesResponse): unknown;
    create(base?: DeepPartial<MsgBlacklistEthereumAddressesResponse>): MsgBlacklistEthereumAddressesResponse;
    fromPartial(_: DeepPartial<MsgBlacklistEthereumAddressesResponse>): MsgBlacklistEthereumAddressesResponse;
};
export declare const MsgRevokeEthereumBlacklist: {
    encode(message: MsgRevokeEthereumBlacklist, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRevokeEthereumBlacklist;
    fromJSON(object: any): MsgRevokeEthereumBlacklist;
    toJSON(message: MsgRevokeEthereumBlacklist): unknown;
    create(base?: DeepPartial<MsgRevokeEthereumBlacklist>): MsgRevokeEthereumBlacklist;
    fromPartial(object: DeepPartial<MsgRevokeEthereumBlacklist>): MsgRevokeEthereumBlacklist;
};
export declare const MsgRevokeEthereumBlacklistResponse: {
    encode(_: MsgRevokeEthereumBlacklistResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRevokeEthereumBlacklistResponse;
    fromJSON(_: any): MsgRevokeEthereumBlacklistResponse;
    toJSON(_: MsgRevokeEthereumBlacklistResponse): unknown;
    create(base?: DeepPartial<MsgRevokeEthereumBlacklistResponse>): MsgRevokeEthereumBlacklistResponse;
    fromPartial(_: DeepPartial<MsgRevokeEthereumBlacklistResponse>): MsgRevokeEthereumBlacklistResponse;
};
export interface Msg {
    ValsetConfirm(request: DeepPartial<MsgValsetConfirm>, metadata?: grpc.Metadata): Promise<MsgValsetConfirmResponse>;
    SendToEth(request: DeepPartial<MsgSendToEth>, metadata?: grpc.Metadata): Promise<MsgSendToEthResponse>;
    RequestBatch(request: DeepPartial<MsgRequestBatch>, metadata?: grpc.Metadata): Promise<MsgRequestBatchResponse>;
    ConfirmBatch(request: DeepPartial<MsgConfirmBatch>, metadata?: grpc.Metadata): Promise<MsgConfirmBatchResponse>;
    DepositClaim(request: DeepPartial<MsgDepositClaim>, metadata?: grpc.Metadata): Promise<MsgDepositClaimResponse>;
    WithdrawClaim(request: DeepPartial<MsgWithdrawClaim>, metadata?: grpc.Metadata): Promise<MsgWithdrawClaimResponse>;
    ValsetUpdateClaim(request: DeepPartial<MsgValsetUpdatedClaim>, metadata?: grpc.Metadata): Promise<MsgValsetUpdatedClaimResponse>;
    ERC20DeployedClaim(request: DeepPartial<MsgERC20DeployedClaim>, metadata?: grpc.Metadata): Promise<MsgERC20DeployedClaimResponse>;
    SetOrchestratorAddresses(request: DeepPartial<MsgSetOrchestratorAddresses>, metadata?: grpc.Metadata): Promise<MsgSetOrchestratorAddressesResponse>;
    CancelSendToEth(request: DeepPartial<MsgCancelSendToEth>, metadata?: grpc.Metadata): Promise<MsgCancelSendToEthResponse>;
    SubmitBadSignatureEvidence(request: DeepPartial<MsgSubmitBadSignatureEvidence>, metadata?: grpc.Metadata): Promise<MsgSubmitBadSignatureEvidenceResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
    /** BlacklistEthereumAddresses adds Ethereum addresses to the peggy blacklist. */
    BlacklistEthereumAddresses(request: DeepPartial<MsgBlacklistEthereumAddresses>, metadata?: grpc.Metadata): Promise<MsgBlacklistEthereumAddressesResponse>;
    /**
     * RevokeEthereumBlacklist removes Ethereum addresses from the peggy
     * blacklist.
     */
    RevokeEthereumBlacklist(request: DeepPartial<MsgRevokeEthereumBlacklist>, metadata?: grpc.Metadata): Promise<MsgRevokeEthereumBlacklistResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    ValsetConfirm(request: DeepPartial<MsgValsetConfirm>, metadata?: grpc.Metadata): Promise<MsgValsetConfirmResponse>;
    SendToEth(request: DeepPartial<MsgSendToEth>, metadata?: grpc.Metadata): Promise<MsgSendToEthResponse>;
    RequestBatch(request: DeepPartial<MsgRequestBatch>, metadata?: grpc.Metadata): Promise<MsgRequestBatchResponse>;
    ConfirmBatch(request: DeepPartial<MsgConfirmBatch>, metadata?: grpc.Metadata): Promise<MsgConfirmBatchResponse>;
    DepositClaim(request: DeepPartial<MsgDepositClaim>, metadata?: grpc.Metadata): Promise<MsgDepositClaimResponse>;
    WithdrawClaim(request: DeepPartial<MsgWithdrawClaim>, metadata?: grpc.Metadata): Promise<MsgWithdrawClaimResponse>;
    ValsetUpdateClaim(request: DeepPartial<MsgValsetUpdatedClaim>, metadata?: grpc.Metadata): Promise<MsgValsetUpdatedClaimResponse>;
    ERC20DeployedClaim(request: DeepPartial<MsgERC20DeployedClaim>, metadata?: grpc.Metadata): Promise<MsgERC20DeployedClaimResponse>;
    SetOrchestratorAddresses(request: DeepPartial<MsgSetOrchestratorAddresses>, metadata?: grpc.Metadata): Promise<MsgSetOrchestratorAddressesResponse>;
    CancelSendToEth(request: DeepPartial<MsgCancelSendToEth>, metadata?: grpc.Metadata): Promise<MsgCancelSendToEthResponse>;
    SubmitBadSignatureEvidence(request: DeepPartial<MsgSubmitBadSignatureEvidence>, metadata?: grpc.Metadata): Promise<MsgSubmitBadSignatureEvidenceResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
    BlacklistEthereumAddresses(request: DeepPartial<MsgBlacklistEthereumAddresses>, metadata?: grpc.Metadata): Promise<MsgBlacklistEthereumAddressesResponse>;
    RevokeEthereumBlacklist(request: DeepPartial<MsgRevokeEthereumBlacklist>, metadata?: grpc.Metadata): Promise<MsgRevokeEthereumBlacklistResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgValsetConfirmDesc: UnaryMethodDefinitionish;
export declare const MsgSendToEthDesc: UnaryMethodDefinitionish;
export declare const MsgRequestBatchDesc: UnaryMethodDefinitionish;
export declare const MsgConfirmBatchDesc: UnaryMethodDefinitionish;
export declare const MsgDepositClaimDesc: UnaryMethodDefinitionish;
export declare const MsgWithdrawClaimDesc: UnaryMethodDefinitionish;
export declare const MsgValsetUpdateClaimDesc: UnaryMethodDefinitionish;
export declare const MsgERC20DeployedClaimDesc: UnaryMethodDefinitionish;
export declare const MsgSetOrchestratorAddressesDesc: UnaryMethodDefinitionish;
export declare const MsgCancelSendToEthDesc: UnaryMethodDefinitionish;
export declare const MsgSubmitBadSignatureEvidenceDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateParamsDesc: UnaryMethodDefinitionish;
export declare const MsgBlacklistEthereumAddressesDesc: UnaryMethodDefinitionish;
export declare const MsgRevokeEthereumBlacklistDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
