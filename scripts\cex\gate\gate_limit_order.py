#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
在Gate.io交易所创建限价订单的脚本
可以用于创建买入和卖出限价订单
"""

import sys
import os
import argparse
import time
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

try:
    from src.cex.gate.client import GateClient
    from src.cex.gate.api import prepare_limit_order, print_order_book_table
    from src.utils.logger import logger
except ImportError:
    import logging
    # 如果无法导入logger，创建一个基本的logger
    logger = logging.getLogger("gate_limit_order")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.warning("无法导入必要的模块，请确保已设置正确的PYTHONPATH")
    sys.exit(1)

# 设置默认输出目录
DEFAULT_OUTPUT_DIR = os.path.join(project_root, 'data', 'cex', 'gate_info')


def place_limit_order(symbol, side, amount, price=None, price_offset=0.0, auto_confirm=False, expected_profit=None, save=False, output_dir=DEFAULT_OUTPUT_DIR):
    """
    下限价单
    
    Args:
        symbol: 交易对，如BTC/USDT
        side: 买卖方向，'buy'或'sell'
        amount: 交易数量
        price: 交易价格，为None时使用当前市场价格
        price_offset: 价格偏移百分比，正数为高于市场价，负数为低于市场价
        auto_confirm: 是否自动确认交易
        expected_profit: 预期利润（用于自动确认时的验证）
        save: 是否保存订单数据到文件
        output_dir: 数据保存目录
    """
    try:
        client = GateClient()
        
        # 获取并显示当前订单簿（简化版）
        logger.info(f"获取{symbol}当前订单簿...")
        order_book = client.get_order_book(symbol, limit=5)
        
        # 打印输入参数
        logger.info(f"输入参数: symbol={symbol}, side={side}, amount={amount}, price={price}, price_offset={price_offset}")
        
        # 这里计算实际要交易的USDT金额（如果是数量则转换为估计的USDT金额）
        base_token, quote_token = symbol.split('/')
        if quote_token == 'USDT':
            if side == 'buy':
                # 如果是买入，amount就是USDT金额
                usdt_amount = float(amount)
            else:
                # 如果是卖出，需要估算USDT金额
                ticker = client.get_ticker(symbol)
                usdt_amount = float(amount) * float(ticker['bid'])
        else:
            logger.warning("非USDT交易对，无法精确估算USDT金额")
            usdt_amount = None
        
        # 使用新的订单簿分析功能
        print_order_book_table(order_book, side, usdt_amount)
        
        # 如果价格参数是字符串且不为None，转换为浮点数
        price_float = None if price is None else float(price)
        logger.info(f"价格转换后: {price_float}")
        
        # 准备限价单参数
        final_amount, final_price = prepare_limit_order(
            client, symbol, side, float(amount), 
            price=price_float,
            price_offset_percentage=float(price_offset)
        )
        
        # 确认最终价格不为0
        if final_price <= 0:
            raise ValueError(f"计算得到的价格无效: {final_price}，必须大于0")
        
        # 确认交易信息
        logger.info(f"\n准备{'买入' if side == 'buy' else '卖出'} {symbol}:")
        logger.info(f"数量: {final_amount}")
        logger.info(f"价格: {final_price}")
        
        # 计算交易所手续费（0.1%）
        fee_rate = 0.001
        fee_amount = final_amount * final_price * fee_rate
        logger.info(f"预估手续费: {fee_amount:.6f} USDT")
        
        # 如果有预期利润，计算实际利润（扣除手续费）
        if expected_profit is not None:
            actual_profit = expected_profit - fee_amount
            logger.info(f"预期利润: {expected_profit:.6f} USDT")
            logger.info(f"实际利润(扣除手续费): {actual_profit:.6f} USDT")
            
            # 如果实际利润小于等于0，提示并询问是否继续
            if actual_profit <= 0 and not auto_confirm:
                logger.warning("警告: 实际利润小于等于0，可能亏损")
                confirmation = input("是否仍然继续交易? [y/N]: ")
                if confirmation.lower() != 'y':
                    logger.info("交易已取消")
                    return None
            elif auto_confirm and actual_profit > 0:
                logger.info("利润满足条件，自动确认交易")
            elif auto_confirm and actual_profit <= 0:
                logger.info("实际利润不满足条件，取消交易")
                return None
        
        # 确认是否执行交易
        should_execute = auto_confirm
        if not should_execute:
            confirmation = input("确认执行此交易? [y/N]: ")
            should_execute = confirmation.lower() == 'y'
        
        if not should_execute:
            logger.info("交易已取消")
            return None
        
        # 执行交易
        logger.info("执行交易...")
        order = None
        if side == 'buy':
            order = client.create_limit_buy_order(symbol, final_amount, final_price)
        else:
            order = client.create_limit_sell_order(symbol, final_amount, final_price)
        
        logger.info("订单已提交:")
        logger.info(f"订单ID: {order.get('id', 'unknown')}")
        logger.info(f"状态: {order.get('status', 'unknown')}")
        logger.info(f"价格: {order.get('price', 0)}")
        logger.info(f"数量: {order.get('amount', 0)}")
        
        # 保存订单数据到文件
        if save:
            save_order_to_file(order, symbol, side, output_dir)
        
        # 等待订单状态更新
        logger.info("等待订单状态更新...")
        try:
            updated_order = client.wait_for_order_status(
                order['id'], symbol, 'closed', timeout=20, check_interval=2
            )
            logger.info(f"订单已完成，最终状态: {updated_order.get('status')}")
            
            # 保存更新后的订单数据
            if save:
                save_order_to_file(updated_order, symbol, side, output_dir, is_updated=True)
                
            return updated_order
        except TimeoutError:
            logger.info("订单尚未完全成交，请稍后通过订单ID查询状态")
            return order
            
    except Exception as e:
        logger.error(f"下单时出错: {e}")
        return None


def save_order_to_file(order_data, symbol, side, output_dir=DEFAULT_OUTPUT_DIR, is_updated=False):
    """
    保存订单数据到文件
    
    Args:
        order_data: 订单数据
        symbol: 交易对
        side: 交易方向
        output_dir: 输出目录
        is_updated: 是否为更新后的订单
    """
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 格式化交易对名称(去除/符号)
        symbol_formatted = symbol.replace('/', '_')
        
        # 构建输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        order_id = order_data.get('id', 'unknown')
        status_prefix = "updated_" if is_updated else ""
        output_file = os.path.join(
            output_dir, 
            f"gate_order_{status_prefix}{symbol_formatted}_{side}_{order_id}_{timestamp}.json"
        )
        
        # 添加元数据
        data = {
            'symbol': symbol,
            'side': side,
            'timestamp': int(datetime.now().timestamp()),
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'exchange': 'gate',
            'is_updated': is_updated,
            'order': order_data
        }
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"{'更新后的' if is_updated else ''}订单数据已保存到: {output_file}")
        return True
    except Exception as e:
        logger.error(f"保存订单数据时出错: {e}")
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Gate.io限价交易")
    parser.add_argument("symbol", help="交易对，例如BTC/USDT")
    parser.add_argument("side", choices=['buy', 'sell'], help="交易方向，buy或sell")
    parser.add_argument("amount", help="交易数量")
    parser.add_argument("-p", "--price", help="交易价格，不指定时使用市场价")
    parser.add_argument("-o", "--offset", type=float, default=0.0, 
                        help="价格偏移百分比，正数为高于市场价，负数为低于市场价")
    parser.add_argument("--auto-confirm", action="store_true", help="自动确认交易")
    parser.add_argument("--expected-profit", type=float, help="预期利润（USDT）")
    parser.add_argument("-s", "--save", action="store_true", help="保存订单数据到文件")
    parser.add_argument("--output-dir", default=DEFAULT_OUTPUT_DIR, help="输出目录路径")
    
    args = parser.parse_args()
    
    place_limit_order(
        args.symbol, args.side, args.amount, args.price, args.offset,
        auto_confirm=args.auto_confirm,
        expected_profit=args.expected_profit,
        save=args.save, 
        output_dir=args.output_dir
    ) 