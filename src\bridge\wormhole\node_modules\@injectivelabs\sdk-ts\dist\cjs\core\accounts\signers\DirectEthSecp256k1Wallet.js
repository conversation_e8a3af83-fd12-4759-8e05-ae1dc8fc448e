"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DirectEthSecp256k1Wallet = void 0;
const proto_signing_1 = require("@cosmjs/proto-signing");
const PrivateKey_js_1 = require("../PrivateKey.js");
const PublicKey_js_1 = require("../PublicKey.js");
class DirectEthSecp256k1Wallet {
    /**
     * Creates a DirectEthSecp256k1Wallet from the given private key
     *
     * @param privKey The private key.
     * @param prefix The bech32 address prefix (human readable part). Defaults to "inj".
     */
    static async fromKey(privKey, prefix = 'inj') {
        const publicKey = PrivateKey_js_1.PrivateKey.fromHex(Buffer.from(privKey).toString('hex'))
            .toPublicKey()
            .toPubKeyBytes();
        return new DirectEthSecp256k1Wallet(privKey, publicKey, prefix);
    }
    privateKey;
    publicKey;
    prefix;
    constructor(privKey, pubKey, prefix) {
        this.privateKey = PrivateKey_js_1.PrivateKey.fromHex(Buffer.from(privKey).toString('hex'));
        this.publicKey = PublicKey_js_1.PublicKey.fromBytes(pubKey);
        this.prefix = prefix;
    }
    get address() {
        return this.publicKey.toAddress().toBech32(this.prefix);
    }
    async getAccounts() {
        return [
            {
                algo: 'eth_secp256k1',
                address: this.address,
                pubkey: this.publicKey.toPubKeyBytes(),
            },
        ];
    }
    async signDirect(address, signDoc) {
        const signBytes = (0, proto_signing_1.makeSignBytes)(signDoc);
        if (address !== this.address) {
            throw new Error(`Address ${address} not found in wallet`);
        }
        const signature = await this.privateKey.sign(Buffer.from(signBytes));
        return {
            signed: signDoc,
            signature: {
                pub_key: {
                    type: 'tendermint/PubKeyEthSecp256k1',
                    value: this.publicKey.toBase64(),
                },
                signature: Buffer.from(signature).toString('base64'),
            },
        };
    }
}
exports.DirectEthSecp256k1Wallet = DirectEthSecp256k1Wallet;
