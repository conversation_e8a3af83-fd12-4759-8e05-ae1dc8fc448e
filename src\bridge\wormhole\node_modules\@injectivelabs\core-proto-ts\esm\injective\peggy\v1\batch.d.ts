import _m0 from "protobufjs/minimal.js";
import { ERC20Token } from "./attestation";
export declare const protobufPackage = "injective.peggy.v1";
/** OutgoingTxBatch represents a batch of transactions going from Peggy to ETH */
export interface OutgoingTxBatch {
    batchNonce: string;
    batchTimeout: string;
    transactions: OutgoingTransferTx[];
    tokenContract: string;
    block: string;
}
/** OutgoingTransferTx represents an individual send from Peggy to ETH */
export interface OutgoingTransferTx {
    id: string;
    sender: string;
    destAddress: string;
    erc20Token: ERC20Token | undefined;
    erc20Fee: ERC20Token | undefined;
}
export declare const OutgoingTxBatch: {
    encode(message: OutgoingTxBatch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OutgoingTxBatch;
    fromJSON(object: any): OutgoingTxBatch;
    toJSON(message: OutgoingTxBatch): unknown;
    create(base?: DeepPartial<OutgoingTxBatch>): OutgoingTxBatch;
    fromPartial(object: DeepPartial<OutgoingTxBatch>): OutgoingTxBatch;
};
export declare const OutgoingTransferTx: {
    encode(message: OutgoingTransferTx, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OutgoingTransferTx;
    fromJSON(object: any): OutgoingTransferTx;
    toJSON(message: OutgoingTransferTx): unknown;
    create(base?: DeepPartial<OutgoingTransferTx>): OutgoingTransferTx;
    fromPartial(object: DeepPartial<OutgoingTransferTx>): OutgoingTransferTx;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
