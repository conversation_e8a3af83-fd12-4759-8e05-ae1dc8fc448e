"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PendingPayeeship = exports.Count = exports.FeedCounts = exports.RewardPool = exports.FeedLatestAggregatorRoundIDs = exports.FeedEpochAndRound = exports.FeedTransmission = exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var ocr_1 = require("./ocr.js");
exports.protobufPackage = "injective.ocr.v1beta1";
function createBaseGenesisState() {
    return {
        params: undefined,
        feedConfigs: [],
        latestEpochAndRounds: [],
        feedTransmissions: [],
        latestAggregatorRoundIds: [],
        rewardPools: [],
        feedObservationCounts: [],
        feedTransmissionCounts: [],
        pendingPayeeships: [],
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            ocr_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _j = __values(message.feedConfigs), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                ocr_1.FeedConfig.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_a = _j.return)) _a.call(_j);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _l = __values(message.latestEpochAndRounds), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                exports.FeedEpochAndRound.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_b = _l.return)) _b.call(_l);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _o = __values(message.feedTransmissions), _p = _o.next(); !_p.done; _p = _o.next()) {
                var v = _p.value;
                exports.FeedTransmission.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_p && !_p.done && (_c = _o.return)) _c.call(_o);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _q = __values(message.latestAggregatorRoundIds), _r = _q.next(); !_r.done; _r = _q.next()) {
                var v = _r.value;
                exports.FeedLatestAggregatorRoundIDs.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_r && !_r.done && (_d = _q.return)) _d.call(_q);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _s = __values(message.rewardPools), _t = _s.next(); !_t.done; _t = _s.next()) {
                var v = _t.value;
                exports.RewardPool.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_t && !_t.done && (_e = _s.return)) _e.call(_s);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _u = __values(message.feedObservationCounts), _v = _u.next(); !_v.done; _v = _u.next()) {
                var v = _v.value;
                exports.FeedCounts.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_v && !_v.done && (_f = _u.return)) _f.call(_u);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _w = __values(message.feedTransmissionCounts), _x = _w.next(); !_x.done; _x = _w.next()) {
                var v = _x.value;
                exports.FeedCounts.encode(v, writer.uint32(66).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_x && !_x.done && (_g = _w.return)) _g.call(_w);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _y = __values(message.pendingPayeeships), _z = _y.next(); !_z.done; _z = _y.next()) {
                var v = _z.value;
                exports.PendingPayeeship.encode(v, writer.uint32(74).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_z && !_z.done && (_h = _y.return)) _h.call(_y);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = ocr_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.feedConfigs.push(ocr_1.FeedConfig.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.latestEpochAndRounds.push(exports.FeedEpochAndRound.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.feedTransmissions.push(exports.FeedTransmission.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.latestAggregatorRoundIds.push(exports.FeedLatestAggregatorRoundIDs.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.rewardPools.push(exports.RewardPool.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.feedObservationCounts.push(exports.FeedCounts.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.feedTransmissionCounts.push(exports.FeedCounts.decode(reader, reader.uint32()));
                    break;
                case 9:
                    message.pendingPayeeships.push(exports.PendingPayeeship.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? ocr_1.Params.fromJSON(object.params) : undefined,
            feedConfigs: Array.isArray(object === null || object === void 0 ? void 0 : object.feedConfigs) ? object.feedConfigs.map(function (e) { return ocr_1.FeedConfig.fromJSON(e); }) : [],
            latestEpochAndRounds: Array.isArray(object === null || object === void 0 ? void 0 : object.latestEpochAndRounds)
                ? object.latestEpochAndRounds.map(function (e) { return exports.FeedEpochAndRound.fromJSON(e); })
                : [],
            feedTransmissions: Array.isArray(object === null || object === void 0 ? void 0 : object.feedTransmissions)
                ? object.feedTransmissions.map(function (e) { return exports.FeedTransmission.fromJSON(e); })
                : [],
            latestAggregatorRoundIds: Array.isArray(object === null || object === void 0 ? void 0 : object.latestAggregatorRoundIds)
                ? object.latestAggregatorRoundIds.map(function (e) { return exports.FeedLatestAggregatorRoundIDs.fromJSON(e); })
                : [],
            rewardPools: Array.isArray(object === null || object === void 0 ? void 0 : object.rewardPools) ? object.rewardPools.map(function (e) { return exports.RewardPool.fromJSON(e); }) : [],
            feedObservationCounts: Array.isArray(object === null || object === void 0 ? void 0 : object.feedObservationCounts)
                ? object.feedObservationCounts.map(function (e) { return exports.FeedCounts.fromJSON(e); })
                : [],
            feedTransmissionCounts: Array.isArray(object === null || object === void 0 ? void 0 : object.feedTransmissionCounts)
                ? object.feedTransmissionCounts.map(function (e) { return exports.FeedCounts.fromJSON(e); })
                : [],
            pendingPayeeships: Array.isArray(object === null || object === void 0 ? void 0 : object.pendingPayeeships)
                ? object.pendingPayeeships.map(function (e) { return exports.PendingPayeeship.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? ocr_1.Params.toJSON(message.params) : undefined);
        if (message.feedConfigs) {
            obj.feedConfigs = message.feedConfigs.map(function (e) { return e ? ocr_1.FeedConfig.toJSON(e) : undefined; });
        }
        else {
            obj.feedConfigs = [];
        }
        if (message.latestEpochAndRounds) {
            obj.latestEpochAndRounds = message.latestEpochAndRounds.map(function (e) { return e ? exports.FeedEpochAndRound.toJSON(e) : undefined; });
        }
        else {
            obj.latestEpochAndRounds = [];
        }
        if (message.feedTransmissions) {
            obj.feedTransmissions = message.feedTransmissions.map(function (e) { return e ? exports.FeedTransmission.toJSON(e) : undefined; });
        }
        else {
            obj.feedTransmissions = [];
        }
        if (message.latestAggregatorRoundIds) {
            obj.latestAggregatorRoundIds = message.latestAggregatorRoundIds.map(function (e) {
                return e ? exports.FeedLatestAggregatorRoundIDs.toJSON(e) : undefined;
            });
        }
        else {
            obj.latestAggregatorRoundIds = [];
        }
        if (message.rewardPools) {
            obj.rewardPools = message.rewardPools.map(function (e) { return e ? exports.RewardPool.toJSON(e) : undefined; });
        }
        else {
            obj.rewardPools = [];
        }
        if (message.feedObservationCounts) {
            obj.feedObservationCounts = message.feedObservationCounts.map(function (e) { return e ? exports.FeedCounts.toJSON(e) : undefined; });
        }
        else {
            obj.feedObservationCounts = [];
        }
        if (message.feedTransmissionCounts) {
            obj.feedTransmissionCounts = message.feedTransmissionCounts.map(function (e) { return e ? exports.FeedCounts.toJSON(e) : undefined; });
        }
        else {
            obj.feedTransmissionCounts = [];
        }
        if (message.pendingPayeeships) {
            obj.pendingPayeeships = message.pendingPayeeships.map(function (e) { return e ? exports.PendingPayeeship.toJSON(e) : undefined; });
        }
        else {
            obj.pendingPayeeships = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? ocr_1.Params.fromPartial(object.params)
            : undefined;
        message.feedConfigs = ((_a = object.feedConfigs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return ocr_1.FeedConfig.fromPartial(e); })) || [];
        message.latestEpochAndRounds = ((_b = object.latestEpochAndRounds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.FeedEpochAndRound.fromPartial(e); })) || [];
        message.feedTransmissions = ((_c = object.feedTransmissions) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.FeedTransmission.fromPartial(e); })) || [];
        message.latestAggregatorRoundIds =
            ((_d = object.latestAggregatorRoundIds) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.FeedLatestAggregatorRoundIDs.fromPartial(e); })) || [];
        message.rewardPools = ((_e = object.rewardPools) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.RewardPool.fromPartial(e); })) || [];
        message.feedObservationCounts = ((_f = object.feedObservationCounts) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.FeedCounts.fromPartial(e); })) || [];
        message.feedTransmissionCounts = ((_g = object.feedTransmissionCounts) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.FeedCounts.fromPartial(e); })) || [];
        message.pendingPayeeships = ((_h = object.pendingPayeeships) === null || _h === void 0 ? void 0 : _h.map(function (e) { return exports.PendingPayeeship.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseFeedTransmission() {
    return { feedId: "", transmission: undefined };
}
exports.FeedTransmission = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.transmission !== undefined) {
            ocr_1.Transmission.encode(message.transmission, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeedTransmission();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.transmission = ocr_1.Transmission.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            transmission: isSet(object.transmission) ? ocr_1.Transmission.fromJSON(object.transmission) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.transmission !== undefined &&
            (obj.transmission = message.transmission ? ocr_1.Transmission.toJSON(message.transmission) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FeedTransmission.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseFeedTransmission();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.transmission = (object.transmission !== undefined && object.transmission !== null)
            ? ocr_1.Transmission.fromPartial(object.transmission)
            : undefined;
        return message;
    },
};
function createBaseFeedEpochAndRound() {
    return { feedId: "", epochAndRound: undefined };
}
exports.FeedEpochAndRound = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.epochAndRound !== undefined) {
            ocr_1.EpochAndRound.encode(message.epochAndRound, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeedEpochAndRound();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.epochAndRound = ocr_1.EpochAndRound.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            epochAndRound: isSet(object.epochAndRound) ? ocr_1.EpochAndRound.fromJSON(object.epochAndRound) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.epochAndRound !== undefined &&
            (obj.epochAndRound = message.epochAndRound ? ocr_1.EpochAndRound.toJSON(message.epochAndRound) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FeedEpochAndRound.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseFeedEpochAndRound();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.epochAndRound = (object.epochAndRound !== undefined && object.epochAndRound !== null)
            ? ocr_1.EpochAndRound.fromPartial(object.epochAndRound)
            : undefined;
        return message;
    },
};
function createBaseFeedLatestAggregatorRoundIDs() {
    return { feedId: "", aggregatorRoundId: "0" };
}
exports.FeedLatestAggregatorRoundIDs = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.aggregatorRoundId !== "0") {
            writer.uint32(16).uint64(message.aggregatorRoundId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeedLatestAggregatorRoundIDs();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.aggregatorRoundId = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            aggregatorRoundId: isSet(object.aggregatorRoundId) ? String(object.aggregatorRoundId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.aggregatorRoundId !== undefined && (obj.aggregatorRoundId = message.aggregatorRoundId);
        return obj;
    },
    create: function (base) {
        return exports.FeedLatestAggregatorRoundIDs.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseFeedLatestAggregatorRoundIDs();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.aggregatorRoundId = (_b = object.aggregatorRoundId) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseRewardPool() {
    return { feedId: "", amount: undefined };
}
exports.RewardPool = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRewardPool();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.RewardPool.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseRewardPool();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseFeedCounts() {
    return { feedId: "", counts: [] };
}
exports.FeedCounts = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        try {
            for (var _b = __values(message.counts), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.Count.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeedCounts();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.counts.push(exports.Count.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            counts: Array.isArray(object === null || object === void 0 ? void 0 : object.counts) ? object.counts.map(function (e) { return exports.Count.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        if (message.counts) {
            obj.counts = message.counts.map(function (e) { return e ? exports.Count.toJSON(e) : undefined; });
        }
        else {
            obj.counts = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.FeedCounts.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseFeedCounts();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.counts = ((_b = object.counts) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.Count.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseCount() {
    return { address: "", count: "0" };
}
exports.Count = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.count !== "0") {
            writer.uint32(16).uint64(message.count);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCount();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.count = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            count: isSet(object.count) ? String(object.count) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.count !== undefined && (obj.count = message.count);
        return obj;
    },
    create: function (base) {
        return exports.Count.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCount();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        message.count = (_b = object.count) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBasePendingPayeeship() {
    return { feedId: "", transmitter: "", proposedPayee: "" };
}
exports.PendingPayeeship = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.transmitter !== "") {
            writer.uint32(18).string(message.transmitter);
        }
        if (message.proposedPayee !== "") {
            writer.uint32(26).string(message.proposedPayee);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePendingPayeeship();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.transmitter = reader.string();
                    break;
                case 3:
                    message.proposedPayee = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            transmitter: isSet(object.transmitter) ? String(object.transmitter) : "",
            proposedPayee: isSet(object.proposedPayee) ? String(object.proposedPayee) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.transmitter !== undefined && (obj.transmitter = message.transmitter);
        message.proposedPayee !== undefined && (obj.proposedPayee = message.proposedPayee);
        return obj;
    },
    create: function (base) {
        return exports.PendingPayeeship.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBasePendingPayeeship();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.transmitter = (_b = object.transmitter) !== null && _b !== void 0 ? _b : "";
        message.proposedPayee = (_c = object.proposedPayee) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
