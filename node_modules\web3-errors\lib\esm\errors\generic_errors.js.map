{"version": 3, "file": "generic_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/generic_errors.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAEF,yCAAyC;AAEzC,OAAO,EACN,gBAAgB,EAChB,cAAc,EACd,0BAA0B,EAC1B,mBAAmB,EACnB,qBAAqB,EACrB,SAAS,EACT,6BAA6B,EAC7B,yBAAyB,GACzB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEtD,MAAM,OAAO,0BAA2B,SAAQ,aAAa;IAG5D,YAA0B,GAAW,EAAS,QAAgB,EAAS,MAAc;QACpF,KAAK,CAAC,qCAAqC,MAAM,WAAW,GAAG,eAAe,QAAQ,IAAI,CAAC,CAAC;QADnE,QAAG,GAAH,GAAG,CAAQ;QAAS,aAAQ,GAAR,QAAQ,CAAQ;QAAS,WAAM,GAAN,MAAM,CAAQ;QAF9E,SAAI,GAAG,SAAS,CAAC;IAIxB,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,MAAM,EAAE,IAAI,CAAC,MAAM,IAClB;IACH,CAAC;CACD;AAED,MAAM,OAAO,wBAAyB,SAAQ,aAAa;IAG1D,YAA0B,IAAa;QACtC,KAAK,CAAC,+BAA+B,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QADxD,SAAI,GAAJ,IAAI,CAAS;QAFhC,SAAI,GAAG,yBAAyB,CAAC;IAIxC,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,IAAI,EAAE,IAAI,CAAC,IAAI,IACd;IACH,CAAC;CACD;AAED,MAAM,OAAO,cAAe,SAAQ,aAAa;IAAjD;;QACQ,SAAI,GAAG,cAAc,CAAC;IAC9B,CAAC;CAAA;AAED,MAAM,OAAO,yBAA0B,SAAQ,aAAa;IAG3D;QACC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAHxD,SAAI,GAAG,0BAA0B,CAAC;IAIzC,CAAC;CACD;AAED,MAAM,OAAO,qBAAsB,SAAQ,aAAa;IAAxD;;QACQ,SAAI,GAAG,qBAAqB,CAAC;IACrC,CAAC;CAAA;AAED,MAAM,OAAO,mBAAoB,SAAQ,aAAa;IAAtD;;QACQ,SAAI,GAAG,mBAAmB,CAAC;IACnC,CAAC;CAAA;AAED,MAAM,OAAO,QAAS,SAAQ,aAAa;IAI1C,YAAmB,OAAe,EAAE,KAAmD;QACtF,KAAK,CAAC,OAAO,CAAC,CAAC;QAJT,SAAI,GAAG,gBAAgB,CAAC;QAK9B,IAAI,CAAC,KAAK,GAAG,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,EAAE,CAAC;IAC1B,CAAC;CACD;AAED,MAAM,OAAO,4BAA6B,SAAQ,aAAa;IAG9D,YAAmB,eAAuB;QACzC,KAAK,CAAC,gCAAgC,eAAe,+BAA+B,CAAC,CAAC;QAHhF,SAAI,GAAG,6BAA6B,CAAC;IAI5C,CAAC;CACD"}