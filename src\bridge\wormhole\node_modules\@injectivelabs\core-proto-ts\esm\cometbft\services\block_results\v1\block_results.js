/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal.js";
import { Event, ExecTxResult, ValidatorUpdate } from "../../../abci/v1/types.js";
import { ConsensusParams } from "../../../types/v1/params.js";
export const protobufPackage = "cometbft.services.block_results.v1";
function createBaseGetBlockResultsRequest() {
    return { height: "0" };
}
export const GetBlockResultsRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockResultsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create(base) {
        return GetBlockResultsRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetBlockResultsRequest();
        message.height = object.height ?? "0";
        return message;
    },
};
function createBaseGetBlockResultsResponse() {
    return {
        height: "0",
        txResults: [],
        finalizeBlockEvents: [],
        validatorUpdates: [],
        consensusParamUpdates: undefined,
        appHash: new Uint8Array(),
    };
}
export const GetBlockResultsResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        for (const v of message.txResults) {
            ExecTxResult.encode(v, writer.uint32(18).fork()).ldelim();
        }
        for (const v of message.finalizeBlockEvents) {
            Event.encode(v, writer.uint32(26).fork()).ldelim();
        }
        for (const v of message.validatorUpdates) {
            ValidatorUpdate.encode(v, writer.uint32(34).fork()).ldelim();
        }
        if (message.consensusParamUpdates !== undefined) {
            ConsensusParams.encode(message.consensusParamUpdates, writer.uint32(42).fork()).ldelim();
        }
        if (message.appHash.length !== 0) {
            writer.uint32(50).bytes(message.appHash);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetBlockResultsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                case 2:
                    message.txResults.push(ExecTxResult.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.finalizeBlockEvents.push(Event.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.validatorUpdates.push(ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.consensusParamUpdates = ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.appHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            height: isSet(object.height) ? String(object.height) : "0",
            txResults: Array.isArray(object?.txResults) ? object.txResults.map((e) => ExecTxResult.fromJSON(e)) : [],
            finalizeBlockEvents: Array.isArray(object?.finalizeBlockEvents)
                ? object.finalizeBlockEvents.map((e) => Event.fromJSON(e))
                : [],
            validatorUpdates: Array.isArray(object?.validatorUpdates)
                ? object.validatorUpdates.map((e) => ValidatorUpdate.fromJSON(e))
                : [],
            consensusParamUpdates: isSet(object.consensusParamUpdates)
                ? ConsensusParams.fromJSON(object.consensusParamUpdates)
                : undefined,
            appHash: isSet(object.appHash) ? bytesFromBase64(object.appHash) : new Uint8Array(),
        };
    },
    toJSON(message) {
        const obj = {};
        message.height !== undefined && (obj.height = message.height);
        if (message.txResults) {
            obj.txResults = message.txResults.map((e) => e ? ExecTxResult.toJSON(e) : undefined);
        }
        else {
            obj.txResults = [];
        }
        if (message.finalizeBlockEvents) {
            obj.finalizeBlockEvents = message.finalizeBlockEvents.map((e) => e ? Event.toJSON(e) : undefined);
        }
        else {
            obj.finalizeBlockEvents = [];
        }
        if (message.validatorUpdates) {
            obj.validatorUpdates = message.validatorUpdates.map((e) => e ? ValidatorUpdate.toJSON(e) : undefined);
        }
        else {
            obj.validatorUpdates = [];
        }
        message.consensusParamUpdates !== undefined && (obj.consensusParamUpdates = message.consensusParamUpdates
            ? ConsensusParams.toJSON(message.consensusParamUpdates)
            : undefined);
        message.appHash !== undefined &&
            (obj.appHash = base64FromBytes(message.appHash !== undefined ? message.appHash : new Uint8Array()));
        return obj;
    },
    create(base) {
        return GetBlockResultsResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetBlockResultsResponse();
        message.height = object.height ?? "0";
        message.txResults = object.txResults?.map((e) => ExecTxResult.fromPartial(e)) || [];
        message.finalizeBlockEvents = object.finalizeBlockEvents?.map((e) => Event.fromPartial(e)) || [];
        message.validatorUpdates = object.validatorUpdates?.map((e) => ValidatorUpdate.fromPartial(e)) || [];
        message.consensusParamUpdates =
            (object.consensusParamUpdates !== undefined && object.consensusParamUpdates !== null)
                ? ConsensusParams.fromPartial(object.consensusParamUpdates)
                : undefined;
        message.appHash = object.appHash ?? new Uint8Array();
        return message;
    },
};
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = tsProtoGlobalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
