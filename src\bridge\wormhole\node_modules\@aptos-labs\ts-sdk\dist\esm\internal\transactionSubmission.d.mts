import { AptosConfig } from '../api/aptosConfig.mjs';
import { AccountAuthenticator } from '../transactions/authenticator/account.mjs';
import { UserTransactionResponse, PendingTransactionResponse, HexInput } from '../types/types.mjs';
import { AccountAddressInput } from '../core/accountAddress.mjs';
import { SimpleTransaction } from '../transactions/instances/simpleTransaction.mjs';
import { MultiAgentTransaction } from '../transactions/instances/multiAgentTransaction.mjs';
import { InputGenerateSingleSignerRawTransactionData, InputGenerateMultiAgentRawTransactionData, InputGenerateTransactionData, AnyTransactionPayloadInstance, AnyRawTransaction, InputSimulateTransactionData, InputSubmitTransactionData, InputGenerateTransactionOptions } from '../transactions/types.mjs';
import { A as Account } from '../Ed25519Account-D9XrCLfE.mjs';
import '../utils/apiEndpoints.mjs';
import '../utils/const.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../bcs/deserializer.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../core/crypto/ed25519.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../core/crypto/privateKey.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';

/**
 * This file contains the underlying implementations for exposed submission API surface in
 * the {@link api/transaction}. By moving the methods out into a separate file,
 * other namespaces and processes can access these methods without depending on the entire
 * transaction namespace and without having a dependency cycle error.
 * @group Implementation
 */

/**
 * We are defining function signatures, each with its specific input and output.
 * These are the possible function signature for `generateTransaction` function.
 * When we call `generateTransaction` function with the relevant type properties,
 * Typescript can infer the return type based on the appropriate function overload.
 * @group Implementation
 */
declare function generateTransaction(args: {
    aptosConfig: AptosConfig;
} & InputGenerateSingleSignerRawTransactionData): Promise<SimpleTransaction>;
declare function generateTransaction(args: {
    aptosConfig: AptosConfig;
} & InputGenerateMultiAgentRawTransactionData): Promise<MultiAgentTransaction>;
/**
 * Builds a transaction payload based on the provided configuration and input data.
 * This function is essential for preparing transaction data for execution on the Aptos blockchain.
 *
 * @param args - The arguments for building the transaction payload.
 * @param args.aptosConfig - Configuration settings for the Aptos network.
 * @param args.data - Input data required to generate the transaction payload, which may include bytecode, multisig address,
 * function name, function arguments, type arguments, and ABI.
 * @returns A promise that resolves to the generated transaction payload instance.
 * @group Implementation
 */
declare function buildTransactionPayload(args: {
    aptosConfig: AptosConfig;
} & InputGenerateTransactionData): Promise<AnyTransactionPayloadInstance>;
/**
 * Builds a raw transaction based on the provided configuration and payload.
 * This function helps in creating a transaction that can be sent to the Aptos blockchain.
 *
 * @param args - The arguments for generating the transaction.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.sender - The address of the sender of the transaction.
 * @param args.options - Additional options for the transaction.
 * @param payload - The payload of the transaction, which defines the action to be performed.
 * @group Implementation
 */
declare function buildRawTransaction(args: {
    aptosConfig: AptosConfig;
} & InputGenerateTransactionData, payload: AnyTransactionPayloadInstance): Promise<AnyRawTransaction>;
/**
 * Builds a signing message that can be signed by external signers.
 *
 * Note: Please prefer using `signTransaction` unless signing outside the SDK.
 *
 * @param args - The arguments for generating the signing message.
 * @param args.transaction - AnyRawTransaction, as generated by `generateTransaction()`.
 *
 * @returns The message to be signed.
 * @group Implementation
 */
declare function getSigningMessage(args: {
    transaction: AnyRawTransaction;
}): Uint8Array;
/**
 * Sign a transaction that can later be submitted to the chain.
 *
 * @param args The arguments for signing the transaction.
 * @param args.signer The signer account to sign the transaction.
 * @param args.transaction An instance of a RawTransaction, plus optional secondary/fee payer addresses.
 *
 * @return The signer AccountAuthenticator.
 * @group Implementation
 */
declare function signTransaction(args: {
    signer: Account;
    transaction: AnyRawTransaction;
}): AccountAuthenticator;
declare function signAsFeePayer(args: {
    signer: Account;
    transaction: AnyRawTransaction;
}): AccountAuthenticator;
/**
 * Simulates a transaction before signing it to evaluate its potential outcome.
 *
 * @param args The arguments for simulating the transaction.
 * @param args.aptosConfig The configuration for the Aptos network.
 * @param args.transaction The raw transaction to simulate.
 * @param args.signerPublicKey Optional. The signer public key.
 * @param args.secondarySignersPublicKeys Optional. For when the transaction involves multiple signers.
 * @param args.feePayerPublicKey Optional. For when the transaction is sponsored by a fee payer.
 * @param args.options Optional. A configuration object to customize the simulation process.
 * @param args.options.estimateGasUnitPrice Optional. Indicates whether to estimate the gas unit price.
 * @param args.options.estimateMaxGasAmount Optional. Indicates whether to estimate the maximum gas amount.
 * @param args.options.estimatePrioritizedGasUnitPrice Optional. Indicates whether to estimate the prioritized gas unit price.
 * @group Implementation
 */
declare function simulateTransaction(args: {
    aptosConfig: AptosConfig;
} & InputSimulateTransactionData): Promise<Array<UserTransactionResponse>>;
/**
 * Submit a transaction to the Aptos blockchain.
 *
 * @param args - The arguments for submitting the transaction.
 * @param args.aptosConfig - The configuration for connecting to the Aptos network.
 * @param args.transaction - The Aptos transaction data to be submitted.
 * @param args.senderAuthenticator - The account authenticator of the transaction sender.
 * @param args.secondarySignerAuthenticators - Optional. Authenticators for additional signers in a multi-signer transaction.
 *
 * @returns PendingTransactionResponse - The response containing the status of the submitted transaction.
 * @group Implementation
 */
declare function submitTransaction(args: {
    aptosConfig: AptosConfig;
} & InputSubmitTransactionData): Promise<PendingTransactionResponse>;
type FeePayerOrFeePayerAuthenticatorOrNeither = {
    feePayer: Account;
    feePayerAuthenticator?: never;
} | {
    feePayer?: never;
    feePayerAuthenticator: AccountAuthenticator;
} | {
    feePayer?: never;
    feePayerAuthenticator?: never;
};
declare function signAndSubmitTransaction(args: FeePayerOrFeePayerAuthenticatorOrNeither & {
    aptosConfig: AptosConfig;
    signer: Account;
    transaction: AnyRawTransaction;
}): Promise<PendingTransactionResponse>;
declare function signAndSubmitAsFeePayer(args: {
    aptosConfig: AptosConfig;
    feePayer: Account;
    senderAuthenticator: AccountAuthenticator;
    transaction: AnyRawTransaction;
}): Promise<PendingTransactionResponse>;
/**
 * Publishes a package transaction to the Aptos blockchain.
 * This function allows you to create and send a transaction that publishes a package with the specified metadata and bytecode.
 *
 * @param args - The arguments for the package transaction.
 * @param args.aptosConfig - The configuration settings for the Aptos client.
 * @param args.account - The address of the account sending the transaction.
 * @param args.metadataBytes - The metadata associated with the package, represented as hexadecimal input.
 * @param args.moduleBytecode - An array of module bytecode, each represented as hexadecimal input.
 * @param args.options - Optional parameters for generating the transaction.
 * @group Implementation
 */
declare function publicPackageTransaction(args: {
    aptosConfig: AptosConfig;
    account: AccountAddressInput;
    metadataBytes: HexInput;
    moduleBytecode: Array<HexInput>;
    options?: InputGenerateTransactionOptions;
}): Promise<SimpleTransaction>;

export { type FeePayerOrFeePayerAuthenticatorOrNeither, buildRawTransaction, buildTransactionPayload, generateTransaction, getSigningMessage, publicPackageTransaction, signAndSubmitAsFeePayer, signAndSubmitTransaction, signAsFeePayer, signTransaction, simulateTransaction, submitTransaction };
