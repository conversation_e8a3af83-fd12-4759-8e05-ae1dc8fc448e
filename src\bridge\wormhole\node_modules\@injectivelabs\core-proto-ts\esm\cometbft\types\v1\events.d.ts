import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cometbft.types.v1";
/** EventDataRoundState is emitted with each new round step. */
export interface EventDataRoundState {
    height: string;
    round: number;
    step: string;
}
export declare const EventDataRoundState: {
    encode(message: EventDataRoundState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventDataRoundState;
    fromJSON(object: any): EventDataRoundState;
    toJSON(message: EventDataRoundState): unknown;
    create(base?: DeepPartial<EventDataRoundState>): EventDataRoundState;
    fromPartial(object: DeepPartial<EventDataRoundState>): EventDataRoundState;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
