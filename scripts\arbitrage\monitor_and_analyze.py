#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
套利机会监控和分析脚本
"""

import os
import sys
import time
import asyncio
import argparse
from datetime import datetime
from decimal import Decimal
from typing import Dict, Any, Tuple
import json

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.cex.gate.client import GateClient
from src.dex.astar.arthswap.client import ArthSwapClient
from src.utils.logger import logger
from analyze_opportunities import analyze_opportunities
from execute_arbitrage import execute_arbitrage
from scripts.dex.local_swap_simulator import LocalSwapSimulator
from src.dex.astar.arthswap.tx_parser import TxParser

# 套利参数设置
PRICE_DIFF_THRESHOLD_CEX_TO_DEX = 0.18  # CEX买入DEX卖出价差阈值（%）
PRICE_DIFF_THRESHOLD_DEX_TO_CEX = 0.48  # DEX买入CEX卖出价差阈值（%）
MIN_PROFIT_THRESHOLD = 0.01  # 最小利润阈值（USDT）

# 添加常量定义
ANALYSIS_LOG_DIR = "analysis_logs"  # 分析日志保存目录
CEX_FEE_RATE = 0.001  # CEX交易手续费率 0.1%

async def get_prices(symbol="ASTR/USDT") -> Tuple[Dict[str, Any], float, float]:
    """获取CEX和DEX的价格"""
    try:
        start_time = time.time()
        
        # 创建客户端实例
        cex_client = GateClient()
        dex_client = ArthSwapClient()
        
        # 获取CEX价格
        order_book = cex_client.get_order_book(symbol)
        cex_ask_price = float(order_book['asks'][0][0])  # CEX卖价
        cex_bid_price = float(order_book['bids'][0][0])  # CEX买价
        
        # 使用LocalSwapSimulator获取DEX价格
        simulator = LocalSwapSimulator("USDT", "WASTR")
        simulator_update_start = time.time()
        if not simulator.update_reserves():
            logger.error("无法更新DEX池子储备量")
            return None, 0, 0
        simulator_update_time = time.time() - simulator_update_start
        
        # 获取DEX价格（使用LocalSwapSimulator）
        simulator_price = simulator.get_price_1_per_0()  # 储备量价格 (WASTR/USDT)
        exact_price = simulator.get_exact_price(Decimal('1.0'), is_0_to_1=False)  # 精确价格（匹配Router）
        
        if simulator_price is None or exact_price is None:
            logger.error("无法从LocalSwapSimulator获取DEX价格")
            return None, 0, 0
            
        # 同时获取Router合约价格作为参考
        router_price_start = time.time()
        router_price = dex_client.get_token_price("WASTR", "USDT")
        router_price_time = time.time() - router_price_start
        
        # 转换为float用于计算
        dex_price = float(exact_price)  # 使用精确价格进行套利计算
        
        # 计算价差
        cex_buy_dex_sell_spread = (dex_price - cex_ask_price) / cex_ask_price * 100
        dex_buy_cex_sell_spread = (cex_bid_price - dex_price) / dex_price * 100
        
        # 记录详细的价格信息
        logger.info("\n=== 价格信息 ===")
        logger.info(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}")
        logger.info(f"CEX卖价(asks): {cex_ask_price:.6f} USDT")
        logger.info(f"CEX买价(bids): {cex_bid_price:.6f} USDT")
        logger.info(f"DEX储备量价格(WASTR/USDT): {float(simulator_price):.6f} USDT")
        logger.info(f"DEX精确价格(WASTR/USDT): {float(exact_price):.6f} USDT")
        logger.info(f"DEX实际Router价格(WASTR/USDT): {router_price:.6f} USDT")
        logger.info(f"价格差异(Router vs 精确): {((router_price - float(exact_price)) / float(exact_price) * 100):.6f}%")
        
        logger.info("\n=== 性能统计 ===")
        logger.info(f"Simulator更新耗时: {simulator_update_time:.3f}秒")
        logger.info(f"Router价格获取耗时: {router_price_time:.3f}秒")
        logger.info(f"总耗时: {time.time() - start_time:.3f}秒")
        
        logger.info("\n=== DEX池子信息 ===")
        logger.info(f"USDT储备: {float(simulator.reserve0):.6f}")
        logger.info(f"WASTR储备: {float(simulator.reserve1):.6f}")
        
        logger.info("\n=== 价差分析 ===")
        logger.info(f"CEX买入DEX卖出价差: {cex_buy_dex_sell_spread:.2f}% (阈值: {PRICE_DIFF_THRESHOLD_CEX_TO_DEX}%)")
        logger.info(f"DEX买入CEX卖出价差: {dex_buy_cex_sell_spread:.2f}% (阈值: {PRICE_DIFF_THRESHOLD_DEX_TO_CEX}%)")
        
        return order_book, cex_buy_dex_sell_spread, dex_buy_cex_sell_spread
    except Exception as e:
        logger.error(f"获取价格时出错: {e}")
        return None, 0, 0

async def monitor_and_execute(symbol: str, interval: int, auto_execute: bool = False):
    """
    监控并执行套利
    
    Args:
        symbol: 交易对
        interval: 监控间隔（秒）
        auto_execute: 是否自动执行套利
    """
    logger.info(f"开始监控 {symbol}，价差阈值 {PRICE_DIFF_THRESHOLD_CEX_TO_DEX}% 和 {PRICE_DIFF_THRESHOLD_DEX_TO_CEX}%")
    
    while True:
        try:
            start_time = time.time()
            
            # 1. 获取价格并检查价差
            order_book, cex_buy_dex_sell_spread, dex_buy_cex_sell_spread = await get_prices(symbol)
            if not order_book:
                await asyncio.sleep(interval)
                continue
            
            # 2. 检查是否达到价差阈值
            logger.info("\n=== 价差分析 ===")
            logger.info(f"CEX买入DEX卖出价差: {cex_buy_dex_sell_spread:.2f}% (阈值: {PRICE_DIFF_THRESHOLD_CEX_TO_DEX}%)")
            logger.info(f"DEX买入CEX卖出价差: {dex_buy_cex_sell_spread:.2f}% (阈值: {PRICE_DIFF_THRESHOLD_DEX_TO_CEX}%)")
            
            # 根据不同方向检查价差阈值，只在价差为正值时进行分析
            if cex_buy_dex_sell_spread > 0 and cex_buy_dex_sell_spread >= PRICE_DIFF_THRESHOLD_CEX_TO_DEX:
                logger.info("CEX买入DEX卖出方向达到套利阈值")
                should_analyze = True
            elif dex_buy_cex_sell_spread > 0 and dex_buy_cex_sell_spread >= PRICE_DIFF_THRESHOLD_DEX_TO_CEX:
                logger.info("DEX买入CEX卖出方向达到套利阈值")
                should_analyze = True
            else:
                should_analyze = False
                logger.info("当前无套利机会，继续监控")
                await asyncio.sleep(interval)
                continue
            
            # 3. 达到阈值，进行套利分析
            if should_analyze:
                logger.info("\n=== 开始套利分析 ===")
                result = await analyze_opportunities(symbol)
                
                if not result:
                    logger.info("套利分析未返回结果")
                    await asyncio.sleep(interval)
                    continue
                    
                if result['analysis_result']['status'] != 'success':
                    logger.info(f"套利分析结果: {result['analysis_result'].get('message', '未知原因')}")
                    await asyncio.sleep(interval)
                    continue
                
                # 4. 检查是否有足够的利润
                profit = result['analysis_result']['profit']
                logger.info("\n=== 利润分析 ===")
                logger.info(f"预期利润: {profit:.6f} USDT (最小阈值: {MIN_PROFIT_THRESHOLD} USDT)")
                
                if profit <= MIN_PROFIT_THRESHOLD:
                    logger.info(f"预期利润低于最小阈值")
                    await asyncio.sleep(interval)
                    continue
                
                # 5. 执行套利（如果启用自动执行）
                if auto_execute:
                    logger.info("\n=== 开始执行套利 ===")
                    direction = 'cex_buy_dex_sell' if result['optimal_direction'] == 'CEX买入DEX卖出' else 'dex_buy_cex_sell'
                    amount_usdt = result['analysis_result'].get('amount_usdt', 0)
                    astr_amount = result['analysis_result'].get('amount_astr', 0)
                    profit = result['analysis_result'].get('profit', 0)
                    
                    logger.info(f"交易方向: {result['optimal_direction']}")
                    logger.info(f"计划使用: {amount_usdt:.6f} USDT")
                    logger.info(f"预期交易: {astr_amount:.6f} ASTR")
                    logger.info(f"预期利润: {profit:.6f} USDT")
                    
                    if direction == 'cex_buy_dex_sell':
                        if 'cex_cost' in result['analysis_result']:
                            logger.info(f"CEX成本: {result['analysis_result']['cex_cost']:.6f} USDT")
                        if 'dex_received' in result['analysis_result']:
                            logger.info(f"DEX预期收入: {result['analysis_result']['dex_received']:.6f} USDT")
                    else:  # dex_buy_cex_sell
                        if 'dex_cost' in result['analysis_result']:
                            logger.info(f"DEX成本: {result['analysis_result']['dex_cost']:.6f} USDT")
                        if 'cex_received' in result['analysis_result']:
                            logger.info(f"CEX预期收入: {result['analysis_result']['cex_received']:.6f} USDT")
                    
                    if 'gas_cost' in result['analysis_result']:
                        logger.info(f"预估Gas成本: {result['analysis_result']['gas_cost']:.6f} USDT")
                    
                    # 执行套利
                    trade_result = await execute_arbitrage(
                        direction=direction,
                        amount_usdt=amount_usdt,
                        astr_amount=astr_amount
                    )
                    
                    # 保存分析和交易结果
                    save_analysis_result(result, trade_result)
                    
                    # 输出交易结果
                    await analyze_arbitrage_result(
                        trade_result['dex_result'].get('tx_hash', ''),
                        trade_result['cex_order'].get('id', ''),
                        profit,
                        float(trade_result['analysis_result'].get('dex_cost', 0)),
                        float(trade_result['analysis_result'].get('cex_received', 0)),
                        float(trade_result['dex_result'].get('gas_cost', 0))
                    )
                    
                    # 输出交易结果
                    logger.info("\n=== 交易执行完成，退出脚本 ===")
                    sys.exit(0)
                else:
                    # 即使不自动执行，也保存分析结果
                    save_analysis_result(result)
            
            # 等待下一次监控
            elapsed = time.time() - start_time
            if elapsed < interval:
                await asyncio.sleep(interval - elapsed)
                
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")
            await asyncio.sleep(interval)

def save_analysis_result(result_data, trade_result=None):
    """
    保存套利分析过程和结果
    
    Args:
        result_data: 分析结果数据
        trade_result: 交易执行结果（如果有）
    """
    try:
        # 创建日志目录
        if not os.path.exists(ANALYSIS_LOG_DIR):
            os.makedirs(ANALYSIS_LOG_DIR)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        direction = result_data['optimal_direction'].replace(' ', '_')
        filename = f"{ANALYSIS_LOG_DIR}/analysis_{direction}_{timestamp}.json"
        
        # 构建完整的分析数据
        analysis_data = {
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "analysis": {
                "symbol": result_data['symbol'],
                "prices": {
                    "cex_sell_price": result_data['cex_price'],
                    "cex_buy_price": result_data['cex_buy_price'],
                    "dex_price": result_data['dex_price']
                },
                "spreads": {
                    "cex_dex_spread": result_data['cex_dex_spread'],
                    "dex_cex_spread": result_data['dex_cex_spread']
                },
                "balances": {
                    "cex": result_data['cex_balances'],
                    "dex": result_data['dex_balances']
                },
                "direction": result_data['optimal_direction'],
                "analysis_result": result_data['analysis_result']
            }
        }
        
        # 如果有交易结果，添加到数据中
        if trade_result:
            analysis_data["trade_execution"] = trade_result
        
        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
            
        logger.info(f"分析结果已保存到: {filename}")
        
    except Exception as e:
        logger.error(f"保存分析结果时出错: {e}")

async def get_order_info(order_id: str) -> Dict[str, Any]:
    """获取CEX订单信息"""
    try:
        cex_client = GateClient()
        order = await cex_client.get_order(order_id)
        return order
    except Exception as e:
        logger.error(f"获取CEX订单信息失败: {e}")
        return None

async def analyze_arbitrage_result(
    dex_tx_hash: str,
    cex_order_id: str,
    expected_profit: float,
    dex_cost: float,
    cex_expected_income: float,
    estimated_gas_cost: float
) -> None:
    """分析套利结果"""
    logger.info("\n=== 交易结果 ===")
    
    # 创建交易解析器
    tx_parser = TxParser()
    
    # 解析DEX交易
    dex_tx_result = None
    if dex_tx_hash:
        dex_tx_result = await tx_parser.parse_tx(dex_tx_hash)
        
    # 获取CEX订单信息
    cex_order = None
    if cex_order_id:
        cex_order = await get_order_info(cex_order_id)
        
    # 计算实际利润
    actual_profit = 0.0
    if dex_tx_result and dex_tx_result['status'] == 'success':
        # DEX交易成功，使用实际的输入输出金额
        dex_amount_in = float(dex_tx_result['amount_in'])
        dex_amount_out = float(dex_tx_result['amount_out'])
        
        if cex_order and cex_order['status'] == 'closed':
            # CEX订单已完成，使用实际成交均价和数量
            cex_executed_amount = float(cex_order['executed'])
            cex_avg_price = float(cex_order['average'])
            
            # 判断交易方向
            if dex_tx_result['token_in'] == 'USDT':  # DEX买入CEX卖出
                # DEX买入ASTR，CEX卖出ASTR
                dex_cost = dex_amount_in  # USDT成本
                cex_income = cex_executed_amount * cex_avg_price * (1 - CEX_FEE_RATE)  # USDT收入
                actual_profit = cex_income - dex_cost - estimated_gas_cost
            else:  # CEX买入DEX卖出
                # CEX买入ASTR，DEX卖出ASTR获得USDT
                cex_cost = cex_executed_amount * cex_avg_price * (1 + CEX_FEE_RATE)  # USDT成本
                dex_income = dex_amount_out  # USDT收入
                actual_profit = dex_income - cex_cost - estimated_gas_cost
            
    # 输出结果
    logger.info("套利交易成功!" if actual_profit > 0 else "套利交易失败!")
    logger.info(f"预期利润: {expected_profit:.6f} USDT")
    logger.info(f"实际利润: {actual_profit:.6f} USDT")
    profit_diff = actual_profit - expected_profit
    profit_diff_pct = (profit_diff / expected_profit * 100) if expected_profit != 0 else float('inf')
    logger.info(f"利润差异: {'+' if profit_diff >= 0 else ''}{profit_diff:.6f} USDT ({'+' if profit_diff_pct >= 0 else ''}{profit_diff_pct:.2f}%)")
    
    # 输出详细的交易信息
    logger.info("\n=== 详细交易信息 ===")
    if dex_tx_result:
        logger.info("DEX交易详情:")
        logger.info(f"交易状态: {dex_tx_result['status']}")
        logger.info(f"交易Hash: {dex_tx_result['tx_hash']}")
        logger.info(f"输入: {dex_tx_result['amount_in']} {dex_tx_result['token_in']}")
        logger.info(f"输出: {dex_tx_result['amount_out']} {dex_tx_result['token_out']}")
        logger.info(f"Gas成本: {estimated_gas_cost:.6f} USDT")
    else:
        logger.info("未获取到DEX交易信息")
        
    if cex_order:
        logger.info("\nCEX交易详情:")
        logger.info(f"订单状态: {cex_order['status']}")
        logger.info(f"成交数量: {float(cex_order['executed']):.6f} ASTR")
        logger.info(f"成交均价: {float(cex_order['average']):.6f} USDT")
        logger.info(f"手续费率: {CEX_FEE_RATE*100:.3f}%")
        if dex_tx_result and dex_tx_result['token_in'] == 'USDT':
            logger.info(f"实际收入: {cex_income:.6f} USDT (已扣除手续费)")
        else:
            logger.info(f"实际成本: {cex_cost:.6f} USDT (已包含手续费)")
    else:
        logger.info("未获取到CEX订单信息")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='监控CEX-DEX套利机会')
    parser.add_argument('symbol', nargs='?', default='ASTR/USDT',
                      help='要监控的交易对，默认为ASTR/USDT')
    parser.add_argument('-i', '--interval', type=int, default=60,
                      help='监控间隔（秒），默认60秒')
    parser.add_argument('--auto-execute', action='store_true',
                      help='发现套利机会时自动执行')
    
    args = parser.parse_args()
    
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    try:
        logger.info(f"开始监控 {args.symbol}，间隔 {args.interval} 秒")
        if args.auto_execute:
            logger.info("已启用自动执行模式")
        
        result = asyncio.run(monitor_and_execute(
            args.symbol,
            args.interval,
            args.auto_execute
        ))
        
        return 0 if result and result['status'] == 'success' else 1
        
    except KeyboardInterrupt:
        logger.info("用户中断监控")
        return 0
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 