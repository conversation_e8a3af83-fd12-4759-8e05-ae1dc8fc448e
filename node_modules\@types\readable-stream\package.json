{"name": "@types/readable-stream", "version": "2.3.15", "description": "TypeScript definitions for readable-stream", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/readable-stream", "license": "MIT", "contributors": [{"name": "TeamworkGuy2", "url": "https://github.com/TeamworkGuy2", "githubUsername": "TeamworkGuy2"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/markdreyer", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/readable-stream"}, "scripts": {}, "dependencies": {"@types/node": "*", "safe-buffer": "~5.1.1"}, "typesPublisherContentHash": "73a801ff00aa91c2cfb03ebbdc97efaf566a78da79ac6880985abff0618c3a94", "typeScriptVersion": "4.1"}