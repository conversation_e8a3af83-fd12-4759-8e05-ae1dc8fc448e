import { ExecPrivilegedArgBase, ExecDataRepresentation } from '../ExecPrivilegedArgBase.js';
export declare namespace ExecPrivilegedArgVaultSubscribe {
    interface Params {
        origin: string;
        vaultSubaccountId: string;
        traderSubaccountId: string;
        args: Record<string, any>;
    }
    interface Data {
        vault_subaccount_id: string;
        trader_subaccount_id: string;
        msg: {
            subscribe: Record<string, any>;
        };
    }
}
/**
 * @category Contract Exec Arguments
 */
export default class ExecPrivilegedArgVaultSubscribe extends ExecPrivilegedArgBase<ExecPrivilegedArgVaultSubscribe.Params, ExecPrivilegedArgVaultSubscribe.Data> {
    static fromJSON(params: ExecPrivilegedArgVaultSubscribe.Params): ExecPrivilegedArgVaultSubscribe;
    toData(): ExecPrivilegedArgVaultSubscribe.Data;
    toExecData(): ExecDataRepresentation<ExecPrivilegedArgVaultSubscribe.Data>;
}
