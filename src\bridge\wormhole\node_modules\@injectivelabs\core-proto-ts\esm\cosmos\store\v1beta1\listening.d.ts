import _m0 from "protobufjs/minimal.js";
import { RequestFinalizeBlock, ResponseCommit, ResponseFinalizeBlock } from "../../../tendermint/abci/types";
export declare const protobufPackage = "cosmos.store.v1beta1";
/**
 * StoreKVPair is a KVStore KVPair used for listening to state changes (Sets and Deletes)
 * It optionally includes the StoreKey for the originating KVStore and a Boolean flag to distinguish between Sets and
 * Deletes
 *
 * Since: cosmos-sdk 0.43
 */
export interface StoreKVPair {
    /** the store key for the KVStore this pair originates from */
    storeKey: string;
    /** true indicates a delete operation, false indicates a set operation */
    delete: boolean;
    key: Uint8Array;
    value: Uint8Array;
}
/**
 * BlockMetadata contains all the abci event data of a block
 * the file streamer dump them into files together with the state changes.
 */
export interface BlockMetadata {
    responseCommit: ResponseCommit | undefined;
    requestFinalizeBlock: RequestFinalizeBlock | undefined;
    /** TODO: should we renumber this? */
    responseFinalizeBlock: ResponseFinalizeBlock | undefined;
}
export declare const StoreKVPair: {
    encode(message: <PERSON><PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StoreKVPair;
    fromJSON(object: any): StoreKVPair;
    toJSON(message: StoreKVPair): unknown;
    create(base?: DeepPartial<StoreKVPair>): StoreKVPair;
    fromPartial(object: DeepPartial<StoreKVPair>): StoreKVPair;
};
export declare const BlockMetadata: {
    encode(message: BlockMetadata, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BlockMetadata;
    fromJSON(object: any): BlockMetadata;
    toJSON(message: BlockMetadata): unknown;
    create(base?: DeepPartial<BlockMetadata>): BlockMetadata;
    fromPartial(object: DeepPartial<BlockMetadata>): BlockMetadata;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
