#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询Astar网络代币价格脚本
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

import argparse
from tabulate import tabulate
from src.dex.astar.arthswap.client import ArthSwapClient
from config.config import config  # 直接导入config对象


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='查询Astar网络代币价格')
    parser.add_argument('tokens', nargs='*', help='要查询的代币符号列表，例如WASTR USDT USDC')
    parser.add_argument('-b', '--base', default='USDT', help='基准代币，默认为USDT')
    parser.add_argument('-f', '--format', choices=['table', 'simple', 'plain'], default='table', 
                       help='输出格式: table=带格式表格，simple=简单表格，plain=纯文本')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细信息，包括流动性和合约地址')
    
    args = parser.parse_args()
    
    # 不需要初始化配置，直接使用已加载的config对象
    
    try:
        # 创建ArthSwap客户端
        client = ArthSwapClient()
        
        # 如果没有指定代币，默认查询常见代币
        default_tokens = ['WASTR', 'USDC', 'WETH', 'WBTC', 'WSDN']
        tokens = [t.upper() for t in args.tokens] if args.tokens else default_tokens
        base_token = args.base.upper()
        
        if base_token in tokens:
            tokens.remove(base_token)
        
        # 获取代币价格
        prices = []
        for token in tokens:
            try:
                # 获取代币价格
                price = client.get_token_price(token, base_token)
                
                # 获取流动性信息
                pair_address = client.get_pair_address(token, base_token)
                reserves = client.get_pair_reserves(token, base_token)
                
                # 获取代币详情
                token_address = client.get_token_address(token)
                token_decimals = client.get_token_decimals(token)
                base_token_address = client.get_token_address(base_token)
                base_token_decimals = client.get_token_decimals(base_token)
                
                # 添加到结果列表
                prices.append({
                    'token': token,
                    'base': base_token,
                    'price': price,
                    'token_reserve': reserves[0],
                    'base_reserve': reserves[1],
                    'pair_address': pair_address,
                    'token_address': token_address,
                    'token_decimals': token_decimals,
                    'base_address': base_token_address,
                    'base_decimals': base_token_decimals
                })
            except Exception as e:
                print(f"获取{token}/{base_token}价格失败: {str(e)}")
        
        # 显示结果
        if prices:
            # 准备表格数据
            table_data = []
            for p in prices:
                if args.verbose:
                    table_data.append([
                        p['token'],
                        f"{p['price']:.8f} {p['base']}",
                        f"{p['token_reserve']:.4f} {p['token']}",
                        f"{p['base_reserve']:.4f} {p['base']}",
                        p['pair_address'],
                        p['token_address']
                    ])
                else:
                    table_data.append([
                        p['token'],
                        f"{p['price']:.8f} {p['base']}"
                    ])
            
            # 表格头
            if args.verbose:
                headers = ['代币', '价格', f'{base_token}流动性', f'{base_token}流动性', '交易对地址', '代币地址']
            else:
                headers = ['代币', '价格']
            
            # 根据指定格式输出
            if args.format == 'table':
                print(tabulate(table_data, headers=headers, tablefmt='pretty'))
            elif args.format == 'simple':
                print(tabulate(table_data, headers=headers, tablefmt='simple'))
            elif args.format == 'plain':
                for row in table_data:
                    print(f"{row[0]}: {row[1]}")
        else:
            print(f"未找到任何{base_token}对的价格信息")
    
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 