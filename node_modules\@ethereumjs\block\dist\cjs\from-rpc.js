"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.blockFromRpc = void 0;
const tx_1 = require("@ethereumjs/tx");
const util_1 = require("@ethereumjs/util");
const header_from_rpc_js_1 = require("./header-from-rpc.js");
const index_js_1 = require("./index.js");
function normalizeTxParams(_txParams) {
    const txParams = Object.assign({}, _txParams);
    txParams.gasLimit = (0, util_1.toType)(txParams.gasLimit ?? txParams.gas, util_1.TypeOutput.BigInt);
    txParams.data = txParams.data === undefined ? txParams.input : txParams.data;
    // check and convert gasPrice and value params
    txParams.gasPrice = txParams.gasPrice !== undefined ? BigInt(txParams.gasPrice) : undefined;
    txParams.value = txParams.value !== undefined ? BigInt(txParams.value) : undefined;
    // strict byte length checking
    txParams.to =
        txParams.to !== null && txParams.to !== undefined
            ? (0, util_1.setLengthLeft)((0, util_1.toBytes)(txParams.to), 20)
            : null;
    txParams.v = (0, util_1.toType)(txParams.v, util_1.TypeOutput.BigInt);
    return txParams;
}
/**
 * Creates a new block object from Ethereum JSON RPC.
 *
 * @param blockParams - Ethereum JSON RPC of block (eth_getBlockByNumber)
 * @param uncles - Optional list of Ethereum JSON RPC of uncles (eth_getUncleByBlockHashAndIndex)
 * @param options - An object describing the blockchain
 * @deprecated
 */
function blockFromRpc(blockParams, uncles = [], options) {
    const header = (0, header_from_rpc_js_1.blockHeaderFromRpc)(blockParams, options);
    const transactions = [];
    const opts = { common: header.common };
    for (const _txParams of blockParams.transactions ?? []) {
        const txParams = normalizeTxParams(_txParams);
        const tx = tx_1.TransactionFactory.fromTxData(txParams, opts);
        transactions.push(tx);
    }
    const uncleHeaders = uncles.map((uh) => (0, header_from_rpc_js_1.blockHeaderFromRpc)(uh, options));
    const requests = blockParams.requests?.map((req) => {
        const bytes = (0, util_1.hexToBytes)(req);
        return util_1.CLRequestFactory.fromSerializedRequest(bytes);
    });
    return index_js_1.Block.fromBlockData({ header, transactions, uncleHeaders, withdrawals: blockParams.withdrawals, requests }, options);
}
exports.blockFromRpc = blockFromRpc;
//# sourceMappingURL=from-rpc.js.map