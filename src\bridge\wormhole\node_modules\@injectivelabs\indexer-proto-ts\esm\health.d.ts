import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "api.v1";
export interface GetStatusRequest {
}
export interface GetStatusResponse {
    /** Status of the response. */
    s: string;
    /** Error message. */
    errmsg: string;
    data: HealthStatus | undefined;
    status: string;
}
/** Status defines the structure for health information */
export interface HealthStatus {
    /** Block height from local mongo exchange db. */
    localHeight: number;
    /** block timestamp from local mongo exchange db. */
    localTimestamp: number;
    /** block height from Horacle service. */
    horacleHeight: number;
    /** block timestamp from Horacle service. */
    horacleTimestamp: number;
    /** Migration version of the database. */
    migrationLastVersion: number;
    /** Block height from event provider service. */
    epHeight: number;
    /** Block UNIX timestamp from event provider service. */
    epTimestamp: number;
}
export declare const GetStatusRequest: {
    encode(_: GetStatusRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetStatusRequest;
    fromJSON(_: any): GetStatusRequest;
    toJSON(_: GetStatusRequest): unknown;
    create(base?: DeepPartial<GetStatusRequest>): GetStatusRequest;
    fromPartial(_: DeepPartial<GetStatusRequest>): GetStatusRequest;
};
export declare const GetStatusResponse: {
    encode(message: GetStatusResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetStatusResponse;
    fromJSON(object: any): GetStatusResponse;
    toJSON(message: GetStatusResponse): unknown;
    create(base?: DeepPartial<GetStatusResponse>): GetStatusResponse;
    fromPartial(object: DeepPartial<GetStatusResponse>): GetStatusResponse;
};
export declare const HealthStatus: {
    encode(message: HealthStatus, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HealthStatus;
    fromJSON(object: any): HealthStatus;
    toJSON(message: HealthStatus): unknown;
    create(base?: DeepPartial<HealthStatus>): HealthStatus;
    fromPartial(object: DeepPartial<HealthStatus>): HealthStatus;
};
/** HealthAPI allows to check if backend data is up-to-date and reliable or not. */
export interface Health {
    /** Get current backend health status */
    GetStatus(request: DeepPartial<GetStatusRequest>, metadata?: grpc.Metadata): Promise<GetStatusResponse>;
}
export declare class HealthClientImpl implements Health {
    private readonly rpc;
    constructor(rpc: Rpc);
    GetStatus(request: DeepPartial<GetStatusRequest>, metadata?: grpc.Metadata): Promise<GetStatusResponse>;
}
export declare const HealthDesc: {
    serviceName: string;
};
export declare const HealthGetStatusDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
