import{a as c,b as o,c as d,d as z,e as m,f as U,g as y}from"./chunk-ORMOQWWH.mjs";import{a as b}from"./chunk-FLZPUYXQ.mjs";import{a as s}from"./chunk-EBMEXURY.mjs";import{b as u}from"./chunk-STY74NUA.mjs";var t=class i extends s{constructor(e){super(),this.values=e}serializeForEntryFunction(e){let r=this.bcsToBytes();e.serializeBytes(r)}serializeForScriptFunction(e){if(this.values[0]!==void 0&&!(this.values[0]instanceof o)){new p(this.bcsToBytes()).serializeForScriptFunction(e);return}e.serializeU32AsUleb128(4),e.serialize(this)}static U8(e){let r;if(Array.isArray(e)&&e.length===0)r=[];else if(Array.isArray(e)&&typeof e[0]=="number")r=e;else if(typeof e=="string"){let n=u.fromHexInput(e);r=Array.from(n.toUint8Array())}else if(e instanceof Uint8Array)r=Array.from(e);else throw new Error("Invalid input type, must be an number[], Uint8Array, or hex string");return new i(r.map(n=>new o(n)))}static U16(e){return new i(e.map(r=>new d(r)))}static U32(e){return new i(e.map(r=>new z(r)))}static U64(e){return new i(e.map(r=>new m(r)))}static U128(e){return new i(e.map(r=>new U(r)))}static U256(e){return new i(e.map(r=>new y(r)))}static Bool(e){return new i(e.map(r=>new c(r)))}static MoveString(e){return new i(e.map(r=>new a(r)))}serialize(e){e.serializeVector(this.values)}static deserialize(e,r){let n=e.deserializeUleb128AsU32(),l=new Array;for(let w=0;w<n;w+=1)l.push(r.deserialize(e));return new i(l)}},p=class i extends s{constructor(e){super(),this.value=u.fromHexInput(e).toUint8Array()}serialize(e){e.serializeBytes(this.value)}serializeForEntryFunction(e){this.serialize(e)}serializeForScriptFunction(e){e.serializeU32AsUleb128(9),this.serialize(e)}static deserialize(e){return new i(e.deserializeBytes())}toMoveVector(e){let r=new b(this.bcsToBytes());r.deserializeUleb128AsU32();let n=r.deserializeVector(e);return new t(n)}},a=class i extends s{constructor(e){super(),this.value=e}serialize(e){e.serializeStr(this.value)}serializeForEntryFunction(e){let r=this.bcsToBytes();e.serializeBytes(r)}serializeForScriptFunction(e){let n=new TextEncoder().encode(this.value);t.U8(n).serializeForScriptFunction(e)}static deserialize(e){return new i(e.deserializeStr())}},A=class i extends s{constructor(e){super(),typeof e<"u"&&e!==null?this.vec=new t([e]):this.vec=new t([]),[this.value]=this.vec.values}serializeForEntryFunction(e){let r=this.bcsToBytes();e.serializeBytes(r)}unwrap(){if(this.isSome())return this.vec.values[0];throw new Error("Called unwrap on a MoveOption with no value")}isSome(){return this.vec.values.length===1}serialize(e){this.vec.serialize(e)}static U8(e){return new i(e!=null?new o(e):void 0)}static U16(e){return new i(e!=null?new d(e):void 0)}static U32(e){return new i(e!=null?new z(e):void 0)}static U64(e){return new i(e!=null?new m(e):void 0)}static U128(e){return new i(e!=null?new U(e):void 0)}static U256(e){return new i(e!=null?new y(e):void 0)}static Bool(e){return new i(e!=null?new c(e):void 0)}static MoveString(e){return new i(e!=null?new a(e):void 0)}static deserialize(e,r){let n=t.deserialize(e,r);return new i(n.values[0])}};export{t as a,p as b,a as c,A as d};
//# sourceMappingURL=chunk-Q4W3WJ2U.mjs.map