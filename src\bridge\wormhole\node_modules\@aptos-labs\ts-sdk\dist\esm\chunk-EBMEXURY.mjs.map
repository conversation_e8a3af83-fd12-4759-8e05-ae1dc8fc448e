{"version": 3, "sources": ["../../src/bcs/serializer.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/* eslint-disable no-bitwise */\nimport {\n  MAX_U128_BIG_INT,\n  MAX_U16_NUMBER,\n  MAX_U32_NUMBER,\n  MAX_U64_BIG_INT,\n  MAX_U8_NUMBER,\n  MAX_U256_BIG_INT,\n} from \"./consts\";\nimport { Hex } from \"../core/hex\";\nimport { AnyNumber, Uint16, Uint32, Uint8 } from \"../types\";\n\n/**\n * This class serves as a base class for all serializable types. It facilitates\n * composable serialization of complex types and enables the serialization of\n * instances to their BCS (Binary Canonical Serialization) representation.\n * @group Implementation\n * @category BCS\n */\nexport abstract class Serializable {\n  abstract serialize(serializer: Serializer): void;\n\n  /**\n   * Serializes a `Serializable` value to its BCS representation.\n   * This function is the TypeScript SDK equivalent of `bcs::to_bytes` in Move.\n   * @returns the BCS representation of the Serializable instance as a byte buffer.\n   * @group Implementation\n   * @category BCS\n   */\n  bcsToBytes(): Uint8Array {\n    const serializer = new Serializer();\n    this.serialize(serializer);\n    return serializer.toUint8Array();\n  }\n\n  /**\n   * Converts the BCS-serialized bytes of a value into a Hex instance.\n   * This function provides a Hex representation of the BCS-serialized data for easier handling and manipulation.\n   * @returns A Hex instance with the BCS-serialized bytes loaded into its underlying Uint8Array.\n   * @group Implementation\n   * @category BCS\n   */\n  bcsToHex(): Hex {\n    const bcsBytes = this.bcsToBytes();\n    return Hex.fromHexInput(bcsBytes);\n  }\n\n  /**\n   * Returns the hex string representation of the `Serializable` value without the 0x prefix.\n   * @returns the hex format as a string without `0x` prefix.\n   */\n  toStringWithoutPrefix(): string {\n    return this.bcsToHex().toStringWithoutPrefix();\n  }\n\n  /**\n   * Returns the hex string representation of the `Serializable` value with the 0x prefix.\n   * @returns the hex formatas a string prefixed by `0x`.\n   */\n  toString(): string {\n    return `0x${this.toStringWithoutPrefix()}`;\n  }\n}\n\n/**\n * A class for serializing various data types into a binary format.\n * It provides methods to serialize strings, bytes, numbers, and other serializable objects\n * using the Binary Coded Serialization (BCS) layout. The serialized data can be retrieved as a\n * Uint8Array.\n * @group Implementation\n * @category BCS\n */\nexport class Serializer {\n  private buffer: ArrayBuffer;\n\n  private offset: number;\n\n  /**\n   * Constructs a serializer with a buffer of size `length` bytes, 64 bytes by default.\n   * The `length` must be greater than 0.\n   *\n   * @param length - The size of the buffer in bytes.\n   * @group Implementation\n   * @category BCS\n   */\n  constructor(length: number = 64) {\n    if (length <= 0) {\n      throw new Error(\"Length needs to be greater than 0\");\n    }\n    this.buffer = new ArrayBuffer(length);\n    this.offset = 0;\n  }\n\n  /**\n   * Ensures that the internal buffer can accommodate the specified number of bytes.\n   * This function dynamically resizes the buffer if the current size is insufficient.\n   *\n   * @param bytes - The number of bytes to ensure the buffer can handle.\n   * @group Implementation\n   * @category BCS\n   */\n  private ensureBufferWillHandleSize(bytes: number) {\n    while (this.buffer.byteLength < this.offset + bytes) {\n      const newBuffer = new ArrayBuffer(this.buffer.byteLength * 2);\n      new Uint8Array(newBuffer).set(new Uint8Array(this.buffer));\n      this.buffer = newBuffer;\n    }\n  }\n\n  /**\n   * Appends the specified values to the buffer, ensuring that the buffer can accommodate the new data.\n   *\n   * @param {Uint8Array} values - The values to be appended to the buffer.\n   * @group Implementation\n   * @category BCS\n   */\n  protected appendToBuffer(values: Uint8Array) {\n    this.ensureBufferWillHandleSize(values.length);\n    new Uint8Array(this.buffer, this.offset).set(values);\n    this.offset += values.length;\n  }\n\n  /**\n   * Serializes a value into the buffer using the provided function, ensuring the buffer can accommodate the size.\n   *\n   * @param fn - The function to serialize the value, which takes a byte offset, the value to serialize, and an optional little-endian flag.\n   * @param fn.byteOffset - The byte offset at which to write the value.\n   * @param fn.value - The numeric value to serialize into the buffer.\n   * @param fn.littleEndian - Optional flag indicating whether to use little-endian byte order (defaults to true).\n   * @group Implementation\n   * @category BCS\n   */\n  // TODO: JSDoc bytesLength and value\n  private serializeWithFunction(\n    fn: (byteOffset: number, value: number, littleEndian?: boolean) => void,\n    bytesLength: number,\n    value: number,\n  ) {\n    this.ensureBufferWillHandleSize(bytesLength);\n    const dv = new DataView(this.buffer, this.offset);\n    fn.apply(dv, [0, value, true]);\n    this.offset += bytesLength;\n  }\n\n  /**\n   * Serializes a string. UTF8 string is supported.\n   * The number of bytes in the string content is serialized first, as a uleb128-encoded u32 integer.\n   * Then the string content is serialized as UTF8 encoded bytes.\n   *\n   * BCS layout for \"string\": string_length | string_content\n   * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.\n   *\n   * @param value - The string to serialize.\n   *\n   * @example\n   * ```typescript\n   * const serializer = new Serializer();\n   * serializer.serializeStr(\"1234abcd\");\n   * assert(serializer.toUint8Array() === new Uint8Array([8, 49, 50, 51, 52, 97, 98, 99, 100]));\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  serializeStr(value: string) {\n    const textEncoder = new TextEncoder();\n    this.serializeBytes(textEncoder.encode(value));\n  }\n\n  /**\n   * Serializes an array of bytes.\n   *\n   * This function encodes the length of the byte array as a u32 integer in uleb128 format, followed by the byte array itself.\n   * BCS layout for \"bytes\": bytes_length | bytes\n   * where bytes_length is a u32 integer encoded as a uleb128 integer, equal to the length of the bytes array.\n   * @param value - The byte array to serialize.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeBytes(value: Uint8Array) {\n    this.serializeU32AsUleb128(value.length);\n    this.appendToBuffer(value);\n  }\n\n  /**\n   * Serializes an array of bytes with a known length, allowing for efficient deserialization without needing to serialize the\n   * length itself.\n   * When deserializing, the number of bytes to deserialize needs to be passed in.\n\n   * @param value - The Uint8Array to be serialized.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeFixedBytes(value: Uint8Array) {\n    this.appendToBuffer(value);\n  }\n\n  /**\n   * Serializes a boolean value into a byte representation.\n   *\n   * The BCS layout for a boolean uses one byte, where \"0x01\" represents true and \"0x00\" represents false.\n   *\n   * @param value - The boolean value to serialize.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeBool(value: boolean) {\n    /**\n     * Ensures that the provided value is a boolean.\n     * This function throws an error if the value is not a boolean, helping to enforce type safety in your code.\n     *\n     * @param value - The value to be checked for boolean type.\n     * @throws {Error} Throws an error if the value is not a boolean.\n     * @group Implementation\n     * @category BCS\n     */\n    ensureBoolean(value);\n    const byteValue = value ? 1 : 0;\n    this.appendToBuffer(new Uint8Array([byteValue]));\n  }\n\n  /**\n   * Serializes a Uint8 value and appends it to the buffer.\n   * BCS layout for \"uint8\": One byte. Binary format in little-endian representation.\n   *\n   * @param value - The Uint8 value to serialize.\n   * @group Implementation\n   * @category BCS\n   */\n  @checkNumberRange(0, MAX_U8_NUMBER)\n  serializeU8(value: Uint8) {\n    this.appendToBuffer(new Uint8Array([value]));\n  }\n\n  /**\n   * Serializes a uint16 number.\n   *\n   * @group Implementation\n   * @category BCS\n\n   */\n\n  /**\n   * Serializes a 16-bit unsigned integer value into a binary format.\n   * BCS layout for \"uint16\": Two bytes. Binary format in little-endian representation.\n   *\n   * @param value - The 16-bit unsigned integer value to serialize.\n   * @example\n   * ```typescript\n   * const serializer = new Serializer();\n   * serializer.serializeU16(4660);\n   * assert(serializer.toUint8Array() === new Uint8Array([0x34, 0x12]));\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  @checkNumberRange(0, MAX_U16_NUMBER)\n  serializeU16(value: Uint16) {\n    this.serializeWithFunction(DataView.prototype.setUint16, 2, value);\n  }\n\n  /**\n   * Serializes a 32-bit unsigned integer value into a binary format.\n   * This function is useful for encoding data that needs to be stored or transmitted in a compact form.\n   * @example\n   * ```typescript\n   * const serializer = new Serializer();\n   * serializer.serializeU32(305419896);\n   * assert(serializer.toUint8Array() === new Uint8Array([0x78, 0x56, 0x34, 0x12]));\n   * ```\n   * @param value - The 32-bit unsigned integer value to serialize.\n   * @group Implementation\n   * @category BCS\n   */\n  @checkNumberRange(0, MAX_U32_NUMBER)\n  serializeU32(value: Uint32) {\n    this.serializeWithFunction(DataView.prototype.setUint32, 4, value);\n  }\n\n  /**\n   * Serializes a 64-bit unsigned integer into a format suitable for storage or transmission.\n   * This function breaks down the value into two 32-bit components and writes them in little-endian order.\n   *\n   * @param value - The 64-bit unsigned integer to serialize, represented as a number.\n   * @example\n   * ```ts\n   * const serializer = new Serializer();\n   * serializer.serializeU64(1311768467750121216);\n   * assert(serializer.toUint8Array() === new Uint8Array([0x00, 0xEF, 0xCD, 0xAB, 0x78, 0x56, 0x34, 0x12]));\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  @checkNumberRange(BigInt(0), MAX_U64_BIG_INT)\n  serializeU64(value: AnyNumber) {\n    const low = BigInt(value) & BigInt(MAX_U32_NUMBER);\n    const high = BigInt(value) >> BigInt(32);\n\n    // write little endian number\n    this.serializeU32(Number(low));\n    this.serializeU32(Number(high));\n  }\n\n  /**\n   * Serializes a U128 value into a format suitable for storage or transmission.\n   *\n   * @param value - The U128 value to serialize, represented as a number.\n   * @group Implementation\n   * @category BCS\n   */\n  @checkNumberRange(BigInt(0), MAX_U128_BIG_INT)\n  serializeU128(value: AnyNumber) {\n    const low = BigInt(value) & MAX_U64_BIG_INT;\n    const high = BigInt(value) >> BigInt(64);\n\n    // write little endian number\n    this.serializeU64(low);\n    this.serializeU64(high);\n  }\n\n  /**\n   * Serializes a U256 value into a byte representation.\n   * This function is essential for encoding large numbers in a compact format suitable for transmission or storage.\n   *\n   * @param value - The U256 value to serialize, represented as an AnyNumber.\n   * @group Implementation\n   * @category BCS\n   */\n  @checkNumberRange(BigInt(0), MAX_U256_BIG_INT)\n  serializeU256(value: AnyNumber) {\n    const low = BigInt(value) & MAX_U128_BIG_INT;\n    const high = BigInt(value) >> BigInt(128);\n\n    // write little endian number\n    this.serializeU128(low);\n    this.serializeU128(high);\n  }\n\n  /**\n   * Serializes a 32-bit unsigned integer as a variable-length ULEB128 encoded byte array.\n   * BCS uses uleb128 encoding in two cases: (1) lengths of variable-length sequences and (2) tags of enum values\n   *\n   * @param val - The 32-bit unsigned integer value to be serialized.\n   * @group Implementation\n   * @category BCS\n   */\n  @checkNumberRange(0, MAX_U32_NUMBER)\n  serializeU32AsUleb128(val: Uint32) {\n    let value = val;\n    const valueArray = [];\n    while (value >>> 7 !== 0) {\n      valueArray.push((value & 0x7f) | 0x80);\n      value >>>= 7;\n    }\n    valueArray.push(value);\n    this.appendToBuffer(new Uint8Array(valueArray));\n  }\n\n  /**\n   * Returns the buffered bytes as a Uint8Array.\n   *\n   * This function allows you to retrieve the byte representation of the buffer up to the current offset.\n   *\n   * @returns Uint8Array - The byte array representation of the buffer.\n   * @group Implementation\n   * @category BCS\n   */\n  toUint8Array(): Uint8Array {\n    return new Uint8Array(this.buffer).slice(0, this.offset);\n  }\n\n  /**\n   * Serializes a `Serializable` value, facilitating composable serialization.\n   *\n   * @param value The Serializable value to serialize.\n   *\n   * @returns the serializer instance\n   * @group Implementation\n   * @category BCS\n   */\n  serialize<T extends Serializable>(value: T): void {\n    // NOTE: The `serialize` method called by `value` is defined in `value`'s\n    // Serializable interface, not the one defined in this class.\n    value.serialize(this);\n  }\n\n  /**\n   * Serializes an array of BCS Serializable values to a serializer instance.\n   * The bytes are added to the serializer instance's byte buffer.\n   *\n   * @param values The array of BCS Serializable values\n   * @example\n   * const addresses = new Array<AccountAddress>(\n   *   AccountAddress.from(\"0x1\"),\n   *   AccountAddress.from(\"0x2\"),\n   *   AccountAddress.from(\"0xa\"),\n   *   AccountAddress.from(\"0xb\"),\n   * );\n   * const serializer = new Serializer();\n   * serializer.serializeVector(addresses);\n   * const serializedBytes = serializer.toUint8Array();\n   * // serializedBytes is now the BCS-serialized bytes\n   * // The equivalent value in Move would be:\n   * // `bcs::to_bytes(&vector<address> [@0x1, @0x2, @0xa, @0xb])`;\n   * @group Implementation\n   * @category BCS\n   */\n  serializeVector<T extends Serializable>(values: Array<T>): void {\n    this.serializeU32AsUleb128(values.length);\n    values.forEach((item) => {\n      item.serialize(this);\n    });\n  }\n\n  /**\n   * Serializes an optional value which can be a Serializable, string, or Uint8Array.\n   * For strings and Uint8Arrays, it uses the appropriate serialization method.\n   *\n   * @param value The value to serialize (Serializable, string, Uint8Array, or undefined)\n   * @param len Optional fixed length for Uint8Array serialization. If provided, uses serializeFixedBytes instead of serializeBytes\n   *\n   * @example\n   * ```typescript\n   * const serializer = new Serializer();\n   * serializer.serializeOption(\"hello\");  // Serializes optional string\n   * serializer.serializeOption(new Uint8Array([1, 2, 3]));  // Serializes optional bytes\n   * serializer.serializeOption(new Uint8Array([1, 2, 3]), 3);  // Serializes optional fixed-length bytes\n   * serializer.serializeOption(new AccountAddress(...));  // Serializes optional Serializable\n   * serializer.serializeOption(undefined);  // Serializes none case\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  serializeOption<T extends Serializable | string | Uint8Array>(value?: T, len?: number): void {\n    const hasValue = value !== undefined;\n    this.serializeBool(hasValue);\n    if (hasValue) {\n      if (typeof value === \"string\") {\n        this.serializeStr(value);\n      } else if (value instanceof Uint8Array) {\n        if (len !== undefined) {\n          this.serializeFixedBytes(value);\n        } else {\n          this.serializeBytes(value);\n        }\n      } else {\n        value.serialize(this);\n      }\n    }\n  }\n\n  /**\n   * @deprecated use `serializeOption` instead.\n   * Serializes an optional string, supporting UTF8 encoding.\n   * The function encodes the existence of the string first, followed by the length and content if it exists.\n   *\n   * BCS layout for optional \"string\": 1 | string_length | string_content\n   * where string_length is a u32 integer encoded as a uleb128 integer, equal to the number of bytes in string_content.\n   * BCS layout for undefined: 0\n   *\n   * @param value - The optional string to serialize. If undefined, it will serialize as 0.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeOptionStr(value?: string): void {\n    if (value === undefined) {\n      this.serializeU32AsUleb128(0);\n    } else {\n      this.serializeU32AsUleb128(1);\n      this.serializeStr(value);\n    }\n  }\n}\n\n/**\n * @group Implementation\n * @category BCS\n */\nexport function ensureBoolean(value: unknown): asserts value is boolean {\n  if (typeof value !== \"boolean\") {\n    throw new Error(`${value} is not a boolean value`);\n  }\n}\n/**\n * @group Implementation\n * @category BCS\n */\nexport const outOfRangeErrorMessage = (value: AnyNumber, min: AnyNumber, max: AnyNumber) =>\n  `${value} is out of range: [${min}, ${max}]`;\n\n/**\n * Validates that a given number is within a specified range.\n * This function throws an error if the value is outside the defined minimum and maximum bounds.\n *\n * @param value - The number to validate.\n * @param minValue - The minimum allowable value (inclusive).\n * @param maxValue - The maximum allowable value (inclusive).\n * @group Implementation\n * @category BCS\n */\nexport function validateNumberInRange<T extends AnyNumber>(value: T, minValue: T, maxValue: T) {\n  const valueBigInt = BigInt(value);\n  if (valueBigInt > BigInt(maxValue) || valueBigInt < BigInt(minValue)) {\n    throw new Error(outOfRangeErrorMessage(value, minValue, maxValue));\n  }\n}\n\n/**\n * A decorator that validates that the input argument for a function is within a specified range.\n * This ensures that the function is only called with valid input values, preventing potential errors.\n *\n * @param minValue - The input argument must be greater than or equal to this value.\n * @param maxValue - The input argument must be less than or equal to this value.\n * @group Implementation\n * @category BCS\n */\nfunction checkNumberRange<T extends AnyNumber>(minValue: T, maxValue: T) {\n  return (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) => {\n    const childFunction = descriptor.value;\n    // eslint-disable-next-line no-param-reassign\n    descriptor.value = function deco(value: AnyNumber) {\n      validateNumberInRange(value, minValue, maxValue);\n      return childFunction.apply(this, [value]);\n    };\n\n    return descriptor;\n  };\n}\n"], "mappings": "yJAsBO,IAAeA,EAAf,KAA4B,CAUjC,YAAyB,CACvB,IAAMC,EAAa,IAAIC,EACvB,YAAK,UAAUD,CAAU,EAClBA,EAAW,aAAa,CACjC,CASA,UAAgB,CACd,IAAME,EAAW,KAAK,WAAW,EACjC,OAAOC,EAAI,aAAaD,CAAQ,CAClC,CAMA,uBAAgC,CAC9B,OAAO,KAAK,SAAS,EAAE,sBAAsB,CAC/C,CAMA,UAAmB,CACjB,MAAO,KAAK,KAAK,sBAAsB,CAAC,EAC1C,CACF,EAUaD,EAAN,KAAiB,CAatB,YAAYG,EAAiB,GAAI,CAC/B,GAAIA,GAAU,EACZ,MAAM,IAAI,MAAM,mCAAmC,EAErD,KAAK,OAAS,IAAI,YAAYA,CAAM,EACpC,KAAK,OAAS,CAChB,CAUQ,2BAA2BC,EAAe,CAChD,KAAO,KAAK,OAAO,WAAa,KAAK,OAASA,GAAO,CACnD,IAAMC,EAAY,IAAI,YAAY,KAAK,OAAO,WAAa,CAAC,EAC5D,IAAI,WAAWA,CAAS,EAAE,IAAI,IAAI,WAAW,KAAK,MAAM,CAAC,EACzD,KAAK,OAASA,CAChB,CACF,CASU,eAAeC,EAAoB,CAC3C,KAAK,2BAA2BA,EAAO,MAAM,EAC7C,IAAI,WAAW,KAAK,OAAQ,KAAK,MAAM,EAAE,IAAIA,CAAM,EACnD,KAAK,QAAUA,EAAO,MACxB,CAaQ,sBACNC,EACAC,EACAC,EACA,CACA,KAAK,2BAA2BD,CAAW,EAC3C,IAAME,EAAK,IAAI,SAAS,KAAK,OAAQ,KAAK,MAAM,EAChDH,EAAG,MAAMG,EAAI,CAAC,EAAGD,EAAO,EAAI,CAAC,EAC7B,KAAK,QAAUD,CACjB,CAqBA,aAAaC,EAAe,CAC1B,IAAME,EAAc,IAAI,YACxB,KAAK,eAAeA,EAAY,OAAOF,CAAK,CAAC,CAC/C,CAYA,eAAeA,EAAmB,CAChC,KAAK,sBAAsBA,EAAM,MAAM,EACvC,KAAK,eAAeA,CAAK,CAC3B,CAWA,oBAAoBA,EAAmB,CACrC,KAAK,eAAeA,CAAK,CAC3B,CAWA,cAAcA,EAAgB,CAU5BG,EAAcH,CAAK,EACnB,IAAMI,EAAYJ,EAAQ,EAAI,EAC9B,KAAK,eAAe,IAAI,WAAW,CAACI,CAAS,CAAC,CAAC,CACjD,CAWA,YAAYJ,EAAc,CACxB,KAAK,eAAe,IAAI,WAAW,CAACA,CAAK,CAAC,CAAC,CAC7C,CAyBA,aAAaA,EAAe,CAC1B,KAAK,sBAAsB,SAAS,UAAU,UAAW,EAAGA,CAAK,CACnE,CAgBA,aAAaA,EAAe,CAC1B,KAAK,sBAAsB,SAAS,UAAU,UAAW,EAAGA,CAAK,CACnE,CAiBA,aAAaA,EAAkB,CAC7B,IAAMK,EAAM,OAAOL,CAAK,EAAI,OAAOM,CAAc,EAC3CC,EAAO,OAAOP,CAAK,GAAK,OAAO,EAAE,EAGvC,KAAK,aAAa,OAAOK,CAAG,CAAC,EAC7B,KAAK,aAAa,OAAOE,CAAI,CAAC,CAChC,CAUA,cAAcP,EAAkB,CAC9B,IAAMK,EAAM,OAAOL,CAAK,EAAIQ,EACtBD,EAAO,OAAOP,CAAK,GAAK,OAAO,EAAE,EAGvC,KAAK,aAAaK,CAAG,EACrB,KAAK,aAAaE,CAAI,CACxB,CAWA,cAAcP,EAAkB,CAC9B,IAAMK,EAAM,OAAOL,CAAK,EAAIS,EACtBF,EAAO,OAAOP,CAAK,GAAK,OAAO,GAAG,EAGxC,KAAK,cAAcK,CAAG,EACtB,KAAK,cAAcE,CAAI,CACzB,CAWA,sBAAsBG,EAAa,CACjC,IAAIV,EAAQU,EACNC,EAAa,CAAC,EACpB,KAAOX,IAAU,GACfW,EAAW,KAAMX,EAAQ,IAAQ,GAAI,EACrCA,KAAW,EAEbW,EAAW,KAAKX,CAAK,EACrB,KAAK,eAAe,IAAI,WAAWW,CAAU,CAAC,CAChD,CAWA,cAA2B,CACzB,OAAO,IAAI,WAAW,KAAK,MAAM,EAAE,MAAM,EAAG,KAAK,MAAM,CACzD,CAWA,UAAkCX,EAAgB,CAGhDA,EAAM,UAAU,IAAI,CACtB,CAuBA,gBAAwCH,EAAwB,CAC9D,KAAK,sBAAsBA,EAAO,MAAM,EACxCA,EAAO,QAASe,GAAS,CACvBA,EAAK,UAAU,IAAI,CACrB,CAAC,CACH,CAqBA,gBAA8DZ,EAAWa,EAAoB,CAC3F,IAAMC,EAAWd,IAAU,OAC3B,KAAK,cAAcc,CAAQ,EACvBA,IACE,OAAOd,GAAU,SACnB,KAAK,aAAaA,CAAK,EACdA,aAAiB,WACtBa,IAAQ,OACV,KAAK,oBAAoBb,CAAK,EAE9B,KAAK,eAAeA,CAAK,EAG3BA,EAAM,UAAU,IAAI,EAG1B,CAeA,mBAAmBA,EAAsB,CACnCA,IAAU,OACZ,KAAK,sBAAsB,CAAC,GAE5B,KAAK,sBAAsB,CAAC,EAC5B,KAAK,aAAaA,CAAK,EAE3B,CACF,EAlPEe,EAAA,CADCC,EAAiB,EAAGC,CAAa,GA5JvB1B,EA6JX,2BA2BAwB,EAAA,CADCC,EAAiB,EAAGE,CAAc,GAvLxB3B,EAwLX,4BAkBAwB,EAAA,CADCC,EAAiB,EAAGV,CAAc,GAzMxBf,EA0MX,4BAmBAwB,EAAA,CADCC,EAAiB,OAAO,CAAC,EAAGR,CAAe,GA5NjCjB,EA6NX,4BAiBAwB,EAAA,CADCC,EAAiB,OAAO,CAAC,EAAGP,CAAgB,GA7OlClB,EA8OX,6BAkBAwB,EAAA,CADCC,EAAiB,OAAO,CAAC,EAAGG,CAAgB,GA/PlC5B,EAgQX,6BAkBAwB,EAAA,CADCC,EAAiB,EAAGV,CAAc,GAjRxBf,EAkRX,qCAmIK,SAASY,EAAcH,EAA0C,CACtE,GAAI,OAAOA,GAAU,UACnB,MAAM,IAAI,MAAM,GAAGA,CAAK,yBAAyB,CAErD,CAKO,IAAMoB,EAAyB,CAACpB,EAAkBqB,EAAgBC,IACvE,GAAGtB,CAAK,sBAAsBqB,CAAG,KAAKC,CAAG,IAYpC,SAASC,EAA2CvB,EAAUwB,EAAaC,EAAa,CAC7F,IAAMC,EAAc,OAAO1B,CAAK,EAChC,GAAI0B,EAAc,OAAOD,CAAQ,GAAKC,EAAc,OAAOF,CAAQ,EACjE,MAAM,IAAI,MAAMJ,EAAuBpB,EAAOwB,EAAUC,CAAQ,CAAC,CAErE,CAWA,SAAST,EAAsCQ,EAAaC,EAAa,CACvE,MAAO,CAACE,EAAiBC,EAAqBC,IAAmC,CAC/E,IAAMC,EAAgBD,EAAW,MAEjC,OAAAA,EAAW,MAAQ,SAAc7B,EAAkB,CACjD,OAAAuB,EAAsBvB,EAAOwB,EAAUC,CAAQ,EACxCK,EAAc,MAAM,KAAM,CAAC9B,CAAK,CAAC,CAC1C,EAEO6B,CACT,CACF", "names": ["Serializable", "serializer", "Serializer", "bcsBytes", "Hex", "length", "bytes", "new<PERSON>uffer", "values", "fn", "bytesLength", "value", "dv", "textEncoder", "ensureBoolean", "byteValue", "low", "MAX_U32_NUMBER", "high", "MAX_U64_BIG_INT", "MAX_U128_BIG_INT", "val", "valueArray", "item", "len", "hasValue", "__decorateClass", "checkNumberRange", "MAX_U8_NUMBER", "MAX_U16_NUMBER", "MAX_U256_BIG_INT", "outOfRangeErrorMessage", "min", "max", "validateNumberInRange", "minValue", "maxValue", "valueBigInt", "target", "propertyKey", "descriptor", "childFunction"]}