/**
 * Takes a value, serializes it to JSON and encodes it as base64.
 *
 * This can be used for creating values of fields that have the CosmWasm Binary type.
 */
export declare function toBinary(obj: any): string;
/**
 * Takes a base64 string, decodes it and parses the content from JSON to an object.
 *
 * This can be used for parsing the values of a CosmWasm Binary field.
 */
export declare function fromBinary(base64: string): any;
