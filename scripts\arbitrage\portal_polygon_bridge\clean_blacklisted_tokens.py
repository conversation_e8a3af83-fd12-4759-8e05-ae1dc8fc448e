#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清理黑名单代币数据脚本

功能：
使用 blacklist.json 中的所有代币 eth_address 删除 zero_tx_stats_detailed.json 中所有对应地址相关的数据

使用方法：
python clean_blacklisted_tokens.py [--dry-run] [--backup]

参数：
--dry-run: 只显示将要删除的数据，不实际执行删除操作
--backup: 在删除前创建备份文件
"""

import os
import sys
import json
import argparse
from datetime import datetime
from typing import Dict, List, Set

# 文件路径
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
BLACKLIST_PATH = os.path.join(SCRIPT_DIR, "results", "blacklist.json")
ZERO_TX_STATS_PATH = os.path.join(SCRIPT_DIR, "..", "..", "..", "src", "bridge", "pol_bridge", "data", "zero_tx_stats_detailed.json")

def load_blacklist() -> Set[str]:
    """
    加载黑名单文件，返回所有 eth_address 的集合
    
    Returns:
        包含所有黑名单地址的集合（转换为小写）
    """
    try:
        with open(BLACKLIST_PATH, 'r', encoding='utf-8') as f:
            blacklist_data = json.load(f)
        
        # 获取所有 eth_address，转换为小写以便比较
        eth_addresses = set()
        if "eth_address" in blacklist_data:
            for addr in blacklist_data["eth_address"]:
                eth_addresses.add(addr.lower())
        
        print(f"✅ 成功加载黑名单文件: {BLACKLIST_PATH}")
        print(f"📊 黑名单中包含 {len(eth_addresses)} 个地址")
        
        return eth_addresses
        
    except FileNotFoundError:
        print(f"❌ 黑名单文件不存在: {BLACKLIST_PATH}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 黑名单文件格式错误: {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 加载黑名单文件时出错: {str(e)}")
        sys.exit(1)

def load_zero_tx_stats() -> Dict:
    """
    加载零交易统计文件
    
    Returns:
        零交易统计数据字典
    """
    try:
        with open(ZERO_TX_STATS_PATH, 'r', encoding='utf-8') as f:
            zero_tx_data = json.load(f)
        
        print(f"✅ 成功加载零交易统计文件: {ZERO_TX_STATS_PATH}")
        print(f"📊 文件中包含 {len(zero_tx_data)} 个代币记录")
        
        return zero_tx_data
        
    except FileNotFoundError:
        print(f"❌ 零交易统计文件不存在: {ZERO_TX_STATS_PATH}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 零交易统计文件格式错误: {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 加载零交易统计文件时出错: {str(e)}")
        sys.exit(1)

def create_backup(file_path: str) -> str:
    """
    创建文件备份
    
    Args:
        file_path: 要备份的文件路径
        
    Returns:
        备份文件路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    
    try:
        import shutil
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ 创建备份文件失败: {str(e)}")
        sys.exit(1)

def find_matching_addresses(zero_tx_data: Dict, blacklist_addresses: Set[str]) -> List[str]:
    """
    查找需要删除的记录键

    Args:
        zero_tx_data: 零交易统计数据
        blacklist_addresses: 黑名单地址集合

    Returns:
        需要删除的记录键列表
    """
    matching_keys = []

    for key, record in zero_tx_data.items():
        # 检查记录中的 eth_address 字段
        if isinstance(record, dict) and "eth_address" in record:
            eth_address = record["eth_address"].lower()
            if eth_address in blacklist_addresses:
                matching_keys.append(key)

    return matching_keys

def clean_blacklisted_data(zero_tx_data: Dict, blacklist_addresses: Set[str], dry_run: bool = False) -> Dict:
    """
    清理黑名单中的代币数据

    Args:
        zero_tx_data: 零交易统计数据
        blacklist_addresses: 黑名单地址集合
        dry_run: 是否为试运行模式

    Returns:
        清理后的数据字典
    """
    # 查找匹配的记录键
    matching_keys = find_matching_addresses(zero_tx_data, blacklist_addresses)

    if not matching_keys:
        print("✅ 没有找到需要删除的数据")
        return zero_tx_data

    print(f"\n🔍 找到 {len(matching_keys)} 个需要删除的记录:")

    # 显示将要删除的数据详情
    for key in matching_keys:
        token_info = zero_tx_data[key]
        symbol = token_info.get('symbol', 'UNKNOWN')
        name = token_info.get('name', 'Unknown Token')
        zero_tx_count = token_info.get('zero_tx_count', 0)
        volume_usd = token_info.get('volume_usd', 0)
        eth_address = token_info.get('eth_address', 'N/A')

        print(f"  📍 记录键: {key}")
        print(f"     ETH地址: {eth_address}")
        print(f"     代币: {symbol} ({name})")
        print(f"     零交易数量: {zero_tx_count}")
        print(f"     交易量(USD): {volume_usd}")
        print()

    if dry_run:
        print("🔍 试运行模式：以上数据将被删除（实际未执行删除操作）")
        return zero_tx_data

    # 执行删除操作
    cleaned_data = zero_tx_data.copy()
    for key in matching_keys:
        del cleaned_data[key]

    print(f"✅ 已删除 {len(matching_keys)} 个代币的数据")
    print(f"📊 剩余代币数量: {len(cleaned_data)}")

    return cleaned_data

def save_cleaned_data(cleaned_data: Dict, file_path: str):
    """
    保存清理后的数据
    
    Args:
        cleaned_data: 清理后的数据
        file_path: 保存文件路径
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已保存清理后的数据到: {file_path}")
        
    except Exception as e:
        print(f"❌ 保存文件时出错: {str(e)}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="清理黑名单代币数据")
    parser.add_argument("--dry-run", action="store_true", help="只显示将要删除的数据，不实际执行删除操作")
    parser.add_argument("--backup", action="store_true", help="在删除前创建备份文件")
    
    args = parser.parse_args()
    
    print("🚀 开始清理黑名单代币数据...")
    print(f"📁 黑名单文件: {BLACKLIST_PATH}")
    print(f"📁 零交易统计文件: {ZERO_TX_STATS_PATH}")
    
    if args.dry_run:
        print("🔍 运行模式: 试运行（不会实际删除数据）")
    else:
        print("⚠️  运行模式: 实际删除")
    
    print("-" * 60)
    
    # 加载数据
    blacklist_addresses = load_blacklist()
    zero_tx_data = load_zero_tx_stats()
    
    # 创建备份（如果需要）
    if args.backup and not args.dry_run:
        create_backup(ZERO_TX_STATS_PATH)
    
    # 清理数据
    cleaned_data = clean_blacklisted_data(zero_tx_data, blacklist_addresses, args.dry_run)
    
    # 保存清理后的数据（非试运行模式）
    if not args.dry_run:
        save_cleaned_data(cleaned_data, ZERO_TX_STATS_PATH)
    
    print("-" * 60)
    print("🎉 清理操作完成！")

if __name__ == "__main__":
    main()
