import _m0 from "protobufjs/minimal.js";
import { BitArray } from "../../libs/bits/v1/types";
import { BlockID, Part, PartSetHeader, Proposal as Proposal1, SignedMsgType, Vote as Vote2 } from "../../types/v1/types";
export declare const protobufPackage = "cometbft.consensus.v1";
/**
 * NewRoundStep is sent for every step taken in the ConsensusState.
 * For every height/round/step transition
 */
export interface NewRoundStep {
    height: string;
    round: number;
    step: number;
    secondsSinceStartTime: string;
    lastCommitRound: number;
}
/**
 * NewValidBlock is sent when a validator observes a valid block B in some round r,
 * i.e., there is a Proposal for block B and 2/3+ prevotes for the block B in the round r.
 * In case the block is also committed, then IsCommit flag is set to true.
 */
export interface NewValidBlock {
    height: string;
    round: number;
    blockPartSetHeader: PartSetHeader | undefined;
    blockParts: BitArray | undefined;
    isCommit: boolean;
}
/** Proposal is sent when a new block is proposed. */
export interface Proposal {
    proposal: Proposal1 | undefined;
}
/** ProposalPOL is sent when a previous proposal is re-proposed. */
export interface ProposalPOL {
    height: string;
    proposalPolRound: number;
    proposalPol: BitArray | undefined;
}
/** BlockPart is sent when gossipping a piece of the proposed block. */
export interface BlockPart {
    height: string;
    round: number;
    part: Part | undefined;
}
/** Vote is sent when voting for a proposal (or lack thereof). */
export interface Vote {
    vote: Vote2 | undefined;
}
/** HasVote is sent to indicate that a particular vote has been received. */
export interface HasVote {
    height: string;
    round: number;
    type: SignedMsgType;
    index: number;
}
/** VoteSetMaj23 is sent to indicate that a given BlockID has seen +2/3 votes. */
export interface VoteSetMaj23 {
    height: string;
    round: number;
    type: SignedMsgType;
    blockId: BlockID | undefined;
}
/** VoteSetBits is sent to communicate the bit-array of votes seen for the BlockID. */
export interface VoteSetBits {
    height: string;
    round: number;
    type: SignedMsgType;
    blockId: BlockID | undefined;
    votes: BitArray | undefined;
}
/** HasProposalBlockPart is sent to indicate that a particular proposal block part has been received. */
export interface HasProposalBlockPart {
    height: string;
    round: number;
    index: number;
}
/** Message is an abstract consensus message. */
export interface Message {
    newRoundStep?: NewRoundStep | undefined;
    newValidBlock?: NewValidBlock | undefined;
    proposal?: Proposal | undefined;
    proposalPol?: ProposalPOL | undefined;
    blockPart?: BlockPart | undefined;
    vote?: Vote | undefined;
    hasVote?: HasVote | undefined;
    voteSetMaj23?: VoteSetMaj23 | undefined;
    voteSetBits?: VoteSetBits | undefined;
    hasProposalBlockPart?: HasProposalBlockPart | undefined;
}
export declare const NewRoundStep: {
    encode(message: NewRoundStep, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): NewRoundStep;
    fromJSON(object: any): NewRoundStep;
    toJSON(message: NewRoundStep): unknown;
    create(base?: DeepPartial<NewRoundStep>): NewRoundStep;
    fromPartial(object: DeepPartial<NewRoundStep>): NewRoundStep;
};
export declare const NewValidBlock: {
    encode(message: NewValidBlock, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): NewValidBlock;
    fromJSON(object: any): NewValidBlock;
    toJSON(message: NewValidBlock): unknown;
    create(base?: DeepPartial<NewValidBlock>): NewValidBlock;
    fromPartial(object: DeepPartial<NewValidBlock>): NewValidBlock;
};
export declare const Proposal: {
    encode(message: Proposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Proposal;
    fromJSON(object: any): Proposal;
    toJSON(message: Proposal): unknown;
    create(base?: DeepPartial<Proposal>): Proposal;
    fromPartial(object: DeepPartial<Proposal>): Proposal;
};
export declare const ProposalPOL: {
    encode(message: ProposalPOL, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ProposalPOL;
    fromJSON(object: any): ProposalPOL;
    toJSON(message: ProposalPOL): unknown;
    create(base?: DeepPartial<ProposalPOL>): ProposalPOL;
    fromPartial(object: DeepPartial<ProposalPOL>): ProposalPOL;
};
export declare const BlockPart: {
    encode(message: BlockPart, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BlockPart;
    fromJSON(object: any): BlockPart;
    toJSON(message: BlockPart): unknown;
    create(base?: DeepPartial<BlockPart>): BlockPart;
    fromPartial(object: DeepPartial<BlockPart>): BlockPart;
};
export declare const Vote: {
    encode(message: Vote, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Vote;
    fromJSON(object: any): Vote;
    toJSON(message: Vote): unknown;
    create(base?: DeepPartial<Vote>): Vote;
    fromPartial(object: DeepPartial<Vote>): Vote;
};
export declare const HasVote: {
    encode(message: HasVote, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HasVote;
    fromJSON(object: any): HasVote;
    toJSON(message: HasVote): unknown;
    create(base?: DeepPartial<HasVote>): HasVote;
    fromPartial(object: DeepPartial<HasVote>): HasVote;
};
export declare const VoteSetMaj23: {
    encode(message: VoteSetMaj23, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VoteSetMaj23;
    fromJSON(object: any): VoteSetMaj23;
    toJSON(message: VoteSetMaj23): unknown;
    create(base?: DeepPartial<VoteSetMaj23>): VoteSetMaj23;
    fromPartial(object: DeepPartial<VoteSetMaj23>): VoteSetMaj23;
};
export declare const VoteSetBits: {
    encode(message: VoteSetBits, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VoteSetBits;
    fromJSON(object: any): VoteSetBits;
    toJSON(message: VoteSetBits): unknown;
    create(base?: DeepPartial<VoteSetBits>): VoteSetBits;
    fromPartial(object: DeepPartial<VoteSetBits>): VoteSetBits;
};
export declare const HasProposalBlockPart: {
    encode(message: HasProposalBlockPart, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HasProposalBlockPart;
    fromJSON(object: any): HasProposalBlockPart;
    toJSON(message: HasProposalBlockPart): unknown;
    create(base?: DeepPartial<HasProposalBlockPart>): HasProposalBlockPart;
    fromPartial(object: DeepPartial<HasProposalBlockPart>): HasProposalBlockPart;
};
export declare const Message: {
    encode(message: Message, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Message;
    fromJSON(object: any): Message;
    toJSON(message: Message): unknown;
    create(base?: DeepPartial<Message>): Message;
    fromPartial(object: DeepPartial<Message>): Message;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
