{"version": 3, "file": "provider.js", "sourceRoot": "", "sources": ["../../src/provider.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,UAAU,EAQV,oBAAoB,GAIrB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,IAAI,EAAE,MAAM,wBAAwB,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AACtE,OAAO,EACL,mBAAmB,GAEpB,MAAM,gBAAgB,CAAC;AA+BxB;;;GAGG;AACH,MAAM,OAAO,cAAc;IAGzB;;;;OAIG;IACH,YACW,UAAsB,EACtB,MAAc,EACd,IAAoB;QAFpB,eAAU,GAAV,UAAU,CAAY;QACtB,WAAM,GAAN,MAAM,CAAQ;QACd,SAAI,GAAJ,IAAI,CAAgB;QAE7B,IAAI,CAAC,SAAS,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,CAAC;IACrC,CAAC;IAED,MAAM,CAAC,cAAc;QACnB,OAAO;YACL,mBAAmB,EAAE,WAAW;YAChC,UAAU,EAAE,WAAW;SACxB,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,GAAY,EAAE,IAAqB;QAC9C,IAAI,SAAS,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAChE;QACD,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,cAAc,CAAC,cAAc,EAAE,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,UAAU,CAC/B,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,uBAAuB,EAC9B,IAAI,CAAC,mBAAmB,CACzB,CAAC;QACF,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;QACtD,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;QAClC,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,GAAG;QACR,IAAI,SAAS,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC9D;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QACnC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAC5C,IAAI,GAAG,KAAK,SAAS,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACvD;QACD,MAAM,OAAO,GAAG,cAAc,CAAC,cAAc,EAAE,CAAC;QAChD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;QACtD,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;QAElC,OAAO,IAAI,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,cAAc,CAClB,EAAsC,EACtC,OAAkB,EAClB,IAAqB;;QAErB,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;SAClB;QAED,IAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE;YAC9B,IAAI,OAAO,EAAE;gBACX,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAClB;SACF;aAAM;YACL,EAAE,CAAC,QAAQ,GAAG,MAAA,EAAE,CAAC,QAAQ,mCAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACnD,EAAE,CAAC,eAAe,GAAG,CACnB,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CACnE,CAAC,SAAS,CAAC;YAEZ,IAAI,OAAO,EAAE;gBACX,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;oBAC5B,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;iBACxB;aACF;SACF;QACD,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;QAE7B,IAAI;YACF,OAAO,MAAM,4BAA4B,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACzE;QAAC,OAAO,GAAG,EAAE;YACZ,uEAAuE;YACvE,8FAA8F;YAC9F,IAAI,GAAG,YAAY,YAAY,EAAE;gBAC/B,gEAAgE;gBAChE,oFAAoF;gBACpF,wFAAwF;gBACxF,iDAAiD;gBACjD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CACvB,sBAAsB,CAAC,EAAE,CAAC;oBACxB,CAAC,CAAC,CAAA,MAAA,EAAE,CAAC,UAAU,0CAAG,CAAC,CAAC,KAAI,IAAI,UAAU,EAAE;oBACxC,CAAC,CAAC,MAAA,EAAE,CAAC,SAAS,mCAAI,IAAI,UAAU,EAAE,CACrC,CAAC;gBACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,EAAE;oBAC3D,UAAU,EAAE,WAAW;iBACxB,CAAC,CAAC;gBACH,IAAI,CAAC,QAAQ,EAAE;oBACb,MAAM,GAAG,CAAC;iBACX;qBAAM;oBACL,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,IAAI,0CAAE,WAAW,CAAC;oBACxC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;iBACjE;aACF;iBAAM;gBACL,MAAM,GAAG,CAAC;aACX;SACF;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,OAAO,CACX,aAGG,EACH,IAAqB;;QAErB,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;SAClB;QACD,MAAM,eAAe,GAAG,CACtB,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CACnE,CAAC,SAAS,CAAC;QAEZ,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;;YAChC,IAAI,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBAChC,IAAI,EAAE,GAAyB,CAAC,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,CAAC,OAAO,EAAE;oBACb,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;iBACpB;gBACD,OAAO,EAAE,CAAC;aACX;iBAAM;gBACL,IAAI,EAAE,GAAgB,CAAC,CAAC,EAAE,CAAC;gBAC3B,IAAI,OAAO,GAAG,MAAA,CAAC,CAAC,OAAO,mCAAI,EAAE,CAAC;gBAE9B,EAAE,CAAC,QAAQ,GAAG,MAAA,EAAE,CAAC,QAAQ,mCAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBACnD,EAAE,CAAC,eAAe,GAAG,eAAe,CAAC;gBAErC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;oBACrB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;gBACH,OAAO,EAAE,CAAC;aACX;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAE7D,MAAM,IAAI,GAA2B,EAAE,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;YAE7B,IAAI;gBACF,IAAI,CAAC,IAAI,CACP,MAAM,4BAA4B,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CACjE,CAAC;aACH;YAAC,OAAO,GAAG,EAAE;gBACZ,uEAAuE;gBACvE,8FAA8F;gBAC9F,IAAI,GAAG,YAAY,YAAY,EAAE;oBAC/B,gEAAgE;oBAChE,oFAAoF;oBACpF,wFAAwF;oBACxF,iDAAiD;oBACjD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CACvB,sBAAsB,CAAC,EAAE,CAAC;wBACxB,CAAC,CAAC,CAAA,MAAA,EAAE,CAAC,UAAU,0CAAG,CAAC,CAAC,KAAI,IAAI,UAAU,EAAE;wBACxC,CAAC,CAAC,MAAA,EAAE,CAAC,SAAS,mCAAI,IAAI,UAAU,EAAE,CACrC,CAAC;oBACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,EAAE;wBAC3D,UAAU,EAAE,WAAW;qBACxB,CAAC,CAAC;oBACH,IAAI,CAAC,QAAQ,EAAE;wBACb,MAAM,GAAG,CAAC;qBACX;yBAAM;wBACL,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,IAAI,0CAAE,WAAW,CAAC;wBACxC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACjE;iBACF;qBAAM;oBACL,MAAM,GAAG,CAAC;iBACX;aACF;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,QAAQ,CACZ,EAAsC,EACtC,OAAkB,EAClB,UAAuB,EACvB,eAAuC;QAEvC,IAAI,eAAe,GAAG,CACpB,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,CACtC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CACzC,CACF,CAAC,SAAS,CAAC;QAEZ,IAAI,MAA2D,CAAC;QAChE,IAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE;YAC9B,IAAI,OAAO,EAAE;gBACX,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACjB,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;aAC5C;YAED,sEAAsE;YACtE,2CAA2C;YAC3C,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;SACxE;aAAM;YACL,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YACnD,EAAE,CAAC,eAAe,GAAG,eAAe,CAAC;YAErC,IAAI,OAAO,EAAE;gBACX,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;aAC5C;YACD,MAAM,GAAG,MAAM,mBAAmB,CAChC,IAAI,CAAC,UAAU,EACf,EAAE,EACF,OAAO,EACP,UAAU,EACV,eAAe,CAChB,CAAC;SACH;QAED,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;YACpB,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACvC;QAED,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;CACF;AAED,MAAM,aAAc,SAAQ,KAAK;IAC/B,YACW,kBAAgD,EACzD,OAAgB;QAEhB,KAAK,CAAC,OAAO,CAAC,CAAC;QAHN,uBAAkB,GAAlB,kBAAkB,CAA8B;IAI3D,CAAC;CACF;AAqBD,8DAA8D;AAC9D,iEAAiE;AACjE,KAAK,UAAU,4BAA4B,CACzC,UAAsB,EACtB,cAAmC,EACnC,OAAwB;IAExB,MAAM,WAAW,GAAG,OAAO,IAAI;QAC7B,aAAa,EAAE,OAAO,CAAC,aAAa;QACpC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,UAAU;KACvE,CAAC;IAEF,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,kBAAkB,CACnD,cAAc,EACd,WAAW,CACZ,CAAC;IAEF,MAAM,MAAM,GAAG,CACb,MAAM,UAAU,CAAC,kBAAkB,CACjC,SAAS,EACT,OAAO,IAAI,OAAO,CAAC,UAAU,CAC9B,CACF,CAAC,KAAK,CAAC;IAER,IAAI,MAAM,CAAC,GAAG,EAAE;QACd,MAAM,IAAI,YAAY,CACpB,mBAAmB,SAAS,YAAY,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAClE,CAAC;KACH;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,YAAa,SAAQ,KAAK;IAC9B,YAAY,OAAgB;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;IACjB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,QAAkB;IAC5C,SAAS,GAAG,QAAQ,CAAC;AACvB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW;IACzB,IAAI,SAAS,KAAK,IAAI,EAAE;QACtB,OAAO,cAAc,CAAC,KAAK,EAAE,CAAC;KAC/B;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,oEAAoE;AACpE,IAAI,SAAS,GAAoB,IAAI,CAAC"}