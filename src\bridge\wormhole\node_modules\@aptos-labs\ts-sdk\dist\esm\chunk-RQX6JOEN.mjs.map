{"version": 3, "sources": ["../../src/core/crypto/abstraction.ts"], "sourcesContent": ["import { Deserializer, Serializer } from \"../../bcs\";\nimport { HexInput } from \"../../types\";\nimport { AccountAddress } from \"../accountAddress\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Hex } from \"../hex\";\nimport { AccountPublicKey, VerifySignatureArgs, VerifySignatureAsyncArgs } from \"./publicKey\";\nimport { Signature } from \"./signature\";\n\nexport class AbstractSignature extends Signature {\n  readonly value: Uint8Array;\n\n  constructor(value: HexInput) {\n    super();\n    this.value = Hex.fromHexInput(value).toUint8Array();\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.value);\n  }\n\n  static deserialize(deserializer: Deserializer): AbstractSignature {\n    return new AbstractSignature(deserializer.deserializeBytes());\n  }\n}\n\nexport class AbstractPublicKey extends AccountPub<PERSON>Key {\n  readonly accountAddress: AccountAddress;\n\n  constructor(accountAddress: AccountAddress) {\n    super();\n    this.accountAddress = accountAddress;\n  }\n\n  authKey(): AuthenticationKey {\n    return new AuthenticationKey({ data: this.accountAddress.toUint8Array() });\n  }\n\n  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars\n  verifySignature(args: VerifySignatureArgs): boolean {\n    throw new Error(\"This function is not implemented for AbstractPublicKey.\");\n  }\n\n  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars\n  async verifySignatureAsync(args: VerifySignatureAsyncArgs): Promise<boolean> {\n    throw new Error(\"This function is not implemented for AbstractPublicKey.\");\n  }\n\n  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars\n  serialize(serializer: Serializer): void {\n    throw new Error(\"This function is not implemented for AbstractPublicKey.\");\n  }\n}\n"], "mappings": "oKAQO,IAAMA,EAAN,MAAMC,UAA0BC,CAAU,CAG/C,YAAYC,EAAiB,CAC3B,MAAM,EACN,KAAK,MAAQC,EAAI,aAAaD,CAAK,EAAE,aAAa,CACpD,CAEA,UAAUE,EAA8B,CACtCA,EAAW,eAAe,KAAK,KAAK,CACtC,CAEA,OAAO,YAAYC,EAA+C,CAChE,OAAO,IAAIL,EAAkBK,EAAa,iBAAiB,CAAC,CAC9D,CACF,EAEaC,EAAN,cAAgCC,CAAiB,CAGtD,YAAYC,EAAgC,CAC1C,MAAM,EACN,KAAK,eAAiBA,CACxB,CAEA,SAA6B,CAC3B,OAAO,IAAIC,EAAkB,CAAE,KAAM,KAAK,eAAe,aAAa,CAAE,CAAC,CAC3E,CAGA,gBAAgBC,EAAoC,CAClD,MAAM,IAAI,MAAM,yDAAyD,CAC3E,CAGA,MAAM,qBAAqBA,EAAkD,CAC3E,MAAM,IAAI,MAAM,yDAAyD,CAC3E,CAGA,UAAUN,EAA8B,CACtC,MAAM,IAAI,MAAM,yDAAyD,CAC3E,CACF", "names": ["AbstractSignature", "_AbstractSignature", "Signature", "value", "Hex", "serializer", "deserializer", "AbstractPublicKey", "Account<PERSON><PERSON><PERSON><PERSON><PERSON>", "accountAddress", "AuthenticationKey", "args"]}