import { IndexerErrorModule, UnspecifiedErrorCode, grpcErrorCodeToErrorCode, GrpcUnaryRequestException, } from '@injectivelabs/exceptions';
import { InjectiveAbacusRpc } from '@injectivelabs/abacus-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
import { AbacusGrpcTransformer } from './transformers/index.js';
export class AbacusGrpcApi extends BaseGrpcConsumer {
    module = IndexerErrorModule.Abacus;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new InjectiveAbacusRpc.PointsSvcClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchAccountLatestPoints(address) {
        const request = InjectiveAbacusRpc.PointsLatestForAccountRequest.create();
        request.accountAddress = address;
        try {
            const response = await this.retry(() => this.client.PointsLatestForAccount(request));
            return AbacusGrpcTransformer.grpcPointsLatestToPointsLatest(response);
        }
        catch (e) {
            if (e instanceof InjectiveAbacusRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'PointsStatsLatestForAccount',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'PointsStatsLatestForAccount',
                contextModule: this.module,
            });
        }
    }
    async fetchAccountDailyPoints(address, daysLimit) {
        const request = InjectiveAbacusRpc.PointsStatsDailyForAccountRequest.create();
        request.accountAddress = address;
        if (daysLimit) {
            request.daysLimit = daysLimit.toString();
        }
        try {
            const response = await this.retry(() => this.client.PointsStatsDailyForAccount(request));
            return AbacusGrpcTransformer.grpcPointsStatsDailyToPointsStatsDaily(response);
        }
        catch (e) {
            if (e instanceof InjectiveAbacusRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'PointsStatsDailyForAccount',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'PointsStatsDailyForAccount',
                contextModule: this.module,
            });
        }
    }
    async fetchAccountWeeklyPoints(address, weeksLimit) {
        const request = InjectiveAbacusRpc.PointsStatsWeeklyForAccountRequest.create();
        request.accountAddress = address;
        if (weeksLimit) {
            request.weeksLimit = weeksLimit.toString();
        }
        try {
            const response = await this.retry(() => this.client.PointsStatsWeeklyForAccount(request));
            return AbacusGrpcTransformer.grpcPointsStatsWeeklyToPointsStatsWeekly(response);
        }
        catch (e) {
            if (e instanceof InjectiveAbacusRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'PointsStatsWeeklyForAccount',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'PointsStatsWeeklyForAccount',
                contextModule: this.module,
            });
        }
    }
}
