import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Observable } from "rxjs";
export declare const protobufPackage = "injective_trading_rpc";
export interface ListTradingStrategiesRequest {
    state: string;
    /** MarketId of the trading strategy */
    marketId: string;
    /** subaccount ID to filter by */
    subaccountId: string;
    /** Account address */
    accountAddress: string;
    /** Indicates whether the trading strategy is pending execution */
    pendingExecution: boolean;
    /**
     * The starting timestamp in UNIX milliseconds for the creation time of the
     * trading strategy
     */
    startTime: string;
    /**
     * The ending timestamp in UNIX milliseconds for the creation time of the
     * trading strategy
     */
    endTime: string;
    limit: number;
    skip: string;
    /** Filter by strategy type */
    strategyType: string[];
    /** Filter by market type */
    marketType: string;
    /**
     * The last executed timestamp in UNIX milliseconds for the last executed time
     * of the trading strategy
     */
    lastExecutedTime: string;
    /** Include TVL in the response */
    withTvl: boolean;
    /** Indicates whether the trading strategy is a trailing strategy */
    isTrailingStrategy: boolean;
    /**
     * Indicates whether the trading strategy performance should be included in the
     * response
     */
    withPerformance: boolean;
}
export interface ListTradingStrategiesResponse {
    /** The trading strategies */
    strategies: TradingStrategy[];
    paging: Paging | undefined;
}
export interface TradingStrategy {
    state: string;
    /** MarketId of the trading strategy */
    marketId: string;
    /** subaccount ID of the trading strategy */
    subaccountId: string;
    /** Account address */
    accountAddress: string;
    /** Contract address */
    contractAddress: string;
    /** Execution price of the trading strategy */
    executionPrice: string;
    /** Base quantity of the trading strategy */
    baseQuantity: string;
    /** Quote quantity of the trading strategy */
    quoteQuantity: string;
    /** Lower bound of the trading strategy */
    lowerBound: string;
    /** Upper bound of the trading strategy */
    upperBound: string;
    /** Stop loss limit of the trading strategy */
    stopLoss: string;
    /** Take profit limit of the trading strategy */
    takeProfit: string;
    /** Swap fee of the trading strategy */
    swapFee: string;
    /** Base deposit at the time of closing the trading strategy */
    baseDeposit: string;
    /** Quote deposit at the time of closing the trading strategy */
    quoteDeposit: string;
    /** Market mid price at the time of closing the trading strategy */
    marketMidPrice: string;
    /** Subscription quote quantity of the trading strategy */
    subscriptionQuoteQuantity: string;
    /** Subscription base quantity of the trading strategy */
    subscriptionBaseQuantity: string;
    /** Number of grid levels of the trading strategy */
    numberOfGridLevels: string;
    /** Indicates whether the trading strategy should exit with quote only */
    shouldExitWithQuoteOnly: boolean;
    /** Indicates the reason for stopping the trading strategy */
    stopReason: string;
    /** Indicates whether the trading strategy is pending execution */
    pendingExecution: boolean;
    /** Block height when the strategy was created. */
    createdHeight: string;
    /** Block height when the strategy was removed. */
    removedHeight: string;
    /** UpdatedAt timestamp in UNIX millis. */
    createdAt: string;
    /** UpdatedAt timestamp in UNIX millis. */
    updatedAt: string;
    /**
     * Indicate how bot will convert funds (into base or quote or keep as is) after
     * strategy ended
     */
    exitType: string;
    /** Exit config for stop loss */
    stopLossConfig: ExitConfig | undefined;
    /** Exit config for take profit */
    takeProfitConfig: ExitConfig | undefined;
    /** Strategy type: arithmetic, geometric... */
    strategyType: string;
    /** Version of the contract */
    contractVersion: string;
    /** Name of the contract */
    contractName: string;
    /** Type of the market */
    marketType: string;
    /** lastExecutedAt timestamp in UNIX millis. */
    lastExecutedAt: string;
    /** trailing up price */
    trailUpPrice: string;
    /** trailing down price */
    trailDownPrice: string;
    /** trailing up counter */
    trailUpCounter: string;
    /** trailing down counter */
    trailDownCounter: string;
    /** TVL of the trading strategy */
    tvl: string;
    /** PnL of the trading strategy */
    pnl: string;
    /** PnL percentage of the trading strategy */
    pnlPerc: string;
    /** pnlUpdatedAt timestamp in UNIX millis. */
    pnlUpdatedAt: string;
    /** Indicates the performance of the trading strategy */
    performance: string;
    /** Return on investment of the trading strategy */
    roi: string;
    /**
     * Initial base price of the trading strategy from asset price service
     * Use strategyFinalData if available to have more accurate data
     */
    initialBasePrice: string;
    /**
     * Initial quote price of the trading strategy from asset price service
     * Use strategyFinalData if available to have more accurate data
     */
    initialQuotePrice: string;
    /**
     * Current base price of the trading strategy from asset price service
     * Use strategyFinalData if available to have more accurate data
     */
    currentBasePrice: string;
    /**
     * Current quote price of the trading strategy from asset price service
     * Use strategyFinalData if available to have more accurate data
     */
    currentQuotePrice: string;
    /**
     * Final base price of the trading strategy from asset price service
     * Use strategyFinalData if available to have more accurate data
     */
    finalBasePrice: string;
    /**
     * Final quote price of the trading strategy from asset price service
     * Use strategyFinalData if available to have more accurate data
     */
    finalQuotePrice: string;
    /** Final data of the trading strategy. This is present from contract v0.8.4. */
    finalData: StrategyFinalData | undefined;
    /** Margin ratio of the trading strategy */
    marginRatio: string;
    /** Lower trailing bound of the trading strategy */
    lowerTrailingBound: string;
    /** Upper trailing bound of the trading strategy */
    upperTrailingBound: string;
    /** New upper bound of the trading strategy */
    newUpperBound: string;
    /** New lower bound of the trading strategy */
    newLowerBound: string;
}
export interface ExitConfig {
    /** strategy exit type (stopLoss/takeProfit) */
    exitType: string;
    /** strategy stopLoss/takeProfit price */
    exitPrice: string;
}
export interface StrategyFinalData {
    /** Initial base amount */
    initialBaseAmount: string;
    /** Initial quote amount */
    initialQuoteAmount: string;
    /** Final base amount */
    finalBaseAmount: string;
    /** Final quote amount */
    finalQuoteAmount: string;
    /** Initial base price */
    initialBasePrice: string;
    /** Initial quote price */
    initialQuotePrice: string;
    /** Final base price */
    finalBasePrice: string;
    /** Final quote price */
    finalQuotePrice: string;
}
/** Paging defines the structure for required params for handling pagination */
export interface Paging {
    /** total number of txs saved in database */
    total: string;
    /** can be either block height or index num */
    from: number;
    /** can be either block height or index num */
    to: number;
    /** count entries by subaccount, serving some places on helix */
    countBySubaccount: string;
    /** array of tokens to navigate to the next pages */
    next: string[];
}
export interface GetTradingStatsRequest {
}
export interface GetTradingStatsResponse {
    /** Total of unique active trading strategies */
    activeTradingStrategies: string;
    /** Total number of created trading strategies */
    totalTradingStrategiesCreated: string;
    /** Total TVL of all active trading strategies */
    totalTvl: string;
    /** Market stats */
    markets: Market[];
}
export interface Market {
    /** MarketId of the trading strategy */
    marketId: string;
    /** Total of unique active trading strategies */
    activeTradingStrategies: string;
}
export interface StreamStrategyRequest {
    /** Account addresses */
    accountAddresses: string[];
    /** MarketId of the trading strategy */
    marketId: string;
}
export interface StreamStrategyResponse {
    /** The trading strategy */
    tradingStrategy: TradingStrategy | undefined;
    /** Timestamp in UNIX millis */
    timestamp: string;
}
export declare const ListTradingStrategiesRequest: {
    encode(message: ListTradingStrategiesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListTradingStrategiesRequest;
    fromJSON(object: any): ListTradingStrategiesRequest;
    toJSON(message: ListTradingStrategiesRequest): unknown;
    create(base?: DeepPartial<ListTradingStrategiesRequest>): ListTradingStrategiesRequest;
    fromPartial(object: DeepPartial<ListTradingStrategiesRequest>): ListTradingStrategiesRequest;
};
export declare const ListTradingStrategiesResponse: {
    encode(message: ListTradingStrategiesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListTradingStrategiesResponse;
    fromJSON(object: any): ListTradingStrategiesResponse;
    toJSON(message: ListTradingStrategiesResponse): unknown;
    create(base?: DeepPartial<ListTradingStrategiesResponse>): ListTradingStrategiesResponse;
    fromPartial(object: DeepPartial<ListTradingStrategiesResponse>): ListTradingStrategiesResponse;
};
export declare const TradingStrategy: {
    encode(message: TradingStrategy, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingStrategy;
    fromJSON(object: any): TradingStrategy;
    toJSON(message: TradingStrategy): unknown;
    create(base?: DeepPartial<TradingStrategy>): TradingStrategy;
    fromPartial(object: DeepPartial<TradingStrategy>): TradingStrategy;
};
export declare const ExitConfig: {
    encode(message: ExitConfig, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExitConfig;
    fromJSON(object: any): ExitConfig;
    toJSON(message: ExitConfig): unknown;
    create(base?: DeepPartial<ExitConfig>): ExitConfig;
    fromPartial(object: DeepPartial<ExitConfig>): ExitConfig;
};
export declare const StrategyFinalData: {
    encode(message: StrategyFinalData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StrategyFinalData;
    fromJSON(object: any): StrategyFinalData;
    toJSON(message: StrategyFinalData): unknown;
    create(base?: DeepPartial<StrategyFinalData>): StrategyFinalData;
    fromPartial(object: DeepPartial<StrategyFinalData>): StrategyFinalData;
};
export declare const Paging: {
    encode(message: Paging, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Paging;
    fromJSON(object: any): Paging;
    toJSON(message: Paging): unknown;
    create(base?: DeepPartial<Paging>): Paging;
    fromPartial(object: DeepPartial<Paging>): Paging;
};
export declare const GetTradingStatsRequest: {
    encode(_: GetTradingStatsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetTradingStatsRequest;
    fromJSON(_: any): GetTradingStatsRequest;
    toJSON(_: GetTradingStatsRequest): unknown;
    create(base?: DeepPartial<GetTradingStatsRequest>): GetTradingStatsRequest;
    fromPartial(_: DeepPartial<GetTradingStatsRequest>): GetTradingStatsRequest;
};
export declare const GetTradingStatsResponse: {
    encode(message: GetTradingStatsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetTradingStatsResponse;
    fromJSON(object: any): GetTradingStatsResponse;
    toJSON(message: GetTradingStatsResponse): unknown;
    create(base?: DeepPartial<GetTradingStatsResponse>): GetTradingStatsResponse;
    fromPartial(object: DeepPartial<GetTradingStatsResponse>): GetTradingStatsResponse;
};
export declare const Market: {
    encode(message: Market, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Market;
    fromJSON(object: any): Market;
    toJSON(message: Market): unknown;
    create(base?: DeepPartial<Market>): Market;
    fromPartial(object: DeepPartial<Market>): Market;
};
export declare const StreamStrategyRequest: {
    encode(message: StreamStrategyRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamStrategyRequest;
    fromJSON(object: any): StreamStrategyRequest;
    toJSON(message: StreamStrategyRequest): unknown;
    create(base?: DeepPartial<StreamStrategyRequest>): StreamStrategyRequest;
    fromPartial(object: DeepPartial<StreamStrategyRequest>): StreamStrategyRequest;
};
export declare const StreamStrategyResponse: {
    encode(message: StreamStrategyResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamStrategyResponse;
    fromJSON(object: any): StreamStrategyResponse;
    toJSON(message: StreamStrategyResponse): unknown;
    create(base?: DeepPartial<StreamStrategyResponse>): StreamStrategyResponse;
    fromPartial(object: DeepPartial<StreamStrategyResponse>): StreamStrategyResponse;
};
/**
 * InjectiveTradingStrategiesRPC defined a gRPC service for Injective Trading
 * Strategies.
 */
export interface InjectiveTradingRPC {
    /** Lists all trading strategies */
    ListTradingStrategies(request: DeepPartial<ListTradingStrategiesRequest>, metadata?: grpc.Metadata): Promise<ListTradingStrategiesResponse>;
    /** GetStats returns global statistics in the last 24hs */
    GetTradingStats(request: DeepPartial<GetTradingStatsRequest>, metadata?: grpc.Metadata): Promise<GetTradingStatsResponse>;
    /** StreamStrategy streams the trading strategies on state change */
    StreamStrategy(request: DeepPartial<StreamStrategyRequest>, metadata?: grpc.Metadata): Observable<StreamStrategyResponse>;
}
export declare class InjectiveTradingRPCClientImpl implements InjectiveTradingRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    ListTradingStrategies(request: DeepPartial<ListTradingStrategiesRequest>, metadata?: grpc.Metadata): Promise<ListTradingStrategiesResponse>;
    GetTradingStats(request: DeepPartial<GetTradingStatsRequest>, metadata?: grpc.Metadata): Promise<GetTradingStatsResponse>;
    StreamStrategy(request: DeepPartial<StreamStrategyRequest>, metadata?: grpc.Metadata): Observable<StreamStrategyResponse>;
}
export declare const InjectiveTradingRPCDesc: {
    serviceName: string;
};
export declare const InjectiveTradingRPCListTradingStrategiesDesc: UnaryMethodDefinitionish;
export declare const InjectiveTradingRPCGetTradingStatsDesc: UnaryMethodDefinitionish;
export declare const InjectiveTradingRPCStreamStrategyDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        streamingTransport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
