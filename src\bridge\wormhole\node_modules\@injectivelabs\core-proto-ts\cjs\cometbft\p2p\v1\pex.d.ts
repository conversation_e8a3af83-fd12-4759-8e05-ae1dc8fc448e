import _m0 from "protobufjs/minimal.js";
import { NetAddress } from "./types";
export declare const protobufPackage = "cometbft.p2p.v1";
/** PexRequest is a request for peer addresses. */
export interface PexRequest {
}
/** PexAddrs is a response with peer addresses. */
export interface PexAddrs {
    addrs: NetAddress[];
}
/** Message is an abstract PEX message. */
export interface Message {
    pexRequest?: PexRequest | undefined;
    pexAddrs?: PexAddrs | undefined;
}
export declare const PexRequest: {
    encode(_: PexRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PexRequest;
    fromJSON(_: any): PexRequest;
    toJSON(_: PexRequest): unknown;
    create(base?: DeepPartial<PexRequest>): PexRequest;
    fromPartial(_: DeepPartial<PexRequest>): PexRequest;
};
export declare const PexAddrs: {
    encode(message: <PERSON><PERSON><PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PexAddrs;
    fromJSON(object: any): PexAddrs;
    toJSON(message: PexAddrs): unknown;
    create(base?: DeepPartial<PexAddrs>): PexAddrs;
    fromPartial(object: DeepPartial<PexAddrs>): PexAddrs;
};
export declare const Message: {
    encode(message: Message, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Message;
    fromJSON(object: any): Message;
    toJSON(message: Message): unknown;
    create(base?: DeepPartial<Message>): Message;
    fromPartial(object: DeepPartial<Message>): Message;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
