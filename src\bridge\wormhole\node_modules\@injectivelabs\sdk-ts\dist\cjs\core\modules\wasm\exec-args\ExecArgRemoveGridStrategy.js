"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgRemoveGridStrategy extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgRemoveGridStrategy(params);
    }
    toData() {
        const { params } = this;
        return {
            subaccount_id: params.subaccountId,
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('remove_strategy', this.toData());
    }
}
exports.default = ExecArgRemoveGridStrategy;
