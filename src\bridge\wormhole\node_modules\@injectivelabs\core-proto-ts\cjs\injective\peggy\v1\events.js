"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Withdrawal = exports.EventWithdrawalsCompleted = exports.EventDepositReceived = exports.EventValidatorSlash = exports.EventSubmitBadSignatureEvidence = exports.EventCancelSendToEth = exports.EventValsetUpdateClaim = exports.EventERC20DeployedClaim = exports.EventWithdrawClaim = exports.EventDepositClaim = exports.EventAttestationVote = exports.EventConfirmBatch = exports.EventSendToEth = exports.EventValsetConfirm = exports.EventSetOrchestratorAddresses = exports.EventValsetUpdateRequest = exports.EventOutgoingBatchCanceled = exports.EventOutgoingBatch = exports.EventBridgeWithdrawCanceled = exports.EventAttestationObserved = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var attestation_1 = require("./attestation.js");
var types_1 = require("./types.js");
exports.protobufPackage = "injective.peggy.v1";
function createBaseEventAttestationObserved() {
    return { attestationType: 0, bridgeContract: "", bridgeChainId: "0", attestationId: new Uint8Array(), nonce: "0" };
}
exports.EventAttestationObserved = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.attestationType !== 0) {
            writer.uint32(8).int32(message.attestationType);
        }
        if (message.bridgeContract !== "") {
            writer.uint32(18).string(message.bridgeContract);
        }
        if (message.bridgeChainId !== "0") {
            writer.uint32(24).uint64(message.bridgeChainId);
        }
        if (message.attestationId.length !== 0) {
            writer.uint32(34).bytes(message.attestationId);
        }
        if (message.nonce !== "0") {
            writer.uint32(40).uint64(message.nonce);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAttestationObserved();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.attestationType = reader.int32();
                    break;
                case 2:
                    message.bridgeContract = reader.string();
                    break;
                case 3:
                    message.bridgeChainId = longToString(reader.uint64());
                    break;
                case 4:
                    message.attestationId = reader.bytes();
                    break;
                case 5:
                    message.nonce = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            attestationType: isSet(object.attestationType) ? (0, attestation_1.claimTypeFromJSON)(object.attestationType) : 0,
            bridgeContract: isSet(object.bridgeContract) ? String(object.bridgeContract) : "",
            bridgeChainId: isSet(object.bridgeChainId) ? String(object.bridgeChainId) : "0",
            attestationId: isSet(object.attestationId) ? bytesFromBase64(object.attestationId) : new Uint8Array(),
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.attestationType !== undefined && (obj.attestationType = (0, attestation_1.claimTypeToJSON)(message.attestationType));
        message.bridgeContract !== undefined && (obj.bridgeContract = message.bridgeContract);
        message.bridgeChainId !== undefined && (obj.bridgeChainId = message.bridgeChainId);
        message.attestationId !== undefined &&
            (obj.attestationId = base64FromBytes(message.attestationId !== undefined ? message.attestationId : new Uint8Array()));
        message.nonce !== undefined && (obj.nonce = message.nonce);
        return obj;
    },
    create: function (base) {
        return exports.EventAttestationObserved.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseEventAttestationObserved();
        message.attestationType = (_a = object.attestationType) !== null && _a !== void 0 ? _a : 0;
        message.bridgeContract = (_b = object.bridgeContract) !== null && _b !== void 0 ? _b : "";
        message.bridgeChainId = (_c = object.bridgeChainId) !== null && _c !== void 0 ? _c : "0";
        message.attestationId = (_d = object.attestationId) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.nonce = (_e = object.nonce) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBaseEventBridgeWithdrawCanceled() {
    return { bridgeContract: "", bridgeChainId: "0" };
}
exports.EventBridgeWithdrawCanceled = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bridgeContract !== "") {
            writer.uint32(10).string(message.bridgeContract);
        }
        if (message.bridgeChainId !== "0") {
            writer.uint32(16).uint64(message.bridgeChainId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBridgeWithdrawCanceled();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bridgeContract = reader.string();
                    break;
                case 2:
                    message.bridgeChainId = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bridgeContract: isSet(object.bridgeContract) ? String(object.bridgeContract) : "",
            bridgeChainId: isSet(object.bridgeChainId) ? String(object.bridgeChainId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bridgeContract !== undefined && (obj.bridgeContract = message.bridgeContract);
        message.bridgeChainId !== undefined && (obj.bridgeChainId = message.bridgeChainId);
        return obj;
    },
    create: function (base) {
        return exports.EventBridgeWithdrawCanceled.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventBridgeWithdrawCanceled();
        message.bridgeContract = (_a = object.bridgeContract) !== null && _a !== void 0 ? _a : "";
        message.bridgeChainId = (_b = object.bridgeChainId) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseEventOutgoingBatch() {
    return { denom: "", orchestratorAddress: "", batchNonce: "0", batchTimeout: "0", batchTxIds: [] };
}
exports.EventOutgoingBatch = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(18).string(message.orchestratorAddress);
        }
        if (message.batchNonce !== "0") {
            writer.uint32(24).uint64(message.batchNonce);
        }
        if (message.batchTimeout !== "0") {
            writer.uint32(32).uint64(message.batchTimeout);
        }
        writer.uint32(42).fork();
        try {
            for (var _b = __values(message.batchTxIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint64(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventOutgoingBatch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.orchestratorAddress = reader.string();
                    break;
                case 3:
                    message.batchNonce = longToString(reader.uint64());
                    break;
                case 4:
                    message.batchTimeout = longToString(reader.uint64());
                    break;
                case 5:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.batchTxIds.push(longToString(reader.uint64()));
                        }
                    }
                    else {
                        message.batchTxIds.push(longToString(reader.uint64()));
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
            batchNonce: isSet(object.batchNonce) ? String(object.batchNonce) : "0",
            batchTimeout: isSet(object.batchTimeout) ? String(object.batchTimeout) : "0",
            batchTxIds: Array.isArray(object === null || object === void 0 ? void 0 : object.batchTxIds) ? object.batchTxIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        message.batchNonce !== undefined && (obj.batchNonce = message.batchNonce);
        message.batchTimeout !== undefined && (obj.batchTimeout = message.batchTimeout);
        if (message.batchTxIds) {
            obj.batchTxIds = message.batchTxIds.map(function (e) { return e; });
        }
        else {
            obj.batchTxIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventOutgoingBatch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseEventOutgoingBatch();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.orchestratorAddress = (_b = object.orchestratorAddress) !== null && _b !== void 0 ? _b : "";
        message.batchNonce = (_c = object.batchNonce) !== null && _c !== void 0 ? _c : "0";
        message.batchTimeout = (_d = object.batchTimeout) !== null && _d !== void 0 ? _d : "0";
        message.batchTxIds = ((_e = object.batchTxIds) === null || _e === void 0 ? void 0 : _e.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseEventOutgoingBatchCanceled() {
    return { bridgeContract: "", bridgeChainId: "0", batchId: "0", nonce: "0" };
}
exports.EventOutgoingBatchCanceled = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bridgeContract !== "") {
            writer.uint32(10).string(message.bridgeContract);
        }
        if (message.bridgeChainId !== "0") {
            writer.uint32(16).uint64(message.bridgeChainId);
        }
        if (message.batchId !== "0") {
            writer.uint32(24).uint64(message.batchId);
        }
        if (message.nonce !== "0") {
            writer.uint32(32).uint64(message.nonce);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventOutgoingBatchCanceled();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bridgeContract = reader.string();
                    break;
                case 2:
                    message.bridgeChainId = longToString(reader.uint64());
                    break;
                case 3:
                    message.batchId = longToString(reader.uint64());
                    break;
                case 4:
                    message.nonce = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bridgeContract: isSet(object.bridgeContract) ? String(object.bridgeContract) : "",
            bridgeChainId: isSet(object.bridgeChainId) ? String(object.bridgeChainId) : "0",
            batchId: isSet(object.batchId) ? String(object.batchId) : "0",
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bridgeContract !== undefined && (obj.bridgeContract = message.bridgeContract);
        message.bridgeChainId !== undefined && (obj.bridgeChainId = message.bridgeChainId);
        message.batchId !== undefined && (obj.batchId = message.batchId);
        message.nonce !== undefined && (obj.nonce = message.nonce);
        return obj;
    },
    create: function (base) {
        return exports.EventOutgoingBatchCanceled.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEventOutgoingBatchCanceled();
        message.bridgeContract = (_a = object.bridgeContract) !== null && _a !== void 0 ? _a : "";
        message.bridgeChainId = (_b = object.bridgeChainId) !== null && _b !== void 0 ? _b : "0";
        message.batchId = (_c = object.batchId) !== null && _c !== void 0 ? _c : "0";
        message.nonce = (_d = object.nonce) !== null && _d !== void 0 ? _d : "0";
        return message;
    },
};
function createBaseEventValsetUpdateRequest() {
    return { valsetNonce: "0", valsetHeight: "0", valsetMembers: [], rewardAmount: "", rewardToken: "" };
}
exports.EventValsetUpdateRequest = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.valsetNonce !== "0") {
            writer.uint32(8).uint64(message.valsetNonce);
        }
        if (message.valsetHeight !== "0") {
            writer.uint32(16).uint64(message.valsetHeight);
        }
        try {
            for (var _b = __values(message.valsetMembers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.BridgeValidator.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (message.rewardAmount !== "") {
            writer.uint32(34).string(message.rewardAmount);
        }
        if (message.rewardToken !== "") {
            writer.uint32(42).string(message.rewardToken);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventValsetUpdateRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.valsetNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.valsetHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.valsetMembers.push(types_1.BridgeValidator.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.rewardAmount = reader.string();
                    break;
                case 5:
                    message.rewardToken = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            valsetNonce: isSet(object.valsetNonce) ? String(object.valsetNonce) : "0",
            valsetHeight: isSet(object.valsetHeight) ? String(object.valsetHeight) : "0",
            valsetMembers: Array.isArray(object === null || object === void 0 ? void 0 : object.valsetMembers)
                ? object.valsetMembers.map(function (e) { return types_1.BridgeValidator.fromJSON(e); })
                : [],
            rewardAmount: isSet(object.rewardAmount) ? String(object.rewardAmount) : "",
            rewardToken: isSet(object.rewardToken) ? String(object.rewardToken) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.valsetNonce !== undefined && (obj.valsetNonce = message.valsetNonce);
        message.valsetHeight !== undefined && (obj.valsetHeight = message.valsetHeight);
        if (message.valsetMembers) {
            obj.valsetMembers = message.valsetMembers.map(function (e) { return e ? types_1.BridgeValidator.toJSON(e) : undefined; });
        }
        else {
            obj.valsetMembers = [];
        }
        message.rewardAmount !== undefined && (obj.rewardAmount = message.rewardAmount);
        message.rewardToken !== undefined && (obj.rewardToken = message.rewardToken);
        return obj;
    },
    create: function (base) {
        return exports.EventValsetUpdateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseEventValsetUpdateRequest();
        message.valsetNonce = (_a = object.valsetNonce) !== null && _a !== void 0 ? _a : "0";
        message.valsetHeight = (_b = object.valsetHeight) !== null && _b !== void 0 ? _b : "0";
        message.valsetMembers = ((_c = object.valsetMembers) === null || _c === void 0 ? void 0 : _c.map(function (e) { return types_1.BridgeValidator.fromPartial(e); })) || [];
        message.rewardAmount = (_d = object.rewardAmount) !== null && _d !== void 0 ? _d : "";
        message.rewardToken = (_e = object.rewardToken) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseEventSetOrchestratorAddresses() {
    return { validatorAddress: "", orchestratorAddress: "", operatorEthAddress: "" };
}
exports.EventSetOrchestratorAddresses = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validatorAddress !== "") {
            writer.uint32(10).string(message.validatorAddress);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(18).string(message.orchestratorAddress);
        }
        if (message.operatorEthAddress !== "") {
            writer.uint32(26).string(message.operatorEthAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSetOrchestratorAddresses();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validatorAddress = reader.string();
                    break;
                case 2:
                    message.orchestratorAddress = reader.string();
                    break;
                case 3:
                    message.operatorEthAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validatorAddress: isSet(object.validatorAddress) ? String(object.validatorAddress) : "",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
            operatorEthAddress: isSet(object.operatorEthAddress) ? String(object.operatorEthAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.validatorAddress !== undefined && (obj.validatorAddress = message.validatorAddress);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        message.operatorEthAddress !== undefined && (obj.operatorEthAddress = message.operatorEthAddress);
        return obj;
    },
    create: function (base) {
        return exports.EventSetOrchestratorAddresses.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventSetOrchestratorAddresses();
        message.validatorAddress = (_a = object.validatorAddress) !== null && _a !== void 0 ? _a : "";
        message.orchestratorAddress = (_b = object.orchestratorAddress) !== null && _b !== void 0 ? _b : "";
        message.operatorEthAddress = (_c = object.operatorEthAddress) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseEventValsetConfirm() {
    return { valsetNonce: "0", orchestratorAddress: "" };
}
exports.EventValsetConfirm = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.valsetNonce !== "0") {
            writer.uint32(8).uint64(message.valsetNonce);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(18).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventValsetConfirm();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.valsetNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            valsetNonce: isSet(object.valsetNonce) ? String(object.valsetNonce) : "0",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.valsetNonce !== undefined && (obj.valsetNonce = message.valsetNonce);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.EventValsetConfirm.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventValsetConfirm();
        message.valsetNonce = (_a = object.valsetNonce) !== null && _a !== void 0 ? _a : "0";
        message.orchestratorAddress = (_b = object.orchestratorAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseEventSendToEth() {
    return { outgoingTxId: "0", sender: "", receiver: "", amount: "", bridgeFee: "" };
}
exports.EventSendToEth = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.outgoingTxId !== "0") {
            writer.uint32(8).uint64(message.outgoingTxId);
        }
        if (message.sender !== "") {
            writer.uint32(18).string(message.sender);
        }
        if (message.receiver !== "") {
            writer.uint32(26).string(message.receiver);
        }
        if (message.amount !== "") {
            writer.uint32(34).string(message.amount);
        }
        if (message.bridgeFee !== "") {
            writer.uint32(42).string(message.bridgeFee);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSendToEth();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.outgoingTxId = longToString(reader.uint64());
                    break;
                case 2:
                    message.sender = reader.string();
                    break;
                case 3:
                    message.receiver = reader.string();
                    break;
                case 4:
                    message.amount = reader.string();
                    break;
                case 5:
                    message.bridgeFee = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            outgoingTxId: isSet(object.outgoingTxId) ? String(object.outgoingTxId) : "0",
            sender: isSet(object.sender) ? String(object.sender) : "",
            receiver: isSet(object.receiver) ? String(object.receiver) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            bridgeFee: isSet(object.bridgeFee) ? String(object.bridgeFee) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.outgoingTxId !== undefined && (obj.outgoingTxId = message.outgoingTxId);
        message.sender !== undefined && (obj.sender = message.sender);
        message.receiver !== undefined && (obj.receiver = message.receiver);
        message.amount !== undefined && (obj.amount = message.amount);
        message.bridgeFee !== undefined && (obj.bridgeFee = message.bridgeFee);
        return obj;
    },
    create: function (base) {
        return exports.EventSendToEth.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseEventSendToEth();
        message.outgoingTxId = (_a = object.outgoingTxId) !== null && _a !== void 0 ? _a : "0";
        message.sender = (_b = object.sender) !== null && _b !== void 0 ? _b : "";
        message.receiver = (_c = object.receiver) !== null && _c !== void 0 ? _c : "";
        message.amount = (_d = object.amount) !== null && _d !== void 0 ? _d : "";
        message.bridgeFee = (_e = object.bridgeFee) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseEventConfirmBatch() {
    return { batchNonce: "0", orchestratorAddress: "" };
}
exports.EventConfirmBatch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.batchNonce !== "0") {
            writer.uint32(8).uint64(message.batchNonce);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(18).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventConfirmBatch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.batchNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            batchNonce: isSet(object.batchNonce) ? String(object.batchNonce) : "0",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.batchNonce !== undefined && (obj.batchNonce = message.batchNonce);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.EventConfirmBatch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventConfirmBatch();
        message.batchNonce = (_a = object.batchNonce) !== null && _a !== void 0 ? _a : "0";
        message.orchestratorAddress = (_b = object.orchestratorAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseEventAttestationVote() {
    return { eventNonce: "0", attestationId: new Uint8Array(), voter: "" };
}
exports.EventAttestationVote = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.attestationId.length !== 0) {
            writer.uint32(18).bytes(message.attestationId);
        }
        if (message.voter !== "") {
            writer.uint32(26).string(message.voter);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAttestationVote();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.attestationId = reader.bytes();
                    break;
                case 3:
                    message.voter = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            attestationId: isSet(object.attestationId) ? bytesFromBase64(object.attestationId) : new Uint8Array(),
            voter: isSet(object.voter) ? String(object.voter) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.attestationId !== undefined &&
            (obj.attestationId = base64FromBytes(message.attestationId !== undefined ? message.attestationId : new Uint8Array()));
        message.voter !== undefined && (obj.voter = message.voter);
        return obj;
    },
    create: function (base) {
        return exports.EventAttestationVote.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventAttestationVote();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.attestationId = (_b = object.attestationId) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.voter = (_c = object.voter) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseEventDepositClaim() {
    return {
        eventNonce: "0",
        eventHeight: "0",
        attestationId: new Uint8Array(),
        ethereumSender: "",
        cosmosReceiver: "",
        tokenContract: "",
        amount: "",
        orchestratorAddress: "",
        data: "",
    };
}
exports.EventDepositClaim = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.eventHeight !== "0") {
            writer.uint32(16).uint64(message.eventHeight);
        }
        if (message.attestationId.length !== 0) {
            writer.uint32(26).bytes(message.attestationId);
        }
        if (message.ethereumSender !== "") {
            writer.uint32(34).string(message.ethereumSender);
        }
        if (message.cosmosReceiver !== "") {
            writer.uint32(42).string(message.cosmosReceiver);
        }
        if (message.tokenContract !== "") {
            writer.uint32(50).string(message.tokenContract);
        }
        if (message.amount !== "") {
            writer.uint32(58).string(message.amount);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(66).string(message.orchestratorAddress);
        }
        if (message.data !== "") {
            writer.uint32(74).string(message.data);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventDepositClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.eventHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.attestationId = reader.bytes();
                    break;
                case 4:
                    message.ethereumSender = reader.string();
                    break;
                case 5:
                    message.cosmosReceiver = reader.string();
                    break;
                case 6:
                    message.tokenContract = reader.string();
                    break;
                case 7:
                    message.amount = reader.string();
                    break;
                case 8:
                    message.orchestratorAddress = reader.string();
                    break;
                case 9:
                    message.data = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            eventHeight: isSet(object.eventHeight) ? String(object.eventHeight) : "0",
            attestationId: isSet(object.attestationId) ? bytesFromBase64(object.attestationId) : new Uint8Array(),
            ethereumSender: isSet(object.ethereumSender) ? String(object.ethereumSender) : "",
            cosmosReceiver: isSet(object.cosmosReceiver) ? String(object.cosmosReceiver) : "",
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
            data: isSet(object.data) ? String(object.data) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.eventHeight !== undefined && (obj.eventHeight = message.eventHeight);
        message.attestationId !== undefined &&
            (obj.attestationId = base64FromBytes(message.attestationId !== undefined ? message.attestationId : new Uint8Array()));
        message.ethereumSender !== undefined && (obj.ethereumSender = message.ethereumSender);
        message.cosmosReceiver !== undefined && (obj.cosmosReceiver = message.cosmosReceiver);
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.amount !== undefined && (obj.amount = message.amount);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        message.data !== undefined && (obj.data = message.data);
        return obj;
    },
    create: function (base) {
        return exports.EventDepositClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseEventDepositClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.eventHeight = (_b = object.eventHeight) !== null && _b !== void 0 ? _b : "0";
        message.attestationId = (_c = object.attestationId) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.ethereumSender = (_d = object.ethereumSender) !== null && _d !== void 0 ? _d : "";
        message.cosmosReceiver = (_e = object.cosmosReceiver) !== null && _e !== void 0 ? _e : "";
        message.tokenContract = (_f = object.tokenContract) !== null && _f !== void 0 ? _f : "";
        message.amount = (_g = object.amount) !== null && _g !== void 0 ? _g : "";
        message.orchestratorAddress = (_h = object.orchestratorAddress) !== null && _h !== void 0 ? _h : "";
        message.data = (_j = object.data) !== null && _j !== void 0 ? _j : "";
        return message;
    },
};
function createBaseEventWithdrawClaim() {
    return {
        eventNonce: "0",
        eventHeight: "0",
        attestationId: new Uint8Array(),
        batchNonce: "0",
        tokenContract: "",
        orchestratorAddress: "",
    };
}
exports.EventWithdrawClaim = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.eventHeight !== "0") {
            writer.uint32(16).uint64(message.eventHeight);
        }
        if (message.attestationId.length !== 0) {
            writer.uint32(26).bytes(message.attestationId);
        }
        if (message.batchNonce !== "0") {
            writer.uint32(32).uint64(message.batchNonce);
        }
        if (message.tokenContract !== "") {
            writer.uint32(42).string(message.tokenContract);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(50).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventWithdrawClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.eventHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.attestationId = reader.bytes();
                    break;
                case 4:
                    message.batchNonce = longToString(reader.uint64());
                    break;
                case 5:
                    message.tokenContract = reader.string();
                    break;
                case 6:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            eventHeight: isSet(object.eventHeight) ? String(object.eventHeight) : "0",
            attestationId: isSet(object.attestationId) ? bytesFromBase64(object.attestationId) : new Uint8Array(),
            batchNonce: isSet(object.batchNonce) ? String(object.batchNonce) : "0",
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.eventHeight !== undefined && (obj.eventHeight = message.eventHeight);
        message.attestationId !== undefined &&
            (obj.attestationId = base64FromBytes(message.attestationId !== undefined ? message.attestationId : new Uint8Array()));
        message.batchNonce !== undefined && (obj.batchNonce = message.batchNonce);
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.EventWithdrawClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseEventWithdrawClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.eventHeight = (_b = object.eventHeight) !== null && _b !== void 0 ? _b : "0";
        message.attestationId = (_c = object.attestationId) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.batchNonce = (_d = object.batchNonce) !== null && _d !== void 0 ? _d : "0";
        message.tokenContract = (_e = object.tokenContract) !== null && _e !== void 0 ? _e : "";
        message.orchestratorAddress = (_f = object.orchestratorAddress) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseEventERC20DeployedClaim() {
    return {
        eventNonce: "0",
        eventHeight: "0",
        attestationId: new Uint8Array(),
        cosmosDenom: "",
        tokenContract: "",
        name: "",
        symbol: "",
        decimals: "0",
        orchestratorAddress: "",
    };
}
exports.EventERC20DeployedClaim = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.eventHeight !== "0") {
            writer.uint32(16).uint64(message.eventHeight);
        }
        if (message.attestationId.length !== 0) {
            writer.uint32(26).bytes(message.attestationId);
        }
        if (message.cosmosDenom !== "") {
            writer.uint32(34).string(message.cosmosDenom);
        }
        if (message.tokenContract !== "") {
            writer.uint32(42).string(message.tokenContract);
        }
        if (message.name !== "") {
            writer.uint32(50).string(message.name);
        }
        if (message.symbol !== "") {
            writer.uint32(58).string(message.symbol);
        }
        if (message.decimals !== "0") {
            writer.uint32(64).uint64(message.decimals);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(74).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventERC20DeployedClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.eventHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.attestationId = reader.bytes();
                    break;
                case 4:
                    message.cosmosDenom = reader.string();
                    break;
                case 5:
                    message.tokenContract = reader.string();
                    break;
                case 6:
                    message.name = reader.string();
                    break;
                case 7:
                    message.symbol = reader.string();
                    break;
                case 8:
                    message.decimals = longToString(reader.uint64());
                    break;
                case 9:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            eventHeight: isSet(object.eventHeight) ? String(object.eventHeight) : "0",
            attestationId: isSet(object.attestationId) ? bytesFromBase64(object.attestationId) : new Uint8Array(),
            cosmosDenom: isSet(object.cosmosDenom) ? String(object.cosmosDenom) : "",
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            name: isSet(object.name) ? String(object.name) : "",
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            decimals: isSet(object.decimals) ? String(object.decimals) : "0",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.eventHeight !== undefined && (obj.eventHeight = message.eventHeight);
        message.attestationId !== undefined &&
            (obj.attestationId = base64FromBytes(message.attestationId !== undefined ? message.attestationId : new Uint8Array()));
        message.cosmosDenom !== undefined && (obj.cosmosDenom = message.cosmosDenom);
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.name !== undefined && (obj.name = message.name);
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.decimals !== undefined && (obj.decimals = message.decimals);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.EventERC20DeployedClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseEventERC20DeployedClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.eventHeight = (_b = object.eventHeight) !== null && _b !== void 0 ? _b : "0";
        message.attestationId = (_c = object.attestationId) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.cosmosDenom = (_d = object.cosmosDenom) !== null && _d !== void 0 ? _d : "";
        message.tokenContract = (_e = object.tokenContract) !== null && _e !== void 0 ? _e : "";
        message.name = (_f = object.name) !== null && _f !== void 0 ? _f : "";
        message.symbol = (_g = object.symbol) !== null && _g !== void 0 ? _g : "";
        message.decimals = (_h = object.decimals) !== null && _h !== void 0 ? _h : "0";
        message.orchestratorAddress = (_j = object.orchestratorAddress) !== null && _j !== void 0 ? _j : "";
        return message;
    },
};
function createBaseEventValsetUpdateClaim() {
    return {
        eventNonce: "0",
        eventHeight: "0",
        attestationId: new Uint8Array(),
        valsetNonce: "0",
        valsetMembers: [],
        rewardAmount: "",
        rewardToken: "",
        orchestratorAddress: "",
    };
}
exports.EventValsetUpdateClaim = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.eventHeight !== "0") {
            writer.uint32(16).uint64(message.eventHeight);
        }
        if (message.attestationId.length !== 0) {
            writer.uint32(26).bytes(message.attestationId);
        }
        if (message.valsetNonce !== "0") {
            writer.uint32(32).uint64(message.valsetNonce);
        }
        try {
            for (var _b = __values(message.valsetMembers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.BridgeValidator.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (message.rewardAmount !== "") {
            writer.uint32(50).string(message.rewardAmount);
        }
        if (message.rewardToken !== "") {
            writer.uint32(58).string(message.rewardToken);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(66).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventValsetUpdateClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.eventHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.attestationId = reader.bytes();
                    break;
                case 4:
                    message.valsetNonce = longToString(reader.uint64());
                    break;
                case 5:
                    message.valsetMembers.push(types_1.BridgeValidator.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.rewardAmount = reader.string();
                    break;
                case 7:
                    message.rewardToken = reader.string();
                    break;
                case 8:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            eventHeight: isSet(object.eventHeight) ? String(object.eventHeight) : "0",
            attestationId: isSet(object.attestationId) ? bytesFromBase64(object.attestationId) : new Uint8Array(),
            valsetNonce: isSet(object.valsetNonce) ? String(object.valsetNonce) : "0",
            valsetMembers: Array.isArray(object === null || object === void 0 ? void 0 : object.valsetMembers)
                ? object.valsetMembers.map(function (e) { return types_1.BridgeValidator.fromJSON(e); })
                : [],
            rewardAmount: isSet(object.rewardAmount) ? String(object.rewardAmount) : "",
            rewardToken: isSet(object.rewardToken) ? String(object.rewardToken) : "",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.eventHeight !== undefined && (obj.eventHeight = message.eventHeight);
        message.attestationId !== undefined &&
            (obj.attestationId = base64FromBytes(message.attestationId !== undefined ? message.attestationId : new Uint8Array()));
        message.valsetNonce !== undefined && (obj.valsetNonce = message.valsetNonce);
        if (message.valsetMembers) {
            obj.valsetMembers = message.valsetMembers.map(function (e) { return e ? types_1.BridgeValidator.toJSON(e) : undefined; });
        }
        else {
            obj.valsetMembers = [];
        }
        message.rewardAmount !== undefined && (obj.rewardAmount = message.rewardAmount);
        message.rewardToken !== undefined && (obj.rewardToken = message.rewardToken);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.EventValsetUpdateClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseEventValsetUpdateClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.eventHeight = (_b = object.eventHeight) !== null && _b !== void 0 ? _b : "0";
        message.attestationId = (_c = object.attestationId) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.valsetNonce = (_d = object.valsetNonce) !== null && _d !== void 0 ? _d : "0";
        message.valsetMembers = ((_e = object.valsetMembers) === null || _e === void 0 ? void 0 : _e.map(function (e) { return types_1.BridgeValidator.fromPartial(e); })) || [];
        message.rewardAmount = (_f = object.rewardAmount) !== null && _f !== void 0 ? _f : "";
        message.rewardToken = (_g = object.rewardToken) !== null && _g !== void 0 ? _g : "";
        message.orchestratorAddress = (_h = object.orchestratorAddress) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseEventCancelSendToEth() {
    return { outgoingTxId: "0" };
}
exports.EventCancelSendToEth = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.outgoingTxId !== "0") {
            writer.uint32(8).uint64(message.outgoingTxId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventCancelSendToEth();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.outgoingTxId = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { outgoingTxId: isSet(object.outgoingTxId) ? String(object.outgoingTxId) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.outgoingTxId !== undefined && (obj.outgoingTxId = message.outgoingTxId);
        return obj;
    },
    create: function (base) {
        return exports.EventCancelSendToEth.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventCancelSendToEth();
        message.outgoingTxId = (_a = object.outgoingTxId) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseEventSubmitBadSignatureEvidence() {
    return { badEthSignature: "", badEthSignatureSubject: "" };
}
exports.EventSubmitBadSignatureEvidence = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.badEthSignature !== "") {
            writer.uint32(10).string(message.badEthSignature);
        }
        if (message.badEthSignatureSubject !== "") {
            writer.uint32(18).string(message.badEthSignatureSubject);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSubmitBadSignatureEvidence();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.badEthSignature = reader.string();
                    break;
                case 2:
                    message.badEthSignatureSubject = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            badEthSignature: isSet(object.badEthSignature) ? String(object.badEthSignature) : "",
            badEthSignatureSubject: isSet(object.badEthSignatureSubject) ? String(object.badEthSignatureSubject) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.badEthSignature !== undefined && (obj.badEthSignature = message.badEthSignature);
        message.badEthSignatureSubject !== undefined && (obj.badEthSignatureSubject = message.badEthSignatureSubject);
        return obj;
    },
    create: function (base) {
        return exports.EventSubmitBadSignatureEvidence.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventSubmitBadSignatureEvidence();
        message.badEthSignature = (_a = object.badEthSignature) !== null && _a !== void 0 ? _a : "";
        message.badEthSignatureSubject = (_b = object.badEthSignatureSubject) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseEventValidatorSlash() {
    return { power: "0", reason: "", consensusAddress: "", operatorAddress: "", moniker: "" };
}
exports.EventValidatorSlash = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.power !== "0") {
            writer.uint32(8).int64(message.power);
        }
        if (message.reason !== "") {
            writer.uint32(18).string(message.reason);
        }
        if (message.consensusAddress !== "") {
            writer.uint32(26).string(message.consensusAddress);
        }
        if (message.operatorAddress !== "") {
            writer.uint32(34).string(message.operatorAddress);
        }
        if (message.moniker !== "") {
            writer.uint32(42).string(message.moniker);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventValidatorSlash();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.power = longToString(reader.int64());
                    break;
                case 2:
                    message.reason = reader.string();
                    break;
                case 3:
                    message.consensusAddress = reader.string();
                    break;
                case 4:
                    message.operatorAddress = reader.string();
                    break;
                case 5:
                    message.moniker = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            power: isSet(object.power) ? String(object.power) : "0",
            reason: isSet(object.reason) ? String(object.reason) : "",
            consensusAddress: isSet(object.consensusAddress) ? String(object.consensusAddress) : "",
            operatorAddress: isSet(object.operatorAddress) ? String(object.operatorAddress) : "",
            moniker: isSet(object.moniker) ? String(object.moniker) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.power !== undefined && (obj.power = message.power);
        message.reason !== undefined && (obj.reason = message.reason);
        message.consensusAddress !== undefined && (obj.consensusAddress = message.consensusAddress);
        message.operatorAddress !== undefined && (obj.operatorAddress = message.operatorAddress);
        message.moniker !== undefined && (obj.moniker = message.moniker);
        return obj;
    },
    create: function (base) {
        return exports.EventValidatorSlash.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseEventValidatorSlash();
        message.power = (_a = object.power) !== null && _a !== void 0 ? _a : "0";
        message.reason = (_b = object.reason) !== null && _b !== void 0 ? _b : "";
        message.consensusAddress = (_c = object.consensusAddress) !== null && _c !== void 0 ? _c : "";
        message.operatorAddress = (_d = object.operatorAddress) !== null && _d !== void 0 ? _d : "";
        message.moniker = (_e = object.moniker) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseEventDepositReceived() {
    return { sender: "", receiver: "", amount: "" };
}
exports.EventDepositReceived = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.receiver !== "") {
            writer.uint32(18).string(message.receiver);
        }
        if (message.amount !== "") {
            writer.uint32(26).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventDepositReceived();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.receiver = reader.string();
                    break;
                case 3:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            receiver: isSet(object.receiver) ? String(object.receiver) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.receiver !== undefined && (obj.receiver = message.receiver);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.EventDepositReceived.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventDepositReceived();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.receiver = (_b = object.receiver) !== null && _b !== void 0 ? _b : "";
        message.amount = (_c = object.amount) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseEventWithdrawalsCompleted() {
    return { denom: "", withdrawals: [] };
}
exports.EventWithdrawalsCompleted = {
    encode: function (message, writer) {
        var e_4, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        try {
            for (var _b = __values(message.withdrawals), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.Withdrawal.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventWithdrawalsCompleted();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.withdrawals.push(exports.Withdrawal.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            withdrawals: Array.isArray(object === null || object === void 0 ? void 0 : object.withdrawals) ? object.withdrawals.map(function (e) { return exports.Withdrawal.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        if (message.withdrawals) {
            obj.withdrawals = message.withdrawals.map(function (e) { return e ? exports.Withdrawal.toJSON(e) : undefined; });
        }
        else {
            obj.withdrawals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventWithdrawalsCompleted.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventWithdrawalsCompleted();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.withdrawals = ((_b = object.withdrawals) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.Withdrawal.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseWithdrawal() {
    return { sender: "", receiver: "", amount: "" };
}
exports.Withdrawal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.receiver !== "") {
            writer.uint32(18).string(message.receiver);
        }
        if (message.amount !== "") {
            writer.uint32(26).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseWithdrawal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.receiver = reader.string();
                    break;
                case 3:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            receiver: isSet(object.receiver) ? String(object.receiver) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.receiver !== undefined && (obj.receiver = message.receiver);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.Withdrawal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseWithdrawal();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.receiver = (_b = object.receiver) !== null && _b !== void 0 ? _b : "";
        message.amount = (_c = object.amount) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
