"""
订阅更新器
从订阅地址下载并解析代理节点信息
"""

import yaml
import requests
import base64
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse, parse_qs

class SubscriptionUpdater:
    """订阅更新器"""
    
    def __init__(self, config_path: str = "config/ip.yaml"):
        """
        初始化订阅更新器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)
        
        # 确保配置文件存在
        self._ensure_config_file()
        
    def _ensure_config_file(self):
        """确保配置文件存在"""
        if not self.config_path.exists():
            # 创建默认配置文件
            default_config = {
                'subscription': {
                    'url': "https://cdn.0be.xyz/api/userhome/rssusernodeext?subtype=0&subkey=060583b8bf8b4e34add50c086d56cb87&inServerType=-1&shownodesubinfo=1&usenodetype=0",
                    'update_interval': 1800,
                    'last_update': None
                },
                'local_proxy': {
                    'mixed_port': 7890,
                    'allow_lan': False,
                    'bind_address': '*',
                    'mode': 'global',
                    'log_level': 'info',
                    'ipv6': False,
                    'external_controller': '127.0.0.1:9090'
                },
                'dns': {
                    'enable': True,
                    'ipv6': False,
                    'default_nameserver': ['*********', '************'],
                    'enhanced_mode': 'fake-ip',
                    'fake_ip_range': '**********/16',
                    'use_hosts': True,
                    'nameserver': ['https://doh.pub/dns-query', 'https://dns.alidns.com/dns-query'],
                    'fallback': ['https://doh.dns.sb/dns-query', 'https://dns.cloudflare.com/dns-query', 'https://dns.twnic.tw/dns-query', 'tls://*******:853'],
                    'fallback_filter': {
                        'geoip': True,
                        'ipcidr': ['240.0.0.0/4', '0.0.0.0/32']
                    }
                },
                'rotation_config': {
                    'interval': 60,
                    'enabled': True,
                    'preferred_regions': ["香港", "台湾", "日本", "新加坡", "美国", "韩国"],
                    'excluded_nodes': [],
                    'test_timeout': 10,
                    'max_retries': 3
                },
                'proxies': []
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"创建默认配置文件: {self.config_path}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            self.logger.info("配置文件保存成功")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def _fetch_subscription(self, url: str) -> Optional[str]:
        """
        获取订阅内容
        
        Args:
            url: 订阅地址
            
        Returns:
            str: 订阅内容
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            self.logger.info(f"成功获取订阅内容，长度: {len(response.text)}")
            return response.text
            
        except Exception as e:
            self.logger.error(f"获取订阅内容失败: {e}")
            return None
    
    def _parse_shadowsocks_url(self, ss_url: str) -> Optional[Dict[str, Any]]:
        """
        解析Shadowsocks URL
        
        Args:
            ss_url: ss://开头的URL
            
        Returns:
            dict: 解析后的代理配置
        """
        try:
            if not ss_url.startswith('ss://'):
                return None
            
            # 移除ss://前缀
            encoded_part = ss_url[5:]
            
            # 处理URL中的@符号分割
            if '@' in encoded_part:
                # 新格式: ss://method:password@server:port#name
                auth_part, server_part = encoded_part.split('@', 1)
                
                # 解码认证部分
                try:
                    decoded_auth = base64.b64decode(auth_part + '==').decode('utf-8')
                    method, password = decoded_auth.split(':', 1)
                except:
                    # 如果base64解码失败，尝试直接解析
                    if ':' in auth_part:
                        method, password = auth_part.split(':', 1)
                    else:
                        return None
                
                # 解析服务器部分
                if '#' in server_part:
                    server_info, name = server_part.split('#', 1)
                    name = requests.utils.unquote(name)
                else:
                    server_info = server_part
                    name = "Unknown"
                
                if ':' in server_info:
                    server, port = server_info.rsplit(':', 1)
                    port = int(port)
                else:
                    return None
            else:
                # 旧格式: ss://base64encoded#name
                if '#' in encoded_part:
                    encoded_config, name = encoded_part.split('#', 1)
                    name = requests.utils.unquote(name)
                else:
                    encoded_config = encoded_part
                    name = "Unknown"
                
                # Base64解码
                try:
                    decoded = base64.b64decode(encoded_config + '==').decode('utf-8')
                except:
                    return None
                
                # 解析格式: method:password@server:port
                if '@' not in decoded:
                    return None
                
                auth_part, server_part = decoded.split('@', 1)
                method, password = auth_part.split(':', 1)
                server, port = server_part.rsplit(':', 1)
                port = int(port)
            
            return {
                'name': name,
                'type': 'ss',
                'server': server,
                'port': port,
                'cipher': method,
                'password': password,
                'udp': True
            }
            
        except Exception as e:
            self.logger.warning(f"解析Shadowsocks URL失败: {e}")
            return None
    
    def _parse_subscription_content(self, content: str) -> List[Dict[str, Any]]:
        """
        解析订阅内容
        
        Args:
            content: 订阅内容
            
        Returns:
            list: 代理配置列表
        """
        proxies = []
        
        try:
            # 尝试Base64解码
            try:
                decoded_content = base64.b64decode(content).decode('utf-8')
            except:
                decoded_content = content
            
            # 按行分割
            lines = decoded_content.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 解析Shadowsocks URL
                if line.startswith('ss://'):
                    proxy = self._parse_shadowsocks_url(line)
                    if proxy:
                        proxies.append(proxy)
                
                # 可以在这里添加其他协议的解析
                # elif line.startswith('vmess://'):
                #     proxy = self._parse_vmess_url(line)
                # elif line.startswith('trojan://'):
                #     proxy = self._parse_trojan_url(line)
            
            self.logger.info(f"解析完成，共获得 {len(proxies)} 个代理节点")
            return proxies
            
        except Exception as e:
            self.logger.error(f"解析订阅内容失败: {e}")
            return []
    
    def update_subscription(self) -> bool:
        """
        更新订阅
        
        Returns:
            bool: 更新是否成功
        """
        config = self._load_config()
        if not config:
            return False
        
        subscription_config = config.get('subscription', {})
        url = subscription_config.get('url')
        
        if not url:
            self.logger.error("配置文件中未找到订阅地址")
            return False
        
        self.logger.info(f"开始更新订阅: {url}")
        
        # 获取订阅内容
        content = self._fetch_subscription(url)
        if not content:
            return False
        
        # 解析代理节点
        proxies = self._parse_subscription_content(content)
        if not proxies:
            self.logger.error("未能解析到任何代理节点")
            return False
        
        # 更新配置
        config['proxies'] = proxies
        config['subscription']['last_update'] = datetime.now().isoformat()
        
        # 保存配置
        self._save_config(config)
        
        self.logger.info(f"订阅更新成功，共 {len(proxies)} 个节点")
        return True
    
    def get_proxy_count(self) -> int:
        """
        获取代理节点数量
        
        Returns:
            int: 节点数量
        """
        config = self._load_config()
        return len(config.get('proxies', []))
    
    def get_last_update_time(self) -> Optional[str]:
        """
        获取上次更新时间
        
        Returns:
            str: 更新时间
        """
        config = self._load_config()
        return config.get('subscription', {}).get('last_update')
    
    def list_proxies_by_region(self, region: str = None) -> List[Dict[str, Any]]:
        """
        按地区列出代理
        
        Args:
            region: 地区名称
            
        Returns:
            list: 代理列表
        """
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        if region:
            proxies = [p for p in proxies if region in p.get('name', '')]
        
        return proxies
