import { WasmContractQueryResponse } from '../types.js';
import { AssetInfo } from './types.js';
export declare class NeptuneQueryTransformer {
    static contractPricesResponseToPrices(response: WasmContractQueryResponse): Array<{
        assetInfo: AssetInfo;
        price: string;
    }>;
    static contractLendingRatesResponseToLendingRates(response: WasmContractQueryResponse): Array<{
        assetInfo: AssetInfo;
        lendingRate: string;
    }>;
}
