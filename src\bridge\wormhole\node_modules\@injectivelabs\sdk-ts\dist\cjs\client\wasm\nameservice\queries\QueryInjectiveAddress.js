"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryInjectiveAddress = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryInjectiveAddress extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        return (0, index_js_1.toBase64)({
            address: {
                node: this.params.node,
            },
        });
    }
}
exports.QueryInjectiveAddress = QueryInjectiveAddress;
