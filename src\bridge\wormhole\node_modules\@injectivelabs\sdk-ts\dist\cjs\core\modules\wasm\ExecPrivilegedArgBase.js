"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MsgExecPrivilegedArgBase = exports.ExecPrivilegedArgBase = exports.dataToExecDataWithInjectiveExec = exports.dataToExecData = void 0;
const utils_js_1 = require("../utils.js");
const dataToExecData = (data, execParams) => {
    return {
        origin: execParams.origin,
        name: execParams.name,
        args: data,
    };
};
exports.dataToExecData = dataToExecData;
const dataToExecDataWithInjectiveExec = (data, execParams) => {
    return {
        injective_exec: {
            origin: execParams.origin,
            name: execParams.name,
            args: data,
        },
    };
};
exports.dataToExecDataWithInjectiveExec = dataToExecDataWithInjectiveExec;
/** Executing Messages with injective_exec */
class ExecPrivilegedArgBase {
    params;
    constructor(params) {
        this.params = params;
    }
    toJSON() {
        return JSON.stringify((0, utils_js_1.prepareSignBytes)(this.toData()));
    }
    toExecJSON() {
        return JSON.stringify((0, utils_js_1.prepareSignBytes)(this.toExecData()));
    }
}
exports.ExecPrivilegedArgBase = ExecPrivilegedArgBase;
class MsgExecPrivilegedArgBase {
    params;
    constructor(params) {
        this.params = params;
    }
    toJSON() {
        return JSON.stringify((0, utils_js_1.prepareSignBytes)(this.toData()));
    }
}
exports.MsgExecPrivilegedArgBase = MsgExecPrivilegedArgBase;
