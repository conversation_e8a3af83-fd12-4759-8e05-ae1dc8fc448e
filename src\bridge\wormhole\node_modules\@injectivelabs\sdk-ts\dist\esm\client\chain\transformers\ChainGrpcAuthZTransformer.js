import { GeneralException } from '@injectivelabs/exceptions';
import { grpcPaginationToPagination } from '../../../utils/pagination.js';
import { CosmosAuthzV1Beta1Authz, } from '@injectivelabs/core-proto-ts';
/**
 * @category Chain Grpc Transformer
 */
export class ChainGrpcAuthZTransformer {
    static grpcGrantToGrant(grant) {
        const authorization = decodeAuthorizationNoThrow(grant.authorization);
        return {
            authorization: authorization?.authorization,
            authorizationType: authorization?.authorizationType || '',
            expiration: grant.expiration,
        };
    }
    static grpcGrantAuthorizationToGrantAuthorization(grant) {
        const authorization = decodeAuthorizationNoThrow(grant.authorization);
        return {
            granter: grant.granter,
            grantee: grant.grantee,
            authorization: authorization?.authorization,
            authorizationType: authorization?.authorizationType || '',
            expiration: grant.expiration,
        };
    }
    static grpcGrantsToGrants(response) {
        return {
            pagination: grpcPaginationToPagination(response.pagination),
            grants: response.grants.map(ChainGrpcAuthZTransformer.grpcGrantToGrant),
        };
    }
    static grpcGranteeGrantsToGranteeGrants(response) {
        return {
            pagination: grpcPaginationToPagination(response.pagination),
            grants: response.grants.map(ChainGrpcAuthZTransformer.grpcGrantAuthorizationToGrantAuthorization),
        };
    }
    static grpcGranterGrantsToGranterGrants(response) {
        return {
            pagination: grpcPaginationToPagination(response.pagination),
            grants: response.grants.map(ChainGrpcAuthZTransformer.grpcGrantAuthorizationToGrantAuthorization),
        };
    }
}
const decodeAuthorization = (authorization) => {
    switch (authorization.typeUrl) {
        case '/cosmos.authz.v1beta1.GenericAuthorization':
            return {
                authorization: CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(authorization.value),
                authorizationType: '/cosmos.authz.v1beta1.GenericAuthorization',
            };
        default:
            throw new GeneralException(new Error('Unsupported authorization type'));
    }
};
const decodeAuthorizationNoThrow = (authorization) => {
    if (!authorization) {
        return undefined;
    }
    try {
        return decodeAuthorization(authorization);
    }
    catch {
        return undefined;
    }
};
