import _m0 from "protobufjs/minimal.js";
import { BaseAccount } from "../../../../cosmos/auth/v1beta1/auth";
export declare const protobufPackage = "ibc.applications.interchain_accounts.v1";
/** An InterchainAccount is defined as a BaseAccount & the address of the account owner on the controller chain */
export interface InterchainAccount {
    baseAccount: BaseAccount | undefined;
    accountOwner: string;
}
export declare const InterchainAccount: {
    encode(message: InterchainAccount, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InterchainAccount;
    fromJSON(object: any): InterchainAccount;
    toJSON(message: InterchainAccount): unknown;
    create(base?: DeepPartial<InterchainAccount>): InterchainAccount;
    fromPartial(object: DeepPartial<InterchainAccount>): InterchainAccount;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
