import { TokenType, TokenSource, TokenStatic, TokenVerification } from './../types/index.js';
/** @deprecated */
export declare class TokenFactoryStatic {
    registry: TokenStatic[];
    tokensByDenom: Record<string, TokenStatic>;
    tokensBySymbol: Record<string, TokenStatic[]>;
    tokensByAddress: Record<string, TokenStatic[]>;
    constructor(registry: TokenStatic[]);
    toToken(denom: string): TokenStatic | undefined;
    getMetaBySymbol(symbol: string, { type, source, verification, }?: {
        source?: TokenSource;
        type?: TokenType;
        verification?: TokenVerification;
    }): TokenStatic | undefined;
    getMetaByDenomOrAddress(denomOrAddress: string): TokenStatic | undefined;
}
