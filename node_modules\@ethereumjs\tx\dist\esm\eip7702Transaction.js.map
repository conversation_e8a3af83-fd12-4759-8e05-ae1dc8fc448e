{"version": 3, "file": "eip7702Transaction.js", "sourceRoot": "", "sources": ["../../src/eip7702Transaction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AACrC,OAAO,EACL,QAAQ,EACR,SAAS,EACT,WAAW,EACX,WAAW,EACX,qBAAqB,EACrB,aAAa,EACb,UAAU,EACV,WAAW,EACX,OAAO,EACP,uBAAuB,GACxB,MAAM,kBAAkB,CAAA;AAEzB,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAA;AACtD,OAAO,KAAK,OAAO,MAAM,2BAA2B,CAAA;AACpD,OAAO,KAAK,OAAO,MAAM,2BAA2B,CAAA;AACpD,OAAO,KAAK,OAAO,MAAM,2BAA2B,CAAA;AACpD,OAAO,KAAK,MAAM,MAAM,0BAA0B,CAAA;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AAC5C,OAAO,EAAE,WAAW,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,WAAW,CAAA;AAiBxE;;;;;GAKG;AACH,MAAM,OAAO,yBAA0B,SAAQ,eAA+C;IAuG5F;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;QACrD,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,CAAA;QAChE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAAG,MAAM,CAAA;QAE7F,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QAEpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAElF,kCAAkC;QAClC,MAAM,cAAc,GAAG,WAAW,CAAC,iBAAiB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,qCAAqC;QACrC,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,wBAAwB,CACvE,iBAAiB,IAAI,EAAE,CACxB,CAAA;QACD,IAAI,CAAC,iBAAiB,GAAG,qBAAqB,CAAC,iBAAiB,CAAA;QAChE,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,qBAAqB,CAAA;QACxE,oCAAoC;QACpC,kBAAkB,CAAC,uBAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAElE,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAA;QACxD,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAA;QAExE,IAAI,CAAC,+BAA+B,CAAC;YACnC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;SAChD,CAAC,CAAA;QAEF,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,WAAW,EAAE;YACnD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,6DAA6D,CAAC,CAAA;YACzF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,iGAAiG,CAClG,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QAE1B,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IA5JD;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC3D,OAAO,IAAI,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACpD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QACzE,IACE,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,KAAK,KAAK,EAC7F;YACA,MAAM,IAAI,KAAK,CACb,sFACE,eAAe,CAAC,cAClB,eAAe,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CACvD,CAAA;SACF;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;QAEjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,yBAAyB,CAAC,eAAe,CAAC,MAAuB,EAAE,IAAI,CAAC,CAAA;IACjF,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACvE,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAChD,MAAM,IAAI,KAAK,CACb,wGAAwG,CACzG,CAAA;SACF;QAED,MAAM,CACJ,OAAO,EACP,KAAK,EACL,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,aAAa,EACb,CAAC,EACD,CAAC,EACD,CAAC,EACF,GAAG,MAAM,CAAA;QAEV,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACtC,uBAAuB,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEhG,OAAO,IAAI,yBAAyB,CAClC;YACE,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC;YAC/B,KAAK;YACL,oBAAoB;YACpB,YAAY;YACZ,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,IAAI,EAAE;YAC5B,iBAAiB,EAAE,aAAa,IAAI,EAAE;YACtC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACjD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAoED;;OAEG;IACH,UAAU;QACR,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IAED;;;OAGG;IACH,uBAAuB,CAAC,OAAe;QACrC,OAAO,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACvD,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,UAAkB,QAAQ;QACvC,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC9C,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC;YACnC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAChD,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC;YACxC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACzD,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;SACzE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS;QACP,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;;;;;OAUG;IACH,gBAAgB;QACd,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IACzD,CAAC;IAED;;;;;;OAMG;IACH,sBAAsB;QACpB,OAAO,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED;;OAEG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;IACtC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC;IAED,YAAY,CACV,CAAS,EACT,CAAsB,EACtB,CAAsB,EACtB,WAAoB,KAAK;QAEzB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QACd,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QACd,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,yBAAyB,CAAC,UAAU,CACzC;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;YACnB,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;SACpB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,cAAc,GAAG,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACrE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;QAE/B,OAAO;YACL,GAAG,QAAQ;YACX,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YAClC,oBAAoB,EAAE,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5D,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;YAC5C,UAAU,EAAE,cAAc;YAC1B,iBAAiB,EAAE,IAAI,CAAC,qBAAqB;SAC9C,CAAA;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,iBAAiB,IAAI,CAAC,YAAY,yBAAyB,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClG,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACnC,CAAC;CACF"}