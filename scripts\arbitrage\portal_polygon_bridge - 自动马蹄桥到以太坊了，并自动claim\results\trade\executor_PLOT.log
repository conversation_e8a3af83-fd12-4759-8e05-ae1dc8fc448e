2025-05-19 07:43:28,760 - INFO - ================================================================================
2025-05-19 07:43:28,760 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 07:43:28
2025-05-19 07:43:28,760 - INFO - 链: polygon, 投入金额: 255.66 USDT
2025-05-19 07:43:28,760 - INFO - 代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-19 07:43:28,761 - INFO - 收到PLOT买入请求 - 链:polygon, 投入:255.66USDT
2025-05-19 07:43:28,761 - INFO - PLOT: 将在polygon链上执行买入，代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-19 07:43:28,761 - INFO - PLOT: 准备使用KyberSwap在polygon上执行255.66USDT买入PLOT交易
2025-05-19 07:43:28,761 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 07:43:28,771 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 07:43:28,771 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 07:43:28,771 - INFO -   chain: polygon
2025-05-19 07:43:28,771 - INFO -   token_in: USDT
2025-05-19 07:43:28,771 - INFO -   token_out: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-19 07:43:28,772 - INFO -   amount: 255.66
2025-05-19 07:43:28,772 - INFO -   slippage: 0.5%
2025-05-19 07:43:28,772 - INFO -   real: True
2025-05-19 07:43:29,774 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 07:43:29,774 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 255.66 USDT，可用: 158.314544 USDT', 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount': 255.66, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount_in_wei': '255660000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 07:43:29,774 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 255.66 USDT，可用: 158.314544 USDT
2025-05-19 07:43:29,775 - INFO - 读取到 122 条现有交易记录
2025-05-19 07:43:29,775 - INFO - 添加新交易记录: PLOT (PLOT_255.66_2025-05-19 07:43:28)
2025-05-19 07:43:29,777 - INFO - 成功保存 123 条交易记录
2025-05-19 07:43:29,777 - INFO - PLOT: 买入交易处理完成，耗时: 1.02秒
2025-05-19 07:43:29,777 - INFO - ================================================================================
2025-05-19 10:12:00,790 - INFO - ================================================================================
2025-05-19 10:12:00,790 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 10:12:00
2025-05-19 10:12:00,790 - INFO - 链: ethereum, 投入金额: 211.32 USDT
2025-05-19 10:12:00,790 - INFO - 代币地址: ******************************************
2025-05-19 10:12:00,790 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:211.32USDT
2025-05-19 10:12:00,790 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:12:00,790 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行211.32USDT买入PLOT交易
2025-05-19 10:12:00,790 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:12:00,791 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:12:00,827 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 10:12:00,827 - INFO -   chain: ethereum
2025-05-19 10:12:00,827 - INFO -   token_in: USDT
2025-05-19 10:12:00,827 - INFO -   token_out: ******************************************
2025-05-19 10:12:00,827 - INFO -   amount: 211.32
2025-05-19 10:12:00,827 - INFO -   slippage: 0.5%
2025-05-19 10:12:00,827 - INFO -   real: True
2025-05-19 10:12:02,265 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:12:02,265 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 211.32, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '211320000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:12:02,265 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT
2025-05-19 10:12:02,266 - INFO - 读取到 128 条现有交易记录
2025-05-19 10:12:02,267 - INFO - 添加新交易记录: PLOT (PLOT_211.32_2025-05-19 10:12:00)
2025-05-19 10:12:02,269 - INFO - 成功保存 129 条交易记录
2025-05-19 10:12:02,269 - INFO - PLOT: 买入交易处理完成，耗时: 1.48秒
2025-05-19 10:12:02,269 - INFO - ================================================================================
2025-05-19 10:43:21,082 - INFO - ================================================================================
2025-05-19 10:43:21,082 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 10:43:21
2025-05-19 10:43:21,082 - INFO - 链: ethereum, 投入金额: 211.32 USDT
2025-05-19 10:43:21,082 - INFO - 代币地址: ******************************************
2025-05-19 10:43:21,082 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:211.32USDT
2025-05-19 10:43:21,082 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:43:21,082 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行211.32USDT买入PLOT交易
2025-05-19 10:43:21,082 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:43:21,082 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:43:21,082 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 10:43:21,082 - INFO -   chain: ethereum
2025-05-19 10:43:21,082 - INFO -   token_in: USDT
2025-05-19 10:43:21,082 - INFO -   token_out: ******************************************
2025-05-19 10:43:21,082 - INFO -   amount: 211.32
2025-05-19 10:43:21,082 - INFO -   slippage: 0.5%
2025-05-19 10:43:21,082 - INFO -   real: True
2025-05-19 10:43:23,513 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:43:23,513 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 211.32, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '211320000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:43:23,513 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT
2025-05-19 10:43:23,514 - INFO - 读取到 132 条现有交易记录
2025-05-19 10:43:23,514 - INFO - 添加新交易记录: PLOT (PLOT_211.32_2025-05-19 10:43:21)
2025-05-19 10:43:23,516 - INFO - 成功保存 133 条交易记录
2025-05-19 10:43:23,516 - INFO - PLOT: 买入交易处理完成，耗时: 2.43秒
2025-05-19 10:43:23,516 - INFO - ================================================================================
2025-05-19 11:14:05,651 - INFO - ================================================================================
2025-05-19 11:14:05,651 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 11:14:05
2025-05-19 11:14:05,652 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-19 11:14:05,652 - INFO - 代币地址: ******************************************
2025-05-19 11:14:05,652 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-19 11:14:05,652 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 11:14:05,652 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行300.0USDT买入PLOT交易
2025-05-19 11:14:05,652 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 11:14:05,652 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 11:14:05,652 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 11:14:05,652 - INFO -   chain: ethereum
2025-05-19 11:14:05,652 - INFO -   token_in: USDT
2025-05-19 11:14:05,652 - INFO -   token_out: ******************************************
2025-05-19 11:14:05,653 - INFO -   amount: 300.0
2025-05-19 11:14:05,653 - INFO -   slippage: 0.5%
2025-05-19 11:14:05,653 - INFO -   real: True
2025-05-19 11:14:08,029 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 11:14:08,030 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 11:14:08,030 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT
2025-05-19 11:14:08,031 - INFO - 读取到 137 条现有交易记录
2025-05-19 11:14:08,032 - INFO - 添加新交易记录: PLOT (PLOT_300.0_2025-05-19 11:14:05)
2025-05-19 11:14:08,034 - INFO - 成功保存 138 条交易记录
2025-05-19 11:14:08,034 - INFO - PLOT: 买入交易处理完成，耗时: 2.38秒
2025-05-19 11:14:08,034 - INFO - ================================================================================
2025-05-19 12:50:52,170 - INFO - ================================================================================
2025-05-19 12:50:52,170 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 12:50:52
2025-05-19 12:50:52,170 - INFO - 链: ethereum, 投入金额: 144.81 USDT
2025-05-19 12:50:52,171 - INFO - 代币地址: ******************************************
2025-05-19 12:50:52,171 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:144.81USDT
2025-05-19 12:50:52,171 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 12:50:52,171 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行144.81USDT买入PLOT交易
2025-05-19 12:50:52,171 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:50:52,171 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:50:52,171 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 12:50:52,171 - INFO -   chain: ethereum
2025-05-19 12:50:52,171 - INFO -   token_in: USDT
2025-05-19 12:50:52,171 - INFO -   token_out: ******************************************
2025-05-19 12:50:52,171 - INFO -   amount: 144.81
2025-05-19 12:50:52,171 - INFO -   slippage: 0.5%
2025-05-19 12:50:52,171 - INFO -   real: True
2025-05-19 12:50:54,684 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:50:54,684 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 144.81 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 144.81, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '144810000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 12:50:54,684 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 144.81 USDT，可用: 67.81312 USDT
2025-05-19 12:50:54,685 - INFO - 读取到 142 条现有交易记录
2025-05-19 12:50:54,685 - INFO - 添加新交易记录: PLOT (PLOT_144.81_2025-05-19 12:50:52)
2025-05-19 12:50:54,688 - INFO - 成功保存 143 条交易记录
2025-05-19 12:50:54,688 - INFO - PLOT: 买入交易处理完成，耗时: 2.52秒
2025-05-19 12:50:54,688 - INFO - ================================================================================
2025-05-19 13:23:30,587 - INFO - ================================================================================
2025-05-19 13:23:30,588 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 13:23:30
2025-05-19 13:23:30,588 - INFO - 链: ethereum, 投入金额: 189.15 USDT
2025-05-19 13:23:30,588 - INFO - 代币地址: ******************************************
2025-05-19 13:23:30,588 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:189.15USDT
2025-05-19 13:23:30,588 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 13:23:30,588 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行189.15USDT买入PLOT交易
2025-05-19 13:23:30,588 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 13:23:30,588 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 13:23:30,588 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 13:23:30,588 - INFO -   chain: ethereum
2025-05-19 13:23:30,588 - INFO -   token_in: USDT
2025-05-19 13:23:30,588 - INFO -   token_out: ******************************************
2025-05-19 13:23:30,588 - INFO -   amount: 189.15
2025-05-19 13:23:30,589 - INFO -   slippage: 0.5%
2025-05-19 13:23:30,589 - INFO -   real: True
2025-05-19 13:23:33,140 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 13:23:33,144 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 189.15 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 189.15, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '189150000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 13:23:33,145 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 189.15 USDT，可用: 67.81312 USDT
2025-05-19 13:23:33,146 - INFO - 读取到 144 条现有交易记录
2025-05-19 13:23:33,146 - INFO - 添加新交易记录: PLOT (PLOT_189.15_2025-05-19 13:23:30)
2025-05-19 13:23:33,151 - INFO - 成功保存 145 条交易记录
2025-05-19 13:23:33,151 - INFO - PLOT: 买入交易处理完成，耗时: 2.56秒
2025-05-19 13:23:33,151 - INFO - ================================================================================
