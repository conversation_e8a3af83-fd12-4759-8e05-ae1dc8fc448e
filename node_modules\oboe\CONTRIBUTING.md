# Contributing to Oboe.js

:+1::tada: First off, thanks for taking the time to contribute! :tada::+1:

The following is a set of guidelines for contributing to Oboe.js. These are
just guidelines, not rules, use your best judgment and feel free to propose
changes to this document in a pull request.

## Read the docs :)

Oboe.js has some [awesome documentation](http://oboejs.com/)
explaining how and why to use the library.

## Questions/Help

Sometimes your question can be addressed by reading the
[API](http://oboejs.com/api) closely. It's short and nicely organized!

Please post questions to [StackOverflow](http://stackoverflow.com/)
using the `oboe.js` and`javascript` tags.

If you file an issue with an implementation question, it will be closed.
We're not trying to be mean, it just helps keep the issues tab cleaner so we can
 keep improving the library.

## Reporting Bugs / Requesting Features

If you've found an issue, please submit it in
[the issues](https://github.com/jimhigson/oboe.js/issues).

To increase our ability to help, please:
- If it's a server-side bug, fork our
[bug-template](https://github.com/JuanCaicedo/oboe-bug-template), recreate your
bug, and then provide a link to that repo.
- If it's a client-side template, provide a link to a
[jsbin](https://jsbin.com/)/[codepen](http://codepen.io/)/[plunkr](https://plnkr.co/)
that demonstrates the issue (if it's on the client), or a github repo
(if it's on the server), greatly increases our ability to help.

## Pull Requests

If you would like to add functionality, please submit
[an issue](https://github.com/jimhigson/oboe.js/issues) first to make sure it's
a direction we want to take.

Please do the following:
* Follow the existing styles
* Create an example for that demonstrates your changes so people can see how
your changes work

In your PR description include any information that will help a maintainer
understand and test your changes. The easier it is to read and run your PR,
the faster it can get merged!

### What does Oboe need help with?

#### Helping others!

There is a [Google Group](https://groups.google.com/forum/#!forum/oboejs) for
general project discussion. If you have a moment to help other people using the
library, please stop in.

#### Contributing to community

- Write examples! The website has a [section](http://oboejs.com/examples)
showing common use-cases, and it could always use some more. Feel free to submit
a PR to [the website](https://github.com/jimhigson/oboe.js-website).
- We would also like to showcase applications using Oboe, so if you've published
one and want to share it, file it
[in the website issues](https://github.com/jimhigson/oboe.js-website/issues)
and we'll showcase it!
