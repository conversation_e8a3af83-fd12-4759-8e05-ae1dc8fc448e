#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
价格监控和套利分析整合脚本
当价格差异达到阈值时自动触发套利分析
只分析低价买入高价卖出的路径
保存有实际利润的套利机会
"""

import os
import sys
import asyncio
import argparse
import json
from datetime import datetime


# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.cex.gate.client import GateClient
from src.dex.astar.arthswap.client import ArthSwapClient
from src.utils.proxy_manager import ProxyManager
from src.utils.logger import logger
from scripts.arbitrage.analyze_opportunities import analyze_opportunities

# 套利参数设置
CEX_TO_DEX_THRESHOLD = 0.22  # CEX买入DEX卖出的价差阈值（%）
DEX_TO_CEX_THRESHOLD = 0.6  # DEX买入CEX卖出的价差阈值（%）
OPPORTUNITIES_FILE = "opportunities.json"  # 套利机会保存文件

def save_opportunity(opportunity_data):
    """
    保存套利机会到文件
    
    Args:
        opportunity_data: 套利机会数据
    """
    try:
        # 读取现有数据
        if os.path.exists(OPPORTUNITIES_FILE):
            with open(OPPORTUNITIES_FILE, 'r') as f:
                opportunities = json.load(f)
        else:
            opportunities = []
        
        # 添加时间戳
        opportunity_data['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 添加新机会
        opportunities.append(opportunity_data)
        
        # 保存数据
        with open(OPPORTUNITIES_FILE, 'w') as f:
            json.dump(opportunities, f, indent=2)
            
        logger.info(f"套利机会已保存到 {OPPORTUNITIES_FILE}")
    except Exception as e:
        logger.error(f"保存套利机会时出错: {e}")

async def get_prices_and_data(symbol="ASTR/USDT", proxy_manager=None):
    """
    同时获取CEX和DEX的价格、订单簿数据和代币价格
    
    Args:
        symbol: 交易对，默认为ASTR/USDT
        proxy_manager: 代理管理器实例
        
    Returns:
        dict: 包含价格和订单簿数据的字典
    """
    try:
        # 创建客户端实例
        cex_client = GateClient()
        dex_client = ArthSwapClient()
        
        # 获取CEX订单簿
        order_book = cex_client.get_order_book(symbol)
        cex_price = float(order_book['asks'][0][0])
        
        # 获取DEX价格
        dex_price = dex_client.get_token_price("WASTR", "USDT")
        
        return {
            'cex_price': cex_price,
            'dex_price': dex_price,
            'order_book': order_book,
            'cex_client': cex_client,
            'dex_client': dex_client
        }
    except Exception as e:
        logger.error(f"获取价格和数据时出错: {e}")
        if proxy_manager:
            proxy_manager.mark_current_proxy_failed()
        return None

async def get_balances(cex_client, dex_client, symbol="ASTR/USDT"):
    """
    获取CEX和DEX的账户余额
    
    Args:
        cex_client: CEX客户端实例
        dex_client: DEX客户端实例
        symbol: 交易对，默认为ASTR/USDT
        
    Returns:
        tuple: (cex_balances, dex_balances)
    """
    try:
        # 解析交易对
        base, quote = symbol.split('/')
        
        # 获取CEX余额
        cex_balances = {}
        cex_balances[base] = float(cex_client.get_balance(base).get(base, 0))
        cex_balances[quote] = float(cex_client.get_balance(quote).get(quote, 0))
        
        logger.info(f"CEX余额: {cex_balances}")
        
        # 获取DEX余额
        dex_usdt_balance = dex_client.wallet.get_token_balance("USDT")
        dex_wastr_balance = dex_client.wallet.get_token_balance("WASTR")
        dex_balances = {
            "USDT": float(dex_usdt_balance),
            "WASTR": float(dex_wastr_balance)
        }
        logger.info(f"DEX余额: {dex_balances}")
        
        return cex_balances, dex_balances
    except Exception as e:
        logger.error(f"获取余额时出错: {e}")
        return None, None

async def quick_analyze_opportunity(data, cex_balances, dex_balances, symbol="ASTR/USDT"):
    """
    快速分析套利机会，不重新获取数据
    
    Args:
        data: 已获取的价格和订单簿数据
        cex_balances: CEX余额
        dex_balances: DEX余额
        symbol: 交易对，默认为ASTR/USDT
        
    Returns:
        dict: 分析结果
    """
    try:
        # 解析交易对
        base, quote = symbol.split('/')
        
        cex_price = data['cex_price']
        dex_price = data['dex_price']
        order_book = data['order_book']
        
        # 计算价差
        price_diff_percent = (cex_price - dex_price) / dex_price * 100
        
        # 根据价差选择套利方向
        if price_diff_percent < 0:  # CEX价格低于DEX价格，CEX买入DEX卖出
            from scripts.arbitrage.analyze_opportunities import analyze_cex_buy_dex_sell
            analysis_result = await analyze_cex_buy_dex_sell(
                order_book, dex_price, cex_balances[quote], dex_balances["WASTR"]
            )
            direction = "CEX买入DEX卖出"
        else:  # DEX价格低于CEX价格，DEX买入CEX卖出
            from scripts.arbitrage.analyze_opportunities import analyze_dex_buy_cex_sell
            analysis_result = await analyze_dex_buy_cex_sell(
                order_book, dex_price, dex_balances["USDT"], cex_balances[base]
            )
            direction = "DEX买入CEX卖出"
        
        # 输出分析结果
        print("\n===== 套利机会分析结果 =====")
        print(f"交易对: {symbol}")
        print(f"CEX价格: {cex_price}")
        print(f"DEX价格: {dex_price}")
        print(f"价差: {abs(price_diff_percent):.2f}%")
        print(f"最优路径: {direction}")
        
        print("\nCEX账户余额:")
        print(f"  {quote}: {cex_balances[quote]:.4f}")
        print(f"  {base}: {cex_balances[base]:.4f}")
        
        print("\nDEX账户余额:")
        print(f"  USDT: {dex_balances['USDT']:.4f}")
        print(f"  WASTR: {dex_balances['WASTR']:.4f}")
        
        print(f"\n{direction}分析:")
        print(f"  状态: {analysis_result.get('status')}")
        if analysis_result.get('message'):
            print(f"  消息: {analysis_result.get('message')}")
        if analysis_result.get('profit', 0) > 0:
            print(f"  最佳交易量: {analysis_result.get('amount', 0):.2f} {base}")
            print(f"  预期利润: {analysis_result.get('profit', 0):.4f} {quote}")
            profit_rate = analysis_result.get('profit', 0) / (analysis_result.get('amount', 0) * min(cex_price, dex_price)) * 100
            print(f"  利润率: {profit_rate:.2f}%")
        
        return {
            "direction": direction,
            "analysis_result": analysis_result,
            "cex_price": cex_price,
            "dex_price": dex_price,
            "price_diff_percent": price_diff_percent
        }
    except Exception as e:
        logger.error(f"分析套利机会时出错: {e}")
        return None

async def monitor_and_analyze(symbol="ASTR/USDT", interval=30):
    """
    监控价格并在满足条件时进行套利分析
    只分析低价买入高价卖出的路径
    保存有实际利润的套利机会
    当发现CEX买入DEX卖出的差价大于设定阈值时，结束监控
    优化：预先获取账户余额，并在监控时获取订单簿数据，减少分析时的数据获取时间
    
    Args:
        symbol: 交易对，默认为ASTR/USDT
        interval: 监控间隔（秒），默认30秒
    """
    proxy_manager = ProxyManager()
    
    # 创建客户端实例
    cex_client = GateClient()
    dex_client = ArthSwapClient()
    
    # 预先获取账户余额
    cex_balances, dex_balances = await get_balances(cex_client, dex_client, symbol)
    if not cex_balances or not dex_balances:
        logger.error("无法获取账户余额，结束监控")
        return
    
    logger.info(f"已预先获取账户余额，开始价格监控...")
    
    while True:
        try:
            # 获取价格和订单簿数据
            data = await get_prices_and_data(symbol, proxy_manager)
            
            if data:
                cex_price = data['cex_price']
                dex_price = data['dex_price']
                
                # 计算价差百分比
                price_diff_percent = (cex_price - dex_price) / dex_price * 100
                
                # 记录当前价格
                logger.info(f"当前价格 - CEX: {cex_price:.4f}, DEX: {dex_price:.4f}, 价差: {price_diff_percent:.2f}%")
                
                # 只分析低价买入高价卖出的路径
                if cex_price < dex_price:  # CEX价格低于DEX价格
                    if abs(price_diff_percent) >= CEX_TO_DEX_THRESHOLD:
                        logger.info(f"发现CEX买入DEX卖出机会！CEX价格({cex_price:.4f}) < DEX价格({dex_price:.4f}), 价差: {abs(price_diff_percent):.2f}% >= {CEX_TO_DEX_THRESHOLD}%")
                        
                        # 使用已获取的数据快速分析套利机会
                        start_time = datetime.now()
                        result = await quick_analyze_opportunity(data, cex_balances, dex_balances, symbol)
                        analysis_time = (datetime.now() - start_time).total_seconds()
                        logger.info(f"套利分析用时: {analysis_time:.2f}秒")
                        
                        # 如果有实际利润，保存套利机会
                        if result and result.get('analysis_result', {}).get('profit', 0) > 0:
                            save_opportunity({
                                'direction': 'CEX买入DEX卖出',
                                'cex_price': cex_price,
                                'dex_price': dex_price,
                                'price_diff_percent': price_diff_percent,
                                'profit': result['analysis_result']['profit'],
                                'amount': result['analysis_result'].get('amount', 0),
                                'analysis_time': analysis_time
                            })
                        
                        # 发现CEX买入DEX卖出机会，结束监控
                        logger.info("根据设置，发现CEX买入DEX卖出机会后结束监控")
                        return
                        
                else:  # DEX价格低于CEX价格
                    if abs(price_diff_percent) >= DEX_TO_CEX_THRESHOLD:
                        logger.info(f"发现DEX买入CEX卖出机会！DEX价格({dex_price:.4f}) < CEX价格({cex_price:.4f}), 价差: {abs(price_diff_percent):.2f}% >= {DEX_TO_CEX_THRESHOLD}%")
                        
                        # 使用已获取的数据快速分析套利机会
                        start_time = datetime.now()
                        result = await quick_analyze_opportunity(data, cex_balances, dex_balances, symbol)
                        analysis_time = (datetime.now() - start_time).total_seconds()
                        logger.info(f"套利分析用时: {analysis_time:.2f}秒")
                        
                        # 如果有实际利润，保存套利机会
                        if result and result.get('analysis_result', {}).get('profit', 0) > 0:
                            save_opportunity({
                                'direction': 'DEX买入CEX卖出',
                                'cex_price': cex_price,
                                'dex_price': dex_price,
                                'price_diff_percent': price_diff_percent,
                                'profit': result['analysis_result']['profit'],
                                'amount': result['analysis_result'].get('amount', 0),
                                'analysis_time': analysis_time
                            })
            
            # 等待下一次监控
            await asyncio.sleep(interval)
            
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")
            await asyncio.sleep(interval)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="价格监控和套利分析")
    parser.add_argument("symbol", nargs="?", default="ASTR/USDT", help="要监控的交易对，默认为ASTR/USDT")
    parser.add_argument("-i", "--interval", type=int, default=30, help="监控间隔（秒），默认30秒")
    
    args = parser.parse_args()
    
    logger.info(f"开始监控 {args.symbol}，间隔 {args.interval} 秒")
    await monitor_and_analyze(args.symbol, args.interval)

if __name__ == "__main__":
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main()) 