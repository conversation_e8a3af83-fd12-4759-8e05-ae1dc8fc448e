import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { OracleType } from "../../oracle/v1beta1/oracle";
export declare const protobufPackage = "injective.exchange.v1beta1";
export declare enum AtomicMarketOrderAccessLevel {
    Nobody = 0,
    /** BeginBlockerSmartContractsOnly - currently unsupported */
    BeginBlockerSmartContractsOnly = 1,
    SmartContractsOnly = 2,
    Everyone = 3,
    UNRECOGNIZED = -1
}
export declare function atomicMarketOrderAccessLevelFromJSON(object: any): AtomicMarketOrderAccessLevel;
export declare function atomicMarketOrderAccessLevelToJSON(object: AtomicMarketOrderAccessLevel): string;
export declare enum MarketStatus {
    Unspecified = 0,
    Active = 1,
    Paused = 2,
    Demolished = 3,
    Expired = 4,
    UNRECOGNIZED = -1
}
export declare function marketStatusFromJSON(object: any): MarketStatus;
export declare function marketStatusToJSON(object: MarketStatus): string;
export declare enum OrderType {
    UNSPECIFIED = 0,
    BUY = 1,
    SELL = 2,
    STOP_BUY = 3,
    STOP_SELL = 4,
    TAKE_BUY = 5,
    TAKE_SELL = 6,
    BUY_PO = 7,
    SELL_PO = 8,
    BUY_ATOMIC = 9,
    SELL_ATOMIC = 10,
    UNRECOGNIZED = -1
}
export declare function orderTypeFromJSON(object: any): OrderType;
export declare function orderTypeToJSON(object: OrderType): string;
export declare enum ExecutionType {
    UnspecifiedExecutionType = 0,
    Market = 1,
    LimitFill = 2,
    LimitMatchRestingOrder = 3,
    LimitMatchNewOrder = 4,
    MarketLiquidation = 5,
    ExpiryMarketSettlement = 6,
    UNRECOGNIZED = -1
}
export declare function executionTypeFromJSON(object: any): ExecutionType;
export declare function executionTypeToJSON(object: ExecutionType): string;
export declare enum OrderMask {
    UNUSED = 0,
    ANY = 1,
    REGULAR = 2,
    CONDITIONAL = 4,
    /** DIRECTION_BUY_OR_HIGHER - for conditional orders means HIGHER */
    DIRECTION_BUY_OR_HIGHER = 8,
    /** DIRECTION_SELL_OR_LOWER - for conditional orders means LOWER */
    DIRECTION_SELL_OR_LOWER = 16,
    TYPE_MARKET = 32,
    TYPE_LIMIT = 64,
    UNRECOGNIZED = -1
}
export declare function orderMaskFromJSON(object: any): OrderMask;
export declare function orderMaskToJSON(object: OrderMask): string;
export interface Params {
    /**
     * spot_market_instant_listing_fee defines the expedited fee in INJ required
     * to create a spot market by bypassing governance
     */
    spotMarketInstantListingFee: Coin | undefined;
    /**
     * derivative_market_instant_listing_fee defines the expedited fee in INJ
     * required to create a derivative market by bypassing governance
     */
    derivativeMarketInstantListingFee: Coin | undefined;
    /**
     * default_spot_maker_fee defines the default exchange trade fee for makers on
     * a spot market
     */
    defaultSpotMakerFeeRate: string;
    /**
     * default_spot_taker_fee_rate defines the default exchange trade fee rate for
     * takers on a new spot market
     */
    defaultSpotTakerFeeRate: string;
    /**
     * default_derivative_maker_fee defines the default exchange trade fee for
     * makers on a new derivative market
     */
    defaultDerivativeMakerFeeRate: string;
    /**
     * default_derivative_taker_fee defines the default exchange trade fee for
     * takers on a new derivative market
     */
    defaultDerivativeTakerFeeRate: string;
    /**
     * default_initial_margin_ratio defines the default initial margin ratio on a
     * new derivative market
     */
    defaultInitialMarginRatio: string;
    /**
     * default_maintenance_margin_ratio defines the default maintenance margin
     * ratio on a new derivative market
     */
    defaultMaintenanceMarginRatio: string;
    /**
     * default_funding_interval defines the default funding interval on a
     * derivative market
     */
    defaultFundingInterval: string;
    /**
     * funding_multiple defines the timestamp multiple that the funding timestamp
     * should be a multiple of
     */
    fundingMultiple: string;
    /**
     * relayer_fee_share_rate defines the trade fee share percentage that goes to
     * relayers
     */
    relayerFeeShareRate: string;
    /**
     * default_hourly_funding_rate_cap defines the default maximum absolute value
     * of the hourly funding rate
     */
    defaultHourlyFundingRateCap: string;
    /** hourly_interest_rate defines the hourly interest rate */
    defaultHourlyInterestRate: string;
    /**
     * max_derivative_order_side_count defines the maximum number of derivative
     * active orders a subaccount can have for a given orderbook side
     */
    maxDerivativeOrderSideCount: number;
    /**
     * inj_reward_staked_requirement_threshold defines the threshold on INJ
     * rewards after which one also needs staked INJ to receive more
     */
    injRewardStakedRequirementThreshold: string;
    /**
     * the trading_rewards_vesting_duration defines the vesting times for trading
     * rewards
     */
    tradingRewardsVestingDuration: string;
    /**
     * liquidator_reward_share_rate defines the ratio of the split of the surplus
     * collateral that goes to the liquidator
     */
    liquidatorRewardShareRate: string;
    /**
     * binary_options_market_instant_listing_fee defines the expedited fee in INJ
     * required to create a derivative market by bypassing governance
     */
    binaryOptionsMarketInstantListingFee: Coin | undefined;
    /**
     * atomic_market_order_access_level defines the required access permissions
     * for executing atomic market orders
     */
    atomicMarketOrderAccessLevel: AtomicMarketOrderAccessLevel;
    /**
     * spot_atomic_market_order_fee_multiplier defines the default multiplier for
     * executing atomic market orders in spot markets
     */
    spotAtomicMarketOrderFeeMultiplier: string;
    /**
     * derivative_atomic_market_order_fee_multiplier defines the default
     * multiplier for executing atomic market orders in derivative markets
     */
    derivativeAtomicMarketOrderFeeMultiplier: string;
    /**
     * binary_options_atomic_market_order_fee_multiplier defines the default
     * multiplier for executing atomic market orders in binary markets
     */
    binaryOptionsAtomicMarketOrderFeeMultiplier: string;
    /** minimal_protocol_fee_rate defines the minimal protocol fee rate */
    minimalProtocolFeeRate: string;
    /**
     * is_instant_derivative_market_launch_enabled defines whether instant
     * derivative market launch is enabled
     */
    isInstantDerivativeMarketLaunchEnabled: boolean;
    postOnlyModeHeightThreshold: string;
    /**
     * Maximum time in seconds since the last mark price update to allow a
     * decrease in margin
     */
    marginDecreasePriceTimestampThresholdSeconds: string;
    /** List of addresses that are allowed to perform exchange admin operations */
    exchangeAdmins: string[];
    /** inj_auction_max_cap defines the maximum cap for INJ sent to auction */
    injAuctionMaxCap: string;
}
export interface MarketFeeMultiplier {
    marketId: string;
    feeMultiplier: string;
}
/** An object describing a derivative market in the Injective Futures Protocol. */
export interface DerivativeMarket {
    /** Ticker for the derivative contract. */
    ticker: string;
    /** Oracle base currency */
    oracleBase: string;
    /** Oracle quote currency */
    oracleQuote: string;
    /** Oracle type */
    oracleType: OracleType;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** Address of the quote currency denomination for the derivative contract */
    quoteDenom: string;
    /** Unique market ID. */
    marketId: string;
    /**
     * initial_margin_ratio defines the initial margin ratio of a derivative
     * market
     */
    initialMarginRatio: string;
    /**
     * maintenance_margin_ratio defines the maintenance margin ratio of a
     * derivative market
     */
    maintenanceMarginRatio: string;
    /** maker_fee_rate defines the maker fee rate of a derivative market */
    makerFeeRate: string;
    /** taker_fee_rate defines the taker fee rate of a derivative market */
    takerFeeRate: string;
    /**
     * relayer_fee_share_rate defines the percentage of the transaction fee shared
     * with the relayer in a derivative market
     */
    relayerFeeShareRate: string;
    /**
     * true if the market is a perpetual market. false if the market is an expiry
     * futures market
     */
    isPerpetual: boolean;
    /** Status of the market */
    status: MarketStatus;
    /**
     * min_price_tick_size defines the minimum tick size that the price and margin
     * required for orders in the market
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the quantity
     * required for orders in the market
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    /** current market admin */
    admin: string;
    /** level of admin permissions */
    adminPermissions: number;
    /** quote token decimals */
    quoteDecimals: number;
}
/** An object describing a binary options market in Injective Protocol. */
export interface BinaryOptionsMarket {
    /** Ticker for the derivative contract. */
    ticker: string;
    /** Oracle symbol */
    oracleSymbol: string;
    /** Oracle Provider */
    oracleProvider: string;
    /** Oracle type */
    oracleType: OracleType;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** expiration timestamp */
    expirationTimestamp: string;
    /** expiration timestamp */
    settlementTimestamp: string;
    /** admin of the market */
    admin: string;
    /** Address of the quote currency denomination for the binary options contract */
    quoteDenom: string;
    /** Unique market ID. */
    marketId: string;
    /** maker_fee_rate defines the maker fee rate of a binary options market */
    makerFeeRate: string;
    /** taker_fee_rate defines the taker fee rate of a derivative market */
    takerFeeRate: string;
    /**
     * relayer_fee_share_rate defines the percentage of the transaction fee shared
     * with the relayer in a derivative market
     */
    relayerFeeShareRate: string;
    /** Status of the market */
    status: MarketStatus;
    /**
     * min_price_tick_size defines the minimum tick size that the price and margin
     * required for orders in the market
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the quantity
     * required for orders in the market
     */
    minQuantityTickSize: string;
    settlementPrice: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    /** level of admin permissions */
    adminPermissions: number;
    /** quote token decimals */
    quoteDecimals: number;
}
export interface ExpiryFuturesMarketInfo {
    /** market ID. */
    marketId: string;
    /**
     * expiration_timestamp defines the expiration time for a time expiry futures
     * market.
     */
    expirationTimestamp: string;
    /**
     * expiration_twap_start_timestamp defines the start time of the TWAP
     * calculation window
     */
    twapStartTimestamp: string;
    /**
     * expiration_twap_start_price_cumulative defines the cumulative price for the
     * start of the TWAP window
     */
    expirationTwapStartPriceCumulative: string;
    /**
     * settlement_price defines the settlement price for a time expiry futures
     * market.
     */
    settlementPrice: string;
}
export interface PerpetualMarketInfo {
    /** market ID. */
    marketId: string;
    /**
     * hourly_funding_rate_cap defines the maximum absolute value of the hourly
     * funding rate
     */
    hourlyFundingRateCap: string;
    /** hourly_interest_rate defines the hourly interest rate */
    hourlyInterestRate: string;
    /**
     * next_funding_timestamp defines the next funding timestamp in seconds of a
     * perpetual market
     */
    nextFundingTimestamp: string;
    /**
     * funding_interval defines the next funding interval in seconds of a
     * perpetual market.
     */
    fundingInterval: string;
}
export interface PerpetualMarketFunding {
    /** cumulative_funding defines the cumulative funding of a perpetual market. */
    cumulativeFunding: string;
    /**
     * cumulative_price defines the cumulative price for the current hour up to
     * the last timestamp
     */
    cumulativePrice: string;
    lastTimestamp: string;
}
export interface DerivativeMarketSettlementInfo {
    /** market ID. */
    marketId: string;
    /** settlement_price defines the settlement price */
    settlementPrice: string;
}
export interface NextFundingTimestamp {
    nextTimestamp: string;
}
export interface MidPriceAndTOB {
    /** mid price of the market */
    midPrice: string;
    /** best buy price of the market */
    bestBuyPrice: string;
    /** best sell price of the market */
    bestSellPrice: string;
}
/** An object describing trade pair of two assets. */
export interface SpotMarket {
    /**
     * A name of the pair in format AAA/BBB, where AAA is base asset, BBB is quote
     * asset.
     */
    ticker: string;
    /** Coin denom used for the base asset */
    baseDenom: string;
    /** Coin used for the quote asset */
    quoteDenom: string;
    /** maker_fee_rate defines the fee percentage makers pay when trading */
    makerFeeRate: string;
    /** taker_fee_rate defines the fee percentage takers pay when trading */
    takerFeeRate: string;
    /**
     * relayer_fee_share_rate defines the percentage of the transaction fee shared
     * with the relayer in a derivative market
     */
    relayerFeeShareRate: string;
    /** Unique market ID. */
    marketId: string;
    /** Status of the market */
    status: MarketStatus;
    /**
     * min_price_tick_size defines the minimum tick size that the price required
     * for orders in the market
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the quantity
     * required for orders in the market
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    /** current market admin */
    admin: string;
    /** level of admin permissions */
    adminPermissions: number;
    /** base token decimals */
    baseDecimals: number;
    /** quote token decimals */
    quoteDecimals: number;
}
/** A subaccount's deposit for a given base currency */
export interface Deposit {
    availableBalance: string;
    totalBalance: string;
}
export interface SubaccountTradeNonce {
    nonce: number;
}
export interface OrderInfo {
    /** bytes32 subaccount ID that created the order */
    subaccountId: string;
    /** address fee_recipient address that will receive fees for the order */
    feeRecipient: string;
    /** price of the order */
    price: string;
    /** quantity of the order */
    quantity: string;
    cid: string;
}
export interface SpotOrder {
    /** market_id represents the unique ID of the market */
    marketId: string;
    /** order_info contains the information of the order */
    orderInfo: OrderInfo | undefined;
    /** order types */
    orderType: OrderType;
    /** trigger_price is the trigger price used by stop/take orders */
    triggerPrice: string;
}
/** A valid Spot limit order with Metadata. */
export interface SpotLimitOrder {
    /** order_info contains the information of the order */
    orderInfo: OrderInfo | undefined;
    /** order types */
    orderType: OrderType;
    /** the amount of the quantity remaining fillable */
    fillable: string;
    /** trigger_price is the trigger price used by stop/take orders */
    triggerPrice: string;
    orderHash: Uint8Array;
}
/** A valid Spot market order with Metadata. */
export interface SpotMarketOrder {
    /** order_info contains the information of the order */
    orderInfo: OrderInfo | undefined;
    balanceHold: string;
    orderHash: Uint8Array;
    /** order types */
    orderType: OrderType;
    /** trigger_price is the trigger price used by stop/take orders */
    triggerPrice: string;
}
export interface DerivativeOrder {
    /** market_id represents the unique ID of the market */
    marketId: string;
    /** order_info contains the information of the order */
    orderInfo: OrderInfo | undefined;
    /** order types */
    orderType: OrderType;
    /** margin is the margin used by the limit order */
    margin: string;
    /** trigger_price is the trigger price used by stop/take orders */
    triggerPrice: string;
}
export interface SubaccountOrderbookMetadata {
    vanillaLimitOrderCount: number;
    reduceOnlyLimitOrderCount: number;
    /**
     * AggregateReduceOnlyQuantity is the aggregate fillable quantity of the
     * subaccount's reduce-only limit orders in the given direction.
     */
    aggregateReduceOnlyQuantity: string;
    /**
     * AggregateVanillaQuantity is the aggregate fillable quantity of the
     * subaccount's vanilla limit orders in the given direction.
     */
    aggregateVanillaQuantity: string;
    vanillaConditionalOrderCount: number;
    reduceOnlyConditionalOrderCount: number;
}
export interface SubaccountOrder {
    /** price of the order */
    price: string;
    /** the amount of the quantity remaining fillable */
    quantity: string;
    isReduceOnly: boolean;
    cid: string;
}
export interface SubaccountOrderData {
    order: SubaccountOrder | undefined;
    orderHash: Uint8Array;
}
/** A valid Derivative limit order with Metadata. */
export interface DerivativeLimitOrder {
    /** order_info contains the information of the order */
    orderInfo: OrderInfo | undefined;
    /** order types */
    orderType: OrderType;
    /** margin is the margin used by the limit order */
    margin: string;
    /** the amount of the quantity remaining fillable */
    fillable: string;
    /** trigger_price is the trigger price used by stop/take orders */
    triggerPrice: string;
    orderHash: Uint8Array;
}
/** A valid Derivative market order with Metadata. */
export interface DerivativeMarketOrder {
    /** order_info contains the information of the order */
    orderInfo: OrderInfo | undefined;
    /** order types */
    orderType: OrderType;
    margin: string;
    marginHold: string;
    /** trigger_price is the trigger price used by stop/take orders */
    triggerPrice: string;
    orderHash: Uint8Array;
}
export interface Position {
    isLong: boolean;
    quantity: string;
    entryPrice: string;
    margin: string;
    cumulativeFundingEntry: string;
}
export interface MarketOrderIndicator {
    /** market_id represents the unique ID of the market */
    marketId: string;
    isBuy: boolean;
}
export interface TradeLog {
    quantity: string;
    price: string;
    /** bytes32 subaccount ID that executed the trade */
    subaccountId: Uint8Array;
    fee: string;
    orderHash: Uint8Array;
    feeRecipientAddress: Uint8Array;
    cid: string;
}
export interface PositionDelta {
    isLong: boolean;
    executionQuantity: string;
    executionMargin: string;
    executionPrice: string;
}
export interface DerivativeTradeLog {
    subaccountId: Uint8Array;
    positionDelta: PositionDelta | undefined;
    payout: string;
    fee: string;
    orderHash: Uint8Array;
    feeRecipientAddress: Uint8Array;
    cid: string;
    pnl: string;
}
export interface SubaccountPosition {
    position: Position | undefined;
    subaccountId: Uint8Array;
}
export interface SubaccountDeposit {
    subaccountId: Uint8Array;
    deposit: Deposit | undefined;
}
export interface DepositUpdate {
    denom: string;
    deposits: SubaccountDeposit[];
}
export interface PointsMultiplier {
    makerPointsMultiplier: string;
    takerPointsMultiplier: string;
}
export interface TradingRewardCampaignBoostInfo {
    boostedSpotMarketIds: string[];
    spotMarketMultipliers: PointsMultiplier[];
    boostedDerivativeMarketIds: string[];
    derivativeMarketMultipliers: PointsMultiplier[];
}
export interface CampaignRewardPool {
    startTimestamp: string;
    /**
     * max_campaign_rewards are the maximum reward amounts to be disbursed at the
     * end of the campaign
     */
    maxCampaignRewards: Coin[];
}
export interface TradingRewardCampaignInfo {
    /** number of seconds of the duration of each campaign */
    campaignDurationSeconds: string;
    /** the trading fee quote denoms which will be counted for the rewards */
    quoteDenoms: string[];
    /** the optional boost info for markets */
    tradingRewardBoostInfo: TradingRewardCampaignBoostInfo | undefined;
    /** the marketIDs which are disqualified from being rewarded */
    disqualifiedMarketIds: string[];
}
export interface FeeDiscountTierInfo {
    makerDiscountRate: string;
    takerDiscountRate: string;
    stakedAmount: string;
    volume: string;
}
export interface FeeDiscountSchedule {
    bucketCount: string;
    bucketDuration: string;
    /**
     * the trading fee quote denoms which will be counted for the fee paid
     * contribution
     */
    quoteDenoms: string[];
    /** the fee discount tiers */
    tierInfos: FeeDiscountTierInfo[];
    /**
     * the marketIDs which are disqualified from contributing to the fee paid
     * amount
     */
    disqualifiedMarketIds: string[];
}
export interface FeeDiscountTierTTL {
    tier: string;
    ttlTimestamp: string;
}
export interface VolumeRecord {
    makerVolume: string;
    takerVolume: string;
}
export interface AccountRewards {
    account: string;
    rewards: Coin[];
}
export interface TradeRecords {
    marketId: string;
    latestTradeRecords: TradeRecord[];
}
export interface SubaccountIDs {
    subaccountIds: Uint8Array[];
}
export interface TradeRecord {
    timestamp: string;
    price: string;
    quantity: string;
}
export interface Level {
    /** price */
    p: string;
    /** quantity */
    q: string;
}
export interface AggregateSubaccountVolumeRecord {
    subaccountId: string;
    marketVolumes: MarketVolume[];
}
export interface AggregateAccountVolumeRecord {
    account: string;
    marketVolumes: MarketVolume[];
}
export interface MarketVolume {
    marketId: string;
    volume: VolumeRecord | undefined;
}
export interface DenomDecimals {
    denom: string;
    decimals: string;
}
export interface GrantAuthorization {
    grantee: string;
    amount: string;
}
export interface ActiveGrant {
    granter: string;
    amount: string;
}
export interface EffectiveGrant {
    granter: string;
    netGrantedStake: string;
    isValid: boolean;
}
export interface DenomMinNotional {
    denom: string;
    minNotional: string;
}
export declare const Params: {
    encode(message: Params, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Params;
    fromJSON(object: any): Params;
    toJSON(message: Params): unknown;
    create(base?: DeepPartial<Params>): Params;
    fromPartial(object: DeepPartial<Params>): Params;
};
export declare const MarketFeeMultiplier: {
    encode(message: MarketFeeMultiplier, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MarketFeeMultiplier;
    fromJSON(object: any): MarketFeeMultiplier;
    toJSON(message: MarketFeeMultiplier): unknown;
    create(base?: DeepPartial<MarketFeeMultiplier>): MarketFeeMultiplier;
    fromPartial(object: DeepPartial<MarketFeeMultiplier>): MarketFeeMultiplier;
};
export declare const DerivativeMarket: {
    encode(message: DerivativeMarket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarket;
    fromJSON(object: any): DerivativeMarket;
    toJSON(message: DerivativeMarket): unknown;
    create(base?: DeepPartial<DerivativeMarket>): DerivativeMarket;
    fromPartial(object: DeepPartial<DerivativeMarket>): DerivativeMarket;
};
export declare const BinaryOptionsMarket: {
    encode(message: BinaryOptionsMarket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BinaryOptionsMarket;
    fromJSON(object: any): BinaryOptionsMarket;
    toJSON(message: BinaryOptionsMarket): unknown;
    create(base?: DeepPartial<BinaryOptionsMarket>): BinaryOptionsMarket;
    fromPartial(object: DeepPartial<BinaryOptionsMarket>): BinaryOptionsMarket;
};
export declare const ExpiryFuturesMarketInfo: {
    encode(message: ExpiryFuturesMarketInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExpiryFuturesMarketInfo;
    fromJSON(object: any): ExpiryFuturesMarketInfo;
    toJSON(message: ExpiryFuturesMarketInfo): unknown;
    create(base?: DeepPartial<ExpiryFuturesMarketInfo>): ExpiryFuturesMarketInfo;
    fromPartial(object: DeepPartial<ExpiryFuturesMarketInfo>): ExpiryFuturesMarketInfo;
};
export declare const PerpetualMarketInfo: {
    encode(message: PerpetualMarketInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PerpetualMarketInfo;
    fromJSON(object: any): PerpetualMarketInfo;
    toJSON(message: PerpetualMarketInfo): unknown;
    create(base?: DeepPartial<PerpetualMarketInfo>): PerpetualMarketInfo;
    fromPartial(object: DeepPartial<PerpetualMarketInfo>): PerpetualMarketInfo;
};
export declare const PerpetualMarketFunding: {
    encode(message: PerpetualMarketFunding, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PerpetualMarketFunding;
    fromJSON(object: any): PerpetualMarketFunding;
    toJSON(message: PerpetualMarketFunding): unknown;
    create(base?: DeepPartial<PerpetualMarketFunding>): PerpetualMarketFunding;
    fromPartial(object: DeepPartial<PerpetualMarketFunding>): PerpetualMarketFunding;
};
export declare const DerivativeMarketSettlementInfo: {
    encode(message: DerivativeMarketSettlementInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketSettlementInfo;
    fromJSON(object: any): DerivativeMarketSettlementInfo;
    toJSON(message: DerivativeMarketSettlementInfo): unknown;
    create(base?: DeepPartial<DerivativeMarketSettlementInfo>): DerivativeMarketSettlementInfo;
    fromPartial(object: DeepPartial<DerivativeMarketSettlementInfo>): DerivativeMarketSettlementInfo;
};
export declare const NextFundingTimestamp: {
    encode(message: NextFundingTimestamp, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): NextFundingTimestamp;
    fromJSON(object: any): NextFundingTimestamp;
    toJSON(message: NextFundingTimestamp): unknown;
    create(base?: DeepPartial<NextFundingTimestamp>): NextFundingTimestamp;
    fromPartial(object: DeepPartial<NextFundingTimestamp>): NextFundingTimestamp;
};
export declare const MidPriceAndTOB: {
    encode(message: MidPriceAndTOB, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MidPriceAndTOB;
    fromJSON(object: any): MidPriceAndTOB;
    toJSON(message: MidPriceAndTOB): unknown;
    create(base?: DeepPartial<MidPriceAndTOB>): MidPriceAndTOB;
    fromPartial(object: DeepPartial<MidPriceAndTOB>): MidPriceAndTOB;
};
export declare const SpotMarket: {
    encode(message: SpotMarket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarket;
    fromJSON(object: any): SpotMarket;
    toJSON(message: SpotMarket): unknown;
    create(base?: DeepPartial<SpotMarket>): SpotMarket;
    fromPartial(object: DeepPartial<SpotMarket>): SpotMarket;
};
export declare const Deposit: {
    encode(message: Deposit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Deposit;
    fromJSON(object: any): Deposit;
    toJSON(message: Deposit): unknown;
    create(base?: DeepPartial<Deposit>): Deposit;
    fromPartial(object: DeepPartial<Deposit>): Deposit;
};
export declare const SubaccountTradeNonce: {
    encode(message: SubaccountTradeNonce, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountTradeNonce;
    fromJSON(object: any): SubaccountTradeNonce;
    toJSON(message: SubaccountTradeNonce): unknown;
    create(base?: DeepPartial<SubaccountTradeNonce>): SubaccountTradeNonce;
    fromPartial(object: DeepPartial<SubaccountTradeNonce>): SubaccountTradeNonce;
};
export declare const OrderInfo: {
    encode(message: OrderInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderInfo;
    fromJSON(object: any): OrderInfo;
    toJSON(message: OrderInfo): unknown;
    create(base?: DeepPartial<OrderInfo>): OrderInfo;
    fromPartial(object: DeepPartial<OrderInfo>): OrderInfo;
};
export declare const SpotOrder: {
    encode(message: SpotOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotOrder;
    fromJSON(object: any): SpotOrder;
    toJSON(message: SpotOrder): unknown;
    create(base?: DeepPartial<SpotOrder>): SpotOrder;
    fromPartial(object: DeepPartial<SpotOrder>): SpotOrder;
};
export declare const SpotLimitOrder: {
    encode(message: SpotLimitOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotLimitOrder;
    fromJSON(object: any): SpotLimitOrder;
    toJSON(message: SpotLimitOrder): unknown;
    create(base?: DeepPartial<SpotLimitOrder>): SpotLimitOrder;
    fromPartial(object: DeepPartial<SpotLimitOrder>): SpotLimitOrder;
};
export declare const SpotMarketOrder: {
    encode(message: SpotMarketOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketOrder;
    fromJSON(object: any): SpotMarketOrder;
    toJSON(message: SpotMarketOrder): unknown;
    create(base?: DeepPartial<SpotMarketOrder>): SpotMarketOrder;
    fromPartial(object: DeepPartial<SpotMarketOrder>): SpotMarketOrder;
};
export declare const DerivativeOrder: {
    encode(message: DerivativeOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeOrder;
    fromJSON(object: any): DerivativeOrder;
    toJSON(message: DerivativeOrder): unknown;
    create(base?: DeepPartial<DerivativeOrder>): DerivativeOrder;
    fromPartial(object: DeepPartial<DerivativeOrder>): DerivativeOrder;
};
export declare const SubaccountOrderbookMetadata: {
    encode(message: SubaccountOrderbookMetadata, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountOrderbookMetadata;
    fromJSON(object: any): SubaccountOrderbookMetadata;
    toJSON(message: SubaccountOrderbookMetadata): unknown;
    create(base?: DeepPartial<SubaccountOrderbookMetadata>): SubaccountOrderbookMetadata;
    fromPartial(object: DeepPartial<SubaccountOrderbookMetadata>): SubaccountOrderbookMetadata;
};
export declare const SubaccountOrder: {
    encode(message: SubaccountOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountOrder;
    fromJSON(object: any): SubaccountOrder;
    toJSON(message: SubaccountOrder): unknown;
    create(base?: DeepPartial<SubaccountOrder>): SubaccountOrder;
    fromPartial(object: DeepPartial<SubaccountOrder>): SubaccountOrder;
};
export declare const SubaccountOrderData: {
    encode(message: SubaccountOrderData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountOrderData;
    fromJSON(object: any): SubaccountOrderData;
    toJSON(message: SubaccountOrderData): unknown;
    create(base?: DeepPartial<SubaccountOrderData>): SubaccountOrderData;
    fromPartial(object: DeepPartial<SubaccountOrderData>): SubaccountOrderData;
};
export declare const DerivativeLimitOrder: {
    encode(message: DerivativeLimitOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeLimitOrder;
    fromJSON(object: any): DerivativeLimitOrder;
    toJSON(message: DerivativeLimitOrder): unknown;
    create(base?: DeepPartial<DerivativeLimitOrder>): DerivativeLimitOrder;
    fromPartial(object: DeepPartial<DerivativeLimitOrder>): DerivativeLimitOrder;
};
export declare const DerivativeMarketOrder: {
    encode(message: DerivativeMarketOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketOrder;
    fromJSON(object: any): DerivativeMarketOrder;
    toJSON(message: DerivativeMarketOrder): unknown;
    create(base?: DeepPartial<DerivativeMarketOrder>): DerivativeMarketOrder;
    fromPartial(object: DeepPartial<DerivativeMarketOrder>): DerivativeMarketOrder;
};
export declare const Position: {
    encode(message: Position, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Position;
    fromJSON(object: any): Position;
    toJSON(message: Position): unknown;
    create(base?: DeepPartial<Position>): Position;
    fromPartial(object: DeepPartial<Position>): Position;
};
export declare const MarketOrderIndicator: {
    encode(message: MarketOrderIndicator, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MarketOrderIndicator;
    fromJSON(object: any): MarketOrderIndicator;
    toJSON(message: MarketOrderIndicator): unknown;
    create(base?: DeepPartial<MarketOrderIndicator>): MarketOrderIndicator;
    fromPartial(object: DeepPartial<MarketOrderIndicator>): MarketOrderIndicator;
};
export declare const TradeLog: {
    encode(message: TradeLog, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradeLog;
    fromJSON(object: any): TradeLog;
    toJSON(message: TradeLog): unknown;
    create(base?: DeepPartial<TradeLog>): TradeLog;
    fromPartial(object: DeepPartial<TradeLog>): TradeLog;
};
export declare const PositionDelta: {
    encode(message: PositionDelta, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PositionDelta;
    fromJSON(object: any): PositionDelta;
    toJSON(message: PositionDelta): unknown;
    create(base?: DeepPartial<PositionDelta>): PositionDelta;
    fromPartial(object: DeepPartial<PositionDelta>): PositionDelta;
};
export declare const DerivativeTradeLog: {
    encode(message: DerivativeTradeLog, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeTradeLog;
    fromJSON(object: any): DerivativeTradeLog;
    toJSON(message: DerivativeTradeLog): unknown;
    create(base?: DeepPartial<DerivativeTradeLog>): DerivativeTradeLog;
    fromPartial(object: DeepPartial<DerivativeTradeLog>): DerivativeTradeLog;
};
export declare const SubaccountPosition: {
    encode(message: SubaccountPosition, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountPosition;
    fromJSON(object: any): SubaccountPosition;
    toJSON(message: SubaccountPosition): unknown;
    create(base?: DeepPartial<SubaccountPosition>): SubaccountPosition;
    fromPartial(object: DeepPartial<SubaccountPosition>): SubaccountPosition;
};
export declare const SubaccountDeposit: {
    encode(message: SubaccountDeposit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountDeposit;
    fromJSON(object: any): SubaccountDeposit;
    toJSON(message: SubaccountDeposit): unknown;
    create(base?: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
    fromPartial(object: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
};
export declare const DepositUpdate: {
    encode(message: DepositUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DepositUpdate;
    fromJSON(object: any): DepositUpdate;
    toJSON(message: DepositUpdate): unknown;
    create(base?: DeepPartial<DepositUpdate>): DepositUpdate;
    fromPartial(object: DeepPartial<DepositUpdate>): DepositUpdate;
};
export declare const PointsMultiplier: {
    encode(message: PointsMultiplier, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsMultiplier;
    fromJSON(object: any): PointsMultiplier;
    toJSON(message: PointsMultiplier): unknown;
    create(base?: DeepPartial<PointsMultiplier>): PointsMultiplier;
    fromPartial(object: DeepPartial<PointsMultiplier>): PointsMultiplier;
};
export declare const TradingRewardCampaignBoostInfo: {
    encode(message: TradingRewardCampaignBoostInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingRewardCampaignBoostInfo;
    fromJSON(object: any): TradingRewardCampaignBoostInfo;
    toJSON(message: TradingRewardCampaignBoostInfo): unknown;
    create(base?: DeepPartial<TradingRewardCampaignBoostInfo>): TradingRewardCampaignBoostInfo;
    fromPartial(object: DeepPartial<TradingRewardCampaignBoostInfo>): TradingRewardCampaignBoostInfo;
};
export declare const CampaignRewardPool: {
    encode(message: CampaignRewardPool, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignRewardPool;
    fromJSON(object: any): CampaignRewardPool;
    toJSON(message: CampaignRewardPool): unknown;
    create(base?: DeepPartial<CampaignRewardPool>): CampaignRewardPool;
    fromPartial(object: DeepPartial<CampaignRewardPool>): CampaignRewardPool;
};
export declare const TradingRewardCampaignInfo: {
    encode(message: TradingRewardCampaignInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingRewardCampaignInfo;
    fromJSON(object: any): TradingRewardCampaignInfo;
    toJSON(message: TradingRewardCampaignInfo): unknown;
    create(base?: DeepPartial<TradingRewardCampaignInfo>): TradingRewardCampaignInfo;
    fromPartial(object: DeepPartial<TradingRewardCampaignInfo>): TradingRewardCampaignInfo;
};
export declare const FeeDiscountTierInfo: {
    encode(message: FeeDiscountTierInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeeDiscountTierInfo;
    fromJSON(object: any): FeeDiscountTierInfo;
    toJSON(message: FeeDiscountTierInfo): unknown;
    create(base?: DeepPartial<FeeDiscountTierInfo>): FeeDiscountTierInfo;
    fromPartial(object: DeepPartial<FeeDiscountTierInfo>): FeeDiscountTierInfo;
};
export declare const FeeDiscountSchedule: {
    encode(message: FeeDiscountSchedule, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeeDiscountSchedule;
    fromJSON(object: any): FeeDiscountSchedule;
    toJSON(message: FeeDiscountSchedule): unknown;
    create(base?: DeepPartial<FeeDiscountSchedule>): FeeDiscountSchedule;
    fromPartial(object: DeepPartial<FeeDiscountSchedule>): FeeDiscountSchedule;
};
export declare const FeeDiscountTierTTL: {
    encode(message: FeeDiscountTierTTL, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeeDiscountTierTTL;
    fromJSON(object: any): FeeDiscountTierTTL;
    toJSON(message: FeeDiscountTierTTL): unknown;
    create(base?: DeepPartial<FeeDiscountTierTTL>): FeeDiscountTierTTL;
    fromPartial(object: DeepPartial<FeeDiscountTierTTL>): FeeDiscountTierTTL;
};
export declare const VolumeRecord: {
    encode(message: VolumeRecord, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VolumeRecord;
    fromJSON(object: any): VolumeRecord;
    toJSON(message: VolumeRecord): unknown;
    create(base?: DeepPartial<VolumeRecord>): VolumeRecord;
    fromPartial(object: DeepPartial<VolumeRecord>): VolumeRecord;
};
export declare const AccountRewards: {
    encode(message: AccountRewards, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountRewards;
    fromJSON(object: any): AccountRewards;
    toJSON(message: AccountRewards): unknown;
    create(base?: DeepPartial<AccountRewards>): AccountRewards;
    fromPartial(object: DeepPartial<AccountRewards>): AccountRewards;
};
export declare const TradeRecords: {
    encode(message: TradeRecords, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradeRecords;
    fromJSON(object: any): TradeRecords;
    toJSON(message: TradeRecords): unknown;
    create(base?: DeepPartial<TradeRecords>): TradeRecords;
    fromPartial(object: DeepPartial<TradeRecords>): TradeRecords;
};
export declare const SubaccountIDs: {
    encode(message: SubaccountIDs, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountIDs;
    fromJSON(object: any): SubaccountIDs;
    toJSON(message: SubaccountIDs): unknown;
    create(base?: DeepPartial<SubaccountIDs>): SubaccountIDs;
    fromPartial(object: DeepPartial<SubaccountIDs>): SubaccountIDs;
};
export declare const TradeRecord: {
    encode(message: TradeRecord, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradeRecord;
    fromJSON(object: any): TradeRecord;
    toJSON(message: TradeRecord): unknown;
    create(base?: DeepPartial<TradeRecord>): TradeRecord;
    fromPartial(object: DeepPartial<TradeRecord>): TradeRecord;
};
export declare const Level: {
    encode(message: Level, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Level;
    fromJSON(object: any): Level;
    toJSON(message: Level): unknown;
    create(base?: DeepPartial<Level>): Level;
    fromPartial(object: DeepPartial<Level>): Level;
};
export declare const AggregateSubaccountVolumeRecord: {
    encode(message: AggregateSubaccountVolumeRecord, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AggregateSubaccountVolumeRecord;
    fromJSON(object: any): AggregateSubaccountVolumeRecord;
    toJSON(message: AggregateSubaccountVolumeRecord): unknown;
    create(base?: DeepPartial<AggregateSubaccountVolumeRecord>): AggregateSubaccountVolumeRecord;
    fromPartial(object: DeepPartial<AggregateSubaccountVolumeRecord>): AggregateSubaccountVolumeRecord;
};
export declare const AggregateAccountVolumeRecord: {
    encode(message: AggregateAccountVolumeRecord, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AggregateAccountVolumeRecord;
    fromJSON(object: any): AggregateAccountVolumeRecord;
    toJSON(message: AggregateAccountVolumeRecord): unknown;
    create(base?: DeepPartial<AggregateAccountVolumeRecord>): AggregateAccountVolumeRecord;
    fromPartial(object: DeepPartial<AggregateAccountVolumeRecord>): AggregateAccountVolumeRecord;
};
export declare const MarketVolume: {
    encode(message: MarketVolume, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MarketVolume;
    fromJSON(object: any): MarketVolume;
    toJSON(message: MarketVolume): unknown;
    create(base?: DeepPartial<MarketVolume>): MarketVolume;
    fromPartial(object: DeepPartial<MarketVolume>): MarketVolume;
};
export declare const DenomDecimals: {
    encode(message: DenomDecimals, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DenomDecimals;
    fromJSON(object: any): DenomDecimals;
    toJSON(message: DenomDecimals): unknown;
    create(base?: DeepPartial<DenomDecimals>): DenomDecimals;
    fromPartial(object: DeepPartial<DenomDecimals>): DenomDecimals;
};
export declare const GrantAuthorization: {
    encode(message: GrantAuthorization, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GrantAuthorization;
    fromJSON(object: any): GrantAuthorization;
    toJSON(message: GrantAuthorization): unknown;
    create(base?: DeepPartial<GrantAuthorization>): GrantAuthorization;
    fromPartial(object: DeepPartial<GrantAuthorization>): GrantAuthorization;
};
export declare const ActiveGrant: {
    encode(message: ActiveGrant, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ActiveGrant;
    fromJSON(object: any): ActiveGrant;
    toJSON(message: ActiveGrant): unknown;
    create(base?: DeepPartial<ActiveGrant>): ActiveGrant;
    fromPartial(object: DeepPartial<ActiveGrant>): ActiveGrant;
};
export declare const EffectiveGrant: {
    encode(message: EffectiveGrant, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EffectiveGrant;
    fromJSON(object: any): EffectiveGrant;
    toJSON(message: EffectiveGrant): unknown;
    create(base?: DeepPartial<EffectiveGrant>): EffectiveGrant;
    fromPartial(object: DeepPartial<EffectiveGrant>): EffectiveGrant;
};
export declare const DenomMinNotional: {
    encode(message: DenomMinNotional, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DenomMinNotional;
    fromJSON(object: any): DenomMinNotional;
    toJSON(message: DenomMinNotional): unknown;
    create(base?: DeepPartial<DenomMinNotional>): DenomMinNotional;
    fromPartial(object: DeepPartial<DenomMinNotional>): DenomMinNotional;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
