{"version": 3, "sources": ["../../src/core/hex.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bytesToHex, hexToBytes } from \"@noble/hashes/utils\";\nimport { ParsingError, ParsingResult } from \"./common\";\nimport { HexInput } from \"../types\";\n\n/**\n * Provides reasons for parsing failures related to hexadecimal values.\n * @group Implementation\n * @category Serialization\n */\nexport enum HexInvalidReason {\n  TOO_SHORT = \"too_short\",\n  INVALID_LENGTH = \"invalid_length\",\n  INVALID_HEX_CHARS = \"invalid_hex_chars\",\n}\n\n/**\n * NOTE: Do not use this class when working with account addresses; use AccountAddress instead.\n * When accepting hex data as input to a function, prefer to accept HexInput and\n *\n * A helper class for working with hex data. Hex data, when represented as a string,\n * generally looks like this, for example: 0xaabbcc, 45cd32, etc.\n *\n * then use the static helper methods of this class to convert it into the desired\n * format. This enables the greatest flexibility for the developer.\n *\n * Example usage:\n * ```typescript\n * getTransactionByHash(txnHash: HexInput): Promise<Transaction> {\n *   const txnHashString = Hex.fromHexInput(txnHash).toString();\n *   return await getTransactionByHashInner(txnHashString);\n * }\n * ```\n * This call to `Hex.fromHexInput().toString()` converts the HexInput to a hex string\n * with a leading 0x prefix, regardless of what the input format was.\n *\n * Other ways to chain the functions together:\n * - `Hex.fromHexString({ hexInput: \"0x1f\" }).toUint8Array()`\n * - `new Hex([1, 3]).toStringWithoutPrefix()`\n * @group Implementation\n * @category Serialization\n */\nexport class Hex {\n  private readonly data: Uint8Array;\n\n  /**\n   * Create a new Hex instance from a Uint8Array.\n   *\n   * @param data - The Uint8Array containing the data to initialize the Hex instance.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(data: Uint8Array) {\n    this.data = data;\n  }\n\n  // ===\n  // Methods for representing an instance of Hex as other types.\n  // ===\n\n  /**\n   * Get the inner hex data as a Uint8Array. The inner data is already a Uint8Array, so no conversion takes place.\n   *\n   * @returns Hex data as Uint8Array\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.data;\n  }\n\n  /**\n   * Get the hex data as a string without the 0x prefix.\n   *\n   * @returns Hex string without 0x prefix\n   * @group Implementation\n   * @category Serialization\n   */\n  toStringWithoutPrefix(): string {\n    return bytesToHex(this.data);\n  }\n\n  /**\n   * Get the hex data as a string with the 0x prefix.\n   *\n   * @returns Hex string with 0x prefix\n   * @group Implementation\n   * @category Serialization\n   */\n  toString(): string {\n    return `0x${this.toStringWithoutPrefix()}`;\n  }\n\n  // ===\n  // Methods for creating an instance of Hex from other types.\n  // ===\n\n  /**\n   * Converts a hex string into a Hex instance, allowing for both prefixed and non-prefixed formats.\n   *\n   * @param str - A hex string, with or without the 0x prefix.\n   *\n   * @throws ParsingError - If the hex string is too short, has an odd number of characters, or contains invalid hex characters.\n   *\n   * @returns Hex - The resulting Hex instance created from the provided string.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromHexString(str: string): Hex {\n    let input = str;\n\n    if (input.startsWith(\"0x\")) {\n      input = input.slice(2);\n    }\n\n    if (input.length === 0) {\n      throw new ParsingError(\n        \"Hex string is too short, must be at least 1 char long, excluding the optional leading 0x.\",\n        HexInvalidReason.TOO_SHORT,\n      );\n    }\n\n    if (input.length % 2 !== 0) {\n      throw new ParsingError(\"Hex string must be an even number of hex characters.\", HexInvalidReason.INVALID_LENGTH);\n    }\n\n    try {\n      return new Hex(hexToBytes(input));\n    } catch (error: any) {\n      throw new ParsingError(\n        `Hex string contains invalid hex characters: ${error?.message}`,\n        HexInvalidReason.INVALID_HEX_CHARS,\n      );\n    }\n  }\n\n  /**\n   * Converts an instance of HexInput, which can be a string or a Uint8Array, into a Hex instance.\n   * This function is useful for transforming hexadecimal representations into a structured Hex object for further manipulation.\n   *\n   * @param hexInput - A HexInput which can be a string or Uint8Array.\n   * @returns A Hex instance created from the provided hexInput.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromHexInput(hexInput: HexInput): Hex {\n    if (hexInput instanceof Uint8Array) return new Hex(hexInput);\n    return Hex.fromHexString(hexInput);\n  }\n\n  /**\n   * Converts an instance of HexInput, which can be a string or a Uint8Array, into a Uint8Array.\n   *\n   * @param hexInput - A HexInput which can be a string or Uint8Array.\n   * @returns A Uint8Array created from the provided hexInput.\n   */\n  static hexInputToUint8Array(hexInput: HexInput): Uint8Array {\n    if (hexInput instanceof Uint8Array) return hexInput;\n    return Hex.fromHexString(hexInput).toUint8Array();\n  }\n\n  /**\n   * Converts a HexInput (string or Uint8Array) to a hex string with '0x' prefix.\n   *\n   * @param hexInput - The input to convert, either a hex string (with/without '0x' prefix) or Uint8Array\n   * @returns A hex string with '0x' prefix (e.g., \"0x1234\")\n   *\n   * @example\n   * ```typescript\n   * Hex.hexInputToString(\"1234\")        // returns \"0x1234\"\n   * Hex.hexInputToString(\"0x1234\")      // returns \"0x1234\"\n   * Hex.hexInputToString(new Uint8Array([0x12, 0x34])) // returns \"0x1234\"\n   * ```\n   */\n  static hexInputToString(hexInput: HexInput): string {\n    return Hex.fromHexInput(hexInput).toString();\n  }\n\n  /**\n   * Converts a HexInput (string or Uint8Array) to a hex string without '0x' prefix.\n   *\n   * @param hexInput - The input to convert, either a hex string (with/without '0x' prefix) or Uint8Array\n   * @returns A hex string without '0x' prefix (e.g., \"1234\")\n   *\n   * @example\n   * ```typescript\n   * Hex.hexInputToStringWithoutPrefix(\"1234\")        // returns \"1234\"\n   * Hex.hexInputToStringWithoutPrefix(\"0x1234\")      // returns \"1234\"\n   * Hex.hexInputToStringWithoutPrefix(new Uint8Array([0x12, 0x34])) // returns \"1234\"\n   * ```\n   */\n  static hexInputToStringWithoutPrefix(hexInput: HexInput): string {\n    return Hex.fromHexInput(hexInput).toStringWithoutPrefix();\n  }\n\n  // ===\n  // Methods for checking validity.\n  // ===\n\n  /**\n   * Check if the provided string is a valid hexadecimal representation.\n   *\n   * @param str - A hex string representing byte data.\n   *\n   * @returns An object containing:\n   *  - valid: A boolean indicating whether the string is valid.\n   *  - invalidReason: The reason for invalidity if the string is not valid.\n   *  - invalidReasonMessage: A message explaining why the string is invalid.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isValid(str: string): ParsingResult<HexInvalidReason> {\n    try {\n      Hex.fromHexString(str);\n      return { valid: true };\n    } catch (error: any) {\n      return {\n        valid: false,\n        invalidReason: error?.invalidReason,\n        invalidReasonMessage: error?.message,\n      };\n    }\n  }\n\n  /**\n   * Determine if two Hex instances are equal by comparing their underlying byte data.\n   *\n   * @param other The Hex instance to compare to.\n   * @returns true if the Hex instances are equal, false if not.\n   * @group Implementation\n   * @category Serialization\n   */\n  equals(other: Hex): boolean {\n    if (this.data.length !== other.data.length) return false;\n    return this.data.every((value, index) => value === other.data[index]);\n  }\n}\n\nexport const hexToAsciiString = (hex: string) => new TextDecoder().decode(Hex.fromHexInput(hex).toUint8Array());\n"], "mappings": "yCAGA,OAAS,cAAAA,EAAY,cAAAC,MAAkB,sBAShC,IAAKC,OACVA,EAAA,UAAY,YACZA,EAAA,eAAiB,iBACjBA,EAAA,kBAAoB,oBAHVA,OAAA,IAgCCC,EAAN,MAAMC,CAAI,CAUf,YAAYC,EAAkB,CAC5B,KAAK,KAAOA,CACd,CAaA,cAA2B,CACzB,OAAO,KAAK,IACd,CASA,uBAAgC,CAC9B,OAAOC,EAAW,KAAK,IAAI,CAC7B,CASA,UAAmB,CACjB,MAAO,KAAK,KAAK,sBAAsB,CAAC,EAC1C,CAiBA,OAAO,cAAcC,EAAkB,CACrC,IAAIC,EAAQD,EAMZ,GAJIC,EAAM,WAAW,IAAI,IACvBA,EAAQA,EAAM,MAAM,CAAC,GAGnBA,EAAM,SAAW,EACnB,MAAM,IAAIC,EACR,4FACA,WACF,EAGF,GAAID,EAAM,OAAS,IAAM,EACvB,MAAM,IAAIC,EAAa,uDAAwD,gBAA+B,EAGhH,GAAI,CACF,OAAO,IAAIL,EAAIM,EAAWF,CAAK,CAAC,CAClC,OAASG,EAAY,CACnB,MAAM,IAAIF,EACR,+CAA+CE,GAAO,OAAO,GAC7D,mBACF,CACF,CACF,CAWA,OAAO,aAAaC,EAAyB,CAC3C,OAAIA,aAAoB,WAAmB,IAAIR,EAAIQ,CAAQ,EACpDR,EAAI,cAAcQ,CAAQ,CACnC,CAQA,OAAO,qBAAqBA,EAAgC,CAC1D,OAAIA,aAAoB,WAAmBA,EACpCR,EAAI,cAAcQ,CAAQ,EAAE,aAAa,CAClD,CAeA,OAAO,iBAAiBA,EAA4B,CAClD,OAAOR,EAAI,aAAaQ,CAAQ,EAAE,SAAS,CAC7C,CAeA,OAAO,8BAA8BA,EAA4B,CAC/D,OAAOR,EAAI,aAAaQ,CAAQ,EAAE,sBAAsB,CAC1D,CAkBA,OAAO,QAAQL,EAA8C,CAC3D,GAAI,CACF,OAAAH,EAAI,cAAcG,CAAG,EACd,CAAE,MAAO,EAAK,CACvB,OAASI,EAAY,CACnB,MAAO,CACL,MAAO,GACP,cAAeA,GAAO,cACtB,qBAAsBA,GAAO,OAC/B,CACF,CACF,CAUA,OAAOE,EAAqB,CAC1B,OAAI,KAAK,KAAK,SAAWA,EAAM,KAAK,OAAe,GAC5C,KAAK,KAAK,MAAM,CAACC,EAAOC,IAAUD,IAAUD,EAAM,KAAKE,CAAK,CAAC,CACtE,CACF,EAEaC,EAAoBC,GAAgB,IAAI,YAAY,EAAE,OAAOd,EAAI,aAAac,CAAG,EAAE,aAAa,CAAC", "names": ["bytesToHex", "hexToBytes", "HexInvalidReason", "Hex", "_Hex", "data", "bytesToHex", "str", "input", "ParsingE<PERSON>r", "hexToBytes", "error", "hexInput", "other", "value", "index", "hexToAsciiString", "hex"]}