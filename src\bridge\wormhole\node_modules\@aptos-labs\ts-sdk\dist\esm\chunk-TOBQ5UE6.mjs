import{a as i}from"./chunk-MT2RJ7H3.mjs";import{a as r}from"./chunk-EBMEXURY.mjs";var t=class l extends r{constructor(e){super(),this.value=new i(e)}serialize(e){e.serialize(this.value)}serializeForEntryFunction(e){e.serializeU32AsUleb128(this.value.value.length),e.serialize(this)}static deserialize(e,s){let a=i.deserialize(e,s);return new l(a.value)}};export{t as a};
//# sourceMappingURL=chunk-TOBQ5UE6.mjs.map