{"version": 3, "file": "rpc_method_wrappers.d.ts", "sourceRoot": "", "sources": ["../../src/rpc_method_wrappers.ts"], "names": [], "mappings": "AAgBA,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AAGlE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAmB,SAAS,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAI9F,eAAO,MAAM,WAAW,mBAA0B,mBAAmB,cAAc,CAAC,sBAInF,CAAC;AAEF,eAAO,MAAM,UAAU,mBACN,mBAAmB,cAAc,CAAC,YACxC,MAAM,oBAOhB,CAAC;AAEF,eAAO,MAAM,aAAa,mBACT,mBAAmB,cAAc,CAAC,WACzC,OAAO,YACN,MAAM,kBACA,MAAM,qBAKtB,CAAC;AAEF,eAAO,MAAM,WAAW,mBACP,mBAAmB,cAAc,CAAC,WACzC,OAAO,qBAKhB,CAAC;AAEF,eAAO,MAAM,YAAY,mBACR,mBAAmB,cAAc,CAAC,WACzC,SAAS,cACN,MAAM,oBAKlB,CAAC;AAEF,eAAO,MAAM,eAAe,mBACX,mBAAmB,cAAc,CAAC,MAC9C,WAAW,cACH,MAAM,WACT,iBAAiB,oBAO1B,CAAC;AAEF,eAAO,MAAM,eAAe,mBACX,mBAAmB,cAAc,CAAC,MAC9C,WAAW,cACH,MAAM,WACT,iBAAiB,oBAO1B,CAAC;AAEF,eAAO,MAAM,IAAI,mBACA,mBAAmB,cAAc,CAAC,QAC5C,SAAS,WACN,OAAO,cACJ,MAAM,oBAOlB,CAAC;AAEF,eAAO,MAAM,SAAS,mBACL,mBAAmB,cAAc,CAAC,cACtC,SAAS,aACV,MAAM,oBAOjB,CAAC"}