"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcStreamClient = void 0;
const IndexerGrpcAccountStream_js_1 = require("./grpc_stream/IndexerGrpcAccountStream.js");
const IndexerGrpcAccountPortfolioStream_js_1 = require("./grpc_stream/IndexerGrpcAccountPortfolioStream.js");
const IndexerGrpcAuctionStream_js_1 = require("./grpc_stream/IndexerGrpcAuctionStream.js");
const IndexerGrpcDerivativesStream_js_1 = require("./grpc_stream/IndexerGrpcDerivativesStream.js");
const IndexerGrpcOracleStream_js_1 = require("./grpc_stream/IndexerGrpcOracleStream.js");
const IndexerGrpcSpotStream_js_1 = require("./grpc_stream/IndexerGrpcSpotStream.js");
const IndexerGrpcExplorerStream_js_1 = require("./grpc_stream/IndexerGrpcExplorerStream.js");
/**
 * @category Indexer Grpc API
 * @hidden
 */
class IndexerGrpcStreamClient {
    derivatives;
    spot;
    account;
    accountPortfolio;
    auction;
    oracle;
    explorer;
    constructor(endpoint) {
        this.account = new IndexerGrpcAccountStream_js_1.IndexerGrpcAccountStream(endpoint);
        this.accountPortfolio = new IndexerGrpcAccountPortfolioStream_js_1.IndexerGrpcAccountPortfolioStream(endpoint);
        this.auction = new IndexerGrpcAuctionStream_js_1.IndexerGrpcAuctionStream(endpoint);
        this.derivatives = new IndexerGrpcDerivativesStream_js_1.IndexerGrpcDerivativesStream(endpoint);
        this.explorer = new IndexerGrpcExplorerStream_js_1.IndexerGrpcExplorerStream(endpoint);
        this.oracle = new IndexerGrpcOracleStream_js_1.IndexerGrpcOracleStream(endpoint);
        this.spot = new IndexerGrpcSpotStream_js_1.IndexerGrpcSpotStream(endpoint);
    }
}
exports.IndexerGrpcStreamClient = IndexerGrpcStreamClient;
