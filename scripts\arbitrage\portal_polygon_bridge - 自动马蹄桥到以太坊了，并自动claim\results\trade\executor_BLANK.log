2025-05-19 11:13:25,415 - INFO - ================================================================================
2025-05-19 11:13:25,415 - INFO - 开始执行 BLANK 买入交易 - 时间: 2025-05-19 11:13:25
2025-05-19 11:13:25,415 - INFO - 链: ethereum, 投入金额: 122.9 USDT
2025-05-19 11:13:25,415 - INFO - 代币地址: ******************************************
2025-05-19 11:13:25,415 - INFO - 收到BLANK买入请求 - 链:ethereum, 投入:122.9USDT
2025-05-19 11:13:25,415 - INFO - BLANK: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 11:13:25,416 - INFO - BLANK: 准备使用KyberSwap在ethereum上执行122.9USDT买入BLANK交易
2025-05-19 11:13:25,416 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 11:13:25,416 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 11:13:25,416 - INFO - BLANK: 准备调用swap_tokens函数，参数：
2025-05-19 11:13:25,416 - INFO -   chain: ethereum
2025-05-19 11:13:25,416 - INFO -   token_in: USDT
2025-05-19 11:13:25,416 - INFO -   token_out: ******************************************
2025-05-19 11:13:25,416 - INFO -   amount: 122.9
2025-05-19 11:13:25,416 - INFO -   slippage: 0.5%
2025-05-19 11:13:25,416 - INFO -   real: True
2025-05-19 11:13:27,410 - INFO - BLANK: swap_tokens返回值类型: <class 'dict'>
2025-05-19 11:13:27,410 - INFO - BLANK: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 122.9 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 122.9, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '122900000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 11:13:27,410 - ERROR - BLANK: 交易失败 - 代币余额不足。请求: 122.9 USDT，可用: 67.81312 USDT
2025-05-19 11:13:27,411 - INFO - 读取到 136 条现有交易记录
2025-05-19 11:13:27,411 - INFO - 添加新交易记录: BLANK (BLANK_122.9_2025-05-19 11:13:25)
2025-05-19 11:13:27,414 - INFO - 成功保存 137 条交易记录
2025-05-19 11:13:27,421 - INFO - BLANK: 买入交易处理完成，耗时: 2.01秒
2025-05-19 11:13:27,421 - INFO - ================================================================================
2025-05-19 11:46:04,777 - INFO - ================================================================================
2025-05-19 11:46:04,777 - INFO - 开始执行 BLANK 买入交易 - 时间: 2025-05-19 11:46:04
2025-05-19 11:46:04,777 - INFO - 链: ethereum, 投入金额: 145.04 USDT
2025-05-19 11:46:04,777 - INFO - 代币地址: ******************************************
2025-05-19 11:46:04,777 - INFO - 收到BLANK买入请求 - 链:ethereum, 投入:145.04USDT
2025-05-19 11:46:04,777 - INFO - BLANK: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 11:46:04,777 - INFO - BLANK: 准备使用KyberSwap在ethereum上执行145.04USDT买入BLANK交易
2025-05-19 11:46:04,777 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 11:46:04,777 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 11:46:04,777 - INFO - BLANK: 准备调用swap_tokens函数，参数：
2025-05-19 11:46:04,777 - INFO -   chain: ethereum
2025-05-19 11:46:04,777 - INFO -   token_in: USDT
2025-05-19 11:46:04,777 - INFO -   token_out: ******************************************
2025-05-19 11:46:04,778 - INFO -   amount: 145.04
2025-05-19 11:46:04,778 - INFO -   slippage: 0.5%
2025-05-19 11:46:04,778 - INFO -   real: True
2025-05-19 11:46:06,862 - INFO - BLANK: swap_tokens返回值类型: <class 'dict'>
2025-05-19 11:46:06,862 - INFO - BLANK: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 145.04 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 145.04, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '145040000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 11:46:06,862 - ERROR - BLANK: 交易失败 - 代币余额不足。请求: 145.04 USDT，可用: 67.81312 USDT
2025-05-19 11:46:06,864 - INFO - 读取到 139 条现有交易记录
2025-05-19 11:46:06,864 - INFO - 添加新交易记录: BLANK (BLANK_145.04_2025-05-19 11:46:04)
2025-05-19 11:46:06,866 - INFO - 成功保存 140 条交易记录
2025-05-19 11:46:06,866 - INFO - BLANK: 买入交易处理完成，耗时: 2.09秒
2025-05-19 11:46:06,866 - INFO - ================================================================================
