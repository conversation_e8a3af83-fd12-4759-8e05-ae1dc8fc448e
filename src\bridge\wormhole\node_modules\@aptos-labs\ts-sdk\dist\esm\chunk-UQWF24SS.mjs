import{a as d}from"./chunk-DPW6ELCQ.mjs";import{d as v,f as P,g as E,h as H,i as b,j as U}from"./chunk-C3Q23D22.mjs";import{a as x}from"./chunk-ROT6S6BM.mjs";import{b as K}from"./chunk-WSR5EBJM.mjs";import{a as S}from"./chunk-WCMW2L3P.mjs";import{a as A}from"./chunk-FGFLPH5K.mjs";import{a as p}from"./chunk-EBMEXURY.mjs";import{b as o}from"./chunk-STY74NUA.mjs";import{ed25519 as y}from"@noble/curves/ed25519";var m=[237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16];function T(I){let e=I.toUint8Array().slice(32);for(let t=m.length-1;t>=0;t-=1){if(e[t]<m[t])return!0;if(e[t]>m[t])return!1}return!1}var n=class n extends K{constructor(e){super();let t=o.fromHexInput(e);if(t.toUint8Array().length!==n.LENGTH)throw new Error(`PublicKey length should be ${n.LENGTH}`);this.key=t}verifySignature(e){let{message:t,signature:i}=e;if(!T(i))return!1;let a=d(t),u=o.fromHexInput(a).toUint8Array(),c=i.toUint8Array(),l=this.key.toUint8Array();return y.verify(c,u,l)}async verifySignatureAsync(e){return this.verifySignature(e)}authKey(){return A.fromSchemeAndBytes({scheme:0,input:this.toUint8Array()})}toUint8Array(){return this.key.toUint8Array()}serialize(e){e.serializeBytes(this.key.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new n(t)}static isPublicKey(e){return e instanceof n}static isInstance(e){return"key"in e&&e.key?.data?.length===n.LENGTH}};n.LENGTH=32;var f=n,r=class r extends p{constructor(e,t){super();let i=x.parseHexInput(e,"ed25519",t);if(i.toUint8Array().length!==r.LENGTH)throw new Error(`PrivateKey length should be ${r.LENGTH}`);this.signingKey=i}static generate(){let e=y.utils.randomPrivateKey();return new r(e,!1)}static fromDerivationPath(e,t){if(!P(e))throw new Error(`Invalid derivation path ${e}`);return r.fromDerivationPathInner(e,U(t))}static fromDerivationPathInner(e,t,i=v){let{key:a,chainCode:u}=E(r.SLIP_0010_SEED,t),c=b(e).map(g=>parseInt(g,10)),{key:l}=c.reduce((g,w)=>H(g,w+i),{key:a,chainCode:u});return new r(l,!1)}publicKey(){let e=y.getPublicKey(this.signingKey.toUint8Array());return new f(e)}sign(e){let t=d(e),i=o.fromHexInput(t).toUint8Array(),a=y.sign(i,this.signingKey.toUint8Array());return new h(a)}toUint8Array(){return this.signingKey.toUint8Array()}toString(){return this.toAIP80String()}toHexString(){return this.signingKey.toString()}toAIP80String(){return x.formatPrivateKey(this.signingKey.toString(),"ed25519")}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new r(t,!1)}static isPrivateKey(e){return e instanceof r}};r.LENGTH=32,r.SLIP_0010_SEED="ed25519 seed";var z=r,s=class s extends S{constructor(e){super();let t=o.fromHexInput(e);if(t.toUint8Array().length!==s.LENGTH)throw new Error(`Signature length should be ${s.LENGTH}`);this.data=t}toUint8Array(){return this.data.toUint8Array()}serialize(e){e.serializeBytes(this.data.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new s(t)}};s.LENGTH=64;var h=s;export{T as a,f as b,z as c,h as d};
//# sourceMappingURL=chunk-UQWF24SS.mjs.map