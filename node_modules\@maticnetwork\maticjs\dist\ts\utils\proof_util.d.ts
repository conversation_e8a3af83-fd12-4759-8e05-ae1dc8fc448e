/// <reference types="node" />
import { BaseWeb3Client } from "../abstracts";
import { ITransactionReceipt, IBlockWithTransaction } from "../interfaces";
import { BlockHeader } from '@ethereumjs/block';
export declare class ProofUtil {
    static getFastMerkleProof(web3: BaseWeb3Client, blockNumber: number, startBlock: number, endBlock: number): Promise<string[]>;
    static buildBlockProof(maticWeb3: BaseWeb3Client, startBlock: number, endBlock: number, blockNumber: number): Promise<string>;
    static queryRootHash(client: BaseWeb3Client, startBlock: number, endBlock: number): Promise<any>;
    static recursiveZeroHash(n: number, client: BaseWeb3Client): any;
    static getReceiptProof(receipt: ITransactionReceipt, block: IBlockWithTransaction, web3: BaseWeb3Client, requestConcurrency?: number, receiptsVal?: ITransactionReceipt[]): Promise<{
        blockHash: Buffer;
        parentNodes: import("@ethereumjs/trie").EmbeddedNode[][];
        root: Uint8Array;
        path: Uint8Array;
        value: Uint8Array | ((v?: Uint8Array) => Uint8Array) | import("rlp").NestedUint8Array;
    }>;
    static isTypedReceipt(receipt: ITransactionReceipt): boolean;
    static getStateSyncTxHash(block: any): Buffer;
    static getReceiptBytes(receipt: ITransactionReceipt): Uint8Array;
    static getRawHeader(_block: any): BlockHeader;
}
