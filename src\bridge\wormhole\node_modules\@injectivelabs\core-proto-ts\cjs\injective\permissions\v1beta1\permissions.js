"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressVoucher = exports.RoleIDs = exports.PolicyManagerCapability = exports.Role = exports.PolicyStatus = exports.RoleManager = exports.RoleActors = exports.ActorRoles = exports.Namespace = exports.Action = exports.protobufPackage = void 0;
exports.actionFromJSON = actionFromJSON;
exports.actionToJSON = actionToJSON;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
exports.protobufPackage = "injective.permissions.v1beta1";
/** each Action enum value should be a power of two */
var Action;
(function (Action) {
    /** UNSPECIFIED - 0 is reserved for ACTION_UNSPECIFIED */
    Action[Action["UNSPECIFIED"] = 0] = "UNSPECIFIED";
    /** MINT - 1 is reserved for MINT */
    Action[Action["MINT"] = 1] = "MINT";
    /** RECEIVE - 2 is reserved for RECEIVE */
    Action[Action["RECEIVE"] = 2] = "RECEIVE";
    /** BURN - 4 is reserved for BURN */
    Action[Action["BURN"] = 4] = "BURN";
    /** SEND - 8 is reserved for SEND */
    Action[Action["SEND"] = 8] = "SEND";
    /** SUPER_BURN - 16 is reserved for SUPER_BURN */
    Action[Action["SUPER_BURN"] = 16] = "SUPER_BURN";
    /** MODIFY_POLICY_MANAGERS - 2^27 is reserved for MODIFY_POLICY_MANAGERS */
    Action[Action["MODIFY_POLICY_MANAGERS"] = 134217728] = "MODIFY_POLICY_MANAGERS";
    /** MODIFY_CONTRACT_HOOK - 2^28 is reserved for MODIFY_CONTRACT_HOOK */
    Action[Action["MODIFY_CONTRACT_HOOK"] = 268435456] = "MODIFY_CONTRACT_HOOK";
    /** MODIFY_ROLE_PERMISSIONS - 2^29 is reserved for MODIFY_ROLE_PERMISSIONS */
    Action[Action["MODIFY_ROLE_PERMISSIONS"] = 536870912] = "MODIFY_ROLE_PERMISSIONS";
    /** MODIFY_ROLE_MANAGERS - 2^30 is reserved for MODIFY_ROLE_MANAGERS */
    Action[Action["MODIFY_ROLE_MANAGERS"] = 1073741824] = "MODIFY_ROLE_MANAGERS";
    Action[Action["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(Action || (exports.Action = Action = {}));
function actionFromJSON(object) {
    switch (object) {
        case 0:
        case "UNSPECIFIED":
            return Action.UNSPECIFIED;
        case 1:
        case "MINT":
            return Action.MINT;
        case 2:
        case "RECEIVE":
            return Action.RECEIVE;
        case 4:
        case "BURN":
            return Action.BURN;
        case 8:
        case "SEND":
            return Action.SEND;
        case 16:
        case "SUPER_BURN":
            return Action.SUPER_BURN;
        case 134217728:
        case "MODIFY_POLICY_MANAGERS":
            return Action.MODIFY_POLICY_MANAGERS;
        case 268435456:
        case "MODIFY_CONTRACT_HOOK":
            return Action.MODIFY_CONTRACT_HOOK;
        case 536870912:
        case "MODIFY_ROLE_PERMISSIONS":
            return Action.MODIFY_ROLE_PERMISSIONS;
        case 1073741824:
        case "MODIFY_ROLE_MANAGERS":
            return Action.MODIFY_ROLE_MANAGERS;
        case -1:
        case "UNRECOGNIZED":
        default:
            return Action.UNRECOGNIZED;
    }
}
function actionToJSON(object) {
    switch (object) {
        case Action.UNSPECIFIED:
            return "UNSPECIFIED";
        case Action.MINT:
            return "MINT";
        case Action.RECEIVE:
            return "RECEIVE";
        case Action.BURN:
            return "BURN";
        case Action.SEND:
            return "SEND";
        case Action.SUPER_BURN:
            return "SUPER_BURN";
        case Action.MODIFY_POLICY_MANAGERS:
            return "MODIFY_POLICY_MANAGERS";
        case Action.MODIFY_CONTRACT_HOOK:
            return "MODIFY_CONTRACT_HOOK";
        case Action.MODIFY_ROLE_PERMISSIONS:
            return "MODIFY_ROLE_PERMISSIONS";
        case Action.MODIFY_ROLE_MANAGERS:
            return "MODIFY_ROLE_MANAGERS";
        case Action.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseNamespace() {
    return {
        denom: "",
        contractHook: "",
        rolePermissions: [],
        actorRoles: [],
        roleManagers: [],
        policyStatuses: [],
        policyManagerCapabilities: [],
    };
}
exports.Namespace = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.contractHook !== "") {
            writer.uint32(18).string(message.contractHook);
        }
        try {
            for (var _f = __values(message.rolePermissions), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                exports.Role.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_a = _f.return)) _a.call(_f);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _h = __values(message.actorRoles), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                exports.ActorRoles.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_b = _h.return)) _b.call(_h);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _k = __values(message.roleManagers), _l = _k.next(); !_l.done; _l = _k.next()) {
                var v = _l.value;
                exports.RoleManager.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_l && !_l.done && (_c = _k.return)) _c.call(_k);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _m = __values(message.policyStatuses), _o = _m.next(); !_o.done; _o = _m.next()) {
                var v = _o.value;
                exports.PolicyStatus.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_o && !_o.done && (_d = _m.return)) _d.call(_m);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _p = __values(message.policyManagerCapabilities), _q = _p.next(); !_q.done; _q = _p.next()) {
                var v = _q.value;
                exports.PolicyManagerCapability.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_q && !_q.done && (_e = _p.return)) _e.call(_p);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseNamespace();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.contractHook = reader.string();
                    break;
                case 3:
                    message.rolePermissions.push(exports.Role.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.actorRoles.push(exports.ActorRoles.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.roleManagers.push(exports.RoleManager.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.policyStatuses.push(exports.PolicyStatus.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.policyManagerCapabilities.push(exports.PolicyManagerCapability.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            contractHook: isSet(object.contractHook) ? String(object.contractHook) : "",
            rolePermissions: Array.isArray(object === null || object === void 0 ? void 0 : object.rolePermissions)
                ? object.rolePermissions.map(function (e) { return exports.Role.fromJSON(e); })
                : [],
            actorRoles: Array.isArray(object === null || object === void 0 ? void 0 : object.actorRoles) ? object.actorRoles.map(function (e) { return exports.ActorRoles.fromJSON(e); }) : [],
            roleManagers: Array.isArray(object === null || object === void 0 ? void 0 : object.roleManagers)
                ? object.roleManagers.map(function (e) { return exports.RoleManager.fromJSON(e); })
                : [],
            policyStatuses: Array.isArray(object === null || object === void 0 ? void 0 : object.policyStatuses)
                ? object.policyStatuses.map(function (e) { return exports.PolicyStatus.fromJSON(e); })
                : [],
            policyManagerCapabilities: Array.isArray(object === null || object === void 0 ? void 0 : object.policyManagerCapabilities)
                ? object.policyManagerCapabilities.map(function (e) { return exports.PolicyManagerCapability.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.contractHook !== undefined && (obj.contractHook = message.contractHook);
        if (message.rolePermissions) {
            obj.rolePermissions = message.rolePermissions.map(function (e) { return e ? exports.Role.toJSON(e) : undefined; });
        }
        else {
            obj.rolePermissions = [];
        }
        if (message.actorRoles) {
            obj.actorRoles = message.actorRoles.map(function (e) { return e ? exports.ActorRoles.toJSON(e) : undefined; });
        }
        else {
            obj.actorRoles = [];
        }
        if (message.roleManagers) {
            obj.roleManagers = message.roleManagers.map(function (e) { return e ? exports.RoleManager.toJSON(e) : undefined; });
        }
        else {
            obj.roleManagers = [];
        }
        if (message.policyStatuses) {
            obj.policyStatuses = message.policyStatuses.map(function (e) { return e ? exports.PolicyStatus.toJSON(e) : undefined; });
        }
        else {
            obj.policyStatuses = [];
        }
        if (message.policyManagerCapabilities) {
            obj.policyManagerCapabilities = message.policyManagerCapabilities.map(function (e) {
                return e ? exports.PolicyManagerCapability.toJSON(e) : undefined;
            });
        }
        else {
            obj.policyManagerCapabilities = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Namespace.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseNamespace();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.contractHook = (_b = object.contractHook) !== null && _b !== void 0 ? _b : "";
        message.rolePermissions = ((_c = object.rolePermissions) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.Role.fromPartial(e); })) || [];
        message.actorRoles = ((_d = object.actorRoles) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.ActorRoles.fromPartial(e); })) || [];
        message.roleManagers = ((_e = object.roleManagers) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.RoleManager.fromPartial(e); })) || [];
        message.policyStatuses = ((_f = object.policyStatuses) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.PolicyStatus.fromPartial(e); })) || [];
        message.policyManagerCapabilities =
            ((_g = object.policyManagerCapabilities) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.PolicyManagerCapability.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseActorRoles() {
    return { actor: "", roles: [] };
}
exports.ActorRoles = {
    encode: function (message, writer) {
        var e_6, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.actor !== "") {
            writer.uint32(10).string(message.actor);
        }
        try {
            for (var _b = __values(message.roles), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseActorRoles();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.actor = reader.string();
                    break;
                case 2:
                    message.roles.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            actor: isSet(object.actor) ? String(object.actor) : "",
            roles: Array.isArray(object === null || object === void 0 ? void 0 : object.roles) ? object.roles.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.actor !== undefined && (obj.actor = message.actor);
        if (message.roles) {
            obj.roles = message.roles.map(function (e) { return e; });
        }
        else {
            obj.roles = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ActorRoles.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseActorRoles();
        message.actor = (_a = object.actor) !== null && _a !== void 0 ? _a : "";
        message.roles = ((_b = object.roles) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseRoleActors() {
    return { role: "", actors: [] };
}
exports.RoleActors = {
    encode: function (message, writer) {
        var e_7, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.role !== "") {
            writer.uint32(10).string(message.role);
        }
        try {
            for (var _b = __values(message.actors), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRoleActors();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.role = reader.string();
                    break;
                case 2:
                    message.actors.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            role: isSet(object.role) ? String(object.role) : "",
            actors: Array.isArray(object === null || object === void 0 ? void 0 : object.actors) ? object.actors.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.role !== undefined && (obj.role = message.role);
        if (message.actors) {
            obj.actors = message.actors.map(function (e) { return e; });
        }
        else {
            obj.actors = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.RoleActors.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseRoleActors();
        message.role = (_a = object.role) !== null && _a !== void 0 ? _a : "";
        message.actors = ((_b = object.actors) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseRoleManager() {
    return { manager: "", roles: [] };
}
exports.RoleManager = {
    encode: function (message, writer) {
        var e_8, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.manager !== "") {
            writer.uint32(10).string(message.manager);
        }
        try {
            for (var _b = __values(message.roles), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRoleManager();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.manager = reader.string();
                    break;
                case 2:
                    message.roles.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            manager: isSet(object.manager) ? String(object.manager) : "",
            roles: Array.isArray(object === null || object === void 0 ? void 0 : object.roles) ? object.roles.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.manager !== undefined && (obj.manager = message.manager);
        if (message.roles) {
            obj.roles = message.roles.map(function (e) { return e; });
        }
        else {
            obj.roles = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.RoleManager.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseRoleManager();
        message.manager = (_a = object.manager) !== null && _a !== void 0 ? _a : "";
        message.roles = ((_b = object.roles) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBasePolicyStatus() {
    return { action: 0, isDisabled: false, isSealed: false };
}
exports.PolicyStatus = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.action !== 0) {
            writer.uint32(8).int32(message.action);
        }
        if (message.isDisabled === true) {
            writer.uint32(16).bool(message.isDisabled);
        }
        if (message.isSealed === true) {
            writer.uint32(24).bool(message.isSealed);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePolicyStatus();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.action = reader.int32();
                    break;
                case 2:
                    message.isDisabled = reader.bool();
                    break;
                case 3:
                    message.isSealed = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            action: isSet(object.action) ? actionFromJSON(object.action) : 0,
            isDisabled: isSet(object.isDisabled) ? Boolean(object.isDisabled) : false,
            isSealed: isSet(object.isSealed) ? Boolean(object.isSealed) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.action !== undefined && (obj.action = actionToJSON(message.action));
        message.isDisabled !== undefined && (obj.isDisabled = message.isDisabled);
        message.isSealed !== undefined && (obj.isSealed = message.isSealed);
        return obj;
    },
    create: function (base) {
        return exports.PolicyStatus.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBasePolicyStatus();
        message.action = (_a = object.action) !== null && _a !== void 0 ? _a : 0;
        message.isDisabled = (_b = object.isDisabled) !== null && _b !== void 0 ? _b : false;
        message.isSealed = (_c = object.isSealed) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBaseRole() {
    return { name: "", roleId: 0, permissions: 0 };
}
exports.Role = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.roleId !== 0) {
            writer.uint32(16).uint32(message.roleId);
        }
        if (message.permissions !== 0) {
            writer.uint32(24).uint32(message.permissions);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRole();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.name = reader.string();
                    break;
                case 2:
                    message.roleId = reader.uint32();
                    break;
                case 3:
                    message.permissions = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            name: isSet(object.name) ? String(object.name) : "",
            roleId: isSet(object.roleId) ? Number(object.roleId) : 0,
            permissions: isSet(object.permissions) ? Number(object.permissions) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.name !== undefined && (obj.name = message.name);
        message.roleId !== undefined && (obj.roleId = Math.round(message.roleId));
        message.permissions !== undefined && (obj.permissions = Math.round(message.permissions));
        return obj;
    },
    create: function (base) {
        return exports.Role.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseRole();
        message.name = (_a = object.name) !== null && _a !== void 0 ? _a : "";
        message.roleId = (_b = object.roleId) !== null && _b !== void 0 ? _b : 0;
        message.permissions = (_c = object.permissions) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBasePolicyManagerCapability() {
    return { manager: "", action: 0, canDisable: false, canSeal: false };
}
exports.PolicyManagerCapability = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.manager !== "") {
            writer.uint32(10).string(message.manager);
        }
        if (message.action !== 0) {
            writer.uint32(16).int32(message.action);
        }
        if (message.canDisable === true) {
            writer.uint32(24).bool(message.canDisable);
        }
        if (message.canSeal === true) {
            writer.uint32(32).bool(message.canSeal);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePolicyManagerCapability();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.manager = reader.string();
                    break;
                case 2:
                    message.action = reader.int32();
                    break;
                case 3:
                    message.canDisable = reader.bool();
                    break;
                case 4:
                    message.canSeal = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            manager: isSet(object.manager) ? String(object.manager) : "",
            action: isSet(object.action) ? actionFromJSON(object.action) : 0,
            canDisable: isSet(object.canDisable) ? Boolean(object.canDisable) : false,
            canSeal: isSet(object.canSeal) ? Boolean(object.canSeal) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.manager !== undefined && (obj.manager = message.manager);
        message.action !== undefined && (obj.action = actionToJSON(message.action));
        message.canDisable !== undefined && (obj.canDisable = message.canDisable);
        message.canSeal !== undefined && (obj.canSeal = message.canSeal);
        return obj;
    },
    create: function (base) {
        return exports.PolicyManagerCapability.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBasePolicyManagerCapability();
        message.manager = (_a = object.manager) !== null && _a !== void 0 ? _a : "";
        message.action = (_b = object.action) !== null && _b !== void 0 ? _b : 0;
        message.canDisable = (_c = object.canDisable) !== null && _c !== void 0 ? _c : false;
        message.canSeal = (_d = object.canSeal) !== null && _d !== void 0 ? _d : false;
        return message;
    },
};
function createBaseRoleIDs() {
    return { roleIds: [] };
}
exports.RoleIDs = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        writer.uint32(10).fork();
        try {
            for (var _b = __values(message.roleIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(v);
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRoleIDs();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.roleIds.push(reader.uint32());
                        }
                    }
                    else {
                        message.roleIds.push(reader.uint32());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { roleIds: Array.isArray(object === null || object === void 0 ? void 0 : object.roleIds) ? object.roleIds.map(function (e) { return Number(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.roleIds) {
            obj.roleIds = message.roleIds.map(function (e) { return Math.round(e); });
        }
        else {
            obj.roleIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.RoleIDs.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseRoleIDs();
        message.roleIds = ((_a = object.roleIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseAddressVoucher() {
    return { address: "", voucher: undefined };
}
exports.AddressVoucher = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.voucher !== undefined) {
            coin_1.Coin.encode(message.voucher, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAddressVoucher();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.voucher = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            voucher: isSet(object.voucher) ? coin_1.Coin.fromJSON(object.voucher) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.voucher !== undefined && (obj.voucher = message.voucher ? coin_1.Coin.toJSON(message.voucher) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.AddressVoucher.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseAddressVoucher();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        message.voucher = (object.voucher !== undefined && object.voucher !== null)
            ? coin_1.Coin.fromPartial(object.voucher)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
