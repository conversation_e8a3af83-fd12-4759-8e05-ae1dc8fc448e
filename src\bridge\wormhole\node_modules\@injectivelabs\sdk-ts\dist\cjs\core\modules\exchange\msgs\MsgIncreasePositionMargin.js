"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const numbers_js_1 = require("../../../../utils/numbers.js");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const createMessage = (params) => {
    const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin.create();
    message.sender = params.injectiveAddress;
    message.sourceSubaccountId = params.srcSubaccountId;
    message.destinationSubaccountId = params.dstSubaccountId;
    message.marketId = params.marketId;
    message.amount = params.amount;
    return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin.fromPartial(message);
};
/**
 * @category Messages
 */
class MsgIncreasePositionMargin extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgIncreasePositionMargin(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            amount: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.amount).toFixed(),
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgIncreasePositionMargin',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const msg = createMessage(params);
        const message = {
            ...(0, snakecase_keys_1.default)(msg),
        };
        return {
            type: 'exchange/MsgIncreasePositionMargin',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgIncreasePositionMargin',
            ...value,
        };
    }
    toEip712() {
        const amino = this.toAmino();
        const { type, value } = amino;
        const messageAdjusted = {
            ...value,
            amount: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.amount).toFixed(),
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const messageAdjusted = {
            ...web3gw,
            amount: (0, numbers_js_1.numberToCosmosSdkDecString)(params.amount),
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgIncreasePositionMargin',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin.encode(this.toProto()).finish();
    }
}
exports.default = MsgIncreasePositionMargin;
