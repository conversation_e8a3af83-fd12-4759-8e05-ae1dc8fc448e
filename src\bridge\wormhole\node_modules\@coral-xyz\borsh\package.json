{"name": "@coral-xyz/borsh", "version": "0.29.0", "description": "<PERSON><PERSON>", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js"}, "license": "Apache-2.0", "publishConfig": {"access": "public"}, "engines": {"node": ">=10"}, "scripts": {"build": "tsc", "test": "", "clean": "rm -rf dist"}, "dependencies": {"bn.js": "^5.1.2", "buffer-layout": "^1.2.0"}, "peerDependencies": {"@solana/web3.js": "^1.68.0"}, "files": ["dist"], "devDependencies": {"@types/node": "^18.11.10"}}