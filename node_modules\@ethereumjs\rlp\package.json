{"name": "@ethereumjs/rlp", "version": "5.0.2", "description": "Recursive Length Prefix Encoding Module", "keywords": ["rlp", "ethereum"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/rlp#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+rlp%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MPL-2.0", "author": {"name": "martin be<PERSON>e", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <Holger.<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>"], "type": "module", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "bin": {"rlp": "bin/rlp.cjs"}, "files": ["dist", "bin", "src"], "scripts": {"benchmarks": "node ./benchmarks/index.js", "build": "../../config/cli/ts-build.sh node", "build:benchmarks": "npm run build && tsc -p tsconfig.benchmarks.json", "clean": "../../config/cli/clean-package.sh", "coverage": "c8 --all --reporter=lcov --reporter=text npm run test:node", "examples": "tsx ../../scripts/examples-runner.ts -- rlp", "examples:build": "npx embedme README.md", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "prepublishOnly": "../../config/cli/prepublish.sh", "test": "npm run test:node && npm run test:browser", "test:browser": "npx vitest run --browser.name=chrome --browser.headless -c=vitest.config.browser.ts", "test:node": "npx vitest run", "tsc": "../../config/cli/ts-compile.sh"}, "engines": {"node": ">=18"}, "devDependencies": {"benchmark": "^2.1.4"}}