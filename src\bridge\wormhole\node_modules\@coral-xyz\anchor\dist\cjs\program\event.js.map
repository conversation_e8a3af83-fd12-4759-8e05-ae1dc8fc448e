{"version": 3, "file": "event.js", "sourceRoot": "", "sources": ["../../../src/program/event.ts"], "names": [], "mappings": ";;;AAMA,MAAM,WAAW,GAAG,eAAe,CAAC;AACpC,MAAM,YAAY,GAAG,gBAAgB,CAAC;AACtC,MAAM,uBAAuB,GAAG,WAAW,CAAC,MAAM,CAAC;AACnD,MAAM,wBAAwB,GAAG,YAAY,CAAC,MAAM,CAAC;AAiBrD,MAAa,YAAY;IAoCvB,YAAY,SAAoB,EAAE,QAAkB,EAAE,KAAY;QAChE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEM,gBAAgB,CACrB,SAAiB,EACjB,QAA+D;;QAE/D,IAAI,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACrC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;QAE3B,yCAAyC;QACzC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,eAAe,CAAC,GAAG,CACtB,SAAS,EACT,CAAC,MAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,mCAAI,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAC7D,CAAC;QAEF,4CAA4C;QAC5C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;QAE1D,gDAAgD;QAChD,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS,EAAE;YAC5C,OAAO,QAAQ,CAAC;SACjB;QAED,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAU,CAAC,UAAU,CAAC,MAAM,CAC5D,IAAI,CAAC,UAAU,EACf,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;YACZ,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,OAAO;aACR;YAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC1D,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAE1D,IAAI,YAAY,EAAE;oBAChB,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;wBAChC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;wBAEtD,IAAI,UAAU,EAAE;4BACd,MAAM,CAAC,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC;4BAChC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;yBAChD;oBACH,CAAC,CAAC,CAAC;iBACJ;aACF;QACH,CAAC,CACF,CAAC;QAEF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAC/C,oBAAoB;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,kBAAkB,QAAQ,iBAAiB,CAAC,CAAC;SAC9D;QACD,MAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;QAE7B,qBAAqB;QACrB,IAAI,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,mCAAmC,SAAS,GAAG,CAAC,CAAC;SAClE;QAED,oBAAoB;QACpB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC/C,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SACxC;QAED,oEAAoE;QACpE,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE;YACnC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE;gBACnC,MAAM,IAAI,KAAK,CACb,iDAAiD,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAC7E,CAAC;aACH;YAED,IAAI,IAAI,CAAC,qBAAqB,KAAK,SAAS,EAAE;gBAC5C,MAAM,IAAI,CAAC,SAAU,CAAC,UAAU,CAAC,oBAAoB,CACnD,IAAI,CAAC,qBAAqB,CAC3B,CAAC;gBACF,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;aACxC;SACF;IACH,CAAC;CACF;AAtID,oCAsIC;AAED,MAAa,WAAW;IAItB,YAAY,SAAoB,EAAE,KAAY;QAC5C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,6DAA6D;IAC7D,yEAAyE;IACzE,sEAAsE;IACtE,4EAA4E;IAC5E,sEAAsE;IACtE,0EAA0E;IAC1E,uEAAuE;IACvE,0EAA0E;IAC1E,qEAAqE;IACrE,yEAAyE;IACzE,sEAAsE;IAC/D,CAAC,SAAS,CAAC,IAAc,EAAE,uBAAgC,KAAK;QACrE,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACzC,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;QAC5B,OAAO,GAAG,KAAK,IAAI,EAAE;YACnB,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAC9C,SAAS,EACT,GAAG,EACH,oBAAoB,CACrB,CAAC;YACF,IAAI,KAAK,EAAE;gBACT,MAAM,KAAK,CAAC;aACb;YACD,IAAI,UAAU,EAAE;gBACd,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC5B;YACD,IAAI,MAAM,EAAE;gBACV,SAAS,CAAC,GAAG,EAAE,CAAC;aACjB;YACD,GAAG,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;SACzB;IACH,CAAC;IAED,oEAAoE;IACpE,qEAAqE;IACrE,uEAAuE;IACvE,oBAAoB;IACZ,SAAS,CACf,SAA2B,EAC3B,GAAW,EACX,oBAA6B;QAE7B,qCAAqC;QACrC,IACE,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;YAC1B,SAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EACjD;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;SACzD;QACD,yCAAyC;aACpC;YACH,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7C;IACH,CAAC;IAED,oCAAoC;IAC5B,gBAAgB,CACtB,GAAW,EACX,oBAA6B;QAE7B,gDAAgD;QAChD,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;YAC/D,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC;gBACxC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC;gBACpC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YACxC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAE/C,IAAI,oBAAoB,IAAI,KAAK,KAAK,IAAI,EAAE;gBAC1C,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,EAAE,CAAC,CAAC;aACrD;YACD,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC7B;QACD,cAAc;aACT;YACH,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7C;IACH,CAAC;IAED,uEAAuE;IAC/D,eAAe,CAAC,GAAW;QACjC,oBAAoB;QACpB,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnC,oCAAoC;QACpC,IAAI,QAAQ,CAAC,KAAK,CAAC,wBAAwB,CAAC,KAAK,IAAI,EAAE;YACrD,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpB,kBAAkB;SACnB;aAAM,IACL,QAAQ,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,EAClE;YACA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;SAC3C;QACD,YAAY;aACP,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACpC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,sBAAsB;SAC9C;aAAM;YACL,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACtB;IACH,CAAC;CACF;AA7GD,kCA6GC;AAED,uEAAuE;AACvE,6BAA6B;AAC7B,MAAM,gBAAgB;IAAtB;QACE,UAAK,GAAa,EAAE,CAAC;IAmBvB,CAAC;IAjBC,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACxD;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,CAAC,UAAkB;QACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED,GAAG;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IACnB,CAAC;CACF;AAED,MAAM,UAAU;IACd,YAAmB,IAAc;QAAd,SAAI,GAAJ,IAAI,CAAU;IAAG,CAAC;IAErC,IAAI;QACF,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,OAAO,CAAC,CAAC;IACX,CAAC;CACF"}