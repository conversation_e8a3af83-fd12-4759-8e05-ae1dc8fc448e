"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OkxWalletException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
const maps_js_1 = require("../utils/maps.js");
const removeOkxWalletFromErrorString = (message) => message
    .replaceAll('OkxWallet', '')
    .replaceAll('Okx', '')
    .replaceAll('OkxWallet:', '');
class OkxWalletException extends base_js_1.ConcreteException {
    static errorClass = 'OkxWalletException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setMessage((0, maps_js_1.mapMetamaskMessage)(removeOkxWalletFromErrorString(message)));
        this.setName(OkxWalletException.errorClass);
    }
}
exports.OkxWalletException = OkxWalletException;
