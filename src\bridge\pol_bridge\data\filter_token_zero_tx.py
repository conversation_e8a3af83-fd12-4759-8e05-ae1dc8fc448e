#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import argparse
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime

# 文件路径
FILTERED_TOKENS_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "filtered_tokens.json")
# 新的输出文件路径
OUTPUT_TOKENS_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "zero_tx_tokens.json")

# API端点
API_ENDPOINTS = {
    "polygonscan": "https://api.polygonscan.com/api"
}

# API密钥
API_KEYS = {
    "polygonscan": "GPHHB2FE5KTCCR27A44E89D695N7WUBXCF"
}

# 0地址
ZERO_ADDRESS = "0x0000000000000000000000000000000000000000"

def load_filtered_tokens() -> Dict[str, Any]:
    """
    加载filtered_tokens.json中的代币数据
    
    Returns:
        Dict[str, Any]: 过滤后的代币数据
    """
    try:
        if not os.path.exists(FILTERED_TOKENS_PATH):
            print(f"错误: 文件不存在 - {FILTERED_TOKENS_PATH}")
            return {}
            
        with open(FILTERED_TOKENS_PATH, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
            
        return tokens
    except Exception as e:
        print(f"加载过滤代币数据失败: {str(e)}")
        return {}

def load_output_tokens() -> Dict[str, Any]:
    """
    加载已存在的输出文件中的代币数据
    
    Returns:
        Dict[str, Any]: 现有的输出代币数据
    """
    try:
        if not os.path.exists(OUTPUT_TOKENS_PATH):
            return {}
            
        with open(OUTPUT_TOKENS_PATH, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
            
        return tokens
    except Exception as e:
        print(f"加载输出代币数据失败: {str(e)}")
        return {}

def save_output_tokens(tokens: Dict[str, Any]) -> bool:
    """
    保存代币数据到输出文件
    
    Args:
        tokens: 要保存的代币数据
        
    Returns:
        bool: 是否保存成功
    """
    try:
        with open(OUTPUT_TOKENS_PATH, 'w', encoding='utf-8') as f:
            json.dump(tokens, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存代币数据失败: {str(e)}")
        return False

def get_token_transactions(token_address: str, minutes: int = 2888, api_key: str = None) -> List[Dict[str, Any]]:
    """
    获取代币最近的交易
    
    Args:
        token_address: 代币地址
        minutes: 过去几分钟的交易(默认2888分钟)
        api_key: API密钥(可选)
        
    Returns:
        List[Dict[str, Any]]: 交易列表
    """
    api_endpoint = API_ENDPOINTS.get("polygonscan")
    
    # 计算时间范围
    now = int(time.time())
    start_time = now - (minutes * 60)
    
    # 构建API请求
    params = {
        "module": "account",
        "action": "tokentx",
        "contractaddress": token_address,
        "startblock": 0,
        "endblock": *********,
        "sort": "desc",
        "page": 1,
        "offset": 200  # 限制结果数量
    }
    
    if api_key:
        params["apikey"] = api_key
    
    try:
        print(f"正在请求API: {api_endpoint}")
        print(f"请求参数: {params}")
        
        response = requests.get(api_endpoint, params=params)
        
        if response.status_code != 200:
            print(f"API请求失败，状态码: {response.status_code}")
            return []
            
        data = response.json()
        print(f"API响应状态: {data.get('status')}, 消息: {data.get('message', 'No message')}")
        
        transactions = []
        
        if data["status"] == "1":
            # 筛选最近n分钟的交易
            for tx in data["result"]:
                tx_timestamp = int(tx["timeStamp"])
                
                if tx_timestamp >= start_time:
                    transactions.append({
                        "hash": tx["hash"],
                        "from": tx["from"].lower(),
                        "to": tx["to"].lower(),
                        "timestamp": tx_timestamp,
                        "datetime": datetime.fromtimestamp(tx_timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    })
                else:
                    # 已经超过时间范围
                    break
            
            print(f"获取到 {len(transactions)} 笔交易")
        else:
            print(f"API请求错误: {data.get('message', 'Unknown error')}")
            
            # 如果是API速率限制错误, 等待并重试
            if "rate limit" in data.get('message', '').lower() or "ratelimit" in data.get('message', '').lower():
                print("触发API速率限制，等待30秒后重试")
                time.sleep(30)
                return get_token_transactions(token_address, minutes, api_key)
        
        return transactions
    
    except Exception as e:
        print(f"获取代币交易失败: {str(e)}")
        return []

def check_zero_address_transactions(token_address: str, minutes: int = 2888) -> bool:
    """
    检查代币是否有与0地址相关的交易
    
    Args:
        token_address: 代币地址
        minutes: 查询过去几分钟的交易
        
    Returns:
        bool: 是否存在与0地址相关的交易
    """
    transactions = get_token_transactions(token_address, minutes, API_KEYS.get("polygonscan"))
    
    if not transactions:
        print(f"未找到任何交易，无法判断是否存在0地址交易")
        return False
    
    # 检查是否存在0地址交易
    for tx in transactions:
        if tx["from"] == ZERO_ADDRESS.lower() or tx["to"] == ZERO_ADDRESS.lower():
            print(f"找到与0地址相关的交易: {tx['hash']}")
            print(f"  时间: {tx['datetime']}")
            print(f"  从: {tx['from']}")
            print(f"  到: {tx['to']}")
            return True
    
    print(f"未找到与0地址相关的交易")
    return False

def filter_tokens_with_zero_tx(minutes: int = 2888):
    """
    筛选出存在0地址交易的代币，并保存到新文件
    
    Args:
        minutes: 查询过去几分钟的交易
    """
    # 加载原始代币数据
    tokens = load_filtered_tokens()
    if not tokens:
        print("未找到有效的代币数据")
        return
    
    print(f"从 {FILTERED_TOKENS_PATH} 加载了 {len(tokens)} 个代币")
    
    # 加载已存在的输出文件数据
    output_tokens = load_output_tokens()
    print(f"从 {OUTPUT_TOKENS_PATH} 加载了 {len(output_tokens)} 个现有代币")
    
    tokens_with_zero_tx = {}
    processed_count = 0
    added_count = 0
    updated_count = 0
    
    # 当前时间戳
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 检查每个代币
    for token_address, token_data in tokens.items():
        processed_count += 1
        print(f"\n处理代币 {processed_count}/{len(tokens)}: {token_address}")
        print(f"符号: {token_data.get('symbol', 'Unknown')}")
        
        # 获取Polygon地址
        polygon_address = None
        
        if "polygon_info" in token_data and "address" in token_data["polygon_info"]:
            polygon_address = token_data["polygon_info"]["address"]
        else:
            # 如果本身就是地址键
            polygon_address = token_address
        
        if not polygon_address:
            print(f"找不到Polygon地址，跳过")
            continue
        
        # 如果该代币已在输出文件中，只更新时间戳
        if token_address in output_tokens:
            print(f"该代币已存在于输出文件中，仅更新检查时间")
            output_tokens[token_address]["check_time"] = current_time
            updated_count += 1
            continue
        
        # 查询是否存在0地址交易
        has_zero_tx = check_zero_address_transactions(polygon_address, minutes)
        
        if has_zero_tx:
            print(f"找到具有0地址交易的代币: {token_data.get('symbol', 'Unknown')} ({polygon_address})")
            # 复制代币数据并添加时间戳
            token_data_with_time = token_data.copy()
            token_data_with_time["check_time"] = current_time
            tokens_with_zero_tx[token_address] = token_data_with_time
            added_count += 1
        
        # API限速控制
        if processed_count < len(tokens):
            print("等待5秒后处理下一个代币...")
            time.sleep(5)
    
    # 合并新找到的代币与现有输出文件中的代币
    if tokens_with_zero_tx or updated_count > 0:
        output_tokens.update(tokens_with_zero_tx)
        
        # 保存结果
        save_success = save_output_tokens(output_tokens)
        
        if save_success:
            print(f"\n已成功将 {added_count} 个具有0地址交易的代币添加到 {OUTPUT_TOKENS_PATH}")
            print(f"已更新 {updated_count} 个现有代币的检查时间")
            print(f"输出文件现包含 {len(output_tokens)} 个代币")
        else:
            print("保存数据失败")
    else:
        print("\n未找到新的具有0地址交易的代币，也没有更新现有代币")

def main():
    """命令行入口点"""
    parser = argparse.ArgumentParser(description="筛选存在0地址交易的代币并保存到新文件")
    parser.add_argument("--minutes", type=int, default=2888, help="查询过去几分钟的交易 (默认: 2888)")
    
    args = parser.parse_args()
    
    # 执行筛选
    filter_tokens_with_zero_tx(args.minutes)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}") 