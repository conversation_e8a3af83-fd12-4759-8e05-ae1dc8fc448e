{"version": 3, "file": "baseTransaction.js", "sourceRoot": "", "sources": ["../src/baseTransaction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA4D;AAC5D,mDAWwB;AACxB,iCAUgB;AAUhB;;;;;;GAMG;AACH;IA+CE,yBAAY,MAAiE,EAAE,IAAe;QAhCpF,UAAK,GAAqB;YAClC,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,SAAS;SACnB,CAAA;QAID;;;;WAIG;QACO,uBAAkB,GAAa,EAAE,CAAA;QAE3C;;;;;;;WAOG;QACO,kBAAa,GAAG,cAAK,CAAC,OAAO,CAAA;QAEvC;;;;;WAKG;QACO,qBAAgB,GAAsB,iBAAQ,CAAC,QAAQ,CAAA;QAGvD,IAAA,KAAK,GAA+C,MAAM,MAArD,EAAE,QAAQ,GAAqC,MAAM,SAA3C,EAAE,EAAE,GAAiC,MAAM,GAAvC,EAAE,KAAK,GAA0B,MAAM,MAAhC,EAAE,IAAI,GAAoB,MAAM,KAA1B,EAAE,CAAC,GAAiB,MAAM,EAAvB,EAAE,CAAC,GAAc,MAAM,EAApB,EAAE,CAAC,GAAW,MAAM,EAAjB,EAAE,IAAI,GAAK,MAAM,KAAX,CAAW;QAClE,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;QAE9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QAErB,IAAM,GAAG,GAAG,IAAA,0BAAQ,EAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAC3C,IAAM,EAAE,GAAG,IAAA,0BAAQ,EAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACxC,IAAM,EAAE,GAAG,IAAA,0BAAQ,EAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACxC,IAAM,EAAE,GAAG,IAAA,0BAAQ,EAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAExC,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;QAC1D,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;QACnE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,yBAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACvD,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;QAC1D,IAAI,CAAC,IAAI,GAAG,IAAA,0BAAQ,EAAC,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QAE/C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,oBAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAC/C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,oBAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAC/C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,oBAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAE/C,IAAI,CAAC,+BAA+B,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAA;QAEjF,iCAAiC;QACjC,IAAI,CAAC,+BAA+B,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAA;QAErE,wDAAwD;QACxD,IAAI,CAAC,+BAA+B,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;IACvE,CAAC;IAOD,sBAAI,4CAAe;QALnB;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,IAAI,CAAA;QAClB,CAAC;;;OAAA;IAOD,sBAAI,iCAAI;QALR;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,KAAK,CAAA;QACnB,CAAC;;;OAAA;IAED;;;;;;;;;;;;;;;OAeG;IACH,kCAAQ,GAAR,UAAS,UAAsB;QAC7B,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;IACrD,CAAC;IASD,kCAAQ,GAAR,UAAS,WAA4B;QAA5B,4BAAA,EAAA,mBAA4B;QACnC,IAAM,MAAM,GAAG,EAAE,CAAA;QAEjB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACvC,MAAM,CAAC,IAAI,CAAC,qCAA8B,IAAI,CAAC,QAAQ,6BAAmB,IAAI,CAAC,UAAU,EAAE,CAAE,CAAC,CAAA;SAC/F;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC9C,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;SACjC;QAED,OAAO,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;IACnD,CAAC;IAED;;OAEG;IACH,oCAAU,GAAV;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAA;QACxE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YACpE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAA;SACxD;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;OAEG;IACH,oCAAU,GAAV;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;QAC/D,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;QAErE,IAAI,IAAI,GAAgB,CAAC,CAAA;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,aAAa,CAAC,CAAA;SACpE;QAED,IAAI,GAAG,IAAI,oBAAE,CAAC,IAAI,CAAC,CAAA;QACnB,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACnF,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAA;YACnD,IAAM,YAAY,GAAG,IAAI,oBAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC,KAAK,CACnF,UAAU,CACX,CAAA;YACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;SACxB;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAOD;;OAEG;IACH,2CAAiB,GAAjB;QACE,OAAO,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,CAAA;IAC1D,CAAC;IA8BM,kCAAQ,GAAf;QACQ,IAAA,KAAc,IAAI,EAAhB,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAS,CAAA;QACxB,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;gBAClB,OAAO,KAAK,CAAA;aACb;iBAAM;gBACL,OAAO,IAAI,CAAA;aACZ;SACF;aAAM;YACL,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;gBAC/B,OAAO,KAAK,CAAA;aACb;iBAAM;gBACL,OAAO,IAAI,CAAA;aACZ;SACF;IACH,CAAC;IAED;;OAEG;IACH,yCAAe,GAAf;QACE,IAAI;YACF,gEAAgE;YAChE,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAC3C,OAAO,IAAA,6BAAW,EAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAA;SAC3C;QAAC,OAAO,CAAM,EAAE;YACf,OAAO,KAAK,CAAA;SACb;IACH,CAAC;IAED;;OAEG;IACH,0CAAgB,GAAhB;QACE,OAAO,IAAI,yBAAO,CAAC,IAAA,iCAAe,EAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAA;IAChE,CAAC;IAOD;;;;;;;;OAQG;IACH,8BAAI,GAAJ,UAAK,UAAkB;QACrB,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;YAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAA;YACrE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,sHAAsH;QACtH,oEAAoE;QACpE,mFAAmF;QACnF,aAAa;QACb,IAAI,WAAW,GAAG,KAAK,CAAA;QACvB,IACE,IAAI,CAAC,IAAI,KAAK,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACzC,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,EACjD;YACA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;YAC/D,WAAW,GAAG,IAAI,CAAA;SACnB;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;QACrC,IAAA,KAAc,IAAA,wBAAM,EAAC,OAAO,EAAE,UAAU,CAAC,EAAvC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAgC,CAAA;QAC/C,IAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;QAE1C,cAAc;QACd,IAAI,WAAW,EAAE;YACf,IAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;YAChF,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;aACzC;SACF;QAED,OAAO,EAAE,CAAA;IACX,CAAC;IAUD;;;;;;;OAOG;IACO,oCAAU,GAApB,UAAqB,MAAe,EAAE,OAAgB;;QACpD,oBAAoB;QACpB,IAAI,OAAO,EAAE;YACX,IAAM,SAAS,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,OAAO,CAAC,CAAC,CAAA;YAC3C,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE;oBACrC,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,oDAAoD,CAAC,CAAA;oBAChF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;iBACrB;gBACD,uCAAuC;gBACvC,4BAA4B;gBAC5B,OAAO,MAAM,CAAC,IAAI,EAAE,CAAA;aACrB;iBAAM;gBACL,IAAI,gBAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE;oBACxC,0CAA0C;oBAC1C,sCAAsC;oBACtC,OAAO,IAAI,gBAAM,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;iBACzE;qBAAM;oBACL,8CAA8C;oBAC9C,0DAA0D;oBAC1D,OAAO,gBAAM,CAAC,cAAc,CAC1B,IAAI,CAAC,aAAa,EAClB;wBACE,IAAI,EAAE,cAAc;wBACpB,SAAS,EAAE,SAAS;wBACpB,OAAO,EAAE,SAAS;qBACnB,EACD,IAAI,CAAC,gBAAgB,CACtB,CAAA;iBACF;aACF;SACF;aAAM;YACL,uBAAuB;YACvB,yDAAyD;YACzD,OAAO,CACL,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,EAAE,mCAAI,IAAI,gBAAM,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAC7F,CAAA;SACF;IACH,CAAC;IAED;;;;;OAKG;IACO,yDAA+B,GAAzC,UACE,MAAyC,EACzC,IAAU,EACV,WAAmB;;QADnB,qBAAA,EAAA,UAAU;QACV,4BAAA,EAAA,mBAAmB;;YAEnB,KAA2B,IAAA,KAAA,SAAA,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,gBAAA,4BAAE;gBAAxC,IAAA,KAAA,mBAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;gBACpB,QAAQ,IAAI,EAAE;oBACZ,KAAK,EAAE;wBACL,IAAI,WAAW,EAAE;4BACf,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,4BAAU,CAAC,EAAE;gCAC1B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,UAAG,GAAG,gEAAsD,KAAK,CAAE,CACpE,CAAA;gCACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;6BACrB;yBACF;6BAAM;4BACL,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,CAAC,4BAAU,CAAC,EAAE;gCACzB,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,UAAG,GAAG,uDAA6C,KAAK,CAAE,CAAC,CAAA;gCACtF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;6BACrB;yBACF;wBACD,MAAK;oBACP,KAAK,GAAG;wBACN,IAAI,WAAW,EAAE;4BACf,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,GAAG,CAAC,6BAAW,CAAC,EAAE;gCAC3B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,UAAG,GAAG,kEAAwD,KAAK,CAAE,CACtE,CAAA;gCACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;6BACrB;yBACF;6BAAM;4BACL,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,EAAE,CAAC,6BAAW,CAAC,EAAE;gCAC1B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,UAAG,GAAG,yDAA+C,KAAK,CAAE,CAC7D,CAAA;gCACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;6BACrB;yBACF;wBACD,MAAK;oBACP,OAAO,CAAC,CAAC;wBACP,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;wBACtD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;qBACrB;iBACF;aACF;;;;;;;;;IACH,CAAC;IAeD;;;OAGG;IACO,gDAAsB,GAAhC;QACE,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IAAI;YACF,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAA,6BAAW,EAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAA;SAC/E;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,GAAG,OAAO,CAAA;SACf;QACD,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,IAAI;YACF,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAA;SACtC;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,GAAG,OAAO,CAAA;SACf;QACD,IAAI,EAAE,GAAG,EAAE,CAAA;QACX,IAAI;YACF,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;SAC5B;QAAC,OAAO,CAAM,EAAE;YACf,EAAE,GAAG,OAAO,CAAA;SACb;QAED,IAAI,OAAO,GAAG,kBAAW,IAAI,CAAC,IAAI,mBAAS,IAAI,oBAAU,IAAI,CAAC,KAAK,oBAAU,IAAI,CAAC,KAAK,MAAG,CAAA;QAC1F,OAAO,IAAI,iBAAU,QAAQ,iBAAO,EAAE,CAAE,CAAA;QAExC,OAAO,OAAO,CAAA;IAChB,CAAC;IACH,sBAAC;AAAD,CAAC,AAhcD,IAgcC;AAhcqB,0CAAe"}