"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenFactoryStatic = void 0;
const index_js_1 = require("./../types/index.js");
/** @deprecated */
class TokenFactoryStatic {
    registry;
    tokensByDenom;
    tokensBySymbol;
    tokensByAddress;
    constructor(registry) {
        this.registry = registry;
        this.tokensByDenom = registry.reduce((list, token) => {
            const denom = token.denom.toLowerCase();
            if (list[denom]) {
                return list;
            }
            list[denom] = token;
            return list;
        }, {});
        this.tokensBySymbol = registry.reduce((list, token) => {
            const symbol = token.symbol.toLowerCase();
            if (list[symbol]) {
                list[symbol] = [...list[symbol], token];
                return list;
            }
            list[symbol] = [token];
            return list;
        }, {});
        this.tokensByAddress = registry.reduce((list, token) => {
            const address = token.address.toLowerCase();
            if (!address) {
                return list;
            }
            if (list[address]) {
                return list;
            }
            list[address] = [token];
            return list;
        }, {});
    }
    toToken(denom) {
        return this.getMetaByDenomOrAddress(denom) || this.getMetaBySymbol(denom);
    }
    getMetaBySymbol(symbol, { type, source, verification, } = {}) {
        const tokensBySymbol = this.tokensBySymbol[symbol.toLowerCase()];
        if (!tokensBySymbol) {
            return;
        }
        const token = tokensBySymbol.find((token) => {
            const isType = !type || token.tokenType === type;
            const isSource = !source || token.source === source;
            const isVerification = !verification || token.tokenVerification === verification;
            return isType && isSource && isVerification;
        });
        const sortedTokens = tokensBySymbol.sort((t1, t2) => {
            const t1IsVerified = t1.tokenVerification === index_js_1.TokenVerification.Verified;
            const t2IsVerified = t2.tokenVerification === index_js_1.TokenVerification.Verified;
            return t1IsVerified && !t2IsVerified ? -1 : 1;
        });
        return token || sortedTokens[0];
    }
    getMetaByDenomOrAddress(denomOrAddress) {
        const formattedDenom = denomOrAddress.toLowerCase();
        if (this.tokensByDenom[formattedDenom]) {
            return this.tokensByDenom[formattedDenom];
        }
        if (!this.tokensByAddress[formattedDenom]) {
            return;
        }
        const verifiedToken = this.tokensByAddress[formattedDenom].find(({ tokenVerification }) => tokenVerification === index_js_1.TokenVerification.Verified);
        return verifiedToken || this.tokensByAddress[formattedDenom][0];
    }
}
exports.TokenFactoryStatic = TokenFactoryStatic;
