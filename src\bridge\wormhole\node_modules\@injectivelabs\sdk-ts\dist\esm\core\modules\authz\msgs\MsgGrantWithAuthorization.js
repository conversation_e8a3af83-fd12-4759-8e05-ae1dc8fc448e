import { MsgBase } from '../../MsgBase.js';
import snakecaseKeys from 'snakecase-keys';
import { CosmosAuthzV1Beta1Tx, CosmosAuthzV1Beta1Authz, GoogleProtobufTimestamp, } from '@injectivelabs/core-proto-ts';
import { GeneralException } from '@injectivelabs/exceptions';
/**
 * @category Messages
 */
export default class MsgGrantWithAuthorization extends MsgBase {
    static fromJSON(params) {
        return new MsgGrantWithAuthorization(params);
    }
    toProto() {
        const { params } = this;
        const timestamp = this.getTimestamp();
        const grant = CosmosAuthzV1Beta1Authz.Grant.create();
        grant.authorization = params.authorization.toAny();
        grant.expiration = new Date(Number(timestamp.seconds) * 1000);
        const message = CosmosAuthzV1Beta1Tx.MsgGrant.create();
        message.granter = params.granter;
        message.grantee = params.grantee;
        message.grant = grant;
        return CosmosAuthzV1Beta1Tx.MsgGrant.fromJSON(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.authz.v1beta1.MsgGrant',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const proto = this.toProto();
        const timestamp = this.getTimestamp();
        const message = proto;
        const messageWithAuthorizationType = snakecaseKeys({
            ...message,
            grant: {
                authorization: params.authorization.toAmino(),
                expiration: new Date(Number(timestamp.seconds) * 1000)
                    .toISOString()
                    .replace('.000Z', 'Z'),
            },
        });
        return {
            type: 'cosmos-sdk/MsgGrant',
            value: messageWithAuthorizationType,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.authz.v1beta1.MsgGrant',
            message: proto,
        };
    }
    toWeb3Gw() {
        const { params } = this;
        const amino = this.toAmino();
        const timestamp = this.getTimestamp();
        const messageWithAuthorizationType = {
            granter: amino.value.granter,
            grantee: amino.value.grantee,
            grant: {
                authorization: params.authorization.toWeb3(),
                expiration: new Date(Number(timestamp.seconds) * 1000)
                    .toISOString()
                    .replace('.000Z', 'Z'),
            },
        };
        return {
            '@type': '/cosmos.authz.v1beta1.MsgGrant',
            ...messageWithAuthorizationType,
        };
    }
    toEip712() {
        throw new GeneralException(new Error('EIP712_v1 is not supported for MsgGrantWithAuthorization. Please use EIP712_v2'));
    }
    getTimestamp() {
        const { params } = this;
        if (params.expiration) {
            const timestamp = GoogleProtobufTimestamp.Timestamp.create();
            timestamp.seconds = params.expiration.toString();
            return timestamp;
        }
        const defaultExpiryYears = params.expiryInSeconds ? 0 : 5;
        const dateNow = new Date();
        const expiration = new Date(dateNow.getFullYear() + (params.expiryInYears || defaultExpiryYears), dateNow.getMonth(), dateNow.getDate());
        const timestamp = GoogleProtobufTimestamp.Timestamp.create();
        const timestampInSeconds = (expiration.getTime() / 1000 +
            (params.expiryInSeconds || 0)).toString();
        timestamp.seconds = timestampInSeconds;
        return timestamp;
    }
    toBinary() {
        return CosmosAuthzV1Beta1Tx.MsgGrant.encode(this.toProto()).finish();
    }
}
