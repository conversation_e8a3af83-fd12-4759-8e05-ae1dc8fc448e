{"version": 3, "sources": ["../../src/core/crypto/utils.ts"], "sourcesContent": ["import { HexInput } from \"../../types\";\nimport { Hex } from \"../hex\";\n\n/**\n * Helper function to convert a message to sign or to verify to a valid message input\n *\n * @param message a message as a string or Uint8Array\n *\n * @returns a valid HexInput - string or Uint8Array\n * @group Implementation\n * @category Serialization\n */\nexport const convertSigningMessage = (message: HexInput): HexInput => {\n  // if message is of type string, verify it is a valid Hex string\n  if (typeof message === \"string\") {\n    const isValid = Hex.isValid(message);\n    // If message is not a valid Hex string, convert it\n    if (!isValid.valid) {\n      return new TextEncoder().encode(message);\n    }\n    // If message is a valid Hex string, return it\n    return message;\n  }\n  // message is a Uint8Array\n  return message;\n};\n"], "mappings": "yCAYO,IAAMA,EAAyBC,GAEhC,OAAOA,GAAY,SACLC,EAAI,QAAQD,CAAO,EAEtB,MAINA,EAHE,IAAI,YAAY,EAAE,OAAOA,CAAO,EAMpCA", "names": ["convertSigningMessage", "message", "Hex"]}