"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchBlocksResult = exports.SearchTxsResult = exports.TxMsgData = exports.MsgData = exports.SimulationResponse = exports.Result = exports.GasInfo = exports.Attribute = exports.StringEvent = exports.ABCIMessageLog = exports.TxResponse = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var any_1 = require("../../../../google/protobuf/any.js");
var types_1 = require("../../../../tendermint/abci/types.js");
var block_1 = require("../../../../tendermint/types/block.js");
exports.protobufPackage = "cosmos.base.abci.v1beta1";
function createBaseTxResponse() {
    return {
        height: "0",
        txhash: "",
        codespace: "",
        code: 0,
        data: "",
        rawLog: "",
        logs: [],
        info: "",
        gasWanted: "0",
        gasUsed: "0",
        tx: undefined,
        timestamp: "",
        events: [],
    };
}
exports.TxResponse = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        if (message.txhash !== "") {
            writer.uint32(18).string(message.txhash);
        }
        if (message.codespace !== "") {
            writer.uint32(26).string(message.codespace);
        }
        if (message.code !== 0) {
            writer.uint32(32).uint32(message.code);
        }
        if (message.data !== "") {
            writer.uint32(42).string(message.data);
        }
        if (message.rawLog !== "") {
            writer.uint32(50).string(message.rawLog);
        }
        try {
            for (var _c = __values(message.logs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exports.ABCIMessageLog.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.info !== "") {
            writer.uint32(66).string(message.info);
        }
        if (message.gasWanted !== "0") {
            writer.uint32(72).int64(message.gasWanted);
        }
        if (message.gasUsed !== "0") {
            writer.uint32(80).int64(message.gasUsed);
        }
        if (message.tx !== undefined) {
            any_1.Any.encode(message.tx, writer.uint32(90).fork()).ldelim();
        }
        if (message.timestamp !== "") {
            writer.uint32(98).string(message.timestamp);
        }
        try {
            for (var _e = __values(message.events), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                types_1.Event.encode(v, writer.uint32(106).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTxResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                case 2:
                    message.txhash = reader.string();
                    break;
                case 3:
                    message.codespace = reader.string();
                    break;
                case 4:
                    message.code = reader.uint32();
                    break;
                case 5:
                    message.data = reader.string();
                    break;
                case 6:
                    message.rawLog = reader.string();
                    break;
                case 7:
                    message.logs.push(exports.ABCIMessageLog.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.info = reader.string();
                    break;
                case 9:
                    message.gasWanted = longToString(reader.int64());
                    break;
                case 10:
                    message.gasUsed = longToString(reader.int64());
                    break;
                case 11:
                    message.tx = any_1.Any.decode(reader, reader.uint32());
                    break;
                case 12:
                    message.timestamp = reader.string();
                    break;
                case 13:
                    message.events.push(types_1.Event.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            height: isSet(object.height) ? String(object.height) : "0",
            txhash: isSet(object.txhash) ? String(object.txhash) : "",
            codespace: isSet(object.codespace) ? String(object.codespace) : "",
            code: isSet(object.code) ? Number(object.code) : 0,
            data: isSet(object.data) ? String(object.data) : "",
            rawLog: isSet(object.rawLog) ? String(object.rawLog) : "",
            logs: Array.isArray(object === null || object === void 0 ? void 0 : object.logs) ? object.logs.map(function (e) { return exports.ABCIMessageLog.fromJSON(e); }) : [],
            info: isSet(object.info) ? String(object.info) : "",
            gasWanted: isSet(object.gasWanted) ? String(object.gasWanted) : "0",
            gasUsed: isSet(object.gasUsed) ? String(object.gasUsed) : "0",
            tx: isSet(object.tx) ? any_1.Any.fromJSON(object.tx) : undefined,
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "",
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return types_1.Event.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        message.txhash !== undefined && (obj.txhash = message.txhash);
        message.codespace !== undefined && (obj.codespace = message.codespace);
        message.code !== undefined && (obj.code = Math.round(message.code));
        message.data !== undefined && (obj.data = message.data);
        message.rawLog !== undefined && (obj.rawLog = message.rawLog);
        if (message.logs) {
            obj.logs = message.logs.map(function (e) { return e ? exports.ABCIMessageLog.toJSON(e) : undefined; });
        }
        else {
            obj.logs = [];
        }
        message.info !== undefined && (obj.info = message.info);
        message.gasWanted !== undefined && (obj.gasWanted = message.gasWanted);
        message.gasUsed !== undefined && (obj.gasUsed = message.gasUsed);
        message.tx !== undefined && (obj.tx = message.tx ? any_1.Any.toJSON(message.tx) : undefined);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? types_1.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TxResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        var message = createBaseTxResponse();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        message.txhash = (_b = object.txhash) !== null && _b !== void 0 ? _b : "";
        message.codespace = (_c = object.codespace) !== null && _c !== void 0 ? _c : "";
        message.code = (_d = object.code) !== null && _d !== void 0 ? _d : 0;
        message.data = (_e = object.data) !== null && _e !== void 0 ? _e : "";
        message.rawLog = (_f = object.rawLog) !== null && _f !== void 0 ? _f : "";
        message.logs = ((_g = object.logs) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.ABCIMessageLog.fromPartial(e); })) || [];
        message.info = (_h = object.info) !== null && _h !== void 0 ? _h : "";
        message.gasWanted = (_j = object.gasWanted) !== null && _j !== void 0 ? _j : "0";
        message.gasUsed = (_k = object.gasUsed) !== null && _k !== void 0 ? _k : "0";
        message.tx = (object.tx !== undefined && object.tx !== null) ? any_1.Any.fromPartial(object.tx) : undefined;
        message.timestamp = (_l = object.timestamp) !== null && _l !== void 0 ? _l : "";
        message.events = ((_m = object.events) === null || _m === void 0 ? void 0 : _m.map(function (e) { return types_1.Event.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseABCIMessageLog() {
    return { msgIndex: 0, log: "", events: [] };
}
exports.ABCIMessageLog = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.msgIndex !== 0) {
            writer.uint32(8).uint32(message.msgIndex);
        }
        if (message.log !== "") {
            writer.uint32(18).string(message.log);
        }
        try {
            for (var _b = __values(message.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.StringEvent.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseABCIMessageLog();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.msgIndex = reader.uint32();
                    break;
                case 2:
                    message.log = reader.string();
                    break;
                case 3:
                    message.events.push(exports.StringEvent.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            msgIndex: isSet(object.msgIndex) ? Number(object.msgIndex) : 0,
            log: isSet(object.log) ? String(object.log) : "",
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return exports.StringEvent.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.msgIndex !== undefined && (obj.msgIndex = Math.round(message.msgIndex));
        message.log !== undefined && (obj.log = message.log);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? exports.StringEvent.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ABCIMessageLog.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseABCIMessageLog();
        message.msgIndex = (_a = object.msgIndex) !== null && _a !== void 0 ? _a : 0;
        message.log = (_b = object.log) !== null && _b !== void 0 ? _b : "";
        message.events = ((_c = object.events) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.StringEvent.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseStringEvent() {
    return { type: "", attributes: [] };
}
exports.StringEvent = {
    encode: function (message, writer) {
        var e_4, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.type !== "") {
            writer.uint32(10).string(message.type);
        }
        try {
            for (var _b = __values(message.attributes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.Attribute.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseStringEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.string();
                    break;
                case 2:
                    message.attributes.push(exports.Attribute.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            type: isSet(object.type) ? String(object.type) : "",
            attributes: Array.isArray(object === null || object === void 0 ? void 0 : object.attributes) ? object.attributes.map(function (e) { return exports.Attribute.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.type !== undefined && (obj.type = message.type);
        if (message.attributes) {
            obj.attributes = message.attributes.map(function (e) { return e ? exports.Attribute.toJSON(e) : undefined; });
        }
        else {
            obj.attributes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.StringEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseStringEvent();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : "";
        message.attributes = ((_b = object.attributes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.Attribute.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseAttribute() {
    return { key: "", value: "" };
}
exports.Attribute = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== "") {
            writer.uint32(18).string(message.value);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAttribute();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { key: isSet(object.key) ? String(object.key) : "", value: isSet(object.value) ? String(object.value) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value);
        return obj;
    },
    create: function (base) {
        return exports.Attribute.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseAttribute();
        message.key = (_a = object.key) !== null && _a !== void 0 ? _a : "";
        message.value = (_b = object.value) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseGasInfo() {
    return { gasWanted: "0", gasUsed: "0" };
}
exports.GasInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.gasWanted !== "0") {
            writer.uint32(8).uint64(message.gasWanted);
        }
        if (message.gasUsed !== "0") {
            writer.uint32(16).uint64(message.gasUsed);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGasInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.gasWanted = longToString(reader.uint64());
                    break;
                case 2:
                    message.gasUsed = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            gasWanted: isSet(object.gasWanted) ? String(object.gasWanted) : "0",
            gasUsed: isSet(object.gasUsed) ? String(object.gasUsed) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.gasWanted !== undefined && (obj.gasWanted = message.gasWanted);
        message.gasUsed !== undefined && (obj.gasUsed = message.gasUsed);
        return obj;
    },
    create: function (base) {
        return exports.GasInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseGasInfo();
        message.gasWanted = (_a = object.gasWanted) !== null && _a !== void 0 ? _a : "0";
        message.gasUsed = (_b = object.gasUsed) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseResult() {
    return { data: new Uint8Array(), log: "", events: [], msgResponses: [] };
}
exports.Result = {
    encode: function (message, writer) {
        var e_5, _a, e_6, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.data.length !== 0) {
            writer.uint32(10).bytes(message.data);
        }
        if (message.log !== "") {
            writer.uint32(18).string(message.log);
        }
        try {
            for (var _c = __values(message.events), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                types_1.Event.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _e = __values(message.msgResponses), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                any_1.Any.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.data = reader.bytes();
                    break;
                case 2:
                    message.log = reader.string();
                    break;
                case 3:
                    message.events.push(types_1.Event.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.msgResponses.push(any_1.Any.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(),
            log: isSet(object.log) ? String(object.log) : "",
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return types_1.Event.fromJSON(e); }) : [],
            msgResponses: Array.isArray(object === null || object === void 0 ? void 0 : object.msgResponses) ? object.msgResponses.map(function (e) { return any_1.Any.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.data !== undefined &&
            (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array()));
        message.log !== undefined && (obj.log = message.log);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? types_1.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        if (message.msgResponses) {
            obj.msgResponses = message.msgResponses.map(function (e) { return e ? any_1.Any.toJSON(e) : undefined; });
        }
        else {
            obj.msgResponses = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Result.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseResult();
        message.data = (_a = object.data) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.log = (_b = object.log) !== null && _b !== void 0 ? _b : "";
        message.events = ((_c = object.events) === null || _c === void 0 ? void 0 : _c.map(function (e) { return types_1.Event.fromPartial(e); })) || [];
        message.msgResponses = ((_d = object.msgResponses) === null || _d === void 0 ? void 0 : _d.map(function (e) { return any_1.Any.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSimulationResponse() {
    return { gasInfo: undefined, result: undefined };
}
exports.SimulationResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.gasInfo !== undefined) {
            exports.GasInfo.encode(message.gasInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.result !== undefined) {
            exports.Result.encode(message.result, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSimulationResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.gasInfo = exports.GasInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.result = exports.Result.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            gasInfo: isSet(object.gasInfo) ? exports.GasInfo.fromJSON(object.gasInfo) : undefined,
            result: isSet(object.result) ? exports.Result.fromJSON(object.result) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.gasInfo !== undefined && (obj.gasInfo = message.gasInfo ? exports.GasInfo.toJSON(message.gasInfo) : undefined);
        message.result !== undefined && (obj.result = message.result ? exports.Result.toJSON(message.result) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.SimulationResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseSimulationResponse();
        message.gasInfo = (object.gasInfo !== undefined && object.gasInfo !== null)
            ? exports.GasInfo.fromPartial(object.gasInfo)
            : undefined;
        message.result = (object.result !== undefined && object.result !== null)
            ? exports.Result.fromPartial(object.result)
            : undefined;
        return message;
    },
};
function createBaseMsgData() {
    return { msgType: "", data: new Uint8Array() };
}
exports.MsgData = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.msgType !== "") {
            writer.uint32(10).string(message.msgType);
        }
        if (message.data.length !== 0) {
            writer.uint32(18).bytes(message.data);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgData();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.msgType = reader.string();
                    break;
                case 2:
                    message.data = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            msgType: isSet(object.msgType) ? String(object.msgType) : "",
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.msgType !== undefined && (obj.msgType = message.msgType);
        message.data !== undefined &&
            (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.MsgData.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgData();
        message.msgType = (_a = object.msgType) !== null && _a !== void 0 ? _a : "";
        message.data = (_b = object.data) !== null && _b !== void 0 ? _b : new Uint8Array();
        return message;
    },
};
function createBaseTxMsgData() {
    return { data: [], msgResponses: [] };
}
exports.TxMsgData = {
    encode: function (message, writer) {
        var e_7, _a, e_8, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.data), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exports.MsgData.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _e = __values(message.msgResponses), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                any_1.Any.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTxMsgData();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.data.push(exports.MsgData.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.msgResponses.push(any_1.Any.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            data: Array.isArray(object === null || object === void 0 ? void 0 : object.data) ? object.data.map(function (e) { return exports.MsgData.fromJSON(e); }) : [],
            msgResponses: Array.isArray(object === null || object === void 0 ? void 0 : object.msgResponses) ? object.msgResponses.map(function (e) { return any_1.Any.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.data) {
            obj.data = message.data.map(function (e) { return e ? exports.MsgData.toJSON(e) : undefined; });
        }
        else {
            obj.data = [];
        }
        if (message.msgResponses) {
            obj.msgResponses = message.msgResponses.map(function (e) { return e ? any_1.Any.toJSON(e) : undefined; });
        }
        else {
            obj.msgResponses = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TxMsgData.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseTxMsgData();
        message.data = ((_a = object.data) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.MsgData.fromPartial(e); })) || [];
        message.msgResponses = ((_b = object.msgResponses) === null || _b === void 0 ? void 0 : _b.map(function (e) { return any_1.Any.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSearchTxsResult() {
    return { totalCount: "0", count: "0", pageNumber: "0", pageTotal: "0", limit: "0", txs: [] };
}
exports.SearchTxsResult = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.totalCount !== "0") {
            writer.uint32(8).uint64(message.totalCount);
        }
        if (message.count !== "0") {
            writer.uint32(16).uint64(message.count);
        }
        if (message.pageNumber !== "0") {
            writer.uint32(24).uint64(message.pageNumber);
        }
        if (message.pageTotal !== "0") {
            writer.uint32(32).uint64(message.pageTotal);
        }
        if (message.limit !== "0") {
            writer.uint32(40).uint64(message.limit);
        }
        try {
            for (var _b = __values(message.txs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TxResponse.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSearchTxsResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.totalCount = longToString(reader.uint64());
                    break;
                case 2:
                    message.count = longToString(reader.uint64());
                    break;
                case 3:
                    message.pageNumber = longToString(reader.uint64());
                    break;
                case 4:
                    message.pageTotal = longToString(reader.uint64());
                    break;
                case 5:
                    message.limit = longToString(reader.uint64());
                    break;
                case 6:
                    message.txs.push(exports.TxResponse.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            totalCount: isSet(object.totalCount) ? String(object.totalCount) : "0",
            count: isSet(object.count) ? String(object.count) : "0",
            pageNumber: isSet(object.pageNumber) ? String(object.pageNumber) : "0",
            pageTotal: isSet(object.pageTotal) ? String(object.pageTotal) : "0",
            limit: isSet(object.limit) ? String(object.limit) : "0",
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return exports.TxResponse.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.totalCount !== undefined && (obj.totalCount = message.totalCount);
        message.count !== undefined && (obj.count = message.count);
        message.pageNumber !== undefined && (obj.pageNumber = message.pageNumber);
        message.pageTotal !== undefined && (obj.pageTotal = message.pageTotal);
        message.limit !== undefined && (obj.limit = message.limit);
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return e ? exports.TxResponse.toJSON(e) : undefined; });
        }
        else {
            obj.txs = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.SearchTxsResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseSearchTxsResult();
        message.totalCount = (_a = object.totalCount) !== null && _a !== void 0 ? _a : "0";
        message.count = (_b = object.count) !== null && _b !== void 0 ? _b : "0";
        message.pageNumber = (_c = object.pageNumber) !== null && _c !== void 0 ? _c : "0";
        message.pageTotal = (_d = object.pageTotal) !== null && _d !== void 0 ? _d : "0";
        message.limit = (_e = object.limit) !== null && _e !== void 0 ? _e : "0";
        message.txs = ((_f = object.txs) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.TxResponse.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSearchBlocksResult() {
    return { totalCount: "0", count: "0", pageNumber: "0", pageTotal: "0", limit: "0", blocks: [] };
}
exports.SearchBlocksResult = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.totalCount !== "0") {
            writer.uint32(8).int64(message.totalCount);
        }
        if (message.count !== "0") {
            writer.uint32(16).int64(message.count);
        }
        if (message.pageNumber !== "0") {
            writer.uint32(24).int64(message.pageNumber);
        }
        if (message.pageTotal !== "0") {
            writer.uint32(32).int64(message.pageTotal);
        }
        if (message.limit !== "0") {
            writer.uint32(40).int64(message.limit);
        }
        try {
            for (var _b = __values(message.blocks), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                block_1.Block.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSearchBlocksResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.totalCount = longToString(reader.int64());
                    break;
                case 2:
                    message.count = longToString(reader.int64());
                    break;
                case 3:
                    message.pageNumber = longToString(reader.int64());
                    break;
                case 4:
                    message.pageTotal = longToString(reader.int64());
                    break;
                case 5:
                    message.limit = longToString(reader.int64());
                    break;
                case 6:
                    message.blocks.push(block_1.Block.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            totalCount: isSet(object.totalCount) ? String(object.totalCount) : "0",
            count: isSet(object.count) ? String(object.count) : "0",
            pageNumber: isSet(object.pageNumber) ? String(object.pageNumber) : "0",
            pageTotal: isSet(object.pageTotal) ? String(object.pageTotal) : "0",
            limit: isSet(object.limit) ? String(object.limit) : "0",
            blocks: Array.isArray(object === null || object === void 0 ? void 0 : object.blocks) ? object.blocks.map(function (e) { return block_1.Block.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.totalCount !== undefined && (obj.totalCount = message.totalCount);
        message.count !== undefined && (obj.count = message.count);
        message.pageNumber !== undefined && (obj.pageNumber = message.pageNumber);
        message.pageTotal !== undefined && (obj.pageTotal = message.pageTotal);
        message.limit !== undefined && (obj.limit = message.limit);
        if (message.blocks) {
            obj.blocks = message.blocks.map(function (e) { return e ? block_1.Block.toJSON(e) : undefined; });
        }
        else {
            obj.blocks = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.SearchBlocksResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseSearchBlocksResult();
        message.totalCount = (_a = object.totalCount) !== null && _a !== void 0 ? _a : "0";
        message.count = (_b = object.count) !== null && _b !== void 0 ? _b : "0";
        message.pageNumber = (_c = object.pageNumber) !== null && _c !== void 0 ? _c : "0";
        message.pageTotal = (_d = object.pageTotal) !== null && _d !== void 0 ? _d : "0";
        message.limit = (_e = object.limit) !== null && _e !== void 0 ? _e : "0";
        message.blocks = ((_f = object.blocks) === null || _f === void 0 ? void 0 : _f.map(function (e) { return block_1.Block.fromPartial(e); })) || [];
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
