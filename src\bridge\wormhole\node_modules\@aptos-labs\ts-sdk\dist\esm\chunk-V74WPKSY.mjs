import{a as u,c as r}from"./chunk-Q4W3WJ2U.mjs";import{e as t}from"./chunk-ORMOQWWH.mjs";import{b as o}from"./chunk-RGKRCZ36.mjs";import{a as i}from"./chunk-EBMEXURY.mjs";var c=class extends i{constructor(e){super();this.accountAddress=o.ONE;this.moduleName=new r("account");this.structName=new r("RotationProofChallenge");this.sequenceNumber=new t(e.sequenceNumber),this.originator=e.originator,this.currentAuthKey=e.currentAuthKey,this.newPublicKey=u.U8(e.newPublicKey.toUint8Array())}serialize(e){e.serialize(this.accountAddress),e.serialize(this.moduleName),e.serialize(this.structName),e.serialize(this.sequenceNumber),e.serialize(this.originator),e.serialize(this.currentAuth<PERSON><PERSON>),e.serialize(this.newPublicKey)}};export{c as a};
//# sourceMappingURL=chunk-V74WPKSY.mjs.map