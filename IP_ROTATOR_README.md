# IP代理轮换器使用说明

## 概述

IP代理轮换器是一个自动切换代理IP的工具，支持每分钟自动轮换IP地址，适用于需要频繁更换IP的应用场景。

## 功能特性

- ✅ 自动IP轮换（每分钟切换一次）
- ✅ 支持多种地区节点（香港、台湾、日本、新加坡、美国、韩国等）
- ✅ 代理连接测试
- ✅ 手动切换代理
- ✅ 系统代理设置
- ✅ 实时状态监控
- ✅ Windows系统支持

## 文件结构

```
├── config/ip.yaml              # IP节点配置文件
├── src/utils/ip/
│   ├── ip_manager.py           # IP管理器核心模块
│   ├── ip_rotator.py           # 命令行工具
│   ├── proxy_server.py         # 代理服务器模块
│   └── __init__.py             # 模块初始化文件
├── ip_rotator.bat              # Windows批处理脚本
├── ip_rotator.ps1              # PowerShell脚本
└── IP_ROTATOR_README.md        # 使用说明（本文件）
```

## 配置说明

### IP节点配置 (config/ip.yaml)

配置文件包含以下部分：

1. **代理配置** - 基本代理设置
2. **DNS配置** - DNS解析设置
3. **代理节点列表** - 所有可用的代理节点
4. **轮换配置** - 自动轮换相关设置

### 轮换配置参数

```yaml
rotation_config:
  interval: 60                    # 轮换间隔（秒）
  enabled: true                   # 是否启用轮换
  preferred_regions: ["香港", "台湾", "日本", "新加坡", "美国", "韩国"]
  excluded_nodes: []              # 排除的节点
  test_timeout: 10                # 连接测试超时时间（秒）
  max_retries: 3                  # 最大重试次数
```

## 使用方法

### 方法一：使用批处理脚本 (推荐)

```bash
# 启动自动轮换
.\ip_rotator.bat start

# 查看状态
.\ip_rotator.bat status

# 手动切换到香港节点
.\ip_rotator.bat switch 香港

# 测试所有代理连接
.\ip_rotator.bat test

# 列出所有可用代理
.\ip_rotator.bat list

# 停止轮换
.\ip_rotator.bat stop
```

### 方法二：使用PowerShell脚本

```powershell
# 启动自动轮换
.\ip_rotator.ps1 start

# 查看状态
.\ip_rotator.ps1 status

# 手动切换到日本节点
.\ip_rotator.ps1 switch 日本

# 测试所有代理连接
.\ip_rotator.ps1 test

# 列出所有可用代理
.\ip_rotator.ps1 list

# 停止轮换
.\ip_rotator.ps1 stop
```

### 方法三：直接使用Python脚本

```bash
# 启动自动轮换
python src/utils/ip/ip_rotator.py start

# 查看状态
python src/utils/ip/ip_rotator.py status

# 手动切换代理
python src/utils/ip/ip_rotator.py switch --name "新加坡"

# 测试代理连接
python src/utils/ip/ip_rotator.py test

# 列出所有代理
python src/utils/ip/ip_rotator.py list
```

## 可用命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `start` | 启动IP自动轮换 | `.\ip_rotator.bat start` |
| `stop` | 停止IP轮换 | `.\ip_rotator.bat stop` |
| `status` | 显示当前状态 | `.\ip_rotator.bat status` |
| `test` | 测试所有代理连接 | `.\ip_rotator.bat test` |
| `switch [name]` | 手动切换代理 | `.\ip_rotator.bat switch 香港` |
| `list` | 列出所有可用代理 | `.\ip_rotator.bat list` |
| `help` | 显示帮助信息 | `.\ip_rotator.bat help` |

## 代理节点说明

### 可用地区

- 🇭🇰 **香港** - 12个节点（推荐使用）
- 🇹🇼 **台湾** - 11个节点
- 🇯🇵 **日本** - 13个节点
- 🇸🇬 **新加坡** - 8个节点
- 🇺🇸 **美国** - 7个节点
- 🇰🇷 **韩国** - 6个节点

### 节点类型

- **华东入口** - 适合华东地区用户
- **三网高级入口** - 支持电信、联通、移动
- **IPLC专线** - 低延迟，金融级安全
- **原生IP** - 原生IP地址
- **家宽IP** - 家庭宽带IP
- **ISP** - 运营商IP

## 工作原理

1. **配置加载** - 从 `config/ip.yaml` 加载代理节点配置
2. **节点筛选** - 根据地区优先级和排除列表筛选可用节点
3. **连接测试** - 测试代理节点的连通性
4. **系统代理设置** - 通过Windows注册表设置系统代理
5. **自动轮换** - 按设定间隔自动切换到下一个可用节点

## 注意事项

### 系统要求

- Windows 10/11
- Python 3.7+
- 管理员权限（用于修改系统代理设置）

### 使用建议

1. **首次使用** - 建议先运行 `test` 命令测试所有代理连接
2. **网络环境** - 确保网络环境允许连接到代理服务器
3. **防火墙设置** - 可能需要在防火墙中允许相关端口
4. **代理软件** - 如果系统中运行了其他代理软件，可能会产生冲突

### 故障排除

1. **代理连接失败**
   ```bash
   # 测试单个代理
   .\ip_rotator.bat switch 香港7
   
   # 测试所有代理
   .\ip_rotator.bat test
   ```

2. **系统代理设置失败**
   - 确保以管理员权限运行
   - 检查Windows注册表权限

3. **轮换不工作**
   ```bash
   # 检查状态
   .\ip_rotator.bat status
   
   # 重新启动
   .\ip_rotator.bat stop
   .\ip_rotator.bat start
   ```

## 日志文件

- 日志文件位置：`logs/ip_rotator.log`
- 包含详细的运行日志和错误信息
- 可用于故障排除和监控

## 安全说明

- 代理密码已在配置文件中加密存储
- 建议定期更新代理节点配置
- 不要在公共网络环境下使用
- 遵守当地法律法规

## 更新说明

如需更新代理节点，请：

1. 访问订阅地址获取最新节点信息
2. 更新 `config/ip.yaml` 配置文件
3. 重启IP轮换器

订阅地址：
```
https://cdn.0be.xyz/api/userhome/rssusernodeext?subtype=0&subkey=060583b8bf8b4e34add50c086d56cb87&inServerType=-1&shownodesubinfo=1&usenodetype=0
```

## 技术支持

如遇到问题，请检查：

1. Python环境是否正确安装
2. 配置文件格式是否正确
3. 网络连接是否正常
4. 系统权限是否足够

---

**注意：请合理使用代理服务，遵守相关法律法规。**
