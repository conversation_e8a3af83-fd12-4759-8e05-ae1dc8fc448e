@echo off
title V2Ray Auto Installer

echo.
echo ========================================
echo        V2Ray Auto Installer
echo ========================================
echo.

REM Create tools directory
if not exist "tools" mkdir tools
if not exist "tools\v2ray" mkdir tools\v2ray

echo Downloading V2Ray automatically...
echo.

REM Download V2Ray using PowerShell
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $url = 'https://github.com/v2fly/v2ray-core/releases/download/v5.12.1/v2ray-windows-64.zip'; $output = 'tools\v2ray-windows-64.zip'; Write-Host 'Downloading V2Ray...'; Invoke-WebRequest -Uri $url -OutFile $output -UserAgent 'Mozilla/5.0'; Write-Host 'Download completed!'}"

REM Check if download was successful
if exist "tools\v2ray-windows-64.zip" (
    echo Download successful!
    echo.
    echo Extracting V2Ray...

    REM Extract using PowerShell
    powershell -Command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('tools\v2ray-windows-64.zip', 'tools\v2ray'); Write-Host 'Extraction completed!'}"

    REM Clean up zip file
    del "tools\v2ray-windows-64.zip"

    echo.
    echo V2Ray installation completed!
) else (
    echo Download failed. Trying alternative method...
    echo.
    echo Please manually download V2Ray:
    echo 1. Go to: https://github.com/v2fly/v2ray-core/releases/latest
    echo 2. Download: v2ray-windows-64.zip
    echo 3. Extract to: tools\v2ray\
    echo.
    pause
)

REM Check if installation is successful
if exist "tools\v2ray\v2ray.exe" (
    echo.
    echo ========================================
    echo V2Ray installation successful!
    echo ========================================
    echo.
    echo Testing V2Ray version:
    tools\v2ray\v2ray.exe version
    echo.
    echo V2Ray is ready to use!
) else (
    echo.
    echo ========================================
    echo V2Ray installation failed
    echo ========================================
    echo.
    echo Please check:
    echo 1. Internet connection
    echo 2. Firewall settings
    echo 3. Manual download from GitHub
)

echo.
pause
