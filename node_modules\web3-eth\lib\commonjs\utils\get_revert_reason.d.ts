import { Web3Context } from 'web3-core';
import { ContractAbi, DataFormat, DEFAULT_RETURN_FORMAT, EthExecutionAPI, TransactionCall } from 'web3-types';
import { RevertReason, RevertReasonWithCustomError } from '../types.js';
export declare const parseTransactionError: (error: unknown, contractAbi?: ContractAbi) => string | RevertReason;
/**
 *	Returns the revert reason generated by the EVM if the transaction were to be executed.
 *
 * @param web3Context - ({@link Web3Context}) Web3 configuration object that contains things such as the provider, request manager, wallet, etc.
 * @param transaction - A transaction object where all properties are optional except `to`, however it's recommended to include the `from` property or it may default to `******************************************` depending on your node or provider.
 * @returns `undefined` if no revert reason was given, a revert reason object, a revert reason string, or an `unknown` error
 */
export declare function getRevertReason<ReturnFormat extends DataFormat = typeof DEFAULT_RETURN_FORMAT>(web3Context: Web3Context<EthExecutionAPI>, transaction: TransactionCall, contractAbi?: ContractAbi, returnFormat?: ReturnFormat): Promise<undefined | RevertReason | RevertReasonWithCustomError | string>;
