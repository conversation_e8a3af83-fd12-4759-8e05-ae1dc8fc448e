# Polygon-Ethereum 双向桥接工具使用指南

## 功能简介

该工具提供了Polygon和Ethereum之间的代币双向桥接功能，使用官方Polygon PoS桥接协议。

**主要特点**:
- 双向桥接：支持Ethereum→Polygon和Polygon→Ethereum两个方向
- 用户友好的命令行界面
- 支持常见代币(USDT, USDC, WETH, WMATIC, DAI等)
- 自动处理授权和转账流程
- 实时查询交易状态
- **新功能**：支持自动监控并认领从Polygon到Ethereum的代币

## 安装和配置

1. 确保已经安装了所有依赖包
2. 配置私钥和RPC节点（推荐使用.env文件）

## 基本使用

```bash
# 列出支持的代币
python -m src.bridge.pol_bridge.bridge_tokens list

# 查询代币余额
python -m src.bridge.pol_bridge.bridge_tokens balance --token-symbol USDC --chain ethereum

# 从以太坊转到Polygon
python -m src.bridge.pol_bridge.bridge_tokens to-polygon --token-symbol USDC --amount 10

# 从Polygon转到以太坊 (带自动认领功能)
python -m src.bridge.pol_bridge.bridge_tokens to-ethereum --token-symbol USDC --amount 10 --auto-claim

# 查询提取状态
python -m src.bridge.pol_bridge.bridge_tokens claim --burn-tx-hash 0x123...
```

## 命令详解

### 列出支持的代币

```bash
python -m src.bridge.pol_bridge.bridge_tokens list
```

该命令显示所有支持桥接的代币列表，包括它们在以太坊和Polygon网络上的地址。

### 查询余额

```bash
python -m src.bridge.pol_bridge.bridge_tokens balance --token-symbol USDC --chain ethereum
# 或者
python -m src.bridge.pol_bridge.bridge_tokens balance --token-address 0x2791... --chain polygon
```

参数:
- `--token-symbol`: 代币符号（如USDC, USDT等）
- `--token-address`: 代币合约地址（与token-symbol二选一）
- `--chain`: 指定链（ethereum或polygon）

### 从以太坊到Polygon

```bash
python -m src.bridge.pol_bridge.bridge_tokens to-polygon --token-symbol USDC --amount 10
```

参数:
- `--token-symbol`: 代币符号
- `--token-address`: 代币合约地址（与token-symbol二选一）
- `--amount`: 要转账的数量（人类可读格式，如10表示10个代币）

### 从Polygon到以太坊

```bash
# 基本用法
python -m src.bridge.pol_bridge.bridge_tokens to-ethereum --token-symbol USDC --amount 10

# 带自动认领功能
python -m src.bridge.pol_bridge.bridge_tokens to-ethereum --token-symbol USDC --amount 10 --auto-claim

# 自定义等待参数
python -m src.bridge.pol_bridge.bridge_tokens to-ethereum --token-symbol USDC --amount 10 --auto-claim --check-interval 30 --initial-wait-time 1200 --max-wait-time 43200
```

参数:
- `--token-symbol`: 代币符号
- `--token-address`: 代币合约地址（与token-symbol二选一）
- `--amount`: 要转账的数量
- `--auto-claim`: 启用自动认领功能，会持续监控交易状态直到代币到账
- `--initial-wait-time`: 初始等待时间（秒），默认1800秒（30分钟）
- `--check-interval`: 自动检查间隔（秒），默认60秒（1分钟）
- `--max-wait-time`: 最大等待时间（秒），默认21600秒（6小时）
- `--no-wait`: 不等待自动认领，仅执行销毁步骤

### 查询提取状态

```bash
python -m src.bridge.pol_bridge.bridge_tokens claim --burn-tx-hash 0x123...
```

参数:
- `--burn-tx-hash`: Polygon上销毁代币的交易哈希

## 重要说明

1. **转账时间**:
   - 从以太坊到Polygon通常需要7-8分钟（未验证）
   - 从Polygon到以太坊需要1-3小时，取决于Polygon检查点（验证过快的时候30分钟到50分钟左右）

2. **自动认领功能**:
   - 使用`--auto-claim`参数可启用自动监控和认领功能
   - 系统会先等待30分钟（可通过`--initial-wait-time`修改），然后开始每分钟检查一次
   - 此功能会定期检查交易状态，直到成功或超时
   - 可以使用Ctrl+C随时中断等待，稍后再用claim命令查询

3. **安全考虑**:
   - 首次使用新代币时建议先转小额测试
   - 确保有足够ETH/MATIC支付gas费

4. **错误处理**:
   - 如遇到错误，脚本会提供详细的错误信息和可能的解决方法
   - 对于不常见的代币，可以先使用check_token工具验证是否支持桥接

## 技术支持

如有问题，请参考:
- [Polygon官方桥接文档](https://docs.polygon.technology/docs/develop/ethereum-polygon/pos/getting-started/)
- [Polygon Portal](https://portal.polygon.technology/bridge) 