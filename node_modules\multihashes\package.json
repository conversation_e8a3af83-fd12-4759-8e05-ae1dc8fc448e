{"name": "multihashes", "version": "0.4.21", "description": "multihash implementation", "leadMaintainer": "<PERSON> <david<PERSON><PERSON>@ipfs.io>", "main": "src/index.js", "scripts": {"lint": "aegir lint", "test:browser": "aegir test --target browser", "test:node": "aegir test --target node", "build": "aegir build", "test": "aegir test", "docs": "aegir docs", "release": "aegir release --docs", "release-minor": "aegir release --type minor --docs", "release-major": "aegir release --type major --docs", "coverage": "aegir coverage", "coverage-publish": "aegir coverage --provider coveralls"}, "files": ["src", "dist"], "repository": {"type": "git", "url": "git://github.com/multiformats/js-multihash.git"}, "keywords": ["multihash"], "license": "MIT", "bugs": {"url": "https://github.com/multiformats/js-multihash/issues"}, "dependencies": {"buffer": "^5.5.0", "multibase": "^0.7.0", "varint": "^5.0.0"}, "devDependencies": {"aegir": "^21.4.4", "chai": "^4.1.2", "dirty-chai": "^2.0.1", "pre-commit": "^1.2.2"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <jaco<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "ᴠɪᴄᴛᴏʀ ʙᴊᴇʟᴋʜᴏʟᴍ <victorb<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> Vagg <<EMAIL>>", "<PERSON> <<EMAIL>>", "kumavis <<EMAIL>>", "<PERSON> <<EMAIL>>", "Fil <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> Mische <<EMAIL>>", "<PERSON> <<EMAIL>>", "nginnever <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]}