/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cometbft.services.version.v1";
function createBaseGetVersionRequest() {
    return {};
}
export const GetVersionRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetVersionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return GetVersionRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseGetVersionRequest();
        return message;
    },
};
function createBaseGetVersionResponse() {
    return { node: "", abci: "", p2p: "0", block: "0" };
}
export const GetVersionResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.node !== "") {
            writer.uint32(10).string(message.node);
        }
        if (message.abci !== "") {
            writer.uint32(18).string(message.abci);
        }
        if (message.p2p !== "0") {
            writer.uint32(24).uint64(message.p2p);
        }
        if (message.block !== "0") {
            writer.uint32(32).uint64(message.block);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetVersionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.node = reader.string();
                    break;
                case 2:
                    message.abci = reader.string();
                    break;
                case 3:
                    message.p2p = longToString(reader.uint64());
                    break;
                case 4:
                    message.block = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            node: isSet(object.node) ? String(object.node) : "",
            abci: isSet(object.abci) ? String(object.abci) : "",
            p2p: isSet(object.p2p) ? String(object.p2p) : "0",
            block: isSet(object.block) ? String(object.block) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.node !== undefined && (obj.node = message.node);
        message.abci !== undefined && (obj.abci = message.abci);
        message.p2p !== undefined && (obj.p2p = message.p2p);
        message.block !== undefined && (obj.block = message.block);
        return obj;
    },
    create(base) {
        return GetVersionResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetVersionResponse();
        message.node = object.node ?? "";
        message.abci = object.abci ?? "";
        message.p2p = object.p2p ?? "0";
        message.block = object.block ?? "0";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
