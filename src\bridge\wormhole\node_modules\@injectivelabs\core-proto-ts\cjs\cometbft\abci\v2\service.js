"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.ABCIServiceFinalizeBlockDesc = exports.ABCIServiceVerifyVoteExtensionDesc = exports.ABCIServiceExtendVoteDesc = exports.ABCIServiceProcessProposalDesc = exports.ABCIServicePrepareProposalDesc = exports.ABCIServiceApplySnapshotChunkDesc = exports.ABCIServiceLoadSnapshotChunkDesc = exports.ABCIServiceOfferSnapshotDesc = exports.ABCIServiceListSnapshotsDesc = exports.ABCIServiceInitChainDesc = exports.ABCIServiceCommitDesc = exports.ABCIServiceQueryDesc = exports.ABCIServiceCheckTxDesc = exports.ABCIServiceInfoDesc = exports.ABCIServiceFlushDesc = exports.ABCIServiceEchoDesc = exports.ABCIServiceDesc = exports.ABCIServiceClientImpl = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var types_1 = require("./types.js");
exports.protobufPackage = "cometbft.abci.v2";
var ABCIServiceClientImpl = /** @class */ (function () {
    function ABCIServiceClientImpl(rpc) {
        this.rpc = rpc;
        this.Echo = this.Echo.bind(this);
        this.Flush = this.Flush.bind(this);
        this.Info = this.Info.bind(this);
        this.CheckTx = this.CheckTx.bind(this);
        this.Query = this.Query.bind(this);
        this.Commit = this.Commit.bind(this);
        this.InitChain = this.InitChain.bind(this);
        this.ListSnapshots = this.ListSnapshots.bind(this);
        this.OfferSnapshot = this.OfferSnapshot.bind(this);
        this.LoadSnapshotChunk = this.LoadSnapshotChunk.bind(this);
        this.ApplySnapshotChunk = this.ApplySnapshotChunk.bind(this);
        this.PrepareProposal = this.PrepareProposal.bind(this);
        this.ProcessProposal = this.ProcessProposal.bind(this);
        this.ExtendVote = this.ExtendVote.bind(this);
        this.VerifyVoteExtension = this.VerifyVoteExtension.bind(this);
        this.FinalizeBlock = this.FinalizeBlock.bind(this);
    }
    ABCIServiceClientImpl.prototype.Echo = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceEchoDesc, types_1.EchoRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.Flush = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceFlushDesc, types_1.FlushRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.Info = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceInfoDesc, types_1.InfoRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.CheckTx = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceCheckTxDesc, types_1.CheckTxRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.Query = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceQueryDesc, types_1.QueryRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.Commit = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceCommitDesc, types_1.CommitRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.InitChain = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceInitChainDesc, types_1.InitChainRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.ListSnapshots = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceListSnapshotsDesc, types_1.ListSnapshotsRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.OfferSnapshot = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceOfferSnapshotDesc, types_1.OfferSnapshotRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.LoadSnapshotChunk = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceLoadSnapshotChunkDesc, types_1.LoadSnapshotChunkRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.ApplySnapshotChunk = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceApplySnapshotChunkDesc, types_1.ApplySnapshotChunkRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.PrepareProposal = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServicePrepareProposalDesc, types_1.PrepareProposalRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.ProcessProposal = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceProcessProposalDesc, types_1.ProcessProposalRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.ExtendVote = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceExtendVoteDesc, types_1.ExtendVoteRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.VerifyVoteExtension = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceVerifyVoteExtensionDesc, types_1.VerifyVoteExtensionRequest.fromPartial(request), metadata);
    };
    ABCIServiceClientImpl.prototype.FinalizeBlock = function (request, metadata) {
        return this.rpc.unary(exports.ABCIServiceFinalizeBlockDesc, types_1.FinalizeBlockRequest.fromPartial(request), metadata);
    };
    return ABCIServiceClientImpl;
}());
exports.ABCIServiceClientImpl = ABCIServiceClientImpl;
exports.ABCIServiceDesc = { serviceName: "cometbft.abci.v2.ABCIService" };
exports.ABCIServiceEchoDesc = {
    methodName: "Echo",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.EchoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.EchoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceFlushDesc = {
    methodName: "Flush",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.FlushRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.FlushResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceInfoDesc = {
    methodName: "Info",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.InfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.InfoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceCheckTxDesc = {
    methodName: "CheckTx",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.CheckTxRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.CheckTxResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceQueryDesc = {
    methodName: "Query",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.QueryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.QueryResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceCommitDesc = {
    methodName: "Commit",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.CommitRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.CommitResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceInitChainDesc = {
    methodName: "InitChain",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.InitChainRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.InitChainResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceListSnapshotsDesc = {
    methodName: "ListSnapshots",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.ListSnapshotsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ListSnapshotsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceOfferSnapshotDesc = {
    methodName: "OfferSnapshot",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.OfferSnapshotRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.OfferSnapshotResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceLoadSnapshotChunkDesc = {
    methodName: "LoadSnapshotChunk",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.LoadSnapshotChunkRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.LoadSnapshotChunkResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceApplySnapshotChunkDesc = {
    methodName: "ApplySnapshotChunk",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.ApplySnapshotChunkRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ApplySnapshotChunkResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServicePrepareProposalDesc = {
    methodName: "PrepareProposal",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.PrepareProposalRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.PrepareProposalResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceProcessProposalDesc = {
    methodName: "ProcessProposal",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.ProcessProposalRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ProcessProposalResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceExtendVoteDesc = {
    methodName: "ExtendVote",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.ExtendVoteRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ExtendVoteResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceVerifyVoteExtensionDesc = {
    methodName: "VerifyVoteExtension",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.VerifyVoteExtensionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.VerifyVoteExtensionResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIServiceFinalizeBlockDesc = {
    methodName: "FinalizeBlock",
    service: exports.ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.FinalizeBlockRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.FinalizeBlockResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
