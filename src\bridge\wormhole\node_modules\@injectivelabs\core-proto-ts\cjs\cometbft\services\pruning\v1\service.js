"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.PruningServiceGetBlockIndexerRetainHeightDesc = exports.PruningServiceSetBlockIndexerRetainHeightDesc = exports.PruningServiceGetTxIndexerRetainHeightDesc = exports.PruningServiceSetTxIndexerRetainHeightDesc = exports.PruningServiceGetBlockResultsRetainHeightDesc = exports.PruningServiceSetBlockResultsRetainHeightDesc = exports.PruningServiceGetBlockRetainHeightDesc = exports.PruningServiceSetBlockRetainHeightDesc = exports.PruningServiceDesc = exports.PruningServiceClientImpl = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var pruning_1 = require("./pruning.js");
exports.protobufPackage = "cometbft.services.pruning.v1";
var PruningServiceClientImpl = /** @class */ (function () {
    function PruningServiceClientImpl(rpc) {
        this.rpc = rpc;
        this.SetBlockRetainHeight = this.SetBlockRetainHeight.bind(this);
        this.GetBlockRetainHeight = this.GetBlockRetainHeight.bind(this);
        this.SetBlockResultsRetainHeight = this.SetBlockResultsRetainHeight.bind(this);
        this.GetBlockResultsRetainHeight = this.GetBlockResultsRetainHeight.bind(this);
        this.SetTxIndexerRetainHeight = this.SetTxIndexerRetainHeight.bind(this);
        this.GetTxIndexerRetainHeight = this.GetTxIndexerRetainHeight.bind(this);
        this.SetBlockIndexerRetainHeight = this.SetBlockIndexerRetainHeight.bind(this);
        this.GetBlockIndexerRetainHeight = this.GetBlockIndexerRetainHeight.bind(this);
    }
    PruningServiceClientImpl.prototype.SetBlockRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceSetBlockRetainHeightDesc, pruning_1.SetBlockRetainHeightRequest.fromPartial(request), metadata);
    };
    PruningServiceClientImpl.prototype.GetBlockRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceGetBlockRetainHeightDesc, pruning_1.GetBlockRetainHeightRequest.fromPartial(request), metadata);
    };
    PruningServiceClientImpl.prototype.SetBlockResultsRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceSetBlockResultsRetainHeightDesc, pruning_1.SetBlockResultsRetainHeightRequest.fromPartial(request), metadata);
    };
    PruningServiceClientImpl.prototype.GetBlockResultsRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceGetBlockResultsRetainHeightDesc, pruning_1.GetBlockResultsRetainHeightRequest.fromPartial(request), metadata);
    };
    PruningServiceClientImpl.prototype.SetTxIndexerRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceSetTxIndexerRetainHeightDesc, pruning_1.SetTxIndexerRetainHeightRequest.fromPartial(request), metadata);
    };
    PruningServiceClientImpl.prototype.GetTxIndexerRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceGetTxIndexerRetainHeightDesc, pruning_1.GetTxIndexerRetainHeightRequest.fromPartial(request), metadata);
    };
    PruningServiceClientImpl.prototype.SetBlockIndexerRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceSetBlockIndexerRetainHeightDesc, pruning_1.SetBlockIndexerRetainHeightRequest.fromPartial(request), metadata);
    };
    PruningServiceClientImpl.prototype.GetBlockIndexerRetainHeight = function (request, metadata) {
        return this.rpc.unary(exports.PruningServiceGetBlockIndexerRetainHeightDesc, pruning_1.GetBlockIndexerRetainHeightRequest.fromPartial(request), metadata);
    };
    return PruningServiceClientImpl;
}());
exports.PruningServiceClientImpl = PruningServiceClientImpl;
exports.PruningServiceDesc = { serviceName: "cometbft.services.pruning.v1.PruningService" };
exports.PruningServiceSetBlockRetainHeightDesc = {
    methodName: "SetBlockRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.SetBlockRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.SetBlockRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.PruningServiceGetBlockRetainHeightDesc = {
    methodName: "GetBlockRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.GetBlockRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.GetBlockRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.PruningServiceSetBlockResultsRetainHeightDesc = {
    methodName: "SetBlockResultsRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.SetBlockResultsRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.SetBlockResultsRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.PruningServiceGetBlockResultsRetainHeightDesc = {
    methodName: "GetBlockResultsRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.GetBlockResultsRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.GetBlockResultsRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.PruningServiceSetTxIndexerRetainHeightDesc = {
    methodName: "SetTxIndexerRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.SetTxIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.SetTxIndexerRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.PruningServiceGetTxIndexerRetainHeightDesc = {
    methodName: "GetTxIndexerRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.GetTxIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.GetTxIndexerRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.PruningServiceSetBlockIndexerRetainHeightDesc = {
    methodName: "SetBlockIndexerRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.SetBlockIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.SetBlockIndexerRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.PruningServiceGetBlockIndexerRetainHeightDesc = {
    methodName: "GetBlockIndexerRetainHeight",
    service: exports.PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return pruning_1.GetBlockIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = pruning_1.GetBlockIndexerRetainHeightResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
