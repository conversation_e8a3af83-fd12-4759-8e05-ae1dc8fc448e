{"name": "@gql.tada/cli-utils", "version": "1.6.3", "public": true, "description": "Main logic for gql.tada’s CLI tool.", "author": "0no.co <<EMAIL>>", "sideEffects": false, "source": "./src/index.ts", "main": "./dist/gql-tada-cli", "module": "./dist/gql-tada-cli.mjs", "types": "./dist/gql-tada-cli.d.ts", "exports": {".": {"types": "./dist/gql-tada-cli.d.ts", "import": "./dist/gql-tada-cli.mjs", "require": "./dist/gql-tada-cli.js", "source": "./src/index.ts"}, "./package.json": "./package.json"}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist/"], "repository": {"type": "git", "url": "https://github.com/0no-co/gql.tada.git", "directory": "packages/cli-utils"}, "bugs": {"url": "https://github.com/0no-co/gql.tada/issues"}, "homepage": "https://gql-tada.0no.co/", "license": "MIT", "devDependencies": {"@clack/prompts": "^0.7.0", "@types/node": "^20.11.0", "@volar/source-map": "~2.1.6", "clipanion": "4.0.0-rc.3", "execa": "^8.0.1", "rollup": "^4.9.4", "sade": "^1.8.1", "semiver": "^1.1.0", "typanion": "^3.14.0", "type-fest": "^4.10.2", "typescript": "^5.5.2", "wonka": "^6.3.4"}, "dependencies": {"@0no-co/graphqlsp": "^1.12.13", "graphql": "^15.5.0 || ^16.0.0 || ^17.0.0", "@gql.tada/internal": "1.0.8"}, "peerDependenciesMeta": {"@gql.tada/svelte-support": {"optional": true}, "@gql.tada/vue-support": {"optional": true}}, "peerDependencies": {"@0no-co/graphqlsp": "^1.12.13", "graphql": "^15.5.0 || ^16.0.0 || ^17.0.0", "typescript": "^5.0.0", "@gql.tada/vue-support": "1.0.1", "@gql.tada/svelte-support": "1.0.1"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"build": "rollup -c ../../scripts/rollup.config.mjs", "clean": "rimraf dist node_modules/.cache"}}