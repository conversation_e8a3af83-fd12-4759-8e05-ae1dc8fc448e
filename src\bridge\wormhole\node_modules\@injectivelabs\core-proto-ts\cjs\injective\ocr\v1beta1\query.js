"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.QueryOcrModuleStateDesc = exports.QueryOwedAmountDesc = exports.QueryLatestTransmissionDetailsDesc = exports.QueryLatestRoundDesc = exports.QueryFeedConfigInfoDesc = exports.QueryFeedConfigDesc = exports.QueryParamsDesc = exports.QueryDesc = exports.QueryClientImpl = exports.QueryModuleStateResponse = exports.QueryModuleStateRequest = exports.QueryOwedAmountResponse = exports.QueryOwedAmountRequest = exports.QueryLatestTransmissionDetailsResponse = exports.QueryLatestTransmissionDetailsRequest = exports.QueryLatestRoundResponse = exports.QueryLatestRoundRequest = exports.QueryFeedConfigInfoResponse = exports.QueryFeedConfigInfoRequest = exports.QueryFeedConfigResponse = exports.QueryFeedConfigRequest = exports.QueryParamsResponse = exports.QueryParamsRequest = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var genesis_1 = require("./genesis.js");
var ocr_1 = require("./ocr.js");
exports.protobufPackage = "injective.ocr.v1beta1";
function createBaseQueryParamsRequest() {
    return {};
}
exports.QueryParamsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryParamsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryParamsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryParamsRequest();
        return message;
    },
};
function createBaseQueryParamsResponse() {
    return { params: undefined };
}
exports.QueryParamsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            ocr_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = ocr_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { params: isSet(object.params) ? ocr_1.Params.fromJSON(object.params) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? ocr_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryParamsResponse();
        message.params = (object.params !== undefined && object.params !== null)
            ? ocr_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseQueryFeedConfigRequest() {
    return { feedId: "" };
}
exports.QueryFeedConfigRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeedConfigRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { feedId: isSet(object.feedId) ? String(object.feedId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        return obj;
    },
    create: function (base) {
        return exports.QueryFeedConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFeedConfigRequest();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryFeedConfigResponse() {
    return { feedConfigInfo: undefined, feedConfig: undefined };
}
exports.QueryFeedConfigResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedConfigInfo !== undefined) {
            ocr_1.FeedConfigInfo.encode(message.feedConfigInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.feedConfig !== undefined) {
            ocr_1.FeedConfig.encode(message.feedConfig, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeedConfigResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedConfigInfo = ocr_1.FeedConfigInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.feedConfig = ocr_1.FeedConfig.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedConfigInfo: isSet(object.feedConfigInfo) ? ocr_1.FeedConfigInfo.fromJSON(object.feedConfigInfo) : undefined,
            feedConfig: isSet(object.feedConfig) ? ocr_1.FeedConfig.fromJSON(object.feedConfig) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedConfigInfo !== undefined &&
            (obj.feedConfigInfo = message.feedConfigInfo ? ocr_1.FeedConfigInfo.toJSON(message.feedConfigInfo) : undefined);
        message.feedConfig !== undefined &&
            (obj.feedConfig = message.feedConfig ? ocr_1.FeedConfig.toJSON(message.feedConfig) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryFeedConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryFeedConfigResponse();
        message.feedConfigInfo = (object.feedConfigInfo !== undefined && object.feedConfigInfo !== null)
            ? ocr_1.FeedConfigInfo.fromPartial(object.feedConfigInfo)
            : undefined;
        message.feedConfig = (object.feedConfig !== undefined && object.feedConfig !== null)
            ? ocr_1.FeedConfig.fromPartial(object.feedConfig)
            : undefined;
        return message;
    },
};
function createBaseQueryFeedConfigInfoRequest() {
    return { feedId: "" };
}
exports.QueryFeedConfigInfoRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeedConfigInfoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { feedId: isSet(object.feedId) ? String(object.feedId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        return obj;
    },
    create: function (base) {
        return exports.QueryFeedConfigInfoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFeedConfigInfoRequest();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryFeedConfigInfoResponse() {
    return { feedConfigInfo: undefined, epochAndRound: undefined };
}
exports.QueryFeedConfigInfoResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedConfigInfo !== undefined) {
            ocr_1.FeedConfigInfo.encode(message.feedConfigInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.epochAndRound !== undefined) {
            ocr_1.EpochAndRound.encode(message.epochAndRound, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeedConfigInfoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedConfigInfo = ocr_1.FeedConfigInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.epochAndRound = ocr_1.EpochAndRound.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedConfigInfo: isSet(object.feedConfigInfo) ? ocr_1.FeedConfigInfo.fromJSON(object.feedConfigInfo) : undefined,
            epochAndRound: isSet(object.epochAndRound) ? ocr_1.EpochAndRound.fromJSON(object.epochAndRound) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedConfigInfo !== undefined &&
            (obj.feedConfigInfo = message.feedConfigInfo ? ocr_1.FeedConfigInfo.toJSON(message.feedConfigInfo) : undefined);
        message.epochAndRound !== undefined &&
            (obj.epochAndRound = message.epochAndRound ? ocr_1.EpochAndRound.toJSON(message.epochAndRound) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryFeedConfigInfoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryFeedConfigInfoResponse();
        message.feedConfigInfo = (object.feedConfigInfo !== undefined && object.feedConfigInfo !== null)
            ? ocr_1.FeedConfigInfo.fromPartial(object.feedConfigInfo)
            : undefined;
        message.epochAndRound = (object.epochAndRound !== undefined && object.epochAndRound !== null)
            ? ocr_1.EpochAndRound.fromPartial(object.epochAndRound)
            : undefined;
        return message;
    },
};
function createBaseQueryLatestRoundRequest() {
    return { feedId: "" };
}
exports.QueryLatestRoundRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLatestRoundRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { feedId: isSet(object.feedId) ? String(object.feedId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        return obj;
    },
    create: function (base) {
        return exports.QueryLatestRoundRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLatestRoundRequest();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryLatestRoundResponse() {
    return { latestRoundId: "0", data: undefined };
}
exports.QueryLatestRoundResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.latestRoundId !== "0") {
            writer.uint32(8).uint64(message.latestRoundId);
        }
        if (message.data !== undefined) {
            ocr_1.Transmission.encode(message.data, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLatestRoundResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.latestRoundId = longToString(reader.uint64());
                    break;
                case 2:
                    message.data = ocr_1.Transmission.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            latestRoundId: isSet(object.latestRoundId) ? String(object.latestRoundId) : "0",
            data: isSet(object.data) ? ocr_1.Transmission.fromJSON(object.data) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.latestRoundId !== undefined && (obj.latestRoundId = message.latestRoundId);
        message.data !== undefined && (obj.data = message.data ? ocr_1.Transmission.toJSON(message.data) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryLatestRoundResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLatestRoundResponse();
        message.latestRoundId = (_a = object.latestRoundId) !== null && _a !== void 0 ? _a : "0";
        message.data = (object.data !== undefined && object.data !== null)
            ? ocr_1.Transmission.fromPartial(object.data)
            : undefined;
        return message;
    },
};
function createBaseQueryLatestTransmissionDetailsRequest() {
    return { feedId: "" };
}
exports.QueryLatestTransmissionDetailsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLatestTransmissionDetailsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { feedId: isSet(object.feedId) ? String(object.feedId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        return obj;
    },
    create: function (base) {
        return exports.QueryLatestTransmissionDetailsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLatestTransmissionDetailsRequest();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryLatestTransmissionDetailsResponse() {
    return { configDigest: new Uint8Array(), epochAndRound: undefined, data: undefined };
}
exports.QueryLatestTransmissionDetailsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.configDigest.length !== 0) {
            writer.uint32(10).bytes(message.configDigest);
        }
        if (message.epochAndRound !== undefined) {
            ocr_1.EpochAndRound.encode(message.epochAndRound, writer.uint32(18).fork()).ldelim();
        }
        if (message.data !== undefined) {
            ocr_1.Transmission.encode(message.data, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLatestTransmissionDetailsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.configDigest = reader.bytes();
                    break;
                case 2:
                    message.epochAndRound = ocr_1.EpochAndRound.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.data = ocr_1.Transmission.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            configDigest: isSet(object.configDigest) ? bytesFromBase64(object.configDigest) : new Uint8Array(),
            epochAndRound: isSet(object.epochAndRound) ? ocr_1.EpochAndRound.fromJSON(object.epochAndRound) : undefined,
            data: isSet(object.data) ? ocr_1.Transmission.fromJSON(object.data) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.configDigest !== undefined &&
            (obj.configDigest = base64FromBytes(message.configDigest !== undefined ? message.configDigest : new Uint8Array()));
        message.epochAndRound !== undefined &&
            (obj.epochAndRound = message.epochAndRound ? ocr_1.EpochAndRound.toJSON(message.epochAndRound) : undefined);
        message.data !== undefined && (obj.data = message.data ? ocr_1.Transmission.toJSON(message.data) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryLatestTransmissionDetailsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLatestTransmissionDetailsResponse();
        message.configDigest = (_a = object.configDigest) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.epochAndRound = (object.epochAndRound !== undefined && object.epochAndRound !== null)
            ? ocr_1.EpochAndRound.fromPartial(object.epochAndRound)
            : undefined;
        message.data = (object.data !== undefined && object.data !== null)
            ? ocr_1.Transmission.fromPartial(object.data)
            : undefined;
        return message;
    },
};
function createBaseQueryOwedAmountRequest() {
    return { transmitter: "" };
}
exports.QueryOwedAmountRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.transmitter !== "") {
            writer.uint32(10).string(message.transmitter);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOwedAmountRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.transmitter = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { transmitter: isSet(object.transmitter) ? String(object.transmitter) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.transmitter !== undefined && (obj.transmitter = message.transmitter);
        return obj;
    },
    create: function (base) {
        return exports.QueryOwedAmountRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryOwedAmountRequest();
        message.transmitter = (_a = object.transmitter) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryOwedAmountResponse() {
    return { amount: undefined };
}
exports.QueryOwedAmountResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOwedAmountResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryOwedAmountResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryOwedAmountResponse();
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseQueryModuleStateRequest() {
    return {};
}
exports.QueryModuleStateRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryModuleStateRequest();
        return message;
    },
};
function createBaseQueryModuleStateResponse() {
    return { state: undefined };
}
exports.QueryModuleStateResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            genesis_1.GenesisState.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = genesis_1.GenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? genesis_1.GenesisState.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined && (obj.state = message.state ? genesis_1.GenesisState.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryModuleStateResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? genesis_1.GenesisState.fromPartial(object.state)
            : undefined;
        return message;
    },
};
var QueryClientImpl = /** @class */ (function () {
    function QueryClientImpl(rpc) {
        this.rpc = rpc;
        this.Params = this.Params.bind(this);
        this.FeedConfig = this.FeedConfig.bind(this);
        this.FeedConfigInfo = this.FeedConfigInfo.bind(this);
        this.LatestRound = this.LatestRound.bind(this);
        this.LatestTransmissionDetails = this.LatestTransmissionDetails.bind(this);
        this.OwedAmount = this.OwedAmount.bind(this);
        this.OcrModuleState = this.OcrModuleState.bind(this);
    }
    QueryClientImpl.prototype.Params = function (request, metadata) {
        return this.rpc.unary(exports.QueryParamsDesc, exports.QueryParamsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.FeedConfig = function (request, metadata) {
        return this.rpc.unary(exports.QueryFeedConfigDesc, exports.QueryFeedConfigRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.FeedConfigInfo = function (request, metadata) {
        return this.rpc.unary(exports.QueryFeedConfigInfoDesc, exports.QueryFeedConfigInfoRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.LatestRound = function (request, metadata) {
        return this.rpc.unary(exports.QueryLatestRoundDesc, exports.QueryLatestRoundRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.LatestTransmissionDetails = function (request, metadata) {
        return this.rpc.unary(exports.QueryLatestTransmissionDetailsDesc, exports.QueryLatestTransmissionDetailsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OwedAmount = function (request, metadata) {
        return this.rpc.unary(exports.QueryOwedAmountDesc, exports.QueryOwedAmountRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OcrModuleState = function (request, metadata) {
        return this.rpc.unary(exports.QueryOcrModuleStateDesc, exports.QueryModuleStateRequest.fromPartial(request), metadata);
    };
    return QueryClientImpl;
}());
exports.QueryClientImpl = QueryClientImpl;
exports.QueryDesc = { serviceName: "injective.ocr.v1beta1.Query" };
exports.QueryParamsDesc = {
    methodName: "Params",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryParamsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryFeedConfigDesc = {
    methodName: "FeedConfig",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFeedConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFeedConfigResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryFeedConfigInfoDesc = {
    methodName: "FeedConfigInfo",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFeedConfigInfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFeedConfigInfoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryLatestRoundDesc = {
    methodName: "LatestRound",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryLatestRoundRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryLatestRoundResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryLatestTransmissionDetailsDesc = {
    methodName: "LatestTransmissionDetails",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryLatestTransmissionDetailsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryLatestTransmissionDetailsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOwedAmountDesc = {
    methodName: "OwedAmount",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryOwedAmountRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryOwedAmountResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOcrModuleStateDesc = {
    methodName: "OcrModuleState",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryModuleStateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryModuleStateResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
