import { ConcreteException } from '../base.js';
import { ErrorType, GRPC_REQUEST_FAILED } from '../types/index.js';
export class GrpcUnaryRequestException extends ConcreteException {
    static errorClass = 'GrpcUnaryRequestException';
    constructor(error, context) {
        super(error, context);
        this.type = ErrorType.GrpcUnaryRequest;
    }
    parse() {
        const { message } = this;
        if (message.toLowerCase().includes('response closed without headers')) {
            this.setMessage('The request has failed. The server has closed the connection without sending any headers.');
            this.setContextCode(GRPC_REQUEST_FAILED);
        }
        this.setName(GrpcUnaryRequestException.errorClass);
    }
}
