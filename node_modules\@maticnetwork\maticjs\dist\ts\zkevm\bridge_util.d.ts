import { Web3SideChainClient } from "../utils";
import { IBaseClientConfig } from "..";
import { TYPE_AMOUNT } from '../types';
interface IBridgeEventInfo {
    originNetwork: number;
    originTokenAddress: string;
    destinationNetwork: number;
    destinationAddress: string;
    amount: TYPE_AMOUNT;
    metadata: string;
    depositCount: number;
}
interface IClaimPayload {
    smtProof: string[];
    smtProofRollup: string[];
    globalIndex: string;
    mainnetExitRoot: string;
    rollupExitRoot: string;
    originNetwork: number;
    originTokenAddress: string;
    destinationNetwork: number;
    destinationAddress: string;
    amount: TYPE_AMOUNT;
    metadata: string;
}
export declare class BridgeUtil {
    private client_;
    private BRIDGE_TOPIC;
    constructor(client: Web3SideChainClient<IBaseClientConfig>);
    private decodedBridgeData_;
    private getBridgeLogData_;
    private getProof_;
    getBridgeLogData(transactionHash: string, isParent: boolean): Promise<IBridgeEventInfo>;
    computeGlobalIndex(indexLocal: number, indexRollup: number, sourceNetworkId: number): bigint;
    buildPayloadForClaim(transactionHash: string, isParent: boolean, networkId: number): Promise<IClaimPayload>;
}
export {};
