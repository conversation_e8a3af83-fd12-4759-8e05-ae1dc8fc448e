{"version": 3, "sources": ["store2.js"], "names": ["window", "define", "_", "version", "areas", "apis", "nsdelim", "inherit", "api", "o", "p", "hasOwnProperty", "Object", "defineProperty", "getOwnPropertyDescriptor", "stringify", "d", "fn", "undefined", "JSON", "replace", "parse", "s", "revive", "e", "name", "storeAPI", "get", "area", "key", "getItem", "set", "string", "setItem", "remove", "removeItem", "i", "length", "clear", "Store", "id", "namespace", "store", "data", "overwrite", "arguments", "getAll", "transact", "each", "setAll", "_id", "_area", "storage", "_ns", "this", "singleArea", "delim", "_delim", "substring", "ns", "isFake", "force", "_real", "toString", "has", "_in", "size", "keys", "fill", "m", "_out", "call", "fillList", "k", "v", "list", "push", "alt", "fill<PERSON><PERSON><PERSON>", "all", "val", "ret", "replacer", "changed", "add", "Array", "concat", "type", "clearAll", "indexOf", "storageAPI", "items", "c", "localStorage", "local", "sessionStorage", "amd", "module", "exports", "conflict"], "mappings": ";;;CAEC,SAAUA,EAAQC,GACf,GAAIC,IACAC,QAAS,SACTC,SACAC,QACAC,QAAS,IAGTC,QAAS,SAASC,EAAKC,GACnB,IAAK,GAAIC,KAAKF,GACLC,EAAEE,eAAeD,IAClBE,OAAOC,eAAeJ,EAAGC,EAAGE,OAAOE,yBAAyBN,EAAKE,GAGzE,OAAOD,IAEXM,UAAW,SAASC,EAAGC,GACnB,WAAaC,KAANF,GAAgC,kBAANA,GAAmBA,EAAE,GAAKG,KAAKJ,UAAUC,EAAEC,GAAIf,EAAEkB,UAEtFC,MAAO,SAASC,EAAGL,GAEf,IAAK,MAAOE,MAAKE,MAAMC,EAAEL,GAAIf,EAAEqB,QAAU,MAAMC,GAAI,MAAOF,KAI9DL,GAAI,SAASQ,EAAMR,GACff,EAAEwB,SAASD,GAAQR,CACnB,KAAK,GAAIT,KAAON,GAAEG,KACdH,EAAEG,KAAKG,GAAKiB,GAAQR,GAG5BU,IAAK,SAASC,EAAMC,GAAM,MAAOD,GAAKE,QAAQD,IAC9CE,IAAK,SAASH,EAAMC,EAAKG,GAASJ,EAAKK,QAAQJ,EAAKG,IACpDE,OAAQ,SAASN,EAAMC,GAAMD,EAAKO,WAAWN,IAC7CA,IAAK,SAASD,EAAMQ,GAAI,MAAOR,GAAKC,IAAIO,IACxCC,OAAQ,SAAST,GAAO,MAAOA,GAAKS,QACpCC,MAAO,SAASV,GAAOA,EAAKU,SAG5BC,MAAO,SAASC,EAAIZ,EAAMa,GACtB,GAAIC,GAAQxC,EAAEK,QAAQL,EAAEwB,SAAU,SAASG,EAAKc,EAAMC,GAClD,MAAyB,KAArBC,UAAUR,OAAsBK,EAAMI,SACtB,kBAATH,GAA6BD,EAAMK,SAASlB,EAAKc,EAAMC,OACrD1B,KAATyB,EAA4BD,EAAMX,IAAIF,EAAKc,EAAMC,GAClC,gBAARf,IAAmC,gBAARA,GAA0Ba,EAAMf,IAAIE,GACvD,kBAARA,GAA4Ba,EAAMM,KAAKnB,GAC7CA,EACEa,EAAMO,OAAOpB,EAAKc,GADPD,EAAMJ,SAG5BI,GAAMQ,IAAMV,CACZ,KAEIZ,EAAKK,QADS,gBACQ,MACtBS,EAAMS,MAAQvB,EACdA,EAAKO,WAHS,iBAIhB,MAAOX,GACLkB,EAAMS,MAAQjD,EAAEkD,QAAQ,QAS5B,MAPAV,GAAMW,IAAMZ,GAAa,GACpBvC,EAAEE,MAAMoC,KACTtC,EAAEE,MAAMoC,GAAME,EAAMS,OAEnBjD,EAAEG,KAAKqC,EAAMW,IAAIX,EAAMQ,OACxBhD,EAAEG,KAAKqC,EAAMW,IAAIX,EAAMQ,KAAOR,GAE3BA,GAEXhB,UAEIE,KAAM,SAASY,EAAIZ,GACf,GAAIc,GAAQY,KAAKd,EAKjB,OAJKE,IAAUA,EAAMd,OACjBc,EAAQxC,EAAEqC,MAAMC,EAAIZ,EAAM0B,KAAKD,KAC1BC,KAAKd,KAAMc,KAAKd,GAAME,IAExBA,GAEXD,UAAW,SAASA,EAAWc,EAAYC,GAEvC,GADAA,EAAQA,GAASF,KAAKG,QAAUvD,EAAEI,SAC7BmC,EACD,MAAOa,MAAKD,IAAMC,KAAKD,IAAIK,UAAU,EAAEJ,KAAKD,IAAIhB,OAAOmB,EAAMnB,QAAU,EAE3E,IAAIsB,GAAKlB,EAAWC,EAAQY,KAAKK,EACjC,MAAKjB,GAAUA,EAAMD,YACjBC,EAAQxC,EAAEqC,MAAMe,KAAKJ,IAAKI,KAAKH,MAAOG,KAAKD,IAAIM,EAAGH,GAClDd,EAAMe,OAASD,EACVF,KAAKK,KAAML,KAAKK,GAAMjB,GACtBa,IACD,IAAK,GAAI9B,KAAQvB,GAAEE,MACfsC,EAAMd,KAAKH,EAAMvB,EAAEE,MAAMqB,GAIrC,OAAOiB,IAEXkB,OAAQ,SAASC,GAOb,MANIA,IACAP,KAAKQ,MAAQR,KAAKH,MAClBG,KAAKH,MAAQjD,EAAEkD,QAAQ,UACN,IAAVS,IACPP,KAAKH,MAAQG,KAAKQ,OAASR,KAAKH,OAET,SAApBG,KAAKH,MAAM1B,MAEtBsC,SAAU,WACN,MAAO,SAAST,KAAKD,IAAI,IAAIC,KAAKb,YAAY,IAAI,IAAIa,KAAKJ,IAAI,KAInEc,IAAK,SAASnC,GACV,MAAIyB,MAAKH,MAAMa,IACJV,KAAKH,MAAMa,IAAIV,KAAKW,IAAIpC,OAEzByB,KAAKW,IAAIpC,IAAQyB,MAAKH,QAEpCe,KAAM,WAAY,MAAOZ,MAAKa,OAAO9B,QACrCW,KAAM,SAAS/B,EAAImD,GACf,IAAK,GAAIhC,GAAE,EAAGiC,EAAEnE,EAAEmC,OAAOiB,KAAKH,OAAQf,EAAEiC,EAAGjC,IAAK,CAC5C,GAAIP,GAAMyB,KAAKgB,KAAKpE,EAAE2B,IAAIyB,KAAKH,MAAOf,GACtC,QAAYlB,KAARW,IACgD,IAA5CZ,EAAGsD,KAAKjB,KAAMzB,EAAKyB,KAAK3B,IAAIE,GAAMuC,GAClC,KAGJC,GAAInE,EAAEmC,OAAOiB,KAAKH,SAAUkB,IAAKjC,KAEzC,MAAOgC,IAAQd,MAEnBa,KAAM,SAASK,GACX,MAAOlB,MAAKN,KAAK,SAASyB,EAAGC,EAAGC,GAAOA,EAAKC,KAAKH,IAAOD,QAE5D7C,IAAK,SAASE,EAAKgD,GACf,GACI5D,GADAK,EAAIpB,EAAEyB,IAAI2B,KAAKH,MAAOG,KAAKW,IAAIpC,GAMnC,OAJmB,kBAARgD,KACP5D,EAAK4D,EACLA,EAAM,MAEG,OAANvD,EAAapB,EAAEmB,MAAMC,EAAGL,GACpB,MAAP4D,EAAcA,EAAMvD,GAE5BwB,OAAQ,SAASgC,GACb,MAAOxB,MAAKN,KAAK,SAASyB,EAAGC,EAAGK,GAAMA,EAAIN,GAAKC,GAAMI,QAEzD/B,SAAU,SAASlB,EAAKZ,EAAI4D,GACxB,GAAIG,GAAM1B,KAAK3B,IAAIE,EAAKgD,GACpBI,EAAMhE,EAAG+D,EAEb,OADA1B,MAAKvB,IAAIF,MAAaX,KAAR+D,EAAoBD,EAAMC,GACjC3B,MAEXvB,IAAK,SAASF,EAAKc,EAAMC,GACrB,GACIsC,GADAlE,EAAIsC,KAAK3B,IAAIE,EAEjB,OAAS,OAALb,IAA2B,IAAd4B,EACND,GAEc,kBAAdC,KACPsC,EAAWtC,EACXA,MAAY1B,IAEThB,EAAE6B,IAAIuB,KAAKH,MAAOG,KAAKW,IAAIpC,GAAM3B,EAAEa,UAAU4B,EAAMuC,GAAWtC,IAAc5B,IAEvFiC,OAAQ,SAASN,EAAMC,GACnB,GAAIuC,GAASH,CACb,KAAK,GAAInD,KAAOc,GACZqC,EAAMrC,EAAKd,GACPyB,KAAKvB,IAAIF,EAAKmD,EAAKpC,KAAeoC,IAClCG,GAAU,EAGlB,OAAOA,IAEXC,IAAK,SAASvD,EAAKc,EAAMuC,GACrB,GAAIlE,GAAIsC,KAAK3B,IAAIE,EACjB,IAAIb,YAAaqE,OACb1C,EAAO3B,EAAEsE,OAAO3C,OACb,IAAU,OAAN3B,EAAY,CACnB,GAAIuE,SAAcvE,EAClB,IAAIuE,UAAgB5C,IAAiB,WAAT4C,EAAmB,CAC3C,IAAK,GAAId,KAAK9B,GACV3B,EAAEyD,GAAK9B,EAAK8B,EAEhB9B,GAAO3B,MAEP2B,GAAO3B,EAAI2B,EAInB,MADAzC,GAAE6B,IAAIuB,KAAKH,MAAOG,KAAKW,IAAIpC,GAAM3B,EAAEa,UAAU4B,EAAMuC,IAC5CvC,GAEXT,OAAQ,SAASL,EAAKgD,GAClB,GAAI7D,GAAIsC,KAAK3B,IAAIE,EAAKgD,EAEtB,OADA3E,GAAEgC,OAAOoB,KAAKH,MAAOG,KAAKW,IAAIpC,IACvBb,GAEXsB,MAAO,WAMH,MALKgB,MAAKD,IAGNC,KAAKN,KAAK,SAASyB,GAAIvE,EAAEgC,OAAOoB,KAAKH,MAAOG,KAAKW,IAAIQ,KAAQ,GAF7DvE,EAAEoC,MAAMgB,KAAKH,OAIVG,MAEXkC,SAAU,WACN,GAAI5D,GAAO0B,KAAKH,KAChB,KAAK,GAAIX,KAAMtC,GAAEE,MACTF,EAAEE,MAAMO,eAAe6B,KACvBc,KAAKH,MAAQjD,EAAEE,MAAMoC,GACrBc,KAAKhB,QAIb,OADAgB,MAAKH,MAAQvB,EACN0B,MAIXW,IAAK,SAASQ,GAEV,MADiB,gBAANA,KAAiBA,EAAIvE,EAAEa,UAAU0D,IACrCnB,KAAKD,IAAMC,KAAKD,IAAMoB,EAAIA,GAErCH,KAAM,SAASG,GACX,MAAOnB,MAAKD,IACRoB,GAA6B,IAAxBA,EAAEgB,QAAQnC,KAAKD,KAChBoB,EAAEf,UAAUJ,KAAKD,IAAIhB,YACrBnB,GACJuD,IAGZrB,QAAS,SAAS3B,GACd,MAAOvB,GAAEK,QAAQL,EAAEwF,YAAcC,SAAWlE,KAAMA,KAEtDiE,YACIrD,OAAQ,EACR2B,IAAK,SAASS,GAAI,MAAOnB,MAAKqC,MAAMhF,eAAe8D,IACnD5C,IAAK,SAASO,GACV,GAAIwD,GAAI,CACR,KAAK,GAAInB,KAAKnB,MAAKqC,MACf,GAAIrC,KAAKU,IAAIS,IAAMrC,IAAMwD,IACrB,MAAOnB,IAInBxC,QAAS,SAASwC,EAAGC,GACZpB,KAAKU,IAAIS,IACVnB,KAAKjB,SAETiB,KAAKqC,MAAMlB,GAAKC,GAEpBvC,WAAY,SAASsC,GACbnB,KAAKU,IAAIS,WACFnB,MAAKqC,MAAMlB,GAClBnB,KAAKjB,WAGbP,QAAS,SAAS2C,GAAI,MAAOnB,MAAKU,IAAIS,GAAKnB,KAAKqC,MAAMlB,GAAK,MAC3DnC,MAAO,WAAY,IAAK,GAAImC,KAAKnB,MAAKqC,MAAQrC,KAAKnB,WAAWsC,MAIlE/B,EAEAxC,EAAEqC,MAAM,QAAS,WAAY,IAAK,MAAOsD,cAAe,MAAMrE,QAClEkB,GAAMoD,MAAQpD,EACdA,EAAMxC,EAAIA,EAEVwC,EAAMd,KAAK,UAAW,WAAY,IAAK,MAAOmE,gBAAiB,MAAMvE,SACrEkB,EAAMd,KAAK,OAAQ1B,EAAEkD,QAAQ,SAEP,kBAAXnD,QAAwCiB,KAAfjB,EAAO+F,IACvC/F,EAAO,YAAc,WACjB,MAAOyC,KAEc,mBAAXuD,SAA0BA,OAAOC,QAC/CD,OAAOC,QAAUxD,GAGb1C,EAAO0C,QAAQxC,EAAEiG,SAAWnG,EAAO0C,OACvC1C,EAAO0C,MAAQA,IAGpBY,KAAMA,MAAQA,KAAKrD", "file": "store2.min.js"}