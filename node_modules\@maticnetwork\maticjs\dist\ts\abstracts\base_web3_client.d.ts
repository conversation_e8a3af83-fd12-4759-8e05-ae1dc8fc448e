import { BaseContract } from "../abstracts";
import { ITransactionRequestConfig, ITransactionReceipt, ITransactionData, IBlock, IBlockWithTransaction, IJsonRpcRequestPayload, IJsonRpcResponse, ITransactionWriteResult } from "../interfaces";
import { Logger } from "../utils";
export declare abstract class BaseWeb3Client {
    logger: Logger;
    abstract name: string;
    constructor(logger: Logger);
    abstract getContract(address: string, abi: any): BaseContract;
    abstract read(config: ITransactionRequestConfig): Promise<string>;
    abstract write(config: ITransactionRequestConfig): ITransactionWriteResult;
    abstract getGasPrice(): Promise<string>;
    abstract estimateGas(config: ITransactionRequestConfig): Promise<number>;
    abstract getChainId(): Promise<number>;
    abstract getTransactionCount(address: string, blockNumber: any): Promise<number>;
    abstract getTransaction(transactionHash: string): Promise<ITransactionData>;
    abstract getTransactionReceipt(transactionHash: string): Promise<ITransactionReceipt>;
    abstract getBlock(blockHashOrBlockNumber: any): Promise<IBlock>;
    abstract getBlockWithTransaction(blockHashOrBlockNumber: any): Promise<IBlockWithTransaction>;
    abstract hexToNumber(value: any): number;
    abstract hexToNumberString(value: any): string;
    abstract getBalance(address: string): Promise<string>;
    abstract getAccounts(): Promise<string[]>;
    abstract signTypedData(signer: string, typedData: object): Promise<string>;
    getRootHash?(startBlock: number, endBlock: number): Promise<string>;
    getAccountsUsingRPC_(): Promise<any>;
    abstract sendRPCRequest(request: IJsonRpcRequestPayload): Promise<IJsonRpcResponse>;
    abstract encodeParameters(params: any[], types: any[]): string;
    abstract decodeParameters(hexString: string, types: any[]): any[];
    abstract etheriumSha3(...value: any[]): string;
}
