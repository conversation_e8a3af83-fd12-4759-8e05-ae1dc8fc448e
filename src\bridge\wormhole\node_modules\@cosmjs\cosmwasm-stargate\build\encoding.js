"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromBinary = exports.toBinary = void 0;
const encoding_1 = require("@cosmjs/encoding");
/**
 * Takes a value, serializes it to JSON and encodes it as base64.
 *
 * This can be used for creating values of fields that have the CosmWasm Binary type.
 */
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
function toBinary(obj) {
    return (0, encoding_1.toBase64)((0, encoding_1.toUtf8)(JSON.stringify(obj)));
}
exports.toBinary = toBinary;
/**
 * Takes a base64 string, decodes it and parses the content from JSON to an object.
 *
 * This can be used for parsing the values of a CosmWasm Binary field.
 */
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
function fromBinary(base64) {
    return JSON.parse((0, encoding_1.fromUtf8)((0, encoding_1.fromBase64)(base64)));
}
exports.fromBinary = fromBinary;
//# sourceMappingURL=encoding.js.map