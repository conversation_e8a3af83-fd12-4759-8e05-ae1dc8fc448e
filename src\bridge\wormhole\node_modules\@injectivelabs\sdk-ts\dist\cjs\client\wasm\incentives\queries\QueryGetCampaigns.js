"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryGetCampaigns = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryGetCampaigns extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        const payload = {
            get_campaigns: {
                campaigns: this.params.campaigns,
            },
        };
        return (0, index_js_1.toBase64)(payload);
    }
}
exports.QueryGetCampaigns = QueryGetCampaigns;
