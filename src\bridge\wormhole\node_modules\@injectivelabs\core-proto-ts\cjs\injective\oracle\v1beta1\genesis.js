"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalldataRecord = exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var oracle_1 = require("./oracle.js");
exports.protobufPackage = "injective.oracle.v1beta1";
function createBaseGenesisState() {
    return {
        params: undefined,
        bandRelayers: [],
        bandPriceStates: [],
        priceFeedPriceStates: [],
        coinbasePriceStates: [],
        bandIbcPriceStates: [],
        bandIbcOracleRequests: [],
        bandIbcParams: undefined,
        bandIbcLatestClientId: "0",
        calldataRecords: [],
        bandIbcLatestRequestId: "0",
        chainlinkPriceStates: [],
        historicalPriceRecords: [],
        providerStates: [],
        pythPriceStates: [],
        storkPriceStates: [],
        storkPublishers: [],
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h, e_9, _j, e_10, _k, e_11, _l, e_12, _m, e_13, _o;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            oracle_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _p = __values(message.bandRelayers), _q = _p.next(); !_q.done; _q = _p.next()) {
                var v = _q.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_q && !_q.done && (_a = _p.return)) _a.call(_p);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _r = __values(message.bandPriceStates), _s = _r.next(); !_s.done; _s = _r.next()) {
                var v = _s.value;
                oracle_1.BandPriceState.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_s && !_s.done && (_b = _r.return)) _b.call(_r);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _t = __values(message.priceFeedPriceStates), _u = _t.next(); !_u.done; _u = _t.next()) {
                var v = _u.value;
                oracle_1.PriceFeedState.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_u && !_u.done && (_c = _t.return)) _c.call(_t);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _v = __values(message.coinbasePriceStates), _w = _v.next(); !_w.done; _w = _v.next()) {
                var v = _w.value;
                oracle_1.CoinbasePriceState.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_w && !_w.done && (_d = _v.return)) _d.call(_v);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _x = __values(message.bandIbcPriceStates), _y = _x.next(); !_y.done; _y = _x.next()) {
                var v = _y.value;
                oracle_1.BandPriceState.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_y && !_y.done && (_e = _x.return)) _e.call(_x);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _z = __values(message.bandIbcOracleRequests), _0 = _z.next(); !_0.done; _0 = _z.next()) {
                var v = _0.value;
                oracle_1.BandOracleRequest.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_0 && !_0.done && (_f = _z.return)) _f.call(_z);
            }
            finally { if (e_6) throw e_6.error; }
        }
        if (message.bandIbcParams !== undefined) {
            oracle_1.BandIBCParams.encode(message.bandIbcParams, writer.uint32(66).fork()).ldelim();
        }
        if (message.bandIbcLatestClientId !== "0") {
            writer.uint32(72).uint64(message.bandIbcLatestClientId);
        }
        try {
            for (var _1 = __values(message.calldataRecords), _2 = _1.next(); !_2.done; _2 = _1.next()) {
                var v = _2.value;
                exports.CalldataRecord.encode(v, writer.uint32(82).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_2 && !_2.done && (_g = _1.return)) _g.call(_1);
            }
            finally { if (e_7) throw e_7.error; }
        }
        if (message.bandIbcLatestRequestId !== "0") {
            writer.uint32(88).uint64(message.bandIbcLatestRequestId);
        }
        try {
            for (var _3 = __values(message.chainlinkPriceStates), _4 = _3.next(); !_4.done; _4 = _3.next()) {
                var v = _4.value;
                oracle_1.ChainlinkPriceState.encode(v, writer.uint32(98).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_4 && !_4.done && (_h = _3.return)) _h.call(_3);
            }
            finally { if (e_8) throw e_8.error; }
        }
        try {
            for (var _5 = __values(message.historicalPriceRecords), _6 = _5.next(); !_6.done; _6 = _5.next()) {
                var v = _6.value;
                oracle_1.PriceRecords.encode(v, writer.uint32(106).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_6 && !_6.done && (_j = _5.return)) _j.call(_5);
            }
            finally { if (e_9) throw e_9.error; }
        }
        try {
            for (var _7 = __values(message.providerStates), _8 = _7.next(); !_8.done; _8 = _7.next()) {
                var v = _8.value;
                oracle_1.ProviderState.encode(v, writer.uint32(114).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_8 && !_8.done && (_k = _7.return)) _k.call(_7);
            }
            finally { if (e_10) throw e_10.error; }
        }
        try {
            for (var _9 = __values(message.pythPriceStates), _10 = _9.next(); !_10.done; _10 = _9.next()) {
                var v = _10.value;
                oracle_1.PythPriceState.encode(v, writer.uint32(122).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_10 && !_10.done && (_l = _9.return)) _l.call(_9);
            }
            finally { if (e_11) throw e_11.error; }
        }
        try {
            for (var _11 = __values(message.storkPriceStates), _12 = _11.next(); !_12.done; _12 = _11.next()) {
                var v = _12.value;
                oracle_1.StorkPriceState.encode(v, writer.uint32(130).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_12 && !_12.done && (_m = _11.return)) _m.call(_11);
            }
            finally { if (e_12) throw e_12.error; }
        }
        try {
            for (var _13 = __values(message.storkPublishers), _14 = _13.next(); !_14.done; _14 = _13.next()) {
                var v = _14.value;
                writer.uint32(138).string(v);
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_14 && !_14.done && (_o = _13.return)) _o.call(_13);
            }
            finally { if (e_13) throw e_13.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = oracle_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.bandRelayers.push(reader.string());
                    break;
                case 3:
                    message.bandPriceStates.push(oracle_1.BandPriceState.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.priceFeedPriceStates.push(oracle_1.PriceFeedState.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.coinbasePriceStates.push(oracle_1.CoinbasePriceState.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.bandIbcPriceStates.push(oracle_1.BandPriceState.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.bandIbcOracleRequests.push(oracle_1.BandOracleRequest.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.bandIbcParams = oracle_1.BandIBCParams.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.bandIbcLatestClientId = longToString(reader.uint64());
                    break;
                case 10:
                    message.calldataRecords.push(exports.CalldataRecord.decode(reader, reader.uint32()));
                    break;
                case 11:
                    message.bandIbcLatestRequestId = longToString(reader.uint64());
                    break;
                case 12:
                    message.chainlinkPriceStates.push(oracle_1.ChainlinkPriceState.decode(reader, reader.uint32()));
                    break;
                case 13:
                    message.historicalPriceRecords.push(oracle_1.PriceRecords.decode(reader, reader.uint32()));
                    break;
                case 14:
                    message.providerStates.push(oracle_1.ProviderState.decode(reader, reader.uint32()));
                    break;
                case 15:
                    message.pythPriceStates.push(oracle_1.PythPriceState.decode(reader, reader.uint32()));
                    break;
                case 16:
                    message.storkPriceStates.push(oracle_1.StorkPriceState.decode(reader, reader.uint32()));
                    break;
                case 17:
                    message.storkPublishers.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? oracle_1.Params.fromJSON(object.params) : undefined,
            bandRelayers: Array.isArray(object === null || object === void 0 ? void 0 : object.bandRelayers) ? object.bandRelayers.map(function (e) { return String(e); }) : [],
            bandPriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.bandPriceStates)
                ? object.bandPriceStates.map(function (e) { return oracle_1.BandPriceState.fromJSON(e); })
                : [],
            priceFeedPriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.priceFeedPriceStates)
                ? object.priceFeedPriceStates.map(function (e) { return oracle_1.PriceFeedState.fromJSON(e); })
                : [],
            coinbasePriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.coinbasePriceStates)
                ? object.coinbasePriceStates.map(function (e) { return oracle_1.CoinbasePriceState.fromJSON(e); })
                : [],
            bandIbcPriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.bandIbcPriceStates)
                ? object.bandIbcPriceStates.map(function (e) { return oracle_1.BandPriceState.fromJSON(e); })
                : [],
            bandIbcOracleRequests: Array.isArray(object === null || object === void 0 ? void 0 : object.bandIbcOracleRequests)
                ? object.bandIbcOracleRequests.map(function (e) { return oracle_1.BandOracleRequest.fromJSON(e); })
                : [],
            bandIbcParams: isSet(object.bandIbcParams) ? oracle_1.BandIBCParams.fromJSON(object.bandIbcParams) : undefined,
            bandIbcLatestClientId: isSet(object.bandIbcLatestClientId) ? String(object.bandIbcLatestClientId) : "0",
            calldataRecords: Array.isArray(object === null || object === void 0 ? void 0 : object.calldataRecords)
                ? object.calldataRecords.map(function (e) { return exports.CalldataRecord.fromJSON(e); })
                : [],
            bandIbcLatestRequestId: isSet(object.bandIbcLatestRequestId) ? String(object.bandIbcLatestRequestId) : "0",
            chainlinkPriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.chainlinkPriceStates)
                ? object.chainlinkPriceStates.map(function (e) { return oracle_1.ChainlinkPriceState.fromJSON(e); })
                : [],
            historicalPriceRecords: Array.isArray(object === null || object === void 0 ? void 0 : object.historicalPriceRecords)
                ? object.historicalPriceRecords.map(function (e) { return oracle_1.PriceRecords.fromJSON(e); })
                : [],
            providerStates: Array.isArray(object === null || object === void 0 ? void 0 : object.providerStates)
                ? object.providerStates.map(function (e) { return oracle_1.ProviderState.fromJSON(e); })
                : [],
            pythPriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.pythPriceStates)
                ? object.pythPriceStates.map(function (e) { return oracle_1.PythPriceState.fromJSON(e); })
                : [],
            storkPriceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.storkPriceStates)
                ? object.storkPriceStates.map(function (e) { return oracle_1.StorkPriceState.fromJSON(e); })
                : [],
            storkPublishers: Array.isArray(object === null || object === void 0 ? void 0 : object.storkPublishers) ? object.storkPublishers.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? oracle_1.Params.toJSON(message.params) : undefined);
        if (message.bandRelayers) {
            obj.bandRelayers = message.bandRelayers.map(function (e) { return e; });
        }
        else {
            obj.bandRelayers = [];
        }
        if (message.bandPriceStates) {
            obj.bandPriceStates = message.bandPriceStates.map(function (e) { return e ? oracle_1.BandPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.bandPriceStates = [];
        }
        if (message.priceFeedPriceStates) {
            obj.priceFeedPriceStates = message.priceFeedPriceStates.map(function (e) { return e ? oracle_1.PriceFeedState.toJSON(e) : undefined; });
        }
        else {
            obj.priceFeedPriceStates = [];
        }
        if (message.coinbasePriceStates) {
            obj.coinbasePriceStates = message.coinbasePriceStates.map(function (e) { return e ? oracle_1.CoinbasePriceState.toJSON(e) : undefined; });
        }
        else {
            obj.coinbasePriceStates = [];
        }
        if (message.bandIbcPriceStates) {
            obj.bandIbcPriceStates = message.bandIbcPriceStates.map(function (e) { return e ? oracle_1.BandPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.bandIbcPriceStates = [];
        }
        if (message.bandIbcOracleRequests) {
            obj.bandIbcOracleRequests = message.bandIbcOracleRequests.map(function (e) { return e ? oracle_1.BandOracleRequest.toJSON(e) : undefined; });
        }
        else {
            obj.bandIbcOracleRequests = [];
        }
        message.bandIbcParams !== undefined &&
            (obj.bandIbcParams = message.bandIbcParams ? oracle_1.BandIBCParams.toJSON(message.bandIbcParams) : undefined);
        message.bandIbcLatestClientId !== undefined && (obj.bandIbcLatestClientId = message.bandIbcLatestClientId);
        if (message.calldataRecords) {
            obj.calldataRecords = message.calldataRecords.map(function (e) { return e ? exports.CalldataRecord.toJSON(e) : undefined; });
        }
        else {
            obj.calldataRecords = [];
        }
        message.bandIbcLatestRequestId !== undefined && (obj.bandIbcLatestRequestId = message.bandIbcLatestRequestId);
        if (message.chainlinkPriceStates) {
            obj.chainlinkPriceStates = message.chainlinkPriceStates.map(function (e) { return e ? oracle_1.ChainlinkPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.chainlinkPriceStates = [];
        }
        if (message.historicalPriceRecords) {
            obj.historicalPriceRecords = message.historicalPriceRecords.map(function (e) { return e ? oracle_1.PriceRecords.toJSON(e) : undefined; });
        }
        else {
            obj.historicalPriceRecords = [];
        }
        if (message.providerStates) {
            obj.providerStates = message.providerStates.map(function (e) { return e ? oracle_1.ProviderState.toJSON(e) : undefined; });
        }
        else {
            obj.providerStates = [];
        }
        if (message.pythPriceStates) {
            obj.pythPriceStates = message.pythPriceStates.map(function (e) { return e ? oracle_1.PythPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.pythPriceStates = [];
        }
        if (message.storkPriceStates) {
            obj.storkPriceStates = message.storkPriceStates.map(function (e) { return e ? oracle_1.StorkPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.storkPriceStates = [];
        }
        if (message.storkPublishers) {
            obj.storkPublishers = message.storkPublishers.map(function (e) { return e; });
        }
        else {
            obj.storkPublishers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? oracle_1.Params.fromPartial(object.params)
            : undefined;
        message.bandRelayers = ((_a = object.bandRelayers) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.bandPriceStates = ((_b = object.bandPriceStates) === null || _b === void 0 ? void 0 : _b.map(function (e) { return oracle_1.BandPriceState.fromPartial(e); })) || [];
        message.priceFeedPriceStates = ((_c = object.priceFeedPriceStates) === null || _c === void 0 ? void 0 : _c.map(function (e) { return oracle_1.PriceFeedState.fromPartial(e); })) || [];
        message.coinbasePriceStates = ((_d = object.coinbasePriceStates) === null || _d === void 0 ? void 0 : _d.map(function (e) { return oracle_1.CoinbasePriceState.fromPartial(e); })) || [];
        message.bandIbcPriceStates = ((_e = object.bandIbcPriceStates) === null || _e === void 0 ? void 0 : _e.map(function (e) { return oracle_1.BandPriceState.fromPartial(e); })) || [];
        message.bandIbcOracleRequests = ((_f = object.bandIbcOracleRequests) === null || _f === void 0 ? void 0 : _f.map(function (e) { return oracle_1.BandOracleRequest.fromPartial(e); })) || [];
        message.bandIbcParams = (object.bandIbcParams !== undefined && object.bandIbcParams !== null)
            ? oracle_1.BandIBCParams.fromPartial(object.bandIbcParams)
            : undefined;
        message.bandIbcLatestClientId = (_g = object.bandIbcLatestClientId) !== null && _g !== void 0 ? _g : "0";
        message.calldataRecords = ((_h = object.calldataRecords) === null || _h === void 0 ? void 0 : _h.map(function (e) { return exports.CalldataRecord.fromPartial(e); })) || [];
        message.bandIbcLatestRequestId = (_j = object.bandIbcLatestRequestId) !== null && _j !== void 0 ? _j : "0";
        message.chainlinkPriceStates = ((_k = object.chainlinkPriceStates) === null || _k === void 0 ? void 0 : _k.map(function (e) { return oracle_1.ChainlinkPriceState.fromPartial(e); })) || [];
        message.historicalPriceRecords = ((_l = object.historicalPriceRecords) === null || _l === void 0 ? void 0 : _l.map(function (e) { return oracle_1.PriceRecords.fromPartial(e); })) || [];
        message.providerStates = ((_m = object.providerStates) === null || _m === void 0 ? void 0 : _m.map(function (e) { return oracle_1.ProviderState.fromPartial(e); })) || [];
        message.pythPriceStates = ((_o = object.pythPriceStates) === null || _o === void 0 ? void 0 : _o.map(function (e) { return oracle_1.PythPriceState.fromPartial(e); })) || [];
        message.storkPriceStates = ((_p = object.storkPriceStates) === null || _p === void 0 ? void 0 : _p.map(function (e) { return oracle_1.StorkPriceState.fromPartial(e); })) || [];
        message.storkPublishers = ((_q = object.storkPublishers) === null || _q === void 0 ? void 0 : _q.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseCalldataRecord() {
    return { clientId: "0", calldata: new Uint8Array() };
}
exports.CalldataRecord = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.clientId !== "0") {
            writer.uint32(8).uint64(message.clientId);
        }
        if (message.calldata.length !== 0) {
            writer.uint32(18).bytes(message.calldata);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCalldataRecord();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.clientId = longToString(reader.uint64());
                    break;
                case 2:
                    message.calldata = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            clientId: isSet(object.clientId) ? String(object.clientId) : "0",
            calldata: isSet(object.calldata) ? bytesFromBase64(object.calldata) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.clientId !== undefined && (obj.clientId = message.clientId);
        message.calldata !== undefined &&
            (obj.calldata = base64FromBytes(message.calldata !== undefined ? message.calldata : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.CalldataRecord.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCalldataRecord();
        message.clientId = (_a = object.clientId) !== null && _a !== void 0 ? _a : "0";
        message.calldata = (_b = object.calldata) !== null && _b !== void 0 ? _b : new Uint8Array();
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
