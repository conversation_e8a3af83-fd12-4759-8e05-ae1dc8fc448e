import { ChainId, Ethereum<PERSON>hainId } from '@injectivelabs/ts-types';
import { Msgs } from '../../modules/msgs.js';
import { GoogleProtobufAny, CosmosTxV1Beta1Tx, InjectiveTypesV1Beta1TxExt, CosmosTxSigningV1Beta1Signing } from '@injectivelabs/core-proto-ts';
export declare const getPublicKey: ({ chainId, key, }: {
    chainId: string;
    key: string | GoogleProtobufAny.Any;
}) => GoogleProtobufAny.Any;
export declare const createBody: ({ message, memo, timeoutHeight, }: {
    message: Msgs | Msgs[];
    memo?: string;
    timeoutHeight?: number;
}) => CosmosTxV1Beta1Tx.TxBody;
export declare const createFee: ({ fee, payer, granter, gasLimit, }: {
    fee: {
        amount: string;
        denom: string;
    };
    payer?: string;
    granter?: string;
    gasLimit: number;
}) => CosmosTxV1Beta1Tx.Fee;
export declare const createSigners: ({ chainId, mode, signers, }: {
    chainId: string;
    signers: {
        pubKey: string | GoogleProtobufAny.Any;
        sequence: number;
    }[];
    mode: CosmosTxSigningV1Beta1Signing.SignMode;
}) => CosmosTxV1Beta1Tx.SignerInfo[];
export declare const createSignerInfo: ({ chainId, publicKey, sequence, mode, }: {
    chainId: string;
    publicKey: string | GoogleProtobufAny.Any;
    sequence: number;
    mode: CosmosTxSigningV1Beta1Signing.SignMode;
}) => CosmosTxV1Beta1Tx.SignerInfo;
export declare const createAuthInfo: ({ signerInfo, fee, }: {
    signerInfo: CosmosTxV1Beta1Tx.SignerInfo[];
    fee: CosmosTxV1Beta1Tx.Fee;
}) => CosmosTxV1Beta1Tx.AuthInfo;
export declare const createSignDoc: ({ bodyBytes, authInfoBytes, chainId, accountNumber, }: {
    bodyBytes: Uint8Array;
    authInfoBytes: Uint8Array;
    chainId: string;
    accountNumber: number;
}) => CosmosTxV1Beta1Tx.SignDoc;
export declare const createSignDocFromTransaction: (args: {
    txRaw: CosmosTxV1Beta1Tx.TxRaw;
    chainId: string;
    accountNumber: number;
}) => CosmosTxV1Beta1Tx.SignDoc;
export declare const createTxRawEIP712: (txRaw: CosmosTxV1Beta1Tx.TxRaw, extension: InjectiveTypesV1Beta1TxExt.ExtensionOptionsWeb3Tx, nonCriticalExtension?: GoogleProtobufAny.Any | GoogleProtobufAny.Any[]) => CosmosTxV1Beta1Tx.TxRaw;
export declare const createWeb3Extension: ({ ethereumChainId, feePayer, feePayerSig, }: {
    ethereumChainId: EthereumChainId;
    feePayer?: string;
    feePayerSig?: Uint8Array;
}) => InjectiveTypesV1Beta1TxExt.ExtensionOptionsWeb3Tx;
export declare const createNonCriticalExtensionFromObject: (object: Record<string, unknown>) => GoogleProtobufAny.Any;
export declare const getTransactionPartsFromTxRaw: (txRaw: CosmosTxV1Beta1Tx.TxRaw) => {
    authInfo: CosmosTxV1Beta1Tx.AuthInfo;
    body: CosmosTxV1Beta1Tx.TxBody;
    signatures: Uint8Array[];
};
export declare const getAminoStdSignDoc: ({ memo, chainId, accountNumber, timeoutHeight, sequence, gas, msgs, }: {
    memo?: string;
    chainId: ChainId;
    timeoutHeight?: string;
    accountNumber: number;
    sequence: number;
    gas?: string;
    msgs: Msgs[];
}) => {
    chain_id: ChainId;
    timeout_height: string;
    account_number: string;
    sequence: string;
    fee: {
        amount: {
            denom: string;
            amount: string;
        }[];
        gas: string;
        payer: string | undefined;
        granter: string | undefined;
        feePayer: string | undefined;
    };
    msgs: {
        type: string;
        value: any;
    }[];
    memo: string;
};
