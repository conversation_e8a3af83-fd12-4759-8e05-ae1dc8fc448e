"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.MsgUpdateParamsDesc = exports.MsgRelayPythPricesDesc = exports.MsgRelayStorkMessageDesc = exports.MsgRelayCoinbaseMessagesDesc = exports.MsgRequestBandIBCRatesDesc = exports.MsgRelayBandRatesDesc = exports.MsgRelayPriceFeedPriceDesc = exports.MsgRelayProviderPricesDesc = exports.MsgDesc = exports.MsgClientImpl = exports.MsgUpdateParamsResponse = exports.MsgUpdateParams = exports.MsgRelayPythPricesResponse = exports.MsgRelayPythPrices = exports.MsgRequestBandIBCRatesResponse = exports.MsgRequestBandIBCRates = exports.MsgRelayStorkPricesResponse = exports.MsgRelayStorkPrices = exports.MsgRelayCoinbaseMessagesResponse = exports.MsgRelayCoinbaseMessages = exports.MsgRelayBandRatesResponse = exports.MsgRelayBandRates = exports.MsgRelayPriceFeedPriceResponse = exports.MsgRelayPriceFeedPrice = exports.MsgRelayProviderPricesResponse = exports.MsgRelayProviderPrices = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var oracle_1 = require("./oracle.js");
exports.protobufPackage = "injective.oracle.v1beta1";
function createBaseMsgRelayProviderPrices() {
    return { sender: "", provider: "", symbols: [], prices: [] };
}
exports.MsgRelayProviderPrices = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.provider !== "") {
            writer.uint32(18).string(message.provider);
        }
        try {
            for (var _c = __values(message.symbols), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(message.prices), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayProviderPrices();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.provider = reader.string();
                    break;
                case 3:
                    message.symbols.push(reader.string());
                    break;
                case 4:
                    message.prices.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            provider: isSet(object.provider) ? String(object.provider) : "",
            symbols: Array.isArray(object === null || object === void 0 ? void 0 : object.symbols) ? object.symbols.map(function (e) { return String(e); }) : [],
            prices: Array.isArray(object === null || object === void 0 ? void 0 : object.prices) ? object.prices.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.provider !== undefined && (obj.provider = message.provider);
        if (message.symbols) {
            obj.symbols = message.symbols.map(function (e) { return e; });
        }
        else {
            obj.symbols = [];
        }
        if (message.prices) {
            obj.prices = message.prices.map(function (e) { return e; });
        }
        else {
            obj.prices = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayProviderPrices.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMsgRelayProviderPrices();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.provider = (_b = object.provider) !== null && _b !== void 0 ? _b : "";
        message.symbols = ((_c = object.symbols) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.prices = ((_d = object.prices) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgRelayProviderPricesResponse() {
    return {};
}
exports.MsgRelayProviderPricesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayProviderPricesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayProviderPricesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRelayProviderPricesResponse();
        return message;
    },
};
function createBaseMsgRelayPriceFeedPrice() {
    return { sender: "", base: [], quote: [], price: [] };
}
exports.MsgRelayPriceFeedPrice = {
    encode: function (message, writer) {
        var e_3, _a, e_4, _b, e_5, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _d = __values(message.base), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _f = __values(message.quote), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _h = __values(message.price), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayPriceFeedPrice();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.base.push(reader.string());
                    break;
                case 3:
                    message.quote.push(reader.string());
                    break;
                case 4:
                    message.price.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            base: Array.isArray(object === null || object === void 0 ? void 0 : object.base) ? object.base.map(function (e) { return String(e); }) : [],
            quote: Array.isArray(object === null || object === void 0 ? void 0 : object.quote) ? object.quote.map(function (e) { return String(e); }) : [],
            price: Array.isArray(object === null || object === void 0 ? void 0 : object.price) ? object.price.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.base) {
            obj.base = message.base.map(function (e) { return e; });
        }
        else {
            obj.base = [];
        }
        if (message.quote) {
            obj.quote = message.quote.map(function (e) { return e; });
        }
        else {
            obj.quote = [];
        }
        if (message.price) {
            obj.price = message.price.map(function (e) { return e; });
        }
        else {
            obj.price = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayPriceFeedPrice.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMsgRelayPriceFeedPrice();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.base = ((_b = object.base) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.quote = ((_c = object.quote) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.price = ((_d = object.price) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgRelayPriceFeedPriceResponse() {
    return {};
}
exports.MsgRelayPriceFeedPriceResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayPriceFeedPriceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayPriceFeedPriceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRelayPriceFeedPriceResponse();
        return message;
    },
};
function createBaseMsgRelayBandRates() {
    return { relayer: "", symbols: [], rates: [], resolveTimes: [], requestIDs: [] };
}
exports.MsgRelayBandRates = {
    encode: function (message, writer) {
        var e_6, _a, e_7, _b, e_8, _c, e_9, _d;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.relayer !== "") {
            writer.uint32(10).string(message.relayer);
        }
        try {
            for (var _e = __values(message.symbols), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_a = _e.return)) _a.call(_e);
            }
            finally { if (e_6) throw e_6.error; }
        }
        writer.uint32(26).fork();
        try {
            for (var _g = __values(message.rates), _h = _g.next(); !_h.done; _h = _g.next()) {
                var v = _h.value;
                writer.uint64(v);
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_h && !_h.done && (_b = _g.return)) _b.call(_g);
            }
            finally { if (e_7) throw e_7.error; }
        }
        writer.ldelim();
        writer.uint32(34).fork();
        try {
            for (var _j = __values(message.resolveTimes), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                writer.uint64(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_c = _j.return)) _c.call(_j);
            }
            finally { if (e_8) throw e_8.error; }
        }
        writer.ldelim();
        writer.uint32(42).fork();
        try {
            for (var _l = __values(message.requestIDs), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                writer.uint64(v);
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_d = _l.return)) _d.call(_l);
            }
            finally { if (e_9) throw e_9.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayBandRates();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.relayer = reader.string();
                    break;
                case 2:
                    message.symbols.push(reader.string());
                    break;
                case 3:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.rates.push(longToString(reader.uint64()));
                        }
                    }
                    else {
                        message.rates.push(longToString(reader.uint64()));
                    }
                    break;
                case 4:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.resolveTimes.push(longToString(reader.uint64()));
                        }
                    }
                    else {
                        message.resolveTimes.push(longToString(reader.uint64()));
                    }
                    break;
                case 5:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.requestIDs.push(longToString(reader.uint64()));
                        }
                    }
                    else {
                        message.requestIDs.push(longToString(reader.uint64()));
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            relayer: isSet(object.relayer) ? String(object.relayer) : "",
            symbols: Array.isArray(object === null || object === void 0 ? void 0 : object.symbols) ? object.symbols.map(function (e) { return String(e); }) : [],
            rates: Array.isArray(object === null || object === void 0 ? void 0 : object.rates) ? object.rates.map(function (e) { return String(e); }) : [],
            resolveTimes: Array.isArray(object === null || object === void 0 ? void 0 : object.resolveTimes) ? object.resolveTimes.map(function (e) { return String(e); }) : [],
            requestIDs: Array.isArray(object === null || object === void 0 ? void 0 : object.requestIDs) ? object.requestIDs.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.relayer !== undefined && (obj.relayer = message.relayer);
        if (message.symbols) {
            obj.symbols = message.symbols.map(function (e) { return e; });
        }
        else {
            obj.symbols = [];
        }
        if (message.rates) {
            obj.rates = message.rates.map(function (e) { return e; });
        }
        else {
            obj.rates = [];
        }
        if (message.resolveTimes) {
            obj.resolveTimes = message.resolveTimes.map(function (e) { return e; });
        }
        else {
            obj.resolveTimes = [];
        }
        if (message.requestIDs) {
            obj.requestIDs = message.requestIDs.map(function (e) { return e; });
        }
        else {
            obj.requestIDs = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayBandRates.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseMsgRelayBandRates();
        message.relayer = (_a = object.relayer) !== null && _a !== void 0 ? _a : "";
        message.symbols = ((_b = object.symbols) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.rates = ((_c = object.rates) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.resolveTimes = ((_d = object.resolveTimes) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        message.requestIDs = ((_e = object.requestIDs) === null || _e === void 0 ? void 0 : _e.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgRelayBandRatesResponse() {
    return {};
}
exports.MsgRelayBandRatesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayBandRatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayBandRatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRelayBandRatesResponse();
        return message;
    },
};
function createBaseMsgRelayCoinbaseMessages() {
    return { sender: "", messages: [], signatures: [] };
}
exports.MsgRelayCoinbaseMessages = {
    encode: function (message, writer) {
        var e_10, _a, e_11, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _c = __values(message.messages), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(18).bytes(v);
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_10) throw e_10.error; }
        }
        try {
            for (var _e = __values(message.signatures), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(26).bytes(v);
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_11) throw e_11.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayCoinbaseMessages();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.messages.push(reader.bytes());
                    break;
                case 3:
                    message.signatures.push(reader.bytes());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            messages: Array.isArray(object === null || object === void 0 ? void 0 : object.messages) ? object.messages.map(function (e) { return bytesFromBase64(e); }) : [],
            signatures: Array.isArray(object === null || object === void 0 ? void 0 : object.signatures) ? object.signatures.map(function (e) { return bytesFromBase64(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.messages) {
            obj.messages = message.messages.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.messages = [];
        }
        if (message.signatures) {
            obj.signatures = message.signatures.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.signatures = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayCoinbaseMessages.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgRelayCoinbaseMessages();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.messages = ((_b = object.messages) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.signatures = ((_c = object.signatures) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgRelayCoinbaseMessagesResponse() {
    return {};
}
exports.MsgRelayCoinbaseMessagesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayCoinbaseMessagesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayCoinbaseMessagesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRelayCoinbaseMessagesResponse();
        return message;
    },
};
function createBaseMsgRelayStorkPrices() {
    return { sender: "", assetPairs: [] };
}
exports.MsgRelayStorkPrices = {
    encode: function (message, writer) {
        var e_12, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.assetPairs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.AssetPair.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayStorkPrices();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.assetPairs.push(oracle_1.AssetPair.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            assetPairs: Array.isArray(object === null || object === void 0 ? void 0 : object.assetPairs) ? object.assetPairs.map(function (e) { return oracle_1.AssetPair.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.assetPairs) {
            obj.assetPairs = message.assetPairs.map(function (e) { return e ? oracle_1.AssetPair.toJSON(e) : undefined; });
        }
        else {
            obj.assetPairs = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayStorkPrices.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgRelayStorkPrices();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.assetPairs = ((_b = object.assetPairs) === null || _b === void 0 ? void 0 : _b.map(function (e) { return oracle_1.AssetPair.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgRelayStorkPricesResponse() {
    return {};
}
exports.MsgRelayStorkPricesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayStorkPricesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayStorkPricesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRelayStorkPricesResponse();
        return message;
    },
};
function createBaseMsgRequestBandIBCRates() {
    return { sender: "", requestId: "0" };
}
exports.MsgRequestBandIBCRates = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.requestId !== "0") {
            writer.uint32(16).uint64(message.requestId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRequestBandIBCRates();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.requestId = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            requestId: isSet(object.requestId) ? String(object.requestId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.requestId !== undefined && (obj.requestId = message.requestId);
        return obj;
    },
    create: function (base) {
        return exports.MsgRequestBandIBCRates.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgRequestBandIBCRates();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.requestId = (_b = object.requestId) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseMsgRequestBandIBCRatesResponse() {
    return {};
}
exports.MsgRequestBandIBCRatesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRequestBandIBCRatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRequestBandIBCRatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRequestBandIBCRatesResponse();
        return message;
    },
};
function createBaseMsgRelayPythPrices() {
    return { sender: "", priceAttestations: [] };
}
exports.MsgRelayPythPrices = {
    encode: function (message, writer) {
        var e_13, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.priceAttestations), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.PriceAttestation.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_13) throw e_13.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayPythPrices();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.priceAttestations.push(oracle_1.PriceAttestation.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            priceAttestations: Array.isArray(object === null || object === void 0 ? void 0 : object.priceAttestations)
                ? object.priceAttestations.map(function (e) { return oracle_1.PriceAttestation.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.priceAttestations) {
            obj.priceAttestations = message.priceAttestations.map(function (e) { return e ? oracle_1.PriceAttestation.toJSON(e) : undefined; });
        }
        else {
            obj.priceAttestations = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayPythPrices.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgRelayPythPrices();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.priceAttestations = ((_b = object.priceAttestations) === null || _b === void 0 ? void 0 : _b.map(function (e) { return oracle_1.PriceAttestation.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgRelayPythPricesResponse() {
    return {};
}
exports.MsgRelayPythPricesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRelayPythPricesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRelayPythPricesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRelayPythPricesResponse();
        return message;
    },
};
function createBaseMsgUpdateParams() {
    return { authority: "", params: undefined };
}
exports.MsgUpdateParams = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.authority !== "") {
            writer.uint32(10).string(message.authority);
        }
        if (message.params !== undefined) {
            oracle_1.Params.encode(message.params, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.authority = reader.string();
                    break;
                case 2:
                    message.params = oracle_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            authority: isSet(object.authority) ? String(object.authority) : "",
            params: isSet(object.params) ? oracle_1.Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.authority !== undefined && (obj.authority = message.authority);
        message.params !== undefined && (obj.params = message.params ? oracle_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgUpdateParams();
        message.authority = (_a = object.authority) !== null && _a !== void 0 ? _a : "";
        message.params = (object.params !== undefined && object.params !== null)
            ? oracle_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseMsgUpdateParamsResponse() {
    return {};
}
exports.MsgUpdateParamsResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateParamsResponse();
        return message;
    },
};
var MsgClientImpl = /** @class */ (function () {
    function MsgClientImpl(rpc) {
        this.rpc = rpc;
        this.RelayProviderPrices = this.RelayProviderPrices.bind(this);
        this.RelayPriceFeedPrice = this.RelayPriceFeedPrice.bind(this);
        this.RelayBandRates = this.RelayBandRates.bind(this);
        this.RequestBandIBCRates = this.RequestBandIBCRates.bind(this);
        this.RelayCoinbaseMessages = this.RelayCoinbaseMessages.bind(this);
        this.RelayStorkMessage = this.RelayStorkMessage.bind(this);
        this.RelayPythPrices = this.RelayPythPrices.bind(this);
        this.UpdateParams = this.UpdateParams.bind(this);
    }
    MsgClientImpl.prototype.RelayProviderPrices = function (request, metadata) {
        return this.rpc.unary(exports.MsgRelayProviderPricesDesc, exports.MsgRelayProviderPrices.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RelayPriceFeedPrice = function (request, metadata) {
        return this.rpc.unary(exports.MsgRelayPriceFeedPriceDesc, exports.MsgRelayPriceFeedPrice.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RelayBandRates = function (request, metadata) {
        return this.rpc.unary(exports.MsgRelayBandRatesDesc, exports.MsgRelayBandRates.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RequestBandIBCRates = function (request, metadata) {
        return this.rpc.unary(exports.MsgRequestBandIBCRatesDesc, exports.MsgRequestBandIBCRates.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RelayCoinbaseMessages = function (request, metadata) {
        return this.rpc.unary(exports.MsgRelayCoinbaseMessagesDesc, exports.MsgRelayCoinbaseMessages.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RelayStorkMessage = function (request, metadata) {
        return this.rpc.unary(exports.MsgRelayStorkMessageDesc, exports.MsgRelayStorkPrices.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RelayPythPrices = function (request, metadata) {
        return this.rpc.unary(exports.MsgRelayPythPricesDesc, exports.MsgRelayPythPrices.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.UpdateParams = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateParamsDesc, exports.MsgUpdateParams.fromPartial(request), metadata);
    };
    return MsgClientImpl;
}());
exports.MsgClientImpl = MsgClientImpl;
exports.MsgDesc = { serviceName: "injective.oracle.v1beta1.Msg" };
exports.MsgRelayProviderPricesDesc = {
    methodName: "RelayProviderPrices",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRelayProviderPrices.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRelayProviderPricesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRelayPriceFeedPriceDesc = {
    methodName: "RelayPriceFeedPrice",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRelayPriceFeedPrice.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRelayPriceFeedPriceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRelayBandRatesDesc = {
    methodName: "RelayBandRates",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRelayBandRates.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRelayBandRatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRequestBandIBCRatesDesc = {
    methodName: "RequestBandIBCRates",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRequestBandIBCRates.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRequestBandIBCRatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRelayCoinbaseMessagesDesc = {
    methodName: "RelayCoinbaseMessages",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRelayCoinbaseMessages.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRelayCoinbaseMessagesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRelayStorkMessageDesc = {
    methodName: "RelayStorkMessage",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRelayStorkPrices.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRelayStorkPricesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRelayPythPricesDesc = {
    methodName: "RelayPythPrices",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRelayPythPrices.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRelayPythPricesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgUpdateParamsDesc = {
    methodName: "UpdateParams",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateParams.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
