"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcClient = void 0;
const ChainGrpcAuctionApi_js_1 = require("./grpc/ChainGrpcAuctionApi.js");
const ChainGrpcBankApi_js_1 = require("./grpc/ChainGrpcBankApi.js");
const ChainGrpcAuthApi_js_1 = require("./grpc/ChainGrpcAuthApi.js");
const ChainGrpcDistributionApi_js_1 = require("./grpc/ChainGrpcDistributionApi.js");
const ChainGrpcExchangeApi_js_1 = require("./grpc/ChainGrpcExchangeApi.js");
const ChainGrpcGovApi_js_1 = require("./grpc/ChainGrpcGovApi.js");
const ChainGrpcIbcApi_js_1 = require("./grpc/ChainGrpcIbcApi.js");
const ChainGrpcInsuranceFundApi_js_1 = require("./grpc/ChainGrpcInsuranceFundApi.js");
const ChainGrpcMintApi_js_1 = require("./grpc/ChainGrpcMintApi.js");
const ChainGrpcOracleApi_js_1 = require("./grpc/ChainGrpcOracleApi.js");
const ChainGrpcPeggyApi_js_1 = require("./grpc/ChainGrpcPeggyApi.js");
const ChainGrpcPermissionsApi_js_1 = require("./grpc/ChainGrpcPermissionsApi.js");
const ChainGrpcStakingApi_js_1 = require("./grpc/ChainGrpcStakingApi.js");
const ChainGrpcTokenFactoryApi_js_1 = require("./grpc/ChainGrpcTokenFactoryApi.js");
const ChainGrpcWasmApi_js_1 = require("./grpc/ChainGrpcWasmApi.js");
const ChainGrpcWasmXApi_js_1 = require("./grpc/ChainGrpcWasmXApi.js");
/**
 * @category Chain Grpc API
 * @hidden
 */
class ChainGrpcClient {
    auction;
    auth;
    bank;
    distribution;
    exchange;
    gov;
    ibc;
    insuranceFund;
    mint;
    oracle;
    peggy;
    permissions;
    staking;
    tokenfactory;
    wasm;
    wasmX;
    constructor(endpoint) {
        this.auction = new ChainGrpcAuctionApi_js_1.ChainGrpcAuctionApi(endpoint);
        this.auth = new ChainGrpcAuthApi_js_1.ChainGrpcAuthApi(endpoint);
        this.bank = new ChainGrpcBankApi_js_1.ChainGrpcBankApi(endpoint);
        this.distribution = new ChainGrpcDistributionApi_js_1.ChainGrpcDistributionApi(endpoint);
        this.exchange = new ChainGrpcExchangeApi_js_1.ChainGrpcExchangeApi(endpoint);
        this.gov = new ChainGrpcGovApi_js_1.ChainGrpcGovApi(endpoint);
        this.ibc = new ChainGrpcIbcApi_js_1.ChainGrpcIbcApi(endpoint);
        this.insuranceFund = new ChainGrpcInsuranceFundApi_js_1.ChainGrpcInsuranceFundApi(endpoint);
        this.mint = new ChainGrpcMintApi_js_1.ChainGrpcMintApi(endpoint);
        this.oracle = new ChainGrpcOracleApi_js_1.ChainGrpcOracleApi(endpoint);
        this.peggy = new ChainGrpcPeggyApi_js_1.ChainGrpcPeggyApi(endpoint);
        this.permissions = new ChainGrpcPermissionsApi_js_1.ChainGrpcPermissionsApi(endpoint);
        this.staking = new ChainGrpcStakingApi_js_1.ChainGrpcStakingApi(endpoint);
        this.tokenfactory = new ChainGrpcTokenFactoryApi_js_1.ChainGrpcTokenFactoryApi(endpoint);
        this.wasm = new ChainGrpcWasmApi_js_1.ChainGrpcWasmApi(endpoint);
        this.wasmX = new ChainGrpcWasmXApi_js_1.ChainGrpcWasmXApi(endpoint);
    }
}
exports.ChainGrpcClient = ChainGrpcClient;
