{"name": "web3-bzz", "version": "1.10.4", "description": "Web3 module to interact with the Swarm network.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-bzz", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "types": "types/index.d.ts", "scripts": {"compile": "tsc -b tsconfig.json", "dtslint": "dtslint --localTs ../../node_modules/typescript/lib types", "postinstall": "echo \"WARNING: the web3-bzz api will be deprecated in the next version\""}, "main": "lib/index.js", "dependencies": {"@types/node": "^12.12.6", "got": "12.1.0", "swarm-js": "^0.1.40"}, "devDependencies": {"dtslint": "^3.4.1", "typescript": "4.9.5"}, "gitHead": "56d35a4a9ec59c2735085dce1a5eebb0bb44fbce"}