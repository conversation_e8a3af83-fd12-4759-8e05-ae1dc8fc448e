import { ITransactionOption } from "../interfaces";
import { Web3Side<PERSON>hainClient } from "../utils";
import { POSToken } from "./pos_token";
import { TYPE_AMOUNT } from "../types";
import { IAllowanceTransactionOption, IApproveTransactionOption, IExitTransactionOption, IPOSClientConfig, IPOSContracts } from "../interfaces";
export declare class ERC20 extends POSToken {
    constructor(tokenAddress: string, isParent: boolean, client: Web3SideChainClient<IPOSClientConfig>, getContracts: () => IPOSContracts);
    getBalance(userAddress: string, option?: ITransactionOption): Promise<string>;
    /**
     * get allowance of user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    getAllowance(userAddress: string, option?: IAllowanceTransactionOption): Promise<string>;
    approve(amount: TYPE_AMOUNT, option?: IApproveTransactionOption): Promise<import("..").ITransactionWriteResult>;
    approveMax(option?: IApproveTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Deposit given amount of token for user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    deposit(amount: TYPE_AMOUNT, userAddress: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Deposit given amount of token for user along with ETHER for gas token
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    depositWithGas(amount: TYPE_AMOUNT, userAddress: string, swapEthAmount: TYPE_AMOUNT, swapCallData: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    private depositEther_;
    private depositEtherWithGas_;
    /**
     * initiate withdraw by burning provided amount
     *
     * @param {TYPE_AMOUNT} amount
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    withdrawStart(amount: TYPE_AMOUNT, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    private withdrawExit_;
    /**
     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    withdrawExit(burnTransactionHash: string, option?: IExitTransactionOption): any;
    /**
     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.
     *
     *  Note:- It create the proof in api call for fast exit.
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    withdrawExitFaster(burnTransactionHash: string, option?: IExitTransactionOption): any;
    /**
     * check if exit has been completed for a transaction hash
     *
     * @param {string} burnTxHash
     * @returns
     * @memberof ERC20
     */
    isWithdrawExited(burnTxHash: string): Promise<boolean>;
    /**
     * transfer amount to another user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} to
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    transfer(amount: TYPE_AMOUNT, to: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
}
