{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";;;AAAA,2CAAyD;AAiCzD;;;GAGG;AACH,IAAY,UA8BX;AA9BD,WAAY,UAAU;IACpB;;;OAGG;IACH,iFAA4B,CAAA;IAE5B;;;OAGG;IACH,sEAAuB,CAAA;IAEvB;;;OAGG;IACH,oFAA8B,CAAA;IAE9B;;;OAGG;IACH,0EAAyB,CAAA;IAEzB;;;OAGG;IACH,kEAAqB,CAAA;AACvB,CAAC,EA9BW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QA8BrB;AAqCD,SAAgB,iBAAiB,CAAC,KAAmC;IACnE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI,CAAA;KACZ;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAA;KACZ;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AATD,8CASC;AAED,SAAgB,YAAY,CAAC,KAAmC;IAC9D,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA,CAAC,iEAAiE;AACpG,CAAC;AAFD,oCAEC;AAED,SAAgB,wBAAwB,CACtC,KAAiD;IAEjD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,IAAI,CAAA;KACZ;IACD,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAA;KACZ;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAXD,4DAWC;AAED,SAAgB,mBAAmB,CACjC,KAAiD;IAEjD,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAA,CAAC,iEAAiE;AAC3G,CAAC;AAJD,kDAIC;AAWD;;GAEG;AACH,IAAY,eAMX;AAND,WAAY,eAAe;IACzB,yDAAU,CAAA;IACV,+EAAqB,CAAA;IACrB,6EAAoB,CAAA;IACpB,mEAAe,CAAA;IACf,yEAAkB,CAAA;AACpB,CAAC,EANW,eAAe,GAAf,uBAAe,KAAf,uBAAe,QAM1B;AAYD,SAAgB,UAAU,CAAC,EAAoB;IAC7C,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,MAAM,CAAA;AAC3C,CAAC;AAFD,gCAEC;AAED,SAAgB,qBAAqB,CAAC,EAAoB;IACxD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,CAAA;AACtD,CAAC;AAFD,sDAEC;AAED,SAAgB,oBAAoB,CAAC,EAAoB;IACvD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,gBAAgB,CAAA;AACrD,CAAC;AAFD,oDAEC;AAED,SAAgB,eAAe,CAAC,EAAoB;IAClD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,WAAW,CAAA;AAChD,CAAC;AAFD,0CAEC;AAED,SAAgB,kBAAkB,CAAC,EAAoB;IACrD,OAAO,EAAE,CAAC,IAAI,KAAK,eAAe,CAAC,cAAc,CAAA;AACnD,CAAC;AAFD,gDAEC;AAoFD,SAAgB,cAAc,CAAC,MAAmB;IAChD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,MAAM,CAAA;AAC1C,CAAC;AAHD,wCAGC;AAED,SAAgB,yBAAyB,CAAC,MAAmB;IAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,iBAAiB,CAAA;AACrD,CAAC;AAHD,8DAGC;AAED,SAAgB,wBAAwB,CAAC,MAAmB;IAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,gBAAgB,CAAA;AACpD,CAAC;AAHD,4DAGC;AAED,SAAgB,mBAAmB,CAAC,MAAmB;IACrD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,WAAW,CAAA;AAC/C,CAAC;AAHD,kDAGC;AAED,SAAgB,sBAAsB,CAAC,MAAmB;IACxD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,KAAK,eAAe,CAAC,cAAc,CAAA;AAClD,CAAC;AAHD,wDAGC"}