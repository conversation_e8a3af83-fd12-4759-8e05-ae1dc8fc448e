"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
const utf8_js_1 = require("./../../../../utils/utf8.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgSubmitVaa extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgSubmitVaa(params);
    }
    toData() {
        const { params } = this;
        return {
            data: (0, utf8_js_1.binaryToBase64)(params.signed),
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('submit_vaa', this.toData());
    }
}
exports.default = ExecArgSubmitVaa;
