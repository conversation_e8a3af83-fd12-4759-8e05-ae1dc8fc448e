/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.tx.config.v1";
function createBaseConfig() {
    return { skipAnteHandler: false, skipPostHandler: false };
}
export const Config = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.skipAnteHandler === true) {
            writer.uint32(8).bool(message.skipAnteHandler);
        }
        if (message.skipPostHandler === true) {
            writer.uint32(16).bool(message.skipPostHandler);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.skipAnteHandler = reader.bool();
                    break;
                case 2:
                    message.skipPostHandler = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            skipAnteHandler: isSet(object.skipAnteHandler) ? Boolean(object.skipAnteHandler) : false,
            skipPostHandler: isSet(object.skipPostHandler) ? Boolean(object.skipPostHandler) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.skipAnteHandler !== undefined && (obj.skipAnteHandler = message.skipAnteHandler);
        message.skipPostHandler !== undefined && (obj.skipPostHandler = message.skipPostHandler);
        return obj;
    },
    create(base) {
        return Config.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseConfig();
        message.skipAnteHandler = object.skipAnteHandler ?? false;
        message.skipPostHandler = object.skipPostHandler ?? false;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
