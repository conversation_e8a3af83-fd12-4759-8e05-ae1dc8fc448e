"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.blockHeaderFromRpc = void 0;
const header_js_1 = require("./header.js");
const helpers_js_1 = require("./helpers.js");
/**
 * Creates a new block header object from Ethereum JSON RPC.
 *
 * @param blockParams - Ethereum JSON RPC of block (eth_getBlockByNumber)
 * @param options - An object describing the blockchain
 */
function blockHeaderFromRpc(blockParams, options) {
    const { parentHash, sha3Uncles, miner, stateRoot, transactionsRoot, receiptsRoot, logsBloom, difficulty, number, gasLimit, gasUsed, timestamp, extraData, mixHash, nonce, baseFeePerGas, withdrawalsRoot, blobGasUsed, excessBlobGas, parentBeaconBlockRoot, requestsRoot, } = blockParams;
    const blockHeader = header_js_1.BlockHeader.fromHeaderData({
        parentHash,
        uncleHash: sha3Uncles,
        coinbase: miner,
        stateRoot,
        transactionsTrie: transactionsRoot,
        receiptTrie: receiptsRoot,
        logsBloom,
        difficulty: (0, helpers_js_1.numberToHex)(difficulty),
        number,
        gasLimit,
        gasUsed,
        timestamp,
        extraData,
        mixHash,
        nonce,
        baseFeePerGas,
        withdrawalsRoot,
        blobGasUsed,
        excessBlobGas,
        parentBeaconBlockRoot,
        requestsRoot,
    }, options);
    return blockHeader;
}
exports.blockHeaderFromRpc = blockHeaderFromRpc;
//# sourceMappingURL=header-from-rpc.js.map