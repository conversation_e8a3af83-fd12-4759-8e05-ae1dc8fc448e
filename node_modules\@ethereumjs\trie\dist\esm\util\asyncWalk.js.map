{"version": 3, "file": "asyncWalk.js", "sourceRoot": "", "sources": ["../../../src/util/asyncWalk.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AACrC,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,6BAA6B,CAAA;AAEhE,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAA;AAC9C,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAA;AAQpD;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,SAAS,CAAC,CAAC,SAAS,CAE9B,QAAoB,EACpB,aAAuB,EAAE,EACzB,UAAmB,KAAK,EAAE,SAAmB,EAAE,IAAc,EAAE,EAAE,GAAE,CAAC,EACpE,SAAqB,KAAK,EAAE,SAAmB,EAAE,IAAc,EAAE,EAAE,CAAC,IAAI,EACxE,UAAuB,IAAI,GAAG,EAAU;IAExC,IAAI,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE;QAC/C,OAAM;KACP;IACD,IAAI;QACF,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;QAC5C,IAAI,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;YAC1E,OAAM;SACP;QACD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAA;QAChD,MAAM,OAAO,CAAC,IAAK,EAAE,UAAU,CAAC,CAAA;QAChC,IAAI,MAAM,MAAM,CAAC,IAAK,EAAE,UAAU,CAAC,EAAE;YACnC,MAAM,EAAE,IAAI,EAAE,IAAK,EAAE,UAAU,EAAE,CAAA;SAClC;QACD,IAAI,IAAI,YAAY,UAAU,EAAE;YAC9B,KAAK,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE;gBAC1D,MAAM,OAAO,GAAG,CAAC,GAAG,UAAU,EAAE,MAAM,CAAC,CAAA;gBACvC,MAAM,UAAU,GACd,SAAS,YAAY,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;gBAChF,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;aAC3E;SACF;aAAM,IAAI,IAAI,YAAY,aAAa,EAAE;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;YAC9B,MAAM,OAAO,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;YACjD,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;SAC1E;KACF;IAAC,OAAO,CAAC,EAAE;QACV,OAAM;KACP;AACH,CAAC"}