{"version": 3, "file": "watch_transaction_for_confirmations.js", "sourceRoot": "", "sources": ["../../../src/utils/watch_transaction_for_confirmations.ts"], "names": [], "mappings": "AAkBA,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,SAAS,EAAc,MAAM,gBAAgB,CAAC;AAEvD,OAAO,EACN,yCAAyC,EACzC,yCAAyC,GACzC,MAAM,aAAa,CAAC;AAErB,OAAO,EAAE,wBAAwB,EAAE,MAAM,eAAe,CAAC;AACzD,OAAO,EACN,yBAAyB,GAEzB,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EAAE,8BAA8B,EAAE,MAAM,wCAAwC,CAAC;AAExF,MAAM,UAAU,gCAAgC,CAK/C,WAAyC,EACzC,qBAA2E,EAC3E,kBAAsC,EACtC,eAAsB,EACtB,YAA0B,EAC1B,8BAA2C;IAE3C,IAAI,SAAS,CAAC,kBAAkB,CAAC,IAAI,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC;QAC3E,MAAM,IAAI,yCAAyC,CAAC;YACnD,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,SAAS,EAAE,YAAY,CAAC;YACrF,eAAe,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,eAAe,EAAE,YAAY,CAAC;SAC7E,CAAC,CAAC;IAEJ,IAAI,CAAC,kBAAkB,CAAC,WAAW;QAClC,MAAM,IAAI,yCAAyC,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAEtF,2EAA2E;IAC3E,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE;QAC1C,aAAa,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC;QAC1D,OAAO,EAAE,MAAM,CACd,8BAA8B,aAA9B,8BAA8B,cAA9B,8BAA8B,GAAI,wBAAwB,EAC1D,kBAAkB,EAClB,YAAY,CACZ;QACD,eAAe,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,kBAAkB,CAAC,SAAS,EAAE,YAAY,CAAC;KAC/C,CAAC,CAAC;IAE9C,uEAAuE;IACvE,MAAM,QAAQ,GAAqB,WAAW,CAAC,cAAc,CAAC,QAA4B,CAAC;IAC3F,IAAI,QAAQ,IAAI,uBAAuB,IAAI,QAAQ,IAAI,QAAQ,CAAC,qBAAqB,EAAE,EAAE,CAAC;QACzF,8BAA8B,CAAC;YAC9B,WAAW;YACX,kBAAkB;YAClB,qBAAqB;YACrB,8BAA8B;YAC9B,YAAY;SACZ,CAAC,CAAC;IACJ,CAAC;SAAM,CAAC;QACP,yBAAyB,CAAC;YACzB,WAAW;YACX,kBAAkB;YAClB,qBAAqB;YACrB,8BAA8B;YAC9B,YAAY;SACZ,CAAC,CAAC;IACJ,CAAC;AACF,CAAC"}