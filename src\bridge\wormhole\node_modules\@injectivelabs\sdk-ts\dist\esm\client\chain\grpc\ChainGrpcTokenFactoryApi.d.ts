import { InjectiveTokenFactoryV1Beta1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
/**
 * @category TokenFactory Grpc API
 */
export declare class ChainGrpcTokenFactoryApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveTokenFactoryV1Beta1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchDenomsFromCreator(creator: string): Promise<string[]>;
    fetchDenomAuthorityMetadata(creator: string, subDenom: string): Promise<import("../index.js").AuthorityMetadata>;
    fetchModuleParams(): Promise<import("../index.js").TokenFactoryModuleParams>;
    fetchModuleState(): Promise<import("../index.js").TokenFactoryModuleState>;
}
