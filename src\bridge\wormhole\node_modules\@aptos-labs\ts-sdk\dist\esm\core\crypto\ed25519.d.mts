import { Deserializer } from '../../bcs/deserializer.mjs';
import { Serializable, Serializer } from '../../bcs/serializer.mjs';
import { b as AccountPublicKey, V as VerifySignatureArgs, a as VerifySignatureAsyncArgs, A as Authentication<PERSON>ey, P as PublicKey } from '../../publicKey-CJOcUwJK.mjs';
import { HexInput } from '../../types/types.mjs';
import { PrivateKey } from './privateKey.mjs';
import { Signature } from './signature.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../hex.mjs';
import '../common.mjs';
import '../accountAddress.mjs';
import '../../transactions/instances/transactionArgument.mjs';
import '../../api/aptosConfig.mjs';
import '../../utils/const.mjs';

/**
 * Checks if an ED25519 signature is non-canonical.
 * This function helps determine the validity of a signature by verifying its canonical form.
 *
 * @param signature - The signature to be checked for canonicality.
 * @returns A boolean indicating whether the signature is non-canonical.
 *
 * Comes from Aptos Core
 * https://github.com/aptos-labs/aptos-core/blob/main/crates/aptos-crypto/src/ed25519/ed25519_sigs.rs#L47-L85
 * @group Implementation
 * @category Serialization
 */
declare function isCanonicalEd25519Signature(signature: Signature): boolean;
/**
 * Represents the public key of an Ed25519 key pair.
 *
 * Since [AIP-55](https://github.com/aptos-foundation/AIPs/pull/263) Aptos supports
 * `Legacy` and `Unified` authentication keys.
 *
 * Ed25519 scheme is represented in the SDK as `Legacy authentication key` and also
 * as `AnyPublicKey` that represents any `Unified authentication key`.
 * @group Implementation
 * @category Serialization
 */
declare class Ed25519PublicKey extends AccountPublicKey {
    /**
     * Length of an Ed25519 public key
     * @group Implementation
     * @category Serialization
     */
    static readonly LENGTH: number;
    /**
     * Bytes of the public key
     * @private
     * @group Implementation
     * @category Serialization
     */
    private readonly key;
    /**
     * Creates an instance of the Ed25519Signature class from a hex input.
     * This constructor validates the length of the signature to ensure it meets the required specifications.
     *
     * @param hexInput - The hex input representing the Ed25519 signature.
     * @throws Error if the signature length is not equal to Ed25519Signature.LENGTH.
     * @group Implementation
     * @category Serialization
     */
    constructor(hexInput: HexInput);
    /**
     * Verifies a signed message using a public key.
     *
     * @param args - The arguments for verification.
     * @param args.message - A signed message as a Hex string or Uint8Array.
     * @param args.signature - The signature of the message.
     * @group Implementation
     * @category Serialization
     */
    verifySignature(args: VerifySignatureArgs): boolean;
    /**
     * Note: Ed25519Signatures can be verified syncronously.
     *
     * Verifies the provided signature against the given message.
     * This function helps ensure the integrity and authenticity of the message by confirming that the signature is valid.
     *
     * @param args - The arguments for signature verification.
     * @param args.aptosConfig - The configuration object for connecting to the Aptos network
     * @param args.message - The message that was signed.
     * @param args.signature - The signature to verify, which must be an instance of Secp256k1Signature.
     * @returns A boolean indicating whether the signature is valid for the given message.
     * @group Implementation
     * @category Serialization
     */
    verifySignatureAsync(args: VerifySignatureAsyncArgs): Promise<boolean>;
    /**
     * Generates an authentication key from the public key using the Ed25519 scheme.
     * This function is essential for creating a secure authentication key that can be used for further cryptographic operations.
     *
     * @returns {AuthenticationKey} The generated authentication key.
     * @group Implementation
     * @category Serialization
     */
    authKey(): AuthenticationKey;
    /**
     * Convert the internal data representation to a Uint8Array.
     *
     * @returns Uint8Array representation of the data.
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    /**
     * Serializes the data into a byte array using the provided serializer.
     * This allows for the conversion of data into a format suitable for transmission or storage.
     *
     * @param serializer - The serializer instance used to perform the serialization.
     * @group Implementation
     * @category Serialization
     */
    serialize(serializer: Serializer): void;
    /**
     * Deserialize bytes into an Ed25519Signature object.
     * This function is used to convert serialized byte data into a usable Ed25519Signature instance.
     *
     * @param deserializer - The deserializer instance used to read the byte data.
     * @group Implementation
     * @category Serialization
     */
    static deserialize(deserializer: Deserializer): Ed25519PublicKey;
    /**
     * Determine if the provided public key is an instance of Ed25519PublicKey.
     *
     * @param publicKey - The public key to check.
     * @returns True if the public key is an instance of Ed25519PublicKey, otherwise false.
     * @deprecated use `instanceof Ed25519PublicKey` instead.
     * @group Implementation
     * @category Serialization
     */
    static isPublicKey(publicKey: AccountPublicKey): publicKey is Ed25519PublicKey;
    /**
     * Determines if the provided public key is a valid Ed25519 public key.
     * This function checks for the presence of the "key" property and verifies that its data length matches the expected length
     * for Ed25519 public keys.
     *
     * @param publicKey - The public key to validate.
     * @returns A boolean indicating whether the public key is a valid Ed25519 public key.
     * @group Implementation
     * @category Serialization
     */
    static isInstance(publicKey: PublicKey): publicKey is Ed25519PublicKey;
}
/**
 * Represents the private key of an Ed25519 key pair.
 * @group Implementation
 * @category Serialization
 */
declare class Ed25519PrivateKey extends Serializable implements PrivateKey {
    /**
     * Length of an Ed25519 private key
     * @group Implementation
     * @category Serialization
     */
    static readonly LENGTH: number;
    /**
     * The Ed25519 key seed to use for BIP-32 compatibility
     * See more {@link https://github.com/satoshilabs/slips/blob/master/slip-0010.md}
     * @group Implementation
     * @category Serialization
     */
    static readonly SLIP_0010_SEED = "ed25519 seed";
    /**
     * The Ed25519 signing key
     * @private
     * @group Implementation
     * @category Serialization
     */
    private readonly signingKey;
    /**
     * Create a new PrivateKey instance from a Uint8Array or String.
     *
     * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)
     *
     * @param hexInput HexInput (string or Uint8Array)
     * @param strict If true, private key must AIP-80 compliant.
     * @group Implementation
     * @category Serialization
     */
    constructor(hexInput: HexInput, strict?: boolean);
    /**
     * Generate a new random private key.
     *
     * @returns Ed25519PrivateKey A newly generated Ed25519 private key.
     * @group Implementation
     * @category Serialization
     */
    static generate(): Ed25519PrivateKey;
    /**
     * Derives a private key from a mnemonic seed phrase using a specified BIP44 path.
     * To derive multiple keys from the same phrase, change the path
     *
     * IMPORTANT: Ed25519 supports hardened derivation only, as it lacks a key homomorphism, making non-hardened derivation impossible.
     *
     * @param path - The BIP44 path used for key derivation.
     * @param mnemonics - The mnemonic seed phrase from which the key will be derived.
     * @throws Error if the provided path is not a valid hardened path.
     * @group Implementation
     * @category Serialization
     */
    static fromDerivationPath(path: string, mnemonics: string): Ed25519PrivateKey;
    /**
     * Derives a child private key from a given BIP44 path and seed.
     * A private inner function so we can separate from the main fromDerivationPath() method
     * to add tests to verify we create the keys correctly.
     *
     * @param path - The BIP44 path used for key derivation.
     * @param seed - The seed phrase created by the mnemonics, represented as a Uint8Array.
     * @param offset - The offset used for key derivation, defaults to HARDENED_OFFSET.
     * @returns An instance of Ed25519PrivateKey derived from the specified path and seed.
     * @group Implementation
     * @category Serialization
     */
    private static fromDerivationPathInner;
    /**
     * Derive the Ed25519PublicKey for this private key.
     *
     * @returns Ed25519PublicKey - The derived public key corresponding to the private key.
     * @group Implementation
     * @category Serialization
     */
    publicKey(): Ed25519PublicKey;
    /**
     * Sign the given message with the private key.
     * This function generates a digital signature for the specified message, ensuring its authenticity and integrity.
     *
     * @param message - A message as a string or Uint8Array in HexInput format.
     * @returns A digital signature for the provided message.
     * @group Implementation
     * @category Serialization
     */
    sign(message: HexInput): Ed25519Signature;
    /**
     * Get the private key in bytes (Uint8Array).
     *
     * @returns Uint8Array representation of the private key
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    /**
     * Get the private key as a hex string with the 0x prefix.
     *
     * @returns string representation of the private key.
     * @group Implementation
     * @category Serialization
     */
    toString(): string;
    /**
     * Get the private key as a hex string with the 0x prefix.
     *
     * @returns string representation of the private key.
     */
    toHexString(): string;
    /**
     * Get the private key as a AIP-80 compliant hex string.
     *
     * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)
     *
     * @returns AIP-80 compliant string representation of the private key.
     */
    toAIP80String(): string;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): Ed25519PrivateKey;
    /**
     * Determines if the provided private key is an instance of Ed25519PrivateKey.
     *
     * @param privateKey - The private key to check.
     * @returns A boolean indicating whether the private key is an Ed25519PrivateKey.
     *
     * @deprecated Use `instanceof Ed25519PrivateKey` instead.
     * @group Implementation
     * @category Serialization
     */
    static isPrivateKey(privateKey: PrivateKey): privateKey is Ed25519PrivateKey;
}
/**
 * Represents a signature of a message signed using an Ed25519 private key.
 * @group Implementation
 * @category Serialization
 */
declare class Ed25519Signature extends Signature {
    /**
     * Length of an Ed25519 signature, which is 64 bytes.
     * @group Implementation
     * @category Serialization
     */
    static readonly LENGTH = 64;
    /**
     * The signature bytes
     * @private
     * @group Implementation
     * @category Serialization
     */
    private readonly data;
    constructor(hexInput: HexInput);
    toUint8Array(): Uint8Array;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): Ed25519Signature;
}

export { Ed25519PrivateKey, Ed25519PublicKey, Ed25519Signature, isCanonicalEd25519Signature };
