import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import { GeneralException } from '@injectivelabs/exceptions';
/**
 * @category Messages
 */
export default class MsgReclaimLockedFunds extends MsgBase {
    static fromJSON(params) {
        return new MsgReclaimLockedFunds(params);
    }
    toProto() {
        const { params } = this;
        const message = InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds.create();
        message.sender = params.sender;
        message.lockedAccountPubKey = Buffer.from(params.lockedAccountPubKey, 'base64');
        message.signature = params.signature;
        return InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgReclaimLockedFunds',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            sender: proto.sender,
            lockedAccountPubKey: Buffer.from(proto.lockedAccountPubKey),
            signature: Buffer.from(proto.signature),
        };
        return {
            type: 'exchange/MsgReclaimLockedFunds',
            value: message,
        };
    }
    toWeb3Gw() {
        throw new GeneralException(new Error('EIP712 is not supported for MsgReclaimLockedFunds.'));
    }
    toEip712() {
        throw new GeneralException(new Error('EIP712 is not supported for MsgReclaimLockedFunds.'));
    }
    toEip712V2() {
        throw new GeneralException(new Error('EIP712 is not supported for MsgReclaimLockedFunds.'));
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgReclaimLockedFunds',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds.encode(this.toProto()).finish();
    }
}
