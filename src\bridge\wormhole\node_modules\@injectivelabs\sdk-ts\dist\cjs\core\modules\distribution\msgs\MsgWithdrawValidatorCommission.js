"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgWithdrawValidatorCommission extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgWithdrawValidatorCommission(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.CosmosDistributionV1Beta1Tx.MsgWithdrawValidatorCommission.create();
        message.validatorAddress = params.validatorAddress;
        return message;
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.distribution.v1beta1.MsgWithdrawValidatorCommission',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'cosmos-sdk/MsgWithdrawValCommission',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmos.distribution.v1beta1.MsgWithdrawValidatorCommission',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.distribution.v1beta1.MsgWithdrawValidatorCommission',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosDistributionV1Beta1Tx.MsgWithdrawValidatorCommission.encode(this.toProto()).finish();
    }
}
exports.default = MsgWithdrawValidatorCommission;
