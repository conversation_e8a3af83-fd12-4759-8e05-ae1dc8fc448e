{"version": 3, "file": "baseTransaction.js", "sourceRoot": "", "sources": ["../../src/baseTransaction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAClD,OAAO,EACL,OAAO,EACP,QAAQ,EACR,WAAW,EACX,UAAU,EACV,WAAW,EACX,aAAa,EACb,UAAU,EACV,MAAM,EACN,eAAe,EACf,OAAO,EACP,UAAU,GACX,MAAM,kBAAkB,CAAA;AAEzB,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,YAAY,CAAA;AACxD,OAAO,EAAE,oBAAoB,EAAE,MAAM,WAAW,CAAA;AAahD;;;;;;GAMG;AACH,MAAM,OAAgB,eAAe;IA0CnC,YAAY,MAAiB,EAAE,IAAe;QAzBvC,UAAK,GAAqB;YAC/B,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,SAAS;YAClB,YAAY,EAAE,SAAS;SACxB,CAAA;QAID;;;;WAIG;QACO,uBAAkB,GAAa,EAAE,CAAA;QAE3C;;;;;;;WAOG;QACO,kBAAa,GAAG,KAAK,CAAC,OAAO,CAAA;QAGrC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA;QAClE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAEjD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;QAErB,MAAM,GAAG,GAAG,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QAC1C,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QACrB,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QACrB,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QAErB,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;QAC1C,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;QAChD,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACvD,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;QAC1C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QAE9C,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACtD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACtD,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAEtD,IAAI,CAAC,+BAA+B,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAA;QAEjF,iCAAiC;QACjC,IAAI,CAAC,+BAA+B,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAA;QAErE,wDAAwD;QACxD,IAAI,CAAC,+BAA+B,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;QAErE,MAAM,cAAc,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAA;QAChE,MAAM,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,IAAI,KAAK,CAAA;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAA;QAC/C,IAAI,cAAc,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,0BAA0B,KAAK,KAAK,EAAE;YACzF,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAC/C;IACH,CAAC;IAED;;;;OAIG;IACH,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,CAAC,UAAsB;QAC7B,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;IACrD,CAAC;IAED;;;OAGG;IACH,mBAAmB;QACjB,MAAM,MAAM,GAAG,EAAE,CAAA;QAEjB,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;YAC9C,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;SACjC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;YACrC,MAAM,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,QAAQ,mBAAmB,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;SAC/F;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAEzC,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QAClD,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QAC3B,IAAI,KAAK;YAAE,GAAG,IAAI,KAAK,CAAA;QACvB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;YACpE,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;YAClE,IAAI,aAAa;gBAAE,GAAG,IAAI,aAAa,CAAA;SACxC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;OAEG;IACH,UAAU;QACR,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;QAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;QAErE,IAAI,IAAI,GAAG,QAAQ,CAAA;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,aAAa,CAAC,CAAA;SACpE;QAED,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACnF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAA;YAC3D,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,kBAAkB,CAAC,GAAG,UAAU,CAAA;YACpF,IAAI,IAAI,YAAY,CAAA;SACrB;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAcD;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAA;IAC5D,CAAC;IA6BM,QAAQ;QACb,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;YACzD,OAAO,KAAK,CAAA;SACb;aAAM;YACL,OAAO,IAAI,CAAA;SACZ;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI;YACF,gEAAgE;YAChE,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;YAC3C,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,CAAA;SAC1C;QAAC,OAAO,CAAM,EAAE;YACf,OAAO,KAAK,CAAA;SACb;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAA;IAChE,CAAC;IAOD;;;;;;;;OAQG;IACH,IAAI,CAAC,UAAsB;QACzB,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,yCAAyC,CAAC,CAAA;YACrE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,sHAAsH;QACtH,oEAAoE;QACpE,mFAAmF;QACnF,aAAa;QACb,IAAI,WAAW,GAAG,KAAK,CAAA;QACvB,IACE,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,MAAM;YACpC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACzC,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,sBAAsB,CAAC,EACjD;YACA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAA;YAC/D,WAAW,GAAG,IAAI,CAAA;SACnB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,IAAI,MAAM,CAAA;QACjE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QACvD,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;QAE3C,cAAc;QACd,IAAI,WAAW,EAAE;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAA;YAChF,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;aACzC;SACF;QAED,OAAO,EAAE,CAAA;IACX,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO;YACL,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SAC1D,CAAA;IACH,CAAC;IAkBD;;;;;;;OAOG;IACO,UAAU,CAAC,MAAe,EAAE,OAAoB;QACxD,oBAAoB;QACpB,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,MAAM,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;YACrD,IAAI,MAAM,EAAE;gBACV,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,aAAa,EAAE;oBACtC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,4DAA4D,aAAa,eAAe,MAAM,CAAC,OAAO,EAAE,EAAE,CAC3G,CAAA;oBACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;iBACrB;gBACD,uCAAuC;gBACvC,4BAA4B;gBAC5B,OAAO,MAAM,CAAC,IAAI,EAAE,CAAA;aACrB;iBAAM;gBACL,IAAI,MAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE;oBAC5C,0CAA0C;oBAC1C,sCAAsC;oBACtC,OAAO,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAA;iBAC5C;qBAAM;oBACL,8CAA8C;oBAC9C,0DAA0D;oBAC1D,OAAO,MAAM,CAAC,MAAM,CAClB;wBACE,IAAI,EAAE,cAAc;wBACpB,SAAS,EAAE,aAAa;wBACxB,OAAO,EAAE,aAAa;qBACvB,EACD,EAAE,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,CAClC,CAAA;iBACF;aACF;SACF;aAAM;YACL,uBAAuB;YACvB,yDAAyD;YACzD,OAAO,MAAM,EAAE,IAAI,EAAE,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAA;SACnE;IACH,CAAC;IAED;;;;;OAKG;IACO,+BAA+B,CACvC,MAA6C,EAC7C,IAAI,GAAG,GAAG,EACV,WAAW,GAAG,KAAK;QAEnB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACjD,QAAQ,IAAI,EAAE;gBACZ,KAAK,EAAE;oBACL,IAAI,WAAW,EAAE;wBACf,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,UAAU,EAAE;4BAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,GAAG,GAAG,sDAAsD,KAAK,EAAE,CACpE,CAAA;4BACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;yBACrB;qBACF;yBAAM;wBACL,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,UAAU,EAAE;4BAC7C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,6CAA6C,KAAK,EAAE,CAAC,CAAA;4BACtF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;yBACrB;qBACF;oBACD,MAAK;gBACP,KAAK,GAAG;oBACN,IAAI,WAAW,EAAE;wBACf,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,WAAW,EAAE;4BAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,GAAG,GAAG,wDAAwD,KAAK,EAAE,CACtE,CAAA;4BACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;yBACrB;qBACF;yBAAM;wBACL,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,WAAW,EAAE;4BAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,GAAG,GAAG,+CAA+C,KAAK,EAAE,CAC7D,CAAA;4BACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;yBACrB;qBACF;oBACD,MAAK;gBACP,OAAO,CAAC,CAAC;oBACP,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;oBACtD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;iBACrB;aACF;SACF;IACH,CAAC;IAES,MAAM,CAAC,iBAAiB,CAAC,MAA8B;QAC/D,MAAM,UAAU,GAAG;YACjB,OAAO;YACP,UAAU;YACV,UAAU;YACV,IAAI;YACJ,OAAO;YACP,MAAM;YACN,GAAG;YACH,GAAG;YACH,GAAG;YACH,MAAM;YACN,SAAS;YACT,cAAc;YACd,SAAS;SACV,CAAA;QACD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACjD,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,qBAAqB,CAAC,CAAA;iBAC7C;aACF;SACF;IACH,CAAC;IAeD;;;OAGG;IACO,sBAAsB;QAC9B,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IAAI;YACF,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAA;SAC9E;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,GAAG,OAAO,CAAA;SACf;QACD,IAAI,QAAQ,GAAG,EAAE,CAAA;QACjB,IAAI;YACF,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAA;SACtC;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,GAAG,OAAO,CAAA;SACf;QACD,IAAI,EAAE,GAAG,EAAE,CAAA;QACX,IAAI;YACF,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;SAC5B;QAAC,OAAO,CAAM,EAAE;YACf,EAAE,GAAG,OAAO,CAAA;SACb;QAED,IAAI,OAAO,GAAG,WAAW,IAAI,CAAC,IAAI,SAAS,IAAI,UAAU,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,GAAG,CAAA;QAC1F,OAAO,IAAI,UAAU,QAAQ,OAAO,EAAE,EAAE,CAAA;QAExC,OAAO,OAAO,CAAA;IAChB,CAAC;CACF"}