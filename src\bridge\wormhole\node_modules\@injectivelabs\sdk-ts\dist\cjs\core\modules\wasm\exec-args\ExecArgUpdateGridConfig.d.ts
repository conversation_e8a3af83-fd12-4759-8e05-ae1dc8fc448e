import { ExecArgBase, ExecDataRepresentation } from '../ExecArgBase.js';
export declare namespace ExecArgUpdateGridConfig {
    interface Params {
        maximumOrderValueDeviation?: string;
        maximumRebalanceRetries?: number;
        slippage?: string;
    }
    interface Data {
        maximum_order_value_deviation?: string;
        maximum_rebalance_retries?: number;
        slippage?: string;
    }
}
/**
 * @category Contract Exec Arguments
 */
export default class ExecArgUpdateGridConfig extends ExecArgBase<ExecArgUpdateGridConfig.Params, ExecArgUpdateGridConfig.Data> {
    static fromJSON(params: ExecArgUpdateGridConfig.Params): ExecArgUpdateGridConfig;
    toData(): ExecArgUpdateGridConfig.Data;
    toExecData(): ExecDataRepresentation<ExecArgUpdateGridConfig.Data>;
}
