{"version": 3, "file": "get_transaction_error.js", "sourceRoot": "", "sources": ["../../../src/utils/get_transaction_error.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;;AAmBF,kDA0DC;AA1ED,6CAIqB;AASrB,2CAA2C;AAC3C,iEAAgF;AAEhF,SAAsB,mBAAmB,CACxC,WAAwB,EACxB,oBAAsC,EACtC,2BAA0E,EAC1E,aAAuB,EACvB,WAAyB,EACzB,WAAiE;;QAEjE,IAAI,OAAO,GAAoE,WAAW,CAAC;QAE3F,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gBACjC,OAAO,GAAG,IAAA,4CAAqB,EAAC,aAAa,CAAC,CAAC;YAChD,CAAC;iBAAM,IAAI,WAAW,CAAC,YAAY,IAAI,oBAAoB,KAAK,SAAS,EAAE,CAAC;gBAC3E,OAAO,GAAG,MAAM,IAAA,sCAAe,EAAC,WAAW,EAAE,oBAAoB,EAAE,WAAW,CAAC,CAAC;YACjF,CAAC;QACF,CAAC;QAED,IAAI,KAG6E,CAAC;QAClF,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC3B,KAAK,GAAG,IAAI,mDAAqC,CAE/C,2BAA2B,CAAC,CAAC;QAChC,CAAC;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACxC,KAAK,GAAG,IAAI,+CAAiC,CAC5C,OAAO,EACP,SAAS,EACT,2BAA2B,CAC3B,CAAC;QACH,CAAC;aAAM,IACL,OAAuC,CAAC,eAAe,KAAK,SAAS;YACrE,OAAuC,CAAC,2BAA2B,KAAK,SAAS;YACjF,OAAuC,CAAC,oBAAoB,KAAK,SAAS,EAC1E,CAAC;YACF,MAAM,qBAAqB,GAC1B,OAAsC,CAAC;YACxC,KAAK,GAAG,IAAI,8CAAgC,CAC3C,qBAAqB,CAAC,MAAM,EAC5B,qBAAqB,CAAC,eAAe,EACrC,qBAAqB,CAAC,2BAA2B,EACjD,qBAAqB,CAAC,oBAAoB,EAC1C,qBAAqB,CAAC,SAAS,EAC/B,2BAA2B,EAC3B,qBAAqB,CAAC,IAAI,CAC1B,CAAC;QACH,CAAC;aAAM,CAAC;YACP,KAAK,GAAG,IAAI,+CAAiC,CAC5C,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,SAAS,EACjB,2BAA2B,EAC3B,OAAO,CAAC,IAAI,CACZ,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACd,CAAC;CAAA"}