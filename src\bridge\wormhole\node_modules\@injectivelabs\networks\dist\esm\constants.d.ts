export declare const CW20_ADAPTER_CONTRACT_BY_NETWORK: {
    mainnet: string;
    mainnetLB: string;
    mainnetOld: string;
    mainnetK8s: string;
    mainnetSentry: string;
    staging: string;
    internal: string;
    testnet: string;
    testnetK8s: string;
    testnetSentry: string;
    testnetOld: string;
    devnet: string;
    devnet1: string;
    devnet2: string;
    devnet3: string;
    local: string;
};
export declare const CW20_SWAP_CONTRACT_BY_NETWORK: {
    mainnet: string;
    mainnetLB: string;
    mainnetOld: string;
    mainnetK8s: string;
    mainnetSentry: string;
    staging: string;
    internal: string;
    testnet: string;
    testnetK8s: string;
    testnetSentry: string;
    testnetOld: string;
    devnet: string;
    devnet1: string;
    devnet2: string;
    devnet3: string;
    local: string;
};
export declare const INCENTIVES_CONTRACT_BY_NETWORK: {
    mainnet: string;
    mainnetLB: string;
    mainnetOld: string;
    mainnetK8s: string;
    mainnetSentry: string;
    staging: string;
    internal: string;
    testnet: string;
    testnetK8s: string;
    testnetSentry: string;
    testnetOld: string;
    devnet: string;
    devnet1: string;
    devnet2: string;
    devnet3: string;
    local: string;
};
export declare const INJ_NAME_REGISTRY_CONTRACT_BY_NETWORK: {
    mainnet: string;
    mainnetLB: string;
    mainnetK8s: string;
    mainnetSentry: string;
    mainnetOld: string;
    staging: string;
    internal: string;
    testnet: string;
    testnetK8s: string;
    testnetSentry: string;
    testnetOld: string;
    devnet: string;
    devnet1: string;
    devnet2: string;
    devnet3: string;
    local: string;
};
export declare const INJ_NAME_REVERSE_RESOLVER_CONTRACT_BY_NETWORK: {
    mainnet: string;
    mainnetLB: string;
    mainnetK8s: string;
    mainnetSentry: string;
    mainnetOld: string;
    staging: string;
    internal: string;
    testnet: string;
    testnetK8s: string;
    testnetSentry: string;
    testnetOld: string;
    devnet: string;
    devnet1: string;
    devnet2: string;
    devnet3: string;
    local: string;
};
export declare const PEGGY_GRAPH_URL_BY_NETWORK: {
    mainnet: string;
    mainnetLB: string;
    mainnetK8s: string;
    mainnetSentry: string;
    mainnetOld: string;
    staging: string;
    internal: string;
    testnet: string;
    testnetK8s: string;
    testnetSentry: string;
    testnetOld: string;
    devnet: string;
    devnet1: string;
    devnet2: string;
    devnet3: string;
    local: string;
};
export declare const ASSET_PRICE_URL_BY_NETWORK: {
    mainnet: string;
    mainnetLB: string;
    mainnetK8s: string;
    mainnetSentry: string;
    mainnetOld: string;
    staging: string;
    internal: string;
    testnet: string;
    testnetK8s: string;
    testnetSentry: string;
    testnetOld: string;
    devnet: string;
    devnet1: string;
    devnet2: string;
    devnet3: string;
    local: string;
};
