{"version": 3, "sources": ["../../src/types/types.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Network } from \"../utils/apiEndpoints\";\nimport { OrderBy, TokenStandard } from \"./indexer\";\n\n/**\n * Different MIME types used for data interchange in transactions and responses.\n */\nexport enum MimeType {\n  /**\n   * JSON representation, used for transaction submission and accept type JSON output\n   */\n  JSON = \"application/json\",\n  /**\n   * BCS representation, used for accept type BCS output\n   */\n  BCS = \"application/x-bcs\",\n  /**\n   * BCS representation, used for transaction submission in BCS input\n   */\n  BCS_SIGNED_TRANSACTION = \"application/x.aptos.signed_transaction+bcs\",\n  BCS_VIEW_FUNCTION = \"application/x.aptos.view_function+bcs\",\n}\n\n/**\n * Hexadecimal data input for functions, supporting both string and Uint8Array formats.\n */\nexport type HexInput = string | Uint8Array;\n\n/**\n * Variants of type tags used in the system, encompassing various data types and structures.\n * {@link https://github.com/aptos-labs/aptos-core/blob/main/third_party/move/move-core/types/src/language_storage.rs#L27}\n */\nexport enum TypeTagVariants {\n  Bool = 0,\n  U8 = 1,\n  U64 = 2,\n  U128 = 3,\n  Address = 4,\n  Signer = 5,\n  Vector = 6,\n  Struct = 7,\n  U16 = 8,\n  U32 = 9,\n  U256 = 10,\n  Reference = 254, // This is specifically a placeholder and does not represent a real type\n  Generic = 255, // This is specifically a placeholder and does not represent a real type\n}\n\n/**\n * Variants of script transaction arguments used in Rust, encompassing various data types for transaction processing.\n * {@link https://github.com/aptos-labs/aptos-core/blob/main/third_party/move/move-core/types/src/transaction_argument.rs#L11}\n */\nexport enum ScriptTransactionArgumentVariants {\n  U8 = 0,\n  U64 = 1,\n  U128 = 2,\n  Address = 3,\n  U8Vector = 4,\n  Bool = 5,\n  U16 = 6,\n  U32 = 7,\n  U256 = 8,\n  Serialized = 9,\n}\n\n/**\n * The payload for various transaction types in the system.\n * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/mod.rs#L478}\n */\nexport enum TransactionPayloadVariants {\n  Script = 0,\n  EntryFunction = 2,\n  Multisig = 3,\n}\n\n/**\n * Variants of transactions used in the system.\n * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/mod.rs#L440}\n */\nexport enum TransactionVariants {\n  MultiAgentTransaction = 0,\n  FeePayerTransaction = 1,\n}\n\n/**\n * Variants of transaction authenticators used in the system.\n * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/authenticator.rs#L44}\n */\nexport enum TransactionAuthenticatorVariant {\n  Ed25519 = 0,\n  MultiEd25519 = 1,\n  MultiAgent = 2,\n  FeePayer = 3,\n  SingleSender = 4,\n}\n\n/**\n * Variants of account authenticators used in transactions.\n * {@link https://github.com/aptos-labs/aptos-core/blob/main/types/src/transaction/authenticator.rs#L414}\n */\nexport enum AccountAuthenticatorVariant {\n  Ed25519 = 0,\n  MultiEd25519 = 1,\n  SingleKey = 2,\n  MultiKey = 3,\n  NoAccountAuthenticator = 4,\n  Abstraction = 5,\n}\n\n/**\n * Variants of private keys that can comply with the AIP-80 standard.\n * {@link https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md}\n */\nexport enum PrivateKeyVariants {\n  Ed25519 = \"ed25519\",\n  Secp256k1 = \"secp256k1\",\n}\n\n/**\n * Variants of public keys used in cryptographic operations.\n */\nexport enum AnyPublicKeyVariant {\n  Ed25519 = 0,\n  Secp256k1 = 1,\n  Keyless = 3,\n  FederatedKeyless = 4,\n}\n\n/**\n * Variants of signature types used for cryptographic operations.\n */\nexport enum AnySignatureVariant {\n  Ed25519 = 0,\n  Secp256k1 = 1,\n  Keyless = 3,\n}\n\n/**\n * Variants of ephemeral public keys used in cryptographic operations.\n */\nexport enum EphemeralPublicKeyVariant {\n  Ed25519 = 0,\n}\n\n/**\n * Variants of ephemeral signatures used for secure communication.\n */\nexport enum EphemeralSignatureVariant {\n  Ed25519 = 0,\n}\n\n/**\n * Variants of ephemeral certificates used in secure transactions.\n */\nexport enum EphemeralCertificateVariant {\n  ZkProof = 0,\n}\n\n/**\n * Variants of zero-knowledge proofs used in cryptographic operations.\n */\nexport enum ZkpVariant {\n  Groth16 = 0,\n}\n\n/**\n * BCS types\n */\nexport type Uint8 = number;\n\n/**\n * A 16-bit unsigned integer.\n */\nexport type Uint16 = number;\n\n/**\n * A 32-bit unsigned integer.\n */\nexport type Uint32 = number;\n\n/**\n * A 64-bit unsigned integer value.\n */\nexport type Uint64 = bigint;\n\n/**\n * A 128-bit unsigned integer used for precise arithmetic operations.\n */\nexport type Uint128 = bigint;\n\n/**\n * A 256-bit unsigned integer used for precise numerical calculations.\n */\nexport type Uint256 = bigint;\n\n/**\n * A number or a bigint value.\n */\nexport type AnyNumber = number | bigint;\n\n/**\n * Configuration options for initializing the SDK, allowing customization of its behavior and interaction with the Aptos network.\n */\nexport type AptosSettings = {\n  readonly network?: Network;\n\n  readonly fullnode?: string;\n\n  readonly faucet?: string;\n\n  readonly indexer?: string;\n\n  readonly pepper?: string;\n\n  readonly prover?: string;\n\n  readonly clientConfig?: ClientConfig;\n\n  readonly client?: Client;\n\n  readonly fullnodeConfig?: FullNodeConfig;\n\n  readonly indexerConfig?: IndexerConfig;\n\n  readonly faucetConfig?: FaucetConfig;\n};\n\n/**\n * Defines the parameters for paginating query results, including the starting position and maximum number of items to return.\n * @param offset Specifies the starting position of the query result. Default is 0.\n * @param limit Specifies the maximum number of items to return. Default is 25.\n */\nexport interface PaginationArgs {\n  offset?: AnyNumber;\n  limit?: number;\n}\n\n/**\n * Defines the parameters for paginating query results, including the starting position and maximum number of items to return.\n * @param cursor Specifies the starting position of the query result. Default is at the beginning if undefined.  This is not a number and must come from the API.\n * @param limit Specifies the maximum number of items to return. Default is 25.\n */\nexport interface CursorPaginationArgs {\n  cursor?: string;\n  limit?: number;\n}\n\n/**\n * Represents the arguments for specifying a token standard.\n *\n * @param tokenStandard - Optional standard of the token.\n */\nexport interface TokenStandardArg {\n  tokenStandard?: TokenStandard;\n}\n\nexport interface OrderByArg<T extends {}> {\n  orderBy?: OrderBy<T>;\n}\n\nexport interface WhereArg<T extends {}> {\n  where?: T;\n}\n\n/**\n * QUERY TYPES\n */\n\n/**\n * A configuration object for requests to the server, including API key, extra headers, and cookie handling options.\n */\nexport type ClientConfig = ClientHeadersType & {\n  WITH_CREDENTIALS?: boolean;\n  API_KEY?: string;\n};\n\n/**\n * A configuration object for a Fullnode, allowing for the inclusion of extra headers in requests.\n */\nexport type FullNodeConfig = ClientHeadersType;\n\n/**\n * An Indexer configuration object for sending requests with additional headers.\n */\nexport type IndexerConfig = ClientHeadersType;\n\n/**\n * A configuration object for a faucet, including optional authentication and headers for requests.\n */\nexport type FaucetConfig = ClientHeadersType & {\n  AUTH_TOKEN?: string;\n};\n\n/**\n * General type definition for client headers.\n */\nexport type ClientHeadersType = {\n  HEADERS?: Record<string, string | number | boolean>;\n};\n\n/**\n * Represents a client for making requests to a service provider.\n *\n * @param Req - The type of the request payload.\n * @param Res - The type of the response payload.\n */\nexport interface ClientRequest<Req> {\n  url: string;\n  method: \"GET\" | \"POST\";\n  originMethod?: string;\n  body?: Req;\n  contentType?: string;\n  params?: any;\n  overrides?: ClientConfig & FullNodeConfig & IndexerConfig & FaucetConfig;\n  headers?: Record<string, any>;\n}\n\nexport interface ClientResponse<Res> {\n  status: number;\n  statusText: string;\n  data: Res;\n  config?: any;\n  request?: any;\n  response?: any;\n  headers?: any;\n}\n\nexport interface Client {\n  /**\n   * Sends a request to the specified URL with the given options.\n   *\n   * @param requestOptions - The options for the request.\n   * @param requestOptions.url - The URL to send the request to.\n   * @param requestOptions.method - The HTTP method to use, either \"GET\" or \"POST\".\n   * @param requestOptions.path - An optional path to append to the URL.\n   * @param requestOptions.body - The body of the request, applicable for POST requests.\n   * @param requestOptions.contentType - The content type of the request body.\n   * @param requestOptions.acceptType - The expected content type of the response.\n   * @param requestOptions.params - Optional parameters to include in the request.\n   * @param requestOptions.originMethod - An optional method to specify the origin of the request.\n   * @param requestOptions.overrides - Optional configuration overrides for the request.\n   */\n  provider<Req, Res>(requestOptions: ClientRequest<Req>): Promise<ClientResponse<Res>>;\n}\n\n/**\n * The API request type\n *\n * @param url - the url to make the request to, i.e. https://fullnode.devnet.aptoslabs.com/v1\n * @param method - the request method \"GET\" | \"POST\"\n * @param endpoint (optional) - the endpoint to make the request to, i.e. transactions\n * @param body (optional) - the body of the request\n * @param contentType (optional) - the content type to set the `content-type` header to,\n * by default is set to `application/json`\n * @param params (optional) - query params to add to the request\n * @param originMethod (optional) - the local method the request came from\n * @param overrides (optional) - a `ClientConfig` object type to override request data\n */\nexport type AptosRequest = {\n  url: string;\n  method: \"GET\" | \"POST\";\n  path?: string;\n  body?: any;\n  contentType?: string;\n  acceptType?: string;\n  params?: Record<string, string | AnyNumber | boolean | undefined>;\n  originMethod?: string;\n  overrides?: ClientConfig & FullNodeConfig & IndexerConfig & FaucetConfig;\n};\n\n/**\n * The API response type\n *\n * @param status - the response status. i.e. 200\n * @param statusText - the response message\n * @param data the response data\n * @param url the url the request was made to\n * @param headers the response headers\n * @param config (optional) - the request object\n * @param request (optional) - the request object\n */\nexport interface AptosResponse<Req, Res> {\n  status: number;\n  statusText: string;\n  data: Res;\n  url: string;\n  headers: any;\n  config?: any;\n  request?: Req;\n}\n\n/**\n * The ledger version of transactions, defaulting to the latest version if not specified.\n */\nexport type LedgerVersionArg = {\n  ledgerVersion?: AnyNumber;\n};\n\n/**\n * RESPONSE TYPES\n */\n\n/**\n * The output of the estimate gas API, including the deprioritized estimate for the gas unit price.\n */\nexport type GasEstimation = {\n  /**\n   * The deprioritized estimate for the gas unit price\n   */\n  deprioritized_gas_estimate?: number;\n  /**\n   * The current estimate for the gas unit price\n   */\n  gas_estimate: number;\n  /**\n   * The prioritized estimate for the gas unit price\n   */\n  prioritized_gas_estimate?: number;\n};\n\nexport type MoveResource<T = {}> = {\n  type: MoveStructId;\n  data: T;\n};\n\n/**\n * The data associated with an account, including its sequence number.\n */\nexport type AccountData = {\n  sequence_number: string;\n  authentication_key: string;\n};\n\n/**\n * A Move module containing an address.\n */\nexport type MoveModuleBytecode = {\n  bytecode: string;\n  abi?: MoveModule;\n};\n\n/**\n * TRANSACTION TYPES\n */\n\n/**\n * Different types of transaction responses that can occur in the system.\n */\nexport enum TransactionResponseType {\n  Pending = \"pending_transaction\",\n  User = \"user_transaction\",\n  Genesis = \"genesis_transaction\",\n  BlockMetadata = \"block_metadata_transaction\",\n  StateCheckpoint = \"state_checkpoint_transaction\",\n  Validator = \"validator_transaction\",\n  BlockEpilogue = \"block_epilogue_transaction\",\n}\n\n/**\n * The response for a transaction, which can be either pending or committed.\n */\nexport type TransactionResponse = PendingTransactionResponse | CommittedTransactionResponse;\n\n/**\n * The response for a committed transaction, which can be one of several transaction types.\n */\nexport type CommittedTransactionResponse =\n  | UserTransactionResponse\n  | GenesisTransactionResponse\n  | BlockMetadataTransactionResponse\n  | StateCheckpointTransactionResponse\n  | ValidatorTransactionResponse\n  | BlockEpilogueTransactionResponse;\n\n/**\n * Determine if the given transaction response is currently pending.\n *\n * @param response - The transaction response to evaluate.\n * @returns A boolean indicating whether the transaction is pending.\n */\nexport function isPendingTransactionResponse(response: TransactionResponse): response is PendingTransactionResponse {\n  return response.type === TransactionResponseType.Pending;\n}\n\n/**\n * Determines if the given transaction response is a user transaction.\n *\n * @param response - The transaction response to evaluate.\n * @returns A boolean indicating whether the transaction is of type User.\n */\nexport function isUserTransactionResponse(response: TransactionResponse): response is UserTransactionResponse {\n  return response.type === TransactionResponseType.User;\n}\n\n/**\n * Determines if the given transaction response is a Genesis transaction.\n *\n * @param response - The transaction response to evaluate.\n * @returns A boolean indicating whether the transaction is a Genesis transaction.\n */\nexport function isGenesisTransactionResponse(response: TransactionResponse): response is GenesisTransactionResponse {\n  return response.type === TransactionResponseType.Genesis;\n}\n\n/**\n * Determine if the given transaction response is of type BlockMetadata.\n *\n * @param response - The transaction response to evaluate.\n * @returns A boolean indicating whether the response is a BlockMetadata transaction.\n */\nexport function isBlockMetadataTransactionResponse(\n  response: TransactionResponse,\n): response is BlockMetadataTransactionResponse {\n  return response.type === TransactionResponseType.BlockMetadata;\n}\n\n/**\n * Determines if the provided transaction response is a state checkpoint transaction.\n *\n * @param response - The transaction response to evaluate.\n * @returns A boolean indicating whether the transaction response is of type StateCheckpoint.\n */\nexport function isStateCheckpointTransactionResponse(\n  response: TransactionResponse,\n): response is StateCheckpointTransactionResponse {\n  return response.type === TransactionResponseType.StateCheckpoint;\n}\n\n/**\n * Determine if the given transaction response is of type Validator.\n *\n * @param response - The transaction response to evaluate.\n * @returns A boolean indicating whether the transaction response is a Validator type.\n */\nexport function isValidatorTransactionResponse(\n  response: TransactionResponse,\n): response is ValidatorTransactionResponse {\n  return response.type === TransactionResponseType.Validator;\n}\n\n/**\n * Determines if the given transaction response is of the type Block Epilogue.\n *\n * @param response - The transaction response to evaluate.\n * @returns A boolean indicating whether the response is a Block Epilogue transaction.\n */\nexport function isBlockEpilogueTransactionResponse(\n  response: TransactionResponse,\n): response is BlockEpilogueTransactionResponse {\n  return response.type === TransactionResponseType.BlockEpilogue;\n}\n\n/**\n * The response for a pending transaction, indicating that the transaction is still being processed.\n */\nexport type PendingTransactionResponse = {\n  type: TransactionResponseType.Pending;\n  hash: string;\n  sender: string;\n  sequence_number: string;\n  max_gas_amount: string;\n  gas_unit_price: string;\n  expiration_timestamp_secs: string;\n  payload: TransactionPayloadResponse;\n  signature?: TransactionSignature;\n};\n\n/**\n * The response structure for a user transaction.\n */\nexport type UserTransactionResponse = {\n  type: TransactionResponseType.User;\n  version: string;\n  hash: string;\n  state_change_hash: string;\n  event_root_hash: string;\n  state_checkpoint_hash: string | null;\n  gas_used: string;\n  /**\n   * Whether the transaction was successful\n   */\n  success: boolean;\n  /**\n   * The VM status of the transaction, can tell useful information in a failure\n   */\n  vm_status: string;\n  accumulator_root_hash: string;\n  /**\n   * Final state of resources changed by the transaction\n   */\n  changes: Array<WriteSetChange>;\n  sender: string;\n  sequence_number: string;\n  max_gas_amount: string;\n  gas_unit_price: string;\n  expiration_timestamp_secs: string;\n  payload: TransactionPayloadResponse;\n  signature?: TransactionSignature;\n  /**\n   * Events generated by the transaction\n   */\n  events: Array<Event>;\n  timestamp: string;\n};\n\n/**\n * The response for a genesis transaction, indicating the type of transaction.\n */\nexport type GenesisTransactionResponse = {\n  type: TransactionResponseType.Genesis;\n  version: string;\n  hash: string;\n  state_change_hash: string;\n  event_root_hash: string;\n  state_checkpoint_hash?: string;\n  gas_used: string;\n  /**\n   * Whether the transaction was successful\n   */\n  success: boolean;\n  /**\n   * The VM status of the transaction, can tell useful information in a failure\n   */\n  vm_status: string;\n  accumulator_root_hash: string;\n  /**\n   * Final state of resources changed by the transaction\n   */\n  changes: Array<WriteSetChange>;\n  payload: GenesisPayload;\n  /**\n   * Events emitted during genesis\n   */\n  events: Array<Event>;\n};\n\n/**\n * The structure representing a blockchain block with its height.\n */\nexport type BlockMetadataTransactionResponse = {\n  type: TransactionResponseType.BlockMetadata;\n  version: string;\n  hash: string;\n  state_change_hash: string;\n  event_root_hash: string;\n  state_checkpoint_hash: string | null;\n  gas_used: string;\n  /**\n   * Whether the transaction was successful\n   */\n  success: boolean;\n  /**\n   * The VM status of the transaction, can tell useful information in a failure\n   */\n  vm_status: string;\n  accumulator_root_hash: string;\n  /**\n   * Final state of resources changed by the transaction\n   */\n  changes: Array<WriteSetChange>;\n  id: string;\n  epoch: string;\n  round: string;\n  /**\n   * The events emitted at the block creation\n   */\n  events: Array<Event>;\n  /**\n   * Previous block votes\n   */\n  previous_block_votes_bitvec: Array<number>;\n  proposer: string;\n  /**\n   * The indices of the proposers who failed to propose\n   */\n  failed_proposer_indices: Array<number>;\n  timestamp: string;\n};\n\n/**\n * The response for a state checkpoint transaction, indicating the type of transaction.\n */\nexport type StateCheckpointTransactionResponse = {\n  type: TransactionResponseType.StateCheckpoint;\n  version: string;\n  hash: string;\n  state_change_hash: string;\n  event_root_hash: string;\n  state_checkpoint_hash: string | null;\n  gas_used: string;\n  /**\n   * Whether the transaction was successful\n   */\n  success: boolean;\n  /**\n   * The VM status of the transaction, can tell useful information in a failure\n   */\n  vm_status: string;\n  accumulator_root_hash: string;\n  /**\n   * Final state of resources changed by the transaction\n   */\n  changes: Array<WriteSetChange>;\n  timestamp: string;\n};\n\n/**\n * The response for a validator transaction, indicating the type of transaction.\n */\nexport type ValidatorTransactionResponse = {\n  type: TransactionResponseType.Validator;\n  version: string;\n  hash: string;\n  state_change_hash: string;\n  event_root_hash: string;\n  state_checkpoint_hash: string | null;\n  gas_used: string;\n  /**\n   * Whether the transaction was successful\n   */\n  success: boolean;\n  /**\n   * The VM status of the transaction, can tell useful information in a failure\n   */\n  vm_status: string;\n  accumulator_root_hash: string;\n  /**\n   * Final state of resources changed by the transaction\n   */\n  changes: Array<WriteSetChange>;\n  /**\n   * The events emitted by the validator transaction\n   */\n  events: Array<Event>;\n  timestamp: string;\n};\n\n/**\n * Describes the gas state of the block, indicating whether the block gas limit has been reached.\n */\nexport type BlockEndInfo = {\n  block_gas_limit_reached: boolean;\n  block_output_limit_reached: boolean;\n  block_effective_block_gas_units: number;\n  block_approx_output_size: number;\n};\n\n/**\n * A transaction executed at the end of a block that tracks data from the entire block.\n */\nexport type BlockEpilogueTransactionResponse = {\n  type: TransactionResponseType.BlockEpilogue;\n  version: string;\n  hash: string;\n  state_change_hash: string;\n  event_root_hash: string;\n  state_checkpoint_hash: string | null;\n  gas_used: string;\n  /**\n   * Whether the transaction was successful\n   */\n  success: boolean;\n  /**\n   * The VM status of the transaction, can tell useful information in a failure\n   */\n  vm_status: string;\n  accumulator_root_hash: string;\n  /**\n   * Final state of resources changed by the transaction\n   */\n  changes: Array<WriteSetChange>;\n  timestamp: string;\n  block_end_info: BlockEndInfo | null;\n};\n\n/**\n * WRITESET CHANGE TYPES\n */\n\n/**\n * A union type that encompasses both script and direct write sets for data operations.\n */\nexport type WriteSetChange =\n  | WriteSetChangeDeleteModule\n  | WriteSetChangeDeleteResource\n  | WriteSetChangeDeleteTableItem\n  | WriteSetChangeWriteModule\n  | WriteSetChangeWriteResource\n  | WriteSetChangeWriteTableItem;\n\n/**\n * The structure for a module deletion change in a write set.\n */\nexport type WriteSetChangeDeleteModule = {\n  type: string;\n  address: string;\n  /**\n   * State key hash\n   */\n  state_key_hash: string;\n  module: MoveModuleId;\n};\n\n/**\n * The payload for a resource deletion in a write set change.\n */\nexport type WriteSetChangeDeleteResource = {\n  type: string;\n  address: string;\n  state_key_hash: string;\n  resource: string;\n};\n\n/**\n * The payload for a write set change that deletes a table item.\n */\nexport type WriteSetChangeDeleteTableItem = {\n  type: string;\n  state_key_hash: string;\n  handle: string;\n  key: string;\n  data?: DeletedTableData;\n};\n\n/**\n * The structure for a write module change in a write set.\n */\nexport type WriteSetChangeWriteModule = {\n  type: string;\n  address: string;\n  state_key_hash: string;\n  data: MoveModuleBytecode;\n};\n\n/**\n * The resource associated with a write set change, identified by its type.\n */\nexport type WriteSetChangeWriteResource = {\n  type: string;\n  address: string;\n  state_key_hash: string;\n  data: MoveResource;\n};\n\n/**\n * The structure for a write operation on a table in a write set change.\n */\nexport type WriteSetChangeWriteTableItem = {\n  type: string;\n  state_key_hash: string;\n  handle: string;\n  key: string;\n  value: string;\n  data?: DecodedTableData;\n};\n\n/**\n * The decoded data for a table, including its key in JSON format.\n */\nexport type DecodedTableData = {\n  /**\n   * Key of table in JSON\n   */\n  key: any;\n  /**\n   * Type of key\n   */\n  key_type: string;\n  /**\n   * Value of table in JSON\n   */\n  value: any;\n  /**\n   * Type of value\n   */\n  value_type: string;\n};\n\n/**\n * Data for a deleted table entry.\n */\nexport type DeletedTableData = {\n  /**\n   * Deleted key\n   */\n  key: any;\n  /**\n   * Deleted key type\n   */\n  key_type: string;\n};\n\n/**\n * The payload for a transaction response, which can be an entry function, script, or multisig payload.\n */\nexport type TransactionPayloadResponse = EntryFunctionPayloadResponse | ScriptPayloadResponse | MultisigPayloadResponse;\n\n/**\n * The response payload for an entry function, containing the type of the entry.\n */\nexport type EntryFunctionPayloadResponse = {\n  type: string;\n  function: MoveFunctionId;\n  /**\n   * Type arguments of the function\n   */\n  type_arguments: Array<string>;\n  /**\n   * Arguments of the function\n   */\n  arguments: Array<any>;\n};\n\n/**\n * The payload for a script response, containing the type of the script.\n */\nexport type ScriptPayloadResponse = {\n  type: string;\n  code: MoveScriptBytecode;\n  /**\n   * Type arguments of the function\n   */\n  type_arguments: Array<string>;\n  /**\n   * Arguments of the function\n   */\n  arguments: Array<any>;\n};\n\n/**\n * The response payload for a multisig transaction, containing the type of the transaction.\n */\nexport type MultisigPayloadResponse = {\n  type: string;\n  multisig_address: string;\n  transaction_payload?: EntryFunctionPayloadResponse;\n};\n\n/**\n * The payload for the genesis block containing the type of the payload.\n */\nexport type GenesisPayload = {\n  type: string;\n  write_set: WriteSet;\n};\n\n/**\n * The bytecode for a Move script.\n */\nexport type MoveScriptBytecode = {\n  bytecode: string;\n  abi?: MoveFunction;\n};\n\n/**\n * JSON representations of transaction signatures returned from the node API.\n */\nexport type TransactionSignature =\n  | TransactionEd25519Signature\n  | TransactionSecp256k1Signature\n  | TransactionMultiEd25519Signature\n  | TransactionMultiAgentSignature\n  | TransactionFeePayerSignature;\n\n/**\n * Determine if the provided signature is an Ed25519 signature.\n * This function checks for the presence of the \"signature\" property\n * and verifies that its value is \"ed25519_signature\".\n *\n * @param signature - The transaction signature to be checked.\n * @returns A boolean indicating whether the signature is an Ed25519 signature.\n */\nexport function isEd25519Signature(signature: TransactionSignature): signature is TransactionFeePayerSignature {\n  return \"signature\" in signature && signature.signature === \"ed25519_signature\";\n}\n\n/**\n * Determine if the provided signature is a valid secp256k1 ECDSA signature.\n *\n * @param signature - The transaction signature to validate.\n * @returns A boolean indicating whether the signature is a secp256k1 ECDSA signature.\n */\nexport function isSecp256k1Signature(signature: TransactionSignature): signature is TransactionFeePayerSignature {\n  return \"signature\" in signature && signature.signature === \"secp256k1_ecdsa_signature\";\n}\n\n/**\n * Determine if the provided transaction signature is a multi-agent signature.\n *\n * @param signature - The transaction signature to evaluate.\n * @returns A boolean indicating whether the signature is a multi-agent signature.\n */\nexport function isMultiAgentSignature(signature: TransactionSignature): signature is TransactionMultiAgentSignature {\n  return signature.type === \"multi_agent_signature\";\n}\n\n/**\n * Determine if the provided signature is a fee payer signature.\n *\n * @param signature - The transaction signature to evaluate.\n * @returns A boolean indicating whether the signature is a fee payer signature.\n */\nexport function isFeePayerSignature(signature: TransactionSignature): signature is TransactionFeePayerSignature {\n  return signature.type === \"fee_payer_signature\";\n}\n\n/**\n * Determine if the provided signature is of type \"multi_ed25519_signature\".\n *\n * @param signature - The transaction signature to check.\n * @returns A boolean indicating whether the signature is a multi-ed25519 signature.\n */\nexport function isMultiEd25519Signature(\n  signature: TransactionSignature,\n): signature is TransactionMultiEd25519Signature {\n  return signature.type === \"multi_ed25519_signature\";\n}\n\n/**\n * The signature for a transaction using the Ed25519 algorithm.\n */\nexport type TransactionEd25519Signature = {\n  type: string;\n  public_key: string;\n  signature: \"ed25519_signature\";\n};\n\n/**\n * The structure for a Secp256k1 signature in a transaction.\n */\nexport type TransactionSecp256k1Signature = {\n  type: string;\n  public_key: string;\n  signature: \"secp256k1_ecdsa_signature\";\n};\n\n/**\n * The structure for a multi-signature transaction using Ed25519.\n */\nexport type TransactionMultiEd25519Signature = {\n  type: \"multi_ed25519_signature\";\n  /**\n   * The public keys for the Ed25519 signature\n   */\n  public_keys: Array<string>;\n  /**\n   * Signature associated with the public keys in the same order\n   */\n  signatures: Array<string>;\n  /**\n   * The number of signatures required for a successful transaction\n   */\n  threshold: number;\n  bitmap: string;\n};\n\n/**\n * The structure for a multi-agent signature in a transaction.\n */\nexport type TransactionMultiAgentSignature = {\n  type: \"multi_agent_signature\";\n  sender: AccountSignature;\n  /**\n   * The other involved parties' addresses\n   */\n  secondary_signer_addresses: Array<string>;\n  /**\n   * The associated signatures, in the same order as the secondary addresses\n   */\n  secondary_signers: Array<AccountSignature>;\n};\n\n/**\n * The signature of the fee payer in a transaction.\n */\nexport type TransactionFeePayerSignature = {\n  type: \"fee_payer_signature\";\n  sender: AccountSignature;\n  /**\n   * The other involved parties' addresses\n   */\n  secondary_signer_addresses: Array<string>;\n  /**\n   * The associated signatures, in the same order as the secondary addresses\n   */\n  secondary_signers: Array<AccountSignature>;\n  fee_payer_address: string;\n  fee_payer_signer: AccountSignature;\n};\n\n/**\n * The union of all single account signatures, including Ed25519, Secp256k1, and MultiEd25519 signatures.\n */\nexport type AccountSignature =\n  | TransactionEd25519Signature\n  | TransactionSecp256k1Signature\n  | TransactionMultiEd25519Signature;\n\nexport type WriteSet = ScriptWriteSet | DirectWriteSet;\n\n/**\n * The set of properties for writing scripts, including the type of script.\n */\nexport type ScriptWriteSet = {\n  type: string;\n  execute_as: string;\n  script: ScriptPayloadResponse;\n};\n\n/**\n * The set of direct write operations, identified by a type string.\n */\nexport type DirectWriteSet = {\n  type: string;\n  changes: Array<WriteSetChange>;\n  events: Array<Event>;\n};\n\n/**\n * The structure for an event's unique identifier, including its creation number.\n */\n\n/**\n * The structure for an event, identified by a unique GUID.\n */\nexport type EventGuid = {\n  creation_number: string;\n  account_address: string;\n};\n\nexport type Event = {\n  guid: EventGuid;\n  sequence_number: string;\n  type: string;\n  /**\n   * The JSON representation of the event\n   */\n  data: any;\n};\n\n/**\n * A number representing a Move uint8 type.\n */\nexport type MoveUint8Type = number;\n\n/**\n * A 16-bit unsigned integer used in the Move programming language.\n */\nexport type MoveUint16Type = number;\n\n/**\n * A 32-bit unsigned integer type used in Move programming.\n */\nexport type MoveUint32Type = number;\n\n/**\n * A string representation of a 64-bit unsigned integer used in Move programming.\n */\nexport type MoveUint64Type = string;\n\n/**\n * A string representing a 128-bit unsigned integer in the Move programming language.\n */\nexport type MoveUint128Type = string;\n\n/**\n * A string representation of a 256-bit unsigned integer used in Move programming.\n */\nexport type MoveUint256Type = string;\n\n/**\n * A string representing a Move address.\n */\nexport type MoveAddressType = string;\n\n/**\n * The type for identifying objects to be moved within the system.\n */\nexport type MoveObjectType = string;\n\n/**\n * The type for move options, which can be a MoveType, null, or undefined.\n */\nexport type MoveOptionType = MoveType | null | undefined;\n\n/**\n * A structure representing a move with a name.\n */\nexport type MoveStructId = `${string}::${string}::${string}`;\n\n/**\n * The move function containing its name. Same as MoveStructId since it reads weird to take a StructId for a Function.\n */\nexport type MoveFunctionId = MoveStructId;\n\n// TODO: Add support for looking up ABI to add proper typing\nexport type MoveStructType = {};\n\n/**\n * A union type that encompasses various data types used in Move, including primitive types, address types, object types, and\n * arrays of MoveType.\n */\nexport type MoveType =\n  | boolean\n  | string\n  | MoveUint8Type\n  | MoveUint16Type\n  | MoveUint32Type\n  | MoveUint64Type\n  | MoveUint128Type\n  | MoveUint256Type\n  | MoveAddressType\n  | MoveObjectType\n  | MoveStructType\n  | Array<MoveType>;\n\n/**\n * Possible Move values acceptable by move functions (entry, view)\n *\n * Map of a Move value to the corresponding TypeScript value\n *\n * `Bool -> boolean`\n *\n * `u8, u16, u32 -> number`\n *\n * `u64, u128, u256 -> string`\n *\n * `String -> string`\n *\n * `Address -> 0x${string}`\n *\n * `Struct - 0x${string}::${string}::${string}`\n *\n * `Object -> 0x${string}`\n *\n * `Vector -> Array<MoveValue>`\n *\n * `Option -> MoveValue | null | undefined`\n */\nexport type MoveValue =\n  | boolean\n  | string\n  | MoveUint8Type\n  | MoveUint16Type\n  | MoveUint32Type\n  | MoveUint64Type\n  | MoveUint128Type\n  | MoveUint256Type\n  | MoveAddressType\n  | MoveObjectType\n  | MoveStructId\n  | MoveOptionType\n  | Array<MoveValue>;\n\n/**\n * A string representation of a Move module, formatted as `module_name::function_name`.\n * Module names are case-sensitive.\n */\nexport type MoveModuleId = `${string}::${string}`;\n\n/**\n * Specifies the visibility levels for move functions, controlling access permissions.\n */\nexport enum MoveFunctionVisibility {\n  PRIVATE = \"private\",\n  PUBLIC = \"public\",\n  FRIEND = \"friend\",\n}\n\n/**\n * Abilities related to moving items within the system.\n */\nexport enum MoveAbility {\n  STORE = \"store\",\n  DROP = \"drop\",\n  KEY = \"key\",\n  COPY = \"copy\",\n}\n\n/**\n * Move abilities associated with the generic type parameter of a function.\n */\nexport type MoveFunctionGenericTypeParam = {\n  constraints: Array<MoveAbility>;\n};\n\n/**\n * A field in a Move struct, identified by its name.\n */\nexport type MoveStructField = {\n  name: string;\n  type: string;\n};\n\n/**\n * A Move module\n */\nexport type MoveModule = {\n  address: string;\n  name: string;\n  /**\n   * Friends of the module\n   */\n  friends: Array<MoveModuleId>;\n  /**\n   * Public functions of the module\n   */\n  exposed_functions: Array<MoveFunction>;\n  /**\n   * Structs of the module\n   */\n  structs: Array<MoveStruct>;\n};\n\n/**\n * A move struct\n */\nexport type MoveStruct = {\n  name: string;\n  /**\n   * Whether the struct is a native struct of Move\n   */\n  is_native: boolean;\n  /**\n   * Whether the struct is a module event (aka v2 event). This will be false for v1\n   * events because the value is derived from the #[event] attribute on the struct in\n   * the Move source code. This attribute is only relevant for v2 events.\n   */\n  is_event: boolean;\n  /**\n   * Abilities associated with the struct\n   */\n  abilities: Array<MoveAbility>;\n  /**\n   * Generic types associated with the struct\n   */\n  generic_type_params: Array<MoveFunctionGenericTypeParam>;\n  /**\n   * Fields associated with the struct\n   */\n  fields: Array<MoveStructField>;\n};\n\n/**\n * Move function\n */\nexport type MoveFunction = {\n  name: string;\n  visibility: MoveFunctionVisibility;\n  /**\n   * Whether the function can be called as an entry function directly in a transaction\n   */\n  is_entry: boolean;\n  /**\n   * Whether the function is a view function or not\n   */\n  is_view: boolean;\n  /**\n   * Generic type params associated with the Move function\n   */\n  generic_type_params: Array<MoveFunctionGenericTypeParam>;\n  /**\n   * Parameters associated with the move function\n   */\n  params: Array<string>;\n  /**\n   * Return type of the function\n   */\n  return: Array<string>;\n};\n\n/**\n * Roles that can be assigned within the system, indicating different levels of access and functionality.\n */\nexport enum RoleType {\n  VALIDATOR = \"validator\",\n  FULL_NODE = \"full_node\",\n}\n\n/**\n * Information about the current blockchain ledger, including its chain ID.\n */\nexport type LedgerInfo = {\n  /**\n   * Chain ID of the current chain\n   */\n  chain_id: number;\n  epoch: string;\n  ledger_version: string;\n  oldest_ledger_version: string;\n  ledger_timestamp: string;\n  node_role: RoleType;\n  oldest_block_height: string;\n  block_height: string;\n  /**\n   * Git hash of the build of the API endpoint.  Can be used to determine the exact\n   * software version used by the API endpoint.\n   */\n  git_hash?: string;\n};\n\n/**\n * A Block type\n */\nexport type Block = {\n  block_height: string;\n  block_hash: string;\n  block_timestamp: string;\n  first_version: string;\n  last_version: string;\n  /**\n   * The transactions in the block in sequential order\n   */\n  transactions?: Array<TransactionResponse>;\n};\n\n// REQUEST TYPES\n\n/**\n * The request payload for the GetTableItem API.\n */\nexport type TableItemRequest = {\n  key_type: MoveValue;\n  value_type: MoveValue;\n  /**\n   * The value of the table item's key\n   */\n  key: any;\n};\n\n/**\n * A list of supported Authentication Key schemes in Aptos, consisting of combinations of signing schemes and derive schemes.\n */\nexport type AuthenticationKeyScheme = SigningScheme | DeriveScheme;\n\n/**\n * Different schemes for signing keys used in cryptographic operations.\n */\nexport enum SigningScheme {\n  /**\n   * For Ed25519PublicKey\n   */\n  Ed25519 = 0,\n  /**\n   * For MultiEd25519PublicKey\n   */\n  MultiEd25519 = 1,\n  /**\n   * For SingleKey ecdsa\n   */\n  SingleKey = 2,\n\n  MultiKey = 3,\n}\n\n/**\n * Specifies the signing schemes available for cryptographic operations.\n */\nexport enum SigningSchemeInput {\n  /**\n   * For Ed25519PublicKey\n   */\n  Ed25519 = 0,\n  /**\n   * For Secp256k1Ecdsa\n   */\n  Secp256k1Ecdsa = 2,\n}\n\n/**\n * Specifies the schemes for deriving account addresses from various data sources.\n */\nexport enum DeriveScheme {\n  /**\n   * Derives an address using an AUID, used for objects\n   */\n  DeriveAuid = 251,\n  /**\n   * Derives an address from another object address\n   */\n  DeriveObjectAddressFromObject = 252,\n  /**\n   * Derives an address from a GUID, used for objects\n   */\n  DeriveObjectAddressFromGuid = 253,\n  /**\n   * Derives an address from seed bytes, used for named objects\n   */\n  DeriveObjectAddressFromSeed = 254,\n  /**\n   * Derives an address from seed bytes, used for resource accounts\n   */\n  DeriveResourceAccountAddress = 255,\n}\n\n/**\n * Options for configuring the behavior of the waitForTransaction() function.\n */\nexport type WaitForTransactionOptions = {\n  timeoutSecs?: number;\n  checkSuccess?: boolean;\n  // Default behavior is to wait for the indexer. Set this to false to disable waiting.\n  waitForIndexer?: boolean;\n};\n\n/**\n * Input type to generate an account using the Ed25519 signing scheme.\n */\nexport type GenerateAccountWithEd25519 = {\n  scheme: SigningSchemeInput.Ed25519;\n  legacy: boolean;\n};\n\n/**\n * Input type to generate an account with a Single Signer using Secp256k1.\n */\nexport type GenerateAccountWithSingleSignerSecp256k1Key = {\n  scheme: SigningSchemeInput.Secp256k1Ecdsa;\n  legacy?: false;\n};\n\nexport type GenerateAccount = GenerateAccountWithEd25519 | GenerateAccountWithSingleSignerSecp256k1Key;\n"], "mappings": "AASO,IAAKA,OAIVA,EAAA,KAAO,mBAIPA,EAAA,IAAM,oBAINA,EAAA,uBAAyB,6CACzBA,EAAA,kBAAoB,wCAbVA,OAAA,IAyBAC,OACVA,IAAA,KAAO,GAAP,OACAA,IAAA,GAAK,GAAL,KACAA,IAAA,IAAM,GAAN,MACAA,IAAA,KAAO,GAAP,OACAA,IAAA,QAAU,GAAV,UACAA,IAAA,OAAS,GAAT,SACAA,IAAA,OAAS,GAAT,SACAA,IAAA,OAAS,GAAT,SACAA,IAAA,IAAM,GAAN,MACAA,IAAA,IAAM,GAAN,MACAA,IAAA,KAAO,IAAP,OACAA,IAAA,UAAY,KAAZ,YACAA,IAAA,QAAU,KAAV,UAbUA,OAAA,IAoBAC,OACVA,IAAA,GAAK,GAAL,KACAA,IAAA,IAAM,GAAN,MACAA,IAAA,KAAO,GAAP,OACAA,IAAA,QAAU,GAAV,UACAA,IAAA,SAAW,GAAX,WACAA,IAAA,KAAO,GAAP,OACAA,IAAA,IAAM,GAAN,MACAA,IAAA,IAAM,GAAN,MACAA,IAAA,KAAO,GAAP,OACAA,IAAA,WAAa,GAAb,aAVUA,OAAA,IAiBAC,OACVA,IAAA,OAAS,GAAT,SACAA,IAAA,cAAgB,GAAhB,gBACAA,IAAA,SAAW,GAAX,WAHUA,OAAA,IAUAC,OACVA,IAAA,sBAAwB,GAAxB,wBACAA,IAAA,oBAAsB,GAAtB,sBAFUA,OAAA,IASAC,OACVA,IAAA,QAAU,GAAV,UACAA,IAAA,aAAe,GAAf,eACAA,IAAA,WAAa,GAAb,aACAA,IAAA,SAAW,GAAX,WACAA,IAAA,aAAe,GAAf,eALUA,OAAA,IAYAC,OACVA,IAAA,QAAU,GAAV,UACAA,IAAA,aAAe,GAAf,eACAA,IAAA,UAAY,GAAZ,YACAA,IAAA,SAAW,GAAX,WACAA,IAAA,uBAAyB,GAAzB,yBACAA,IAAA,YAAc,GAAd,cANUA,OAAA,IAaAC,OACVA,EAAA,QAAU,UACVA,EAAA,UAAY,YAFFA,OAAA,IAQAC,OACVA,IAAA,QAAU,GAAV,UACAA,IAAA,UAAY,GAAZ,YACAA,IAAA,QAAU,GAAV,UACAA,IAAA,iBAAmB,GAAnB,mBAJUA,OAAA,IAUAC,OACVA,IAAA,QAAU,GAAV,UACAA,IAAA,UAAY,GAAZ,YACAA,IAAA,QAAU,GAAV,UAHUA,OAAA,IASAC,OACVA,IAAA,QAAU,GAAV,UADUA,OAAA,IAOAC,OACVA,IAAA,QAAU,GAAV,UADUA,OAAA,IAOAC,OACVA,IAAA,QAAU,GAAV,UADUA,OAAA,IAOAC,OACVA,IAAA,QAAU,GAAV,UADUA,OAAA,IA+RAC,OACVA,EAAA,QAAU,sBACVA,EAAA,KAAO,mBACPA,EAAA,QAAU,sBACVA,EAAA,cAAgB,6BAChBA,EAAA,gBAAkB,+BAClBA,EAAA,UAAY,wBACZA,EAAA,cAAgB,6BAPNA,OAAA,IAgCL,SAASC,EAA6BC,EAAuE,CAClH,OAAOA,EAAS,OAAS,qBAC3B,CAQO,SAASC,EAA0BD,EAAoE,CAC5G,OAAOA,EAAS,OAAS,kBAC3B,CAQO,SAASE,EAA6BF,EAAuE,CAClH,OAAOA,EAAS,OAAS,qBAC3B,CAQO,SAASG,EACdH,EAC8C,CAC9C,OAAOA,EAAS,OAAS,4BAC3B,CAQO,SAASI,EACdJ,EACgD,CAChD,OAAOA,EAAS,OAAS,8BAC3B,CAQO,SAASK,EACdL,EAC0C,CAC1C,OAAOA,EAAS,OAAS,uBAC3B,CAQO,SAASM,EACdN,EAC8C,CAC9C,OAAOA,EAAS,OAAS,4BAC3B,CAsaO,SAASO,EAAmBC,EAA4E,CAC7G,MAAO,cAAeA,GAAaA,EAAU,YAAc,mBAC7D,CAQO,SAASC,EAAqBD,EAA4E,CAC/G,MAAO,cAAeA,GAAaA,EAAU,YAAc,2BAC7D,CAQO,SAASE,EAAsBF,EAA8E,CAClH,OAAOA,EAAU,OAAS,uBAC5B,CAQO,SAASG,EAAoBH,EAA4E,CAC9G,OAAOA,EAAU,OAAS,qBAC5B,CAQO,SAASI,EACdJ,EAC+C,CAC/C,OAAOA,EAAU,OAAS,yBAC5B,CAuPO,IAAKK,OACVA,EAAA,QAAU,UACVA,EAAA,OAAS,SACTA,EAAA,OAAS,SAHCA,OAAA,IASAC,OACVA,EAAA,MAAQ,QACRA,EAAA,KAAO,OACPA,EAAA,IAAM,MACNA,EAAA,KAAO,OAJGA,OAAA,IAsGAC,OACVA,EAAA,UAAY,YACZA,EAAA,UAAY,YAFFA,OAAA,IAgEAC,OAIVA,IAAA,QAAU,GAAV,UAIAA,IAAA,aAAe,GAAf,eAIAA,IAAA,UAAY,GAAZ,YAEAA,IAAA,SAAW,GAAX,WAdUA,OAAA,IAoBAC,OAIVA,IAAA,QAAU,GAAV,UAIAA,IAAA,eAAiB,GAAjB,iBARUA,OAAA,IAcAC,OAIVA,IAAA,WAAa,KAAb,aAIAA,IAAA,8BAAgC,KAAhC,gCAIAA,IAAA,4BAA8B,KAA9B,8BAIAA,IAAA,4BAA8B,KAA9B,8BAIAA,IAAA,6BAA+B,KAA/B,+BApBUA,OAAA", "names": ["MimeType", "TypeTagVariants", "ScriptTransactionArgumentVariants", "TransactionPayloadVariants", "TransactionVariants", "TransactionAuthenticatorVariant", "AccountAuthenticator<PERSON><PERSON>t", "PrivateKeyVariants", "AnyPublicKeyVariant", "AnySignatureVariant", "EphemeralPublicKeyVariant", "EphemeralSignatureVariant", "EphemeralCertificateVariant", "ZkpVariant", "TransactionResponseType", "isPendingTransactionResponse", "response", "isUserTransactionResponse", "isGenesisTransactionResponse", "isBlockMetadataTransactionResponse", "isStateCheckpointTransactionResponse", "isValidatorTransactionResponse", "isBlockEpilogueTransactionResponse", "isEd25519Signature", "signature", "isSecp256k1Signature", "isMultiAgentSignature", "isFeePayerSignature", "isMultiEd25519Signature", "MoveFunctionVisibility", "MoveAbility", "RoleType", "SigningScheme", "SigningSchemeInput", "DeriveScheme"]}