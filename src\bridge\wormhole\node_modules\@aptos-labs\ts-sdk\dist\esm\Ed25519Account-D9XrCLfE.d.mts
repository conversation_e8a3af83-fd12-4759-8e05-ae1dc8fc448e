import { AccountAuthent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AccountAuthenticator, AccountAuthenticatorEd25519 } from './transactions/authenticator/account.mjs';
import { SigningScheme, SigningSchemeInput, HexInput } from './types/types.mjs';
import { AccountAddress, AccountAddressInput } from './core/accountAddress.mjs';
import { b as AccountPublicKey, A as AuthenticationKey, V as VerifySignatureArgs } from './publicKey-CJOcUwJK.mjs';
import { Signature } from './core/crypto/signature.mjs';
import { Ed25519PrivateKey, Ed25519Signature, Ed25519PublicKey } from './core/crypto/ed25519.mjs';
import { AnyRawTransaction } from './transactions/types.mjs';
import { AptosConfig } from './api/aptosConfig.mjs';
import { AnyPublicKey, PrivateKeyInput, AnySignature } from './core/crypto/singleKey.mjs';

/**
 * An interface which defines if an Account utilizes SingleKey signing.
 *
 * Such an account will use the AnyPublicKey enum to represent its public key when deriving the auth key.
 */
interface SingleKeySigner extends Account {
    getAnyPublicKey(): AnyPublicKey;
}
declare function isSingleKeySigner(obj: unknown): obj is SingleKeySigner;
type SingleKeySignerOrLegacyEd25519Account = SingleKeySigner | Ed25519Account;
/**
 * Arguments required to create a single key signer.
 *
 * @param privateKey - The private key used for signing.
 * @param address - Optional account address associated with the signer.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface SingleKeySignerConstructorArgs {
    privateKey: PrivateKeyInput;
    address?: AccountAddressInput;
}
/**
 * Arguments for generating a single key signer.
 *
 * @param scheme - The signing scheme to be used.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface SingleKeySignerGenerateArgs {
    scheme?: SigningSchemeInput;
}
/**
 * The arguments for generating a single key signer from a specified derivation path.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
type SingleKeySignerFromDerivationPathArgs = SingleKeySignerGenerateArgs & {
    path: string;
    mnemonic: string;
};
/**
 * Arguments required to verify a single key signature for a given message.
 *
 * @param message - The message to be verified, represented in hexadecimal format.
 * @param signature - The signature that corresponds to the message.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface VerifySingleKeySignatureArgs {
    message: HexInput;
    signature: AnySignature;
}
/**
 * Signer implementation for the SingleKey authentication scheme.
 * This class extends a SingleKeyAccount by adding signing capabilities through a valid private key.
 * Currently, the only supported signature schemes are Ed25519 and Secp256k1.
 *
 * Note: Generating a signer instance does not create the account on-chain.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
declare class SingleKeyAccount implements Account, SingleKeySigner {
    /**
     * Private key associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly privateKey: PrivateKeyInput;
    readonly publicKey: AnyPublicKey;
    readonly accountAddress: AccountAddress;
    readonly signingScheme = SigningScheme.SingleKey;
    /**
     * Creates an instance of the SingleKeySigner using the provided private key and address.
     * This allows for signing transactions and messages with the specified private key.
     *
     * @param args - The constructor arguments for initializing the SingleKeySigner.
     * @param args.privateKey - The private key used for signing.
     * @param args.address - The optional account address; if not provided, it will derive the address from the public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    constructor(args: SingleKeySignerConstructorArgs);
    getAnyPublicKey(): AnyPublicKey;
    /**
     * Derives an account from a randomly generated private key based on the specified signing scheme.
     * The default generation scheme is Ed25519, but it can also support Secp256k1Ecdsa.
     *
     * @param args - The arguments for generating the account.
     * @param args.scheme - The signing scheme to use for generating the private key. Defaults to SigningSchemeInput.Ed25519.
     * @returns An account with the generated private key based on the specified signing scheme.
     * @throws Error if an unsupported signature scheme is provided.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static generate(args?: SingleKeySignerGenerateArgs): SingleKeyAccount;
    /**
     * Derives an account using a specified BIP44 path and mnemonic seed phrase, defaulting to the Ed25519 signature scheme.
     * This function allows you to create a single key account based on the provided derivation path and mnemonic.
     *
     * @param args - The arguments for deriving the account.
     * @param args.scheme - The signature scheme to derive the private key with. Defaults to Ed25519.
     * @param args.path - The BIP44 derive hardened path (e.g. m/44'/637'/0'/0'/0') for Ed25519, or non-hardened path
     * (e.g. m/44'/637'/0'/0/0) for secp256k1.
     * Detailed description: {@link https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki}
     * @param args.mnemonic - The mnemonic seed phrase of the account.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static fromDerivationPath(args: SingleKeySignerFromDerivationPathArgs): SingleKeyAccount;
    /**
     * Verify the given message and signature with the public key.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.message - The raw message data in HexInput format.
     * @param args.signature - The signed message signature.
     * @returns A boolean indicating whether the signature is valid.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignature(args: VerifySingleKeySignatureArgs): boolean;
    /**
     * Verify the given message and signature with the account's public key.
     *
     * This function checks if the provided signature is valid for the given message using the account's public key.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.message - The raw message data in HexInput format.
     * @param args.signature - The signed message signature.
     * @param args.options.throwErrorWithReason - Whether to throw an error with the reason for the verification failure.
     * @returns A boolean indicating whether the signature is valid for the message.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: Signature;
        options?: {
            throwErrorWithReason?: boolean;
        };
    }): Promise<boolean>;
    /**
     * Sign a message using the account's private key and return an AccountAuthenticator containing the signature along with the
     * account's public key.
     * @param message - The signing message, represented as binary input in hexadecimal format.
     * @returns An instance of AccountAuthenticatorSingleKey containing the signature and the public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signWithAuthenticator(message: HexInput): AccountAuthenticatorSingleKey;
    /**
     * Sign a transaction using the account's private key.
     * This function returns an AccountAuthenticator that contains the signature of the transaction along with the account's public key.
     * @param transaction - The raw transaction to be signed.
     * @returns An AccountAuthenticatorSingleKey containing the signature of the transaction and the account's public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorSingleKey;
    /**
     * Sign the given message using the account's private key.
     * @param message - The message to be signed in HexInput format.
     * @returns A new AnySignature containing the signature of the message.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    sign(message: HexInput): AnySignature;
    /**
     * Sign the given transaction using the account's private key.
     * This function generates a signing message for the transaction and then signs it.
     *
     * @param transaction - The transaction to be signed.
     * @returns Signature - The resulting signature for the signed transaction.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransaction(transaction: AnyRawTransaction): AnySignature;
    static fromEd25519Account(account: Ed25519Account): SingleKeyAccount;
}

/**
 * Arguments for creating an `Ed25519Account` from an `Ed25519PrivateKey`.
 * To use the SingleKey authentication scheme, set `legacy` to false.
 *
 * @param privateKey - The private key used to create the account.
 * @param address - Optional address for the account.
 * @param legacy - Indicates whether to use legacy authentication (default is true).
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface CreateEd25519AccountFromPrivateKeyArgs {
    privateKey: Ed25519PrivateKey;
    address?: AccountAddressInput;
    legacy?: true;
}
/**
 * Arguments for creating a `SingleKeyAccount` using an `Ed25519PrivateKey`.
 * The `legacy` property must be set to false to utilize the `SingleKey` authentication scheme.
 *
 * @param privateKey - The Ed25519 private key used for account creation.
 * @param address - Optional account address input.
 * @param legacy - Must be false to enable the `SingleKey` authentication scheme.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface CreateEd25519SingleKeyAccountFromPrivateKeyArgs {
    privateKey: Ed25519PrivateKey;
    address?: AccountAddressInput;
    legacy: false;
}
/**
 * Arguments for creating a `SingleKeyAccount` from a supported private key, excluding `Ed25519PrivateKey`.
 * The `legacy` argument is always false and cannot be set to true.
 *
 * @param privateKey - The private key used to create the account.
 * @param address - Optional address input for the account.
 * @param legacy - Always false; cannot be explicitly set to true.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface CreateSingleKeyAccountFromPrivateKeyArgs {
    privateKey: PrivateKeyInput;
    address?: AccountAddressInput;
    legacy?: false;
}
/**
 * Arguments for creating an `Account` from a private key when the key type is unknown at compile time.
 *
 * @param privateKey - The private key used to create the account.
 * @param address - Optional address for the account.
 * @param legacy - Optional flag indicating if the account is a legacy account.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface CreateAccountFromPrivateKeyArgs {
    privateKey: PrivateKeyInput;
    address?: AccountAddressInput;
    legacy?: boolean;
}
/**
 * Arguments for generating an Ed25519 account, specifying the signing scheme and legacy option.
 *
 * @param scheme - The signing scheme to use for the account.
 * @param legacy - Indicates if the account should be created in legacy mode.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface GenerateEd25519AccountArgs {
    scheme?: SigningSchemeInput.Ed25519;
    legacy?: true;
}
/**
 * Arguments for generating a `SingleKeyAccount` with an underlying `Ed25519PrivateKey`.
 * The `legacy` argument must be set to false to ensure an `Ed25519SingleKeyAccount` is returned.
 *
 * @param scheme - Optional signing scheme input for the account.
 * @param legacy - Indicates whether to use legacy account generation.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface GenerateEd25519SingleKeyAccountArgs {
    scheme?: SigningSchemeInput.Ed25519;
    legacy: false;
}
/**
 * Arguments for generating a `SingleKeyAccount` using a supported private key other than `Ed25519PrivateKey`.
 * The `legacy` argument is optional and defaults to false, and cannot be set to true.
 *
 * @param scheme - The signing scheme to use for the account.
 * @param legacy - Indicates whether to use legacy account generation (defaults to false).
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface GenerateSingleKeyAccountArgs {
    scheme: Exclude<SigningSchemeInput, SigningSchemeInput.Ed25519>;
    legacy?: false;
}
/**
 * Arguments for generating an opaque `Account` when the input signature scheme is unknown at compile time.
 *
 * @param scheme - The signing scheme to use for account generation.
 * @param legacy - Indicates whether to use legacy account generation methods.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface GenerateAccountArgs {
    scheme?: SigningSchemeInput;
    legacy?: boolean;
}
/**
 * Arguments for deriving a private key using a mnemonic phrase and a specified BIP44 path.
 *
 * @param path - The BIP44 derivation path for the key.
 * @param mnemonic - The mnemonic phrase used for key generation.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface PrivateKeyFromDerivationPathArgs {
    path: string;
    mnemonic: string;
}
/**
 * Abstract class representing a generic Aptos account.
 *
 * This class serves as a single entry point for account generation, allowing accounts to be created
 * either through `Account.generate()` or `Account.fromDerivationPath`. Although it is defined as an
 * abstract class, it should be treated as an interface and enforced using the `implements` keyword.
 *
 * Note: Generating an account instance does not create the account on-chain.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
declare abstract class Account {
    /**
     * Public key associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    abstract readonly publicKey: AccountPublicKey;
    /**
     * Account address associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    abstract readonly accountAddress: AccountAddress;
    /**
     * Signing scheme used to sign transactions
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    abstract signingScheme: SigningScheme;
    /**
     * Generates a new account based on the specified signing scheme and legacy option.
     * This function allows you to create an account with either the Ed25519 signing scheme or a different scheme as specified.
     *
     * @param args - The arguments for generating the account.
     * @param args.scheme - The signing scheme to use for account generation. Defaults to Ed25519.
     * @param args.legacy - Indicates whether to use the legacy account generation method. Defaults to true.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static generate(args?: GenerateEd25519AccountArgs): Ed25519Account;
    static generate(args: GenerateEd25519SingleKeyAccountArgs): SingleKeyAccount;
    static generate(args: GenerateSingleKeyAccountArgs): SingleKeyAccount;
    static generate(args: GenerateAccountArgs): Account;
    /**
     * Creates an account from a given private key and address.
     * This function allows you to instantiate an account based on the provided private key,
     * and it can differentiate between legacy and non-legacy accounts.
     *
     * @param args - The arguments for creating the account.
     * @param args.privateKey - The private key used to create the account.
     * @param args.address - The address associated with the account.
     * @param args.legacy - A boolean indicating whether to create a legacy account (default is true).
     * @returns An instance of either Ed25519Account or SingleKeyAccount based on the provided private key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static fromPrivateKey(args: CreateEd25519AccountFromPrivateKeyArgs): Ed25519Account;
    static fromPrivateKey(args: CreateSingleKeyAccountFromPrivateKeyArgs): SingleKeyAccount;
    static fromPrivateKey(args: CreateAccountFromPrivateKeyArgs): SingleKeyAccount;
    /**
     * @deprecated use `fromPrivateKey` instead.
     * Instantiates an account using a private key and a specified account address. This is primarily used to instantiate an
     * `Account` that has had its authentication key rotated.
     *
     * @param args - The arguments required to create an account from a private key.
     * @param args.privateKey - The underlying private key for the account.
     * @param args.address - The account address the `Account` will sign for.
     * @param args.legacy - Optional. If set to false, the keypair generated is a Unified keypair. Defaults to generating a Legacy
     * Ed25519 keypair.
     *
     * @returns Account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static fromPrivateKeyAndAddress(args: CreateAccountFromPrivateKeyArgs): SingleKeyAccount;
    /**
     * Generates an account from a specified derivation path and mnemonic.
     * This function allows you to create an account using different signing schemes based on the provided arguments.
     *
     * @param args - The arguments for generating the account.
     * @param args.scheme - The signing scheme to use for account generation. Defaults to Ed25519.
     * @param args.mnemonic - The mnemonic phrase used to derive the account.
     * @param args.path - The derivation path used to generate the account.
     * @param args.legacy - A boolean indicating whether to use the legacy account generation method. Defaults to true.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static fromDerivationPath(args: GenerateEd25519AccountArgs & PrivateKeyFromDerivationPathArgs): Ed25519Account;
    static fromDerivationPath(args: GenerateEd25519SingleKeyAccountArgs & PrivateKeyFromDerivationPathArgs): SingleKeyAccount;
    static fromDerivationPath(args: GenerateSingleKeyAccountArgs & PrivateKeyFromDerivationPathArgs): SingleKeyAccount;
    static fromDerivationPath(args: GenerateAccountArgs & PrivateKeyFromDerivationPathArgs): Account;
    /**
     * Retrieve the authentication key for the associated account using the provided public key.
     * This key enables account owners to rotate their private key(s) associated with the account without changing the address that
     * hosts their account.
     * See here for more info: {@link https://aptos.dev/concepts/accounts#single-signer-authentication}
     *
     * @param args - The arguments for retrieving the authentication key.
     * @param args.publicKey - The public key of the account.
     * @returns The authentication key for the associated account.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static authKey(args: {
        publicKey: AccountPublicKey;
    }): AuthenticationKey;
    /**
     * Sign a message using the available signing capabilities.
     * @param message the signing message, as binary input
     * @return the AccountAuthenticator containing the signature, together with the account's public key
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    abstract signWithAuthenticator(message: HexInput): AccountAuthenticator;
    /**
     * Sign a transaction using the available signing capabilities.
     * @param transaction the raw transaction
     * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    abstract signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticator;
    /**
     * Sign the given message using the available signing capabilities.
     * @param message in HexInput format
     * @returns Signature
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    abstract sign(message: HexInput): Signature;
    /**
     * Sign the given transaction using the available signing capabilities.
     * @param transaction the transaction to be signed
     * @returns Signature
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    abstract signTransaction(transaction: AnyRawTransaction): Signature;
    /**
     * Verify the given message and signature with the public key.
     * This function helps ensure the integrity and authenticity of a message by validating its signature.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.message - The raw message data in HexInput format.
     * @param args.signature - The signed message signature.
     * @returns A boolean indicating whether the signature is valid.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignature(args: VerifySignatureArgs): boolean;
    /**
     * Verify the given message and signature with the public key. It fetches any on chain state if needed for verification.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.aptosConfig - The configuration object for connecting to the Aptos network
     * @param args.message - Raw message data in HexInput format.
     * @param args.signature - Signed message signature.
     * @returns A boolean indicating whether the signature is valid.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: Signature;
    }): Promise<boolean>;
}

/**
 * Arguments required to create an instance of an Ed25519 signer.
 *
 * @param privateKey - The private key used for signing.
 * @param address - Optional account address associated with the signer.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface Ed25519SignerConstructorArgs {
    privateKey: Ed25519PrivateKey;
    address?: AccountAddressInput;
}
/**
 * Arguments for creating an Ed25519 signer from a derivation path.
 *
 * @param path - The derivation path for the Ed25519 key.
 * @param mnemonic - The mnemonic phrase used to generate the key.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface Ed25519SignerFromDerivationPathArgs {
    path: string;
    mnemonic: string;
}
/**
 * Arguments required to verify an Ed25519 signature against a given message.
 *
 * @param message - The message to be verified, represented in hexadecimal format.
 * @param signature - The Ed25519 signature to validate.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface VerifyEd25519SignatureArgs {
    message: HexInput;
    signature: Ed25519Signature;
}
/**
 * Represents an Ed25519 account that provides signing capabilities through an Ed25519 private key.
 * This class allows for the creation of accounts, signing messages and transactions, and verifying signatures.
 *
 * Note: Generating an instance of this class does not create the account on-chain.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
declare class Ed25519Account implements Account {
    /**
     * Private key associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly privateKey: Ed25519PrivateKey;
    readonly publicKey: Ed25519PublicKey;
    readonly accountAddress: AccountAddress;
    readonly signingScheme = SigningScheme.Ed25519;
    /**
     * Creates an instance of the Ed25519Signer with the specified parameters.
     * This constructor initializes the private key, public key, and account address for the signer.
     *
     * @param args - The constructor arguments for the Ed25519Signer.
     * @param args.privateKey - The private key used for signing.
     * @param args.address - The optional account address; if not provided, it will derive the address from the public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    constructor(args: Ed25519SignerConstructorArgs);
    /**
     * Generates a new Ed25519 account using a randomly generated private key.
     * This function is useful for creating a signer that can be used for cryptographic operations.
     *
     * @returns {Ed25519Account} The newly generated Ed25519 account.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static generate(): Ed25519Account;
    /**
     * Derives an Ed25519 account using a specified BIP44 path and mnemonic seed phrase.
     *
     * @param args - The arguments for deriving the account.
     * @param args.path - The BIP44 derive hardened path, e.g., m/44'/637'/0'/0'/0'.
     * Detailed description: {@link https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki}
     * @param args.mnemonic - The mnemonic seed phrase of the account.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static fromDerivationPath(args: Ed25519SignerFromDerivationPathArgs): Ed25519Account;
    /**
     * Verify the given message and signature with the public key.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.message - Raw message data in HexInput format.
     * @param args.signature - Signed message signature.
     * @returns A boolean indicating whether the signature is valid.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignature(args: VerifyEd25519SignatureArgs): boolean;
    /**
     * Verify the given message and signature with the public key.
     *
     * Ed25519 signatures do not depend on chain state, so this function is equivalent to the synchronous verifySignature method.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.aptosConfig - The configuration object for connecting to the Aptos network
     * @param args.message - Raw message data in HexInput format.
     * @param args.signature - Signed message signature.
     * @returns A boolean indicating whether the signature is valid.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: Signature;
    }): Promise<boolean>;
    /**
     * Sign a message using the account's Ed25519 private key.
     * This function returns an AccountAuthenticator containing the signature along with the account's public key.
     *
     * @param message - The signing message, represented as hexadecimal input.
     * @returns An AccountAuthenticator containing the signature and the account's public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signWithAuthenticator(message: HexInput): AccountAuthenticatorEd25519;
    /**
     * Sign a transaction using the account's Ed25519 private key.
     * This function returns an AccountAuthenticator that contains the signature of the transaction along with the account's public key.
     *
     * @param transaction - The raw transaction to be signed.
     * @returns An AccountAuthenticator containing the signature and the public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorEd25519;
    /**
     * Sign the given message using the account's Ed25519 private key.
     * @param message - The message to be signed in HexInput format.
     * @returns Signature - The resulting signature of the signed message.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    sign(message: HexInput): Ed25519Signature;
    /**
     * Sign the given transaction using the available signing capabilities.
     * This function helps ensure that the transaction is properly authenticated before submission.
     *
     * @param transaction - The transaction to be signed.
     * @returns Signature - The resulting signature for the transaction.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransaction(transaction: AnyRawTransaction): Ed25519Signature;
}

export { Account as A, type CreateEd25519AccountFromPrivateKeyArgs as C, type Ed25519SignerConstructorArgs as E, type GenerateEd25519AccountArgs as G, type PrivateKeyFromDerivationPathArgs as P, type SingleKeySigner as S, type VerifyEd25519SignatureArgs as V, type Ed25519SignerFromDerivationPathArgs as a, Ed25519Account as b, type CreateEd25519SingleKeyAccountFromPrivateKeyArgs as c, type CreateSingleKeyAccountFromPrivateKeyArgs as d, type CreateAccountFromPrivateKeyArgs as e, type GenerateEd25519SingleKeyAccountArgs as f, type GenerateSingleKeyAccountArgs as g, type GenerateAccountArgs as h, isSingleKeySigner as i, type SingleKeySignerOrLegacyEd25519Account as j, type SingleKeySignerConstructorArgs as k, type SingleKeySignerGenerateArgs as l, type SingleKeySignerFromDerivationPathArgs as m, type VerifySingleKeySignatureArgs as n, SingleKeyAccount as o };
