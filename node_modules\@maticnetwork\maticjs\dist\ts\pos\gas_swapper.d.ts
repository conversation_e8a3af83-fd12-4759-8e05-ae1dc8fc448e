import { BaseToken, Web3SideChainClient } from "../utils";
import { IPOSClientConfig, ITransactionOption } from "../interfaces";
export declare class GasSwapper extends BaseToken<IPOSClientConfig> {
    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string);
    method(methodName: string, ...args: any[]): Promise<import("..").BaseContractMethod>;
    depositWithGas(tokenAddress: string, depositAmount: string, userAddress: string, swapCallData: string, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
}
