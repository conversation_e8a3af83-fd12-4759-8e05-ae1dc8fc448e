"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcExchangeTransformer = void 0;
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const utils_1 = require("@injectivelabs/utils");
const numbers_js_1 = require("./../../../utils/numbers.js");
/**
 * @category Chain Grpc Transformer
 */
class ChainGrpcExchangeTransformer {
    static moduleParamsResponseToParams(response) {
        const params = response.params;
        const spotMarketInstantListingFee = params.spotMarketInstantListingFee;
        const derivativeMarketInstantListingFee = params.derivativeMarketInstantListingFee;
        return {
            spotMarketInstantListingFee: spotMarketInstantListingFee
                ? {
                    amount: spotMarketInstantListingFee.amount,
                    denom: spotMarketInstantListingFee.denom,
                }
                : undefined,
            derivativeMarketInstantListingFee: derivativeMarketInstantListingFee
                ? {
                    amount: derivativeMarketInstantListingFee.amount,
                    denom: derivativeMarketInstantListingFee.denom,
                }
                : undefined,
            defaultSpotMakerFeeRate: params.defaultSpotMakerFeeRate,
            defaultSpotTakerFeeRate: params.defaultSpotTakerFeeRate,
            defaultDerivativeMakerFeeRate: params.defaultDerivativeMakerFeeRate,
            defaultDerivativeTakerFeeRate: params.defaultDerivativeTakerFeeRate,
            defaultInitialMarginRatio: params.defaultInitialMarginRatio,
            defaultMaintenanceMarginRatio: params.defaultMaintenanceMarginRatio,
            defaultFundingInterval: parseInt(params.defaultFundingInterval, 10),
            fundingMultiple: parseInt(params.fundingMultiple, 10),
            relayerFeeShareRate: params.relayerFeeShareRate,
            defaultHourlyFundingRateCap: params.defaultHourlyFundingRateCap,
            defaultHourlyInterestRate: params.defaultHourlyInterestRate,
            maxDerivativeOrderSideCount: params.maxDerivativeOrderSideCount,
            injRewardStakedRequirementThreshold: params.injRewardStakedRequirementThreshold,
            tradingRewardsVestingDuration: parseInt(params.tradingRewardsVestingDuration, 10),
            liquidatorRewardShareRate: params.liquidatorRewardShareRate,
            binaryOptionsMarketInstantListingFee: params.binaryOptionsMarketInstantListingFee
                ? {
                    amount: params.binaryOptionsMarketInstantListingFee.amount,
                    denom: params.binaryOptionsMarketInstantListingFee.denom,
                }
                : undefined,
            atomicMarketOrderAccessLevel: core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.atomicMarketOrderAccessLevelToJSON(params.atomicMarketOrderAccessLevel),
            spotAtomicMarketOrderFeeMultiplier: params.spotAtomicMarketOrderFeeMultiplier,
            derivativeAtomicMarketOrderFeeMultiplier: params.derivativeAtomicMarketOrderFeeMultiplier,
            binaryOptionsAtomicMarketOrderFeeMultiplier: params.binaryOptionsAtomicMarketOrderFeeMultiplier,
            minimalProtocolFeeRate: params.minimalProtocolFeeRate,
            isInstantDerivativeMarketLaunchEnabled: params.isInstantDerivativeMarketLaunchEnabled,
            postOnlyModeHeightThreshold: params.postOnlyModeHeightThreshold,
            marginDecreasePriceTimestampThresholdSeconds: params.marginDecreasePriceTimestampThresholdSeconds,
            exchangeAdmins: params.exchangeAdmins,
        };
    }
    static feeDiscountScheduleResponseToFeeDiscountSchedule(response) {
        const schedule = response.feeDiscountSchedule;
        return {
            bucketCount: parseInt(schedule.bucketCount, 10),
            bucketDuration: parseInt(schedule.bucketDuration, 10),
            quoteDenomsList: schedule.quoteDenoms,
            tierInfosList: schedule.tierInfos
                .map(ChainGrpcExchangeTransformer.grpcFeeDiscountTierInfoToFeeDiscountTierInfo)
                .filter((info) => info),
            disqualifiedMarketIdsList: schedule.disqualifiedMarketIds,
        };
    }
    static tradingRewardsCampaignResponseToTradingRewardsCampaign(response) {
        return {
            tradingRewardCampaignInfo: ChainGrpcExchangeTransformer.grpcTradingRewardCampaignInfoToTradingRewardCampaignInfo(response.tradingRewardCampaignInfo),
            tradingRewardPoolCampaignScheduleList: response.tradingRewardPoolCampaignSchedule.map(ChainGrpcExchangeTransformer.grpcCampaignRewardPoolToCampaignRewardPool),
            pendingTradingRewardPoolCampaignScheduleList: response.pendingTradingRewardPoolCampaignSchedule.map(ChainGrpcExchangeTransformer.grpcCampaignRewardPoolToCampaignRewardPool),
            totalTradeRewardPoints: response.totalTradeRewardPoints,
            pendingTotalTradeRewardPointsList: response.pendingTotalTradeRewardPoints,
        };
    }
    static feeDiscountAccountInfoResponseToFeeDiscountAccountInfo(response) {
        return {
            tierLevel: parseInt(response.tierLevel, 10),
            accountInfo: ChainGrpcExchangeTransformer.grpcFeeDiscountTierInfoToFeeDiscountTierInfo(response.accountInfo),
            accountTtl: ChainGrpcExchangeTransformer.grpcFeeDiscountTierTTLToFeeDiscountTierTTL(response.accountTtl),
        };
    }
    static grpcFeeDiscountTierInfoToFeeDiscountTierInfo(info) {
        if (!info) {
            return;
        }
        return {
            makerDiscountRate: info.makerDiscountRate,
            takerDiscountRate: info.takerDiscountRate,
            stakedAmount: info.stakedAmount,
            volume: info.volume == undefined ? '0' : info.volume,
        };
    }
    static grpcFeeDiscountTierTTLToFeeDiscountTierTTL(info) {
        if (!info) {
            return;
        }
        return {
            tier: parseInt(info.tier, 10),
            ttlTimestamp: parseInt(info.ttlTimestamp, 10),
        };
    }
    static grpcPointsMultiplierToPointsMultiplier(point) {
        return {
            makerPointsMultiplier: point.makerPointsMultiplier,
            takerPointsMultiplier: point.takerPointsMultiplier,
        };
    }
    static grpcTradingRewardCampaignBoostInfoToTradingRewardCampaignBoostInfo(info) {
        if (!info) {
            return;
        }
        return {
            boostedSpotMarketIdsList: info.boostedSpotMarketIds,
            boostedDerivativeMarketIdsList: info.boostedDerivativeMarketIds,
            spotMarketMultipliersList: info.spotMarketMultipliers.map(ChainGrpcExchangeTransformer.grpcPointsMultiplierToPointsMultiplier),
            derivativeMarketMultipliersList: info.derivativeMarketMultipliers.map(ChainGrpcExchangeTransformer.grpcPointsMultiplierToPointsMultiplier),
        };
    }
    static grpcTradingRewardCampaignInfoToTradingRewardCampaignInfo(info) {
        if (!info) {
            return;
        }
        return {
            campaignDurationSeconds: parseInt(info.campaignDurationSeconds, 10),
            quoteDenomsList: info.quoteDenoms,
            tradingRewardBoostInfo: ChainGrpcExchangeTransformer.grpcTradingRewardCampaignBoostInfoToTradingRewardCampaignBoostInfo(info.tradingRewardBoostInfo),
            disqualifiedMarketIdsList: info.disqualifiedMarketIds,
        };
    }
    static grpcCampaignRewardPoolToCampaignRewardPool(pool) {
        return {
            startTimestamp: parseInt(pool.startTimestamp, 10),
            maxCampaignRewardsList: pool.maxCampaignRewards.map((coin) => ({
                amount: coin.amount,
                denom: coin.denom,
            })),
        };
    }
    static grpcPositionToPosition(position) {
        return {
            islong: position.isLong,
            ...position,
        };
    }
    static positionsResponseToPositions(response) {
        return response.state.map((position) => {
            return {
                subaccountId: position.subaccountId,
                marketId: position.marketId,
                position: ChainGrpcExchangeTransformer.grpcPositionToPosition(position.position),
            };
        });
    }
    static isOptedOutOfRewardsResponseToIsOptedOutOfRewards(response) {
        return {
            isOptedOut: response.isOptedOut,
        };
    }
    static activeStakeGrantResponseToActiveStakeGrant(response) {
        return {
            grant: response.grant,
            effectiveGrant: response.effectiveGrant,
        };
    }
    static denomMinNotionalResponseToDenomMinNotional(response) {
        return response.amount;
    }
    static denomDecimalsResponseToDenomDecimals(response) {
        return response.denomDecimals.map((denomDecimals) => ({
            denom: denomDecimals.denom,
            decimals: denomDecimals.decimals,
        }));
    }
    static denomMinNotionalsResponseToDenomMinNotionals(response) {
        return response.denomMinNotionals.map((denomDecimals) => ({
            denom: denomDecimals.denom,
            minNotional: new utils_1.BigNumberInBase(denomDecimals.minNotional)
                .dividedBy(10 ** 18)
                .toFixed(),
        }));
    }
    static spotMarketsResponseToSpotMarkets(response) {
        return response.markets.map((market) => {
            return ChainGrpcExchangeTransformer.grpcSpotMarketToSpotMarket(market);
        });
    }
    static grpcSpotMarketToSpotMarket(market) {
        const marketInfo = market;
        return {
            marketId: marketInfo.marketId,
            marketStatus: core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.marketStatusToJSON(marketInfo.status),
            ticker: marketInfo.ticker,
            baseDenom: marketInfo.baseDenom,
            quoteDenom: marketInfo.quoteDenom,
            makerFeeRate: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.makerFeeRate).toFixed(),
            quoteToken: undefined,
            baseToken: undefined,
            takerFeeRate: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.takerFeeRate).toFixed(),
            serviceProviderFee: '',
            minPriceTickSize: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.minPriceTickSize).toNumber(),
            minQuantityTickSize: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.minQuantityTickSize).toNumber(),
            minNotional: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.minNotional).toNumber(),
        };
    }
    static fullSpotMarketsResponseToSpotMarkets(response) {
        return response.markets.map((market) => {
            return ChainGrpcExchangeTransformer.grpcFullSpotMarketToSpotMarket(market);
        });
    }
    static grpcFullSpotMarketToSpotMarket(market) {
        const marketInfo = market.market;
        return {
            marketId: marketInfo.marketId,
            marketStatus: core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.marketStatusToJSON(marketInfo.status),
            ticker: marketInfo.ticker,
            baseDenom: marketInfo.baseDenom,
            quoteDenom: marketInfo.quoteDenom,
            makerFeeRate: marketInfo.makerFeeRate,
            quoteToken: undefined,
            baseToken: undefined,
            takerFeeRate: marketInfo.takerFeeRate,
            serviceProviderFee: '',
            minPriceTickSize: Number(marketInfo.minPriceTickSize),
            minQuantityTickSize: Number(marketInfo.minQuantityTickSize),
            minNotional: Number(marketInfo.minNotional),
        };
    }
    static fullDerivativeMarketsResponseToDerivativeMarkets(response) {
        return response.markets.map((market) => {
            return ChainGrpcExchangeTransformer.grpcFullDerivativeMarketToDerivativeMarket(market);
        });
    }
    static grpcFullDerivativeMarketToDerivativeMarket(market) {
        const marketInfo = market.market;
        return {
            oracleType: core_proto_ts_1.InjectiveOracleV1Beta1Oracle.oracleTypeToJSON(marketInfo.oracleType),
            marketId: marketInfo.marketId,
            marketStatus: core_proto_ts_1.InjectiveExchangeV1Beta1Exchange.marketStatusToJSON(marketInfo.status),
            ticker: marketInfo.ticker,
            quoteDenom: marketInfo.quoteDenom,
            makerFeeRate: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.makerFeeRate).toFixed(),
            takerFeeRate: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.takerFeeRate).toFixed(),
            serviceProviderFee: '',
            quoteToken: undefined,
            minPriceTickSize: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.minPriceTickSize).toNumber(),
            minQuantityTickSize: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.minQuantityTickSize).toNumber(),
            minNotional: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.minNotional).toNumber(),
            initialMarginRatio: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.initialMarginRatio).toFixed(),
            maintenanceMarginRatio: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(marketInfo.maintenanceMarginRatio).toFixed(),
            isPerpetual: marketInfo.isPerpetual,
            oracleBase: marketInfo.oracleBase,
            oracleQuote: marketInfo.oracleQuote,
            oracleScaleFactor: marketInfo.oracleScaleFactor,
            perpetualMarketInfo: {
                hourlyFundingRateCap: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(market.perpetualInfo?.marketInfo?.hourlyFundingRateCap ?? '0').toFixed(),
                hourlyInterestRate: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(market.perpetualInfo?.marketInfo?.hourlyInterestRate ?? '0').toFixed(),
                nextFundingTimestamp: parseInt(market.perpetualInfo?.marketInfo?.nextFundingTimestamp ?? '', 10),
                fundingInterval: parseInt(market.perpetualInfo?.marketInfo?.fundingInterval ?? '', 10),
            },
            perpetualMarketFunding: {
                cumulativeFunding: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(market.perpetualInfo?.fundingInfo?.cumulativeFunding ?? '0').toFixed(),
                cumulativePrice: (0, numbers_js_1.denomAmountFromGrpcChainDenomAmount)(market.perpetualInfo?.fundingInfo?.cumulativePrice ?? '0').toFixed(),
                lastTimestamp: parseInt(market.perpetualInfo?.fundingInfo?.lastTimestamp ?? '', 10),
            },
        };
    }
}
exports.ChainGrpcExchangeTransformer = ChainGrpcExchangeTransformer;
