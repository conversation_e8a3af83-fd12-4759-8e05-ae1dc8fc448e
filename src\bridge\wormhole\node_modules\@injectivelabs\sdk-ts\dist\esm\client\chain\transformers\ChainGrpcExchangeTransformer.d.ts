import { InjectiveExchangeV1Beta1Query, InjectiveExchangeV1Beta1Exchange } from '@injectivelabs/core-proto-ts';
import { ChainPosition, PointsMultiplier, ChainDenomDecimal, GrpcChainPosition, CampaignRewardPool, FeeDiscountTierTTL, IsOptedOutOfRewards, FeeDiscountSchedule, FeeDiscountTierInfo, TradeRewardCampaign, ChainDenomMinNotional, GrpcPointsMultiplier, ExchangeModuleParams, GrpcCampaignRewardPool, FeeDiscountAccountInfo, GrpcFeeDiscountTierTTL, GrpcFeeDiscountTierInfo, ChainDerivativePosition, TradingRewardCampaignInfo, GrpcTradingRewardCampaignInfo, TradingRewardCampaignBoostInfo, GrpcTradingRewardCampaignBoostInfo, type GrpcChainFullDerivativeMarket, type GrpcChainFullSpotMarket, type GrpcChainSpotMarket } from '../types/exchange.js';
import type { DerivativeMarket } from '../../indexer/types/derivatives.js';
import type { SpotMarket } from '../../indexer/types/spot.js';
/**
 * @category Chain Grpc Transformer
 */
export declare class ChainGrpcExchangeTransformer {
    static moduleParamsResponseToParams(response: InjectiveExchangeV1Beta1Query.QueryExchangeParamsResponse): ExchangeModuleParams;
    static feeDiscountScheduleResponseToFeeDiscountSchedule(response: InjectiveExchangeV1Beta1Query.QueryFeeDiscountScheduleResponse): FeeDiscountSchedule;
    static tradingRewardsCampaignResponseToTradingRewardsCampaign(response: InjectiveExchangeV1Beta1Query.QueryTradeRewardCampaignResponse): TradeRewardCampaign;
    static feeDiscountAccountInfoResponseToFeeDiscountAccountInfo(response: InjectiveExchangeV1Beta1Query.QueryFeeDiscountAccountInfoResponse): FeeDiscountAccountInfo;
    static grpcFeeDiscountTierInfoToFeeDiscountTierInfo(info?: GrpcFeeDiscountTierInfo): FeeDiscountTierInfo | undefined;
    static grpcFeeDiscountTierTTLToFeeDiscountTierTTL(info?: GrpcFeeDiscountTierTTL): FeeDiscountTierTTL | undefined;
    static grpcPointsMultiplierToPointsMultiplier(point: GrpcPointsMultiplier): PointsMultiplier;
    static grpcTradingRewardCampaignBoostInfoToTradingRewardCampaignBoostInfo(info?: GrpcTradingRewardCampaignBoostInfo): TradingRewardCampaignBoostInfo | undefined;
    static grpcTradingRewardCampaignInfoToTradingRewardCampaignInfo(info?: GrpcTradingRewardCampaignInfo): TradingRewardCampaignInfo | undefined;
    static grpcCampaignRewardPoolToCampaignRewardPool(pool: GrpcCampaignRewardPool): CampaignRewardPool;
    static grpcPositionToPosition(position: GrpcChainPosition): ChainPosition;
    static positionsResponseToPositions(response: InjectiveExchangeV1Beta1Query.QueryPositionsResponse): ChainDerivativePosition[];
    static isOptedOutOfRewardsResponseToIsOptedOutOfRewards(response: InjectiveExchangeV1Beta1Query.QueryIsOptedOutOfRewardsResponse): IsOptedOutOfRewards;
    static activeStakeGrantResponseToActiveStakeGrant(response: InjectiveExchangeV1Beta1Query.QueryActiveStakeGrantResponse): {
        grant: InjectiveExchangeV1Beta1Exchange.ActiveGrant;
        effectiveGrant: InjectiveExchangeV1Beta1Exchange.EffectiveGrant;
    };
    static denomMinNotionalResponseToDenomMinNotional(response: InjectiveExchangeV1Beta1Query.QueryDenomMinNotionalResponse): string;
    static denomDecimalsResponseToDenomDecimals(response: InjectiveExchangeV1Beta1Query.QueryDenomDecimalsResponse): ChainDenomDecimal[];
    static denomMinNotionalsResponseToDenomMinNotionals(response: InjectiveExchangeV1Beta1Query.QueryDenomMinNotionalsResponse): ChainDenomMinNotional[];
    static spotMarketsResponseToSpotMarkets(response: InjectiveExchangeV1Beta1Query.QuerySpotMarketsResponse): SpotMarket[];
    static grpcSpotMarketToSpotMarket(market: GrpcChainSpotMarket): SpotMarket;
    static fullSpotMarketsResponseToSpotMarkets(response: InjectiveExchangeV1Beta1Query.QueryFullSpotMarketsResponse): SpotMarket[];
    static grpcFullSpotMarketToSpotMarket(market: GrpcChainFullSpotMarket): SpotMarket;
    static fullDerivativeMarketsResponseToDerivativeMarkets(response: InjectiveExchangeV1Beta1Query.QueryDerivativeMarketsResponse): DerivativeMarket[];
    static grpcFullDerivativeMarketToDerivativeMarket(market: GrpcChainFullDerivativeMarket): DerivativeMarket;
}
