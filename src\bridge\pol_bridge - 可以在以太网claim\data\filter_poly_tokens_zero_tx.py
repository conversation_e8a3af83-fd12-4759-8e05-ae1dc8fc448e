#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查Polygon网络上的代币是否有与0地址相关的交易
使用多线程并发执行，提高处理效率
筛选出有0地址交易的代币并保存详细的交易记录
"""

import os
import json
import time
import argparse
import requests
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import random
import decimal

# 文件路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
INPUT_TOKENS_PATH = os.path.join(CURRENT_DIR, "low_volume_routable_tokens.json")
OUTPUT_TOKENS_PATH = os.path.join(CURRENT_DIR, "zero_tx_tokens_filtered.json")
STATS_OUTPUT_PATH = os.path.join(CURRENT_DIR, "zero_tx_stats_detailed.json")

# API端点
API_ENDPOINTS = {
    "polygonscan": "https://api.polygonscan.com/api",
    "geckoterminal": "https://api.geckoterminal.com/api/v2"
}

# API密钥
API_KEYS = {
    "polygonscan": "GPHHB2FE5KTCCR27A44E89D695N7WUBXCF"
}

# 0地址
ZERO_ADDRESS = "0x0000000000000000000000000000000000000000"

# 重试设置
MAX_RETRIES = 3  # 最大重试次数
INITIAL_RETRY_DELAY = 3  # 初始重试延迟（秒）
MAX_RETRY_DELAY = 30  # 最大重试延迟（秒）
MAX_WORKERS = 8  # 最大线程数

def load_input_tokens() -> Dict[str, Dict]:
    """
    加载low_volume_routable_tokens.json中的代币数据，仅提取Polygon网络数据
    
    Returns:
        Dict[str, Dict]: Polygon网络的代币数据，格式为{token_address: token_data}
    """
    try:
        if not os.path.exists(INPUT_TOKENS_PATH):
            print(f"错误: 文件不存在 - {INPUT_TOKENS_PATH}")
            return {}
            
        with open(INPUT_TOKENS_PATH, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
        
        # 提取所有包含Polygon网络的代币，并按地址为键重新组织
        polygon_tokens = {}
        for token_symbol, token_data in tokens.items():
            if "137" in token_data:
                polygon_data = token_data["137"]
                address = polygon_data["address"].lower()
                
                # 重新组织数据结构
                token_info = {
                    "symbol": token_symbol,
                    "name": polygon_data.get("name", "Unknown"),
                    "decimals": polygon_data.get("decimals", 18),
                    "chainId": polygon_data.get("chainId", 137),
                    "address": address,
                    "volume_usd": polygon_data.get("volume_usd", 0)
                }
                
                # 如果有以太坊数据，也保存
                if "1" in token_data:
                    token_info["eth_address"] = token_data["1"]["address"].lower()
                    token_info["eth_name"] = token_data["1"].get("name", "Unknown")
                
                polygon_tokens[address] = token_info
        
        print(f"从{INPUT_TOKENS_PATH}加载了 {len(polygon_tokens)} 个Polygon网络代币")
        return polygon_tokens
    
    except Exception as e:
        print(f"加载代币数据失败: {str(e)}")
        return {}

def load_output_tokens() -> Dict[str, Any]:
    """
    加载已存在的输出文件中的代币数据
    
    Returns:
        Dict[str, Any]: 现有的输出代币数据
    """
    try:
        if not os.path.exists(OUTPUT_TOKENS_PATH):
            return {}
            
        with open(OUTPUT_TOKENS_PATH, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
            
        return tokens
    except Exception as e:
        print(f"加载输出代币数据失败: {str(e)}")
        return {}

def load_stats_data() -> Dict[str, Any]:
    """
    加载已存在的统计数据文件
    
    Returns:
        Dict[str, Any]: 现有的统计数据
    """
    try:
        if not os.path.exists(STATS_OUTPUT_PATH):
            return {}
            
        with open(STATS_OUTPUT_PATH, 'r', encoding='utf-8') as f:
            stats = json.load(f)
            
        return stats
    except Exception as e:
        print(f"加载统计数据失败: {str(e)}")
        return {}

def save_output_tokens(tokens: Dict[str, Any]) -> bool:
    """
    保存代币数据到输出文件，使用更新方式，不删除现有数据
    
    Args:
        tokens: 要保存的代币数据
        
    Returns:
        bool: 是否保存成功
    """
    try:
        # 加载现有文件
        existing_tokens = {}
        if os.path.exists(OUTPUT_TOKENS_PATH):
            with open(OUTPUT_TOKENS_PATH, 'r', encoding='utf-8') as f:
                existing_tokens = json.load(f)
        
        # 更新现有数据，而不是替换
        existing_tokens.update(tokens)
        
        with open(OUTPUT_TOKENS_PATH, 'w', encoding='utf-8') as f:
            json.dump(existing_tokens, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存代币数据失败: {str(e)}")
        return False

def save_stats_data(stats: Dict[str, Any]) -> bool:
    """
    保存统计数据到输出文件，使用更新方式，不删除现有数据
    
    Args:
        stats: 要保存的统计数据
        
    Returns:
        bool: 是否保存成功
    """
    try:
        # 加载现有文件
        existing_stats = {}
        if os.path.exists(STATS_OUTPUT_PATH):
            with open(STATS_OUTPUT_PATH, 'r', encoding='utf-8') as f:
                existing_stats = json.load(f)
        
        # 更新现有数据，而不是替换
        for address, data in stats.items():
            if address in existing_stats:
                # 更新last_check_time和交易数据，保留其他字段
                existing_stats[address]["last_check_time"] = data["last_check_time"]
                existing_stats[address]["zero_tx_count"] = data["zero_tx_count"]
                existing_stats[address]["zero_tx_volume_usd"] = data["zero_tx_volume_usd"]
                existing_stats[address]["token_price_usd"] = data["token_price_usd"]
                existing_stats[address]["transactions"] = data["transactions"]
                # 确保有eth_address字段
                if "eth_address" not in existing_stats[address] and "eth_address" in data:
                    existing_stats[address]["eth_address"] = data["eth_address"]
            else:
                # 新增数据
                existing_stats[address] = data
        
        with open(STATS_OUTPUT_PATH, 'w', encoding='utf-8') as f:
            json.dump(existing_stats, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存统计数据失败: {str(e)}")
        return False

def exponential_backoff(retry_count: int) -> float:
    """
    计算指数退避策略的等待时间
    
    Args:
        retry_count: 当前重试次数
    
    Returns:
        float: 等待时间（秒）
    """
    delay = min(MAX_RETRY_DELAY, INITIAL_RETRY_DELAY * (2 ** retry_count))
    # 增加随机因子，避免多个请求同时重试
    jitter = random.uniform(0, 0.1 * delay)
    return delay + jitter

def get_token_transactions(token_address: str, minutes: int = 2888, api_key: str = None, max_tx: int = 200) -> List[Dict[str, Any]]:
    """
    获取代币最近的交易，包含完整的重试机制
    
    Args:
        token_address: 代币地址
        minutes: 过去几分钟的交易(默认2888分钟)
        api_key: API密钥(可选)
        max_tx: 最大获取交易数(默认200)
        
    Returns:
        List[Dict[str, Any]]: 交易列表
    """
    api_endpoint = API_ENDPOINTS.get("polygonscan")
    
    # 计算时间范围
    now = int(time.time())
    start_time = now - (minutes * 60)
    
    # 构建API请求
    params = {
        "module": "account",
        "action": "tokentx",
        "contractaddress": token_address,
        "startblock": 0,
        "endblock": *********,
        "sort": "desc",
        "page": 1,
        "offset": max_tx  # 限制结果数量
    }
    
    if api_key:
        params["apikey"] = api_key
    
    # 初始化重试计数
    retry_count = 0
    
    while retry_count <= MAX_RETRIES:
        try:
            # 设置请求超时时间为10秒
            response = requests.get(api_endpoint, params=params, timeout=10)
            
            if response.status_code != 200:
                print(f"API请求失败，状态码: {response.status_code}")
                
                # 如果是服务器错误（5xx），进行重试
                if 500 <= response.status_code < 600 and retry_count < MAX_RETRIES:
                    retry_count += 1
                    wait_time = exponential_backoff(retry_count)
                    print(f"服务器错误，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                    time.sleep(wait_time)
                    continue
                    
                return []
                
            data = response.json()
            
            transactions = []
            
            if data["status"] == "1":
                # 筛选最近n分钟的交易
                for tx in data["result"]:
                    tx_timestamp = int(tx["timeStamp"])
                    
                    if tx_timestamp >= start_time:
                        transactions.append({
                            "hash": tx["hash"],
                            "from": tx["from"].lower(),
                            "to": tx["to"].lower(),
                            "value": tx["value"],
                            "tokenName": tx["tokenName"],
                            "tokenSymbol": tx["tokenSymbol"],
                            "timestamp": tx_timestamp,
                            "datetime": datetime.fromtimestamp(tx_timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                            "gas": tx["gas"],
                            "gasPrice": tx["gasPrice"],
                            "blockNumber": tx["blockNumber"]
                        })
                    else:
                        # 已经超过时间范围
                        break
                
                return transactions
                
            else:
                print(f"API请求错误: {data.get('message', 'Unknown error')}")
                
                # 如果是API速率限制错误, 等待并重试
                if ("rate limit" in data.get('message', '').lower() or 
                    "ratelimit" in data.get('message', '').lower() or
                    "max rate" in data.get('message', '').lower()):
                    
                    if retry_count < MAX_RETRIES:
                        retry_count += 1
                        wait_time = exponential_backoff(retry_count)
                        print(f"触发API速率限制，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                        time.sleep(wait_time)
                        continue
                
                # 其他类型的错误
                return []
                
        except requests.exceptions.Timeout:
            print("API请求超时")
            if retry_count < MAX_RETRIES:
                retry_count += 1
                wait_time = exponential_backoff(retry_count)
                print(f"请求超时，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                time.sleep(wait_time)
            else:
                print("达到最大重试次数，放弃请求")
                return []
                
        except requests.exceptions.ConnectionError:
            print("API连接错误")
            if retry_count < MAX_RETRIES:
                retry_count += 1
                wait_time = exponential_backoff(retry_count)
                print(f"连接错误，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                time.sleep(wait_time)
            else:
                print("达到最大重试次数，放弃请求")
                return []
                
        except Exception as e:
            print(f"获取代币交易失败: {str(e)}")
            if retry_count < MAX_RETRIES:
                retry_count += 1
                wait_time = exponential_backoff(retry_count)
                print(f"发生错误，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                time.sleep(wait_time)
            else:
                print("达到最大重试次数，放弃请求")
                return []
    
    # 如果所有重试都失败
    print("所有重试均失败，无法获取交易数据")
    return []

def filter_zero_address_transactions(transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    筛选与0地址相关的交易
    
    Args:
        transactions: 交易列表
        
    Returns:
        List[Dict[str, Any]]: 与0地址相关的交易列表
    """
    return [
        tx for tx in transactions 
        if tx["from"] == ZERO_ADDRESS.lower() or tx["to"] == ZERO_ADDRESS.lower()
    ]

def calculate_tx_volume_usd(transactions: List[Dict[str, Any]], token_price_usd: float, token_symbol: str) -> List[Dict[str, Any]]:
    """
    计算每笔交易的美元价值
    
    Args:
        transactions: 交易列表
        token_price_usd: 代币价格（美元）
        token_symbol: 代币符号，用于日志
        
    Returns:
        List[Dict[str, Any]]: 增加了美元价值的交易列表
    """
    for tx in transactions:
        # 获取代币精度
        token_decimal = int(tx.get("tokenDecimal", 18))
        
        # 计算代币数量
        try:
            value_decimal = decimal.Decimal(tx["value"]) / decimal.Decimal(10 ** token_decimal)
            # 计算美元价值
            volume_usd = float(value_decimal) * token_price_usd
            tx["token_amount"] = float(value_decimal)
            tx["volume_usd"] = round(volume_usd, 6)
            tx["price_used"] = token_price_usd
            
            # 记录大额交易的日志
            if volume_usd > 10000:
                print(f"警告: 检测到大额交易 - {token_symbol} 交易量: {float(value_decimal):,.2f} 代币，估值: ${volume_usd:,.2f}")
                print(f"  - 使用价格: ${token_price_usd:.8f} / 交易哈希: {tx['hash']}")
        
        except (decimal.InvalidOperation, ValueError, TypeError) as e:
            # 如果计算失败，设置默认值
            print(f"计算交易价值时出错: {str(e)}")
            print(f"  - 代币: {token_symbol}, 值: {tx['value']}, 精度: {token_decimal}")
            tx["token_amount"] = 0
            tx["volume_usd"] = 0
            tx["price_used"] = 0
    
    return transactions

def process_token(token_address: str, token_data: Dict[str, Any], minutes: int = 2888) -> Tuple[bool, Dict[str, Any], List[Dict[str, Any]]]:
    """
    处理单个代币，检查是否有0地址交易
    
    Args:
        token_address: 代币地址
        token_data: 代币信息
        minutes: 查询过去几分钟的交易
        
    Returns:
        Tuple[bool, Dict[str, Any], List[Dict[str, Any]]]: 
            - 是否有0地址交易
            - 更新后的代币数据
            - 0地址交易列表
    """
    try:
        token_symbol = token_data.get("symbol", "Unknown")
        print(f"\n处理代币: {token_symbol} ({token_address})")
        
        # 获取交易
        transactions = get_token_transactions(token_address, minutes, API_KEYS.get("polygonscan"))
        
        if not transactions:
            print(f"未找到任何交易，跳过 {token_symbol}")
            return False, token_data, []
        
        # 筛选0地址交易
        zero_address_txs = filter_zero_address_transactions(transactions)
        
        if not zero_address_txs:
            print(f"代币 {token_symbol} 没有与0地址相关的交易")
            return False, token_data, []
        
        # 获取代币价格 - 尝试通过GeckoTerminal获取
        token_price_usd = get_token_price_from_geckoterminal(token_address)
        
        if token_price_usd <= 0:
            # 如果无法获取价格，使用默认值
            token_price_usd = 0.0001
            print(f"无法获取价格，使用默认价格: ${token_price_usd}")
        else:
            print(f"获取到价格: ${token_price_usd}")
        
        # 计算每笔交易的美元价值
        zero_address_txs = calculate_tx_volume_usd(zero_address_txs, token_price_usd, token_symbol)
        
        # 计算0地址交易的总美元价值
        total_zero_tx_volume_usd = sum(tx.get("volume_usd", 0) for tx in zero_address_txs)
        
        # 有0地址交易，更新代币数据并返回
        token_data_updated = token_data.copy()
        token_data_updated["check_time"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        token_data_updated["zero_tx_count"] = len(zero_address_txs)
        token_data_updated["total_tx_count"] = len(transactions)
        token_data_updated["zero_tx_volume_usd"] = round(total_zero_tx_volume_usd, 6)
        token_data_updated["token_price_usd"] = token_price_usd
        token_data_updated["price_source"] = "geckoterminal"
        
        print(f"找到 {len(zero_address_txs)} 个与0地址相关的交易，总价值: ${total_zero_tx_volume_usd:.2f}")
        return True, token_data_updated, zero_address_txs
    
    except Exception as e:
        print(f"处理代币 {token_address} 时出错: {str(e)}")
        return False, token_data, []

def get_token_price_from_geckoterminal(token_address: str, network: str = "polygon_pos") -> float:
    """
    从GeckoTerminal API获取代币价格信息
    
    Args:
        token_address: 代币地址
        network: 网络名称，默认为polygon_pos
        
    Returns:
        float: 代币价格(USD)，如果无法获取则返回0
    """
    retry_count = 0
    
    while retry_count <= MAX_RETRIES:
        try:
            api_endpoint = API_ENDPOINTS.get("geckoterminal")
            # 使用正确的价格API端点
            url = f"{api_endpoint}/simple/networks/{network}/token_price/{token_address}"
            
            print(f"从GeckoTerminal获取价格: {url}")
            
            # 设置请求头，模拟浏览器行为
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
                "Accept": "application/json",
                "Referer": "https://www.geckoterminal.com/"
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code != 200:
                print(f"GeckoTerminal API请求失败，状态码: {response.status_code}")
                
                # 如果是服务器错误（5xx），进行重试
                if 500 <= response.status_code < 600 and retry_count < MAX_RETRIES:
                    retry_count += 1
                    wait_time = exponential_backoff(retry_count)
                    print(f"GeckoTerminal服务器错误，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                    time.sleep(wait_time)
                    continue
                
                # 如果是429 Too Many Requests，等待更长时间后重试
                if response.status_code == 429 and retry_count < MAX_RETRIES:
                    retry_count += 1
                    wait_time = exponential_backoff(retry_count) * 2  # 等待时间加倍
                    print(f"GeckoTerminal请求频率限制，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                    time.sleep(wait_time)
                    continue
                
                return 0
                
            data = response.json()
            
            # 根据实际返回的数据格式解析价格信息
            # 格式: {"data":{"id":"xxx","type":"simple_token_price","attributes":{"token_prices":{"0x...地址":"价格"}}}}
            if ("data" in data and 
                "attributes" in data["data"] and 
                "token_prices" in data["data"]["attributes"]):
                
                token_prices = data["data"]["attributes"]["token_prices"]
                token_address_lower = token_address.lower()
                
                # 尝试获取价格，可能地址格式有所不同，尝试多种格式
                price_str = None
                for addr, price in token_prices.items():
                    if addr.lower() == token_address_lower:
                        price_str = price
                        break
                
                if price_str:
                    try:
                        price = float(price_str)
                        print(f"GeckoTerminal返回价格: ${price}")
                        return price
                    except (ValueError, TypeError):
                        print(f"价格转换失败: {price_str}")
                else:
                    print(f"未找到代币 {token_address} 的价格数据")
            
            print("GeckoTerminal返回的数据格式不正确或未找到价格")
            return 0
            
        except requests.exceptions.Timeout:
            print("GeckoTerminal API请求超时")
            if retry_count < MAX_RETRIES:
                retry_count += 1
                wait_time = exponential_backoff(retry_count)
                print(f"请求超时，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                time.sleep(wait_time)
            else:
                print("达到最大重试次数，放弃请求")
                return 0
                
        except requests.exceptions.ConnectionError:
            print("GeckoTerminal API连接错误")
            if retry_count < MAX_RETRIES:
                retry_count += 1
                wait_time = exponential_backoff(retry_count)
                print(f"连接错误，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                time.sleep(wait_time)
            else:
                print("达到最大重试次数，放弃请求")
                return 0
            
        except Exception as e:
            print(f"从GeckoTerminal获取价格时出错: {str(e)}")
            if retry_count < MAX_RETRIES:
                retry_count += 1
                wait_time = exponential_backoff(retry_count)
                print(f"发生错误，{wait_time:.2f}秒后重试 ({retry_count}/{MAX_RETRIES})...")
                time.sleep(wait_time)
            else:
                print("达到最大重试次数，放弃请求")
                return 0
    
    # 如果所有重试都失败
    print("所有GeckoTerminal API重试均失败，无法获取价格数据")
    return 0

def main():
    """主函数入口"""
    parser = argparse.ArgumentParser(description="筛选Polygon网络上存在0地址交易的代币并保存统计数据")
    parser.add_argument("--minutes", type=int, default=2888, help="查询过去几分钟的交易 (默认: 2888)")
    parser.add_argument("--threads", type=int, default=MAX_WORKERS, help=f"线程数 (默认: {MAX_WORKERS})")
    
    args = parser.parse_args()
    
    # 加载代币数据
    input_tokens = load_input_tokens()
    if not input_tokens:
        print("未找到有效的代币数据")
        return
    
    print(f"开始处理 {len(input_tokens)} 个代币...")
    
    # 准备结果存储
    tokens_with_zero_tx = {}
    token_transactions = {}
    processed_count = 0
    found_count = 0
    
    # 多线程处理代币
    with concurrent.futures.ThreadPoolExecutor(max_workers=args.threads) as executor:
        # 提交所有任务
        future_to_address = {
            executor.submit(process_token, address, data, args.minutes): address
            for address, data in input_tokens.items()
        }
        
        # 处理结果
        for future in concurrent.futures.as_completed(future_to_address):
            address = future_to_address[future]
            processed_count += 1
            
            try:
                has_zero_tx, token_data, zero_txs = future.result()
                
                if has_zero_tx:
                    # 保存代币数据和交易记录
                    tokens_with_zero_tx[address] = token_data
                    token_transactions[address] = zero_txs
                    found_count += 1
                
                # 显示进度
                print(f"进度: {processed_count}/{len(input_tokens)} ({processed_count/len(input_tokens)*100:.1f}%)")
                print(f"找到: {found_count} 个有0地址交易的代币")
                
            except Exception as e:
                print(f"处理代币 {address} 时出现异常: {str(e)}")
    
    # 保存找到的代币数据
    if tokens_with_zero_tx:
        print(f"\n保存 {len(tokens_with_zero_tx)} 个有0地址交易的代币数据...")
        save_success = save_output_tokens(tokens_with_zero_tx)
        
        if save_success:
            print(f"成功保存代币数据到 {OUTPUT_TOKENS_PATH}")
        else:
            print("保存代币数据失败")
    else:
        print("\n未找到任何有0地址交易的代币")
    
    # 保存详细的交易统计数据
    if token_transactions:
        print(f"\n保存 {len(token_transactions)} 个代币的详细交易记录...")
        
        # 将交易记录格式化为更易读的格式
        detailed_stats = {}
        for address, txs in token_transactions.items():
            token_data = tokens_with_zero_tx.get(address, {})
            token_symbol = token_data.get("symbol", "Unknown")
            
            # 计算0地址交易的总美元价值
            total_zero_tx_volume_usd = sum(tx.get("volume_usd", 0) for tx in txs)
            
            detailed_stats[address] = {
                "name": token_data.get("name", "Unknown"),
                "symbol": token_symbol,
                "address": address,
                "volume_usd": token_data.get("volume_usd", 0),
                "zero_tx_count": len(txs),
                "zero_tx_volume_usd": round(total_zero_tx_volume_usd, 6),
                "token_price_usd": token_data.get("token_price_usd", 0),
                "last_check_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "transactions": txs
            }
            
            # 添加eth_address字段
            if "eth_address" in token_data:
                detailed_stats[address]["eth_address"] = token_data["eth_address"]
        
        save_success = save_stats_data(detailed_stats)
        
        if save_success:
            print(f"成功保存详细交易记录到 {STATS_OUTPUT_PATH}")
        else:
            print("保存详细交易记录失败")
    
    print("\n处理完成!")
    print(f"总处理: {processed_count} 个代币")
    print(f"找到: {found_count} 个有0地址交易的代币")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc() 