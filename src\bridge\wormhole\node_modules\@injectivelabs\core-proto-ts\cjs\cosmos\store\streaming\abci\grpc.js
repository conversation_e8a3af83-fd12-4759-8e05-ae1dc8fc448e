"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.ABCIListenerServiceListenCommitDesc = exports.ABCIListenerServiceListenFinalizeBlockDesc = exports.ABCIListenerServiceDesc = exports.ABCIListenerServiceClientImpl = exports.ListenCommitResponse = exports.ListenCommitRequest = exports.ListenFinalizeBlockResponse = exports.ListenFinalizeBlockRequest = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var types_1 = require("../../../../tendermint/abci/types.js");
var listening_1 = require("../../v1beta1/listening.js");
exports.protobufPackage = "cosmos.store.streaming.abci";
function createBaseListenFinalizeBlockRequest() {
    return { req: undefined, res: undefined };
}
exports.ListenFinalizeBlockRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.req !== undefined) {
            types_1.RequestFinalizeBlock.encode(message.req, writer.uint32(10).fork()).ldelim();
        }
        if (message.res !== undefined) {
            types_1.ResponseFinalizeBlock.encode(message.res, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseListenFinalizeBlockRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.req = types_1.RequestFinalizeBlock.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.res = types_1.ResponseFinalizeBlock.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            req: isSet(object.req) ? types_1.RequestFinalizeBlock.fromJSON(object.req) : undefined,
            res: isSet(object.res) ? types_1.ResponseFinalizeBlock.fromJSON(object.res) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.req !== undefined && (obj.req = message.req ? types_1.RequestFinalizeBlock.toJSON(message.req) : undefined);
        message.res !== undefined && (obj.res = message.res ? types_1.ResponseFinalizeBlock.toJSON(message.res) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ListenFinalizeBlockRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseListenFinalizeBlockRequest();
        message.req = (object.req !== undefined && object.req !== null)
            ? types_1.RequestFinalizeBlock.fromPartial(object.req)
            : undefined;
        message.res = (object.res !== undefined && object.res !== null)
            ? types_1.ResponseFinalizeBlock.fromPartial(object.res)
            : undefined;
        return message;
    },
};
function createBaseListenFinalizeBlockResponse() {
    return {};
}
exports.ListenFinalizeBlockResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseListenFinalizeBlockResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.ListenFinalizeBlockResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseListenFinalizeBlockResponse();
        return message;
    },
};
function createBaseListenCommitRequest() {
    return { blockHeight: "0", res: undefined, changeSet: [] };
}
exports.ListenCommitRequest = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.blockHeight !== "0") {
            writer.uint32(8).int64(message.blockHeight);
        }
        if (message.res !== undefined) {
            types_1.ResponseCommit.encode(message.res, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.changeSet), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                listening_1.StoreKVPair.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseListenCommitRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.blockHeight = longToString(reader.int64());
                    break;
                case 2:
                    message.res = types_1.ResponseCommit.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.changeSet.push(listening_1.StoreKVPair.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            res: isSet(object.res) ? types_1.ResponseCommit.fromJSON(object.res) : undefined,
            changeSet: Array.isArray(object === null || object === void 0 ? void 0 : object.changeSet) ? object.changeSet.map(function (e) { return listening_1.StoreKVPair.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.res !== undefined && (obj.res = message.res ? types_1.ResponseCommit.toJSON(message.res) : undefined);
        if (message.changeSet) {
            obj.changeSet = message.changeSet.map(function (e) { return e ? listening_1.StoreKVPair.toJSON(e) : undefined; });
        }
        else {
            obj.changeSet = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ListenCommitRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseListenCommitRequest();
        message.blockHeight = (_a = object.blockHeight) !== null && _a !== void 0 ? _a : "0";
        message.res = (object.res !== undefined && object.res !== null)
            ? types_1.ResponseCommit.fromPartial(object.res)
            : undefined;
        message.changeSet = ((_b = object.changeSet) === null || _b === void 0 ? void 0 : _b.map(function (e) { return listening_1.StoreKVPair.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseListenCommitResponse() {
    return {};
}
exports.ListenCommitResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseListenCommitResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.ListenCommitResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseListenCommitResponse();
        return message;
    },
};
var ABCIListenerServiceClientImpl = /** @class */ (function () {
    function ABCIListenerServiceClientImpl(rpc) {
        this.rpc = rpc;
        this.ListenFinalizeBlock = this.ListenFinalizeBlock.bind(this);
        this.ListenCommit = this.ListenCommit.bind(this);
    }
    ABCIListenerServiceClientImpl.prototype.ListenFinalizeBlock = function (request, metadata) {
        return this.rpc.unary(exports.ABCIListenerServiceListenFinalizeBlockDesc, exports.ListenFinalizeBlockRequest.fromPartial(request), metadata);
    };
    ABCIListenerServiceClientImpl.prototype.ListenCommit = function (request, metadata) {
        return this.rpc.unary(exports.ABCIListenerServiceListenCommitDesc, exports.ListenCommitRequest.fromPartial(request), metadata);
    };
    return ABCIListenerServiceClientImpl;
}());
exports.ABCIListenerServiceClientImpl = ABCIListenerServiceClientImpl;
exports.ABCIListenerServiceDesc = { serviceName: "cosmos.store.streaming.abci.ABCIListenerService" };
exports.ABCIListenerServiceListenFinalizeBlockDesc = {
    methodName: "ListenFinalizeBlock",
    service: exports.ABCIListenerServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.ListenFinalizeBlockRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ListenFinalizeBlockResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIListenerServiceListenCommitDesc = {
    methodName: "ListenCommit",
    service: exports.ABCIListenerServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.ListenCommitRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ListenCommitResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
