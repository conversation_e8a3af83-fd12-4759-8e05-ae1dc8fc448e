{"version": 3, "sources": ["../../src/core/authenticationKey.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { sha3_256 as sha3Hash } from \"@noble/hashes/sha3\";\nimport { AccountAddress } from \"./accountAddress\";\nimport type { AccountPub<PERSON><PERSON>ey } from \"./crypto\";\nimport { Hex } from \"./hex\";\nimport { AuthenticationKeyScheme, HexInput } from \"../types\";\nimport { Serializable, Serializer } from \"../bcs/serializer\";\nimport { Deserializer } from \"../bcs/deserializer\";\n\n/**\n * Represents an authentication key used for account management. Each account stores an authentication key that enables account\n * owners to rotate their private key(s) without changing the address that hosts their account. The authentication key is a\n * SHA3-256 hash of data and is always 32 bytes in length.\n *\n * @see {@link https://aptos.dev/concepts/accounts | Account Basics}\n *\n * Account addresses can be derived from the AuthenticationKey.\n * @group Implementation\n * @category Serialization\n */\nexport class Authentication<PERSON>ey extends Serializable {\n  /**\n   * An authentication key is always a SHA3-256 hash of data, and is always 32 bytes.\n   *\n   * The data to hash depends on the underlying public key type and the derivation scheme.\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The raw bytes of the authentication key.\n   * @group Implementation\n   * @category Serialization\n   */\n  public readonly data: Hex;\n\n  /**\n   * Creates an instance of the AuthenticationKey using the provided hex input.\n   * This ensures that the hex input is valid and conforms to the required length for an Authentication Key.\n   *\n   * @param args - The arguments for constructing the AuthenticationKey.\n   * @param args.data - The hex input data to be used for the Authentication Key.\n   * @throws {Error} Throws an error if the length of the provided hex input is not equal to the required Authentication Key\n   * length.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(args: { data: HexInput }) {\n    super();\n    const { data } = args;\n    const hex = Hex.fromHexInput(data);\n    if (hex.toUint8Array().length !== AuthenticationKey.LENGTH) {\n      throw new Error(`Authentication Key length should be ${AuthenticationKey.LENGTH}`);\n    }\n    this.data = hex;\n  }\n\n  /**\n   * Serializes the fixed bytes data into a format suitable for transmission or storage.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category Serialization\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeFixedBytes(this.data.toUint8Array());\n  }\n\n  /**\n   * Deserialize an AuthenticationKey from the byte buffer in a Deserializer instance.\n   * @param deserializer - The deserializer to deserialize the AuthenticationKey from.\n   * @returns An instance of AuthenticationKey.\n   * @group Implementation\n   * @category Serialization\n   */\n  static deserialize(deserializer: Deserializer): AuthenticationKey {\n    const bytes = deserializer.deserializeFixedBytes(AuthenticationKey.LENGTH);\n    return new AuthenticationKey({ data: bytes });\n  }\n\n  /**\n   * Convert the internal data representation to a Uint8Array.\n   *\n   * This function is useful for obtaining a byte representation of the data, which can be utilized for serialization or transmission.\n   *\n   * @returns Uint8Array representation of the internal data.\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.data.toUint8Array();\n  }\n\n  /**\n   * Generates an AuthenticationKey from the specified scheme and input bytes.\n   * This function is essential for creating a valid authentication key based on a given scheme.\n   *\n   * @param args - The arguments for generating the AuthenticationKey.\n   * @param args.scheme - The authentication key scheme to use.\n   * @param args.input - The input data in hexadecimal format to derive the key.\n   * @returns An instance of AuthenticationKey containing the generated key data.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromSchemeAndBytes(args: { scheme: AuthenticationKeyScheme; input: HexInput }): AuthenticationKey {\n    const { scheme, input } = args;\n    const inputBytes = Hex.fromHexInput(input).toUint8Array();\n    const hashInput = new Uint8Array([...inputBytes, scheme]);\n    const hash = sha3Hash.create();\n    hash.update(hashInput);\n    const hashDigest = hash.digest();\n    return new AuthenticationKey({ data: hashDigest });\n  }\n\n  /**\n   * Derives an AuthenticationKey from the provided public key using a specified derivation scheme.\n   *\n   * @deprecated Use `fromPublicKey` instead.\n   * @param args - The arguments for deriving the authentication key.\n   * @param args.publicKey - The public key used for the derivation.\n   * @param args.scheme - The scheme to use for deriving the authentication key.\n   * @group Implementation\n   * @category Serialization\n   */\n  public static fromPublicKeyAndScheme(args: { publicKey: AccountPublicKey; scheme: AuthenticationKeyScheme }) {\n    const { publicKey } = args;\n    return publicKey.authKey();\n  }\n\n  /**\n   * Converts a PublicKey to an AuthenticationKey using the derivation scheme inferred from the provided PublicKey instance.\n   *\n   * @param args - The arguments for the function.\n   * @param args.publicKey - The PublicKey to be converted.\n   * @returns AuthenticationKey - The derived AuthenticationKey.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromPublicKey(args: { publicKey: AccountPublicKey }): AuthenticationKey {\n    const { publicKey } = args;\n    return publicKey.authKey();\n  }\n\n  /**\n   * Derives an account address from an AuthenticationKey by translating the AuthenticationKey bytes directly to an AccountAddress.\n   *\n   * @returns AccountAddress - The derived account address.\n   * @group Implementation\n   * @category Serialization\n   */\n  derivedAddress(): AccountAddress {\n    return new AccountAddress(this.data.toUint8Array());\n  }\n}\n"], "mappings": "2HAGA,OAAS,YAAYA,MAAgB,qBAmB9B,IAAMC,EAAN,MAAMA,UAA0BC,CAAa,CA4BlD,YAAYC,EAA0B,CACpC,MAAM,EACN,GAAM,CAAE,KAAAC,CAAK,EAAID,EACXE,EAAMC,EAAI,aAAaF,CAAI,EACjC,GAAIC,EAAI,aAAa,EAAE,SAAWJ,EAAkB,OAClD,MAAM,IAAI,MAAM,uCAAuCA,EAAkB,MAAM,EAAE,EAEnF,KAAK,KAAOI,CACd,CASA,UAAUE,EAA8B,CACtCA,EAAW,oBAAoB,KAAK,KAAK,aAAa,CAAC,CACzD,CASA,OAAO,YAAYC,EAA+C,CAChE,IAAMC,EAAQD,EAAa,sBAAsBP,EAAkB,MAAM,EACzE,OAAO,IAAIA,EAAkB,CAAE,KAAMQ,CAAM,CAAC,CAC9C,CAWA,cAA2B,CACzB,OAAO,KAAK,KAAK,aAAa,CAChC,CAaA,OAAO,mBAAmBN,EAA+E,CACvG,GAAM,CAAE,OAAAO,EAAQ,MAAAC,CAAM,EAAIR,EACpBS,EAAaN,EAAI,aAAaK,CAAK,EAAE,aAAa,EAClDE,EAAY,IAAI,WAAW,CAAC,GAAGD,EAAYF,CAAM,CAAC,EAClDI,EAAOC,EAAS,OAAO,EAC7BD,EAAK,OAAOD,CAAS,EACrB,IAAMG,EAAaF,EAAK,OAAO,EAC/B,OAAO,IAAIb,EAAkB,CAAE,KAAMe,CAAW,CAAC,CACnD,CAYA,OAAc,uBAAuBb,EAAwE,CAC3G,GAAM,CAAE,UAAAc,CAAU,EAAId,EACtB,OAAOc,EAAU,QAAQ,CAC3B,CAWA,OAAO,cAAcd,EAA0D,CAC7E,GAAM,CAAE,UAAAc,CAAU,EAAId,EACtB,OAAOc,EAAU,QAAQ,CAC3B,CASA,gBAAiC,CAC/B,OAAO,IAAIC,EAAe,KAAK,KAAK,aAAa,CAAC,CACpD,CACF,EAtIajB,EAQK,OAAiB,GAR5B,IAAMkB,EAANlB", "names": ["sha3Hash", "_AuthenticationKey", "Serializable", "args", "data", "hex", "Hex", "serializer", "deserializer", "bytes", "scheme", "input", "inputBytes", "hashInput", "hash", "sha3Hash", "hashDigest", "public<PERSON>ey", "Account<PERSON><PERSON><PERSON>", "AuthenticationKey"]}