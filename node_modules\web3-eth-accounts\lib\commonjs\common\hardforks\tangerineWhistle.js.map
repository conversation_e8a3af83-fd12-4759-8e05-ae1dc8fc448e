{"version": 3, "file": "tangerineWhistle.js", "sourceRoot": "", "sources": ["../../../../src/common/hardforks/tangerineWhistle.ts"], "names": [], "mappings": ";;AAAA,kBAAe;IACd,IAAI,EAAE,kBAAkB;IACxB,OAAO,EAAE,wDAAwD;IACjE,GAAG,EAAE,wCAAwC;IAC7C,MAAM,EAAE,OAAO;IACf,SAAS,EAAE,EAAE;IACb,SAAS,EAAE;QACV,KAAK,EAAE;YACN,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,0BAA0B;SAC7B;QACD,IAAI,EAAE;YACL,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,oDAAoD;SACvD;QACD,WAAW,EAAE;YACZ,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,oCAAoC;SACvC;QACD,WAAW,EAAE;YACZ,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,oCAAoC;SACvC;QACD,OAAO,EAAE;YACR,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,gCAAgC;SACnC;QACD,YAAY,EAAE;YACb,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,qCAAqC;SACxC;QACD,QAAQ,EAAE;YACT,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,iCAAiC;SACpC;QACD,YAAY,EAAE;YACb,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,qCAAqC;SACxC;KACD;IACD,EAAE,EAAE,EAAE;IACN,GAAG,EAAE,EAAE;CACP,CAAC"}