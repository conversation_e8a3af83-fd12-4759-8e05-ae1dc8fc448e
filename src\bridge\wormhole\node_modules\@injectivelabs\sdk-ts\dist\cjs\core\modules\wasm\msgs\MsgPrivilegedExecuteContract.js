"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgPrivilegedExecuteContract extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgPrivilegedExecuteContract(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgPrivilegedExecuteContract.create();
        message.sender = params.sender;
        message.funds = params.funds;
        message.contractAddress = params.contractAddress;
        message.data = params.data.toExecJSON();
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgPrivilegedExecuteContract.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgPrivilegedExecuteContract',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'exchange/MsgPrivilegedExecuteContract',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgPrivilegedExecuteContract',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgPrivilegedExecuteContract',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgPrivilegedExecuteContract.encode(this.toProto()).finish();
    }
}
exports.default = MsgPrivilegedExecuteContract;
