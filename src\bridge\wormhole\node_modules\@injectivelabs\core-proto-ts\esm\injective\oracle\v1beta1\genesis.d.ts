import _m0 from "protobufjs/minimal.js";
import { BandIBCParams, BandOracleRequest, BandPriceState, ChainlinkPriceState, CoinbasePriceState, Params, PriceFeedState, PriceRecords, ProviderState, PythPriceState, StorkPriceState } from "./oracle";
export declare const protobufPackage = "injective.oracle.v1beta1";
/** GenesisState defines the oracle module's genesis state. */
export interface GenesisState {
    /** params defines all the parameters of related to oracle. */
    params: Params | undefined;
    bandRelayers: string[];
    bandPriceStates: BandPriceState[];
    priceFeedPriceStates: PriceFeedState[];
    coinbasePriceStates: CoinbasePriceState[];
    bandIbcPriceStates: BandPriceState[];
    bandIbcOracleRequests: BandOracleRequest[];
    bandIbcParams: BandIBCParams | undefined;
    bandIbcLatestClientId: string;
    calldataRecords: CalldataRecord[];
    bandIbcLatestRequestId: string;
    chainlinkPriceStates: ChainlinkPriceState[];
    historicalPriceRecords: PriceRecords[];
    providerStates: ProviderState[];
    pythPriceStates: PythPriceState[];
    storkPriceStates: StorkPriceState[];
    storkPublishers: string[];
}
export interface CalldataRecord {
    clientId: string;
    calldata: Uint8Array;
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
export declare const CalldataRecord: {
    encode(message: CalldataRecord, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CalldataRecord;
    fromJSON(object: any): CalldataRecord;
    toJSON(message: CalldataRecord): unknown;
    create(base?: DeepPartial<CalldataRecord>): CalldataRecord;
    fromPartial(object: DeepPartial<CalldataRecord>): CalldataRecord;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
