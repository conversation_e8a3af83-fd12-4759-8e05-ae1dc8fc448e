/*! For license information please see matic.umd.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("@ethereumjs/util"),require("buffer"),require("bn.js"),require("rlp"),require("@ethereumjs/trie"),require("@ethereumjs/block"),require("@ethereumjs/common"),require("node-fetch")):"function"==typeof define&&define.amd?define("matic",["@ethereumjs/util","buffer","bn.js","rlp","@ethereumjs/trie","@ethereumjs/block","@ethereumjs/common","node-fetch"],e):"object"==typeof exports?exports.matic=e(require("@ethereumjs/util"),require("buffer"),require("bn.js"),require("rlp"),require("@ethereumjs/trie"),require("@ethereumjs/block"),require("@ethereumjs/common"),require("node-fetch")):t.matic=e(t["@ethereumjs/util"],t.buffer,t["bn.js"],t.rlp,t["@ethereumjs/trie"],t["@ethereumjs/block"],t["@ethereumjs/common"],t["node-fetch"])}(self,((t,e,r,n,o,i,a,s)=>(()=>{var c={861:(t,e,r)=>{var n=r(18),o=n.Buffer;function i(t,e){for(var r in t)e[r]=t[r]}function a(t,e,r){return o(t,e,r)}o.from&&o.alloc&&o.allocUnsafe&&o.allocUnsafeSlow?t.exports=n:(i(n,e),e.Buffer=a),i(o,a),a.from=function(t,e,r){if("number"==typeof t)throw new TypeError("Argument must not be a number");return o(t,e,r)},a.alloc=function(t,e,r){if("number"!=typeof t)throw new TypeError("Argument must be a number");var n=o(t);return void 0!==e?"string"==typeof r?n.fill(e,r):n.fill(e):n.fill(0),n},a.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return o(t)},a.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},804:t=>{"use strict";t.exports=i},858:t=>{"use strict";t.exports=a},935:t=>{"use strict";t.exports=o},335:e=>{"use strict";e.exports=t},773:t=>{"use strict";t.exports=r},18:t=>{"use strict";t.exports=e},849:t=>{"use strict";t.exports=s},514:t=>{"use strict";t.exports=n}},u={};function f(t){var e=u[t];if(void 0!==e)return e.exports;var r=u[t]={exports:{}};return c[t](r,r.exports,f),r.exports}f.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return f.d(e,{a:e}),e},f.d=(t,e)=>{for(var r in e)f.o(e,r)&&!f.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},f.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),f.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var h={};return(()=>{"use strict";f.r(h),f.d(h,{ABIManager:()=>Lt,ADDRESS_ZERO:()=>_t,BN:()=>ft(),BaseBigNumber:()=>a,BaseContract:()=>i,BaseContractMethod:()=>n,BaseToken:()=>Ot,BaseWeb3Client:()=>o,BridgeClient:()=>Ut,BridgeUtil:()=>ve,BufferUtil:()=>ht,Converter:()=>u,DAI_PERMIT_TYPEHASH:()=>Bt,EIP_2612_DOMAIN_TYPEHASH:()=>It,EIP_2612_PERMIT_TYPEHASH:()=>At,ERROR_TYPE:()=>r,EventBus:()=>d,ExitUtil:()=>Kt,GasSwapper:()=>ae,HttpRequest:()=>bt,Keccak:()=>it,Log_Event_Signature:()=>e,Logger:()=>y,MAX_AMOUNT:()=>Et,NetworkService:()=>St,POSClient:()=>ce,Permit:()=>ot,ProofUtil:()=>vt,RootChain:()=>te,RootChainManager:()=>Jt,UNISWAP_DOMAIN_TYPEHASH:()=>Tt,Web3SideChainClient:()=>kt,ZkEVMWrapper:()=>be,ZkEvmBridge:()=>me,ZkEvmBridgeClient:()=>Gt,ZkEvmClient:()=>ke,_GLOBAL_INDEX_MAINNET_FLAG:()=>Rt,default:()=>xe,eventBusPromise:()=>l,mapPromise:()=>w,merge:()=>m,promiseAny:()=>Ct,promiseResolve:()=>xt,resolve:()=>Wt,service:()=>Ft,setProofApi:()=>jt,setZkEvmProofApi:()=>Ht,throwNotImplemented:()=>qt,use:()=>p,utils:()=>zt});var t,e,r,n=function(t){this.logger=t},o=function(){function t(t){this.logger=t}return t.prototype.getRootHash=function(t,e){return this.sendRPCRequest({jsonrpc:"2.0",method:"eth_getRootHash",params:[Number(t),Number(e)],id:(new Date).getTime()}).then((function(t){return String(t.result)}))},t.prototype.getAccountsUsingRPC_=function(){return this.sendRPCRequest({jsonrpc:"2.0",method:"eth_accounts",params:[],id:(new Date).getTime()}).then((function(t){return t.result}))},t}(),i=function(t,e){this.address=t,this.logger=e},a=function(){function t(){}return t.isBN=function(t){return qt()},t}(),s=(t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)},function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}),c=function(t){function e(e){return t.call(this)||this}return s(e,t),e.prototype.toString=function(t){return qt()},e.prototype.toNumber=function(){return qt()},e.prototype.add=function(t){return qt()},e.prototype.sub=function(t){return qt()},e.prototype.mul=function(t){return qt()},e.prototype.div=function(t){return qt()},e.prototype.lte=function(t){return qt()},e.prototype.lt=function(t){return qt()},e.prototype.gte=function(t){return qt()},e.prototype.gt=function(t){return qt()},e.prototype.eq=function(t){return qt()},e}(a),u=function(){function t(){}return t.toHex=function(t){var e=typeof t;if("number"===e)t=new zt.BN(t);else if("string"===e){if("0x"===t.slice(0,2))return t;t=new zt.BN(t)}if(zt.BN.isBN(t))return"0x"+t.toString(16);throw new Error("Invalid value ".concat(t,", value is not a number."))},t.toBN=function(t){return"string"==typeof t&&"0x"===t.slice(0,2)&&(t=parseInt(t,16)),zt.BN.isBN(t)||(t=new zt.BN(t)),t},t}(),p=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n="function"==typeof t?new t:t;return n.setup.apply(n,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([ue],e,!1))},l=function(t){var e=new Promise(t),r=new d;return e.on=r.on.bind(r),e.emit=r.emit.bind(r),e},d=function(){function t(t){this._events={},this._ctx=t}return t.prototype.on=function(t,e){return null==this._events[t]&&(this._events[t]=[]),this._events[t].push(e),this},t.prototype.off=function(t,e){if(this._events[t])if(e){var r=this._events[t].indexOf(e);this._events[t].splice(r,1)}else this._events[t]=[]},t.prototype.emit=function(t){for(var e=this,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=this._events[t]||[];return Promise.all(o.map((function(t){var n=t.call.apply(t,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([e._ctx],r,!1));return n&&n.then?n:Promise.resolve(n)})))},t.prototype.destroy=function(){this._events=null,this._ctx=null},t}();!function(t){t.Erc20Transfer="0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",t.Erc721Transfer="0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",t.Erc1155Transfer="0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62",t.Erc721BatchTransfer="0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df",t.Erc1155BatchTransfer="0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb",t.Erc721TransferWithMetadata="0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14"}(e||(e={})),function(t){t.AllowedOnRoot="allowed_on_root",t.AllowedOnChild="allowed_on_child",t.Unknown="unknown",t.ProofAPINotSet="proof_api_not_set",t.TransactionOptionNotObject="transation_object_not_object",t.BurnTxNotCheckPointed="burn_tx_not_checkpointed",t.EIP1559NotSupported="eip-1559_not_supported",t.NullSpenderAddress="null_spender_address",t.AllowedOnNonNativeTokens="allowed_on_non_native_token",t.AllowedOnMainnet="allowed_on_mainnet",t.BridgeAdapterNotFound="bridge_adapter_address_not_passed"}(r||(r={}));var g=function(){function t(t,e){this.type=t,this.message=this.getMsg_(e)}return t.prototype.throw=function(){throw this.get()},t.prototype.get=function(){return{message:this.message,type:this.type}},t.prototype.getMsg_=function(t){var e;switch(this.type){case r.AllowedOnChild:e="The action ".concat(t," is allowed only on child token.");break;case r.AllowedOnRoot:e="The action ".concat(t," is allowed only on root token.");break;case r.AllowedOnMainnet:e="The action is allowed only on mainnet chains.";break;case r.ProofAPINotSet:e='Proof api is not set, please set it using "setProofApi"';break;case r.BurnTxNotCheckPointed:e="Burn transaction has not been checkpointed as yet";break;case r.EIP1559NotSupported:e="".concat(t?"Root":"Child"," chain doesn't support eip-1559");break;case r.NullSpenderAddress:e="Please provide spender address.";break;default:this.type||(this.type=r.Unknown),e=this.message}return e},t}(),y=function(){function t(){}return t.prototype.enableLog=function(t){this.isEnabled=!!t},t.prototype.log=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.isEnabled&&console.log.apply(console,t)},t.prototype.error=function(t,e){return new g(t,e)},t}(),m=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Object.assign.apply(Object,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([{}],t,!1))},v=function(t,e){var r=t.map((function(t,r){return e(t,r)}));return Promise.all(r)};function w(t,e,r){void 0===r&&(r={});var n=t.length,o=r.concurrency||n,i=[],a=function(){var r=t.splice(0,o);return v(r,e).then((function(t){return i=i.concat(t),n>i.length?a():xt(i)}))};return a()}var b=f(335);function P(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`positive integer expected, not ${t}`)}function k(t,...e){if(!((r=t)instanceof Uint8Array||null!=r&&"object"==typeof r&&"Uint8Array"===r.constructor.name))throw new Error("Uint8Array expected");var r;if(e.length>0&&!e.includes(t.length))throw new Error(`Uint8Array expected of length ${e}, not of length=${t.length}`)}function x(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function C(t,e){k(t);const r=e.outputLen;if(t.length<r)throw new Error(`digestInto() expects output buffer of length at least ${r}`)}const E={number:P,bool:function(t){if("boolean"!=typeof t)throw new Error(`boolean expected, not ${t}`)},bytes:k,hash:function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");P(t.outputLen),P(t.blockLen)},exists:x,output:C},_=BigInt(2**32-1),B=BigInt(32);function A(t,e=!1){return e?{h:Number(t&_),l:Number(t>>B&_)}:{h:0|Number(t>>B&_),l:0|Number(t&_)}}function I(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let o=0;o<t.length;o++){const{h:i,l:a}=A(t[o],e);[r[o],n[o]]=[i,a]}return[r,n]}const T=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0];function R(t){for(let r=0;r<t.length;r++)t[r]=(e=t[r])<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255;var e}function O(t){return"string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}(t)),k(t),t}class N{clone(){return this._cloneInto()}}const M=[],S=[],F=[],j=BigInt(0),H=BigInt(1),W=BigInt(2),U=BigInt(7),D=BigInt(256),L=BigInt(113);for(let t=0,e=H,r=1,n=0;t<24;t++){[r,n]=[n,(2*r+3*n)%5],M.push(2*(5*n+r)),S.push((t+1)*(t+2)/2%64);let o=j;for(let t=0;t<7;t++)e=(e<<H^(e>>U)*L)%D,e&W&&(o^=H<<(H<<BigInt(t))-H);F.push(o)}const[q,G]=I(F,!0),z=(t,e,r)=>r>32?((t,e,r)=>e<<r-32|t>>>64-r)(t,e,r):((t,e,r)=>t<<r|e>>>32-r)(t,e,r),Z=(t,e,r)=>r>32?((t,e,r)=>t<<r-32|e>>>64-r)(t,e,r):((t,e,r)=>e<<r|t>>>32-r)(t,e,r);class V extends N{constructor(t,e,r,n=!1,o=24){if(super(),this.blockLen=t,this.suffix=e,this.outputLen=r,this.enableXOF=n,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,P(r),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");var i;this.state=new Uint8Array(200),this.state32=(i=this.state,new Uint32Array(i.buffer,i.byteOffset,Math.floor(i.byteLength/4)))}keccak(){T||R(this.state32),function(t,e=24){const r=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let e=0;e<10;e++)r[e]=t[e]^t[e+10]^t[e+20]^t[e+30]^t[e+40];for(let e=0;e<10;e+=2){const n=(e+8)%10,o=(e+2)%10,i=r[o],a=r[o+1],s=z(i,a,1)^r[n],c=Z(i,a,1)^r[n+1];for(let r=0;r<50;r+=10)t[e+r]^=s,t[e+r+1]^=c}let e=t[2],o=t[3];for(let r=0;r<24;r++){const n=S[r],i=z(e,o,n),a=Z(e,o,n),s=M[r];e=t[s],o=t[s+1],t[s]=i,t[s+1]=a}for(let e=0;e<50;e+=10){for(let n=0;n<10;n++)r[n]=t[e+n];for(let n=0;n<10;n++)t[e+n]^=~r[(n+2)%10]&r[(n+4)%10]}t[0]^=q[n],t[1]^=G[n]}r.fill(0)}(this.state32,this.rounds),T||R(this.state32),this.posOut=0,this.pos=0}update(t){x(this);const{blockLen:e,state:r}=this,n=(t=O(t)).length;for(let o=0;o<n;){const i=Math.min(e-this.pos,n-o);for(let e=0;e<i;e++)r[this.pos++]^=t[o++];this.pos===e&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:t,suffix:e,pos:r,blockLen:n}=this;t[r]^=e,128&e&&r===n-1&&this.keccak(),t[n-1]^=128,this.keccak()}writeInto(t){x(this,!1),k(t),this.finish();const e=this.state,{blockLen:r}=this;for(let n=0,o=t.length;n<o;){this.posOut>=r&&this.keccak();const i=Math.min(r-this.posOut,o-n);t.set(e.subarray(this.posOut,this.posOut+i),n),this.posOut+=i,n+=i}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return P(t),this.xofInto(new Uint8Array(t))}digestInto(t){if(C(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){const{blockLen:e,suffix:r,outputLen:n,rounds:o,enableXOF:i}=this;return t||(t=new V(e,r,n,i,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=r,t.outputLen=n,t.enableXOF=i,t.destroyed=this.destroyed,t}}const X=(t,e,r)=>function(t){const e=e=>t().update(O(e)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}((()=>new V(e,t,r))),Y=X(1,144,28),$=X(1,136,32),J=X(1,104,48),K=X(1,72,64);function Q(t){return e=>(E.bytes(e),t(e))}E.bool,E.bytes,(()=>{const t="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0,e="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);e&&!t&&e("crypto")})();const tt=Q(Y),et=(()=>{const t=Q($);return t.create=$.create,t})(),rt=Q(J),nt=Q(K);var ot,it=function(){function t(){}return t.assertIsBuffer=function(t){if(!Buffer.isBuffer(t)){var e="This method only supports Buffer but input was: ".concat(t);throw new Error(e)}},t.keccak=function(e,r){switch(void 0===r&&(r=256),t.assertIsBuffer(e),r){case 224:return Buffer.from(tt(e));case 256:return Buffer.from(et(e));case 384:return Buffer.from(rt(e));case 512:return Buffer.from(nt(e));default:throw new Error("Invald algorithm: keccak".concat(r))}},t.keccak256=function(e){return t.keccak(e)},t}(),at=f(861),st=it.keccak256,ct=function(){function t(t){if(void 0===t&&(t=[]),t.length<1)throw new Error("Atleast 1 leaf needed");var e=Math.ceil(Math.log(t.length)/Math.log(2));if(e>20)throw new Error("Depth must be 20 or less");this.leaves=t.concat(Array.from(Array(Math.pow(2,e)-t.length),(function(){return(0,b.zeros)(32)}))),this.layers=[this.leaves],this.createHashes(this.leaves)}return t.prototype.createHashes=function(t){if(1===t.length)return!1;for(var e=[],r=0;r<t.length;r+=2){var n=t[r],o=t[r+1],i=at.Buffer.concat([n,o]);e.push(st(i))}t.length%2==1&&e.push(t[t.length-1]),this.layers.push(e),this.createHashes(e)},t.prototype.getLeaves=function(){return this.leaves},t.prototype.getLayers=function(){return this.layers},t.prototype.getRoot=function(){return this.layers[this.layers.length-1][0]},t.prototype.getProof=function(t){for(var e=-1,r=0;r<this.leaves.length;r++)0===at.Buffer.compare(t,this.leaves[r])&&(e=r);var n=[];if(e<=this.getLeaves().length){var o=void 0;for(r=0;r<this.layers.length-1;r++)o=e%2==0?e+1:e-1,e=Math.floor(e/2),n.push(this.layers[r][o])}return n},t.prototype.verify=function(t,e,r,n){if(!Array.isArray(n)||!t||!r)return!1;for(var o=t,i=0;i<n.length;i++){var a=n[i];o=st(e%2==0?at.Buffer.concat([o,a]):at.Buffer.concat([a,o])),e=Math.floor(e/2)}return 0===at.Buffer.compare(o,r)},t}(),ut=f(773),ft=f.n(ut),ht=function(){function t(){}return t.padToEven=function(t){var e=t;if("string"!=typeof e)throw new Error("[padToEven] value must be type 'string', received ".concat(typeof e));return e.length%2&&(e="0".concat(e)),e},t.isHexPrefixed=function(t){if("string"!=typeof t)throw new Error("[isHexPrefixed] input must be type 'string', received type ".concat(typeof t));return"0"===t[0]&&"x"===t[1]},t.isHexString=function(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/)||e&&t.length!==2+2*e)},t.intToHex=function(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("Received an invalid integer type: ".concat(t));return"0x".concat(t.toString(16))},t.stripHexPrefix=function(e){if("string"!=typeof e)throw new Error("[stripHexPrefix] input must be type 'string', received ".concat(typeof e));return t.isHexPrefixed(e)?e.slice(2):e},t.intToBuffer=function(e){var r=t.intToHex(e);return Buffer.from(t.padToEven(r.slice(2)),"hex")},t.toBuffer=function(e){if(null==e)return Buffer.allocUnsafe(0);if(Buffer.isBuffer(e))return Buffer.from(e);if(Array.isArray(e)||e instanceof Uint8Array)return Buffer.from(e);if("string"==typeof e){if(!t.isHexString(e))throw new Error("Cannot convert string to buffer. toBuffer only supports 0x-prefixed hex strings and this string was given: ".concat(e));return Buffer.from(t.padToEven(t.stripHexPrefix(e)),"hex")}if("number"==typeof e)return t.intToBuffer(e);if(ft().isBN(e)){if(e.isNeg())throw new Error("Cannot convert negative BN to buffer. Given: ".concat(e));return e.toArrayLike(Buffer)}if(e.toArray)return Buffer.from(e.toArray());if(e.toBuffer)return Buffer.from(e.toBuffer());throw new Error("invalid type")},t.bufferToHex=function(e){return"0x"+(e=t.toBuffer(e)).toString("hex")},t}(),pt=f(514),lt=f.n(pt),dt=f(935),gt=f(804),yt=f(858),mt=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},vt=function(){function t(){}return t.getFastMerkleProof=function(t,e,r,n){return o=this,i=void 0,s=function(){var o,i,a,s,c,u,f,h,p;return mt(this,(function(l){switch(l.label){case 0:o=Math.ceil(Math.log2(n-r+1)),i=[],s=e-(a=r),c=0,u=n-a,f=function(e){var r,n,f,p,l,d,g,y,m,v,w;return mt(this,(function(b){switch(b.label){case 0:return r=Math.pow(2,o-e),s>(n=c+r/2-1)?(f=n+1,[4,h.queryRootHash(t,a+c,a+n)]):[3,2];case 1:return w=b.sent(),i.push(w),c=f,[3,6];case 2:return p=Math.min(u,n),l=o-(e+1),u<=n?(w=h.recursiveZeroHash(l,t),i.push(w),[3,5]):[3,3];case 3:return d=Math.ceil(Math.log2(u-n)),g=l-d,[4,h.queryRootHash(t,a+n+1,a+u)];case 4:y=b.sent(),m=h.recursiveZeroHash(d,t),(v=Array.from({length:Math.pow(2,g)},(function(){return ht.toBuffer(m)})))[0]=y,w=new ct(v).getRoot(),i.push(w),b.label=5;case 5:u=p,b.label=6;case 6:return[2]}}))},h=this,p=0,l.label=1;case 1:return p<o?[5,f(p)]:[3,4];case 2:l.sent(),l.label=3;case 3:return p+=1,[3,1];case 4:return[2,i.reverse()]}}))},new((a=void 0)||(a=Promise))((function(t,e){function r(t){try{c(s.next(t))}catch(t){e(t)}}function n(t){try{c(s.throw(t))}catch(t){e(t)}}function c(e){var o;e.done?t(e.value):(o=e.value,o instanceof a?o:new a((function(t){t(o)}))).then(r,n)}c((s=s.apply(o,i||[])).next())}));var o,i,a,s},t.buildBlockProof=function(e,r,n,o){return t.getFastMerkleProof(e,o,r,n).then((function(t){return ht.bufferToHex(Buffer.concat(t.map((function(t){return ht.toBuffer(t)}))))}))},t.queryRootHash=function(t,e,r){return t.getRootHash(e,r).then((function(t){return ht.toBuffer("0x".concat(t))})).catch((function(t){return null}))},t.recursiveZeroHash=function(t,e){if(0===t)return"0x0000000000000000000000000000000000000000000000000000000000000000";var r=this.recursiveZeroHash(t-1,e);return it.keccak256(ht.toBuffer(e.encodeParameters([r,r],["bytes32","bytes32"])))},t.getReceiptProof=function(e,r,n,o,i){void 0===o&&(o=1/0);var a,s=ht.bufferToHex(t.getStateSyncTxHash(r)),c=new dt.Trie;if(i)a=xt(i);else{var u=[];r.transactions.forEach((function(t){t.transactionHash!==s&&u.push(n.getTransactionReceipt(t.transactionHash))})),a=w(u,(function(t){return t}),{concurrency:o})}return a.then((function(e){return Promise.all(e.map((function(e){var r=lt().encode(e.transactionIndex),n=t.getReceiptBytes(e);return c.put(r,n)})))})).then((function(t){return c.findPath(lt().encode(e.transactionIndex),!0)})).then((function(n){if(n.remaining.length>0)throw new Error("Node does not contain the key");return{blockHash:ht.toBuffer(e.blockHash),parentNodes:n.stack.map((function(t){return t.raw()})),root:t.getRawHeader(r).receiptTrie,path:lt().encode(e.transactionIndex),value:t.isTypedReceipt(e)?n.node.value:lt().decode(n.node.value.toString())}}))},t.isTypedReceipt=function(t){var e=u.toHex(t.type);return null!=t.status&&"0x0"!==e&&"0x"!==e},t.getStateSyncTxHash=function(t){return it.keccak256(Buffer.concat([Buffer.from("matic-bor-receipt-","utf-8"),(0,b.setLengthLeft)(ht.toBuffer(t.number),8),ht.toBuffer(t.hash)]))},t.getReceiptBytes=function(e){var r=lt().encode([ht.toBuffer(void 0!==e.status&&null!=e.status?e.status?"0x1":"0x":e.root),ht.toBuffer(e.cumulativeGasUsed),ht.toBuffer(e.logsBloom),e.logs.map((function(t){return[ht.toBuffer(t.address),t.topics.map(ht.toBuffer),ht.toBuffer(t.data)]}))]);return t.isTypedReceipt(e)&&(r=Buffer.concat([ht.toBuffer(e.type),r])),r},t.getRawHeader=function(t){t.difficulty=u.toHex(t.difficulty);var e=new yt.Common({chain:yt.Chain.Mainnet,hardfork:yt.Hardfork.London});return gt.BlockHeader.fromHeaderData(t,{common:e,skipConsensusFormatValidation:!0})},t}(),wt="node"===process.env.BUILD_ENV?f(849).default:window.fetch,bt=function(){function t(t){void 0===t&&(t={}),this.baseUrl="",(t="string"==typeof t?{baseUrl:t}:t).baseUrl&&(this.baseUrl=t.baseUrl)}return t.prototype.get=function(t,e){return void 0===t&&(t=""),void 0===e&&(e={}),t=this.baseUrl+t+Object.keys(e).map((function(t){return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(e[t]))})).join("&"),wt(t,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"}}).then((function(t){return t.json()}))},t.prototype.post=function(t,e){return void 0===t&&(t=""),t=this.baseUrl+t,wt(t,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:e?JSON.stringify(e):null}).then((function(t){return t.json()}))},t}(),Pt={1:"Main",5:"Main",11155111:"Main",137:"Matic",80001:"Matic",80002:"Matic",1442:"zkEVM",2442:"zkEVM",1101:"zkEVM"},kt=function(){function t(){this.logger=new y}return t.prototype.init=function(t){(t=t||{}).parent.defaultConfig=t.parent.defaultConfig||{},t.child.defaultConfig=t.child.defaultConfig||{},this.config=t;var e=zt.Web3Client;if(!e)throw new Error("Web3Client is not set");zt.UnstoppableDomains&&(this.resolution=zt.UnstoppableDomains),this.parent=new e(t.parent.provider,this.logger),this.child=new e(t.child.provider,this.logger),this.logger.enableLog(t.log);var r=t.network,n=t.version,o=this.abiManager=new Lt(r,n);return this.logger.log("init called",o),o.init().catch((function(t){throw new Error("network ".concat(r," - ").concat(n," is not supported"))}))},t.prototype.getABI=function(t,e){return this.abiManager.getABI(t,e)},t.prototype.getConfig=function(t){return this.abiManager.getConfig(t)},Object.defineProperty(t.prototype,"mainPlasmaContracts",{get:function(){return this.getConfig("Main.Contracts")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mainPOSContracts",{get:function(){return this.getConfig("Main.POSContracts")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mainZkEvmContracts",{get:function(){return this.getConfig("Main.Contracts")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"zkEvmContracts",{get:function(){return this.getConfig("zkEVM.Contracts")},enumerable:!1,configurable:!0}),t.prototype.isEIP1559Supported=function(t){return this.getConfig("".concat(Pt[t],".SupportsEIP1559"))},t}(),xt=function(t){return Promise.resolve(t)},Ct=function(t){var e=new Array(t.length),r=0;return new Promise((function(n,o){t.forEach((function(i){Promise.resolve(i).then(n).catch((function(n){e[r]=n,(r+=1)===t.length&&o(e)}))}))}))},Et="0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",_t="0x0000000000000000000000000000000000000000",Bt="0xea2aa0a1be11a07ed86d755c93467f4f82362b452371d1ba94d1715123511acb",At="0x6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9",It="0x8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f",Tt="0x8cad95687ba82c2ce50e74f7b754645e5117c3a5bec8151c0726d5857980a866",Rt=BigInt(Math.pow(2,64));!function(t){t.DAI="DAI",t.EIP_2612="EIP_2612",t.UNISWAP="UNISWAP"}(ot||(ot={}));var Ot=function(){function t(t,e){this.contractParam=t,this.client=e}return Object.defineProperty(t.prototype,"contractAddress",{get:function(){return this.contractParam.address},enumerable:!1,configurable:!0}),t.prototype.getContract=function(){var t=this;if(this.contract_)return xt(this.contract_);var e=this.contractParam;return this.client.getABI(e.name,e.bridgeType).then((function(r){return t.contract_=t.getContract_({abi:r,isParent:e.isParent,tokenAddress:e.address}),t.contract_}))},t.prototype.getChainId=function(){var t=this;return this.chainId_?xt(this.chainId_):this.getClient(this.contractParam.isParent).getChainId().then((function(e){return t.chainId_=e,t.chainId_}))},t.prototype.processWrite=function(t,e){var r=this;return void 0===e&&(e={}),this.validateTxOption_(e),this.client.logger.log("process write"),this.createTransactionConfig({txConfig:e,isWrite:!0,method:t,isParent:this.contractParam.isParent}).then((function(n){return r.client.logger.log("process write config"),e.returnTransaction?m(n,{data:t.encodeABI(),to:t.address}):t.write(n)}))},t.prototype.sendTransaction=function(t){void 0===t&&(t={}),this.validateTxOption_(t);var e=this.contractParam.isParent,r=this.getClient(e);return r.logger.log("process write"),this.createTransactionConfig({txConfig:t,isWrite:!0,method:null,isParent:this.contractParam.isParent}).then((function(e){return r.logger.log("process write config"),t.returnTransaction?e:r.write(e)}))},t.prototype.readTransaction=function(t){void 0===t&&(t={}),this.validateTxOption_(t);var e=this.contractParam.isParent,r=this.getClient(e);return r.logger.log("process read"),this.createTransactionConfig({txConfig:t,isWrite:!0,method:null,isParent:this.contractParam.isParent}).then((function(e){return r.logger.log("write tx config created"),t.returnTransaction?e:r.read(e)}))},t.prototype.validateTxOption_=function(t){("object"!=typeof t||Array.isArray(t))&&new g(r.TransactionOptionNotObject).throw()},t.prototype.processRead=function(t,e){var r=this;return void 0===e&&(e={}),this.validateTxOption_(e),this.client.logger.log("process read"),this.createTransactionConfig({txConfig:e,isWrite:!1,method:t,isParent:this.contractParam.isParent}).then((function(n){return r.client.logger.log("read tx config created"),e.returnTransaction?m(n,{data:t.encodeABI(),to:r.contract_.address}):t.read(n)}))},t.prototype.getClient=function(t){return t?this.client.parent:this.client.child},t.prototype.getContract_=function(t){var e=t.isParent,r=t.tokenAddress,n=t.abi;return this.getClient(e).getContract(r,n)},Object.defineProperty(t.prototype,"parentDefaultConfig",{get:function(){return this.client.config.parent.defaultConfig},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childDefaultConfig",{get:function(){return this.client.config.child.defaultConfig},enumerable:!1,configurable:!0}),t.prototype.createTransactionConfig=function(t){var e=this,n=t.txConfig,o=t.method,i=t.isParent,a=t.isWrite,s=i?this.parentDefaultConfig:this.childDefaultConfig;n=m(s,n||{});var c=i?this.client.parent:this.client.child;return c.logger.log("txConfig",n,"onRoot",i,"isWrite",a),a?this.getChainId().then((function(t){var a,s,u,f,h,p=n.maxFeePerGas,l=n.maxPriorityFeePerGas,d=e.client.isEIP1559Supported(t),g=p||l;return n.chainId=n.chainId||t,!d&&g&&c.logger.error(r.EIP1559NotSupported,i).throw(),Promise.all([n.gasLimit?n.gasLimit:(a={from:n.from,value:n.value,to:n.to},s=e,u=void 0,f=void 0,h=function(){var t,e;return function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}(this,(function(r){switch(r.label){case 0:return o?[4,o.estimateGas(a)]:[3,2];case 1:return e=r.sent(),[3,4];case 2:return[4,c.estimateGas(a)];case 3:e=r.sent(),r.label=4;case 4:return t=e,[2,new zt.BN(Math.trunc(1.15*Number(t))).toString()]}}))},new(f||(f=Promise))((function(t,e){function r(t){try{o(h.next(t))}catch(t){e(t)}}function n(t){try{o(h.throw(t))}catch(t){e(t)}}function o(e){var o;e.done?t(e.value):(o=e.value,o instanceof f?o:new f((function(t){t(o)}))).then(r,n)}o((h=h.apply(s,u||[])).next())}))),n.nonce?n.nonce:c.getTransactionCount(n.from,"pending")]).then((function(t){var e=t[0],r=t[1];return c.logger.log("options filled"),n.gasLimit=Number(e),n.nonce=r,n}))})):xt(n)},t.prototype.transferERC20=function(t,e,r){var n=this;return this.getContract().then((function(o){var i=o.method("transfer",t,u.toHex(e));return n.processWrite(i,r)}))},t.prototype.transferERC721=function(t,e,r,n){var o=this;return this.getContract().then((function(i){var a=i.method("transferFrom",t,e,r);return o.processWrite(a,n)}))},t.prototype.checkForNonNative=function(t){this.contractParam.address===_t&&this.client.logger.error(r.AllowedOnNonNativeTokens,t).throw()},t.prototype.checkForRoot=function(t){this.contractParam.isParent||this.client.logger.error(r.AllowedOnRoot,t).throw()},t.prototype.checkForChild=function(t){this.contractParam.isParent&&this.client.logger.error(r.AllowedOnChild,t).throw()},t.prototype.checkAdapterPresent=function(t){this.contractParam.bridgeAdapterAddress||this.client.logger.error(r.BridgeAdapterNotFound,t).throw()},t.prototype.transferERC1155=function(t,e){var r=this;return this.getContract().then((function(n){var o=n.method("safeTransferFrom",t.from,t.to,u.toHex(t.tokenId),u.toHex(t.amount),t.data||"0x");return r.processWrite(o,e)}))},t}(),Nt=function(){function t(t){this.httpRequest=new bt(t)}return t.prototype.getABI=function(t,e,r,n){var o="".concat(t,"/").concat(e,"/artifacts/").concat(r,"/").concat(n,".json");return this.httpRequest.get(o).then((function(t){return t.abi}))},t.prototype.getAddress=function(t,e){var r="".concat(t,"/").concat(e,"/index.json");return this.httpRequest.get(r)},t}(),Mt={abiStoreUrl:"https://static.polygon.technology/network/",zkEvmBridgeService:"https://proof-generator.polygon.technology/"},St=function(){function t(t){this.httpRequest=new bt(t)}return t.prototype.createUrlForPos=function(t,e){return"".concat("v1"===t?"matic":t).concat(e)},t.prototype.createUrlForZkEvm=function(t,e){return"".concat(t,"/").concat(e)},t.prototype.getBlockIncluded=function(t,e){var r=this.createUrlForPos(t,"/block-included/".concat(e));return this.httpRequest.get(r).then((function(t){var e=t.headerBlockNumber,r="0x"===e.slice(0,2)?parseInt(e,16):e;return t.headerBlockNumber=new zt.BN(r),t}))},t.prototype.getExitProof=function(t,e,r){var n=this.createUrlForPos(t,"/exit-payload/".concat(e,"?eventSignature=").concat(r));return this.httpRequest.get(n).then((function(t){return t.result}))},t.prototype.getProof=function(t,e,r,n){var o=this.createUrlForPos(t,"/fast-merkle-proof?start=".concat(e,"&end=").concat(r,"&number=").concat(n));return this.httpRequest.get(o).then((function(t){return t.proof}))},t.prototype.getMerkleProofForZkEvm=function(t,e,r){var n=this.createUrlForZkEvm(t,"merkle-proof?net_id=".concat(e,"&deposit_cnt=").concat(r));return this.httpRequest.get(n).then((function(t){return t.proof}))},t.prototype.getBridgeTransactionDetails=function(t,e,r){var n=this.createUrlForZkEvm(t,"bridge?net_id=".concat(e,"&deposit_cnt=").concat(r));return this.httpRequest.get(n).then((function(t){return t.deposit}))},t}(),Ft=new function(){};Ft.abi=new Nt(Mt.abiStoreUrl);var jt=function(t){"/"!==t[t.length-1]&&(t+="/"),t+="api/v1/",Ft.network=new St(t)},Ht=function(t){"/"!==t[t.length-1]&&(t+="/"),t+="api/zkevm/",Ft.zkEvmNetwork=new St(t)};function Wt(t,e){return(Array.isArray(e)?e:e.split(".")).reduce((function(t,e){return t&&t[e]}),t)}var Ut=function(){function t(){this.client=new kt}return t.prototype.isCheckPointed=function(t){return this.exitUtil.isCheckPointed(t)},t.prototype.isDeposited=function(t){var e=this.client,r=new Ot({address:e.abiManager.getConfig("Matic.GenesisContracts.StateReceiver"),isParent:!1,name:"StateReceiver",bridgeType:"genesis"},e);return r.getContract().then((function(n){return Promise.all([e.parent.getTransactionReceipt(t),r.processRead(n.method("lastStateId"))])})).then((function(t){var r=t[0],n=t[1],o=r.logs.find((function(t){return"0x103fed9db65eac19c4d870f49ab7520fe03b99f1838e5996caf47e9e43308392"===t.topics[0]}));if(!o)throw new Error("StateSynced event not found");var i=e.child.decodeParameters(o.topics[1],["uint256"])[0],a=zt.BN.isBN(i)?i:new zt.BN(i);return new zt.BN(n).gte(a)}))},t}(),Dt={},Lt=function(){function t(t,e){this.networkName=t,this.version=e}return t.prototype.init=function(){var t=this;return Ft.abi.getAddress(this.networkName,this.version).then((function(e){var r;Dt[t.networkName]=((r={})[t.version]={address:e,abi:{}},r)}))},t.prototype.getConfig=function(t){return Wt(Dt[this.networkName][this.version].address,t)},t.prototype.getABI=function(t,e){var r,n=this;if(void 0===e&&(e="plasma"),Dt[this.networkName]&&Dt[this.networkName][this.version]&&Dt[this.networkName][this.version].abi&&(r=Dt[this.networkName][this.version].abi[e]),r){var o=r[t];if(o)return xt(o)}return Ft.abi.getABI(this.networkName,this.version,e,t).then((function(r){return n.setABI(t,e,r),r}))},t.prototype.setABI=function(t,e,r){var n=Dt[this.networkName][this.version].abi;n[e]||(n[e]={}),n[e][t]=r},t}(),qt=function(){throw new Error("not implemented")},Gt=function(){function t(){this.client=new kt}return t.prototype.isDepositClaimable=function(t){var e=this;return Promise.all([this.rootChainBridge.networkID(),this.bridgeUtil.getBridgeLogData(t,!0)]).then((function(t){return Ft.zkEvmNetwork.getBridgeTransactionDetails(e.client.config.version,t[0],t[1].depositCount)})).then((function(t){return t.ready_for_claim}))},t.prototype.isWithdrawExitable=function(t){var e=this;return Promise.all([this.childChainBridge.networkID(),this.bridgeUtil.getBridgeLogData(t,!1)]).then((function(t){return Ft.zkEvmNetwork.getBridgeTransactionDetails(e.client.config.version,t[0],t[1].depositCount)})).then((function(t){return t.ready_for_claim}))},t.prototype.isDeposited=function(t){var e=this;return this.bridgeUtil.getBridgeLogData(t,!0).then((function(t){return e.childChainBridge.isClaimed(t.depositCount,0)}))},t.prototype.isExited=function(t){var e=this;return this.bridgeUtil.getBridgeLogData(t,!1).then((function(t){return e.rootChainBridge.isClaimed(t.depositCount,1)}))},t}(),zt={converter:u,Web3Client:o,BN:c,UnstoppableDomains:Object},Zt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Vt=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.getPOSContracts=n,o}return Zt(e,t),Object.defineProperty(e.prototype,"rootChainManager",{get:function(){return this.getPOSContracts().rootChainManager},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"gasSwapper",{get:function(){return this.getPOSContracts().gasSwapper},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"exitUtil",{get:function(){return this.getPOSContracts().exitUtil},enumerable:!1,configurable:!0}),e.prototype.getPredicateAddress=function(){var t=this;return this.predicateAddress?xt(this.predicateAddress):this.rootChainManager.method("tokenToType",this.contractParam.address).then((function(t){return t.read()})).then((function(e){if(!e)throw new Error("Invalid Token Type");return t.rootChainManager.method("typeToPredicate",e)})).then((function(t){return t.read()})).then((function(e){return t.predicateAddress=e,e}))},e.prototype.isWithdrawn=function(t,e){var r=this;if(!t)throw new Error("txHash not provided");return this.exitUtil.getExitHash(t,0,e).then((function(t){return r.rootChainManager.isExitProcessed(t)}))},e.prototype.isWithdrawnOnIndex=function(t,e,r){var n=this;if(!t)throw new Error("txHash not provided");return this.exitUtil.getExitHash(t,e,r).then((function(t){return n.rootChainManager.isExitProcessed(t)}))},e.prototype.withdrawExitPOS=function(t,e,r,n){var o=this;return this.exitUtil.buildPayloadForExit(t,e,r).then((function(t){return o.rootChainManager.exit(t,n)}))},e}(Ot),Xt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Yt=function(t){function n(e,r,n,o){return t.call(this,{isParent:r,address:e,name:"ChildERC20",bridgeType:"pos"},n,o)||this}return Xt(n,t),n.prototype.getBalance=function(t,e){var r=this;return this.getContract().then((function(n){var o=n.method("balanceOf",t);return r.processRead(o,e)}))},n.prototype.getAllowance=function(t,e){var r=this;void 0===e&&(e={});var n=e.spenderAddress,o=n?xt(n):this.getPredicateAddress();return Promise.all([o,this.getContract()]).then((function(n){var o=n[0],i=n[1].method("allowance",t,o);return r.processRead(i,e)}))},n.prototype.approve=function(t,e){var n=this;void 0===e&&(e={});var o=e.spenderAddress;o||this.contractParam.isParent||this.client.logger.error(r.NullSpenderAddress).throw();var i=o?xt(o):this.getPredicateAddress();return Promise.all([i,this.getContract()]).then((function(r){var o=r[0],i=r[1].method("approve",o,u.toHex(t));return n.processWrite(i,e)}))},n.prototype.approveMax=function(t){return void 0===t&&(t={}),this.approve(Et,t)},n.prototype.deposit=function(t,e,r){this.checkForRoot("deposit");var n=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return this.rootChainManager.deposit(e,this.contractParam.address,n,r)},n.prototype.depositWithGas=function(t,e,n,o,i){var a=this;return this.checkForRoot("deposit"),this.getChainId().then((function(s){1!==s&&a.client.logger.error(r.AllowedOnMainnet).throw();var c=a.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return i.value=u.toHex(n),a.gasSwapper.depositWithGas(a.contractParam.address,c,e,o,i)}))},n.prototype.depositEther_=function(t,e,r){var n=this;return void 0===r&&(r={}),this.checkForRoot("depositEther"),r.value=u.toHex(t),this.rootChainManager.method("depositEtherFor",e).then((function(t){return n.processWrite(t,r)}))},n.prototype.depositEtherWithGas_=function(t,e,n,o,i){var a=this;return void 0===i&&(i={}),this.checkForRoot("depositEtherWithGas"),this.getChainId().then((function(s){1!==s&&a.client.logger.error(r.AllowedOnMainnet).throw();var c=a.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return i.value=u.toHex(u.toBN(t).add(u.toBN(n))),a.gasSwapper.depositWithGas("******************************************",c,e,o,i)}))},n.prototype.withdrawStart=function(t,e){var r=this;return this.checkForChild("withdrawStart"),this.getContract().then((function(n){var o=n.method("withdraw",u.toHex(t));return r.processWrite(o,e)}))},n.prototype.withdrawExit_=function(t,r,n){var o=this;void 0===n&&(n={});var i=n.burnEventSignature?n.burnEventSignature:e.Erc20Transfer;return this.exitUtil.buildPayloadForExit(t,i,r).then((function(t){return o.rootChainManager.exit(t,n)}))},n.prototype.withdrawExit=function(t,e){return this.checkForRoot("withdrawExit"),this.withdrawExit_(t,!1,e)},n.prototype.withdrawExitFaster=function(t,e){return this.checkForRoot("withdrawExitFaster"),this.withdrawExit_(t,!0,e)},n.prototype.isWithdrawExited=function(t){return this.isWithdrawn(t,e.Erc20Transfer)},n.prototype.transfer=function(t,e,r){return this.transferERC20(e,t,r)},n}(Vt),$t=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Jt=function(t){function e(e,r){return t.call(this,{address:r,name:"RootChainManager",bridgeType:"pos",isParent:!0},e)||this}return $t(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.deposit=function(t,e,r,n){var o=this;return this.method("depositFor",t,e,r).then((function(t){return o.processWrite(t,n)}))},e.prototype.exit=function(t,e){var r=this;return this.method("exit",t).then((function(t){return r.processWrite(t,e)}))},e.prototype.isExitProcessed=function(t){var e=this;return this.method("processedExits",t).then((function(t){return e.processRead(t)}))},e}(Ot),Kt=function(){function t(t,e){this.maticClient_=t.child,this.rootChain=e;var r=t.config;this.config=r,this.requestConcurrency=r.requestConcurrency}return t.prototype.getLogIndex_=function(t,e){var r=-1;switch(t){case"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef":case"0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14":r=e.logs.findIndex((function(e){return e.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===e.topics[2].toLowerCase()}));break;case"0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62":case"0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb":r=e.logs.findIndex((function(e){return e.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===e.topics[3].toLowerCase()}));break;default:r=e.logs.findIndex((function(e){return e.topics[0].toLowerCase()===t.toLowerCase()}))}if(r<0)throw new Error("Log not found in receipt");return r},t.prototype.getAllLogIndices_=function(t,e){var r=[];switch(t){case"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef":case"0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14":r=e.logs.reduce((function(e,n,o){return n.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===n.topics[2].toLowerCase()&&r.push(o),r}),[]);break;case"0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62":case"0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb":r=e.logs.reduce((function(e,n,o){return n.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===n.topics[3].toLowerCase()&&r.push(o),r}),[]);break;case"0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df":r=e.logs.reduce((function(t,e,n){return"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"===e.topics[0].toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===e.topics[2].toLowerCase()&&r.push(n),r}),[]);break;default:r=e.logs.reduce((function(e,n,o){return n.topics[0].toLowerCase()===t.toLowerCase()&&r.push(o),r}),[])}if(0===r.length)throw new Error("Log not found in receipt");return r},t.prototype.getChainBlockInfo=function(t){return Promise.all([this.rootChain.getLastChildBlock(),this.maticClient_.getTransaction(t)]).then((function(t){return{lastChildBlock:t[0],txBlockNumber:t[1].blockNumber}}))},t.prototype.isCheckPointed_=function(t){return new zt.BN(t.lastChildBlock).gte(new zt.BN(t.txBlockNumber))},t.prototype.isCheckPointed=function(t){var e=this;return this.getChainBlockInfo(t).then((function(t){return e.isCheckPointed_(t)}))},t.prototype.getRootBlockInfo=function(t){var e,r=this;return this.rootChain.findRootBlockFromChild(t).then((function(t){return e=t,r.rootChain.method("headerBlocks",u.toHex(t))})).then((function(t){return t.read()})).then((function(t){return{headerBlockNumber:e,end:t.end.toString(),start:t.start.toString()}}))},t.prototype.getRootBlockInfoFromAPI=function(t){var e=this;return this.maticClient_.logger.log("block info from API 1"),Ft.network.getBlockIncluded(this.config.version,t).then((function(t){if(e.maticClient_.logger.log("block info from API 2",t),!(t&&t.start&&t.end&&t.headerBlockNumber))throw Error("Network API Error");return t})).catch((function(r){return e.maticClient_.logger.log("block info from API",r),e.getRootBlockInfo(t)}))},t.prototype.getBlockProof=function(t,e){return vt.buildBlockProof(this.maticClient_,parseInt(e.start,10),parseInt(e.end,10),parseInt(t+"",10))},t.prototype.getBlockProofFromAPI=function(t,e){var r=this;return Ft.network.getProof(this.config.version,e.start,e.end,t).then((function(t){if(!t)throw Error("Network API Error");return r.maticClient_.logger.log("block proof from API 1"),t})).catch((function(n){return r.getBlockProof(t,e)}))},t.prototype.getExitProofFromAPI=function(t,e){var r=this;return Ft.network.getExitProof(this.config.version,t,e).then((function(t){if(!t)throw Error("Network API Error");return r.maticClient_.logger.log("exit proof from API 1"),t})).catch((function(n){return r.buildPayloadForExit(t,e,!1)}))},t.prototype.buildPayloadForExit=function(t,e,n,o){var i,a,s,c,u,f=this;if(void 0===o&&(o=0),n&&!Ft.network&&new g(r.ProofAPINotSet).throw(),o<0)throw new Error("Index must not be a negative integer");return n?this.getExitProofFromAPI(t,e):this.getChainBlockInfo(t).then((function(e){if(!f.isCheckPointed_(e))throw new Error("Burn transaction has not been checkpointed as yet");return i=e.txBlockNumber,Promise.all([f.maticClient_.getTransactionReceipt(t),f.maticClient_.getBlockWithTransaction(i)])})).then((function(t){return s=t[0],c=t[1],f.getRootBlockInfo(i)})).then((function(t){return a=t,f.getBlockProof(i,a)})).then((function(t){return u=t,vt.getReceiptProof(s,c,f.maticClient_,f.requestConcurrency)})).then((function(t){if(o>0){var r=f.getAllLogIndices_(e,s);if(o>=r.length)throw new Error("Index is greater than the number of tokens in this transaction");return f.encodePayload_(a.headerBlockNumber.toNumber(),u,i,c.timestamp,Buffer.from(c.transactionsRoot.slice(2),"hex"),Buffer.from(c.receiptsRoot.slice(2),"hex"),vt.getReceiptBytes(s),t.parentNodes,t.path,r[o])}var n=f.getLogIndex_(e,s);return f.encodePayload_(a.headerBlockNumber.toNumber(),u,i,c.timestamp,Buffer.from(c.transactionsRoot.slice(2),"hex"),Buffer.from(c.receiptsRoot.slice(2),"hex"),vt.getReceiptBytes(s),t.parentNodes,t.path,n)}))},t.prototype.buildMultiplePayloadsForExit=function(t,e,n){var o,i,a,s,c,u=this;return n&&!Ft.network&&new g(r.ProofAPINotSet).throw(),this.getChainBlockInfo(t).then((function(e){if(!n&&!u.isCheckPointed_(e))throw new Error("Burn transaction has not been checkpointed as yet");return o=e.txBlockNumber,Promise.all([u.maticClient_.getTransactionReceipt(t),u.maticClient_.getBlockWithTransaction(o)])})).then((function(t){return a=t[0],s=t[1],n?u.getRootBlockInfoFromAPI(o):u.getRootBlockInfo(o)})).then((function(t){return i=t,n?u.getBlockProofFromAPI(o,i):u.getBlockProof(o,i)})).then((function(t){return c=t,vt.getReceiptProof(a,s,u.maticClient_,u.requestConcurrency)})).then((function(t){for(var r=[],n=0,f=u.getAllLogIndices_(e,a);n<f.length;n++){var h=f[n];r.push(u.encodePayload_(i.headerBlockNumber.toNumber(),c,o,s.timestamp,Buffer.from(s.transactionsRoot.slice(2),"hex"),Buffer.from(s.receiptsRoot.slice(2),"hex"),vt.getReceiptBytes(a),t.parentNodes,t.path,h))}return r}))},t.prototype.encodePayload_=function(t,e,r,n,o,i,a,s,c,u){return ht.bufferToHex(lt().encode([t,e,r,n,ht.bufferToHex(o),ht.bufferToHex(i),ht.bufferToHex(a),ht.bufferToHex(lt().encode(s)),ht.bufferToHex(Buffer.concat([Buffer.from("00","hex"),c])),u]))},t.prototype.getExitHash=function(t,e,n){var o,i,a,s=this;return Promise.all([this.rootChain.getLastChildBlock(),this.maticClient_.getTransactionReceipt(t)]).then((function(t){return o=t[0],i=t[1],s.maticClient_.getBlockWithTransaction(i.blockNumber)})).then((function(t){return a=t,s.isCheckPointed_({lastChildBlock:o,txBlockNumber:i.blockNumber})||s.maticClient_.logger.error(r.BurnTxNotCheckPointed).throw(),vt.getReceiptProof(i,a,s.maticClient_,s.requestConcurrency)})).then((function(t){var r,o=[];return t.path.forEach((function(t){o.push(Buffer.from("0"+(t/16).toString(16),"hex")),o.push(Buffer.from("0"+(t%16).toString(16),"hex"))})),e>0&&(r=s.getAllLogIndices_(n,i)[e]),r=s.getLogIndex_(n,i),s.maticClient_.etheriumSha3(i.blockNumber,ht.bufferToHex(Buffer.concat(o)),r)}))},t}(),Qt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),te=function(t){function e(e,r){return t.call(this,{address:r,name:"RootChain",isParent:!0},e)||this}return Qt(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.getLastChildBlock=function(){var t=this;return this.method("getLastChildBlock").then((function(e){return e.read({},t.client.config.rootChainDefaultBlock||"safe")}))},e.prototype.findRootBlockFromChild=function(t){return e=this,r=void 0,o=function(){var e,r,n,o,i,a,s,c,u,f,h;return function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}(this,(function(p){switch(p.label){case 0:return e=new zt.BN(1),r=new zt.BN(2),n=new zt.BN(1e4),t=new zt.BN(t),o=e,[4,this.method("currentHeaderBlock")];case 1:return[4,p.sent().read()];case 2:i=p.sent(),a=new zt.BN(i).div(n),p.label=3;case 3:return o.lte(a)?o.eq(a)?(s=o,[3,6]):(c=o.add(a).div(r),[4,this.method("headerBlocks",c.mul(n).toString())]):[3,6];case 4:return[4,p.sent().read()];case 5:return u=p.sent(),f=new zt.BN(u.start),h=new zt.BN(u.end),f.lte(t)&&t.lte(h)?(s=c,[3,6]):(f.gt(t)?a=c.sub(e):h.lt(t)&&(o=c.add(e)),[3,3]);case 6:return[2,s.mul(n)]}}))},new((n=void 0)||(n=Promise))((function(t,i){function a(t){try{c(o.next(t))}catch(t){i(t)}}function s(t){try{c(o.throw(t))}catch(t){i(t)}}function c(e){var r;e.done?t(e.value):(r=e.value,r instanceof n?r:new n((function(t){t(r)}))).then(a,s)}c((o=o.apply(e,r||[])).next())}));var e,r,n,o},e}(Ot),ee=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),re=function(t){function r(e,r,n,o){return t.call(this,{isParent:r,address:e,name:"ChildERC721",bridgeType:"pos"},n,o)||this}return ee(r,t),r.prototype.validateMany_=function(t){if(t.length>20)throw new Error("can not process more than 20 tokens");return t.map((function(t){return u.toHex(t)}))},r.prototype.getTokensCount=function(t,e){var r=this;return this.getContract().then((function(n){var o=n.method("balanceOf",t);return r.processRead(o,e)})).then((function(t){return Number(t)}))},r.prototype.getTokenIdAtIndexForUser=function(t,e,r){var n=this;return this.getContract().then((function(o){var i=o.method("tokenOfOwnerByIndex",e,t);return n.processRead(i,r)}))},r.prototype.getAllTokens=function(t,e){var r=this;return void 0===e&&(e=1/0),this.getTokensCount(t).then((function(n){(n=Number(n))>e&&(n=e);for(var o=[],i=0;i<n;i++)o.push(r.getTokenIdAtIndexForUser(i,t));return Promise.all(o)}))},r.prototype.isApproved=function(t,e){var r=this;return this.checkForRoot("isApproved"),this.getContract().then((function(n){var o=n.method("getApproved",t);return Promise.all([r.processRead(o,e),r.getPredicateAddress()]).then((function(t){return t[0]===t[1]}))}))},r.prototype.isApprovedAll=function(t,e){var r=this;return this.checkForRoot("isApprovedAll"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(n){var o=n[0],i=n[1],a=o.method("isApprovedForAll",t,i);return r.processRead(a,e)}))},r.prototype.approve=function(t,e){var r=this;return this.checkForRoot("approve"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(n){var o=n[0],i=n[1],a=o.method("approve",i,u.toHex(t));return r.processWrite(a,e)}))},r.prototype.approveAll=function(t){var e=this;return this.checkForRoot("approveAll"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(r){var n=r[0],o=r[1],i=n.method("setApprovalForAll",o,!0);return e.processWrite(i,t)}))},r.prototype.deposit=function(t,e,r){this.checkForRoot("deposit");var n=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return this.rootChainManager.deposit(e,this.contractParam.address,n,r)},r.prototype.depositMany=function(t,e,r){this.checkForRoot("depositMany");var n=this.validateMany_(t),o=this.client.parent.encodeParameters([n],["uint256[]"]);return this.rootChainManager.deposit(e,this.contractParam.address,o,r)},r.prototype.withdrawStart=function(t,e){var r=this;return this.checkForChild("withdrawStart"),this.getContract().then((function(n){var o=n.method("withdraw",u.toHex(t));return r.processWrite(o,e)}))},r.prototype.withdrawStartWithMetaData=function(t,e){var r=this;return this.checkForChild("withdrawStartWithMetaData"),this.getContract().then((function(n){var o=n.method("withdrawWithMetadata",u.toHex(t));return r.processWrite(o,e)}))},r.prototype.withdrawStartMany=function(t,e){var r=this;this.checkForChild("withdrawStartMany");var n=this.validateMany_(t);return this.getContract().then((function(t){var o=t.method("withdrawBatch",n);return r.processWrite(o,e)}))},r.prototype.withdrawExit=function(t,r){var n=this;return this.checkForRoot("withdrawExit"),this.exitUtil.buildPayloadForExit(t,e.Erc721Transfer,!1).then((function(t){return n.rootChainManager.exit(t,r)}))},r.prototype.withdrawExitOnIndex=function(t,r,n){var o=this;return this.checkForRoot("withdrawExit"),this.exitUtil.buildPayloadForExit(t,e.Erc721Transfer,!1,r).then((function(t){return o.rootChainManager.exit(t,n)}))},r.prototype.withdrawExitFaster=function(t,r){var n=this;return this.checkForRoot("withdrawExitFaster"),this.exitUtil.buildPayloadForExit(t,e.Erc721Transfer,!0).then((function(t){return n.rootChainManager.exit(t,r)}))},r.prototype.isWithdrawExited=function(t){return this.isWithdrawn(t,e.Erc721Transfer)},r.prototype.isWithdrawExitedMany=function(t){return this.isWithdrawn(t,e.Erc721BatchTransfer)},r.prototype.isWithdrawExitedOnIndex=function(t,r){return this.isWithdrawnOnIndex(t,r,e.Erc721Transfer)},r.prototype.transfer=function(t,e,r,n){return this.transferERC721(e,r,t,n)},r}(Vt),ne=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oe=function(t){function r(e,r,n,o){return t.call(this,{isParent:r,address:e,name:"ChildERC1155",bridgeType:"pos"},n,o)||this}return ne(r,t),Object.defineProperty(r.prototype,"addressConfig",{get:function(){return this.client.config.erc1155||{}},enumerable:!1,configurable:!0}),r.prototype.getAddress_=function(t){var e=this.addressConfig;return e[t]?xt(e[t]):this.client.getConfig(t)},r.prototype.getBalance=function(t,e,r){var n=this;return this.getContract().then((function(o){var i=o.method("balanceOf",t,u.toHex(e));return n.processRead(i,r)}))},r.prototype.isApprovedAll=function(t,e){var r=this;return this.checkForRoot("isApprovedAll"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(n){var o=n[0],i=n[1],a=o.method("isApprovedForAll",t,i);return r.processRead(a,e)}))},r.prototype.approveAll_=function(t,e){var r=this;return this.checkForRoot("approve"),Promise.all([this.getContract(),t]).then((function(t){var n=t[0],o=t[1],i=n.method("setApprovalForAll",o,!0);return r.processWrite(i,e)}))},r.prototype.approveAll=function(t){return this.checkForRoot("approve"),this.approveAll_(this.getPredicateAddress(),t)},r.prototype.approveAllForMintable=function(t){return this.checkForRoot("approveForMintable"),this.approveAll_(this.getAddress_("Main.POSContracts.MintableERC1155PredicateProxy"),t)},r.prototype.deposit=function(t,e){return this.checkForRoot("deposit"),this.depositMany({amounts:[t.amount],tokenIds:[t.tokenId],userAddress:t.userAddress,data:t.data},e)},r.prototype.depositMany=function(t,e){this.checkForRoot("depositMany");var r=t.tokenIds,n=t.amounts,o=t.data,i=t.userAddress,a=u.toHex(0),s=this.client.parent.encodeParameters([r.map((function(t){return u.toHex(t)})),n.map((function(t){return u.toHex(t)})),o||a],["uint256[]","uint256[]","bytes"]);return this.rootChainManager.deposit(i,this.contractParam.address,s,e)},r.prototype.withdrawStart=function(t,e,r){var n=this;return this.checkForChild("withdrawStart"),this.getContract().then((function(o){var i=o.method("withdrawSingle",u.toHex(t),u.toHex(e));return n.processWrite(i,r)}))},r.prototype.withdrawStartMany=function(t,e,r){var n=this;this.checkForChild("withdrawStartMany");var o=t.map((function(t){return u.toHex(t)})),i=e.map((function(t){return u.toHex(t)}));return this.getContract().then((function(t){var e=t.method("withdrawBatch",o,i);return n.processWrite(e,r)}))},r.prototype.withdrawExit=function(t,r){return this.checkForRoot("withdrawExit"),this.withdrawExitPOS(t,e.Erc1155Transfer,!1,r)},r.prototype.withdrawExitFaster=function(t,r){return this.checkForRoot("withdrawExitFaster"),this.withdrawExitPOS(t,e.Erc1155Transfer,!0,r)},r.prototype.withdrawExitMany=function(t,r){return this.checkForRoot("withdrawExitMany"),this.withdrawExitPOS(t,e.Erc1155BatchTransfer,!1,r)},r.prototype.withdrawExitFasterMany=function(t,r){return this.checkForRoot("withdrawExitFasterMany"),this.withdrawExitPOS(t,e.Erc1155BatchTransfer,!0,r)},r.prototype.isWithdrawExited=function(t){return this.isWithdrawn(t,e.Erc1155Transfer)},r.prototype.isWithdrawExitedMany=function(t){return this.isWithdrawn(t,e.Erc1155BatchTransfer)},r.prototype.transfer=function(t,e){return this.transferERC1155(t,e)},r}(Vt),ie=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ae=function(t){function e(e,r){return t.call(this,{address:r,name:"GasSwapper",bridgeType:"pos",isParent:!0},e)||this}return ie(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.depositWithGas=function(t,e,r,n,o){var i=this;return this.method("swapAndBridge",t,e,r,n).then((function(t){return i.processWrite(t,o)}))},e}(Ot),se=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ce=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return se(e,t),e.prototype.init=function(t){var e=this,r=this.client;return r.init(t).then((function(n){var o=r.mainPOSContracts;r.config=t=Object.assign({rootChainManager:o.RootChainManagerProxy,rootChain:r.mainPlasmaContracts.RootChainProxy,gasSwapper:o.GasSwapper},t),e.rootChainManager=new Jt(e.client,t.rootChainManager);var i=new te(e.client,t.rootChain);return e.exitUtil=new Kt(e.client,i),e.gasSwapper=new ae(e.client,t.gasSwapper),e}))},e.prototype.erc20=function(t,e){return new Yt(t,e,this.client,this.getContracts_.bind(this))},e.prototype.erc721=function(t,e){return new re(t,e,this.client,this.getContracts_.bind(this))},e.prototype.erc1155=function(t,e){return new oe(t,e,this.client,this.getContracts_.bind(this))},e.prototype.depositEther=function(t,e,r){return new Yt("",!0,this.client,this.getContracts_.bind(this)).depositEther_(t,e,r)},e.prototype.depositEtherWithGas=function(t,e,r,n,o){return new Yt("",!0,this.client,this.getContracts_.bind(this)).depositEtherWithGas_(t,e,r,n,o)},e.prototype.getContracts_=function(){return{exitUtil:this.exitUtil,rootChainManager:this.rootChainManager,gasSwapper:this.gasSwapper}},e}(Ut),ue={utils:zt,use:p,POSClient:ce},fe=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),he=function(t){function e(e,r,n){var o=t.call(this,e,r)||this;return o.getZkEvmContracts=n,o}return fe(e,t),Object.defineProperty(e.prototype,"parentBridge",{get:function(){return this.getZkEvmContracts().parentBridge},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"zkEVMWrapper",{get:function(){return this.getZkEvmContracts().zkEVMWrapper},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"childBridge",{get:function(){return this.getZkEvmContracts().childBridge},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bridgeUtil",{get:function(){return this.getZkEvmContracts().bridgeUtil},enumerable:!1,configurable:!0}),e}(Ot),pe=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),le=function(t){function e(e,r,n){return t.call(this,{address:r,name:"ZkEVMBridgeAdapter",bridgeType:"zkevm",isParent:n},e)||this}return pe(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.bridgeToken=function(t,e,r,n){var o=this;return this.method("bridgeToken",t,u.toHex(e),r).then((function(t){return o.processWrite(t,n)}))},e}(Ot),de=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ge=function(t){function e(e,r,n,o,i){var a=t.call(this,{isParent:r,address:e,bridgeAdapterAddress:n,name:"ERC20",bridgeType:"zkevm"},o,i)||this;return n&&(a.bridgeAdapter=new le(a.client,n,r)),a}return de(e,t),e.prototype.getBridgeAddress=function(){return(this.contractParam.isParent?this.parentBridge:this.childBridge).contractAddress},e.prototype.isEtherToken=function(){return this.contractParam.address===_t},e.prototype.getBalance=function(t,e){var r=this;return this.isEtherToken()?(this.contractParam.isParent?this.client.parent:this.client.child).getBalance(t):this.getContract().then((function(n){var o=n.method("balanceOf",t);return r.processRead(o,e)}))},e.prototype.isApprovalNeeded=function(){return!this.isEtherToken()&&(this.contractParam.isParent?this.parentBridge:this.childBridge).getOriginTokenInfo(this.contractParam.address).then((function(t){return t[1]===_t}))},e.prototype.getAllowance=function(t,e){var r=this;void 0===e&&(e={}),this.checkForNonNative("getAllowance");var n=e.spenderAddress?e.spenderAddress:this.getBridgeAddress();return this.getContract().then((function(o){var i=o.method("allowance",t,n);return r.processRead(i,e)}))},e.prototype.approve=function(t,e){var r=this;void 0===e&&(e={}),this.checkForNonNative("approve");var n=e.spenderAddress?e.spenderAddress:this.getBridgeAddress();return this.getContract().then((function(o){var i=o.method("approve",n,u.toHex(t));return r.processWrite(i,e)}))},e.prototype.approveMax=function(t){return void 0===t&&(t={}),this.checkForNonNative("approveMax"),this.approve(Et,t)},e.prototype.deposit=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForRoot("deposit");var o=r.permitData||"0x",i=r.forceUpdateGlobalExitRoot||!0,a=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return this.isEtherToken()&&(r.value=u.toHex(t)),this.childBridge.networkID().then((function(t){return n.parentBridge.bridgeAsset(t,e,a,n.contractParam.address,i,o,r)}))},e.prototype.depositWithGas=function(t,e,r,n){void 0===n&&(n={}),this.checkForRoot("deposit");var o=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return n.value=u.toHex(r),n.v&&n.r&&n.s?this.zkEVMWrapper.depositPermitWithGas(this.contractParam.address,o,e,Math.floor((Date.now()+36e5)/1e3).toString(),n.v,n.r,n.s,n):this.zkEVMWrapper.depositWithGas(this.contractParam.address,o,e,n)},e.prototype.depositPermitWithGas=function(t,e,r,n){var o=this;void 0===n&&(n={}),this.checkForRoot("deposit"),this.checkForNonNative("getPermitData");var i=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return n.value=u.toHex(r),this.getPermitSignatureParams_(t,this.zkEVMWrapper.contractAddress).then((function(t){return o.zkEVMWrapper.depositPermitWithGas(o.contractParam.address,i,e,Math.floor((Date.now()+36e5)/1e3).toString(),t.v,t.r,t.s,n)}))},e.prototype.depositWithPermit=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForRoot("deposit"),this.checkForNonNative("depositWithPermit");var o=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]),i=r.forceUpdateGlobalExitRoot||!0;return this.getPermitData(o,r).then((function(t){return n.childBridge.networkID().then((function(a){return n.parentBridge.bridgeAsset(a,e,o,n.contractParam.address,i,t,r)}))}))},e.prototype.depositCustomERC20=function(t,e,r){return void 0===r&&(r=!0),this.checkForRoot("depositCustomERC20"),this.checkAdapterPresent("depositCustomERC20"),this.checkForNonNative("depositCustomERC20"),this.bridgeAdapter.bridgeToken(e,t,r)},e.prototype.customERC20DepositClaim=function(t,e){var r=this;return this.checkForChild("customERC20DepositClaim"),this.parentBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!0,e)})).then((function(t){return r.childBridge.claimMessage(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.depositClaim=function(t,e){var r=this;return this.checkForChild("depositClaim"),this.parentBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!0,e)})).then((function(t){return r.childBridge.claimAsset(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.withdraw=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForChild("withdraw");var o=r.permitData||"0x",i=r.forceUpdateGlobalExitRoot||!0,a=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]);return this.isEtherToken()&&(r.value=u.toHex(t)),this.parentBridge.networkID().then((function(t){return n.childBridge.bridgeAsset(t,e,a,n.contractParam.address,i,o,r)}))},e.prototype.withdrawCustomERC20=function(t,e,r){return void 0===r&&(r=!0),this.checkForChild("withdrawCustomERC20"),this.checkAdapterPresent("depositCustomERC20"),this.checkForNonNative("withdrawCustomERC20"),this.bridgeAdapter.bridgeToken(e,t,r)},e.prototype.customERC20WithdrawExit=function(t,e){var r=this;return this.checkForRoot("customERC20WithdrawExit"),this.childBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!1,e)})).then((function(t){return r.parentBridge.claimMessage(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.withdrawWithPermit=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForChild("withdraw");var o=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]),i=r.forceUpdateGlobalExitRoot||!0;return this.getPermitData(o,r).then((function(t){return n.parentBridge.networkID().then((function(a){return n.childBridge.bridgeAsset(a,e,o,n.contractParam.address,i,t,r)}))}))},e.prototype.withdrawExit=function(t,e){var r=this;return this.checkForRoot("withdrawExit"),this.childBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!1,e)})).then((function(t){return r.parentBridge.claimAsset(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.transfer=function(t,e,r){return void 0===r&&(r={}),this.contractParam.address===_t?(r.to=e,r.value=u.toHex(t),this.sendTransaction(r)):this.transferERC20(e,t,r)},e.prototype.getPermit=function(){var t,e=this;return this.getContract().then((function(r){var n=(t=r).method("PERMIT_TYPEHASH");return e.processRead(n)})).then((function(r){switch(r){case Bt:return ot.DAI;case At:var n=t.method("DOMAIN_TYPEHASH"),o=t.method("EIP712DOMAIN_HASH");return Ct([e.processRead(n),e.processRead(o)]).then((function(t){switch(t){case It:return ot.EIP_2612;case Tt:return ot.UNISWAP;default:return Promise.reject(new Error("Unsupported domain typehash: ".concat(t)))}}));default:return Promise.reject(new Error("Unsupported permit typehash: ".concat(r)))}}))},e.prototype.getTypedData_=function(t,e,r,n,o,i,a){var s={types:{EIP712Domain:[{name:"name",type:"string"},{name:"version",type:"string"},{name:"chainId",type:"uint256"},{name:"verifyingContract",type:"address"}],Permit:[]},primaryType:"Permit",domain:{name:n,version:"1",chainId:r,verifyingContract:this.contractParam.address},message:{}};switch(t){case ot.DAI:s.types.Permit=[{name:"holder",type:"address"},{name:"spender",type:"address"},{name:"nonce",type:"uint256"},{name:"expiry",type:"uint256"},{name:"allowed",type:"bool"}],s.message={holder:e,spender:i,nonce:o,expiry:Math.floor((Date.now()+36e5)/1e3),allowed:!0};case ot.EIP_2612:case ot.UNISWAP:t===ot.UNISWAP&&(s.types.EIP712Domain=[{name:"name",type:"string"},{name:"chainId",type:"uint256"},{name:"verifyingContract",type:"address"}],delete s.domain.version),s.types.Permit=[{name:"owner",type:"address"},{name:"spender",type:"address"},{name:"value",type:"uint256"},{name:"nonce",type:"uint256"},{name:"deadline",type:"uint256"}],s.message={owner:e,spender:i,value:a,nonce:o,deadline:Math.floor((Date.now()+36e5)/1e3)}}return s},e.prototype.getSignatureParameters_=function(t,e){if(!(0,b.isHexString)(e))throw new Error('Given value "'.concat(e,'" is not a valid hex string.'));"0x"!==e.slice(0,2)&&(e="0x".concat(e));var r=e.slice(0,66),n="0x".concat(e.slice(66,130)),o=t.hexToNumber("0x".concat(e.slice(130,132)));return[27,28].includes(o)||(o+=27),{r,s:n,v:o}},e.prototype.encodePermitFunctionData_=function(t,e,r,n,o,i,a){var s,c=r.r,u=r.s,f=r.v;switch(e){case ot.DAI:s=t.method("permit",o,n,i,Math.floor((Date.now()+36e5)/1e3),!0,f,c,u);break;case ot.EIP_2612:case ot.UNISWAP:s=t.method("permit",o,n,a,Math.floor((Date.now()+36e5)/1e3),f,c,u)}return s.encodeABI()},e.prototype.getPermitSignatureParams_=function(t,e){var r,n,o,i,a,s=this,c=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]),f=this.contractParam.isParent?this.client.parent:this.client.child;return Promise.all(["WEB3"===f.name?f.getAccountsUsingRPC_():f.getAccounts(),this.getContract(),f.getChainId(),this.getPermit()]).then((function(t){r=t[0][0],i=t[1],n=t[2],o=t[3];var e=i.method("name"),a=i.method("nonces",r);return Promise.all([s.processRead(e),s.processRead(a)])})).then((function(t){var i=t[0];return a=t[1],s.getTypedData_(o,r,n,i,a,e,c)})).then((function(t){return f.signTypedData(r,t)})).then((function(t){return s.getSignatureParameters_(f,t)}))},e.prototype.getPermitData_=function(t,e){var r,n,o,i,a,s=this,c=this.client.parent.encodeParameters([u.toHex(t)],["uint256"]),f=this.contractParam.isParent?this.client.parent:this.client.child;return Promise.all(["WEB3"===f.name?f.getAccountsUsingRPC_():f.getAccounts(),this.getContract(),f.getChainId(),this.getPermit()]).then((function(t){r=t[0][0],i=t[1],n=t[2],o=t[3];var e=i.method("name"),a=i.method("nonces",r);return Promise.all([s.processRead(e),s.processRead(a)])})).then((function(t){var i=t[0];return a=t[1],s.getTypedData_(o,r,n,i,a,e,c)})).then((function(t){return f.signTypedData(r,t)})).then((function(t){var n=s.getSignatureParameters_(f,t);return s.encodePermitFunctionData_(i,o,n,e,r,a,c)}))},e.prototype.getPermitData=function(t,e){void 0===e&&(e={}),this.checkForNonNative("getPermitData");var r=e.spenderAddress?e.spenderAddress:this.getBridgeAddress();return this.getPermitData_(t,r)},e}(he),ye=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),me=function(t){function e(e,r,n){return t.call(this,{address:r,name:"PolygonZkEVMBridge",bridgeType:"zkevm",isParent:n},e)||this}return ye(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.bridgeAsset=function(t,e,r,n,o,i,a){var s=this;return void 0===i&&(i="0x"),this.method("bridgeAsset",t,e,u.toHex(r),n,o,i).then((function(t){return s.processWrite(t,a)}))},e.prototype.claimAsset=function(t,e,r,n,o,i,a,s,c,u,f,h){var p=this;return this.method("claimAsset",t,e,r,n,o,i,a,s,c,u,f).then((function(t){return p.processWrite(t,h)}))},e.prototype.bridgeMessage=function(t,e,r,n,o){var i=this;return void 0===n&&(n="0x"),this.method("bridgeMessage",t,e,r,n).then((function(t){return i.processWrite(t,o)}))},e.prototype.claimMessage=function(t,e,r,n,o,i,a,s,c,u,f,h){var p=this;return this.method("claimMessage",t,e,r,n,o,i,a,s,c,u,f).then((function(t){return p.processWrite(t,h)}))},e.prototype.getMappedTokenInfo=function(t,e){var r=this;return this.method("getTokenWrappedAddress",t,e).then((function(t){return r.processRead(t)}))},e.prototype.isClaimed=function(t,e){var r=this;return this.method("isClaimed",t,e).then((function(t){return r.processRead(t)}))},e.prototype.precalculatedMappedTokenInfo=function(t,e){var r=this;return this.method("precalculatedWrapperAddress",t,e).then((function(t){return r.processRead(t)}))},e.prototype.getOriginTokenInfo=function(t){var e=this;return this.method("wrappedTokenToTokenInfo",t).then((function(t){return e.processRead(t)}))},e.prototype.networkID=function(){var t=this;return this.networkID_?xt(this.networkID_):this.method("networkID").then((function(e){return t.processRead(e).then((function(e){return t.networkID_=e,e}))}))},e}(Ot),ve=function(){function t(t){this.BRIDGE_TOPIC="0x501781209a1f8899323b96b4ef08b168df93e0a90c673d1e4cce39366cb62f9b",this.client_=t}return t.prototype.decodedBridgeData_=function(t,e){var r=e?this.client_.parent:this.client_.child;return this.client_.getABI("PolygonZkEVMBridge","zkevm").then((function(e){var n=e.filter((function(t){return"BridgeEvent"===t.name}));if(!n.length)throw new Error("Data not decoded");var o=r.decodeParameters(t,n[0].inputs);return{leafType:o[0],originNetwork:o[1],originTokenAddress:o[2],destinationNetwork:o[3],destinationAddress:o[4],amount:o[5],metadata:o[6]||"0x",depositCount:o[7]}}))},t.prototype.getBridgeLogData_=function(t,e){var r=this;return(e?this.client_.parent:this.client_.child).getTransactionReceipt(t).then((function(t){var n=t.logs.filter((function(t){return t.topics[0].toLowerCase()===r.BRIDGE_TOPIC}));if(!n.length)throw new Error("Log not found in receipt");var o=n[0].data;return r.decodedBridgeData_(o,e)}))},t.prototype.getProof_=function(t,e){return Ft.zkEvmNetwork.getMerkleProofForZkEvm(this.client_.config.version,t,e).then((function(t){return t})).catch((function(t){throw new Error("Error in creating proof")}))},t.prototype.getBridgeLogData=function(t,e){return this.getBridgeLogData_(t,e)},t.prototype.computeGlobalIndex=function(t,e,r){return BigInt(r)===BigInt(0)?BigInt(t)+Rt:BigInt(t)+BigInt(e)*BigInt(Math.pow(2,32))},t.prototype.buildPayloadForClaim=function(t,e,r){var n=this;return this.getBridgeLogData_(t,e).then((function(t){var e=t.originNetwork,o=t.originTokenAddress,i=t.destinationNetwork,a=t.destinationAddress,s=t.amount,c=t.metadata,u=t.depositCount;return n.getProof_(r,u).then((function(t){var f={};return f.smtProof=t.merkle_proof,f.smtProofRollup=t.rollup_merkle_proof,f.globalIndex=n.computeGlobalIndex(u,i,r).toString(),f.mainnetExitRoot=t.main_exit_root,f.rollupExitRoot=t.rollup_exit_root,f.originNetwork=e,f.originTokenAddress=o,f.destinationNetwork=i,f.destinationAddress=a,f.amount=s,f.metadata=c,f}))}))},t}(),we=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),be=function(t){function e(e,r){return t.call(this,{address:r,name:"ZkEVMWrapper",bridgeType:"zkevm",isParent:!0},e)||this}return we(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.depositWithGas=function(t,e,r,n){var o=this;return this.method("deposit",t,e,r).then((function(t){return o.processWrite(t,n)}))},e.prototype.depositPermitWithGas=function(t,e,r,n,o,i,a,s){var c=this;return this.method("deposit",t,e,r,n,o,i,a).then((function(t){return c.processWrite(t,s)}))},e}(Ot),Pe=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ke=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Pe(e,t),e.prototype.init=function(t){var e=this,r=this.client;return r.init(t).then((function(n){var o=r.mainZkEvmContracts,i=r.zkEvmContracts;return r.config=t=Object.assign({parentBridge:o.PolygonZkEVMBridgeProxy,childBridge:i.PolygonZkEVMBridge,zkEVMWrapper:o.ZkEVMWrapper},t),e.rootChainBridge=new me(e.client,t.parentBridge,!0),e.childChainBridge=new me(e.client,t.childBridge,!1),e.zkEVMWrapper=new be(e.client,t.zkEVMWrapper),e.bridgeUtil=new ve(e.client),Ft.zkEvmNetwork||("/"!==Mt.zkEvmBridgeService[Mt.zkEvmBridgeService.length-1]&&(Mt.zkEvmBridgeService+="/"),Mt.zkEvmBridgeService+="api/zkevm/",Ft.zkEvmNetwork=new St(Mt.zkEvmBridgeService)),e}))},e.prototype.erc20=function(t,e,r){return new ge(t,e,r,this.client,this.getContracts_.bind(this))},e.prototype.getContracts_=function(){return{parentBridge:this.rootChainBridge,childBridge:this.childChainBridge,bridgeUtil:this.bridgeUtil,zkEVMWrapper:this.zkEVMWrapper}},e}(Gt);const xe=ue})(),h})()));
//# sourceMappingURL=matic.umd.min.js.map