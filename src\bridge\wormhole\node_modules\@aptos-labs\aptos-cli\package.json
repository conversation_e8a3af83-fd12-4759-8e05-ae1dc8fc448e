{"name": "@aptos-labs/aptos-cli", "version": "1.0.2", "description": "Aptos CLI available from npmjs", "bin": {"aptos": "./dist/aptos.js"}, "type": "module", "author": "aptoslabs.com", "keywords": ["aptos node cli", "aptos"], "repository": {"type": "git", "url": "https://github.com/aptos-labs/aptos-cli"}, "bugs": {"url": "https://github.com/aptos-labs/aptos-cli/issues"}, "license": "Apache-2.0", "scripts": {"clean": "rm -rf dist", "build": "npm run clean && tsc", "dev": "npm run build && node ./dist/aptos.js"}, "files": ["bin", "dist"], "dependencies": {"commander": "^12.1.0"}, "devDependencies": {"@types/node": "^20.5.0", "typescript": "^5.6.2"}}