import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgReclaimLockedFunds {
    interface Params {
        sender: string;
        lockedAccountPubKey: string;
        signature: Uint8Array;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds;
}
/**
 * @category Messages
 */
export default class MsgReclaimLockedFunds extends MsgBase<MsgReclaimLockedFunds.Params, MsgReclaimLockedFunds.Proto> {
    static fromJSON(params: MsgReclaimLockedFunds.Params): MsgReclaimLockedFunds;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds;
    toData(): {
        sender: string;
        lockedAccountPubKey: Uint8Array;
        signature: Uint8Array;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            lockedAccountPubKey: Buffer<ArrayBuffer>;
            signature: Buffer<ArrayBuffer>;
        };
    };
    toWeb3Gw(): never;
    toEip712(): never;
    toEip712V2(): never;
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgReclaimLockedFunds;
    };
    toBinary(): Uint8Array;
}
