import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "danielvladco.protobuf.graphql";
export declare enum Type {
    DEFAULT = 0,
    MUTATION = 1,
    QUERY = 2,
    UNRECOGNIZED = -1
}
export declare function typeFromJSON(object: any): Type;
export declare function typeToJSON(object: Type): string;
export declare enum Upstream {
    UPSTREAM_UNSPECIFIED = 0,
    UPSTREAM_SERVER = 1,
    UPSTREAM_CLIENT = 2,
    UNRECOGNIZED = -1
}
export declare function upstreamFromJSON(object: any): Upstream;
export declare function upstreamToJSON(object: Upstream): string;
export interface Oneof {
    ignore: boolean;
    name: string;
}
export interface Field {
    required: boolean;
    params: string;
    dirs: string;
    ignore: boolean;
    name: string;
}
export interface Rpc {
    type: Type;
    ignore: boolean;
    name: string;
}
export interface Svc {
    type: Type;
    ignore: boolean;
    name: string;
    upstream: Upstream;
}
export declare const Oneof: {
    encode(message: Oneof, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Oneof;
    fromJ<PERSON><PERSON>(object: any): Oneof;
    toJSON(message: Oneof): unknown;
    create(base?: DeepPartial<Oneof>): Oneof;
    fromPartial(object: DeepPartial<Oneof>): Oneof;
};
export declare const Field: {
    encode(message: Field, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Field;
    fromJSON(object: any): Field;
    toJSON(message: Field): unknown;
    create(base?: DeepPartial<Field>): Field;
    fromPartial(object: DeepPartial<Field>): Field;
};
export declare const Rpc: {
    encode(message: Rpc, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Rpc;
    fromJSON(object: any): Rpc;
    toJSON(message: Rpc): unknown;
    create(base?: DeepPartial<Rpc>): Rpc;
    fromPartial(object: DeepPartial<Rpc>): Rpc;
};
export declare const Svc: {
    encode(message: Svc, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Svc;
    fromJSON(object: any): Svc;
    toJSON(message: Svc): unknown;
    create(base?: DeepPartial<Svc>): Svc;
    fromPartial(object: DeepPartial<Svc>): Svc;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
