#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Gate交易所代币信息获取脚本
获取所有支持充值和提现的USDT交易对的信息，包括代币合约地址、支持的网络和充值地址
"""

import os
import sys
import json
import asyncio
import ccxt
import argparse
import yaml
import time
import hmac
import hashlib
import aiohttp
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

try:
    from src.utils.logger import logger
except ImportError:
    import logging
    # 如果无法导入logger，创建一个基本的logger
    logger = logging.getLogger("gate_token_info")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.warning("无法导入src.utils.logger，使用基本日志记录器")

def load_config():
    """加载配置文件"""
    config_path = os.path.join(project_root, 'config', 'config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info("成功加载配置文件")
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}

# 加载配置
CONFIG = load_config()

# 确保输出目录存在
os.makedirs('data/cex/gate_info', exist_ok=True)

class GateTokenInfo:
    def __init__(self, limit=None, debug=False, get_deposit_address=False, get_fees=True, get_history=False):
        # 从配置文件中读取Gate.io API密钥
        gate_config = CONFIG.get('cex', {}).get('gate', {})
        self.api_key = gate_config.get('api_key', '')
        self.api_secret = gate_config.get('secret_key', '')
        self.debug = debug
        self.should_fetch_deposit_address = get_deposit_address
        self.get_fees = get_fees
        self.get_history = get_history
        
        if not self.api_key or not self.api_secret:
            logger.warning("配置文件中未找到有效的Gate.io API密钥，无法获取充值地址")
        else:
            logger.info("已从配置文件中加载Gate.io API密钥")
            
        # 配置CCXT，CEX不能使用代理，因为API绑定到固定IP
        self.exchange = ccxt.gate({
            'apiKey': self.api_key,
            'secret': self.api_secret,
            'enableRateLimit': True,
            'timeout': 60000,  # 增加超时时间到60秒
            'options': {
                'fetchCurrencies': 'v4',  # 使用V4 API获取币种信息
                'recvWindow': 60000,  # 增加接收窗口
            }
        })
        
        self.usdt_pairs = []
        self.all_usdt_pairs = []  # 所有USDT交易对
        self.deposit_withdraw_enabled_pairs = []  # 支持充值和提现的USDT交易对
        self.token_info = {}
        self.network_groups = defaultdict(list)
        self.deposit_addresses = {}  # 存储充值地址
        self.fees_data = {}          # 存储费用数据
        self.deposits_history = []   # 存储充值历史
        self.withdrawals_history = [] # 存储提现历史
        self.limit = limit
        self.all_currencies_cache = None  # 缓存所有币种信息
        self.symbols_to_currencies = {}  # 符号到币种的映射
        
    async def get_all_usdt_pairs(self):
        """获取所有USDT交易对"""
        try:
            # 使用CCXT获取所有交易对
            markets = self.exchange.load_markets()
            
            # 过滤出USDT交易对，排除3S, 3L, 5S, 5L结尾的交易对
            all_pairs = [
                symbol for symbol in markets.keys()
                if symbol.endswith('/USDT') 
                and markets[symbol].get('active', False)
                and not symbol.endswith('3S/USDT')
                and not symbol.endswith('3L/USDT')
                and not symbol.endswith('5S/USDT')
                and not symbol.endswith('5L/USDT')
            ]
            
            # 构建符号到币种的映射
            for symbol in all_pairs:
                base_currency = symbol.split('/')[0]
                self.symbols_to_currencies[symbol] = base_currency
            
            self.all_usdt_pairs = all_pairs
            logger.info(f"获取所有 {len(self.all_usdt_pairs)} 个USDT交易对")
                
            return self.all_usdt_pairs
        except Exception as e:
            logger.error(f"获取USDT交易对时出错: {e}")
            return []
    
    async def fetch_all_currencies(self):
        """使用CCXT获取所有币种信息"""
        if self.all_currencies_cache:
            return self.all_currencies_cache
            
        try:
            logger.info("开始获取所有币种详细信息...")
            currencies = self.exchange.fetch_currencies()
            logger.info(f"成功获取 {len(currencies)} 个币种的详细信息")
            self.all_currencies_cache = currencies
            
            # 调试模式: 保存原始数据进行分析
            if self.debug:
                os.makedirs('data/cex/gate_info/debug', exist_ok=True)
                with open('data/cex/gate_info/debug/raw_currencies.json', 'w', encoding='utf-8') as f:
                    json.dump(currencies, f, indent=2, ensure_ascii=False)
                    
            return currencies
        except Exception as e:
            logger.error(f"获取币种详情失败: {e}")
            return {}

    async def filter_deposit_withdraw_enabled_pairs(self):
        """筛选支持充值和提现的USDT交易对"""
        if not self.all_usdt_pairs:
            await self.get_all_usdt_pairs()
            
        # 获取所有币种信息
        currencies = await self.fetch_all_currencies()
        if not currencies:
            logger.error("无法获取币种详情，无法筛选支持充值和提现的币种")
            return []
            
        # 筛选支持充值和提现的交易对
        enabled_pairs = []
        for symbol in self.all_usdt_pairs:
            currency = symbol.split('/')[0]
            
            if currency in currencies:
                currency_info = currencies[currency]
                
                # 检查是否同时支持充值和提现
                deposit_enabled = currency_info.get('deposit', False)
                withdraw_enabled = currency_info.get('withdraw', False)
                
                if deposit_enabled and withdraw_enabled:
                    enabled_pairs.append(symbol)
                    logger.debug(f"币种 {currency} 支持充值和提现，添加到筛选列表")
            
        logger.info(f"从 {len(self.all_usdt_pairs)} 个USDT交易对中筛选出 {len(enabled_pairs)} 个同时支持充值和提现的交易对")
        
        # 如果设置了限制，只取指定数量的交易对
        if self.limit and self.limit > 0 and len(enabled_pairs) > self.limit:
            self.deposit_withdraw_enabled_pairs = enabled_pairs[:self.limit]
            logger.info(f"限制获取前 {self.limit} 个支持充值和提现的USDT交易对")
        else:
            self.deposit_withdraw_enabled_pairs = enabled_pairs
            
        # 将筛选后的交易对设置为要处理的交易对
        self.usdt_pairs = self.deposit_withdraw_enabled_pairs
        
        return self.deposit_withdraw_enabled_pairs
            
    async def get_deposit_address(self, currency, network=None):
        """
        获取指定币种和网络的充值地址
        使用CCXT的fetchDepositAddress方法，并只返回指定网络的地址
        """
        if not self.api_key or not self.api_secret:
            logger.warning(f"未提供API密钥，无法获取 {currency} 充值地址")
            return None
        
        try:
            logger.info(f"尝试获取 {currency} 在 {network or '默认'} 网络的充值地址...")
            
            # 使用CCXT获取充值地址
            loop = asyncio.get_event_loop()
            address_info = await loop.run_in_executor(
                None,
                lambda: self.exchange.fetch_deposit_address(currency)
            )
            
            if address_info and isinstance(address_info, dict):
                # 获取原始响应数据
                raw_info = address_info.get('info', {})
                multichain_addresses = raw_info.get('multichain_addresses', [])
                
                # 如果指定了网络，找到对应网络的地址
                if network and multichain_addresses:
                    for chain_address in multichain_addresses:
                        if chain_address.get('chain') == network:
                            return {
                                'address': chain_address.get('address', ''),
                                'tag': '',  # Gate.io的BTC地址不使用tag
                                'network': network,
                                'currency': currency,
                                'info': {
                                    'currency': currency,
                                    'address': chain_address.get('address', ''),
                                    'multichain_addresses': [chain_address],  # 只保留当前网络的地址
                                    'min_deposit_amount': raw_info.get('min_deposit_amount')
                                }
                            }
                    logger.warning(f"未找到 {currency} 在 {network} 网络的充值地址")
                    return None
                
                # 如果没有指定网络或没有找到指定网络的地址，返回默认地址
                if address_info.get('address'):
                    logger.info(f"成功获取 {currency} 充值地址")
                    return {
                        'address': address_info.get('address', ''),
                        'tag': address_info.get('tag', ''),
                        'network': network or '',
                        'currency': currency,
                        'info': raw_info
                    }
                else:
                    logger.warning(f"未获取到 {currency} 的有效充值地址")
                    return None
            else:
                logger.warning(f"获取 {currency} 充值地址失败：返回数据无效")
                return None
                
        except Exception as e:
            logger.error(f"获取 {currency} 充值地址时出错: {e}")
            return None
            
    def extract_network_info(self, currency_info):
        """
        从CCXT币种信息中提取网络和合约地址信息
        """
        networks = []
        info = currency_info.get('info', {})
        
        # 检查是否有chains字段
        chains = info.get('chains', [])
        if not chains and 'chain' in info:
            # 有些币种没有chains字段，但有chain字段
            chain_name = info.get('chain')
            if chain_name:
                chains = [{'name': chain_name}]
        
        # 如果有chains字段，从中提取网络信息
        if chains and isinstance(chains, list):
            for chain in chains:
                chain_name = chain.get('name', '')
                contract_address = chain.get('contract_address', '') or chain.get('addr', '')
                withdraw_disabled = chain.get('withdraw_disabled', False)
                deposit_disabled = chain.get('deposit_disabled', False)
                
                # 获取该网络的充值地址
                deposit_address = self.deposit_addresses.get(f"{currency_info['code']}_{chain_name}", {})
                
                network = {
                    'chain': chain_name,
                    'chain_name': chain_name,
                    'withdraw_enabled': not withdraw_disabled,
                    'deposit_enabled': not deposit_disabled,
                    'contract_address': contract_address,
                    'withdraw_fee': self.extract_withdraw_fee(currency_info, chain_name),
                    'withdraw_min': self.extract_withdraw_min(currency_info, chain_name),
                    'withdraw_max': self.extract_withdraw_max(currency_info, chain_name),
                    'confirmation_blocks': '',
                    'deposit_address': deposit_address.get('address', ''),
                    'deposit_tag': deposit_address.get('tag', '')
                }
                networks.append(network)
        
        # 如果没有找到网络信息，添加默认网络
        if not networks:
            # 添加默认网络
            deposit = currency_info.get('deposit', False)
            withdraw = currency_info.get('withdraw', False)
            
            # 获取默认网络的充值地址
            deposit_address = self.deposit_addresses.get(f"{currency_info['code']}_default", {})
            
            networks.append({
                'chain': 'default',
                'chain_name': '默认链',
                'withdraw_enabled': withdraw,
                'deposit_enabled': deposit,
                'contract_address': '',
                'withdraw_fee': self.extract_withdraw_fee(currency_info),
                'withdraw_min': self.extract_withdraw_min(currency_info),
                'withdraw_max': self.extract_withdraw_max(currency_info),
                'confirmation_blocks': '',
                'deposit_address': deposit_address.get('address', ''),
                'deposit_tag': deposit_address.get('tag', '')
            })
            
        return networks
    
    def extract_withdraw_fee(self, currency_info, chain_name=None):
        """提取提现手续费"""
        try:
            # 尝试从原始信息中获取
            info = currency_info.get('info', {})
            
            # 如果指定了链名，尝试从对应链获取
            if chain_name and 'chains' in info:
                for chain in info.get('chains', []):
                    if chain.get('name') == chain_name and 'withdraw_fee' in chain:
                        return str(chain['withdraw_fee'])
            
            # 尝试从全局信息获取
            if 'withdraw_fee' in info:
                return str(info['withdraw_fee'])
                
            # 尝试从fee字段获取
            fee = currency_info.get('fee', {})
            if isinstance(fee, dict) and 'withdraw' in fee:
                withdraw_fee = fee['withdraw']
                if isinstance(withdraw_fee, dict) and 'flat' in withdraw_fee:
                    return str(withdraw_fee['flat'])
                return str(withdraw_fee)
                
            return ''
        except Exception:
            return ''
    
    def extract_withdraw_min(self, currency_info, chain_name=None):
        """提取最小提现限额"""
        try:
            # 尝试从原始信息中获取
            info = currency_info.get('info', {})
            
            # 如果指定了链名，尝试从对应链获取
            if chain_name and 'chains' in info:
                for chain in info.get('chains', []):
                    if chain.get('name') == chain_name and 'withdraw_min' in chain:
                        return str(chain['withdraw_min'])
            
            # 尝试从全局信息获取
            if 'withdraw_min' in info:
                return str(info['withdraw_min'])
                
            # 尝试从limits字段获取
            limits = currency_info.get('limits', {})
            if isinstance(limits, dict) and 'withdraw' in limits:
                withdraw_limits = limits['withdraw']
                if isinstance(withdraw_limits, dict) and 'min' in withdraw_limits:
                    return str(withdraw_limits['min'])
                    
            return ''
        except Exception:
            return ''
    
    def extract_withdraw_max(self, currency_info, chain_name=None):
        """提取最大提现限额"""
        try:
            # 尝试从原始信息中获取
            info = currency_info.get('info', {})
            
            # 如果指定了链名，尝试从对应链获取
            if chain_name and 'chains' in info:
                for chain in info.get('chains', []):
                    if chain.get('name') == chain_name and 'withdraw_max' in chain:
                        return str(chain['withdraw_max'])
            
            # 尝试从全局信息获取
            if 'withdraw_max' in info:
                return str(info['withdraw_max'])
                
            # 尝试从limits字段获取
            limits = currency_info.get('limits', {})
            if isinstance(limits, dict) and 'withdraw' in limits:
                withdraw_limits = limits['withdraw']
                if isinstance(withdraw_limits, dict) and 'max' in withdraw_limits:
                    return str(withdraw_limits['max'])
                    
            return ''
        except Exception:
            return ''
            
    async def get_token_info(self, symbol: str) -> Optional[Dict]:
        """
        获取代币详细信息
        
        Args:
            symbol: 交易对符号，如 BTC/USDT
            
        Returns:
            dict: 代币详细信息
        """
        try:
            # 从交易对中提取币种名称
            currency = symbol.split('/')[0]
            
            # 获取所有币种信息
            currencies = await self.fetch_all_currencies()
            if not currencies:
                logger.warning(f"未获取到币种信息，无法处理 {symbol}")
                return None
                
            # 检查币种是否存在
            if currency not in currencies:
                logger.warning(f"未在CCXT数据中找到 {currency} 的信息")
                return None
                
            # 获取币种详细信息
            currency_info = currencies[currency]
            
            # 提取充提状态
            deposit_enabled = currency_info.get('deposit', False)
            withdraw_enabled = currency_info.get('withdraw', False)
            
            # 创建代币信息对象
            self.token_info[symbol] = {
                'currency': currency,
                'name': currency_info.get('name', currency),  # 如果没有名称，使用币种代码
                'withdraw_enabled': withdraw_enabled,
                'deposit_enabled': deposit_enabled,
                'precision': currency_info.get('precision', ''),
                'networks': []
            }
            
            # 获取网络信息
            networks = self.extract_network_info(currency_info)
            
            # 添加网络信息
            for network_info in networks:
                self.token_info[symbol]['networks'].append(network_info)
                
                # 按网络分组
                if network_info['withdraw_enabled'] or network_info['deposit_enabled']:
                    self.network_groups[network_info['chain']].append({
                        'symbol': symbol,
                        'currency': currency,
                        'info': network_info
                    })
            
            logger.info(f"获取 {symbol} 代币信息成功")
            return self.token_info[symbol]
        except Exception as e:
            logger.error(f"获取 {symbol} 代币信息时出错: {e}")
            return None
            
    async def get_all_token_info(self):
        """获取所有代币信息"""
        if not self.usdt_pairs:
            if len(self.deposit_withdraw_enabled_pairs) > 0:
                # 已经筛选过支持充值和提现的交易对
                self.usdt_pairs = self.deposit_withdraw_enabled_pairs
            else:
                # 没有筛选过，现在筛选
                await self.filter_deposit_withdraw_enabled_pairs()
            
        # 先获取所有币种信息缓存
        await self.fetch_all_currencies()
        
        # 获取充值提现费用信息
        if self.get_fees:
            self.fees_data = await self.fetch_deposit_withdraw_fees()
        
        # 获取历史记录
        if self.get_history and self.api_key and self.api_secret:
            self.deposits_history = await self.fetch_recent_deposits()
            self.withdrawals_history = await self.fetch_recent_withdrawals()
        
        # 逐个获取代币信息
        for symbol in self.usdt_pairs:
            currency = symbol.split('/')[0]
            token_info = await self.get_token_info(symbol)
            
            # 如果配置了获取充值地址，为每个网络获取充值地址
            if token_info and self.should_fetch_deposit_address and self.api_key and self.api_secret:
                # 首先获取默认地址信息，其中包含所有网络的地址
                default_address_info = await self.get_deposit_address(currency)
                if default_address_info:
                    # 保存默认地址信息
                    self.deposit_addresses[f"{currency}_default"] = default_address_info
                    
                    # 从默认地址信息中提取各个网络的地址
                    multichain_addresses = default_address_info.get('info', {}).get('multichain_addresses', [])
                    for network in token_info['networks']:
                        chain_name = network['chain']
                        if chain_name != 'default':
                            # 从multichain_addresses中找到对应网络的地址
                            for chain_address in multichain_addresses:
                                if chain_address.get('chain') == chain_name:
                                    network['deposit_address'] = chain_address.get('address', '')
                                    network['deposit_tag'] = ''
                                    # 保存该网络的地址信息
                                    self.deposit_addresses[f"{currency}_{chain_name}"] = {
                                        'address': chain_address.get('address', ''),
                                        'tag': '',
                                        'network': chain_name,
                                        'currency': currency,
                                        'info': {
                                            'currency': currency,
                                            'address': chain_address.get('address', ''),
                                            'multichain_addresses': [chain_address],
                                            'min_deposit_amount': default_address_info.get('info', {}).get('min_deposit_amount')
                                        }
                                    }
                                    break
            
            await asyncio.sleep(0.2)  # 短暂休眠以避免请求过快
        
        # 整合费用信息
        if self.get_fees and self.fees_data:
            self.token_info = self.integrate_fees_info(self.token_info, self.fees_data)
        
    def save_results(self):
        """保存结果到文件"""
        try:
            # 创建输出目录
            os.makedirs('data/cex/gate_info', exist_ok=True)
            
            # 保存所有代币信息
            token_info_file = os.path.join('data/cex/gate_info', 'token_info_deposit_withdraw_enabled.json')
            with open(token_info_file, 'w', encoding='utf-8') as f:
                json.dump(self.token_info, f, indent=2, ensure_ascii=False)
            logger.info(f"支持充值和提现的代币信息已保存到 {token_info_file}")
            
            # 保存按网络分组的信息
            network_groups_file = os.path.join('data/cex/gate_info', 'network_groups_deposit_withdraw_enabled.json')
            with open(network_groups_file, 'w', encoding='utf-8') as f:
                json.dump(self.network_groups, f, indent=2, ensure_ascii=False)
            logger.info(f"支持充值和提现的网络分组信息已保存到 {network_groups_file}")
            
            # 保存充值地址信息
            if self.deposit_addresses:
                deposit_addresses_file = os.path.join('data/cex/gate_info', 'deposit_addresses_deposit_withdraw_enabled.json')
                with open(deposit_addresses_file, 'w', encoding='utf-8') as f:
                    json.dump(self.deposit_addresses, f, indent=2, ensure_ascii=False)
                logger.info(f"充值地址信息已保存到 {deposit_addresses_file}")
            
            # 保存费用信息
            if self.fees_data:
                fees_file = os.path.join('data/cex/gate_info', 'fees_data_deposit_withdraw_enabled.json')
                with open(fees_file, 'w', encoding='utf-8') as f:
                    json.dump(self.fees_data, f, indent=2, ensure_ascii=False)
                logger.info(f"费用信息已保存到 {fees_file}")
                
            # 保存历史记录
            if self.deposits_history:
                deposits_file = os.path.join('data/cex/gate_info', 'recent_deposits.json')
                with open(deposits_file, 'w', encoding='utf-8') as f:
                    # 转换为可序列化格式
                    serialized = []
                    for record in self.deposits_history:
                        item = {k: v for k, v in record.items() if k != 'info'}
                        # 添加必要的info字段
                        if 'txid' in record.get('info', {}):
                            item['txid'] = record['info']['txid']
                        serialized.append(item)
                    json.dump(serialized, f, indent=2, ensure_ascii=False)
                logger.info(f"最近充值记录已保存到 {deposits_file}")
                
            if self.withdrawals_history:
                withdrawals_file = os.path.join('data/cex/gate_info', 'recent_withdrawals.json')
                with open(withdrawals_file, 'w', encoding='utf-8') as f:
                    # 转换为可序列化格式
                    serialized = []
                    for record in self.withdrawals_history:
                        item = {k: v for k, v in record.items() if k != 'info'}
                        # 添加必要的info字段
                        if 'txid' in record.get('info', {}):
                            item['txid'] = record['info']['txid']
                        serialized.append(item)
                    json.dump(serialized, f, indent=2, ensure_ascii=False)
                logger.info(f"最近提现记录已保存到 {withdrawals_file}")
            
            # 保存CCXT原始数据
            if self.all_currencies_cache:
                # 创建简化版本，仅包含我们关心的币种
                simplified_data = {}
                for symbol in self.usdt_pairs:
                    currency = symbol.split('/')[0]
                    if currency in self.all_currencies_cache:
                        simplified_data[currency] = self.all_currencies_cache[currency]
                
                raw_data_file = os.path.join('data/cex/gate_info', 'raw_currencies_data_deposit_withdraw_enabled.json')
                with open(raw_data_file, 'w', encoding='utf-8') as f:
                    json.dump(simplified_data, f, indent=2, ensure_ascii=False)
                logger.info(f"CCXT原始币种数据已保存到 {raw_data_file}")
            
            # 生成网络统计报告
            report_file = os.path.join('data/cex/gate_info', 'network_report_deposit_withdraw_enabled.txt')
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"Gate交易所支持充值和提现的代币网络支持统计报告\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"API认证: {'已启用' if self.api_key and self.api_secret else '未启用'}\n")
                f.write(f"是否获取充值地址: {'是' if self.should_fetch_deposit_address else '否'}\n")
                f.write(f"是否获取费用信息: {'是' if self.get_fees else '否'}\n")
                f.write(f"是否获取历史记录: {'是' if self.get_history else '否'}\n")
                if self.limit:
                    f.write(f"获取币种数量限制: {self.limit}\n")
                f.write("\n")
                
                # 添加币种充提状态统计
                deposit_enabled_count = sum(1 for info in self.token_info.values() if info.get('deposit_enabled', False))
                withdraw_enabled_count = sum(1 for info in self.token_info.values() if info.get('withdraw_enabled', False))
                
                # 添加网络和手续费统计
                total_networks = sum(len(info.get('networks', [])) for info in self.token_info.values())
                has_fee_info = sum(1 for info in self.token_info.values() 
                                    for network in info.get('networks', []) 
                                    if network.get('withdraw_fee'))
                has_limit_info = sum(1 for info in self.token_info.values() 
                                    for network in info.get('networks', []) 
                                    if network.get('withdraw_min') or network.get('withdraw_max'))
                
                # 添加合约地址统计
                has_contract_info = sum(1 for info in self.token_info.values() 
                                        for network in info.get('networks', []) 
                                        if network.get('contract_address'))
                
                # 添加充值地址统计
                has_deposit_address = sum(1 for info in self.token_info.values() 
                                            for network in info.get('networks', []) 
                                            if network.get('deposit_address'))
                
                f.write(f"支持充值的币种数量: {deposit_enabled_count}/{len(self.token_info)}\n")
                f.write(f"支持提现的币种数量: {withdraw_enabled_count}/{len(self.token_info)}\n")
                f.write(f"总网络数量: {total_networks}\n")
                f.write(f"有提现手续费信息的网络数量: {has_fee_info}/{total_networks}\n")
                f.write(f"有提现限额信息的网络数量: {has_limit_info}/{total_networks}\n")
                f.write(f"有合约地址信息的网络数量: {has_contract_info}/{total_networks}\n")
                f.write(f"已获取充值地址的网络数量: {has_deposit_address}/{total_networks}\n\n")
                
                # 添加历史记录统计
                if self.deposits_history:
                    f.write(f"\n最近充值记录: {len(self.deposits_history)}条\n")
                    currencies_set = set(deposit['currency'] for deposit in self.deposits_history)
                    f.write(f"涉及币种: {', '.join(currencies_set)}\n")
                    
                if self.withdrawals_history:
                    f.write(f"\n最近提现记录: {len(self.withdrawals_history)}条\n")
                    currencies_set = set(withdrawal['currency'] for withdrawal in self.withdrawals_history)
                    f.write(f"涉及币种: {', '.join(currencies_set)}\n")
                
                for network, tokens in self.network_groups.items():
                    f.write(f"\n{network} 网络:\n")
                    f.write(f"支持币种数量: {len(tokens)}\n")
                    f.write("支持的币种:\n")
                    for token in tokens:
                        f.write(f"  - {token['symbol']} ({token['currency']})\n")
                        if token['info']['contract_address']:
                            f.write(f"    合约地址: {token['info']['contract_address']}\n")
                        if token['info']['withdraw_fee']:
                            f.write(f"    提现费用: {token['info']['withdraw_fee']}\n")
                        if token['info']['withdraw_min']:
                            f.write(f"    最小提现: {token['info']['withdraw_min']}\n")
                        if token['info']['withdraw_max']:
                            f.write(f"    最大提现: {token['info']['withdraw_max']}\n")
                        if token['info']['deposit_address']:
                            f.write(f"    充值地址: {token['info']['deposit_address']}\n")
                            if token['info']['deposit_tag']:
                                f.write(f"    充值标签: {token['info']['deposit_tag']}\n")
                    f.write("\n")
                    
            logger.info(f"支持充值和提现的代币网络统计报告已保存到 {report_file}")
            
        except Exception as e:
            logger.error(f"保存结果时出错: {e}")
            
    def print_summary(self):
        """打印统计摘要"""
        print("\n===== Gate交易所支持充值和提现的代币信息统计 =====")
        print(f"API认证: {'已启用' if self.api_key and self.api_secret else '未启用'}")
        print(f"是否获取充值地址: {'是' if self.should_fetch_deposit_address else '否'}")
        print(f"是否获取费用信息: {'是' if self.get_fees else '否'}")
        print(f"是否获取历史记录: {'是' if self.get_history else '否'}")
        if self.limit:
            print(f"获取币种数量限制: {self.limit}")
        print(f"总USDT交易对数量: {len(self.all_usdt_pairs)}")
        print(f"支持充值和提现的USDT交易对数量: {len(self.deposit_withdraw_enabled_pairs)}")
        print(f"已获取信息的代币数量: {len(self.token_info)}")
        
        # 添加币种充提状态统计
        deposit_enabled_count = sum(1 for info in self.token_info.values() if info.get('deposit_enabled', False))
        withdraw_enabled_count = sum(1 for info in self.token_info.values() if info.get('withdraw_enabled', False))
        
        # 添加网络和手续费统计
        total_networks = sum(len(info.get('networks', [])) for info in self.token_info.values())
        has_fee_info = sum(1 for info in self.token_info.values() 
                            for network in info.get('networks', []) 
                            if network.get('withdraw_fee'))
        has_limit_info = sum(1 for info in self.token_info.values() 
                            for network in info.get('networks', []) 
                            if network.get('withdraw_min') or network.get('withdraw_max'))
        
        # 添加合约地址统计
        has_contract_info = sum(1 for info in self.token_info.values() 
                                for network in info.get('networks', []) 
                                if network.get('contract_address'))
        
        # 添加充值地址统计
        has_deposit_address = sum(1 for info in self.token_info.values() 
                                    for network in info.get('networks', []) 
                                    if network.get('deposit_address'))
                                
        print(f"支持充值的币种数量: {deposit_enabled_count}/{len(self.token_info)}")
        print(f"支持提现的币种数量: {withdraw_enabled_count}/{len(self.token_info)}")
        print(f"总网络数量: {total_networks}")
        print(f"有提现手续费信息的网络数量: {has_fee_info}/{total_networks}")
        print(f"有提现限额信息的网络数量: {has_limit_info}/{total_networks}")
        print(f"有合约地址信息的网络数量: {has_contract_info}/{total_networks}")
        print(f"已获取充值地址的网络数量: {has_deposit_address}/{total_networks}")
        
        # 添加充值提现历史统计
        if self.deposits_history:
            print(f"\n最近充值记录: {len(self.deposits_history)}条")
            currencies_set = set(deposit['currency'] for deposit in self.deposits_history)
            print(f"涉及币种: {', '.join(currencies_set)}")
            
        if self.withdrawals_history:
            print(f"\n最近提现记录: {len(self.withdrawals_history)}条")
            currencies_set = set(withdrawal['currency'] for withdrawal in self.withdrawals_history)
            print(f"涉及币种: {', '.join(currencies_set)}")
        
        print("\n网络支持统计:")
        for network, tokens in self.network_groups.items():
            print(f"\n{network}:")
            print(f"  支持币种数量: {len(tokens)}")
            print("  支持的币种:")
            for token in tokens[:5]:  # 只显示前5个
                print(f"    - {token['symbol']}")
            if len(tokens) > 5:
                print(f"    ... 等 {len(tokens)} 个币种")

    async def fetch_deposit_withdraw_fees(self, currency=None):
        """获取充值和提现费用信息"""
        try:
            logger.info(f"获取{'指定币种' if currency else '所有币种'}的充值提现费用信息...")
            
            # 使用ccxt的fetchDepositWithdrawFees获取费用
            params = {}
            if currency:
                currencies = [currency]
            else:
                currencies = [symbol.split('/')[0] for symbol in self.usdt_pairs]
                
            fees_data = {}
            
            # 分批获取以避免请求参数过长
            batch_size = 20
            for i in range(0, len(currencies), batch_size):
                batch = currencies[i:i+batch_size]
                logger.info(f"获取第 {i//batch_size + 1} 批币种费用信息 ({len(batch)}个)")
                
                loop = asyncio.get_event_loop()
                batch_fees = await loop.run_in_executor(
                    None,
                    lambda: self.exchange.fetch_deposit_withdraw_fees(batch)
                )
                
                fees_data.update(batch_fees)
                await asyncio.sleep(0.3)  # 避免请求过快
            
            logger.info(f"成功获取 {len(fees_data)} 个币种的充值提现费用信息")
            
            # 调试模式: 保存原始费用数据
            if self.debug:
                os.makedirs('data/cex/gate_info/debug', exist_ok=True)
                with open('data/cex/gate_info/debug/raw_fees.json', 'w', encoding='utf-8') as f:
                    json.dump(fees_data, f, indent=2, ensure_ascii=False)
            
            return fees_data
        except Exception as e:
            logger.error(f"获取充值提现费用信息失败: {e}")
            return {}

    async def fetch_recent_withdrawals(self, limit=20):
        """获取最近的提现记录"""
        if not self.api_key or not self.api_secret:
            logger.warning("未提供API密钥，无法获取提现记录")
            return []
            
        try:
            logger.info(f"获取最近 {limit} 条提现记录...")
            
            # 使用ccxt的fetchWithdrawals获取提现记录
            loop = asyncio.get_event_loop()
            withdrawals = await loop.run_in_executor(
                None,
                lambda: self.exchange.fetch_withdrawals(limit=limit)
            )
            
            logger.info(f"成功获取 {len(withdrawals)} 条提现记录")
            
            # 调试模式: 保存提现记录
            if self.debug and withdrawals:
                os.makedirs('data/cex/gate_info/debug', exist_ok=True)
                with open('data/cex/gate_info/debug/recent_withdrawals.json', 'w', encoding='utf-8') as f:
                    # 转换为序列化格式
                    serialized = []
                    for record in withdrawals:
                        item = {k: v for k, v in record.items() if k != 'info'}
                        # 添加必要的info字段
                        if 'txid' in record.get('info', {}):
                            item['txid'] = record['info']['txid']
                        serialized.append(item)
                    json.dump(serialized, f, indent=2, ensure_ascii=False)
            
            return withdrawals
        except Exception as e:
            logger.error(f"获取提现记录失败: {e}")
            return []

    async def fetch_recent_deposits(self, limit=20):
        """获取最近的充值记录"""
        if not self.api_key or not self.api_secret:
            logger.warning("未提供API密钥，无法获取充值记录")
            return []
            
        try:
            logger.info(f"获取最近 {limit} 条充值记录...")
            
            # 使用ccxt的fetchDeposits获取充值记录
            loop = asyncio.get_event_loop()
            deposits = await loop.run_in_executor(
                None,
                lambda: self.exchange.fetch_deposits(limit=limit)
            )
            
            logger.info(f"成功获取 {len(deposits)} 条充值记录")
            
            # 调试模式: 保存充值记录
            if self.debug and deposits:
                os.makedirs('data/cex/gate_info/debug', exist_ok=True)
                with open('data/cex/gate_info/debug/recent_deposits.json', 'w', encoding='utf-8') as f:
                    # 转换为序列化格式
                    serialized = []
                    for record in deposits:
                        item = {k: v for k, v in record.items() if k != 'info'}
                        # 添加必要的info字段
                        if 'txid' in record.get('info', {}):
                            item['txid'] = record['info']['txid']
                        serialized.append(item)
                    json.dump(serialized, f, indent=2, ensure_ascii=False)
            
            return deposits
        except Exception as e:
            logger.error(f"获取充值记录失败: {e}")
            return []
            
    def integrate_fees_info(self, token_info, fees_data):
        """整合费用信息到代币信息中"""
        if not fees_data:
            return token_info
            
        for symbol, info in token_info.items():
            currency = info['currency']
            if currency in fees_data:
                fee_info = fees_data[currency]
                
                # 获取网络特定费用
                networks = info['networks']
                for network in networks:
                    chain = network['chain']
                    
                    # 尝试找到对应网络的费用信息
                    if 'networks' in fee_info and chain in fee_info['networks']:
                        network_fee = fee_info['networks'][chain]
                        
                        # 更新提现费用
                        if not network['withdraw_fee'] and 'withdraw' in network_fee and 'fee' in network_fee['withdraw']:
                            network['withdraw_fee'] = str(network_fee['withdraw']['fee'])
                            
                        # 更新提现限制
                        if 'withdraw' in network_fee:
                            withdraw_info = network_fee['withdraw']
                            if not network['withdraw_min'] and 'min' in withdraw_info:
                                network['withdraw_min'] = str(withdraw_info['min'])
                            if not network['withdraw_max'] and 'max' in withdraw_info:
                                network['withdraw_max'] = str(withdraw_info['max'])
                    
                    # 如果没有网络特定费用，使用全局费用
                    elif not network['withdraw_fee'] and 'withdraw' in fee_info and 'fee' in fee_info['withdraw']:
                        network['withdraw_fee'] = str(fee_info['withdraw']['fee'])
                        
                    # 使用全局提现限制
                    if 'withdraw' in fee_info:
                        withdraw_info = fee_info['withdraw']
                        if not network['withdraw_min'] and 'min' in withdraw_info:
                            network['withdraw_min'] = str(withdraw_info['min'])
                        if not network['withdraw_max'] and 'max' in withdraw_info:
                            network['withdraw_max'] = str(withdraw_info['max'])
        
        return token_info

async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='获取Gate交易所支持充值和提现的代币信息')
    parser.add_argument('-l', '--limit', type=int, help='限制获取的币种数量，默认为获取所有币种')
    parser.add_argument('-d', '--debug', action='store_true', help='开启调试模式')
    parser.add_argument('-a', '--address', action='store_true', help='获取充值地址')
    parser.add_argument('-f', '--fees', action='store_true', help='获取费用信息')
    parser.add_argument('--no-fees', action='store_true', help='不获取费用信息')
    parser.add_argument('-H', '--history', action='store_true', help='获取充值提现历史')
    parser.add_argument('-s', '--symbol', help='指定要查询的交易对，例如 BTC/USDT')
    parser.add_argument('-c', '--currency', help='指定要查询的币种，例如 BTC')
    args = parser.parse_args()
    
    # 处理费用参数
    get_fees = True  # 默认获取费用
    if args.no_fees:
        get_fees = False
    if args.fees:
        get_fees = True
    
    token_info = GateTokenInfo(
        limit=args.limit, 
        debug=args.debug, 
        get_deposit_address=args.address,
        get_fees=get_fees,
        get_history=args.history
    )
    
    # 获取所有USDT交易对
    await token_info.get_all_usdt_pairs()
    
    # 处理指定交易对或币种的情况
    if args.symbol or args.currency:
        if args.symbol:
            # 验证并规范化交易对格式
            symbol = args.symbol.upper()
            if '/' not in symbol:
                symbol = f"{symbol}/USDT"  # 假设是USDT交易对
            
            # 验证交易对是否存在
            if symbol not in token_info.exchange.symbols:
                logger.error(f"交易对 {symbol} 不存在！")
                return
                
            logger.info(f"获取指定交易对 {symbol} 的信息")
            
            # 检查是否支持充值和提现
            currency = symbol.split('/')[0]
            currencies = await token_info.fetch_all_currencies()
            if currency in currencies:
                currency_info = currencies[currency]
                deposit_enabled = currency_info.get('deposit', False)
                withdraw_enabled = currency_info.get('withdraw', False)
                
                if not (deposit_enabled and withdraw_enabled):
                    logger.warning(f"交易对 {symbol} 不同时支持充值和提现，但仍将获取其信息")
            
            token_info.usdt_pairs = [symbol]
        else:  # args.currency
            # 提取币种并创建交易对
            currency = args.currency.upper()
            symbol = f"{currency}/USDT"
            
            # 验证交易对是否存在
            if symbol not in token_info.exchange.symbols:
                logger.error(f"交易对 {symbol} 不存在！")
                return
                
            logger.info(f"获取指定币种 {currency} 的信息")
            
            # 检查是否支持充值和提现
            currencies = await token_info.fetch_all_currencies()
            if currency in currencies:
                currency_info = currencies[currency]
                deposit_enabled = currency_info.get('deposit', False)
                withdraw_enabled = currency_info.get('withdraw', False)
                
                if not (deposit_enabled and withdraw_enabled):
                    logger.warning(f"币种 {currency} 不同时支持充值和提现，但仍将获取其信息")
            
            token_info.usdt_pairs = [symbol]
    else:
        # 筛选支持充值和提现的交易对
        await token_info.filter_deposit_withdraw_enabled_pairs()
    
    # 获取所有代币信息
    await token_info.get_all_token_info()
    
    # 保存结果
    token_info.save_results()
    
    # 打印统计摘要
    token_info.print_summary()

if __name__ == "__main__":
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())
