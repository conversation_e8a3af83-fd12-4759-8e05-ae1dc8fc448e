"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderData = exports.MsgCancelBinaryOptionsOrderResponse = exports.MsgCancelBinaryOptionsOrder = exports.MsgCancelDerivativeOrderResponse = exports.MsgCancelDerivativeOrder = exports.MsgCreateBinaryOptionsMarketOrderResponse = exports.MsgCreateBinaryOptionsMarketOrder = exports.DerivativeMarketOrderResults = exports.MsgCreateDerivativeMarketOrderResponse = exports.MsgCreateDerivativeMarketOrder = exports.MsgBatchUpdateOrdersResponse = exports.MsgBatchUpdateOrders = exports.MsgBatchCancelBinaryOptionsOrdersResponse = exports.MsgBatchCancelBinaryOptionsOrders = exports.MsgBatchCancelSpotOrdersResponse = exports.MsgBatchCancelSpotOrders = exports.MsgCancelSpotOrderResponse = exports.MsgCancelSpotOrder = exports.MsgBatchCreateDerivativeLimitOrdersResponse = exports.MsgBatchCreateDerivativeLimitOrders = exports.MsgCreateBinaryOptionsLimitOrderResponse = exports.MsgCreateBinaryOptionsLimitOrder = exports.MsgCreateDerivativeLimitOrderResponse = exports.MsgCreateDerivativeLimitOrder = exports.SpotMarketOrderResults = exports.MsgCreateSpotMarketOrderResponse = exports.MsgCreateSpotMarketOrder = exports.MsgInstantExpiryFuturesMarketLaunchResponse = exports.MsgInstantExpiryFuturesMarketLaunch = exports.MsgInstantBinaryOptionsMarketLaunchResponse = exports.MsgInstantBinaryOptionsMarketLaunch = exports.MsgInstantPerpetualMarketLaunchResponse = exports.MsgInstantPerpetualMarketLaunch = exports.MsgInstantSpotMarketLaunchResponse = exports.MsgInstantSpotMarketLaunch = exports.MsgBatchCreateSpotLimitOrdersResponse = exports.MsgBatchCreateSpotLimitOrders = exports.MsgCreateSpotLimitOrderResponse = exports.MsgCreateSpotLimitOrder = exports.MsgWithdrawResponse = exports.MsgWithdraw = exports.MsgDepositResponse = exports.MsgDeposit = exports.MsgUpdateParamsResponse = exports.MsgUpdateParams = exports.MsgUpdateDerivativeMarketResponse = exports.MsgUpdateDerivativeMarket = exports.MsgUpdateSpotMarketResponse = exports.MsgUpdateSpotMarket = exports.protobufPackage = void 0;
exports.MsgInstantBinaryOptionsMarketLaunchDesc = exports.MsgBatchCancelDerivativeOrdersDesc = exports.MsgCancelDerivativeOrderDesc = exports.MsgCreateDerivativeMarketOrderDesc = exports.MsgBatchCreateDerivativeLimitOrdersDesc = exports.MsgCreateDerivativeLimitOrderDesc = exports.MsgPrivilegedExecuteContractDesc = exports.MsgBatchUpdateOrdersDesc = exports.MsgBatchCancelSpotOrdersDesc = exports.MsgCancelSpotOrderDesc = exports.MsgCreateSpotMarketOrderDesc = exports.MsgBatchCreateSpotLimitOrdersDesc = exports.MsgCreateSpotLimitOrderDesc = exports.MsgInstantExpiryFuturesMarketLaunchDesc = exports.MsgInstantPerpetualMarketLaunchDesc = exports.MsgInstantSpotMarketLaunchDesc = exports.MsgWithdrawDesc = exports.MsgDepositDesc = exports.MsgDesc = exports.MsgClientImpl = exports.MsgBatchExchangeModificationResponse = exports.MsgBatchExchangeModification = exports.MsgActivateStakeGrantResponse = exports.MsgActivateStakeGrant = exports.MsgAuthorizeStakeGrantsResponse = exports.MsgAuthorizeStakeGrants = exports.MsgAdminUpdateBinaryOptionsMarketResponse = exports.MsgAdminUpdateBinaryOptionsMarket = exports.MsgSignDoc = exports.MsgSignData = exports.MsgReclaimLockedFundsResponse = exports.MsgReclaimLockedFunds = exports.MsgRewardsOptOutResponse = exports.MsgRewardsOptOut = exports.MsgPrivilegedExecuteContractResponse = exports.MsgPrivilegedExecuteContract = exports.MsgDecreasePositionMarginResponse = exports.MsgDecreasePositionMargin = exports.MsgIncreasePositionMarginResponse = exports.MsgIncreasePositionMargin = exports.MsgEmergencySettleMarketResponse = exports.MsgEmergencySettleMarket = exports.MsgLiquidatePositionResponse = exports.MsgLiquidatePosition = exports.MsgExternalTransferResponse = exports.MsgExternalTransfer = exports.MsgSubaccountTransferResponse = exports.MsgSubaccountTransfer = exports.MsgBatchCancelDerivativeOrdersResponse = exports.MsgBatchCancelDerivativeOrders = void 0;
exports.GrpcWebError = exports.GrpcWebImpl = exports.MsgBatchExchangeModificationDesc = exports.MsgActivateStakeGrantDesc = exports.MsgAuthorizeStakeGrantsDesc = exports.MsgUpdateDerivativeMarketDesc = exports.MsgUpdateSpotMarketDesc = exports.MsgUpdateParamsDesc = exports.MsgAdminUpdateBinaryOptionsMarketDesc = exports.MsgRewardsOptOutDesc = exports.MsgDecreasePositionMarginDesc = exports.MsgIncreasePositionMarginDesc = exports.MsgEmergencySettleMarketDesc = exports.MsgLiquidatePositionDesc = exports.MsgExternalTransferDesc = exports.MsgSubaccountTransferDesc = exports.MsgBatchCancelBinaryOptionsOrdersDesc = exports.MsgCancelBinaryOptionsOrderDesc = exports.MsgCreateBinaryOptionsMarketOrderDesc = exports.MsgCreateBinaryOptionsLimitOrderDesc = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var oracle_1 = require("../../oracle/v1beta1/oracle.js");
var exchange_1 = require("./exchange.js");
var proposal_1 = require("./proposal.js");
exports.protobufPackage = "injective.exchange.v1beta1";
function createBaseMsgUpdateSpotMarket() {
    return {
        admin: "",
        marketId: "",
        newTicker: "",
        newMinPriceTickSize: "",
        newMinQuantityTickSize: "",
        newMinNotional: "",
    };
}
exports.MsgUpdateSpotMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.admin !== "") {
            writer.uint32(10).string(message.admin);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.newTicker !== "") {
            writer.uint32(26).string(message.newTicker);
        }
        if (message.newMinPriceTickSize !== "") {
            writer.uint32(34).string(message.newMinPriceTickSize);
        }
        if (message.newMinQuantityTickSize !== "") {
            writer.uint32(42).string(message.newMinQuantityTickSize);
        }
        if (message.newMinNotional !== "") {
            writer.uint32(50).string(message.newMinNotional);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateSpotMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.admin = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.newTicker = reader.string();
                    break;
                case 4:
                    message.newMinPriceTickSize = reader.string();
                    break;
                case 5:
                    message.newMinQuantityTickSize = reader.string();
                    break;
                case 6:
                    message.newMinNotional = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            admin: isSet(object.admin) ? String(object.admin) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            newTicker: isSet(object.newTicker) ? String(object.newTicker) : "",
            newMinPriceTickSize: isSet(object.newMinPriceTickSize) ? String(object.newMinPriceTickSize) : "",
            newMinQuantityTickSize: isSet(object.newMinQuantityTickSize) ? String(object.newMinQuantityTickSize) : "",
            newMinNotional: isSet(object.newMinNotional) ? String(object.newMinNotional) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.admin !== undefined && (obj.admin = message.admin);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.newTicker !== undefined && (obj.newTicker = message.newTicker);
        message.newMinPriceTickSize !== undefined && (obj.newMinPriceTickSize = message.newMinPriceTickSize);
        message.newMinQuantityTickSize !== undefined && (obj.newMinQuantityTickSize = message.newMinQuantityTickSize);
        message.newMinNotional !== undefined && (obj.newMinNotional = message.newMinNotional);
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateSpotMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseMsgUpdateSpotMarket();
        message.admin = (_a = object.admin) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.newTicker = (_c = object.newTicker) !== null && _c !== void 0 ? _c : "";
        message.newMinPriceTickSize = (_d = object.newMinPriceTickSize) !== null && _d !== void 0 ? _d : "";
        message.newMinQuantityTickSize = (_e = object.newMinQuantityTickSize) !== null && _e !== void 0 ? _e : "";
        message.newMinNotional = (_f = object.newMinNotional) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseMsgUpdateSpotMarketResponse() {
    return {};
}
exports.MsgUpdateSpotMarketResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateSpotMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateSpotMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateSpotMarketResponse();
        return message;
    },
};
function createBaseMsgUpdateDerivativeMarket() {
    return {
        admin: "",
        marketId: "",
        newTicker: "",
        newMinPriceTickSize: "",
        newMinQuantityTickSize: "",
        newMinNotional: "",
        newInitialMarginRatio: "",
        newMaintenanceMarginRatio: "",
    };
}
exports.MsgUpdateDerivativeMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.admin !== "") {
            writer.uint32(10).string(message.admin);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.newTicker !== "") {
            writer.uint32(26).string(message.newTicker);
        }
        if (message.newMinPriceTickSize !== "") {
            writer.uint32(34).string(message.newMinPriceTickSize);
        }
        if (message.newMinQuantityTickSize !== "") {
            writer.uint32(42).string(message.newMinQuantityTickSize);
        }
        if (message.newMinNotional !== "") {
            writer.uint32(50).string(message.newMinNotional);
        }
        if (message.newInitialMarginRatio !== "") {
            writer.uint32(58).string(message.newInitialMarginRatio);
        }
        if (message.newMaintenanceMarginRatio !== "") {
            writer.uint32(66).string(message.newMaintenanceMarginRatio);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateDerivativeMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.admin = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.newTicker = reader.string();
                    break;
                case 4:
                    message.newMinPriceTickSize = reader.string();
                    break;
                case 5:
                    message.newMinQuantityTickSize = reader.string();
                    break;
                case 6:
                    message.newMinNotional = reader.string();
                    break;
                case 7:
                    message.newInitialMarginRatio = reader.string();
                    break;
                case 8:
                    message.newMaintenanceMarginRatio = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            admin: isSet(object.admin) ? String(object.admin) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            newTicker: isSet(object.newTicker) ? String(object.newTicker) : "",
            newMinPriceTickSize: isSet(object.newMinPriceTickSize) ? String(object.newMinPriceTickSize) : "",
            newMinQuantityTickSize: isSet(object.newMinQuantityTickSize) ? String(object.newMinQuantityTickSize) : "",
            newMinNotional: isSet(object.newMinNotional) ? String(object.newMinNotional) : "",
            newInitialMarginRatio: isSet(object.newInitialMarginRatio) ? String(object.newInitialMarginRatio) : "",
            newMaintenanceMarginRatio: isSet(object.newMaintenanceMarginRatio)
                ? String(object.newMaintenanceMarginRatio)
                : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.admin !== undefined && (obj.admin = message.admin);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.newTicker !== undefined && (obj.newTicker = message.newTicker);
        message.newMinPriceTickSize !== undefined && (obj.newMinPriceTickSize = message.newMinPriceTickSize);
        message.newMinQuantityTickSize !== undefined && (obj.newMinQuantityTickSize = message.newMinQuantityTickSize);
        message.newMinNotional !== undefined && (obj.newMinNotional = message.newMinNotional);
        message.newInitialMarginRatio !== undefined && (obj.newInitialMarginRatio = message.newInitialMarginRatio);
        message.newMaintenanceMarginRatio !== undefined &&
            (obj.newMaintenanceMarginRatio = message.newMaintenanceMarginRatio);
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateDerivativeMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseMsgUpdateDerivativeMarket();
        message.admin = (_a = object.admin) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.newTicker = (_c = object.newTicker) !== null && _c !== void 0 ? _c : "";
        message.newMinPriceTickSize = (_d = object.newMinPriceTickSize) !== null && _d !== void 0 ? _d : "";
        message.newMinQuantityTickSize = (_e = object.newMinQuantityTickSize) !== null && _e !== void 0 ? _e : "";
        message.newMinNotional = (_f = object.newMinNotional) !== null && _f !== void 0 ? _f : "";
        message.newInitialMarginRatio = (_g = object.newInitialMarginRatio) !== null && _g !== void 0 ? _g : "";
        message.newMaintenanceMarginRatio = (_h = object.newMaintenanceMarginRatio) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseMsgUpdateDerivativeMarketResponse() {
    return {};
}
exports.MsgUpdateDerivativeMarketResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateDerivativeMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateDerivativeMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateDerivativeMarketResponse();
        return message;
    },
};
function createBaseMsgUpdateParams() {
    return { authority: "", params: undefined };
}
exports.MsgUpdateParams = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.authority !== "") {
            writer.uint32(10).string(message.authority);
        }
        if (message.params !== undefined) {
            exchange_1.Params.encode(message.params, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.authority = reader.string();
                    break;
                case 2:
                    message.params = exchange_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            authority: isSet(object.authority) ? String(object.authority) : "",
            params: isSet(object.params) ? exchange_1.Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.authority !== undefined && (obj.authority = message.authority);
        message.params !== undefined && (obj.params = message.params ? exchange_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgUpdateParams();
        message.authority = (_a = object.authority) !== null && _a !== void 0 ? _a : "";
        message.params = (object.params !== undefined && object.params !== null)
            ? exchange_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseMsgUpdateParamsResponse() {
    return {};
}
exports.MsgUpdateParamsResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateParamsResponse();
        return message;
    },
};
function createBaseMsgDeposit() {
    return { sender: "", subaccountId: "", amount: undefined };
}
exports.MsgDeposit = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgDeposit();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgDeposit.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgDeposit();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseMsgDepositResponse() {
    return {};
}
exports.MsgDepositResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgDepositResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgDepositResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgDepositResponse();
        return message;
    },
};
function createBaseMsgWithdraw() {
    return { sender: "", subaccountId: "", amount: undefined };
}
exports.MsgWithdraw = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgWithdraw();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgWithdraw.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgWithdraw();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseMsgWithdrawResponse() {
    return {};
}
exports.MsgWithdrawResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgWithdrawResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgWithdrawResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgWithdrawResponse();
        return message;
    },
};
function createBaseMsgCreateSpotLimitOrder() {
    return { sender: "", order: undefined };
}
exports.MsgCreateSpotLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.order !== undefined) {
            exchange_1.SpotOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateSpotLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.SpotOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            order: isSet(object.order) ? exchange_1.SpotOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.order !== undefined && (obj.order = message.order ? exchange_1.SpotOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateSpotLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgCreateSpotLimitOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.SpotOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateSpotLimitOrderResponse() {
    return { orderHash: "", cid: "" };
}
exports.MsgCreateSpotLimitOrderResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderHash !== "") {
            writer.uint32(10).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(18).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateSpotLimitOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHash = reader.string();
                    break;
                case 2:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateSpotLimitOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgCreateSpotLimitOrderResponse();
        message.orderHash = (_a = object.orderHash) !== null && _a !== void 0 ? _a : "";
        message.cid = (_b = object.cid) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgBatchCreateSpotLimitOrders() {
    return { sender: "", orders: [] };
}
exports.MsgBatchCreateSpotLimitOrders = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.SpotOrder.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCreateSpotLimitOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.orders.push(exchange_1.SpotOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders) ? object.orders.map(function (e) { return exchange_1.SpotOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exchange_1.SpotOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCreateSpotLimitOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgBatchCreateSpotLimitOrders();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.orders = ((_b = object.orders) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.SpotOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgBatchCreateSpotLimitOrdersResponse() {
    return { orderHashes: [], createdOrdersCids: [], failedOrdersCids: [] };
}
exports.MsgBatchCreateSpotLimitOrdersResponse = {
    encode: function (message, writer) {
        var e_2, _a, e_3, _b, e_4, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _d = __values(message.orderHashes), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _f = __values(message.createdOrdersCids), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _h = __values(message.failedOrdersCids), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCreateSpotLimitOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHashes.push(reader.string());
                    break;
                case 2:
                    message.createdOrdersCids.push(reader.string());
                    break;
                case 3:
                    message.failedOrdersCids.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHashes: Array.isArray(object === null || object === void 0 ? void 0 : object.orderHashes) ? object.orderHashes.map(function (e) { return String(e); }) : [],
            createdOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.createdOrdersCids)
                ? object.createdOrdersCids.map(function (e) { return String(e); })
                : [],
            failedOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.failedOrdersCids)
                ? object.failedOrdersCids.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orderHashes) {
            obj.orderHashes = message.orderHashes.map(function (e) { return e; });
        }
        else {
            obj.orderHashes = [];
        }
        if (message.createdOrdersCids) {
            obj.createdOrdersCids = message.createdOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.createdOrdersCids = [];
        }
        if (message.failedOrdersCids) {
            obj.failedOrdersCids = message.failedOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.failedOrdersCids = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCreateSpotLimitOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgBatchCreateSpotLimitOrdersResponse();
        message.orderHashes = ((_a = object.orderHashes) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.createdOrdersCids = ((_b = object.createdOrdersCids) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.failedOrdersCids = ((_c = object.failedOrdersCids) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgInstantSpotMarketLaunch() {
    return {
        sender: "",
        ticker: "",
        baseDenom: "",
        quoteDenom: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
        baseDecimals: 0,
        quoteDecimals: 0,
    };
}
exports.MsgInstantSpotMarketLaunch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.ticker !== "") {
            writer.uint32(18).string(message.ticker);
        }
        if (message.baseDenom !== "") {
            writer.uint32(26).string(message.baseDenom);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(34).string(message.quoteDenom);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(42).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(50).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(58).string(message.minNotional);
        }
        if (message.baseDecimals !== 0) {
            writer.uint32(64).uint32(message.baseDecimals);
        }
        if (message.quoteDecimals !== 0) {
            writer.uint32(72).uint32(message.quoteDecimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantSpotMarketLaunch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.ticker = reader.string();
                    break;
                case 3:
                    message.baseDenom = reader.string();
                    break;
                case 4:
                    message.quoteDenom = reader.string();
                    break;
                case 5:
                    message.minPriceTickSize = reader.string();
                    break;
                case 6:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 7:
                    message.minNotional = reader.string();
                    break;
                case 8:
                    message.baseDecimals = reader.uint32();
                    break;
                case 9:
                    message.quoteDecimals = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            baseDenom: isSet(object.baseDenom) ? String(object.baseDenom) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            baseDecimals: isSet(object.baseDecimals) ? Number(object.baseDecimals) : 0,
            quoteDecimals: isSet(object.quoteDecimals) ? Number(object.quoteDecimals) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.baseDenom !== undefined && (obj.baseDenom = message.baseDenom);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.baseDecimals !== undefined && (obj.baseDecimals = Math.round(message.baseDecimals));
        message.quoteDecimals !== undefined && (obj.quoteDecimals = Math.round(message.quoteDecimals));
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantSpotMarketLaunch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseMsgInstantSpotMarketLaunch();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.ticker = (_b = object.ticker) !== null && _b !== void 0 ? _b : "";
        message.baseDenom = (_c = object.baseDenom) !== null && _c !== void 0 ? _c : "";
        message.quoteDenom = (_d = object.quoteDenom) !== null && _d !== void 0 ? _d : "";
        message.minPriceTickSize = (_e = object.minPriceTickSize) !== null && _e !== void 0 ? _e : "";
        message.minQuantityTickSize = (_f = object.minQuantityTickSize) !== null && _f !== void 0 ? _f : "";
        message.minNotional = (_g = object.minNotional) !== null && _g !== void 0 ? _g : "";
        message.baseDecimals = (_h = object.baseDecimals) !== null && _h !== void 0 ? _h : 0;
        message.quoteDecimals = (_j = object.quoteDecimals) !== null && _j !== void 0 ? _j : 0;
        return message;
    },
};
function createBaseMsgInstantSpotMarketLaunchResponse() {
    return {};
}
exports.MsgInstantSpotMarketLaunchResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantSpotMarketLaunchResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantSpotMarketLaunchResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgInstantSpotMarketLaunchResponse();
        return message;
    },
};
function createBaseMsgInstantPerpetualMarketLaunch() {
    return {
        sender: "",
        ticker: "",
        quoteDenom: "",
        oracleBase: "",
        oracleQuote: "",
        oracleScaleFactor: 0,
        oracleType: 0,
        makerFeeRate: "",
        takerFeeRate: "",
        initialMarginRatio: "",
        maintenanceMarginRatio: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
    };
}
exports.MsgInstantPerpetualMarketLaunch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.ticker !== "") {
            writer.uint32(18).string(message.ticker);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(26).string(message.quoteDenom);
        }
        if (message.oracleBase !== "") {
            writer.uint32(34).string(message.oracleBase);
        }
        if (message.oracleQuote !== "") {
            writer.uint32(42).string(message.oracleQuote);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(48).uint32(message.oracleScaleFactor);
        }
        if (message.oracleType !== 0) {
            writer.uint32(56).int32(message.oracleType);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(66).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(74).string(message.takerFeeRate);
        }
        if (message.initialMarginRatio !== "") {
            writer.uint32(82).string(message.initialMarginRatio);
        }
        if (message.maintenanceMarginRatio !== "") {
            writer.uint32(90).string(message.maintenanceMarginRatio);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(98).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(106).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(114).string(message.minNotional);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantPerpetualMarketLaunch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.ticker = reader.string();
                    break;
                case 3:
                    message.quoteDenom = reader.string();
                    break;
                case 4:
                    message.oracleBase = reader.string();
                    break;
                case 5:
                    message.oracleQuote = reader.string();
                    break;
                case 6:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 7:
                    message.oracleType = reader.int32();
                    break;
                case 8:
                    message.makerFeeRate = reader.string();
                    break;
                case 9:
                    message.takerFeeRate = reader.string();
                    break;
                case 10:
                    message.initialMarginRatio = reader.string();
                    break;
                case 11:
                    message.maintenanceMarginRatio = reader.string();
                    break;
                case 12:
                    message.minPriceTickSize = reader.string();
                    break;
                case 13:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 14:
                    message.minNotional = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            oracleBase: isSet(object.oracleBase) ? String(object.oracleBase) : "",
            oracleQuote: isSet(object.oracleQuote) ? String(object.oracleQuote) : "",
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            initialMarginRatio: isSet(object.initialMarginRatio) ? String(object.initialMarginRatio) : "",
            maintenanceMarginRatio: isSet(object.maintenanceMarginRatio) ? String(object.maintenanceMarginRatio) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.oracleBase !== undefined && (obj.oracleBase = message.oracleBase);
        message.oracleQuote !== undefined && (obj.oracleQuote = message.oracleQuote);
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.initialMarginRatio !== undefined && (obj.initialMarginRatio = message.initialMarginRatio);
        message.maintenanceMarginRatio !== undefined && (obj.maintenanceMarginRatio = message.maintenanceMarginRatio);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantPerpetualMarketLaunch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        var message = createBaseMsgInstantPerpetualMarketLaunch();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.ticker = (_b = object.ticker) !== null && _b !== void 0 ? _b : "";
        message.quoteDenom = (_c = object.quoteDenom) !== null && _c !== void 0 ? _c : "";
        message.oracleBase = (_d = object.oracleBase) !== null && _d !== void 0 ? _d : "";
        message.oracleQuote = (_e = object.oracleQuote) !== null && _e !== void 0 ? _e : "";
        message.oracleScaleFactor = (_f = object.oracleScaleFactor) !== null && _f !== void 0 ? _f : 0;
        message.oracleType = (_g = object.oracleType) !== null && _g !== void 0 ? _g : 0;
        message.makerFeeRate = (_h = object.makerFeeRate) !== null && _h !== void 0 ? _h : "";
        message.takerFeeRate = (_j = object.takerFeeRate) !== null && _j !== void 0 ? _j : "";
        message.initialMarginRatio = (_k = object.initialMarginRatio) !== null && _k !== void 0 ? _k : "";
        message.maintenanceMarginRatio = (_l = object.maintenanceMarginRatio) !== null && _l !== void 0 ? _l : "";
        message.minPriceTickSize = (_m = object.minPriceTickSize) !== null && _m !== void 0 ? _m : "";
        message.minQuantityTickSize = (_o = object.minQuantityTickSize) !== null && _o !== void 0 ? _o : "";
        message.minNotional = (_p = object.minNotional) !== null && _p !== void 0 ? _p : "";
        return message;
    },
};
function createBaseMsgInstantPerpetualMarketLaunchResponse() {
    return {};
}
exports.MsgInstantPerpetualMarketLaunchResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantPerpetualMarketLaunchResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantPerpetualMarketLaunchResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgInstantPerpetualMarketLaunchResponse();
        return message;
    },
};
function createBaseMsgInstantBinaryOptionsMarketLaunch() {
    return {
        sender: "",
        ticker: "",
        oracleSymbol: "",
        oracleProvider: "",
        oracleType: 0,
        oracleScaleFactor: 0,
        makerFeeRate: "",
        takerFeeRate: "",
        expirationTimestamp: "0",
        settlementTimestamp: "0",
        admin: "",
        quoteDenom: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
    };
}
exports.MsgInstantBinaryOptionsMarketLaunch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.ticker !== "") {
            writer.uint32(18).string(message.ticker);
        }
        if (message.oracleSymbol !== "") {
            writer.uint32(26).string(message.oracleSymbol);
        }
        if (message.oracleProvider !== "") {
            writer.uint32(34).string(message.oracleProvider);
        }
        if (message.oracleType !== 0) {
            writer.uint32(40).int32(message.oracleType);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(48).uint32(message.oracleScaleFactor);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(58).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(66).string(message.takerFeeRate);
        }
        if (message.expirationTimestamp !== "0") {
            writer.uint32(72).int64(message.expirationTimestamp);
        }
        if (message.settlementTimestamp !== "0") {
            writer.uint32(80).int64(message.settlementTimestamp);
        }
        if (message.admin !== "") {
            writer.uint32(90).string(message.admin);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(98).string(message.quoteDenom);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(106).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(114).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(122).string(message.minNotional);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantBinaryOptionsMarketLaunch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.ticker = reader.string();
                    break;
                case 3:
                    message.oracleSymbol = reader.string();
                    break;
                case 4:
                    message.oracleProvider = reader.string();
                    break;
                case 5:
                    message.oracleType = reader.int32();
                    break;
                case 6:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 7:
                    message.makerFeeRate = reader.string();
                    break;
                case 8:
                    message.takerFeeRate = reader.string();
                    break;
                case 9:
                    message.expirationTimestamp = longToString(reader.int64());
                    break;
                case 10:
                    message.settlementTimestamp = longToString(reader.int64());
                    break;
                case 11:
                    message.admin = reader.string();
                    break;
                case 12:
                    message.quoteDenom = reader.string();
                    break;
                case 13:
                    message.minPriceTickSize = reader.string();
                    break;
                case 14:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 15:
                    message.minNotional = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            oracleSymbol: isSet(object.oracleSymbol) ? String(object.oracleSymbol) : "",
            oracleProvider: isSet(object.oracleProvider) ? String(object.oracleProvider) : "",
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            expirationTimestamp: isSet(object.expirationTimestamp) ? String(object.expirationTimestamp) : "0",
            settlementTimestamp: isSet(object.settlementTimestamp) ? String(object.settlementTimestamp) : "0",
            admin: isSet(object.admin) ? String(object.admin) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.oracleSymbol !== undefined && (obj.oracleSymbol = message.oracleSymbol);
        message.oracleProvider !== undefined && (obj.oracleProvider = message.oracleProvider);
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.expirationTimestamp !== undefined && (obj.expirationTimestamp = message.expirationTimestamp);
        message.settlementTimestamp !== undefined && (obj.settlementTimestamp = message.settlementTimestamp);
        message.admin !== undefined && (obj.admin = message.admin);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantBinaryOptionsMarketLaunch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        var message = createBaseMsgInstantBinaryOptionsMarketLaunch();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.ticker = (_b = object.ticker) !== null && _b !== void 0 ? _b : "";
        message.oracleSymbol = (_c = object.oracleSymbol) !== null && _c !== void 0 ? _c : "";
        message.oracleProvider = (_d = object.oracleProvider) !== null && _d !== void 0 ? _d : "";
        message.oracleType = (_e = object.oracleType) !== null && _e !== void 0 ? _e : 0;
        message.oracleScaleFactor = (_f = object.oracleScaleFactor) !== null && _f !== void 0 ? _f : 0;
        message.makerFeeRate = (_g = object.makerFeeRate) !== null && _g !== void 0 ? _g : "";
        message.takerFeeRate = (_h = object.takerFeeRate) !== null && _h !== void 0 ? _h : "";
        message.expirationTimestamp = (_j = object.expirationTimestamp) !== null && _j !== void 0 ? _j : "0";
        message.settlementTimestamp = (_k = object.settlementTimestamp) !== null && _k !== void 0 ? _k : "0";
        message.admin = (_l = object.admin) !== null && _l !== void 0 ? _l : "";
        message.quoteDenom = (_m = object.quoteDenom) !== null && _m !== void 0 ? _m : "";
        message.minPriceTickSize = (_o = object.minPriceTickSize) !== null && _o !== void 0 ? _o : "";
        message.minQuantityTickSize = (_p = object.minQuantityTickSize) !== null && _p !== void 0 ? _p : "";
        message.minNotional = (_q = object.minNotional) !== null && _q !== void 0 ? _q : "";
        return message;
    },
};
function createBaseMsgInstantBinaryOptionsMarketLaunchResponse() {
    return {};
}
exports.MsgInstantBinaryOptionsMarketLaunchResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantBinaryOptionsMarketLaunchResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantBinaryOptionsMarketLaunchResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgInstantBinaryOptionsMarketLaunchResponse();
        return message;
    },
};
function createBaseMsgInstantExpiryFuturesMarketLaunch() {
    return {
        sender: "",
        ticker: "",
        quoteDenom: "",
        oracleBase: "",
        oracleQuote: "",
        oracleType: 0,
        oracleScaleFactor: 0,
        expiry: "0",
        makerFeeRate: "",
        takerFeeRate: "",
        initialMarginRatio: "",
        maintenanceMarginRatio: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
    };
}
exports.MsgInstantExpiryFuturesMarketLaunch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.ticker !== "") {
            writer.uint32(18).string(message.ticker);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(26).string(message.quoteDenom);
        }
        if (message.oracleBase !== "") {
            writer.uint32(34).string(message.oracleBase);
        }
        if (message.oracleQuote !== "") {
            writer.uint32(42).string(message.oracleQuote);
        }
        if (message.oracleType !== 0) {
            writer.uint32(48).int32(message.oracleType);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(56).uint32(message.oracleScaleFactor);
        }
        if (message.expiry !== "0") {
            writer.uint32(64).int64(message.expiry);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(74).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(82).string(message.takerFeeRate);
        }
        if (message.initialMarginRatio !== "") {
            writer.uint32(90).string(message.initialMarginRatio);
        }
        if (message.maintenanceMarginRatio !== "") {
            writer.uint32(98).string(message.maintenanceMarginRatio);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(106).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(114).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(122).string(message.minNotional);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantExpiryFuturesMarketLaunch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.ticker = reader.string();
                    break;
                case 3:
                    message.quoteDenom = reader.string();
                    break;
                case 4:
                    message.oracleBase = reader.string();
                    break;
                case 5:
                    message.oracleQuote = reader.string();
                    break;
                case 6:
                    message.oracleType = reader.int32();
                    break;
                case 7:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 8:
                    message.expiry = longToString(reader.int64());
                    break;
                case 9:
                    message.makerFeeRate = reader.string();
                    break;
                case 10:
                    message.takerFeeRate = reader.string();
                    break;
                case 11:
                    message.initialMarginRatio = reader.string();
                    break;
                case 12:
                    message.maintenanceMarginRatio = reader.string();
                    break;
                case 13:
                    message.minPriceTickSize = reader.string();
                    break;
                case 14:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 15:
                    message.minNotional = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            oracleBase: isSet(object.oracleBase) ? String(object.oracleBase) : "",
            oracleQuote: isSet(object.oracleQuote) ? String(object.oracleQuote) : "",
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            expiry: isSet(object.expiry) ? String(object.expiry) : "0",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            initialMarginRatio: isSet(object.initialMarginRatio) ? String(object.initialMarginRatio) : "",
            maintenanceMarginRatio: isSet(object.maintenanceMarginRatio) ? String(object.maintenanceMarginRatio) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.oracleBase !== undefined && (obj.oracleBase = message.oracleBase);
        message.oracleQuote !== undefined && (obj.oracleQuote = message.oracleQuote);
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.expiry !== undefined && (obj.expiry = message.expiry);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.initialMarginRatio !== undefined && (obj.initialMarginRatio = message.initialMarginRatio);
        message.maintenanceMarginRatio !== undefined && (obj.maintenanceMarginRatio = message.maintenanceMarginRatio);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantExpiryFuturesMarketLaunch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        var message = createBaseMsgInstantExpiryFuturesMarketLaunch();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.ticker = (_b = object.ticker) !== null && _b !== void 0 ? _b : "";
        message.quoteDenom = (_c = object.quoteDenom) !== null && _c !== void 0 ? _c : "";
        message.oracleBase = (_d = object.oracleBase) !== null && _d !== void 0 ? _d : "";
        message.oracleQuote = (_e = object.oracleQuote) !== null && _e !== void 0 ? _e : "";
        message.oracleType = (_f = object.oracleType) !== null && _f !== void 0 ? _f : 0;
        message.oracleScaleFactor = (_g = object.oracleScaleFactor) !== null && _g !== void 0 ? _g : 0;
        message.expiry = (_h = object.expiry) !== null && _h !== void 0 ? _h : "0";
        message.makerFeeRate = (_j = object.makerFeeRate) !== null && _j !== void 0 ? _j : "";
        message.takerFeeRate = (_k = object.takerFeeRate) !== null && _k !== void 0 ? _k : "";
        message.initialMarginRatio = (_l = object.initialMarginRatio) !== null && _l !== void 0 ? _l : "";
        message.maintenanceMarginRatio = (_m = object.maintenanceMarginRatio) !== null && _m !== void 0 ? _m : "";
        message.minPriceTickSize = (_o = object.minPriceTickSize) !== null && _o !== void 0 ? _o : "";
        message.minQuantityTickSize = (_p = object.minQuantityTickSize) !== null && _p !== void 0 ? _p : "";
        message.minNotional = (_q = object.minNotional) !== null && _q !== void 0 ? _q : "";
        return message;
    },
};
function createBaseMsgInstantExpiryFuturesMarketLaunchResponse() {
    return {};
}
exports.MsgInstantExpiryFuturesMarketLaunchResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgInstantExpiryFuturesMarketLaunchResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgInstantExpiryFuturesMarketLaunchResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgInstantExpiryFuturesMarketLaunchResponse();
        return message;
    },
};
function createBaseMsgCreateSpotMarketOrder() {
    return { sender: "", order: undefined };
}
exports.MsgCreateSpotMarketOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.order !== undefined) {
            exchange_1.SpotOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateSpotMarketOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.SpotOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            order: isSet(object.order) ? exchange_1.SpotOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.order !== undefined && (obj.order = message.order ? exchange_1.SpotOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateSpotMarketOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgCreateSpotMarketOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.SpotOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateSpotMarketOrderResponse() {
    return { orderHash: "", results: undefined, cid: "" };
}
exports.MsgCreateSpotMarketOrderResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderHash !== "") {
            writer.uint32(10).string(message.orderHash);
        }
        if (message.results !== undefined) {
            exports.SpotMarketOrderResults.encode(message.results, writer.uint32(18).fork()).ldelim();
        }
        if (message.cid !== "") {
            writer.uint32(26).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateSpotMarketOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHash = reader.string();
                    break;
                case 2:
                    message.results = exports.SpotMarketOrderResults.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            results: isSet(object.results) ? exports.SpotMarketOrderResults.fromJSON(object.results) : undefined,
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.results !== undefined &&
            (obj.results = message.results ? exports.SpotMarketOrderResults.toJSON(message.results) : undefined);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateSpotMarketOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgCreateSpotMarketOrderResponse();
        message.orderHash = (_a = object.orderHash) !== null && _a !== void 0 ? _a : "";
        message.results = (object.results !== undefined && object.results !== null)
            ? exports.SpotMarketOrderResults.fromPartial(object.results)
            : undefined;
        message.cid = (_b = object.cid) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseSpotMarketOrderResults() {
    return { quantity: "", price: "", fee: "" };
}
exports.SpotMarketOrderResults = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.quantity !== "") {
            writer.uint32(10).string(message.quantity);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        if (message.fee !== "") {
            writer.uint32(26).string(message.fee);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotMarketOrderResults();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.quantity = reader.string();
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                case 3:
                    message.fee = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            price: isSet(object.price) ? String(object.price) : "",
            fee: isSet(object.fee) ? String(object.fee) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.price !== undefined && (obj.price = message.price);
        message.fee !== undefined && (obj.fee = message.fee);
        return obj;
    },
    create: function (base) {
        return exports.SpotMarketOrderResults.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSpotMarketOrderResults();
        message.quantity = (_a = object.quantity) !== null && _a !== void 0 ? _a : "";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "";
        message.fee = (_c = object.fee) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseMsgCreateDerivativeLimitOrder() {
    return { sender: "", order: undefined };
}
exports.MsgCreateDerivativeLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.order !== undefined) {
            exchange_1.DerivativeOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateDerivativeLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.DerivativeOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            order: isSet(object.order) ? exchange_1.DerivativeOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.order !== undefined && (obj.order = message.order ? exchange_1.DerivativeOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateDerivativeLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgCreateDerivativeLimitOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.DerivativeOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateDerivativeLimitOrderResponse() {
    return { orderHash: "", cid: "" };
}
exports.MsgCreateDerivativeLimitOrderResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderHash !== "") {
            writer.uint32(10).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(18).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateDerivativeLimitOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHash = reader.string();
                    break;
                case 2:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateDerivativeLimitOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgCreateDerivativeLimitOrderResponse();
        message.orderHash = (_a = object.orderHash) !== null && _a !== void 0 ? _a : "";
        message.cid = (_b = object.cid) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgCreateBinaryOptionsLimitOrder() {
    return { sender: "", order: undefined };
}
exports.MsgCreateBinaryOptionsLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.order !== undefined) {
            exchange_1.DerivativeOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateBinaryOptionsLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.DerivativeOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            order: isSet(object.order) ? exchange_1.DerivativeOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.order !== undefined && (obj.order = message.order ? exchange_1.DerivativeOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateBinaryOptionsLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgCreateBinaryOptionsLimitOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.DerivativeOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateBinaryOptionsLimitOrderResponse() {
    return { orderHash: "", cid: "" };
}
exports.MsgCreateBinaryOptionsLimitOrderResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderHash !== "") {
            writer.uint32(10).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(18).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateBinaryOptionsLimitOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHash = reader.string();
                    break;
                case 2:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateBinaryOptionsLimitOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgCreateBinaryOptionsLimitOrderResponse();
        message.orderHash = (_a = object.orderHash) !== null && _a !== void 0 ? _a : "";
        message.cid = (_b = object.cid) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgBatchCreateDerivativeLimitOrders() {
    return { sender: "", orders: [] };
}
exports.MsgBatchCreateDerivativeLimitOrders = {
    encode: function (message, writer) {
        var e_5, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DerivativeOrder.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCreateDerivativeLimitOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.orders.push(exchange_1.DerivativeOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders) ? object.orders.map(function (e) { return exchange_1.DerivativeOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exchange_1.DerivativeOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCreateDerivativeLimitOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgBatchCreateDerivativeLimitOrders();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.orders = ((_b = object.orders) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.DerivativeOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgBatchCreateDerivativeLimitOrdersResponse() {
    return { orderHashes: [], createdOrdersCids: [], failedOrdersCids: [] };
}
exports.MsgBatchCreateDerivativeLimitOrdersResponse = {
    encode: function (message, writer) {
        var e_6, _a, e_7, _b, e_8, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _d = __values(message.orderHashes), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _f = __values(message.createdOrdersCids), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _h = __values(message.failedOrdersCids), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCreateDerivativeLimitOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHashes.push(reader.string());
                    break;
                case 2:
                    message.createdOrdersCids.push(reader.string());
                    break;
                case 3:
                    message.failedOrdersCids.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHashes: Array.isArray(object === null || object === void 0 ? void 0 : object.orderHashes) ? object.orderHashes.map(function (e) { return String(e); }) : [],
            createdOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.createdOrdersCids)
                ? object.createdOrdersCids.map(function (e) { return String(e); })
                : [],
            failedOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.failedOrdersCids)
                ? object.failedOrdersCids.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orderHashes) {
            obj.orderHashes = message.orderHashes.map(function (e) { return e; });
        }
        else {
            obj.orderHashes = [];
        }
        if (message.createdOrdersCids) {
            obj.createdOrdersCids = message.createdOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.createdOrdersCids = [];
        }
        if (message.failedOrdersCids) {
            obj.failedOrdersCids = message.failedOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.failedOrdersCids = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCreateDerivativeLimitOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgBatchCreateDerivativeLimitOrdersResponse();
        message.orderHashes = ((_a = object.orderHashes) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.createdOrdersCids = ((_b = object.createdOrdersCids) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.failedOrdersCids = ((_c = object.failedOrdersCids) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgCancelSpotOrder() {
    return { sender: "", marketId: "", subaccountId: "", orderHash: "", cid: "" };
}
exports.MsgCancelSpotOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(26).string(message.subaccountId);
        }
        if (message.orderHash !== "") {
            writer.uint32(34).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(42).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelSpotOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.subaccountId = reader.string();
                    break;
                case 4:
                    message.orderHash = reader.string();
                    break;
                case 5:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelSpotOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseMsgCancelSpotOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.subaccountId = (_c = object.subaccountId) !== null && _c !== void 0 ? _c : "";
        message.orderHash = (_d = object.orderHash) !== null && _d !== void 0 ? _d : "";
        message.cid = (_e = object.cid) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseMsgCancelSpotOrderResponse() {
    return {};
}
exports.MsgCancelSpotOrderResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelSpotOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelSpotOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgCancelSpotOrderResponse();
        return message;
    },
};
function createBaseMsgBatchCancelSpotOrders() {
    return { sender: "", data: [] };
}
exports.MsgBatchCancelSpotOrders = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.data), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.OrderData.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCancelSpotOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.data.push(exports.OrderData.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            data: Array.isArray(object === null || object === void 0 ? void 0 : object.data) ? object.data.map(function (e) { return exports.OrderData.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.data) {
            obj.data = message.data.map(function (e) { return e ? exports.OrderData.toJSON(e) : undefined; });
        }
        else {
            obj.data = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCancelSpotOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgBatchCancelSpotOrders();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.data = ((_b = object.data) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.OrderData.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgBatchCancelSpotOrdersResponse() {
    return { success: [] };
}
exports.MsgBatchCancelSpotOrdersResponse = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        writer.uint32(10).fork();
        try {
            for (var _b = __values(message.success), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.bool(v);
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCancelSpotOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.success.push(reader.bool());
                        }
                    }
                    else {
                        message.success.push(reader.bool());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { success: Array.isArray(object === null || object === void 0 ? void 0 : object.success) ? object.success.map(function (e) { return Boolean(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.success) {
            obj.success = message.success.map(function (e) { return e; });
        }
        else {
            obj.success = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCancelSpotOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgBatchCancelSpotOrdersResponse();
        message.success = ((_a = object.success) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgBatchCancelBinaryOptionsOrders() {
    return { sender: "", data: [] };
}
exports.MsgBatchCancelBinaryOptionsOrders = {
    encode: function (message, writer) {
        var e_11, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.data), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.OrderData.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_11) throw e_11.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCancelBinaryOptionsOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.data.push(exports.OrderData.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            data: Array.isArray(object === null || object === void 0 ? void 0 : object.data) ? object.data.map(function (e) { return exports.OrderData.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.data) {
            obj.data = message.data.map(function (e) { return e ? exports.OrderData.toJSON(e) : undefined; });
        }
        else {
            obj.data = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCancelBinaryOptionsOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgBatchCancelBinaryOptionsOrders();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.data = ((_b = object.data) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.OrderData.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgBatchCancelBinaryOptionsOrdersResponse() {
    return { success: [] };
}
exports.MsgBatchCancelBinaryOptionsOrdersResponse = {
    encode: function (message, writer) {
        var e_12, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        writer.uint32(10).fork();
        try {
            for (var _b = __values(message.success), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.bool(v);
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_12) throw e_12.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCancelBinaryOptionsOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.success.push(reader.bool());
                        }
                    }
                    else {
                        message.success.push(reader.bool());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { success: Array.isArray(object === null || object === void 0 ? void 0 : object.success) ? object.success.map(function (e) { return Boolean(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.success) {
            obj.success = message.success.map(function (e) { return e; });
        }
        else {
            obj.success = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCancelBinaryOptionsOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgBatchCancelBinaryOptionsOrdersResponse();
        message.success = ((_a = object.success) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgBatchUpdateOrders() {
    return {
        sender: "",
        subaccountId: "",
        spotMarketIdsToCancelAll: [],
        derivativeMarketIdsToCancelAll: [],
        spotOrdersToCancel: [],
        derivativeOrdersToCancel: [],
        spotOrdersToCreate: [],
        derivativeOrdersToCreate: [],
        binaryOptionsOrdersToCancel: [],
        binaryOptionsMarketIdsToCancelAll: [],
        binaryOptionsOrdersToCreate: [],
    };
}
exports.MsgBatchUpdateOrders = {
    encode: function (message, writer) {
        var e_13, _a, e_14, _b, e_15, _c, e_16, _d, e_17, _e, e_18, _f, e_19, _g, e_20, _h, e_21, _j;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        try {
            for (var _k = __values(message.spotMarketIdsToCancelAll), _l = _k.next(); !_l.done; _l = _k.next()) {
                var v = _l.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_l && !_l.done && (_a = _k.return)) _a.call(_k);
            }
            finally { if (e_13) throw e_13.error; }
        }
        try {
            for (var _m = __values(message.derivativeMarketIdsToCancelAll), _o = _m.next(); !_o.done; _o = _m.next()) {
                var v = _o.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_o && !_o.done && (_b = _m.return)) _b.call(_m);
            }
            finally { if (e_14) throw e_14.error; }
        }
        try {
            for (var _p = __values(message.spotOrdersToCancel), _q = _p.next(); !_q.done; _q = _p.next()) {
                var v = _q.value;
                exports.OrderData.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_q && !_q.done && (_c = _p.return)) _c.call(_p);
            }
            finally { if (e_15) throw e_15.error; }
        }
        try {
            for (var _r = __values(message.derivativeOrdersToCancel), _s = _r.next(); !_s.done; _s = _r.next()) {
                var v = _s.value;
                exports.OrderData.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_s && !_s.done && (_d = _r.return)) _d.call(_r);
            }
            finally { if (e_16) throw e_16.error; }
        }
        try {
            for (var _t = __values(message.spotOrdersToCreate), _u = _t.next(); !_u.done; _u = _t.next()) {
                var v = _u.value;
                exchange_1.SpotOrder.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_u && !_u.done && (_e = _t.return)) _e.call(_t);
            }
            finally { if (e_17) throw e_17.error; }
        }
        try {
            for (var _v = __values(message.derivativeOrdersToCreate), _w = _v.next(); !_w.done; _w = _v.next()) {
                var v = _w.value;
                exchange_1.DerivativeOrder.encode(v, writer.uint32(66).fork()).ldelim();
            }
        }
        catch (e_18_1) { e_18 = { error: e_18_1 }; }
        finally {
            try {
                if (_w && !_w.done && (_f = _v.return)) _f.call(_v);
            }
            finally { if (e_18) throw e_18.error; }
        }
        try {
            for (var _x = __values(message.binaryOptionsOrdersToCancel), _y = _x.next(); !_y.done; _y = _x.next()) {
                var v = _y.value;
                exports.OrderData.encode(v, writer.uint32(74).fork()).ldelim();
            }
        }
        catch (e_19_1) { e_19 = { error: e_19_1 }; }
        finally {
            try {
                if (_y && !_y.done && (_g = _x.return)) _g.call(_x);
            }
            finally { if (e_19) throw e_19.error; }
        }
        try {
            for (var _z = __values(message.binaryOptionsMarketIdsToCancelAll), _0 = _z.next(); !_0.done; _0 = _z.next()) {
                var v = _0.value;
                writer.uint32(82).string(v);
            }
        }
        catch (e_20_1) { e_20 = { error: e_20_1 }; }
        finally {
            try {
                if (_0 && !_0.done && (_h = _z.return)) _h.call(_z);
            }
            finally { if (e_20) throw e_20.error; }
        }
        try {
            for (var _1 = __values(message.binaryOptionsOrdersToCreate), _2 = _1.next(); !_2.done; _2 = _1.next()) {
                var v = _2.value;
                exchange_1.DerivativeOrder.encode(v, writer.uint32(90).fork()).ldelim();
            }
        }
        catch (e_21_1) { e_21 = { error: e_21_1 }; }
        finally {
            try {
                if (_2 && !_2.done && (_j = _1.return)) _j.call(_1);
            }
            finally { if (e_21) throw e_21.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchUpdateOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.spotMarketIdsToCancelAll.push(reader.string());
                    break;
                case 4:
                    message.derivativeMarketIdsToCancelAll.push(reader.string());
                    break;
                case 5:
                    message.spotOrdersToCancel.push(exports.OrderData.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.derivativeOrdersToCancel.push(exports.OrderData.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.spotOrdersToCreate.push(exchange_1.SpotOrder.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.derivativeOrdersToCreate.push(exchange_1.DerivativeOrder.decode(reader, reader.uint32()));
                    break;
                case 9:
                    message.binaryOptionsOrdersToCancel.push(exports.OrderData.decode(reader, reader.uint32()));
                    break;
                case 10:
                    message.binaryOptionsMarketIdsToCancelAll.push(reader.string());
                    break;
                case 11:
                    message.binaryOptionsOrdersToCreate.push(exchange_1.DerivativeOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            spotMarketIdsToCancelAll: Array.isArray(object === null || object === void 0 ? void 0 : object.spotMarketIdsToCancelAll)
                ? object.spotMarketIdsToCancelAll.map(function (e) { return String(e); })
                : [],
            derivativeMarketIdsToCancelAll: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeMarketIdsToCancelAll)
                ? object.derivativeMarketIdsToCancelAll.map(function (e) { return String(e); })
                : [],
            spotOrdersToCancel: Array.isArray(object === null || object === void 0 ? void 0 : object.spotOrdersToCancel)
                ? object.spotOrdersToCancel.map(function (e) { return exports.OrderData.fromJSON(e); })
                : [],
            derivativeOrdersToCancel: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeOrdersToCancel)
                ? object.derivativeOrdersToCancel.map(function (e) { return exports.OrderData.fromJSON(e); })
                : [],
            spotOrdersToCreate: Array.isArray(object === null || object === void 0 ? void 0 : object.spotOrdersToCreate)
                ? object.spotOrdersToCreate.map(function (e) { return exchange_1.SpotOrder.fromJSON(e); })
                : [],
            derivativeOrdersToCreate: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeOrdersToCreate)
                ? object.derivativeOrdersToCreate.map(function (e) { return exchange_1.DerivativeOrder.fromJSON(e); })
                : [],
            binaryOptionsOrdersToCancel: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsOrdersToCancel)
                ? object.binaryOptionsOrdersToCancel.map(function (e) { return exports.OrderData.fromJSON(e); })
                : [],
            binaryOptionsMarketIdsToCancelAll: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsMarketIdsToCancelAll)
                ? object.binaryOptionsMarketIdsToCancelAll.map(function (e) { return String(e); })
                : [],
            binaryOptionsOrdersToCreate: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsOrdersToCreate)
                ? object.binaryOptionsOrdersToCreate.map(function (e) { return exchange_1.DerivativeOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.spotMarketIdsToCancelAll) {
            obj.spotMarketIdsToCancelAll = message.spotMarketIdsToCancelAll.map(function (e) { return e; });
        }
        else {
            obj.spotMarketIdsToCancelAll = [];
        }
        if (message.derivativeMarketIdsToCancelAll) {
            obj.derivativeMarketIdsToCancelAll = message.derivativeMarketIdsToCancelAll.map(function (e) { return e; });
        }
        else {
            obj.derivativeMarketIdsToCancelAll = [];
        }
        if (message.spotOrdersToCancel) {
            obj.spotOrdersToCancel = message.spotOrdersToCancel.map(function (e) { return e ? exports.OrderData.toJSON(e) : undefined; });
        }
        else {
            obj.spotOrdersToCancel = [];
        }
        if (message.derivativeOrdersToCancel) {
            obj.derivativeOrdersToCancel = message.derivativeOrdersToCancel.map(function (e) { return e ? exports.OrderData.toJSON(e) : undefined; });
        }
        else {
            obj.derivativeOrdersToCancel = [];
        }
        if (message.spotOrdersToCreate) {
            obj.spotOrdersToCreate = message.spotOrdersToCreate.map(function (e) { return e ? exchange_1.SpotOrder.toJSON(e) : undefined; });
        }
        else {
            obj.spotOrdersToCreate = [];
        }
        if (message.derivativeOrdersToCreate) {
            obj.derivativeOrdersToCreate = message.derivativeOrdersToCreate.map(function (e) {
                return e ? exchange_1.DerivativeOrder.toJSON(e) : undefined;
            });
        }
        else {
            obj.derivativeOrdersToCreate = [];
        }
        if (message.binaryOptionsOrdersToCancel) {
            obj.binaryOptionsOrdersToCancel = message.binaryOptionsOrdersToCancel.map(function (e) {
                return e ? exports.OrderData.toJSON(e) : undefined;
            });
        }
        else {
            obj.binaryOptionsOrdersToCancel = [];
        }
        if (message.binaryOptionsMarketIdsToCancelAll) {
            obj.binaryOptionsMarketIdsToCancelAll = message.binaryOptionsMarketIdsToCancelAll.map(function (e) { return e; });
        }
        else {
            obj.binaryOptionsMarketIdsToCancelAll = [];
        }
        if (message.binaryOptionsOrdersToCreate) {
            obj.binaryOptionsOrdersToCreate = message.binaryOptionsOrdersToCreate.map(function (e) {
                return e ? exchange_1.DerivativeOrder.toJSON(e) : undefined;
            });
        }
        else {
            obj.binaryOptionsOrdersToCreate = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchUpdateOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        var message = createBaseMsgBatchUpdateOrders();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.spotMarketIdsToCancelAll = ((_c = object.spotMarketIdsToCancelAll) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.derivativeMarketIdsToCancelAll = ((_d = object.derivativeMarketIdsToCancelAll) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        message.spotOrdersToCancel = ((_e = object.spotOrdersToCancel) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.OrderData.fromPartial(e); })) || [];
        message.derivativeOrdersToCancel = ((_f = object.derivativeOrdersToCancel) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.OrderData.fromPartial(e); })) || [];
        message.spotOrdersToCreate = ((_g = object.spotOrdersToCreate) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exchange_1.SpotOrder.fromPartial(e); })) || [];
        message.derivativeOrdersToCreate = ((_h = object.derivativeOrdersToCreate) === null || _h === void 0 ? void 0 : _h.map(function (e) { return exchange_1.DerivativeOrder.fromPartial(e); })) ||
            [];
        message.binaryOptionsOrdersToCancel = ((_j = object.binaryOptionsOrdersToCancel) === null || _j === void 0 ? void 0 : _j.map(function (e) { return exports.OrderData.fromPartial(e); })) ||
            [];
        message.binaryOptionsMarketIdsToCancelAll = ((_k = object.binaryOptionsMarketIdsToCancelAll) === null || _k === void 0 ? void 0 : _k.map(function (e) { return e; })) || [];
        message.binaryOptionsOrdersToCreate =
            ((_l = object.binaryOptionsOrdersToCreate) === null || _l === void 0 ? void 0 : _l.map(function (e) { return exchange_1.DerivativeOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgBatchUpdateOrdersResponse() {
    return {
        spotCancelSuccess: [],
        derivativeCancelSuccess: [],
        spotOrderHashes: [],
        derivativeOrderHashes: [],
        binaryOptionsCancelSuccess: [],
        binaryOptionsOrderHashes: [],
        createdSpotOrdersCids: [],
        failedSpotOrdersCids: [],
        createdDerivativeOrdersCids: [],
        failedDerivativeOrdersCids: [],
        createdBinaryOptionsOrdersCids: [],
        failedBinaryOptionsOrdersCids: [],
    };
}
exports.MsgBatchUpdateOrdersResponse = {
    encode: function (message, writer) {
        var e_22, _a, e_23, _b, e_24, _c, e_25, _d, e_26, _e, e_27, _f, e_28, _g, e_29, _h, e_30, _j, e_31, _k, e_32, _l, e_33, _m;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        writer.uint32(10).fork();
        try {
            for (var _o = __values(message.spotCancelSuccess), _p = _o.next(); !_p.done; _p = _o.next()) {
                var v = _p.value;
                writer.bool(v);
            }
        }
        catch (e_22_1) { e_22 = { error: e_22_1 }; }
        finally {
            try {
                if (_p && !_p.done && (_a = _o.return)) _a.call(_o);
            }
            finally { if (e_22) throw e_22.error; }
        }
        writer.ldelim();
        writer.uint32(18).fork();
        try {
            for (var _q = __values(message.derivativeCancelSuccess), _r = _q.next(); !_r.done; _r = _q.next()) {
                var v = _r.value;
                writer.bool(v);
            }
        }
        catch (e_23_1) { e_23 = { error: e_23_1 }; }
        finally {
            try {
                if (_r && !_r.done && (_b = _q.return)) _b.call(_q);
            }
            finally { if (e_23) throw e_23.error; }
        }
        writer.ldelim();
        try {
            for (var _s = __values(message.spotOrderHashes), _t = _s.next(); !_t.done; _t = _s.next()) {
                var v = _t.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_24_1) { e_24 = { error: e_24_1 }; }
        finally {
            try {
                if (_t && !_t.done && (_c = _s.return)) _c.call(_s);
            }
            finally { if (e_24) throw e_24.error; }
        }
        try {
            for (var _u = __values(message.derivativeOrderHashes), _v = _u.next(); !_v.done; _v = _u.next()) {
                var v = _v.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_25_1) { e_25 = { error: e_25_1 }; }
        finally {
            try {
                if (_v && !_v.done && (_d = _u.return)) _d.call(_u);
            }
            finally { if (e_25) throw e_25.error; }
        }
        writer.uint32(42).fork();
        try {
            for (var _w = __values(message.binaryOptionsCancelSuccess), _x = _w.next(); !_x.done; _x = _w.next()) {
                var v = _x.value;
                writer.bool(v);
            }
        }
        catch (e_26_1) { e_26 = { error: e_26_1 }; }
        finally {
            try {
                if (_x && !_x.done && (_e = _w.return)) _e.call(_w);
            }
            finally { if (e_26) throw e_26.error; }
        }
        writer.ldelim();
        try {
            for (var _y = __values(message.binaryOptionsOrderHashes), _z = _y.next(); !_z.done; _z = _y.next()) {
                var v = _z.value;
                writer.uint32(50).string(v);
            }
        }
        catch (e_27_1) { e_27 = { error: e_27_1 }; }
        finally {
            try {
                if (_z && !_z.done && (_f = _y.return)) _f.call(_y);
            }
            finally { if (e_27) throw e_27.error; }
        }
        try {
            for (var _0 = __values(message.createdSpotOrdersCids), _1 = _0.next(); !_1.done; _1 = _0.next()) {
                var v = _1.value;
                writer.uint32(58).string(v);
            }
        }
        catch (e_28_1) { e_28 = { error: e_28_1 }; }
        finally {
            try {
                if (_1 && !_1.done && (_g = _0.return)) _g.call(_0);
            }
            finally { if (e_28) throw e_28.error; }
        }
        try {
            for (var _2 = __values(message.failedSpotOrdersCids), _3 = _2.next(); !_3.done; _3 = _2.next()) {
                var v = _3.value;
                writer.uint32(66).string(v);
            }
        }
        catch (e_29_1) { e_29 = { error: e_29_1 }; }
        finally {
            try {
                if (_3 && !_3.done && (_h = _2.return)) _h.call(_2);
            }
            finally { if (e_29) throw e_29.error; }
        }
        try {
            for (var _4 = __values(message.createdDerivativeOrdersCids), _5 = _4.next(); !_5.done; _5 = _4.next()) {
                var v = _5.value;
                writer.uint32(74).string(v);
            }
        }
        catch (e_30_1) { e_30 = { error: e_30_1 }; }
        finally {
            try {
                if (_5 && !_5.done && (_j = _4.return)) _j.call(_4);
            }
            finally { if (e_30) throw e_30.error; }
        }
        try {
            for (var _6 = __values(message.failedDerivativeOrdersCids), _7 = _6.next(); !_7.done; _7 = _6.next()) {
                var v = _7.value;
                writer.uint32(82).string(v);
            }
        }
        catch (e_31_1) { e_31 = { error: e_31_1 }; }
        finally {
            try {
                if (_7 && !_7.done && (_k = _6.return)) _k.call(_6);
            }
            finally { if (e_31) throw e_31.error; }
        }
        try {
            for (var _8 = __values(message.createdBinaryOptionsOrdersCids), _9 = _8.next(); !_9.done; _9 = _8.next()) {
                var v = _9.value;
                writer.uint32(90).string(v);
            }
        }
        catch (e_32_1) { e_32 = { error: e_32_1 }; }
        finally {
            try {
                if (_9 && !_9.done && (_l = _8.return)) _l.call(_8);
            }
            finally { if (e_32) throw e_32.error; }
        }
        try {
            for (var _10 = __values(message.failedBinaryOptionsOrdersCids), _11 = _10.next(); !_11.done; _11 = _10.next()) {
                var v = _11.value;
                writer.uint32(98).string(v);
            }
        }
        catch (e_33_1) { e_33 = { error: e_33_1 }; }
        finally {
            try {
                if (_11 && !_11.done && (_m = _10.return)) _m.call(_10);
            }
            finally { if (e_33) throw e_33.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchUpdateOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.spotCancelSuccess.push(reader.bool());
                        }
                    }
                    else {
                        message.spotCancelSuccess.push(reader.bool());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.derivativeCancelSuccess.push(reader.bool());
                        }
                    }
                    else {
                        message.derivativeCancelSuccess.push(reader.bool());
                    }
                    break;
                case 3:
                    message.spotOrderHashes.push(reader.string());
                    break;
                case 4:
                    message.derivativeOrderHashes.push(reader.string());
                    break;
                case 5:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.binaryOptionsCancelSuccess.push(reader.bool());
                        }
                    }
                    else {
                        message.binaryOptionsCancelSuccess.push(reader.bool());
                    }
                    break;
                case 6:
                    message.binaryOptionsOrderHashes.push(reader.string());
                    break;
                case 7:
                    message.createdSpotOrdersCids.push(reader.string());
                    break;
                case 8:
                    message.failedSpotOrdersCids.push(reader.string());
                    break;
                case 9:
                    message.createdDerivativeOrdersCids.push(reader.string());
                    break;
                case 10:
                    message.failedDerivativeOrdersCids.push(reader.string());
                    break;
                case 11:
                    message.createdBinaryOptionsOrdersCids.push(reader.string());
                    break;
                case 12:
                    message.failedBinaryOptionsOrdersCids.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            spotCancelSuccess: Array.isArray(object === null || object === void 0 ? void 0 : object.spotCancelSuccess)
                ? object.spotCancelSuccess.map(function (e) { return Boolean(e); })
                : [],
            derivativeCancelSuccess: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeCancelSuccess)
                ? object.derivativeCancelSuccess.map(function (e) { return Boolean(e); })
                : [],
            spotOrderHashes: Array.isArray(object === null || object === void 0 ? void 0 : object.spotOrderHashes) ? object.spotOrderHashes.map(function (e) { return String(e); }) : [],
            derivativeOrderHashes: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeOrderHashes)
                ? object.derivativeOrderHashes.map(function (e) { return String(e); })
                : [],
            binaryOptionsCancelSuccess: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsCancelSuccess)
                ? object.binaryOptionsCancelSuccess.map(function (e) { return Boolean(e); })
                : [],
            binaryOptionsOrderHashes: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsOrderHashes)
                ? object.binaryOptionsOrderHashes.map(function (e) { return String(e); })
                : [],
            createdSpotOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.createdSpotOrdersCids)
                ? object.createdSpotOrdersCids.map(function (e) { return String(e); })
                : [],
            failedSpotOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.failedSpotOrdersCids)
                ? object.failedSpotOrdersCids.map(function (e) { return String(e); })
                : [],
            createdDerivativeOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.createdDerivativeOrdersCids)
                ? object.createdDerivativeOrdersCids.map(function (e) { return String(e); })
                : [],
            failedDerivativeOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.failedDerivativeOrdersCids)
                ? object.failedDerivativeOrdersCids.map(function (e) { return String(e); })
                : [],
            createdBinaryOptionsOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.createdBinaryOptionsOrdersCids)
                ? object.createdBinaryOptionsOrdersCids.map(function (e) { return String(e); })
                : [],
            failedBinaryOptionsOrdersCids: Array.isArray(object === null || object === void 0 ? void 0 : object.failedBinaryOptionsOrdersCids)
                ? object.failedBinaryOptionsOrdersCids.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.spotCancelSuccess) {
            obj.spotCancelSuccess = message.spotCancelSuccess.map(function (e) { return e; });
        }
        else {
            obj.spotCancelSuccess = [];
        }
        if (message.derivativeCancelSuccess) {
            obj.derivativeCancelSuccess = message.derivativeCancelSuccess.map(function (e) { return e; });
        }
        else {
            obj.derivativeCancelSuccess = [];
        }
        if (message.spotOrderHashes) {
            obj.spotOrderHashes = message.spotOrderHashes.map(function (e) { return e; });
        }
        else {
            obj.spotOrderHashes = [];
        }
        if (message.derivativeOrderHashes) {
            obj.derivativeOrderHashes = message.derivativeOrderHashes.map(function (e) { return e; });
        }
        else {
            obj.derivativeOrderHashes = [];
        }
        if (message.binaryOptionsCancelSuccess) {
            obj.binaryOptionsCancelSuccess = message.binaryOptionsCancelSuccess.map(function (e) { return e; });
        }
        else {
            obj.binaryOptionsCancelSuccess = [];
        }
        if (message.binaryOptionsOrderHashes) {
            obj.binaryOptionsOrderHashes = message.binaryOptionsOrderHashes.map(function (e) { return e; });
        }
        else {
            obj.binaryOptionsOrderHashes = [];
        }
        if (message.createdSpotOrdersCids) {
            obj.createdSpotOrdersCids = message.createdSpotOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.createdSpotOrdersCids = [];
        }
        if (message.failedSpotOrdersCids) {
            obj.failedSpotOrdersCids = message.failedSpotOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.failedSpotOrdersCids = [];
        }
        if (message.createdDerivativeOrdersCids) {
            obj.createdDerivativeOrdersCids = message.createdDerivativeOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.createdDerivativeOrdersCids = [];
        }
        if (message.failedDerivativeOrdersCids) {
            obj.failedDerivativeOrdersCids = message.failedDerivativeOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.failedDerivativeOrdersCids = [];
        }
        if (message.createdBinaryOptionsOrdersCids) {
            obj.createdBinaryOptionsOrdersCids = message.createdBinaryOptionsOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.createdBinaryOptionsOrdersCids = [];
        }
        if (message.failedBinaryOptionsOrdersCids) {
            obj.failedBinaryOptionsOrdersCids = message.failedBinaryOptionsOrdersCids.map(function (e) { return e; });
        }
        else {
            obj.failedBinaryOptionsOrdersCids = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchUpdateOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        var message = createBaseMsgBatchUpdateOrdersResponse();
        message.spotCancelSuccess = ((_a = object.spotCancelSuccess) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.derivativeCancelSuccess = ((_b = object.derivativeCancelSuccess) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.spotOrderHashes = ((_c = object.spotOrderHashes) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.derivativeOrderHashes = ((_d = object.derivativeOrderHashes) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        message.binaryOptionsCancelSuccess = ((_e = object.binaryOptionsCancelSuccess) === null || _e === void 0 ? void 0 : _e.map(function (e) { return e; })) || [];
        message.binaryOptionsOrderHashes = ((_f = object.binaryOptionsOrderHashes) === null || _f === void 0 ? void 0 : _f.map(function (e) { return e; })) || [];
        message.createdSpotOrdersCids = ((_g = object.createdSpotOrdersCids) === null || _g === void 0 ? void 0 : _g.map(function (e) { return e; })) || [];
        message.failedSpotOrdersCids = ((_h = object.failedSpotOrdersCids) === null || _h === void 0 ? void 0 : _h.map(function (e) { return e; })) || [];
        message.createdDerivativeOrdersCids = ((_j = object.createdDerivativeOrdersCids) === null || _j === void 0 ? void 0 : _j.map(function (e) { return e; })) || [];
        message.failedDerivativeOrdersCids = ((_k = object.failedDerivativeOrdersCids) === null || _k === void 0 ? void 0 : _k.map(function (e) { return e; })) || [];
        message.createdBinaryOptionsOrdersCids = ((_l = object.createdBinaryOptionsOrdersCids) === null || _l === void 0 ? void 0 : _l.map(function (e) { return e; })) || [];
        message.failedBinaryOptionsOrdersCids = ((_m = object.failedBinaryOptionsOrdersCids) === null || _m === void 0 ? void 0 : _m.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgCreateDerivativeMarketOrder() {
    return { sender: "", order: undefined };
}
exports.MsgCreateDerivativeMarketOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.order !== undefined) {
            exchange_1.DerivativeOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateDerivativeMarketOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.DerivativeOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            order: isSet(object.order) ? exchange_1.DerivativeOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.order !== undefined && (obj.order = message.order ? exchange_1.DerivativeOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateDerivativeMarketOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgCreateDerivativeMarketOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.DerivativeOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateDerivativeMarketOrderResponse() {
    return { orderHash: "", results: undefined, cid: "" };
}
exports.MsgCreateDerivativeMarketOrderResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderHash !== "") {
            writer.uint32(10).string(message.orderHash);
        }
        if (message.results !== undefined) {
            exports.DerivativeMarketOrderResults.encode(message.results, writer.uint32(18).fork()).ldelim();
        }
        if (message.cid !== "") {
            writer.uint32(26).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateDerivativeMarketOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHash = reader.string();
                    break;
                case 2:
                    message.results = exports.DerivativeMarketOrderResults.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            results: isSet(object.results) ? exports.DerivativeMarketOrderResults.fromJSON(object.results) : undefined,
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.results !== undefined &&
            (obj.results = message.results ? exports.DerivativeMarketOrderResults.toJSON(message.results) : undefined);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateDerivativeMarketOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgCreateDerivativeMarketOrderResponse();
        message.orderHash = (_a = object.orderHash) !== null && _a !== void 0 ? _a : "";
        message.results = (object.results !== undefined && object.results !== null)
            ? exports.DerivativeMarketOrderResults.fromPartial(object.results)
            : undefined;
        message.cid = (_b = object.cid) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseDerivativeMarketOrderResults() {
    return { quantity: "", price: "", fee: "", positionDelta: undefined, payout: "" };
}
exports.DerivativeMarketOrderResults = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.quantity !== "") {
            writer.uint32(10).string(message.quantity);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        if (message.fee !== "") {
            writer.uint32(26).string(message.fee);
        }
        if (message.positionDelta !== undefined) {
            exchange_1.PositionDelta.encode(message.positionDelta, writer.uint32(34).fork()).ldelim();
        }
        if (message.payout !== "") {
            writer.uint32(42).string(message.payout);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeMarketOrderResults();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.quantity = reader.string();
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                case 3:
                    message.fee = reader.string();
                    break;
                case 4:
                    message.positionDelta = exchange_1.PositionDelta.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.payout = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            price: isSet(object.price) ? String(object.price) : "",
            fee: isSet(object.fee) ? String(object.fee) : "",
            positionDelta: isSet(object.positionDelta) ? exchange_1.PositionDelta.fromJSON(object.positionDelta) : undefined,
            payout: isSet(object.payout) ? String(object.payout) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.price !== undefined && (obj.price = message.price);
        message.fee !== undefined && (obj.fee = message.fee);
        message.positionDelta !== undefined &&
            (obj.positionDelta = message.positionDelta ? exchange_1.PositionDelta.toJSON(message.positionDelta) : undefined);
        message.payout !== undefined && (obj.payout = message.payout);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeMarketOrderResults.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseDerivativeMarketOrderResults();
        message.quantity = (_a = object.quantity) !== null && _a !== void 0 ? _a : "";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "";
        message.fee = (_c = object.fee) !== null && _c !== void 0 ? _c : "";
        message.positionDelta = (object.positionDelta !== undefined && object.positionDelta !== null)
            ? exchange_1.PositionDelta.fromPartial(object.positionDelta)
            : undefined;
        message.payout = (_d = object.payout) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseMsgCreateBinaryOptionsMarketOrder() {
    return { sender: "", order: undefined };
}
exports.MsgCreateBinaryOptionsMarketOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.order !== undefined) {
            exchange_1.DerivativeOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateBinaryOptionsMarketOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.DerivativeOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            order: isSet(object.order) ? exchange_1.DerivativeOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.order !== undefined && (obj.order = message.order ? exchange_1.DerivativeOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateBinaryOptionsMarketOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgCreateBinaryOptionsMarketOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.DerivativeOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateBinaryOptionsMarketOrderResponse() {
    return { orderHash: "", results: undefined, cid: "" };
}
exports.MsgCreateBinaryOptionsMarketOrderResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderHash !== "") {
            writer.uint32(10).string(message.orderHash);
        }
        if (message.results !== undefined) {
            exports.DerivativeMarketOrderResults.encode(message.results, writer.uint32(18).fork()).ldelim();
        }
        if (message.cid !== "") {
            writer.uint32(26).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateBinaryOptionsMarketOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderHash = reader.string();
                    break;
                case 2:
                    message.results = exports.DerivativeMarketOrderResults.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            results: isSet(object.results) ? exports.DerivativeMarketOrderResults.fromJSON(object.results) : undefined,
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.results !== undefined &&
            (obj.results = message.results ? exports.DerivativeMarketOrderResults.toJSON(message.results) : undefined);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateBinaryOptionsMarketOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgCreateBinaryOptionsMarketOrderResponse();
        message.orderHash = (_a = object.orderHash) !== null && _a !== void 0 ? _a : "";
        message.results = (object.results !== undefined && object.results !== null)
            ? exports.DerivativeMarketOrderResults.fromPartial(object.results)
            : undefined;
        message.cid = (_b = object.cid) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgCancelDerivativeOrder() {
    return { sender: "", marketId: "", subaccountId: "", orderHash: "", orderMask: 0, cid: "" };
}
exports.MsgCancelDerivativeOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(26).string(message.subaccountId);
        }
        if (message.orderHash !== "") {
            writer.uint32(34).string(message.orderHash);
        }
        if (message.orderMask !== 0) {
            writer.uint32(40).int32(message.orderMask);
        }
        if (message.cid !== "") {
            writer.uint32(50).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelDerivativeOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.subaccountId = reader.string();
                    break;
                case 4:
                    message.orderHash = reader.string();
                    break;
                case 5:
                    message.orderMask = reader.int32();
                    break;
                case 6:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            orderMask: isSet(object.orderMask) ? Number(object.orderMask) : 0,
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.orderMask !== undefined && (obj.orderMask = Math.round(message.orderMask));
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelDerivativeOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseMsgCancelDerivativeOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.subaccountId = (_c = object.subaccountId) !== null && _c !== void 0 ? _c : "";
        message.orderHash = (_d = object.orderHash) !== null && _d !== void 0 ? _d : "";
        message.orderMask = (_e = object.orderMask) !== null && _e !== void 0 ? _e : 0;
        message.cid = (_f = object.cid) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseMsgCancelDerivativeOrderResponse() {
    return {};
}
exports.MsgCancelDerivativeOrderResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelDerivativeOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelDerivativeOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgCancelDerivativeOrderResponse();
        return message;
    },
};
function createBaseMsgCancelBinaryOptionsOrder() {
    return { sender: "", marketId: "", subaccountId: "", orderHash: "", orderMask: 0, cid: "" };
}
exports.MsgCancelBinaryOptionsOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(26).string(message.subaccountId);
        }
        if (message.orderHash !== "") {
            writer.uint32(34).string(message.orderHash);
        }
        if (message.orderMask !== 0) {
            writer.uint32(40).int32(message.orderMask);
        }
        if (message.cid !== "") {
            writer.uint32(50).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelBinaryOptionsOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.subaccountId = reader.string();
                    break;
                case 4:
                    message.orderHash = reader.string();
                    break;
                case 5:
                    message.orderMask = reader.int32();
                    break;
                case 6:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            orderMask: isSet(object.orderMask) ? Number(object.orderMask) : 0,
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.orderMask !== undefined && (obj.orderMask = Math.round(message.orderMask));
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelBinaryOptionsOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseMsgCancelBinaryOptionsOrder();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.subaccountId = (_c = object.subaccountId) !== null && _c !== void 0 ? _c : "";
        message.orderHash = (_d = object.orderHash) !== null && _d !== void 0 ? _d : "";
        message.orderMask = (_e = object.orderMask) !== null && _e !== void 0 ? _e : 0;
        message.cid = (_f = object.cid) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseMsgCancelBinaryOptionsOrderResponse() {
    return {};
}
exports.MsgCancelBinaryOptionsOrderResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelBinaryOptionsOrderResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelBinaryOptionsOrderResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgCancelBinaryOptionsOrderResponse();
        return message;
    },
};
function createBaseOrderData() {
    return { marketId: "", subaccountId: "", orderHash: "", orderMask: 0, cid: "" };
}
exports.OrderData = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.orderHash !== "") {
            writer.uint32(26).string(message.orderHash);
        }
        if (message.orderMask !== 0) {
            writer.uint32(32).int32(message.orderMask);
        }
        if (message.cid !== "") {
            writer.uint32(42).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderData();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.orderHash = reader.string();
                    break;
                case 4:
                    message.orderMask = reader.int32();
                    break;
                case 5:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            orderMask: isSet(object.orderMask) ? Number(object.orderMask) : 0,
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.orderMask !== undefined && (obj.orderMask = Math.round(message.orderMask));
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.OrderData.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseOrderData();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.orderHash = (_c = object.orderHash) !== null && _c !== void 0 ? _c : "";
        message.orderMask = (_d = object.orderMask) !== null && _d !== void 0 ? _d : 0;
        message.cid = (_e = object.cid) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseMsgBatchCancelDerivativeOrders() {
    return { sender: "", data: [] };
}
exports.MsgBatchCancelDerivativeOrders = {
    encode: function (message, writer) {
        var e_34, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.data), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.OrderData.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_34_1) { e_34 = { error: e_34_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_34) throw e_34.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCancelDerivativeOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.data.push(exports.OrderData.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            data: Array.isArray(object === null || object === void 0 ? void 0 : object.data) ? object.data.map(function (e) { return exports.OrderData.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.data) {
            obj.data = message.data.map(function (e) { return e ? exports.OrderData.toJSON(e) : undefined; });
        }
        else {
            obj.data = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCancelDerivativeOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgBatchCancelDerivativeOrders();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.data = ((_b = object.data) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.OrderData.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgBatchCancelDerivativeOrdersResponse() {
    return { success: [] };
}
exports.MsgBatchCancelDerivativeOrdersResponse = {
    encode: function (message, writer) {
        var e_35, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        writer.uint32(10).fork();
        try {
            for (var _b = __values(message.success), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.bool(v);
            }
        }
        catch (e_35_1) { e_35 = { error: e_35_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_35) throw e_35.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchCancelDerivativeOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.success.push(reader.bool());
                        }
                    }
                    else {
                        message.success.push(reader.bool());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { success: Array.isArray(object === null || object === void 0 ? void 0 : object.success) ? object.success.map(function (e) { return Boolean(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.success) {
            obj.success = message.success.map(function (e) { return e; });
        }
        else {
            obj.success = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchCancelDerivativeOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgBatchCancelDerivativeOrdersResponse();
        message.success = ((_a = object.success) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgSubaccountTransfer() {
    return { sender: "", sourceSubaccountId: "", destinationSubaccountId: "", amount: undefined };
}
exports.MsgSubaccountTransfer = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.sourceSubaccountId !== "") {
            writer.uint32(18).string(message.sourceSubaccountId);
        }
        if (message.destinationSubaccountId !== "") {
            writer.uint32(26).string(message.destinationSubaccountId);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSubaccountTransfer();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.sourceSubaccountId = reader.string();
                    break;
                case 3:
                    message.destinationSubaccountId = reader.string();
                    break;
                case 4:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            sourceSubaccountId: isSet(object.sourceSubaccountId) ? String(object.sourceSubaccountId) : "",
            destinationSubaccountId: isSet(object.destinationSubaccountId) ? String(object.destinationSubaccountId) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.sourceSubaccountId !== undefined && (obj.sourceSubaccountId = message.sourceSubaccountId);
        message.destinationSubaccountId !== undefined && (obj.destinationSubaccountId = message.destinationSubaccountId);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgSubaccountTransfer.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgSubaccountTransfer();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.sourceSubaccountId = (_b = object.sourceSubaccountId) !== null && _b !== void 0 ? _b : "";
        message.destinationSubaccountId = (_c = object.destinationSubaccountId) !== null && _c !== void 0 ? _c : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseMsgSubaccountTransferResponse() {
    return {};
}
exports.MsgSubaccountTransferResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSubaccountTransferResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgSubaccountTransferResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgSubaccountTransferResponse();
        return message;
    },
};
function createBaseMsgExternalTransfer() {
    return { sender: "", sourceSubaccountId: "", destinationSubaccountId: "", amount: undefined };
}
exports.MsgExternalTransfer = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.sourceSubaccountId !== "") {
            writer.uint32(18).string(message.sourceSubaccountId);
        }
        if (message.destinationSubaccountId !== "") {
            writer.uint32(26).string(message.destinationSubaccountId);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgExternalTransfer();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.sourceSubaccountId = reader.string();
                    break;
                case 3:
                    message.destinationSubaccountId = reader.string();
                    break;
                case 4:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            sourceSubaccountId: isSet(object.sourceSubaccountId) ? String(object.sourceSubaccountId) : "",
            destinationSubaccountId: isSet(object.destinationSubaccountId) ? String(object.destinationSubaccountId) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.sourceSubaccountId !== undefined && (obj.sourceSubaccountId = message.sourceSubaccountId);
        message.destinationSubaccountId !== undefined && (obj.destinationSubaccountId = message.destinationSubaccountId);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgExternalTransfer.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgExternalTransfer();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.sourceSubaccountId = (_b = object.sourceSubaccountId) !== null && _b !== void 0 ? _b : "";
        message.destinationSubaccountId = (_c = object.destinationSubaccountId) !== null && _c !== void 0 ? _c : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseMsgExternalTransferResponse() {
    return {};
}
exports.MsgExternalTransferResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgExternalTransferResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgExternalTransferResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgExternalTransferResponse();
        return message;
    },
};
function createBaseMsgLiquidatePosition() {
    return { sender: "", subaccountId: "", marketId: "", order: undefined };
}
exports.MsgLiquidatePosition = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(26).string(message.marketId);
        }
        if (message.order !== undefined) {
            exchange_1.DerivativeOrder.encode(message.order, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgLiquidatePosition();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.order = exchange_1.DerivativeOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            order: isSet(object.order) ? exchange_1.DerivativeOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.order !== undefined && (obj.order = message.order ? exchange_1.DerivativeOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgLiquidatePosition.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgLiquidatePosition();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.marketId = (_c = object.marketId) !== null && _c !== void 0 ? _c : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.DerivativeOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseMsgLiquidatePositionResponse() {
    return {};
}
exports.MsgLiquidatePositionResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgLiquidatePositionResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgLiquidatePositionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgLiquidatePositionResponse();
        return message;
    },
};
function createBaseMsgEmergencySettleMarket() {
    return { sender: "", subaccountId: "", marketId: "" };
}
exports.MsgEmergencySettleMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(26).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgEmergencySettleMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.MsgEmergencySettleMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgEmergencySettleMarket();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.marketId = (_c = object.marketId) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseMsgEmergencySettleMarketResponse() {
    return {};
}
exports.MsgEmergencySettleMarketResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgEmergencySettleMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgEmergencySettleMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgEmergencySettleMarketResponse();
        return message;
    },
};
function createBaseMsgIncreasePositionMargin() {
    return { sender: "", sourceSubaccountId: "", destinationSubaccountId: "", marketId: "", amount: "" };
}
exports.MsgIncreasePositionMargin = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.sourceSubaccountId !== "") {
            writer.uint32(18).string(message.sourceSubaccountId);
        }
        if (message.destinationSubaccountId !== "") {
            writer.uint32(26).string(message.destinationSubaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(34).string(message.marketId);
        }
        if (message.amount !== "") {
            writer.uint32(42).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgIncreasePositionMargin();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.sourceSubaccountId = reader.string();
                    break;
                case 3:
                    message.destinationSubaccountId = reader.string();
                    break;
                case 4:
                    message.marketId = reader.string();
                    break;
                case 5:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            sourceSubaccountId: isSet(object.sourceSubaccountId) ? String(object.sourceSubaccountId) : "",
            destinationSubaccountId: isSet(object.destinationSubaccountId) ? String(object.destinationSubaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.sourceSubaccountId !== undefined && (obj.sourceSubaccountId = message.sourceSubaccountId);
        message.destinationSubaccountId !== undefined && (obj.destinationSubaccountId = message.destinationSubaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.MsgIncreasePositionMargin.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseMsgIncreasePositionMargin();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.sourceSubaccountId = (_b = object.sourceSubaccountId) !== null && _b !== void 0 ? _b : "";
        message.destinationSubaccountId = (_c = object.destinationSubaccountId) !== null && _c !== void 0 ? _c : "";
        message.marketId = (_d = object.marketId) !== null && _d !== void 0 ? _d : "";
        message.amount = (_e = object.amount) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseMsgIncreasePositionMarginResponse() {
    return {};
}
exports.MsgIncreasePositionMarginResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgIncreasePositionMarginResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgIncreasePositionMarginResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgIncreasePositionMarginResponse();
        return message;
    },
};
function createBaseMsgDecreasePositionMargin() {
    return { sender: "", sourceSubaccountId: "", destinationSubaccountId: "", marketId: "", amount: "" };
}
exports.MsgDecreasePositionMargin = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.sourceSubaccountId !== "") {
            writer.uint32(18).string(message.sourceSubaccountId);
        }
        if (message.destinationSubaccountId !== "") {
            writer.uint32(26).string(message.destinationSubaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(34).string(message.marketId);
        }
        if (message.amount !== "") {
            writer.uint32(42).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgDecreasePositionMargin();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.sourceSubaccountId = reader.string();
                    break;
                case 3:
                    message.destinationSubaccountId = reader.string();
                    break;
                case 4:
                    message.marketId = reader.string();
                    break;
                case 5:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            sourceSubaccountId: isSet(object.sourceSubaccountId) ? String(object.sourceSubaccountId) : "",
            destinationSubaccountId: isSet(object.destinationSubaccountId) ? String(object.destinationSubaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.sourceSubaccountId !== undefined && (obj.sourceSubaccountId = message.sourceSubaccountId);
        message.destinationSubaccountId !== undefined && (obj.destinationSubaccountId = message.destinationSubaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.MsgDecreasePositionMargin.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseMsgDecreasePositionMargin();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.sourceSubaccountId = (_b = object.sourceSubaccountId) !== null && _b !== void 0 ? _b : "";
        message.destinationSubaccountId = (_c = object.destinationSubaccountId) !== null && _c !== void 0 ? _c : "";
        message.marketId = (_d = object.marketId) !== null && _d !== void 0 ? _d : "";
        message.amount = (_e = object.amount) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseMsgDecreasePositionMarginResponse() {
    return {};
}
exports.MsgDecreasePositionMarginResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgDecreasePositionMarginResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgDecreasePositionMarginResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgDecreasePositionMarginResponse();
        return message;
    },
};
function createBaseMsgPrivilegedExecuteContract() {
    return { sender: "", funds: "", contractAddress: "", data: "" };
}
exports.MsgPrivilegedExecuteContract = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.funds !== "") {
            writer.uint32(18).string(message.funds);
        }
        if (message.contractAddress !== "") {
            writer.uint32(26).string(message.contractAddress);
        }
        if (message.data !== "") {
            writer.uint32(34).string(message.data);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgPrivilegedExecuteContract();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.funds = reader.string();
                    break;
                case 3:
                    message.contractAddress = reader.string();
                    break;
                case 4:
                    message.data = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            funds: isSet(object.funds) ? String(object.funds) : "",
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
            data: isSet(object.data) ? String(object.data) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.funds !== undefined && (obj.funds = message.funds);
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        message.data !== undefined && (obj.data = message.data);
        return obj;
    },
    create: function (base) {
        return exports.MsgPrivilegedExecuteContract.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMsgPrivilegedExecuteContract();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.funds = (_b = object.funds) !== null && _b !== void 0 ? _b : "";
        message.contractAddress = (_c = object.contractAddress) !== null && _c !== void 0 ? _c : "";
        message.data = (_d = object.data) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseMsgPrivilegedExecuteContractResponse() {
    return { fundsDiff: [] };
}
exports.MsgPrivilegedExecuteContractResponse = {
    encode: function (message, writer) {
        var e_36, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.fundsDiff), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                coin_1.Coin.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_36_1) { e_36 = { error: e_36_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_36) throw e_36.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgPrivilegedExecuteContractResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.fundsDiff.push(coin_1.Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { fundsDiff: Array.isArray(object === null || object === void 0 ? void 0 : object.fundsDiff) ? object.fundsDiff.map(function (e) { return coin_1.Coin.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.fundsDiff) {
            obj.fundsDiff = message.fundsDiff.map(function (e) { return e ? coin_1.Coin.toJSON(e) : undefined; });
        }
        else {
            obj.fundsDiff = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgPrivilegedExecuteContractResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgPrivilegedExecuteContractResponse();
        message.fundsDiff = ((_a = object.fundsDiff) === null || _a === void 0 ? void 0 : _a.map(function (e) { return coin_1.Coin.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgRewardsOptOut() {
    return { sender: "" };
}
exports.MsgRewardsOptOut = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRewardsOptOut();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { sender: isSet(object.sender) ? String(object.sender) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        return obj;
    },
    create: function (base) {
        return exports.MsgRewardsOptOut.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgRewardsOptOut();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseMsgRewardsOptOutResponse() {
    return {};
}
exports.MsgRewardsOptOutResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRewardsOptOutResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRewardsOptOutResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRewardsOptOutResponse();
        return message;
    },
};
function createBaseMsgReclaimLockedFunds() {
    return { sender: "", lockedAccountPubKey: new Uint8Array(), signature: new Uint8Array() };
}
exports.MsgReclaimLockedFunds = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.lockedAccountPubKey.length !== 0) {
            writer.uint32(18).bytes(message.lockedAccountPubKey);
        }
        if (message.signature.length !== 0) {
            writer.uint32(26).bytes(message.signature);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgReclaimLockedFunds();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.lockedAccountPubKey = reader.bytes();
                    break;
                case 3:
                    message.signature = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            lockedAccountPubKey: isSet(object.lockedAccountPubKey)
                ? bytesFromBase64(object.lockedAccountPubKey)
                : new Uint8Array(),
            signature: isSet(object.signature) ? bytesFromBase64(object.signature) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.lockedAccountPubKey !== undefined &&
            (obj.lockedAccountPubKey = base64FromBytes(message.lockedAccountPubKey !== undefined ? message.lockedAccountPubKey : new Uint8Array()));
        message.signature !== undefined &&
            (obj.signature = base64FromBytes(message.signature !== undefined ? message.signature : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.MsgReclaimLockedFunds.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgReclaimLockedFunds();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.lockedAccountPubKey = (_b = object.lockedAccountPubKey) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.signature = (_c = object.signature) !== null && _c !== void 0 ? _c : new Uint8Array();
        return message;
    },
};
function createBaseMsgReclaimLockedFundsResponse() {
    return {};
}
exports.MsgReclaimLockedFundsResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgReclaimLockedFundsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgReclaimLockedFundsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgReclaimLockedFundsResponse();
        return message;
    },
};
function createBaseMsgSignData() {
    return { Signer: new Uint8Array(), Data: new Uint8Array() };
}
exports.MsgSignData = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.Signer.length !== 0) {
            writer.uint32(10).bytes(message.Signer);
        }
        if (message.Data.length !== 0) {
            writer.uint32(18).bytes(message.Data);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSignData();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.Signer = reader.bytes();
                    break;
                case 2:
                    message.Data = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            Signer: isSet(object.Signer) ? bytesFromBase64(object.Signer) : new Uint8Array(),
            Data: isSet(object.Data) ? bytesFromBase64(object.Data) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.Signer !== undefined &&
            (obj.Signer = base64FromBytes(message.Signer !== undefined ? message.Signer : new Uint8Array()));
        message.Data !== undefined &&
            (obj.Data = base64FromBytes(message.Data !== undefined ? message.Data : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.MsgSignData.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgSignData();
        message.Signer = (_a = object.Signer) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.Data = (_b = object.Data) !== null && _b !== void 0 ? _b : new Uint8Array();
        return message;
    },
};
function createBaseMsgSignDoc() {
    return { signType: "", value: undefined };
}
exports.MsgSignDoc = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.signType !== "") {
            writer.uint32(10).string(message.signType);
        }
        if (message.value !== undefined) {
            exports.MsgSignData.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSignDoc();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.signType = reader.string();
                    break;
                case 2:
                    message.value = exports.MsgSignData.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            signType: isSet(object.signType) ? String(object.signType) : "",
            value: isSet(object.value) ? exports.MsgSignData.fromJSON(object.value) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.signType !== undefined && (obj.signType = message.signType);
        message.value !== undefined && (obj.value = message.value ? exports.MsgSignData.toJSON(message.value) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgSignDoc.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgSignDoc();
        message.signType = (_a = object.signType) !== null && _a !== void 0 ? _a : "";
        message.value = (object.value !== undefined && object.value !== null)
            ? exports.MsgSignData.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseMsgAdminUpdateBinaryOptionsMarket() {
    return {
        sender: "",
        marketId: "",
        settlementPrice: "",
        expirationTimestamp: "0",
        settlementTimestamp: "0",
        status: 0,
    };
}
exports.MsgAdminUpdateBinaryOptionsMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.settlementPrice !== "") {
            writer.uint32(26).string(message.settlementPrice);
        }
        if (message.expirationTimestamp !== "0") {
            writer.uint32(32).int64(message.expirationTimestamp);
        }
        if (message.settlementTimestamp !== "0") {
            writer.uint32(40).int64(message.settlementTimestamp);
        }
        if (message.status !== 0) {
            writer.uint32(48).int32(message.status);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgAdminUpdateBinaryOptionsMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.settlementPrice = reader.string();
                    break;
                case 4:
                    message.expirationTimestamp = longToString(reader.int64());
                    break;
                case 5:
                    message.settlementTimestamp = longToString(reader.int64());
                    break;
                case 6:
                    message.status = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            settlementPrice: isSet(object.settlementPrice) ? String(object.settlementPrice) : "",
            expirationTimestamp: isSet(object.expirationTimestamp) ? String(object.expirationTimestamp) : "0",
            settlementTimestamp: isSet(object.settlementTimestamp) ? String(object.settlementTimestamp) : "0",
            status: isSet(object.status) ? (0, exchange_1.marketStatusFromJSON)(object.status) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.settlementPrice !== undefined && (obj.settlementPrice = message.settlementPrice);
        message.expirationTimestamp !== undefined && (obj.expirationTimestamp = message.expirationTimestamp);
        message.settlementTimestamp !== undefined && (obj.settlementTimestamp = message.settlementTimestamp);
        message.status !== undefined && (obj.status = (0, exchange_1.marketStatusToJSON)(message.status));
        return obj;
    },
    create: function (base) {
        return exports.MsgAdminUpdateBinaryOptionsMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseMsgAdminUpdateBinaryOptionsMarket();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.settlementPrice = (_c = object.settlementPrice) !== null && _c !== void 0 ? _c : "";
        message.expirationTimestamp = (_d = object.expirationTimestamp) !== null && _d !== void 0 ? _d : "0";
        message.settlementTimestamp = (_e = object.settlementTimestamp) !== null && _e !== void 0 ? _e : "0";
        message.status = (_f = object.status) !== null && _f !== void 0 ? _f : 0;
        return message;
    },
};
function createBaseMsgAdminUpdateBinaryOptionsMarketResponse() {
    return {};
}
exports.MsgAdminUpdateBinaryOptionsMarketResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgAdminUpdateBinaryOptionsMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgAdminUpdateBinaryOptionsMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgAdminUpdateBinaryOptionsMarketResponse();
        return message;
    },
};
function createBaseMsgAuthorizeStakeGrants() {
    return { sender: "", grants: [] };
}
exports.MsgAuthorizeStakeGrants = {
    encode: function (message, writer) {
        var e_37, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        try {
            for (var _b = __values(message.grants), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.GrantAuthorization.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_37_1) { e_37 = { error: e_37_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_37) throw e_37.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgAuthorizeStakeGrants();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.grants.push(exchange_1.GrantAuthorization.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            grants: Array.isArray(object === null || object === void 0 ? void 0 : object.grants) ? object.grants.map(function (e) { return exchange_1.GrantAuthorization.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        if (message.grants) {
            obj.grants = message.grants.map(function (e) { return e ? exchange_1.GrantAuthorization.toJSON(e) : undefined; });
        }
        else {
            obj.grants = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgAuthorizeStakeGrants.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgAuthorizeStakeGrants();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.grants = ((_b = object.grants) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.GrantAuthorization.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgAuthorizeStakeGrantsResponse() {
    return {};
}
exports.MsgAuthorizeStakeGrantsResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgAuthorizeStakeGrantsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgAuthorizeStakeGrantsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgAuthorizeStakeGrantsResponse();
        return message;
    },
};
function createBaseMsgActivateStakeGrant() {
    return { sender: "", granter: "" };
}
exports.MsgActivateStakeGrant = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.granter !== "") {
            writer.uint32(18).string(message.granter);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgActivateStakeGrant();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.granter = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            granter: isSet(object.granter) ? String(object.granter) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.granter !== undefined && (obj.granter = message.granter);
        return obj;
    },
    create: function (base) {
        return exports.MsgActivateStakeGrant.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgActivateStakeGrant();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.granter = (_b = object.granter) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgActivateStakeGrantResponse() {
    return {};
}
exports.MsgActivateStakeGrantResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgActivateStakeGrantResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgActivateStakeGrantResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgActivateStakeGrantResponse();
        return message;
    },
};
function createBaseMsgBatchExchangeModification() {
    return { sender: "", proposal: undefined };
}
exports.MsgBatchExchangeModification = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.proposal !== undefined) {
            proposal_1.BatchExchangeModificationProposal.encode(message.proposal, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchExchangeModification();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.proposal = proposal_1.BatchExchangeModificationProposal.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            proposal: isSet(object.proposal) ? proposal_1.BatchExchangeModificationProposal.fromJSON(object.proposal) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.proposal !== undefined &&
            (obj.proposal = message.proposal ? proposal_1.BatchExchangeModificationProposal.toJSON(message.proposal) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchExchangeModification.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgBatchExchangeModification();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.proposal = (object.proposal !== undefined && object.proposal !== null)
            ? proposal_1.BatchExchangeModificationProposal.fromPartial(object.proposal)
            : undefined;
        return message;
    },
};
function createBaseMsgBatchExchangeModificationResponse() {
    return {};
}
exports.MsgBatchExchangeModificationResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBatchExchangeModificationResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgBatchExchangeModificationResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgBatchExchangeModificationResponse();
        return message;
    },
};
var MsgClientImpl = /** @class */ (function () {
    function MsgClientImpl(rpc) {
        this.rpc = rpc;
        this.Deposit = this.Deposit.bind(this);
        this.Withdraw = this.Withdraw.bind(this);
        this.InstantSpotMarketLaunch = this.InstantSpotMarketLaunch.bind(this);
        this.InstantPerpetualMarketLaunch = this.InstantPerpetualMarketLaunch.bind(this);
        this.InstantExpiryFuturesMarketLaunch = this.InstantExpiryFuturesMarketLaunch.bind(this);
        this.CreateSpotLimitOrder = this.CreateSpotLimitOrder.bind(this);
        this.BatchCreateSpotLimitOrders = this.BatchCreateSpotLimitOrders.bind(this);
        this.CreateSpotMarketOrder = this.CreateSpotMarketOrder.bind(this);
        this.CancelSpotOrder = this.CancelSpotOrder.bind(this);
        this.BatchCancelSpotOrders = this.BatchCancelSpotOrders.bind(this);
        this.BatchUpdateOrders = this.BatchUpdateOrders.bind(this);
        this.PrivilegedExecuteContract = this.PrivilegedExecuteContract.bind(this);
        this.CreateDerivativeLimitOrder = this.CreateDerivativeLimitOrder.bind(this);
        this.BatchCreateDerivativeLimitOrders = this.BatchCreateDerivativeLimitOrders.bind(this);
        this.CreateDerivativeMarketOrder = this.CreateDerivativeMarketOrder.bind(this);
        this.CancelDerivativeOrder = this.CancelDerivativeOrder.bind(this);
        this.BatchCancelDerivativeOrders = this.BatchCancelDerivativeOrders.bind(this);
        this.InstantBinaryOptionsMarketLaunch = this.InstantBinaryOptionsMarketLaunch.bind(this);
        this.CreateBinaryOptionsLimitOrder = this.CreateBinaryOptionsLimitOrder.bind(this);
        this.CreateBinaryOptionsMarketOrder = this.CreateBinaryOptionsMarketOrder.bind(this);
        this.CancelBinaryOptionsOrder = this.CancelBinaryOptionsOrder.bind(this);
        this.BatchCancelBinaryOptionsOrders = this.BatchCancelBinaryOptionsOrders.bind(this);
        this.SubaccountTransfer = this.SubaccountTransfer.bind(this);
        this.ExternalTransfer = this.ExternalTransfer.bind(this);
        this.LiquidatePosition = this.LiquidatePosition.bind(this);
        this.EmergencySettleMarket = this.EmergencySettleMarket.bind(this);
        this.IncreasePositionMargin = this.IncreasePositionMargin.bind(this);
        this.DecreasePositionMargin = this.DecreasePositionMargin.bind(this);
        this.RewardsOptOut = this.RewardsOptOut.bind(this);
        this.AdminUpdateBinaryOptionsMarket = this.AdminUpdateBinaryOptionsMarket.bind(this);
        this.UpdateParams = this.UpdateParams.bind(this);
        this.UpdateSpotMarket = this.UpdateSpotMarket.bind(this);
        this.UpdateDerivativeMarket = this.UpdateDerivativeMarket.bind(this);
        this.AuthorizeStakeGrants = this.AuthorizeStakeGrants.bind(this);
        this.ActivateStakeGrant = this.ActivateStakeGrant.bind(this);
        this.BatchExchangeModification = this.BatchExchangeModification.bind(this);
    }
    MsgClientImpl.prototype.Deposit = function (request, metadata) {
        return this.rpc.unary(exports.MsgDepositDesc, exports.MsgDeposit.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.Withdraw = function (request, metadata) {
        return this.rpc.unary(exports.MsgWithdrawDesc, exports.MsgWithdraw.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.InstantSpotMarketLaunch = function (request, metadata) {
        return this.rpc.unary(exports.MsgInstantSpotMarketLaunchDesc, exports.MsgInstantSpotMarketLaunch.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.InstantPerpetualMarketLaunch = function (request, metadata) {
        return this.rpc.unary(exports.MsgInstantPerpetualMarketLaunchDesc, exports.MsgInstantPerpetualMarketLaunch.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.InstantExpiryFuturesMarketLaunch = function (request, metadata) {
        return this.rpc.unary(exports.MsgInstantExpiryFuturesMarketLaunchDesc, exports.MsgInstantExpiryFuturesMarketLaunch.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CreateSpotLimitOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCreateSpotLimitOrderDesc, exports.MsgCreateSpotLimitOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BatchCreateSpotLimitOrders = function (request, metadata) {
        return this.rpc.unary(exports.MsgBatchCreateSpotLimitOrdersDesc, exports.MsgBatchCreateSpotLimitOrders.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CreateSpotMarketOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCreateSpotMarketOrderDesc, exports.MsgCreateSpotMarketOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CancelSpotOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCancelSpotOrderDesc, exports.MsgCancelSpotOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BatchCancelSpotOrders = function (request, metadata) {
        return this.rpc.unary(exports.MsgBatchCancelSpotOrdersDesc, exports.MsgBatchCancelSpotOrders.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BatchUpdateOrders = function (request, metadata) {
        return this.rpc.unary(exports.MsgBatchUpdateOrdersDesc, exports.MsgBatchUpdateOrders.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.PrivilegedExecuteContract = function (request, metadata) {
        return this.rpc.unary(exports.MsgPrivilegedExecuteContractDesc, exports.MsgPrivilegedExecuteContract.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CreateDerivativeLimitOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCreateDerivativeLimitOrderDesc, exports.MsgCreateDerivativeLimitOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BatchCreateDerivativeLimitOrders = function (request, metadata) {
        return this.rpc.unary(exports.MsgBatchCreateDerivativeLimitOrdersDesc, exports.MsgBatchCreateDerivativeLimitOrders.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CreateDerivativeMarketOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCreateDerivativeMarketOrderDesc, exports.MsgCreateDerivativeMarketOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CancelDerivativeOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCancelDerivativeOrderDesc, exports.MsgCancelDerivativeOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BatchCancelDerivativeOrders = function (request, metadata) {
        return this.rpc.unary(exports.MsgBatchCancelDerivativeOrdersDesc, exports.MsgBatchCancelDerivativeOrders.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.InstantBinaryOptionsMarketLaunch = function (request, metadata) {
        return this.rpc.unary(exports.MsgInstantBinaryOptionsMarketLaunchDesc, exports.MsgInstantBinaryOptionsMarketLaunch.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CreateBinaryOptionsLimitOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCreateBinaryOptionsLimitOrderDesc, exports.MsgCreateBinaryOptionsLimitOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CreateBinaryOptionsMarketOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCreateBinaryOptionsMarketOrderDesc, exports.MsgCreateBinaryOptionsMarketOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CancelBinaryOptionsOrder = function (request, metadata) {
        return this.rpc.unary(exports.MsgCancelBinaryOptionsOrderDesc, exports.MsgCancelBinaryOptionsOrder.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BatchCancelBinaryOptionsOrders = function (request, metadata) {
        return this.rpc.unary(exports.MsgBatchCancelBinaryOptionsOrdersDesc, exports.MsgBatchCancelBinaryOptionsOrders.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.SubaccountTransfer = function (request, metadata) {
        return this.rpc.unary(exports.MsgSubaccountTransferDesc, exports.MsgSubaccountTransfer.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.ExternalTransfer = function (request, metadata) {
        return this.rpc.unary(exports.MsgExternalTransferDesc, exports.MsgExternalTransfer.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.LiquidatePosition = function (request, metadata) {
        return this.rpc.unary(exports.MsgLiquidatePositionDesc, exports.MsgLiquidatePosition.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.EmergencySettleMarket = function (request, metadata) {
        return this.rpc.unary(exports.MsgEmergencySettleMarketDesc, exports.MsgEmergencySettleMarket.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.IncreasePositionMargin = function (request, metadata) {
        return this.rpc.unary(exports.MsgIncreasePositionMarginDesc, exports.MsgIncreasePositionMargin.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.DecreasePositionMargin = function (request, metadata) {
        return this.rpc.unary(exports.MsgDecreasePositionMarginDesc, exports.MsgDecreasePositionMargin.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RewardsOptOut = function (request, metadata) {
        return this.rpc.unary(exports.MsgRewardsOptOutDesc, exports.MsgRewardsOptOut.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.AdminUpdateBinaryOptionsMarket = function (request, metadata) {
        return this.rpc.unary(exports.MsgAdminUpdateBinaryOptionsMarketDesc, exports.MsgAdminUpdateBinaryOptionsMarket.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.UpdateParams = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateParamsDesc, exports.MsgUpdateParams.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.UpdateSpotMarket = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateSpotMarketDesc, exports.MsgUpdateSpotMarket.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.UpdateDerivativeMarket = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateDerivativeMarketDesc, exports.MsgUpdateDerivativeMarket.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.AuthorizeStakeGrants = function (request, metadata) {
        return this.rpc.unary(exports.MsgAuthorizeStakeGrantsDesc, exports.MsgAuthorizeStakeGrants.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.ActivateStakeGrant = function (request, metadata) {
        return this.rpc.unary(exports.MsgActivateStakeGrantDesc, exports.MsgActivateStakeGrant.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BatchExchangeModification = function (request, metadata) {
        return this.rpc.unary(exports.MsgBatchExchangeModificationDesc, exports.MsgBatchExchangeModification.fromPartial(request), metadata);
    };
    return MsgClientImpl;
}());
exports.MsgClientImpl = MsgClientImpl;
exports.MsgDesc = { serviceName: "injective.exchange.v1beta1.Msg" };
exports.MsgDepositDesc = {
    methodName: "Deposit",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgDeposit.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgDepositResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgWithdrawDesc = {
    methodName: "Withdraw",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgWithdraw.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgWithdrawResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgInstantSpotMarketLaunchDesc = {
    methodName: "InstantSpotMarketLaunch",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgInstantSpotMarketLaunch.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgInstantSpotMarketLaunchResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgInstantPerpetualMarketLaunchDesc = {
    methodName: "InstantPerpetualMarketLaunch",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgInstantPerpetualMarketLaunch.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgInstantPerpetualMarketLaunchResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgInstantExpiryFuturesMarketLaunchDesc = {
    methodName: "InstantExpiryFuturesMarketLaunch",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgInstantExpiryFuturesMarketLaunch.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgInstantExpiryFuturesMarketLaunchResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCreateSpotLimitOrderDesc = {
    methodName: "CreateSpotLimitOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCreateSpotLimitOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCreateSpotLimitOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBatchCreateSpotLimitOrdersDesc = {
    methodName: "BatchCreateSpotLimitOrders",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBatchCreateSpotLimitOrders.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBatchCreateSpotLimitOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCreateSpotMarketOrderDesc = {
    methodName: "CreateSpotMarketOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCreateSpotMarketOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCreateSpotMarketOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCancelSpotOrderDesc = {
    methodName: "CancelSpotOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCancelSpotOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCancelSpotOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBatchCancelSpotOrdersDesc = {
    methodName: "BatchCancelSpotOrders",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBatchCancelSpotOrders.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBatchCancelSpotOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBatchUpdateOrdersDesc = {
    methodName: "BatchUpdateOrders",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBatchUpdateOrders.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBatchUpdateOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgPrivilegedExecuteContractDesc = {
    methodName: "PrivilegedExecuteContract",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgPrivilegedExecuteContract.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgPrivilegedExecuteContractResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCreateDerivativeLimitOrderDesc = {
    methodName: "CreateDerivativeLimitOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCreateDerivativeLimitOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCreateDerivativeLimitOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBatchCreateDerivativeLimitOrdersDesc = {
    methodName: "BatchCreateDerivativeLimitOrders",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBatchCreateDerivativeLimitOrders.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBatchCreateDerivativeLimitOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCreateDerivativeMarketOrderDesc = {
    methodName: "CreateDerivativeMarketOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCreateDerivativeMarketOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCreateDerivativeMarketOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCancelDerivativeOrderDesc = {
    methodName: "CancelDerivativeOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCancelDerivativeOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCancelDerivativeOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBatchCancelDerivativeOrdersDesc = {
    methodName: "BatchCancelDerivativeOrders",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBatchCancelDerivativeOrders.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBatchCancelDerivativeOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgInstantBinaryOptionsMarketLaunchDesc = {
    methodName: "InstantBinaryOptionsMarketLaunch",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgInstantBinaryOptionsMarketLaunch.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgInstantBinaryOptionsMarketLaunchResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCreateBinaryOptionsLimitOrderDesc = {
    methodName: "CreateBinaryOptionsLimitOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCreateBinaryOptionsLimitOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCreateBinaryOptionsLimitOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCreateBinaryOptionsMarketOrderDesc = {
    methodName: "CreateBinaryOptionsMarketOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCreateBinaryOptionsMarketOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCreateBinaryOptionsMarketOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCancelBinaryOptionsOrderDesc = {
    methodName: "CancelBinaryOptionsOrder",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCancelBinaryOptionsOrder.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCancelBinaryOptionsOrderResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBatchCancelBinaryOptionsOrdersDesc = {
    methodName: "BatchCancelBinaryOptionsOrders",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBatchCancelBinaryOptionsOrders.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBatchCancelBinaryOptionsOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgSubaccountTransferDesc = {
    methodName: "SubaccountTransfer",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgSubaccountTransfer.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgSubaccountTransferResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgExternalTransferDesc = {
    methodName: "ExternalTransfer",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgExternalTransfer.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgExternalTransferResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgLiquidatePositionDesc = {
    methodName: "LiquidatePosition",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgLiquidatePosition.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgLiquidatePositionResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgEmergencySettleMarketDesc = {
    methodName: "EmergencySettleMarket",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgEmergencySettleMarket.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgEmergencySettleMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgIncreasePositionMarginDesc = {
    methodName: "IncreasePositionMargin",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgIncreasePositionMargin.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgIncreasePositionMarginResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgDecreasePositionMarginDesc = {
    methodName: "DecreasePositionMargin",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgDecreasePositionMargin.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgDecreasePositionMarginResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRewardsOptOutDesc = {
    methodName: "RewardsOptOut",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRewardsOptOut.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRewardsOptOutResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgAdminUpdateBinaryOptionsMarketDesc = {
    methodName: "AdminUpdateBinaryOptionsMarket",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgAdminUpdateBinaryOptionsMarket.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgAdminUpdateBinaryOptionsMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgUpdateParamsDesc = {
    methodName: "UpdateParams",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateParams.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgUpdateSpotMarketDesc = {
    methodName: "UpdateSpotMarket",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateSpotMarket.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateSpotMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgUpdateDerivativeMarketDesc = {
    methodName: "UpdateDerivativeMarket",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateDerivativeMarket.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateDerivativeMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgAuthorizeStakeGrantsDesc = {
    methodName: "AuthorizeStakeGrants",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgAuthorizeStakeGrants.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgAuthorizeStakeGrantsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgActivateStakeGrantDesc = {
    methodName: "ActivateStakeGrant",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgActivateStakeGrant.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgActivateStakeGrantResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBatchExchangeModificationDesc = {
    methodName: "BatchExchangeModification",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBatchExchangeModification.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBatchExchangeModificationResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
