"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DenomAuthorityMetadata = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "injective.tokenfactory.v1beta1";
function createBaseDenomAuthorityMetadata() {
    return { admin: "", adminBurnAllowed: false };
}
exports.DenomAuthorityMetadata = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.admin !== "") {
            writer.uint32(10).string(message.admin);
        }
        if (message.adminBurnAllowed === true) {
            writer.uint32(16).bool(message.adminBurnAllowed);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDenomAuthorityMetadata();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.admin = reader.string();
                    break;
                case 2:
                    message.adminBurnAllowed = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            admin: isSet(object.admin) ? String(object.admin) : "",
            adminBurnAllowed: isSet(object.adminBurnAllowed) ? Boolean(object.adminBurnAllowed) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.admin !== undefined && (obj.admin = message.admin);
        message.adminBurnAllowed !== undefined && (obj.adminBurnAllowed = message.adminBurnAllowed);
        return obj;
    },
    create: function (base) {
        return exports.DenomAuthorityMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDenomAuthorityMetadata();
        message.admin = (_a = object.admin) !== null && _a !== void 0 ? _a : "";
        message.adminBurnAllowed = (_b = object.adminBurnAllowed) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
