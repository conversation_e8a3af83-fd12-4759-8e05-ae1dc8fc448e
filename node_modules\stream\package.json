{"name": "stream", "version": "0.0.3", "description": "Node.js streams in the browser", "main": "index.js", "browser": {"emitter": "emitter-component"}, "repository": {"type": "git", "url": "git://github.com/juliangruber/stream.git"}, "keywords": ["stream"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"component-emitter": "^2.0.0"}, "devDependencies": {"mocha": "*", "expect.js": "*"}}