"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenSource = exports.TokenVerification = exports.TokenType = void 0;
var TokenType;
(function (TokenType) {
    TokenType["Ibc"] = "ibc";
    TokenType["Cw20"] = "cw20";
    TokenType["Spl"] = "spl";
    TokenType["Erc20"] = "erc20";
    TokenType["Lp"] = "lp";
    TokenType["Evm"] = "evm";
    TokenType["Native"] = "native";
    TokenType["Symbol"] = "symbol";
    TokenType["TokenFactory"] = "tokenFactory";
    TokenType["InsuranceFund"] = "insuranceFund";
    TokenType["Unknown"] = "unknown";
})(TokenType || (exports.TokenType = TokenType = {}));
var TokenVerification;
(function (TokenVerification) {
    TokenVerification["Verified"] = "verified"; /** verified on token-metadata package */
    TokenVerification["Submitted"] = "submitted"; /** submitted on token-metadata package but not verified */
    TokenVerification["Internal"] = "internal"; /** verified from on-chain data */
    TokenVerification["External"] = "external"; /** verified on external source */
    TokenVerification["Unverified"] = "unverified"; /** unverified on any source */
})(TokenVerification || (exports.TokenVerification = TokenVerification = {}));
var TokenSource;
(function (TokenSource) {
    TokenSource["Aptos"] = "aptos";
    TokenSource["Solana"] = "solana";
    TokenSource["Cosmos"] = "cosmos";
    TokenSource["Ethereum"] = "ethereum";
    TokenSource["EthereumWh"] = "ethereum-wormhole";
    TokenSource["Polygon"] = "polygon";
    TokenSource["Klaytn"] = "klaytn";
    TokenSource["Arbitrum"] = "arbitrum";
    TokenSource["Sui"] = "sui";
    TokenSource["Ibc"] = "ibc";
    TokenSource["BinanceSmartChain"] = "binance-smart-chain";
    TokenSource["Axelar"] = "axelar";
})(TokenSource || (exports.TokenSource = TokenSource = {}));
