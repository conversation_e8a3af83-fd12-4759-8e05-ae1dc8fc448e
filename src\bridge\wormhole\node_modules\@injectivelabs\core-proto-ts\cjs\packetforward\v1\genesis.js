"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InFlightPacket = exports.GenesisState_InFlightPacketsEntry = exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "packetforward.v1";
function createBaseGenesisState() {
    return { inFlightPackets: {} };
}
exports.GenesisState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        Object.entries(message.inFlightPackets).forEach(function (_a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            exports.GenesisState_InFlightPacketsEntry.encode({ key: key, value: value }, writer.uint32(18).fork()).ldelim();
        });
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 2:
                    var entry2 = exports.GenesisState_InFlightPacketsEntry.decode(reader, reader.uint32());
                    if (entry2.value !== undefined) {
                        message.inFlightPackets[entry2.key] = entry2.value;
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            inFlightPackets: isObject(object.inFlightPackets)
                ? Object.entries(object.inFlightPackets).reduce(function (acc, _a) {
                    var _b = __read(_a, 2), key = _b[0], value = _b[1];
                    acc[key] = exports.InFlightPacket.fromJSON(value);
                    return acc;
                }, {})
                : {},
        };
    },
    toJSON: function (message) {
        var obj = {};
        obj.inFlightPackets = {};
        if (message.inFlightPackets) {
            Object.entries(message.inFlightPackets).forEach(function (_a) {
                var _b = __read(_a, 2), k = _b[0], v = _b[1];
                obj.inFlightPackets[k] = exports.InFlightPacket.toJSON(v);
            });
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseGenesisState();
        message.inFlightPackets = Object.entries((_a = object.inFlightPackets) !== null && _a !== void 0 ? _a : {}).reduce(function (acc, _a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            if (value !== undefined) {
                acc[key] = exports.InFlightPacket.fromPartial(value);
            }
            return acc;
        }, {});
        return message;
    },
};
function createBaseGenesisState_InFlightPacketsEntry() {
    return { key: "", value: undefined };
}
exports.GenesisState_InFlightPacketsEntry = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            exports.InFlightPacket.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState_InFlightPacketsEntry();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = exports.InFlightPacket.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? exports.InFlightPacket.fromJSON(object.value) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value ? exports.InFlightPacket.toJSON(message.value) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.GenesisState_InFlightPacketsEntry.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseGenesisState_InFlightPacketsEntry();
        message.key = (_a = object.key) !== null && _a !== void 0 ? _a : "";
        message.value = (object.value !== undefined && object.value !== null)
            ? exports.InFlightPacket.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseInFlightPacket() {
    return {
        originalSenderAddress: "",
        refundChannelId: "",
        refundPortId: "",
        packetSrcChannelId: "",
        packetSrcPortId: "",
        packetTimeoutTimestamp: "0",
        packetTimeoutHeight: "",
        packetData: new Uint8Array(),
        refundSequence: "0",
        retriesRemaining: 0,
        timeout: "0",
        nonrefundable: false,
    };
}
exports.InFlightPacket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.originalSenderAddress !== "") {
            writer.uint32(10).string(message.originalSenderAddress);
        }
        if (message.refundChannelId !== "") {
            writer.uint32(18).string(message.refundChannelId);
        }
        if (message.refundPortId !== "") {
            writer.uint32(26).string(message.refundPortId);
        }
        if (message.packetSrcChannelId !== "") {
            writer.uint32(34).string(message.packetSrcChannelId);
        }
        if (message.packetSrcPortId !== "") {
            writer.uint32(42).string(message.packetSrcPortId);
        }
        if (message.packetTimeoutTimestamp !== "0") {
            writer.uint32(48).uint64(message.packetTimeoutTimestamp);
        }
        if (message.packetTimeoutHeight !== "") {
            writer.uint32(58).string(message.packetTimeoutHeight);
        }
        if (message.packetData.length !== 0) {
            writer.uint32(66).bytes(message.packetData);
        }
        if (message.refundSequence !== "0") {
            writer.uint32(72).uint64(message.refundSequence);
        }
        if (message.retriesRemaining !== 0) {
            writer.uint32(80).int32(message.retriesRemaining);
        }
        if (message.timeout !== "0") {
            writer.uint32(88).uint64(message.timeout);
        }
        if (message.nonrefundable === true) {
            writer.uint32(96).bool(message.nonrefundable);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInFlightPacket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.originalSenderAddress = reader.string();
                    break;
                case 2:
                    message.refundChannelId = reader.string();
                    break;
                case 3:
                    message.refundPortId = reader.string();
                    break;
                case 4:
                    message.packetSrcChannelId = reader.string();
                    break;
                case 5:
                    message.packetSrcPortId = reader.string();
                    break;
                case 6:
                    message.packetTimeoutTimestamp = longToString(reader.uint64());
                    break;
                case 7:
                    message.packetTimeoutHeight = reader.string();
                    break;
                case 8:
                    message.packetData = reader.bytes();
                    break;
                case 9:
                    message.refundSequence = longToString(reader.uint64());
                    break;
                case 10:
                    message.retriesRemaining = reader.int32();
                    break;
                case 11:
                    message.timeout = longToString(reader.uint64());
                    break;
                case 12:
                    message.nonrefundable = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            originalSenderAddress: isSet(object.originalSenderAddress) ? String(object.originalSenderAddress) : "",
            refundChannelId: isSet(object.refundChannelId) ? String(object.refundChannelId) : "",
            refundPortId: isSet(object.refundPortId) ? String(object.refundPortId) : "",
            packetSrcChannelId: isSet(object.packetSrcChannelId) ? String(object.packetSrcChannelId) : "",
            packetSrcPortId: isSet(object.packetSrcPortId) ? String(object.packetSrcPortId) : "",
            packetTimeoutTimestamp: isSet(object.packetTimeoutTimestamp) ? String(object.packetTimeoutTimestamp) : "0",
            packetTimeoutHeight: isSet(object.packetTimeoutHeight) ? String(object.packetTimeoutHeight) : "",
            packetData: isSet(object.packetData) ? bytesFromBase64(object.packetData) : new Uint8Array(),
            refundSequence: isSet(object.refundSequence) ? String(object.refundSequence) : "0",
            retriesRemaining: isSet(object.retriesRemaining) ? Number(object.retriesRemaining) : 0,
            timeout: isSet(object.timeout) ? String(object.timeout) : "0",
            nonrefundable: isSet(object.nonrefundable) ? Boolean(object.nonrefundable) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.originalSenderAddress !== undefined && (obj.originalSenderAddress = message.originalSenderAddress);
        message.refundChannelId !== undefined && (obj.refundChannelId = message.refundChannelId);
        message.refundPortId !== undefined && (obj.refundPortId = message.refundPortId);
        message.packetSrcChannelId !== undefined && (obj.packetSrcChannelId = message.packetSrcChannelId);
        message.packetSrcPortId !== undefined && (obj.packetSrcPortId = message.packetSrcPortId);
        message.packetTimeoutTimestamp !== undefined && (obj.packetTimeoutTimestamp = message.packetTimeoutTimestamp);
        message.packetTimeoutHeight !== undefined && (obj.packetTimeoutHeight = message.packetTimeoutHeight);
        message.packetData !== undefined &&
            (obj.packetData = base64FromBytes(message.packetData !== undefined ? message.packetData : new Uint8Array()));
        message.refundSequence !== undefined && (obj.refundSequence = message.refundSequence);
        message.retriesRemaining !== undefined && (obj.retriesRemaining = Math.round(message.retriesRemaining));
        message.timeout !== undefined && (obj.timeout = message.timeout);
        message.nonrefundable !== undefined && (obj.nonrefundable = message.nonrefundable);
        return obj;
    },
    create: function (base) {
        return exports.InFlightPacket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        var message = createBaseInFlightPacket();
        message.originalSenderAddress = (_a = object.originalSenderAddress) !== null && _a !== void 0 ? _a : "";
        message.refundChannelId = (_b = object.refundChannelId) !== null && _b !== void 0 ? _b : "";
        message.refundPortId = (_c = object.refundPortId) !== null && _c !== void 0 ? _c : "";
        message.packetSrcChannelId = (_d = object.packetSrcChannelId) !== null && _d !== void 0 ? _d : "";
        message.packetSrcPortId = (_e = object.packetSrcPortId) !== null && _e !== void 0 ? _e : "";
        message.packetTimeoutTimestamp = (_f = object.packetTimeoutTimestamp) !== null && _f !== void 0 ? _f : "0";
        message.packetTimeoutHeight = (_g = object.packetTimeoutHeight) !== null && _g !== void 0 ? _g : "";
        message.packetData = (_h = object.packetData) !== null && _h !== void 0 ? _h : new Uint8Array();
        message.refundSequence = (_j = object.refundSequence) !== null && _j !== void 0 ? _j : "0";
        message.retriesRemaining = (_k = object.retriesRemaining) !== null && _k !== void 0 ? _k : 0;
        message.timeout = (_l = object.timeout) !== null && _l !== void 0 ? _l : "0";
        message.nonrefundable = (_m = object.nonrefundable) !== null && _m !== void 0 ? _m : false;
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
