"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgRequestRedemption extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgRequestRedemption(params);
    }
    toProto() {
        const { params } = this;
        const amountCoin = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        amountCoin.denom = params.amount.denom;
        amountCoin.amount = params.amount.amount;
        const message = core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgRequestRedemption.create();
        message.sender = params.injectiveAddress;
        message.marketId = params.marketId;
        message.amount = amountCoin;
        return core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgRequestRedemption.fromJSON(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.insurance.v1beta1.MsgRequestRedemption',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'insurance/MsgRequestRedemption',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.insurance.v1beta1.MsgRequestRedemption',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.insurance.v1beta1.MsgRequestRedemption',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgRequestRedemption.encode(this.toProto()).finish();
    }
}
exports.default = MsgRequestRedemption;
