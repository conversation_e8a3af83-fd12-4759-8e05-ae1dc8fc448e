#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复KyberSwap中的函数调用问题
创建包装函数以适应不同版本的API
w无用脚本
"""

import os
import sys
import inspect
import logging
import importlib

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("kyberswap_fix.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("KyberSwapFix")

def check_module():
    """检查KyberSwap模块"""
    try:
        # 导入模块
        try:
            import src.dex.KyberSwap.swap as swap_module
            logger.info("成功导入KyberSwap模块")
            return swap_module
        except ImportError:
            logger.error("无法导入KyberSwap模块")
            return None
            
    except Exception as e:
        logger.error(f"检查KyberSwap模块时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def create_wrapper_functions(swap_module):
    """创建包装函数以适应不同版本的API"""
    try:
        # 检查handle_amount_and_chain函数
        handle_params = None
        if hasattr(swap_module, 'handle_amount_and_chain'):
            handle_params = inspect.signature(swap_module.handle_amount_and_chain).parameters
            logger.info(f"handle_amount_and_chain 函数参数: {list(handle_params.keys())}")
        else:
            logger.error("未找到handle_amount_and_chain函数")
            return False
            
        # 检查swap_tokens函数
        swap_params = None
        if hasattr(swap_module, 'swap_tokens'):
            swap_params = inspect.signature(swap_module.swap_tokens).parameters
            logger.info(f"swap_tokens 函数参数: {list(swap_params.keys())}")
        else:
            logger.error("未找到swap_tokens函数")
            return False
            
        # 创建修复模块
        fix_code = [
            "# 自动生成的修复模块",
            "import asyncio",
            "import sys",
            "import os",
            "import logging",
            "",
            "# 添加项目根目录到系统路径",
            "sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))",
            "",
            "logger = logging.getLogger('KyberSwapFix')",
            "",
            "# 导入原始函数",
            "from src.dex.KyberSwap.swap import handle_amount_and_chain as original_handle_amount_and_chain",
            "from src.dex.KyberSwap.swap import swap_tokens as original_swap_tokens",
            ""
        ]
        
        # 包装handle_amount_and_chain函数
        if 'ctx' in handle_params:
            logger.info("检测到handle_amount_and_chain需要ctx参数，创建包装函数")
            fix_code.extend([
                "async def handle_amount_and_chain(chain, token_in, token_out, amount):",
                "    # 创建一个空上下文",
                "    ctx = {}",
                "    logger.info('使用包装的handle_amount_and_chain函数，添加ctx参数')",
                "    return await original_handle_amount_and_chain(chain=chain, token_in=token_in, token_out=token_out, amount=amount, ctx=ctx)",
                ""
            ])
        else:
            logger.info("handle_amount_and_chain不需要ctx参数，使用原始函数")
            fix_code.extend([
                "# 直接使用原始handle_amount_and_chain函数",
                "handle_amount_and_chain = original_handle_amount_and_chain",
                ""
            ])
            
        # 包装swap_tokens函数，检查是否需要添加其他参数
        if 'ctx' in swap_params:
            logger.info("检测到swap_tokens需要ctx参数，创建包装函数")
            fix_code.extend([
                "async def swap_tokens(chain, token_in, token_out, amount, slippage=0.5, deadline_minutes=20, gas_limit=1500000, real=True):",
                "    # 创建一个空上下文",
                "    ctx = {}",
                "    logger.info('使用包装的swap_tokens函数，添加ctx参数')",
                "    return await original_swap_tokens(chain=chain, token_in=token_in, token_out=token_out, amount=amount,",
                "                                    slippage=slippage, deadline_minutes=deadline_minutes,",
                "                                    gas_limit=gas_limit, real=real, ctx=ctx)",
                ""
            ])
        else:
            logger.info("swap_tokens不需要ctx参数，使用原始函数")
            fix_code.extend([
                "# 直接使用原始swap_tokens函数",
                "swap_tokens = original_swap_tokens",
                ""
            ])
            
        # 写入文件
        fix_path = os.path.join(os.path.dirname(__file__), "kyberswap_wrapper.py")
        with open(fix_path, "w", encoding="utf-8") as f:
            f.write("\n".join(fix_code))
            
        logger.info(f"成功创建包装模块: {fix_path}")
        
        # 编辑bridge_arb_executor.py以使用修复模块
        executor_path = os.path.join(os.path.dirname(__file__), "bridge_arb_executor.py")
        if os.path.exists(executor_path):
            try:
                with open(executor_path, "r", encoding="utf-8") as f:
                    executor_code = f.read()
                    
                # 替换导入语句
                if "from src.dex.KyberSwap.swap import swap_tokens, handle_amount_and_chain" in executor_code:
                    executor_code = executor_code.replace(
                        "from src.dex.KyberSwap.swap import swap_tokens, handle_amount_and_chain",
                        "# 使用修复后的包装函数\nfrom scripts.arbitrage.portal_polygon_bridge.kyberswap_wrapper import swap_tokens, handle_amount_and_chain"
                    )
                    
                    # 写回文件
                    with open(executor_path, "w", encoding="utf-8") as f:
                        f.write(executor_code)
                        
                    logger.info(f"成功修改 {executor_path} 以使用包装函数")
                else:
                    logger.warning(f"未找到导入语句，未修改 {executor_path}")
            except Exception as e:
                logger.error(f"修改 {executor_path} 时出错: {str(e)}")
                
        return True
                
    except Exception as e:
        logger.error(f"创建包装函数时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("开始修复KyberSwap函数调用问题")
    
    # 检查模块
    swap_module = check_module()
    if not swap_module:
        logger.error("无法继续修复，退出")
        return
        
    # 创建包装函数
    success = create_wrapper_functions(swap_module)
    
    if success:
        logger.info("修复完成，请重新运行bridge_arb_executor.py")
    else:
        logger.error("修复失败")

if __name__ == "__main__":
    main() 