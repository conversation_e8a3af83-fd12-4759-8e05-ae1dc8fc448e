"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgBurn extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgBurn(params);
    }
    toProto() {
        const { params } = this;
        const coin = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        coin.denom = params.amount.denom;
        coin.amount = params.amount.amount;
        const message = core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgBurn.create();
        message.sender = params.sender;
        message.amount = coin;
        if (params.burnFromAddress) {
            message.burnFromAddress = params.burnFromAddress;
        }
        return core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgBurn.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.tokenfactory.v1beta1.MsgBurn',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
            burnFromAddress: proto.burnFromAddress,
        };
        const { burn_from_address, ...messageWithoutBurnFromAddress } = message;
        return {
            type: 'injective/tokenfactory/burn',
            value: messageWithoutBurnFromAddress,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.tokenfactory.v1beta1.MsgBurn',
            ...value,
        };
    }
    toEip712Types() {
        const map = new Map();
        map.set('TypeAmount', [
            {
                name: 'denom',
                type: 'string',
            },
            {
                name: 'amount',
                type: 'string',
            },
        ]);
        map.set('MsgValue', [
            {
                name: 'sender',
                type: 'string',
            },
            {
                name: 'amount',
                type: 'TypeAmount',
            },
            {
                name: 'burnFromAddress',
                type: 'string',
            },
        ]);
        return map;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.tokenfactory.v1beta1.MsgBurn',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgBurn.encode(this.toProto()).finish();
    }
}
exports.default = MsgBurn;
