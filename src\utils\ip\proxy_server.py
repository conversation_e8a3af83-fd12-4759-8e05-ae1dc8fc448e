"""
代理服务器
将Shadowsocks代理转换为HTTP代理供系统使用
"""

import json
import yaml
import time
import logging
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional

class ProxyServer:
    """代理服务器管理器"""
    
    def __init__(self, config_path: str = "config/ip.yaml"):
        """
        初始化代理服务器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.clash_process = None
        self.clash_config_file = None
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _generate_clash_config(self, proxy: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成Clash配置
        
        Args:
            proxy: 代理配置
            
        Returns:
            dict: Clash配置
        """
        clash_config = {
            'mixed-port': self.config.get('proxy_config', {}).get('mixed_port', 7890),
            'allow-lan': self.config.get('proxy_config', {}).get('allow_lan', False),
            'bind-address': self.config.get('proxy_config', {}).get('bind_address', '*'),
            'mode': self.config.get('proxy_config', {}).get('mode', 'rule'),
            'log-level': self.config.get('proxy_config', {}).get('log_level', 'info'),
            'ipv6': self.config.get('proxy_config', {}).get('ipv6', False),
            'external-controller': self.config.get('proxy_config', {}).get('external_controller', '127.0.0.1:9090'),
            'dns': self.config.get('dns', {}),
            'proxies': [proxy],
            'proxy-groups': [
                {
                    'name': 'PROXY',
                    'type': 'select',
                    'proxies': [proxy['name']]
                }
            ],
            'rules': [
                'MATCH,PROXY'
            ]
        }
        
        return clash_config
    
    def start_proxy_server(self, proxy: Dict[str, Any]) -> bool:
        """
        启动代理服务器
        
        Args:
            proxy: 代理配置
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 停止现有的代理服务器
            self.stop_proxy_server()
            
            # 生成Clash配置
            clash_config = self._generate_clash_config(proxy)
            
            # 创建临时配置文件
            self.clash_config_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.yaml', 
                delete=False,
                encoding='utf-8'
            )
            
            yaml.dump(clash_config, self.clash_config_file, 
                     default_flow_style=False, allow_unicode=True)
            self.clash_config_file.close()
            
            # 启动Clash
            clash_cmd = [
                'clash',
                '-f', self.clash_config_file.name
            ]
            
            self.clash_process = subprocess.Popen(
                clash_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
            )
            
            # 等待启动
            time.sleep(3)
            
            # 检查进程是否正常运行
            if self.clash_process.poll() is None:
                self.logger.info(f"代理服务器启动成功，使用节点: {proxy['name']}")
                return True
            else:
                self.logger.error("代理服务器启动失败")
                return False
                
        except Exception as e:
            self.logger.error(f"启动代理服务器失败: {e}")
            return False
    
    def stop_proxy_server(self):
        """停止代理服务器"""
        try:
            if self.clash_process:
                self.clash_process.terminate()
                try:
                    self.clash_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.clash_process.kill()
                    self.clash_process.wait()
                
                self.clash_process = None
                self.logger.info("代理服务器已停止")
            
            # 删除临时配置文件
            if self.clash_config_file and Path(self.clash_config_file.name).exists():
                Path(self.clash_config_file.name).unlink()
                self.clash_config_file = None
                
        except Exception as e:
            self.logger.error(f"停止代理服务器失败: {e}")
    
    def is_running(self) -> bool:
        """
        检查代理服务器是否运行
        
        Returns:
            bool: 是否运行
        """
        return self.clash_process is not None and self.clash_process.poll() is None
    
    def get_proxy_info(self) -> Optional[Dict[str, Any]]:
        """
        获取代理信息
        
        Returns:
            dict: 代理信息
        """
        if not self.is_running():
            return None
            
        try:
            import requests
            
            # 通过Clash API获取信息
            api_url = "http://127.0.0.1:9090/proxies"
            response = requests.get(api_url, timeout=5)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"获取代理信息失败: {e}")
            return None

class SimpleProxyServer:
    """简单的代理服务器（不依赖Clash）"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def set_system_proxy(self, server: str, port: int) -> bool:
        """
        设置系统代理
        
        Args:
            server: 代理服务器地址
            port: 代理端口
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # Windows系统设置代理
            cmd_enable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f'
            cmd_server = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer /t REG_SZ /d "{server}:{port}" /f'
            
            # 执行命令
            subprocess.run(cmd_enable, shell=True, check=True, capture_output=True)
            subprocess.run(cmd_server, shell=True, check=True, capture_output=True)
            
            self.logger.info(f"系统代理设置成功: {server}:{port}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"设置系统代理失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"设置系统代理异常: {e}")
            return False
    
    def disable_system_proxy(self) -> bool:
        """
        禁用系统代理
        
        Returns:
            bool: 禁用是否成功
        """
        try:
            cmd_disable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f'
            subprocess.run(cmd_disable, shell=True, check=True, capture_output=True)
            
            self.logger.info("系统代理已禁用")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"禁用系统代理失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"禁用系统代理异常: {e}")
            return False
