"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BitGetException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
const maps_js_1 = require("../utils/maps.js");
const removeBitGetFromErrorString = (message) => message
    .replaceAll('BitGet', '')
    .replaceAll('Bitget:', '')
    .replaceAll('Bitkeep:', '');
class BitGetException extends base_js_1.ConcreteException {
    static errorClass = 'BitGetException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setName(BitGetException.errorClass);
        this.setMessage((0, maps_js_1.mapMetamaskMessage)(removeBitGetFromErrorString(message)));
    }
}
exports.BitGetException = BitGetException;
