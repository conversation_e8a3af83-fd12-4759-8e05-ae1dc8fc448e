import { InjectiveTradingRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { MarketType, GridStrategyType } from '../types/index.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcTradingApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveTradingRpc.InjectiveTradingRPCClientImpl;
    constructor(endpoint: string);
    fetchTradingStats(): Promise<InjectiveTradingRpc.GetTradingStatsResponse>;
    fetchGridStrategies({ skip, state, limit, withTvl, endTime, marketId, startTime, marketType, strategyType, subaccountId, accountAddress, withPerformance, pendingExecution, lastExecutedTime, isTrailingStrategy, }: {
        skip?: number;
        state?: string;
        limit?: number;
        endTime?: number;
        withTvl?: boolean;
        marketId?: string;
        startTime?: number;
        marketType?: MarketType;
        subaccountId?: string;
        strategyType?: GridStrategyType[];
        accountAddress?: string;
        withPerformance?: boolean;
        pendingExecution?: boolean;
        lastExecutedTime?: number;
        isTrailingStrategy?: boolean;
    }): Promise<InjectiveTradingRpc.ListTradingStrategiesResponse>;
}
