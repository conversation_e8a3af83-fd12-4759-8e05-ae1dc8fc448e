"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWasmAminoConverters = exports.accessTypeToString = exports.accessTypeFromString = void 0;
/* eslint-disable @typescript-eslint/naming-convention */
const amino_1 = require("@cosmjs/amino");
const encoding_1 = require("@cosmjs/encoding");
const types_1 = require("cosmjs-types/cosmwasm/wasm/v1/types");
function accessTypeFromString(str) {
    switch (str) {
        case "Unspecified":
            return types_1.AccessType.ACCESS_TYPE_UNSPECIFIED;
        case "Nobody":
            return types_1.AccessType.ACCESS_TYPE_NOBODY;
        case "OnlyAddress":
            return types_1.AccessType.ACCESS_TYPE_ONLY_ADDRESS;
        case "Everybody":
            return types_1.AccessType.ACCESS_TYPE_EVERYBODY;
        case "AnyOfAddresses":
            return types_1.AccessType.ACCESS_TYPE_ANY_OF_ADDRESSES;
        default:
            return types_1.AccessType.UNRECOGNIZED;
    }
}
exports.accessTypeFromString = accessTypeFromString;
function accessTypeToString(object) {
    switch (object) {
        case types_1.AccessType.ACCESS_TYPE_UNSPECIFIED:
            return "Unspecified";
        case types_1.AccessType.ACCESS_TYPE_NOBODY:
            return "Nobody";
        case types_1.AccessType.ACCESS_TYPE_ONLY_ADDRESS:
            return "OnlyAddress";
        case types_1.AccessType.ACCESS_TYPE_EVERYBODY:
            return "Everybody";
        case types_1.AccessType.ACCESS_TYPE_ANY_OF_ADDRESSES:
            return "AnyOfAddresses";
        case types_1.AccessType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
exports.accessTypeToString = accessTypeToString;
function createWasmAminoConverters() {
    return {
        "/cosmwasm.wasm.v1.MsgStoreCode": {
            aminoType: "wasm/MsgStoreCode",
            toAmino: ({ sender, wasmByteCode, instantiatePermission, }) => ({
                sender: sender,
                wasm_byte_code: (0, encoding_1.toBase64)(wasmByteCode),
                instantiate_permission: instantiatePermission
                    ? {
                        permission: accessTypeToString(instantiatePermission.permission),
                        address: instantiatePermission.address || undefined,
                        addresses: instantiatePermission.addresses.length !== 0 ? instantiatePermission.addresses : undefined,
                    }
                    : undefined,
            }),
            fromAmino: ({ sender, wasm_byte_code, instantiate_permission, }) => ({
                sender: sender,
                wasmByteCode: (0, encoding_1.fromBase64)(wasm_byte_code),
                instantiatePermission: instantiate_permission
                    ? types_1.AccessConfig.fromPartial({
                        permission: accessTypeFromString(instantiate_permission.permission),
                        address: instantiate_permission.address ?? "",
                        addresses: instantiate_permission.addresses ?? [],
                    })
                    : undefined,
            }),
        },
        "/cosmwasm.wasm.v1.MsgInstantiateContract": {
            aminoType: "wasm/MsgInstantiateContract",
            toAmino: ({ sender, codeId, label, msg, funds, admin, }) => ({
                sender: sender,
                code_id: codeId.toString(),
                label: label,
                msg: JSON.parse((0, encoding_1.fromUtf8)(msg)),
                funds: funds,
                admin: (0, amino_1.omitDefault)(admin),
            }),
            fromAmino: ({ sender, code_id, label, msg, funds, admin, }) => ({
                sender: sender,
                codeId: BigInt(code_id),
                label: label,
                msg: (0, encoding_1.toUtf8)(JSON.stringify(msg)),
                funds: [...funds],
                admin: admin ?? "",
            }),
        },
        "/cosmwasm.wasm.v1.MsgInstantiateContract2": {
            aminoType: "wasm/MsgInstantiateContract2",
            toAmino: ({ sender, codeId, label, msg, funds, admin, salt, fixMsg, }) => ({
                sender: sender,
                code_id: codeId.toString(),
                label: label,
                msg: JSON.parse((0, encoding_1.fromUtf8)(msg)),
                funds: funds,
                admin: (0, amino_1.omitDefault)(admin),
                salt: (0, encoding_1.toBase64)(salt),
                fix_msg: (0, amino_1.omitDefault)(fixMsg),
            }),
            fromAmino: ({ sender, code_id, label, msg, funds, admin, salt, fix_msg, }) => ({
                sender: sender,
                codeId: BigInt(code_id),
                label: label,
                msg: (0, encoding_1.toUtf8)(JSON.stringify(msg)),
                funds: [...funds],
                admin: admin ?? "",
                salt: (0, encoding_1.fromBase64)(salt),
                fixMsg: fix_msg ?? false,
            }),
        },
        "/cosmwasm.wasm.v1.MsgUpdateAdmin": {
            aminoType: "wasm/MsgUpdateAdmin",
            toAmino: ({ sender, newAdmin, contract }) => ({
                sender: sender,
                new_admin: newAdmin,
                contract: contract,
            }),
            fromAmino: ({ sender, new_admin, contract }) => ({
                sender: sender,
                newAdmin: new_admin,
                contract: contract,
            }),
        },
        "/cosmwasm.wasm.v1.MsgClearAdmin": {
            aminoType: "wasm/MsgClearAdmin",
            toAmino: ({ sender, contract }) => ({
                sender: sender,
                contract: contract,
            }),
            fromAmino: ({ sender, contract }) => ({
                sender: sender,
                contract: contract,
            }),
        },
        "/cosmwasm.wasm.v1.MsgExecuteContract": {
            aminoType: "wasm/MsgExecuteContract",
            toAmino: ({ sender, contract, msg, funds }) => ({
                sender: sender,
                contract: contract,
                msg: JSON.parse((0, encoding_1.fromUtf8)(msg)),
                funds: funds,
            }),
            fromAmino: ({ sender, contract, msg, funds, }) => ({
                sender: sender,
                contract: contract,
                msg: (0, encoding_1.toUtf8)(JSON.stringify(msg)),
                funds: [...funds],
            }),
        },
        "/cosmwasm.wasm.v1.MsgMigrateContract": {
            aminoType: "wasm/MsgMigrateContract",
            toAmino: ({ sender, contract, codeId, msg }) => ({
                sender: sender,
                contract: contract,
                code_id: codeId.toString(),
                msg: JSON.parse((0, encoding_1.fromUtf8)(msg)),
            }),
            fromAmino: ({ sender, contract, code_id, msg, }) => ({
                sender: sender,
                contract: contract,
                codeId: BigInt(code_id),
                msg: (0, encoding_1.toUtf8)(JSON.stringify(msg)),
            }),
        },
    };
}
exports.createWasmAminoConverters = createWasmAminoConverters;
//# sourceMappingURL=aminomessages.js.map